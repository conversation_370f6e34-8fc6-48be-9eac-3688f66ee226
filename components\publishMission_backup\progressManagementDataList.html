<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

    <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">


    <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script>
    <script src="../../plugins/layui/layui.js"></script>
    <script src="../js/util.js"></script>
    <title>过程控制数据清单</title>
</head>
<body>
<table class="easyui-datagrid" title="数据清单列表" id="taskTable" style="height: 500px">

</table>
<div id="tb" style="padding:2px 5px;">
    <a class="easyui-linkbutton" iconCls="icon-myadd" plain="true" func-id="craftDataList-1" style=""  onclick="addTask()">模板导入</a>
    <a class="easyui-linkbutton" iconCls="icon-myadd" plain="true" func-id="craftDataList-2" style=""  onclick="addPMData()">新增</a>
    <a class="easyui-linkbutton" iconCls="icon-myedit" plain="true" func-id="craftDataList-3" style=""  onclick="editTask()">编辑</a>
    <a class="easyui-linkbutton" iconCls="icon-myremove" plain="true" func-id="craftDataList-4" style=""  onclick="delTask()">删除</a>
    <a class="easyui-linkbutton" iconCls="icon-save" plain="true" func-id="craftDataList-5" style=""  onclick="delTask()">保存</a>

</div>


<script>

    var table = undefined;
    var rowdata="";
    // layui.extend({
    //     utils: '../../content/js/utils' // {/}的意思即代表采用自有路径，即不跟随 base 路径
    // }).use(['laydate', 'laypage', 'layer', 'table', 'carousel', 'upload', 'element','utils'], function(){
    //     var laydate = layui.laydate //日期
    //         ,laypage = layui.laypage //分页
    //         ,layer = layui.layer //弹层
    //         ,utils = layui.utils
    //         ,carousel = layui.carousel //轮播
    //         ,upload = layui.upload //上传
    //         ,element = layui.element; //元素操作
    //     table = layui.table ;//表格
    //     //'http://**************:8000/Thingworx/Things/TaskManagementThing/Services/getAllTaskInfo?method=post&appKey=41f1a2e7-2c6e-4602-a9ea-448e053fca22'
    //     //执行一个 table 实例
    //
    // });

</script>
<script>
    $(function () {
        reloadTable();

    });

    function  reloadTable() {
        var dataJson = twxAjax("publishMissionThing","getAllDataListDataByType",{type:"PROCESS_CONTROL_DATA_LIST"});
        $('#taskTable').datagrid({
            data:dataJson.data,
            singleSelect:true,
            fitColumns:true,
            striped:true,
            toolbar:'#tb',
            fit: true,
            columns:[[
                {field: 'ROWNUM', title: '序号'}
                ,{field: 'FILE_TYPE', title: '文件类别'}
                ,{field: 'GATHERING_METHOD', title: '采集方式'}
                ,{field: 'SOURCE_SYSTEM', title: '来源系统'}
                ,{field: 'MODIFIED_TIMESTAMP', title: '修改时间'}
                ,{field: 'DELIVERY_STATE', title: '交付状态'}
            ]]
        });
    }

    function addPMData() {
        layui.layer.open({
            type: 2,
            title:'新增过程管理数据',
            area:["50%",'98%'],
            content: '/Thingworx/DataPackageManagement/components/publishMission/addProcessManagementData.html' ,//这里content是一个普通的String
            end:function () {
                //layer.msg("保存成功！");
                reloadTable();
            }
        });
    }



</script>
</body>
