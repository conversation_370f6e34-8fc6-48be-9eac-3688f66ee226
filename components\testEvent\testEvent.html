<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=8">
    <link rel="Shortcut Icon" href="../../img/favicon.ico">
    <link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all">

    <link rel="stylesheet" href="../../css/icon.css">
    <link rel="stylesheet" href="testEvent.css">

    <script src="../../plugins/common/jquery-3.7.1.min.js"></script>
    <script src="../../plugins/layui-lasted/layui.js"></script>


    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>

    <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
    <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">

    <script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
    <script type="text/javascript" src="../../plugins/index/jquery.fileDownload.js"></script>
<!--    <script type="text/javascript" src="../js/intercept.js"></script>-->
    <script type="text/javascript" src="../js/logUtil.js"></script>
    <title>问题列表</title>
</head>
<body style="overflow: hidden;">
<div class="layui-fluid">
    <div class="layui-row">
        <div class="layui-col-md12">
            <form class="layui-form search-form" lay-filter="search-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">卫星名称</label>
                        <div class="layui-input-inline" style="width: 175px">
                            <select name="model_name" id="model_name" lay-filter="model_name" lay-search></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">测试项目</label>
                        <div class="layui-input-inline" style="width: 300px">
                            <select name="test_item" id="test_item" lay-filter="test_item" lay-search></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">发生时刻</label>
                        <div class="layui-inline" id="range-date">
                            <div class="layui-input-inline" style="width: 130px">
                                <input type="text" autocomplete="off" name="start-date" id="start-date" class="layui-input" placeholder="开始日期">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 130px">
                                <input type="text" autocomplete="off" name="end-date" id="end-date" class="layui-input" placeholder="结束日期">
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">问题分类</label>
                        <div class="layui-input-inline" style="width: 130px">
                            <select name="problem_category" id="problem_category" lay-filter="problem_category"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">闭环情况</label>
                        <div class="layui-input-inline" style="width: 128px">
                            <select name="closure_status" id="closure_status" lay-filter="closure_status"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="event-table-search" style="margin-left: 10px">搜索</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
                        <button class="layui-btn layui-btn-sm layui-bg-blue" id="sync-data">同步数据</button>
                        <button class="layui-btn layui-btn-sm layui-bg-blue" id="export-data">导出</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="layui-row">
        <div class="layui-col-md12">
            <table id="event-table" lay-filter="event-table"></table>
        </div>
    </div>
</div>
<script type="text/javascript" src="js/table.js"></script>
<script type="text/javascript" src="testEvent.js"></script>
</body>