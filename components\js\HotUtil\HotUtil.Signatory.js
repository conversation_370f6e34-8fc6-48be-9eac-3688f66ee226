/**
 * HotUtil.Signatory.js - Handsontable工具类 (签署人管理模块)
 *
 * 负责设定和管理单元格的指定签署人。
 */

/**
 * 设置或取消设置选中单元格为签署框
 * @param {Object} hot Handsontable实例
 * @param {Array} selection 选择区域
 */
HotUtil.setSignBox = function (hot, selection) {
    if (!selection || selection.length === 0) {
        return;
    }

    // 获取选中区域的起始和结束行列
    var startRow = selection[0].start.row;
    var startCol = selection[0].start.col;
    var endRow = selection[0].end.row;
    var endCol = selection[0].end.col;

    // 检查选中区域是否包含已设置签署框的单元格
    var hasSignBox = false;
    var operation = '设置';

    // 首先检查是否有单元格已设置为签署框
    for (var row = startRow; row <= endRow; row++) {
        for (var col = startCol; col <= endCol; col++) {
            var currentClassName = hot.getCellMeta(row, col).className || '';
            if (currentClassName.indexOf('sign-box') !== -1) {
                hasSignBox = true;
                break;
            }
        }
        if (hasSignBox) break;
    }

    // 根据检查结果决定是设置还是取消设置
    for (var row = startRow; row <= endRow; row++) {
        for (var col = startCol; col <= endCol; col++) {
            var currentClassName = hot.getCellMeta(row, col).className || '';

            if (hasSignBox) {
                // 如果有签署框，则取消设置（但不解除锁定状态）
                if (currentClassName.indexOf('sign-box') !== -1) {
                    var newClassName = currentClassName.replace(/\s*sign-box\s*/g, ' ').trim();
                    hot.setCellMeta(row, col, 'className', newClassName);
                    operation = '取消设置';
                    // 取消设定签署框后不解除锁定状态，保持原有的readOnly属性
                }
            } else {
                // 如果没有签署框，则设置并自动锁定
                if (currentClassName.indexOf('sign-box') === -1) {
                    var newClassName = currentClassName + ' sign-box';
                    hot.setCellMeta(row, col, 'className', newClassName.trim());
                    // 设定为签署框时自动锁定单元格
                    hot.setCellMeta(row, col, 'readOnly', true);
                }
            }
        }
    }

    // 重新渲染表格
    hot.render();

    // 显示提示
    layer.msg('已' + operation + '签署框，请保存表格');
};

/**
 * 设定签署人员
 * @param {Object} hot Handsontable实例
 * @param {Array} selection 选择区域
 */
HotUtil.setSignUsers = function (hot, selection) {
    if (!selection || selection.length === 0) {
        return;
    }

    // 获取选中区域的起始和结束行列
    var startRow = selection[0].start.row;
    var startCol = selection[0].start.col;
    var endRow = selection[0].end.row;
    var endCol = selection[0].end.col;

    // 检查选中区域是否包含签署框类型的单元格
    var hasSignBox = false;
    for (var row = startRow; row <= endRow; row++) {
        for (var col = startCol; col <= endCol; col++) {
            var currentClassName = hot.getCellMeta(row, col).className || '';
            if (currentClassName.indexOf('sign-box') !== -1) {
                hasSignBox = true;
                break;
            }
        }
        if (hasSignBox) break;
    }

    if (!hasSignBox) {
        layer.alert('请先选择签署框类型的单元格！', {
            icon: 2
        });
        return;
    }

    // 打开人员选择弹窗
    HotUtil.openSignUsersDialog(hot, selection);
};

/**
 * 打开签署人员选择弹窗
 * @param {Object} hot Handsontable实例
 * @param {Array} selection 选择区域
 */
HotUtil.openSignUsersDialog = function (hot, selection) {
    // 将selectedUsers存储在全局变量中，以便在弹窗中访问
    window.currentSelectedUsers = []; // 存储选中的用户
    var currentCellMeta = null; // 当前单元格的meta信息

    // 获取当前选中单元格的签名信息（用于回显）
    if (selection && selection.length > 0) {
        var row = selection[0].start.row;
        var col = selection[0].start.col;
        currentCellMeta = hot.getCellMeta(row, col);
        if (currentCellMeta.signUsers) {
            window.currentSelectedUsers = JSON.parse(JSON.stringify(currentCellMeta.signUsers));
        }
    }

    const dialogContent = `
        <div style="padding: 15px; height: 100%;">
            <div style="display: flex; height: 100%;">
                <div style="width: 50%; padding-right: 10px;">
                    <div style="margin-bottom: 15px; height: 65px;">
                        <h4 style="margin: 0 0 10px 0;">全部用户列表</h4>
                        <input type="text" id="userFullnameSearch" placeholder="请输入用户全名进行搜索" class="layui-input" style="width: 210px; float: left;">
                        <button type="button" id="searchUserBtn" class="layui-btn" style="float: left; margin-left: 20px;">搜索</button>
                    </div>
                    <table id="allUserTableContainer"></table>
                </div>
                <div style="width: 50%; padding-left: 10px;">
                    <div style="margin-bottom: 15px; height: 65px;">
                        <h4 style="margin: 0 0 10px 0;">已选用户列表</h4>
                    </div>
                    <table id="selectedUserTableContainer"></table>
                </div>
            </div>
            <script type="text/html" id="addUserToolbar">
                <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="add">➔</a>
            </script>
            <script type="text/html" id="removeUserToolbar">
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="remove">✖</a>
            </script>
        </div>
    `;

    layer.open({
        title: '设定签署人员',
        type: 1,
        area: ['1000px', '680px'],
        content: dialogContent,
        btn: ['确定', '取消'],
        yes: function (index) {
            // 确定按钮回调
            HotUtil.saveSignUsers(hot, selection, window.currentSelectedUsers);
            layer.close(index);
        },
        btn2: function () {
            // 取消按钮回调
            return true;
        },
        success: function () {
            // 弹窗打开成功后的回调
            HotUtil.initSignUsersDialog(window.currentSelectedUsers);
        }
    });
};

/**
 * 初始化签署人员选择弹窗
 * @param {Array} selectedUsers 已选择的用户列表
 */
HotUtil.initSignUsersDialog = function (selectedUsers) {
    // 初始化左侧全部用户表格
    HotUtil.renderAllUserTable();

    // 初始化右侧已选用户表格
    HotUtil.renderSelectedUserTable();

    // 绑定搜索事件
    $('#searchUserBtn').on('click', function () {
        var fullname = $('#userFullnameSearch').val();
        table.reload('allUserTable', {
            where: {
                fullname: fullname
            },
            page: {
                curr: 1 // 重置页码为第1页，确保搜索结果能正确显示
            }
        });
    });

    // 绑定回车搜索事件
    $('#userFullnameSearch').on('keypress', function (e) {
        if (e.which === 13) {
            $('#searchUserBtn').click();
        }
    });

    // 监听左侧表格工具栏事件（添加用户）
    table.on('tool(allUserTable)', function (obj) {
        if (obj.event === 'add') {
            HotUtil.addUserToSelected(obj.data);
        }
    });

    // 监听右侧表格工具栏事件（删除用户）
    table.on('tool(selectedUserTable)', function (obj) {
        if (obj.event === 'remove') {
            HotUtil.removeUserFromSelected(obj.data.user_id);
        }
    });
};

/**
 * 渲染全部用户表格（左侧）
 */
HotUtil.renderAllUserTable = function () {
    table.render({
        elem: '#allUserTableContainer',
        id: 'allUserTable',
        url: getUrl('Thing.Fn.SystemManagement', 'QuerySignUserPage'),
        height: 472,
        page: {
            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
            groups: 1,
            first: false,
            last: false
        },
        cols: [[
            { type: 'numbers', title: '序号', width: 60 },
            { field: 'USER_ID', title: 'ID', hide: true },
            { field: 'USER_NAME', title: '用户名' },
            { field: 'USER_FULLNAME', title: '全名', width: 100 },
            { field: 'USER_WORKNO', title: '工号', width: 108 },
            { field: 'operation', align: 'center', title: '操作', width: 60, toolbar: '#addUserToolbar' }
        ]]
    });
};

/**
 * 渲染已选用户表格（右侧）
 */
HotUtil.renderSelectedUserTable = function () {
    table.render({
        elem: '#selectedUserTableContainer',
        id: 'selectedUserTable',
        data: window.currentSelectedUsers || [],
        height: 472,
        page: false,
        cols: [[
            { type: 'numbers', title: '序号', width: 60 },
            {
                field: 'user_info', title: '用户信息', templet: function (d) {
                    return d.user_fullname + '(' + d.user_workno + ')';
                }
            },
            { field: 'operation', title: '操作', width: 60, toolbar: '#removeUserToolbar' }
        ]]
    });
};

/**
 * 添加用户到已选列表
 * @param {Object} userData 用户数据
 */
HotUtil.addUserToSelected = function (userData) {
    var selectedUsers = window.currentSelectedUsers || [];

    // 检查工号是否已存在（去重）
    for (var i = 0; i < selectedUsers.length; i++) {
        if (selectedUsers[i].user_workno === userData.USER_WORKNO) {
            layer.msg('该用户已在选择列表中', { icon: 2 });
            return;
        }
    }

    // 添加用户到已选列表
    var user = {
        user_id: userData.USER_ID,
        user_fullname: userData.USER_FULLNAME,
        user_workno: userData.USER_WORKNO,
        user_name: userData.USER_NAME
    };
    selectedUsers.push(user);
    window.currentSelectedUsers = selectedUsers;

    // 刷新右侧表格
    table.reload('selectedUserTable', {
        data: selectedUsers
    });
};

/**
 * 从已选列表移除用户
 * @param {Number} userId 用户ID
 */
HotUtil.removeUserFromSelected = function (userId) {
    var selectedUsers = window.currentSelectedUsers || [];

    // 从数组中移除用户
    for (var i = selectedUsers.length - 1; i >= 0; i--) {
        if (selectedUsers[i].user_id === userId) {
            selectedUsers.splice(i, 1);
            break;
        }
    }
    window.currentSelectedUsers = selectedUsers;

    // 刷新右侧表格
    table.reload('selectedUserTable', {
        data: selectedUsers
    });
};

/**
 * 保存签署人员信息
 * @param {Object} hot Handsontable实例
 * @param {Array} selection 选择区域
 * @param {Array} selectedUsers 选中的用户列表
 */
HotUtil.saveSignUsers = function (hot, selection, selectedUsers) {
    if (!selection || selection.length === 0) {
        return;
    }

    // 获取选中区域的起始和结束行列
    var startRow = selection[0].start.row;
    var startCol = selection[0].start.col;
    var endRow = selection[0].end.row;
    var endCol = selection[0].end.col;

    // 为选中区域的每个单元格设置签名信息
    for (var row = startRow; row <= endRow; row++) {
        for (var col = startCol; col <= endCol; col++) {
            var currentClassName = hot.getCellMeta(row, col).className || '';
            if (currentClassName.indexOf('sign-box') !== -1) {
                // 存储完整的签名信息到meta对象
                hot.setCellMeta(row, col, 'signUsers', selectedUsers);
            }
        }
    }

    // 重新渲染表格
    hot.render();

    // 显示提示
    layer.msg('已设定签署人员，请保存表格');
};