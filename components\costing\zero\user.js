/**
 * 获取启动流程的下一节点的分配人信息
 */
function getNextFlowNodeByStart(successFn) {
	var loadIndex = layer.load(0);
	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: 'getNextFlowNodeByStart'
		},
	}, true, function(res) {
		layer.close(loadIndex);
		if (res.success) {
			successFn(res.data.assignee);
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('获取启动流程的下一节点的分配人信息请求出错！', {
			icon: 2
		});
	});
}

/**
 * 获取下一节点的分配人信息
 */
function getNextFlowNode(taskId, successFn) {
	var loadIndex = layer.load(0);
	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: 'getNextFlowNode',
			taskId: taskId
		},
	}, true, function(res) {
		layer.close(loadIndex);
		if (res.success) {
			successFn(res.data.assignee);
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('获取下一节点的分配人信息请求出错！', {
			icon: 2
		});
	});
}

/**
 * 获取当前节点的分配人信息
 */
function getCurrentFlowNode(taskId, successFn) {
	var loadIndex = layer.load(0);
	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: 'getCurrentFlowNode',
			taskId: taskId
		},
	}, true, function(res) {
		layer.close(loadIndex);
		if (res.success) {
			successFn(res.data.assignee);
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('获取当前节点的分配人信息请求出错！', {
			icon: 2
		});
	});
}

/**
 * 依据数据字典查询候选用户
 * @param {string} assignee 分配的用户类型 依据数据字典中的流程接收人配置的部门和岗位
 */
function queryCandidateUser(assignee, successFn) {
	twxAjax(THING, 'QueryCandidateUser', {
		assignee: assignee
	}, true, function(res) {
		if (res.success) {
			successFn(res.data);
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('请求分配的用户类型出错！', {
			icon: 2
		});
	});
}
/**
 * 弹出用户选择框
 * @param {string} assignee 分配的用户类型 依据数据字典中的流程接收人配置的部门和岗位
 * @param {Function} submitFn 选择用户之后执行的方法
 */
function alterSelectUser(assignee, submitFn) {
	layer.open({
		title: "选择下一步执行人",
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: true,
		resize: false, //不允许拉伸
		area: ['500px', '300px'],
		scrollbar: false,
		btn: ['确定', '取消'],
		btnAlign: 'c',
		btn1: function(index, layero, that) {
			$("#user-form-submit").click();
		},
		btn2: function(index, layero, that) {
			return true;
		},
		content: '<div class="user-form-content" id="user-form-content"></div>',
		success: function(layero, userLayerIndex, that) {
			$(layero).find('.layui-layer-content').css("overflow", "visible");
			queryCandidateUser(assignee, function(resData) {
				var department = resData.department;
				var job = resData.job;
				var users = resData.users;

				var html = $("#user-form-html")[0].innerHTML;
				$("#user-form-content").append(html);
				$("#department").val(department);
				$("#job").val(job);
				$("#user").append('<option value="">请选择<option>');
				for (var i = 0; i < users.length; i++) {
					$("#user").append('<option value="' + users[i]["USER_NAME"] + '">' + users[i]["USER_FULLNAME"] + '<option>');
				}
				form.render(null, "user-form");

				form.on('submit(user-form-submit)', function(data) {
					var field = data.field; // 获取表单字段值
					layer.close(userLayerIndex);
					submitFn(field.user);
					return false; // 阻止默认 form 跳转
				});

			});
		}
	});

}