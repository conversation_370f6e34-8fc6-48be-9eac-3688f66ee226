/**
 * 现场问题处理单相关图表处理
 * <AUTHOR>
 * @date 2025-04-23
 */

/**
 * 现场问题处理单柱状图提示格式化函数
 * @param {Object} params
 */
var problemTooltipFormatter = function (params) {
    var value = params.value[params.dimensionNames.indexOf(params.seriesName)];
    if (!value || value === '0') {
        return '';
    }
    // 兼容渐变色和纯色
    var dotColor = (typeof params.color === 'object' && params.color.colorStops)
        ? params.color.colorStops[0].color
        : params.color;

    // 创建一个唯一ID，用于饼图容器
    var pieId = 'pie-chart-' + new Date().getTime();

    // 构建包含饼图的tooltip HTML
    var tooltipHtml = '<div style="padding:10px;background:rgba(255,255,255,0.95);border-radius:5px;box-shadow:0 0 10px rgba(0,0,0,0.1);">\
						<div style="font-size:15px;color:#333;font-weight:bold;margin-bottom:8px;border-bottom:1px solid #eee;padding-bottom:6px;">' +
        '<span style="display:inline-block;width:12px;height:12px;border-radius:50%;background:' + dotColor + ';margin-right:6px;vertical-align:middle;"></span>' +
        params.seriesName + '</div>\
						<div style="text-align:center;margin-bottom:10px;">\
							<span style="font-size:16px;color:#333;font-weight:bold;">' + params.name.split("~~~")[0] + '</span>\
							<span style="font-size:16px;color:#333;font-weight:bold;margin-left:5px;">' + value + '</span>\
						</div>\
						<div id="' + pieId + '" style="width:240px;height:180px;margin:0 auto;"></div>\
					</div>';

    // 使用setTimeout创建饼图（因为需要等待DOM元素创建完成）
    setTimeout(function () {
        var pieContainer = document.getElementById(pieId);
        if (!pieContainer) return;

        // 获取状态数据
        var finishedCount = 0;
        var unfinishedCount = 0;
        var total = parseInt(value);
        var legendTitle = '';
        var modelId = params.name.split("~~~")[1];
        var seriesName = params.seriesName;

        // 根据不同系列选择不同的数据和标题
        if (window.problemStatusData) {
            if (seriesName === '全部') {
                legendTitle = '闭环情况';
                finishedCount = window.problemStatusData.closedLoop[modelId] || 0;
                unfinishedCount = window.problemStatusData.openLoop[modelId] || 0;
                // 如果没有闭环/未闭环数据，尝试计算
                if (finishedCount === 0 && unfinishedCount === 0 && total > 0) {
                    // 默认随机数据
                    finishedCount = Math.round(Math.random() * total);
                    unfinishedCount = total - finishedCount;
                }
            } else if (seriesName === '更改设计文件') {
                legendTitle = '完成情况';
                finishedCount = window.problemStatusData.designFinished[modelId] || 0;
                unfinishedCount = window.problemStatusData.designUnfinished[modelId] || 0;
                // 如果没有完成/未完成数据，尝试计算
                if (finishedCount === 0 && unfinishedCount === 0 && total > 0) {
                    // 默认随机数据
                    finishedCount = Math.round(Math.random() * total);
                    unfinishedCount = total - finishedCount;
                }
            } else if (seriesName === '更改工艺文件') {
                legendTitle = '完成情况';
                finishedCount = window.problemStatusData.techFinished[modelId] || 0;
                unfinishedCount = window.problemStatusData.techUnfinished[modelId] || 0;
                // 如果没有完成/未完成数据，尝试计算
                if (finishedCount === 0 && unfinishedCount === 0 && total > 0) {
                    // 默认随机数据
                    finishedCount = Math.round(Math.random() * total);
                    unfinishedCount = total - finishedCount;
                }
            }
        } else {
            // 默认随机数据
            finishedCount = Math.round(Math.random() * total);
            unfinishedCount = total - finishedCount;
            legendTitle = seriesName === '全部' ? '闭环情况' : '完成情况';
        }

        var finishedPercent = total > 0 ? Math.round((finishedCount / total) * 100) : 0;
        var unfinishedPercent = 100 - finishedPercent;

        var finishedLabel = seriesName === '全部' ? '已闭环' : '已完成';
        var unfinishedLabel = seriesName === '全部' ? '未闭环' : '未完成';

        var pieChart = echarts.init(pieContainer);
        pieChart.setOption({
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                formatter: '{b}: {c} ({d}%)',
                backgroundColor: 'rgba(255,255,255,0.8)',
                borderColor: '#ccc',
                borderWidth: 1,
                textStyle: {
                    color: '#333'
                }
            },
            legend: {
                orient: 'vertical',
                right: '0%',
                top: 'middle',
                itemWidth: 10,
                itemHeight: 10,
                itemGap: 10,
                textStyle: {
                    color: '#666',
                    fontSize: 12
                },
                formatter: function (name) {
                    if (name === finishedLabel) {
                        return name + ' ' + finishedCount + '(' + finishedPercent + '%)';
                    } else {
                        return name + ' ' + unfinishedCount + '(' + unfinishedPercent + '%)';
                    }
                },
                data: [finishedLabel, unfinishedLabel]
            },
            series: [{
                name: legendTitle,
                type: 'pie',
                radius: ['40%', '65%'],
                center: ['28%', '50%'],
                avoidLabelOverlap: true,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: finishedCount, name: finishedLabel, itemStyle: { color: '#67C23A' } },
                    { value: unfinishedCount, name: unfinishedLabel, itemStyle: { color: '#F56C6C' } }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }],
            animation: true
        });
    }, 10);

    return tooltipHtml;
}

/**
 * 处理现场问题处理单柱状图点击事件
 * @param {Object} params 图表点击参数
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 * @param {String} seriesName 系列名称
 */
function problemChartClick(params, startDate, endDate, seriesName) {
    var modelId = params.name.split("~~~")[1];
    var fileType = "现场问题处理单|" + seriesName;
    
    // 定义当前状态值，用于初始化选中状态
    var currentStatus = 'all';
    var chartType = '';

    // 检查是哪种类型的系列被点击
    if (seriesName === '全部') {
        chartType = 'whole';

        // 流程图的状态下拉框 - 全部/已闭环/未闭环
        var flowSelectHtml = '<div class="status-select-container layui-form" style="position:absolute;right:50px;z-index:10;width:200px;">' +
            '<div class="layui-form-item" style="margin-bottom:0;">' +
            '<div class="layui-inline" style="margin-right:0;margin-bottom:0;">' +
            '<span style="color:#fff;font-weight:500;float:left;line-height:30px;margin-right:10px;">状态筛选：</span>' +
            '<div class="model-select" style="width:100px;">' +
            '<select id="flow-status-select" name="flow-status-select" lay-filter="status-select">' +
            '<option value="all"' + (currentStatus === 'all' ? ' selected' : '') + '>全部</option>' +
            '<option value="closed"' + (currentStatus === 'closed' ? ' selected' : '') + '>已闭环</option>' +
            '<option value="open"' + (currentStatus === 'open' ? ' selected' : '') + '>未闭环</option>' +
            '</select>' +
            '</div>' +
            '</div>' +
            '</div>' +
            '</div>';

        // 详细表格的状态下拉框，并增加导出按钮
        var tableSelectHtml = '<div class="status-select-container layui-form" style="position:absolute;right:50px;z-index:10;width:285px;">' +
            '<div class="layui-form-item" style="margin-bottom:0;">' +
            '<div class="layui-inline" style="margin-right:0;margin-bottom:0;">' +
            '<span style="color:#fff;font-weight:500;float:left;line-height:30px;margin-right:10px;">状态筛选：</span>' +
            '<div class="model-select" style="width:100px;float:left;">' +
            '<select id="table-status-select" name="table-status-select" lay-filter="status-select">' +
            '<option value="all"' + (currentStatus === 'all' ? ' selected' : '') + '>全部</option>' +
            '<option value="closed"' + (currentStatus === 'closed' ? ' selected' : '') + '>已闭环</option>' +
            '<option value="open"' + (currentStatus === 'open' ? ' selected' : '') + '>未闭环</option>' +
            '</select>' +
            '</div>' +
            '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="export-table-btn" style="margin-left:10px;margin-top:0px;float:left;">' +
            '<i class="layui-icon layui-icon-export"></i> 导出数据' +
            '</button>' +
            '</div>' +
            '</div>' +
            '</div>';

    } else if (seriesName === '更改设计文件' || seriesName === '更改工艺文件') {
        chartType = seriesName === '更改设计文件' ? 'design' : 'tech';

        // 流程图的状态下拉框 - 全部/已完成/未完成
        var flowSelectHtml = '<div class="status-select-container layui-form" style="position:absolute;right:50px;z-index:10;width:200px;">' +
            '<div class="layui-form-item" style="margin-bottom:0;">' +
            '<div class="layui-inline" style="margin-right:0;margin-bottom:0;">' +
            '<span style="color:#fff;font-weight:500;float:left;line-height:30px;margin-right:10px;">状态筛选：</span>' +
            '<div class="model-select" style="width:100px;">' +
            '<select id="flow-status-select" name="flow-status-select" lay-filter="status-select">' +
            '<option value="all"' + (currentStatus === 'all' ? ' selected' : '') + '>全部</option>' +
            '<option value="finished"' + (currentStatus === 'finished' ? ' selected' : '') + '>已完成</option>' +
            '<option value="unfinished"' + (currentStatus === 'unfinished' ? ' selected' : '') + '>未完成</option>' +
            '</select>' +
            '</div>' +
            '</div>' +
            '</div>' +
            '</div>';

        // 详细表格的状态下拉框，并增加导出按钮
        var tableSelectHtml = '<div class="status-select-container layui-form" style="position:absolute;right:50px;z-index:10;width:285px;">' +
            '<div class="layui-form-item" style="margin-bottom:0;">' +
            '<div class="layui-inline" style="margin-right:0;margin-bottom:0;">' +
            '<span style="color:#fff;font-weight:500;float:left;line-height:30px;margin-right:10px;">状态筛选：</span>' +
            '<div class="model-select" style="width:100px;float:left;">' +
            '<select id="table-status-select" name="table-status-select" lay-filter="status-select">' +
            '<option value="all"' + (currentStatus === 'all' ? ' selected' : '') + '>全部</option>' +
            '<option value="finished"' + (currentStatus === 'finished' ? ' selected' : '') + '>已完成</option>' +
            '<option value="unfinished"' + (currentStatus === 'unfinished' ? ' selected' : '') + '>未完成</option>' +
            '</select>' +
            '</div>' +
            '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="export-table-btn" style="margin-left:10px;margin-top:0px;float:left;">' +
            '<i class="layui-icon layui-icon-export"></i> 导出数据' +
            '</button>' +
            '</div>' +
            '</div>' +
            '</div>';
    }

    var filterBtns = {
        flow: flowSelectHtml,
        table: tableSelectHtml,
        chartType: chartType // 存储当前柱状图类型，用于状态筛选
    };

    showDetailTab(modelId, startDate, endDate, fileType, filterBtns);
}

/**
 * 加载现场问题处理单柱状图
 * @param {String} modelId 模型ID
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 */
function loadProblemChart(modelId, startDate, endDate) {
    loadHandleChart(modelId, startDate, endDate, "现场问题处理单", "xcwt-chart", "QueryProblemCount");
} 