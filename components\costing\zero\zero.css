.zero-tab {
	margin: 0;
}

.zero-tab .layui-tab-content {
	padding: 0;
}

.zero-form {
	width: 1000px;
}

.zero-form .layui-textarea[readonly="readonly"] {
	resize: none;
}

.zero-form .layui-form-label {
	width: 120px;
}

.zero-form .layui-input-block {
	margin-left: 150px;
}

.zero-form .layui-input,
.zero-form .layui-textarea {
	width: 824px;
}

.zero-form .has-suffix {
	width: 673px;
}

.zero-form .layui-inline .layui-input {
	width: 330px;
}


.zero-form .layui-input-suffix {
	width: 152px;
	right: 26px;
	text-align: left;
}

.zero-form .layui-input-suffix div {
	position: absolute;
	bottom: 0;
}

.zero-form .layui-input-suffix div p {
	line-height: 30px;
}

.form-content {
	padding: 15px 0px 0px 15px;
}


.record-content {
	padding-left: 30px;
	padding-top: 30px;
}

.record-content p span {
	padding: 0px 20px;
}

.record-content .layui-icon-time {
	color: #c2c2c2
}

.record-content .layui-icon-backspace {
	color: #ff5722
}

.chat-content {
	width: 100%;
	height: 100%;
}

.chat-content .canvas {
	width: 100%;
	height: 100%;
}

.bjs-powered-by {
	display: none;
}

.highlight.djs-shape .djs-visual> :nth-child(1) {
	fill: green !important;
	stroke: green !important;
	fill-opacity: 0.2 !important;
}

.highlight.djs-shape .djs-visual> :nth-child(2) {
	fill: green !important;
}

.highlight.djs-shape .djs-visual>path {
	fill: green !important;
	fill-opacity: 0.2 !important;
	stroke: green !important;
}

.highlight.djs-connection>.djs-visual>path {
	stroke: green !important;
}

.highlight-todo.djs-connection>.djs-visual>path {
	stroke: orange !important;
	stroke-dasharray: 4px !important;
	fill-opacity: 0.2 !important;
}

.highlight-todo.djs-shape .djs-visual> :nth-child(1) {
	fill: orange !important;
	stroke: orange !important;
	stroke-dasharray: 4px !important;
	fill-opacity: 0.2 !important;
}

.user-form-content {
	padding: 15px;
}


.upload-choose {
	float: left;
	margin-top: 4px;
	margin-right: 15px;
}

.upload-name {
	float: left;
	width: 600px !important;
}

.upload-msg {
	float: left;
	margin-left: 15px;
	margin-top: 9.75px;
}

.upload-msg .layui-icon-success {
	color: #16b777;
}

.upload-msg .layui-icon-error {
	color: #ff5722;
}

.upload-preview {
	float: left;
	margin-left: 15px;
	margin-top: 4px;
}