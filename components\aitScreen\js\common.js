var loadingOption = {
    text: '加载中',
    // color: '#31c218',
    // textColor: '#000',
    // maskColor: 'rgba(255, 255, 255, 0.5)',
    zlevel: 0,

    // 字体大小。从 `v4.8.0` 开始支持。
    fontSize: 16,
    // 是否显示旋转动画（spinner）。从 `v4.8.0` 开始支持。
    showSpinner: true,
    // 旋转动画（spinner）的半径。从 `v4.8.0` 开始支持。
    spinnerRadius: 10,
    // 旋转动画（spinner）的线宽。从 `v4.8.0` 开始支持。
    lineWidth: 5,
    // 字体粗细。从 `v5.0.1` 开始支持。
    fontWeight: 'normal',
    // 字体风格。从 `v5.0.1` 开始支持。
    fontStyle: 'normal',
    // 字体系列。从 `v5.0.1` 开始支持。
    fontFamily: 'sans-serif'
};

/**
 * 柱状图的x轴显示标签格式化函数
 * @param {Object} value
 */
var xAxisLabelFormatter = function (value) {
    // 将标签文本截断为4个字符
    return value.split("~~~")[0];
};


function previewTempList(tableHeader, htmlData, saveData) {
    var htmlDataKey = parseInt(new Date().getTime() * Math.random());
    var tableHeaderKey = parseInt(new Date().getTime() * Math.random());
    var saveDataKey = parseInt(new Date().getTime() * Math.random());
    sessionStorage.setItem(tableHeaderKey, tableHeader);
    sessionStorage.setItem(saveDataKey, saveData);
    sessionStorage.setItem(htmlDataKey, htmlData);
    window.open("/DataPackageManagement/components/aitScreen/viewTable.html?htmlDataKey=" + htmlDataKey + "&tableHeaderKey=" + tableHeaderKey + "&saveDataKey=" + saveDataKey);
}

function previewLinkList(srcId, srcTableName) {
    twxAjax('Thing.Fn.ListData', 'QueryLinkListData', {
        srcId: srcId,
        srcTableName: srcTableName
    }, true, function (res) {
        if (res.success) {
            var datas = res.data;
            if (datas.length > 0) {
                layer.open({
                    title: false,
                    type: 1,
                    anim: false,
                    openDuration: 200,
                    isOutAnim: false,
                    closeDuration: 200,
                    shadeClose: false,
                    maxmin: false,
                    resize: false,
                    skin: 'my-layer',
                    area: ['1400px', '600px'],
                    content: '<div id="viewLinkTable"></div>',
                    success: function () {

                        table.render({
                            id: "viewLinkTable",
                            elem: '#viewLinkTable',
                            skin: "nob",
                            data: datas,
                            height: 598,
                            page: {
                                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                                groups: 1,
                                first: false,
                                last: false
                            },
                            cols: [
                                [{
                                    title: '序号',
                                    type: 'numbers',
                                    width: 60
                                }, {
                                    field: 'FILE_TYPE',
                                    title: '文件类别',
                                    width: 300
                                }, {
                                    field: 'FILE_NAME',
                                    title: '文件名称',
                                    width: 400
                                }, {
                                    field: 'GATHERING_METHOD',
                                    title: '采集方式',
                                    width: 100
                                }, {
                                    field: 'SOURCE_SYSTEM',
                                    title: '来源系统',
                                    width: 100
                                }, {
                                    field: 'FILEPATH',
                                    title: '操作',
                                    templet: function (d) {
                                        var html = '<span style="cursor: pointer;" class="layui-badge layui-bg-blue" onClick = "previewList(\'' +
                                            d.ID + '\',\'' + d.TABLENAME + '\',\'' + d.FILEPATH + '\',\'' + d.FILE_NAME + '\',\'' +
                                            d.FILE_FORMAT + '\',\'' + d.FILE_TYPE + '\',\'' + d.SOURCE_SYSTEM + '\',\'' + d.GATHERING_METHOD + '\')">查看</span>';
                                        return html;
                                    }
                                }]
                            ],
                            even: true
                        });
                    }
                });
            } else {
                layer.msg('未发现关联的更改单！');
            }
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (xhr, textStatus, errorThrown) {
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}