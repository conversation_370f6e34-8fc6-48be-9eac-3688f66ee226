{"version": 3, "file": "docx-preview.min.js", "mappings": "sHAAA,eACA,SACA,SACA,SAOA,MAAaA,EAGT,WAAAC,CAAoBC,EAAoBC,GAApB,KAAAD,KAAAA,EAAoB,KAAAC,QAAAA,EAFxC,KAAAC,UAAuB,IAAI,EAAAC,SAG3B,CAEA,GAAAC,CAAIC,GACA,OAAOC,KAAKN,KAAKO,MAsCzB,SAAuBF,GACnB,OAAOA,EAAKG,WAAW,KAAOH,EAAKI,OAAO,GAAKJ,CACnD,CAxC+BK,CAAcL,GACzC,CAEA,MAAAM,CAAON,EAAcO,GACjBN,KAAKN,KAAKa,KAAKR,EAAMO,EACzB,CAEA,iBAAaE,CAAKC,EAAmBd,GACjC,MAAMe,QAAYC,EAAMC,UAAUH,GACxC,OAAO,IAAIjB,EAAekB,EAAKf,EAC7B,CAEA,IAAAkB,CAAKC,EAAY,QACb,OAAOd,KAAKN,KAAKqB,cAAc,CAAED,QACrC,CAEA,IAAAN,CAAKT,EAAce,EAAyB,UACxC,OAAOd,KAAKF,IAAIC,IAAOiB,MAAMF,IAASG,QAAQC,QAAQ,KAC1D,CAEA,uBAAMC,CAAkBpB,EAAe,MACnC,IAAIqB,EAAW,cAEf,GAAY,MAARrB,EAAc,CACd,MAAOsB,EAAGC,IAAM,IAAAC,WAAUxB,GAC1BqB,EAAW,GAAGC,UAAUC,Q,CAG5B,MAAME,QAAYxB,KAAKQ,KAAKY,GAClC,OAAOI,GAAM,IAAAC,oBAAmBzB,KAAK0B,iBAAiBF,GAAKG,kBAAmB3B,KAAKJ,WAAa,IAC9F,CAGA,gBAAA8B,CAAiBF,GACb,OAAO,IAAAI,gBAAeJ,EAAKxB,KAAKL,QAAQkC,mBAC5C,EA1CJ,kB,+ECVA,eAIA,aAKI,WAAApC,CAAsBqC,EAAiC/B,GAAjC,KAAA+B,SAAAA,EAAiC,KAAA/B,KAAAA,CACvD,CAEA,UAAMS,GACRR,KAAK+B,WAAa/B,KAAK8B,SAASX,kBAAkBnB,KAAKD,MAEvD,MAAMiC,QAAgBhC,KAAK8B,SAAStB,KAAKR,KAAKD,MACxCkC,EAASjC,KAAK8B,SAASJ,iBAAiBM,GAE1ChC,KAAK8B,SAASnC,QAAQuC,aACzBlC,KAAKmC,aAAeF,GAGrBjC,KAAKoC,SAASH,EAAON,kBACnB,CAEA,IAAAd,GACIb,KAAK8B,SAASzB,OAAOL,KAAKD,MAAM,IAAAsC,oBAAmBrC,KAAKmC,cAC5D,CAEU,QAAAC,CAASE,GACnB,E,cCrBJ,IAAYC,E,iGAAZ,SAAYA,GACR,sGACA,4FACA,oFACA,4FACA,sFACA,+FACA,oFACA,0FACA,gGACA,4FACA,4FACH,0FACG,sFACA,sFACA,+GACA,yGACH,4GACA,CAlBD,CAAYA,IAAiB,oBAAjBA,EAAiB,KAoB7B,8BAAmCD,EAAeE,GAC9C,OAAOA,EAAIC,SAASH,GAAMI,KAAIC,IAAK,CAC/BC,GAAIJ,EAAIK,KAAKF,EAAG,MAChB7B,KAAM0B,EAAIK,KAAKF,EAAG,QAClBG,OAAQN,EAAIK,KAAKF,EAAG,UACpBI,WAAYP,EAAIK,KAAKF,EAAG,iBAEhC,C,iGCpCA,eAMA,SACA,QACA,SACA,SACA,SAGA,SACA,SAEW,EAAAK,MAAQ,CAClBC,IAAK,UACLC,MAAO,QACPC,YAAa,QACbC,UAAW,eAGZ,MAAMC,EAAyB,GAEzBC,EAAY,CACjB,MAAS,EAAAC,QAAQC,QACjB,UAAa,EAAAD,QAAQE,iBACrB,EAAK,EAAAF,QAAQG,YACb,KAAQ,EAAAH,QAAQI,YAChB,MAAS,EAAAJ,QAAQK,gBACjB,IAAO,EAAAL,QAAQM,aACf,IAAO,EAAAN,QAAQO,eACf,IAAO,EAAAP,QAAQQ,WACf,IAAO,EAAAR,QAAQS,UACf,EAAK,EAAAT,QAAQU,QACb,KAAQ,EAAAV,QAAQW,eAChB,KAAQ,EAAAX,QAAQY,aAChB,KAAQ,EAAAZ,QAAQa,eAChB,IAAO,EAAAb,QAAQc,iBACf,IAAO,EAAAd,QAAQe,eACf,EAAK,EAAAf,QAAQgB,aACb,KAAQ,EAAAhB,QAAQiB,QAChB,MAAS,EAAAjB,QAAQkB,iBACjB,IAAO,EAAAlB,QAAQmB,SACf,OAAU,EAAAnB,QAAQoB,cAClB,EAAK,EAAApB,QAAQqB,UACb,GAAM,EAAArB,QAAQsB,aACd,IAAO,EAAAtB,QAAQuB,OACf,IAAO,EAAAvB,QAAQwB,OACf,SAAY,EAAAxB,QAAQyB,cAQrB,uBAGC,WAAAvF,CAAYE,GACXK,KAAKL,QAAU,CACdsF,aAAa,EACbC,OAAO,KACJvF,EAEL,CAEA,UAAAwF,CAAWlD,EAAiBmD,EAAkBC,GAC7C,IAAIC,EAAS,GAEb,IAAK,IAAIC,KAAM,UAAI9C,SAASR,EAAQmD,GAAW,CAC9C,MAAMI,EAAO,IAAIH,EACjBG,EAAK5C,GAAK,UAAIC,KAAK0C,EAAI,MACvBC,EAAKC,SAAW,UAAI5C,KAAK0C,EAAI,QAC7BC,EAAKE,SAAW1F,KAAK2F,kBAAkBJ,GACvCD,EAAOM,KAAKJ,E,CAGb,OAAOF,CACR,CAEA,iBAAAO,CAAkB5D,GACjB,IAAI6D,EAAQ,UAAIC,QAAQ9D,EAAQ,QAC5B+D,EAAa,UAAID,QAAQ9D,EAAQ,cACjCgE,EAAS,UAAIF,QAAQD,EAAO,UAEhC,MAAO,CACNhF,KAAM,EAAAyC,QAAQ2C,SACdR,SAAU1F,KAAK2F,kBAAkBG,GACjCK,MAAOF,GAAS,IAAAG,wBAAuBH,EAAQ,WAAO,CAAC,EACvDI,SAAUL,EAAahG,KAAKsG,gBAAgBN,GAAc,CAAC,EAE7D,CAEA,eAAAM,CAAgBC,GACf,IAAIjB,EAAS,CAAC,EACVpC,EAAQsD,EAAQC,UAAUF,EAAM,SAMpC,OAJIrD,IACHoC,EAAO,oBAAsBpC,GAGvBoC,CACR,CAEA,iBAAAK,CAAkBI,GACjB,IAAIL,EAAW,GAEf,IAAK,IAAIa,KAAQ,UAAI9D,SAASsD,GAC7B,OAAQQ,EAAKG,WACZ,IAAK,IACJhB,EAASE,KAAK5F,KAAK2G,eAAeJ,IAClC,MAED,IAAK,MACJb,EAASE,KAAK5F,KAAK4G,WAAWL,IAC9B,MAED,IAAK,MACJb,EAASE,QAAQ5F,KAAK6G,SAASN,GAAM5D,GAAK3C,KAAK2F,kBAAkBhD,MAKpE,OAAO+C,CACR,CAEA,eAAAoB,CAAgBC,GACf,IAAIzB,EAAS,GAcb,OAZAkB,EAAQQ,QAAQD,GAASE,IACxB,OAAQA,EAAEP,WACT,IAAK,QACJpB,EAAOM,KAAK5F,KAAKkH,WAAWD,IAC5B,MAED,IAAK,cACJ3B,EAAOM,KAAK5F,KAAKmH,mBAAmBF,I,IAKhC3B,CACR,CAEA,kBAAA6B,CAAmB3B,GAClB,IAAIF,EAAoB,CACvB1C,GAAI,KACJwE,KAAM,KACNtE,OAAQ,KACRuE,QAAS,KACTC,OAAQ,IA2BT,OAxBAd,EAAQQ,QAAQxB,GAAM+B,IACrB,OAAQA,EAAEb,WACT,IAAK,aACJ,IAAIc,EAAM,UAAIzB,QAAQwB,EAAG,OAErBC,GACHlC,EAAOgC,OAAO1B,KAAK,CAClB9C,OAAQ,OACR2E,OAAQzH,KAAK0H,uBAAuBF,EAAK,CAAC,KAE5C,MAED,IAAK,aACJ,IAAIG,EAAM,UAAI5B,QAAQwB,EAAG,OAErBI,GACHrC,EAAOgC,OAAO1B,KAAK,CAClB9C,OAAQ,IACR2E,OAAQzH,KAAK0H,uBAAuBC,EAAK,CAAC,K,IAMxCrC,CACR,CAEA,UAAA4B,CAAW1B,GACV,IAAIF,EAAoB,CACvB1C,GAAI,UAAIC,KAAK2C,EAAM,WACnBoC,UAAW,UAAIC,SAASrC,EAAM,WAC9B4B,KAAM,KACNtE,OAAQ,KACRuE,QAAS,KACTC,OAAQ,GACRQ,OAAQ,MAGT,OAAQ,UAAIjF,KAAK2C,EAAM,SACtB,IAAK,YAAaF,EAAOxC,OAAS,IAAK,MACvC,IAAK,QAASwC,EAAOxC,OAAS,QAAS,MACvC,IAAK,YAAawC,EAAOxC,OAAS,OAsEnC,OAlEA0D,EAAQQ,QAAQxB,GAAMyB,IACrB,OAAQA,EAAEP,WACT,IAAK,UACJpB,EAAO+B,QAAU,UAAIxE,KAAKoE,EAAG,OAC7B,MAED,IAAK,OACJ3B,EAAO8B,KAAO,UAAIvE,KAAKoE,EAAG,OAC1B,MAED,IAAK,OACJ3B,EAAOwC,OAAS,UAAIjF,KAAKoE,EAAG,OAC5B,MAED,IAAK,OACJ3B,EAAOyC,KAAO,UAAIlF,KAAKoE,EAAG,OAC1B,MAED,IAAK,UACJ3B,EAAO0C,QAAU,UAAInF,KAAKoE,EAAG,OAAOgB,MAAM,KAC1C,MAED,IAAK,MACJ3C,EAAOgC,OAAO1B,KAAK,CAClB9C,OAAQ,IACR2E,OAAQzH,KAAK0H,uBAAuBT,EAAG,CAAC,KAEzC3B,EAAO4C,gBAAiB,IAAAC,0BAAyBlB,EAAG,WACpD,MAED,IAAK,MACJ3B,EAAOgC,OAAO1B,KAAK,CAClB9C,OAAQ,OACR2E,OAAQzH,KAAK0H,uBAAuBT,EAAG,CAAC,KAEzC3B,EAAO8C,UAAW,IAAAC,oBAAmBpB,EAAG,WACxC,MAED,IAAK,QACL,IAAK,OACJ3B,EAAOgC,OAAO1B,KAAK,CAClB9C,OAAQ,KACR2E,OAAQzH,KAAK0H,uBAAuBT,EAAG,CAAC,KAEzC,MAED,IAAK,aACJ,IAAK,IAAIqB,KAAKtI,KAAKuI,gBAAgBtB,GAClC3B,EAAOgC,OAAO1B,KAAK0C,GACpB,MAED,IAAK,OACL,IAAK,UACL,IAAK,SACL,IAAK,aACL,IAAK,iBACL,IAAK,eACL,IAAK,aAEJ,MAED,QACCtI,KAAKL,QAAQuF,OAASsD,QAAQC,KAAK,gCAAgCxB,EAAEP,a,IAIjEpB,CACR,CAEA,eAAAiD,CAAgB/C,GACf,IAAIF,EAAS,GAETxE,EAAO,UAAI+B,KAAK2C,EAAM,QACtBkD,EAAW,GACXC,EAAc,GAElB,OAAQ7H,GACP,IAAK,WACJ6H,EAAc,aACdD,EAAW,kBACX,MACD,IAAK,UACJC,EAAc,YACdD,EAAW,iBACX,MACD,IAAK,WACJC,EAAc,aACdD,EAAW,eACX,MACD,IAAK,UACJC,EAAc,YACdD,EAAW,cACX,MACD,IAAK,YACJC,EAAc,kBACdD,EAAW,aACX,MACD,IAAK,YACJC,EAAc,kBACdD,EAAW,cACX,MACD,IAAK,YACJC,EAAc,kBACdD,EAAW,aACX,MACD,IAAK,YACJC,EAAc,kBACdD,EAAW,cACX,MACD,QAAS,MAAO,GAgCjB,OA7BAlC,EAAQQ,QAAQxB,GAAMyB,IACrB,OAAQA,EAAEP,WACT,IAAK,MACJpB,EAAOM,KAAK,CACX9C,OAAQ,GAAG4F,MACXE,IAAKD,EACLlB,OAAQzH,KAAK0H,uBAAuBT,EAAG,CAAC,KAEzC,MAED,IAAK,MACJ3B,EAAOM,KAAK,CACX9C,OAAQ,GAAG4F,SACXE,IAAKD,EACLlB,OAAQzH,KAAK0H,uBAAuBT,EAAG,CAAC,KAEzC,MAED,IAAK,QACL,IAAK,OACJ3B,EAAOM,KAAK,CACX9C,OAAQ4F,EACRE,IAAKD,EACLlB,OAAQzH,KAAK0H,uBAAuBT,EAAG,CAAC,K,IAMrC3B,CACR,CAEA,kBAAAuD,CAAmBC,GAClB,IAAIxD,EAAS,GACTyD,EAAU,CAAC,EACXC,EAAU,GAuBd,OArBAxC,EAAQQ,QAAQ8B,GAAO7B,IACtB,OAAQA,EAAEP,WACT,IAAK,cACJ1G,KAAKiJ,uBAAuBhC,EAAG+B,GAC7BE,SAAQC,GAAK7D,EAAOM,KAAKuD,KAC3B,MAED,IAAK,eACJH,EAAQpD,KAAK5F,KAAKoJ,wBAAwBnC,IAC1C,MAED,IAAK,MACJ,IAAIoC,EAAQ,UAAIxG,KAAKoE,EAAG,SACpBqC,EAAgB,UAAIC,YAAYtC,EAAG,gBAAiB,OACxD8B,EAAQO,GAAiBD,E,IAK5B/D,EAAO4D,SAAQC,GAAKA,EAAEvG,GAAKmG,EAAQI,EAAEvG,MAE9B0C,CACR,CAEA,uBAAA8D,CAAwB7C,GACvB,IAAIiD,EAAO,UAAIzD,QAAQQ,EAAM,QACzBkD,EAAQD,GAAQ,UAAIzD,QAAQyD,EAAM,SAClCE,EAAYD,GAAS,UAAI1D,QAAQ0D,EAAO,aAE5C,OAAOC,EAAY,CAClB9G,GAAI,UAAI+G,QAAQpD,EAAM,kBACtBqD,IAAK,UAAI/G,KAAK6G,EAAW,MACzBG,MAAO,UAAIhH,KAAK4G,EAAO,UACpB,IACL,CAEA,sBAAAR,CAAuBzD,EAAewD,GACrC,IAAI1D,EAAS,GACT1C,EAAK,UAAIC,KAAK2C,EAAM,iBAUxB,OARAgB,EAAQQ,QAAQxB,GAAMyB,IAEf,QADEA,EAAEP,WAERpB,EAAOM,KAAK5F,KAAK8J,oBAAoBlH,EAAIqE,EAAG+B,G,IAKxC1D,CACR,CAEA,mBAAAwE,CAAoBlH,EAAY4C,EAAewD,GAC9C,IAAI1D,EAAwB,CAC3B1C,GAAIA,EACJmH,MAAO,UAAIJ,QAAQnE,EAAM,QACzBwE,MAAO,EACPC,gBAAYC,EACZC,OAAQ,CAAC,EACTC,OAAQ,CAAC,EACTC,KAAM,OAwCP,OArCA7D,EAAQQ,QAAQxB,GAAMyB,IACrB,OAAQA,EAAEP,WACT,IAAK,QACJpB,EAAO0E,MAAQ,UAAIL,QAAQ1C,EAAG,OAC9B,MAED,IAAK,MACJjH,KAAK0H,uBAAuBT,EAAG3B,EAAO6E,QACtC,MAED,IAAK,MACJnK,KAAK0H,uBAAuBT,EAAG3B,EAAO8E,QACtC,MAED,IAAK,iBACJ,IAAIxH,EAAK,UAAI+G,QAAQ1C,EAAG,OACxB3B,EAAOgF,OAAStB,EAAQuB,MAAKpB,GAAKA,EAAEvG,IAAMA,IAC1C,MAED,IAAK,UACJ0C,EAAOkF,UAAY,UAAI3H,KAAKoE,EAAG,OAC/B,MAED,IAAK,SACJ3B,EAAO2E,WAAa,UAAIpH,KAAKoE,EAAG,OAChC,MAED,IAAK,SACJ3B,EAAOmF,OAAS,UAAI5H,KAAKoE,EAAG,OAC5B,MAED,IAAK,OACJ3B,EAAO+E,KAAO,UAAIxH,KAAKoE,EAAG,O,IAKtB3B,CACR,CAEA,QAAAuB,CAASrB,EAAekF,GACvB,MAAMC,EAAa,UAAI5E,QAAQP,EAAM,cACrC,OAAOmF,EAAaD,EAAOC,GAAc,EAC1C,CAEA,aAAAC,CAAcpF,EAAeqF,GAC5B,MAAuB,CACtB/J,KAAM,EAAAyC,QAAQuH,SACdpF,SAAUmF,EAAarF,IAAOE,UAAY,GAE5C,CAEA,YAAAqF,CAAavF,EAAeqF,GAC3B,MAAuB,CACtB/J,KAAM,EAAAyC,QAAQyH,QACdtF,SAAUmF,EAAarF,IAAOE,UAAY,GAE5C,CAEA,cAAAiB,CAAenB,GACd,IAAIF,EAAuB,CAAExE,KAAM,EAAAyC,QAAQ0H,UAAWvF,SAAU,IAEhE,IAAK,IAAIH,KAAM,UAAI9C,SAAS+C,GAC3B,OAAQD,EAAGmB,WACV,IAAK,MACJ1G,KAAKmI,yBAAyB5C,EAAID,GAClC,MAED,IAAK,IACJA,EAAOI,SAASE,KAAK5F,KAAKkL,SAAS3F,EAAID,IACvC,MAED,IAAK,YACJA,EAAOI,SAASE,KAAK5F,KAAKmL,eAAe5F,EAAID,IAC7C,MAED,IAAK,gBACJA,EAAOI,SAASE,MAAK,IAAAwF,oBAAmB7F,EAAI,YAC5C,MAED,IAAK,cACJD,EAAOI,SAASE,MAAK,IAAAyF,kBAAiB9F,EAAI,YAC1C,MAED,IAAK,QACL,IAAK,YACJD,EAAOI,SAASE,KAAK5F,KAAKsL,iBAAiB/F,IAC3C,MAED,IAAK,MACJD,EAAOI,SAASE,QAAQ5F,KAAK6G,SAAStB,GAAI5C,GAAK3C,KAAK2G,eAAehE,GAAG+C,YACtE,MAED,IAAK,MACJJ,EAAOI,SAASE,KAAK5F,KAAK4K,cAAcrF,GAAI5C,GAAK3C,KAAK2G,eAAehE,MACrE,MAED,IAAK,MACJ2C,EAAOI,SAASE,KAAK5F,KAAK+K,aAAaxF,GAAI5C,GAAK3C,KAAK2G,eAAehE,MAKvE,OAAO2C,CACR,CAEA,wBAAA6C,CAAyB5B,EAAegF,GACvCvL,KAAK0H,uBAAuBnB,EAAMgF,EAAUlF,SAAW,CAAC,EAAG,MAAMkB,IAChE,IAAI,IAAAiE,wBAAuBjE,EAAGgE,EAAW,WACxC,OAAO,EAER,OAAQhE,EAAEb,WACT,IAAK,SACJ6E,EAAUE,UAAY,UAAI5I,KAAK0E,EAAG,OAClC,MAED,IAAK,WACJgE,EAAUG,UAAYjE,EAAOkE,oBAAoBpE,GACjD,MAED,IAAK,UACJvH,KAAK4L,WAAWrE,EAAGgE,GACnB,MAED,IAAK,MAEJ,MAED,QACC,OAAO,EAGT,OAAO,CAAI,GAEb,CAEA,UAAAK,CAAWpG,EAAe+F,GAGV,QAFD,UAAI1I,KAAK2C,EAAM,aAG5B+F,EAAUlF,SAAgB,MAAI,OAChC,CAEA,cAAA8E,CAAe3F,EAAeqG,GAC7B,IAAIvG,EAAqC,CAAExE,KAAM,EAAAyC,QAAQuI,UAAWD,OAAQA,EAAQnG,SAAU,IAC1FqG,EAAS,UAAIlJ,KAAK2C,EAAM,UACxBwG,EAAQ,UAAInJ,KAAK2C,EAAM,MAgB3B,OAdIuG,IACHzG,EAAO2G,KAAO,IAAMF,GAEjBC,IACH1G,EAAO1C,GAAKoJ,GAEbxF,EAAQQ,QAAQxB,GAAM+B,IAEf,MADEA,EAAEb,WAERpB,EAAOI,SAASE,KAAK5F,KAAKkL,SAAS3D,EAAGjC,G,IAKlCA,CACR,CAEA,QAAA4F,CAAS1F,EAAeqG,GACvB,IAAIvG,EAAyB,CAAExE,KAAM,EAAAyC,QAAQ2I,IAAKL,OAAQA,EAAQnG,SAAU,IA4G5E,OA1GAc,EAAQQ,QAAQxB,GAAM+B,IAGrB,QAFAA,EAAIvH,KAAKmM,sBAAsB5E,IAErBb,WACT,IAAK,IACJpB,EAAOI,SAASE,KAAc,CAC7B9E,KAAM,EAAAyC,QAAQ6I,KACdC,KAAM9E,EAAE+E,cAET,MAED,IAAK,UACJhH,EAAOI,SAASE,KAAc,CAC7B9E,KAAM,EAAAyC,QAAQgJ,YACdF,KAAM9E,EAAE+E,cAET,MAED,IAAK,YACJhH,EAAOI,SAASE,KAAqB,CACpC9E,KAAM,EAAAyC,QAAQiJ,YACdC,YAAa,UAAI5J,KAAK0E,EAAG,SACzBmF,KAAM,UAAI7E,SAASN,EAAG,QAAQ,GAC9BoF,MAAO,UAAI9E,SAASN,EAAG,SAAS,KAEjC,MAED,IAAK,YACJjC,EAAOsH,UAAW,EAClBtH,EAAOI,SAASE,KAAyB,CACxC9E,KAAM,EAAAyC,QAAQsJ,YACdR,KAAM9E,EAAE+E,cAET,MAED,IAAK,UACJhH,EAAOsH,UAAW,EAClBtH,EAAOI,SAASE,KAAmB,CAClC9E,KAAM,EAAAyC,QAAQuJ,aACdC,SAAU,UAAIlK,KAAK0E,EAAG,eACtBmF,KAAM,UAAI7E,SAASN,EAAG,QAAQ,GAC9BoF,MAAO,UAAI9E,SAASN,EAAG,SAAS,KAEjC,MAED,IAAK,gBACJjC,EAAOI,SAASE,KAAK,CAAE9E,KAAM,EAAAyC,QAAQyJ,gBACrC,MAED,IAAK,KACJ1H,EAAOI,SAASE,KAAe,CAC9B9E,KAAM,EAAAyC,QAAQ0J,MACdC,MAAO,UAAIrK,KAAK0E,EAAG,SAAW,iBAE/B,MAED,IAAK,wBACJjC,EAAOI,SAASE,KAAe,CAC9B9E,KAAM,EAAAyC,QAAQ0J,MACdC,MAAO,0BAER,MAED,IAAK,MACJ5H,EAAOI,SAASE,KAAgB,CAC/B9E,KAAM,EAAAyC,QAAQ4J,OACdC,KAAM,UAAIvK,KAAK0E,EAAG,QAClB8F,KAAM,UAAIxK,KAAK0E,EAAG,UAEnB,MAED,IAAK,MACJjC,EAAOI,SAASE,KAAK,CAAE9E,KAAM,EAAAyC,QAAQ+J,MACrC,MAED,IAAK,oBACJhI,EAAOI,SAASE,KAAuB,CACtC9E,KAAM,EAAAyC,QAAQgK,kBACd3K,GAAI,UAAIC,KAAK0E,EAAG,QAEjB,MAED,IAAK,mBACJjC,EAAOI,SAASE,KAAuB,CACtC9E,KAAM,EAAAyC,QAAQiK,iBACd5K,GAAI,UAAIC,KAAK0E,EAAG,QAEjB,MAED,IAAK,UACJ,IAAIkG,EAAIzN,KAAK0N,aAAanG,GAEtBkG,IACHnI,EAAOI,SAAW,CAAC+H,IACpB,MAED,IAAK,OACJnI,EAAOI,SAASE,KAAK5F,KAAK2N,gBAAgBpG,IAC1C,MAED,IAAK,MACJvH,KAAKqI,mBAAmBd,EAAGjC,G,IAKvBA,CACR,CAEA,gBAAAgG,CAAiB/E,GAChB,MAAMqH,EAAW,GAAGrH,EAAKG,cACnBpB,EAAS,CAAExE,KAAMwC,EAAUiD,EAAKG,WAAYhB,SAAU,IAE5D,IAAK,MAAMH,KAAM,UAAI9C,SAAS8D,GAG7B,GAFkBjD,EAAUiC,EAAGmB,WAG9BpB,EAAOI,SAASE,KAAK5F,KAAKsL,iBAAiB/F,SACrC,GAAoB,KAAhBA,EAAGmB,UAAkB,CAC/B,IAAImH,EAAM7N,KAAKkL,SAAS3F,GACxBsI,EAAI/M,KAAO,EAAAyC,QAAQuK,OACnBxI,EAAOI,SAASE,KAAKiI,E,MACXtI,EAAGmB,WAAakH,IAC1BtI,EAAOa,MAAQnG,KAAK+N,mBAAmBxI,IAIzC,OAAOD,CACR,CAEA,kBAAAyI,CAAmBxH,GAClB,MAAMjB,EAA8B,CAAC,EAErC,IAAK,MAAMC,KAAM,UAAI9C,SAAS8D,GAC7B,OAAQhB,EAAGmB,WACV,IAAK,MAAOpB,EAAO+H,KAAO,UAAIxK,KAAK0C,EAAI,OAAQ,MAC/C,IAAK,SAAUD,EAAO0I,sBAAwB,UAAInL,KAAK0C,EAAI,OAAQ,MACnE,IAAK,MAAOD,EAAO2I,SAAW,UAAIpL,KAAK0C,EAAI,OAAQ,MACnD,IAAK,UAAWD,EAAO4I,WAAa,UAAIrG,SAAStC,EAAI,OAAQ,MAC7D,IAAK,SAAUD,EAAO6I,UAAY,UAAItL,KAAK0C,EAAI,OAAQ,MACvD,IAAK,SAAUD,EAAO8I,QAAU,UAAIvL,KAAK0C,EAAI,OAI/C,OAAOD,CACR,CAEA,kBAAA+C,CAAmB9B,EAAesH,GACjC7N,KAAK0H,uBAAuBnB,EAAMsH,EAAIxH,SAAW,CAAC,EAAG,MAAMkB,IAC1D,OAAQA,EAAEb,WACT,IAAK,SACJmH,EAAIpC,UAAY,UAAI5I,KAAK0E,EAAG,OAC5B,MAED,IAAK,YACJsG,EAAIQ,cAAgB5G,EAAO6G,iBAAiB/G,GAAG,GAC/C,MAED,QACC,OAAO,EAGT,OAAO,CAAI,GAEb,CAEA,eAAAoG,CAAgBpH,GACf,MAAMjB,EAAS,CAAExE,KAAM,EAAAyC,QAAQgL,WAAY7I,SAAU,IAErD,IAAK,MAAMH,KAAM,UAAI9C,SAAS8D,GAAO,CACpC,MAAMiI,GAAQ,IAAAC,iBAAgBlJ,EAAIvF,MAClCwO,GAASlJ,EAAOI,SAASE,KAAK4I,E,CAG/B,OAAOlJ,CACR,CAEA,qBAAA6G,CAAsB5F,GACrB,GAAsB,oBAAlBA,EAAKG,UACR,OAAOH,EAER,IAAImI,EAAS,UAAI3I,QAAQQ,EAAM,UAE/B,GAAImI,EAAQ,CACX,IAAIC,EAAW,UAAI9L,KAAK6L,EAAQ,YAC5BE,EAAerI,EAAKsI,mBAAmBF,GAE3C,GAAItL,EAAuByL,SAASF,GACnC,OAAOF,EAAO/M,iB,CAGhB,OAAO,UAAIoE,QAAQQ,EAAM,aAAa5E,iBACvC,CAEA,YAAA+L,CAAalI,GACZ,IAAK,IAAIyB,KAAK,UAAIxE,SAAS+C,GAC1B,OAAQyB,EAAEP,WACT,IAAK,SACL,IAAK,SACJ,OAAO1G,KAAK+O,oBAAoB9H,GAGpC,CAEA,mBAAA8H,CAAoBvJ,GACnB,IAAIF,EAAyB,CAAExE,KAAM,EAAAyC,QAAQyL,QAAStJ,SAAU,GAAIW,SAAU,CAAC,GAC3E4I,EAA6B,UAAlBzJ,EAAKkB,UAQpB,IAAIwI,EAAmD,KACnDC,EAAY,UAAItH,SAASrC,EAAM,aAE/B4J,EAAO,CAAEC,SAAU,OAAQC,MAAO,OAAQC,OAAQ,KAClDC,EAAO,CAAEH,SAAU,OAAQC,MAAO,MAAOC,OAAQ,KAErD,IAAK,IAAItI,KAAK,UAAIxE,SAAS+C,GAC1B,OAAQyB,EAAEP,WACT,IAAK,YACAyI,IACHC,EAAKG,OAAS,UAAIE,WAAWxI,EAAG,IAAK,EAAAyI,YAAYC,KACjDH,EAAKD,OAAS,UAAIE,WAAWxI,EAAG,IAAK,EAAAyI,YAAYC,MAElD,MAED,IAAK,SACJrK,EAAOe,SAAgB,MAAI,UAAIoJ,WAAWxI,EAAG,KAAM,EAAAyI,YAAYC,KAC/DrK,EAAOe,SAAiB,OAAI,UAAIoJ,WAAWxI,EAAG,KAAM,EAAAyI,YAAYC,KAChE,MAED,IAAK,YACL,IAAK,YACJ,IAAKR,EAAW,CACf,IAAIS,EAAqB,aAAf3I,EAAEP,UAA2B0I,EAAOI,EAC9C,IAAIK,EAAY,UAAI9J,QAAQkB,EAAG,SAC3B6I,EAAa,UAAI/J,QAAQkB,EAAG,aAEhC2I,EAAIP,SAAW,UAAIxM,KAAKoE,EAAG,iBAAmB2I,EAAIP,SAE9CQ,IACHD,EAAIN,MAAQO,EAAUvD,aAEnBwD,IACHF,EAAIL,OAAS/I,EAAQuJ,UAAUD,EAAY,EAAAJ,YAAYC,K,CAEzD,MAED,IAAK,mBACJT,EAAW,mBACX,MAED,IAAK,WACJA,EAAW,WACX,MAED,IAAK,UACJ,IAAIc,EAAIhQ,KAAKiQ,aAAahJ,GAEtB+I,GACH1K,EAAOI,SAASE,KAAKoK,GA4BzB,MAvBgB,oBAAZd,GACH5J,EAAOe,SAAkB,QAAI,QAEzB+I,EAAKE,QACRhK,EAAOe,SAAS,cAAgB+I,EAAKE,MACrChK,EAAOe,SAAgB,MAAI,SAGR,YAAZ6I,GACR5J,EAAOe,SAAkB,QAAI,QAC7Bf,EAAOe,SAAmB,SAAI,WAC9Bf,EAAOe,SAAgB,MAAI,MAC3Bf,EAAOe,SAAiB,OAAI,MAExB+I,EAAKG,SACRjK,EAAOe,SAAe,KAAI+I,EAAKG,QAC5BC,EAAKD,SACRjK,EAAOe,SAAc,IAAImJ,EAAKD,UAEvBN,GAA2B,QAAdG,EAAKE,OAAiC,SAAdF,EAAKE,QAClDhK,EAAOe,SAAgB,MAAI+I,EAAKE,OAG1BhK,CACR,CAEA,YAAA2K,CAAa1J,GACZ,IAAI2J,EAAc,UAAInK,QAAQQ,EAAM,eAEpC,IAAK,IAAIU,KAAK,UAAIxE,SAASyN,GAC1B,GACM,QADEjJ,EAAEP,UAER,OAAO1G,KAAKmQ,aAAalJ,GAI5B,OAAO,IACR,CAEA,YAAAkJ,CAAa5J,GACZ,IAAIjB,EAAoB,CAAExE,KAAM,EAAAyC,QAAQ6M,MAAOxG,IAAK,GAAIvD,SAAU,CAAC,GAC/DgK,EAAW,UAAItK,QAAQQ,EAAM,YAC7B+J,EAAO,UAAIvK,QAAQsK,EAAU,QAEjC/K,EAAOsE,IAAM,UAAI/G,KAAKyN,EAAM,SAE5B,IAAIC,EAAO,UAAIxK,QAAQQ,EAAM,QACzBiK,EAAO,UAAIzK,QAAQwK,EAAM,QAI7B,IAAK,IAAItJ,KAFT3B,EAAOe,SAAmB,SAAI,WAEhB,UAAI5D,SAAS+N,IAC1B,OAAQvJ,EAAEP,WACT,IAAK,MACJpB,EAAOe,SAAgB,MAAI,UAAIoJ,WAAWxI,EAAG,KAAM,EAAAyI,YAAYC,KAC/DrK,EAAOe,SAAiB,OAAI,UAAIoJ,WAAWxI,EAAG,KAAM,EAAAyI,YAAYC,KAChE,MAED,IAAK,MACJrK,EAAOe,SAAe,KAAI,UAAIoJ,WAAWxI,EAAG,IAAK,EAAAyI,YAAYC,KAC7DrK,EAAOe,SAAc,IAAI,UAAIoJ,WAAWxI,EAAG,IAAK,EAAAyI,YAAYC,KAK/D,OAAOrK,CACR,CAEA,UAAAsB,CAAWpB,GACV,IAAIF,EAAmB,CAAExE,KAAM,EAAAyC,QAAQkN,MAAO/K,SAAU,IAkBxD,OAhBAc,EAAQQ,QAAQxB,GAAM+B,IACrB,OAAQA,EAAEb,WACT,IAAK,KACJpB,EAAOI,SAASE,KAAK5F,KAAK0Q,cAAcnJ,IACxC,MAED,IAAK,UACJjC,EAAOqL,QAAU3Q,KAAK4Q,kBAAkBrJ,GACxC,MAED,IAAK,QACJvH,KAAK6Q,qBAAqBtJ,EAAGjC,G,IAKzBA,CACR,CAEA,iBAAAsL,CAAkBpL,GACjB,IAAIF,EAAS,GAUb,OARAkB,EAAQQ,QAAQxB,GAAMyB,IAEf,YADEA,EAAEP,WAERpB,EAAOM,KAAK,CAAEkL,MAAO,UAAIrB,WAAWxI,EAAG,M,IAKnC3B,CACR,CAEA,oBAAAuL,CAAqBtK,EAAewK,GAiCnC,OAhCAA,EAAM1K,SAAW,CAAC,EAClB0K,EAAMC,UAAY,CAAC,EAEnBhR,KAAK0H,uBAAuBnB,EAAMwK,EAAM1K,SAAU0K,EAAMC,WAAWzJ,IAClE,OAAQA,EAAEb,WACT,IAAK,WACJqK,EAAMtF,UAAY,UAAI5I,KAAK0E,EAAG,OAC9B,MAED,IAAK,UACJwJ,EAAMrF,UAAYjE,EAAOwJ,mBAAmB1J,GAC5C,MAED,IAAK,SACJvH,KAAKkR,mBAAmB3J,EAAGwJ,GAC3B,MAED,IAAK,sBACJA,EAAMI,YAAc,UAAIxH,QAAQpC,EAAG,OACnC,MAED,IAAK,sBACJwJ,EAAMK,YAAc,UAAIzH,QAAQpC,EAAG,OACnC,MAED,QACC,OAAO,EAGT,OAAO,CAAI,IAGJwJ,EAAM1K,SAAS,eACtB,IAAK,gBACG0K,EAAM1K,SAAS,cACtB0K,EAAM1K,SAAS,eAAiB,OAChC0K,EAAM1K,SAAS,gBAAkB,OACjC,MAED,IAAK,eACG0K,EAAM1K,SAAS,cACtB0K,EAAM1K,SAAS,eAAiB,OAGnC,CAEA,kBAAA6K,CAAmB1L,EAAeuL,GACjC,IAAIM,EAAc,UAAI5B,WAAWjK,EAAM,eACnC8L,EAAiB,UAAI7B,WAAWjK,EAAM,kBACtC+L,EAAgB,UAAI9B,WAAWjK,EAAM,iBACrCgM,EAAe,UAAI/B,WAAWjK,EAAM,gBAExCuL,EAAM1K,SAAgB,MAAI,OAC1B0K,EAAM1K,SAAS,iBAAmBoB,EAAOgK,QAAQV,EAAM1K,SAAS,iBAAkBiL,GAClFP,EAAM1K,SAAS,eAAiBoB,EAAOgK,QAAQV,EAAM1K,SAAS,eAAgBmL,GAC9ET,EAAM1K,SAAS,gBAAkBoB,EAAOgK,QAAQV,EAAM1K,SAAS,gBAAiBkL,GAChFR,EAAM1K,SAAS,cAAgBoB,EAAOgK,QAAQV,EAAM1K,SAAS,cAAegL,EAC7E,CAEA,aAAAX,CAAclL,GACb,IAAIF,EAAsB,CAAExE,KAAM,EAAAyC,QAAQmO,IAAKhM,SAAU,IAczD,OAZAc,EAAQQ,QAAQxB,GAAM+B,IACrB,OAAQA,EAAEb,WACT,IAAK,KACJpB,EAAOI,SAASE,KAAK5F,KAAK2R,eAAepK,IACzC,MAED,IAAK,OACJvH,KAAK4R,wBAAwBrK,EAAGjC,G,IAK5BA,CACR,CAEA,uBAAAsM,CAAwBrL,EAAesL,GACtCA,EAAIxL,SAAWrG,KAAK0H,uBAAuBnB,EAAM,CAAC,EAAG,MAAMgB,IAC1D,OAAQA,EAAEb,WACT,IAAK,WACJmL,EAAInG,UAAYjE,EAAOkE,oBAAoBpE,GAC3C,MAED,IAAK,YACJsK,EAAIC,SAAW,UAAIjK,SAASN,EAAG,OAC/B,MAED,QACC,OAAO,EAGT,OAAO,CAAI,GAEb,CAEA,cAAAoK,CAAenM,GACd,IAAIF,EAAuB,CAAExE,KAAM,EAAAyC,QAAQwO,KAAMrM,SAAU,IAkB3D,OAhBAc,EAAQQ,QAAQxB,GAAM+B,IACrB,OAAQA,EAAEb,WACT,IAAK,MACJpB,EAAOI,SAASE,KAAK5F,KAAK4G,WAAWW,IACrC,MAED,IAAK,IACJjC,EAAOI,SAASE,KAAK5F,KAAK2G,eAAeY,IACzC,MAED,IAAK,OACJvH,KAAKgS,yBAAyBzK,EAAGjC,G,IAK7BA,CACR,CAEA,wBAAA0M,CAAyBzL,EAAe0L,GACvCA,EAAK5L,SAAWrG,KAAK0H,uBAAuBnB,EAAM,CAAC,EAAG,MAAMgB,IAC3D,OAAQA,EAAEb,WACT,IAAK,WACJuL,EAAKC,KAAO,UAAIvI,QAAQpC,EAAG,MAAO,MAClC,MAED,IAAK,SACJ0K,EAAKE,cAAgB,UAAItP,KAAK0E,EAAG,QAAU,WAC3C,MAED,IAAK,WACJ0K,EAAKvG,UAAYjE,EAAOkE,oBAAoBpE,GAC5C,MAED,QACC,OAAO,EAGT,OAAO,CAAI,GAEb,CAEA,sBAAAG,CAAuBnB,EAAesD,EAAgC,KAAMuI,EAAqC,KAAMC,EAAsC,MAoL5J,OAnLAxI,EAAQA,GAAS,CAAC,EAElBrD,EAAQQ,QAAQT,GAAMgB,IACrB,IAAI8K,IAAU9K,GAGd,OAAQA,EAAEb,WACT,IAAK,KACJmD,EAAM,cAAgBpC,EAAO6K,UAAU/K,GACvC,MAED,IAAK,gBACJsC,EAAM,kBAAoBpC,EAAO8K,qBAAqBhL,GACtD,MAED,IAAK,QACJsC,EAAa,MAAIrD,EAAQC,UAAUc,EAAG,MAAO,KAAM,EAAAvE,MAAME,OACzD,MAED,IAAK,KACJ2G,EAAM,aAAeA,EAAM,cAAgB,UAAI4F,WAAWlI,EAAG,MAAO,EAAAmI,YAAY8C,UAChF,MAED,IAAK,MACJ3I,EAAM,oBAAsBrD,EAAQC,UAAUc,EAAG,OAAQ,KAAM,EAAAvE,MAAMC,KACrE,MAED,IAAK,YACJ4G,EAAM,oBAAsBrD,EAAQC,UAAUc,EAAG,MAAO,KAAM,EAAAvE,MAAMI,WACpE,MAED,IAAK,YAGJ,MAED,IAAK,WACJyG,EAAMwE,cAAgB,UAAIoB,WAAWlI,EAAG,MAAO,EAAAmI,YAAY8C,UAC3D,MAED,IAAK,MACJ,GAAIxS,KAAKL,QAAQsF,YAChB,MAEF,IAAK,OACJ4E,EAAa,MAAIpC,EAAOgL,YAAYlL,EAAG,KACvC,MAED,IAAK,WACJvH,KAAK0S,cAAcnL,EAAGsC,GACtB,MAED,IAAK,SACJA,EAAM,mBAAqB,UAAIhC,SAASN,EAAG,OAAO,GAAQ,eAAiB,OAC3E,MAED,IAAK,IACJsC,EAAM,eAAiB,UAAIhC,SAASN,EAAG,OAAO,GAAQ,OAAS,SAC/D,MAED,IAAK,IACJsC,EAAM,cAAgB,UAAIhC,SAASN,EAAG,OAAO,GAAQ,SAAW,SAChE,MAED,IAAK,OACJsC,EAAM,kBAAoB,UAAIhC,SAASN,EAAG,OAAO,GAAQ,YAAc,OACvE,MAED,IAAK,YACJsC,EAAM,kBAAoB,UAAIhC,SAASN,EAAG,OAAO,GAAQ,YAAc,OACvE,MAED,IAAK,IACJvH,KAAK2S,eAAepL,EAAGsC,GACvB,MAED,IAAK,MACL,IAAK,SACJ7J,KAAK4S,iBAAiBrL,EAAGsC,GACzB,MAED,IAAK,SACJ7J,KAAK6S,UAAUtL,EAAGsC,GAClB,MAED,IAAK,aACJ7J,KAAK8S,sBAAsBvL,EAAG6K,GAAcvI,GAC5C,MAED,IAAK,iBACJA,EAAM,kBAAoBpC,EAAOsL,cAAcxL,GAC/CsC,EAAM,mBAAqB,WAC3B,MAED,IAAK,OACJ7J,KAAK8S,sBAAsBvL,EAAGsC,GAC9B,MAED,IAAK,MACJA,EAAc,OAAIpC,EAAOuL,cAAczL,GACvC,MAED,IAAK,YACJvH,KAAK8S,sBAAsBvL,EAAGsC,GAC9B,MAED,IAAK,SACA,UAAIhC,SAASN,EAAG,OAAO,KAC1BsC,EAAe,QAAI,QACpB,MAED,IAAK,OAKL,IAAK,SAGJ,MAED,IAAK,aACL,IAAK,QACJ7J,KAAKiT,sBAAsB1L,EAAG6K,GAAcvI,GAC5C,MAED,IAAK,YACJA,EAAM,gBAAkBpC,EAAOyL,iBAAiB3L,GAChD,MAED,IAAK,SACJsC,EAAM,kBAAoBpC,EAAO8K,qBAAqBhL,GACtD,MAED,IAAK,UACkB,OAAlBhB,EAAKG,WACR1G,KAAKmT,aAAa5L,EAAGsC,GACtB,MAED,IAAK,WACA,UAAIhC,SAASN,EAAG,SACnBsC,EAAM,iBAAmB,cAC1B,MAED,IAAK,sBACJA,EAAe,QAAI,UAAIhC,SAASN,EAAG,OAAO,GAAQ,OAAS,OAC3D,MAED,IAAK,OACJsC,EAAa,MAAI,UAAIhH,KAAK0E,EAAG,OAC7B,MAED,IAAK,MACL,IAAK,MACL,IAAK,OACL,IAAK,OACL,IAAK,aACL,IAAK,oBACL,IAAK,sBACL,IAAK,sBACL,IAAK,YACL,IAAK,kBACL,IAAK,sBACL,IAAK,YACL,IAAK,WACL,IAAK,eACL,IAAK,OACL,IAAK,MACL,IAAK,UAEJ,MAED,QACKvH,KAAKL,QAAQuF,OAChBsD,QAAQC,KAAK,mCAAmClC,EAAKG,aAAaa,EAAEb,a,IAKjEmD,CACR,CAEA,cAAA8I,CAAenN,EAAeqE,GAC7B,IAAIuJ,EAAM,UAAIvQ,KAAK2C,EAAM,OAEzB,GAAW,MAAP4N,EAAJ,CAGA,OAAQA,GACP,IAAK,OACL,IAAK,kBACL,IAAK,eACL,IAAK,cACL,IAAK,WACL,IAAK,gBACL,IAAK,UACL,IAAK,aACJvJ,EAAM,yBAA2B,SACjC,MAED,IAAK,SACL,IAAK,cACJA,EAAM,yBAA2B,SACjC,MAED,IAAK,SACJA,EAAM,yBAA2B,SACjC,MAED,IAAK,SACL,IAAK,QAUL,IAAK,QACJA,EAAM,mBAAqB,YAC3B,MARD,IAAK,OACL,IAAK,aACL,IAAK,YACJA,EAAM,yBAA2B,OACjC,MAMD,IAAK,OACJA,EAAM,mBAAqB,OAI7B,IAAIwJ,EAAM7M,EAAQC,UAAUjB,EAAM,SAE9B6N,IACHxJ,EAAM,yBAA2BwJ,EA9C3B,CA+CR,CAEA,SAAAR,CAAUrN,EAAeqE,GACxB,IAGIyJ,EAAQ,CAHA,UAAIzQ,KAAK2C,EAAM,SACViC,EAAO8L,WAAW/N,EAAM,eAETgO,QAAOrK,GAAKA,IAAGsK,KAAK,MAEhDH,EAAMI,OAAS,IAClB7J,EAAM,eAAiByJ,EACzB,CAEA,gBAAAV,CAAiBpN,EAAeqE,GAC/B,IAAI8J,EAAY,UAAIlE,WAAWjK,EAAM,aACjCoO,EAAU,UAAInE,WAAWjK,EAAM,WAC/BqO,EAAO,UAAIpE,WAAWjK,EAAM,QAC5BwE,EAAQ,UAAIyF,WAAWjK,EAAM,SAC7BsO,EAAQ,UAAIrE,WAAWjK,EAAM,SAC7BuO,EAAM,UAAItE,WAAWjK,EAAM,OAE3BmO,IAAW9J,EAAM,eAAiB8J,GAClCC,IAAS/J,EAAM,eAAiB,IAAI+J,MACpCC,GAAQ7J,KAAOH,EAAM,eAAiBgK,GAAQ7J,IAC9C8J,GAASC,KAAKlK,EAAM,gBAAkBiK,GAASC,EACpD,CAEA,YAAAZ,CAAa3N,EAAeqE,GAC3B,IAAImK,EAAS,UAAIvE,WAAWjK,EAAM,UAC9ByO,EAAQ,UAAIxE,WAAWjK,EAAM,SAC7B0O,EAAO,UAAIvK,QAAQnE,EAAM,OAAQ,MACjC2O,EAAW,UAAItR,KAAK2C,EAAM,YAK9B,GAHIwO,IAAQnK,EAAM,cAAgBmK,GAC9BC,IAAOpK,EAAM,iBAAmBoK,GAEvB,OAATC,EACH,OAAQC,GACP,IAAK,OACJtK,EAAM,eAAiB,IAAIqK,EAAO,KAAKE,QAAQ,KAC/C,MAED,IAAK,UACJvK,EAAM,eAAiB,eAAeqK,EAAO,QAC7C,MAED,QACCrK,EAAM,eAAiBA,EAAM,cAAmBqK,EAAO,GAAV,KAIjD,CAEA,qBAAAjB,CAAsBzN,EAAe6O,GACpC7N,EAAQQ,QAAQxB,GAAM+B,IACrB,OAAQA,EAAEb,WACT,IAAK,OACJ2N,EAAO,gBAAkB5M,EAAOsL,cAAcxL,GAC9C,MAED,IAAK,QACJ8M,EAAO,iBAAmB5M,EAAOsL,cAAcxL,GAC/C,MAED,IAAK,MACJ8M,EAAO,eAAiB5M,EAAOsL,cAAcxL,GAC7C,MAED,IAAK,SACJ8M,EAAO,kBAAoB5M,EAAOsL,cAAcxL,G,GAIpD,CAEA,aAAAmL,CAAclN,EAAe6O,GACpB,UAAIxR,KAAK2C,EAAM,SAOrB6O,EAAe,OAAI,UAAI5E,WAAWjK,EAAM,MAK3C,CAEA,qBAAAsN,CAAsBtN,EAAe6O,GACpC7N,EAAQQ,QAAQxB,GAAM+B,IACrB,OAAQA,EAAEb,WACT,IAAK,QACL,IAAK,OACJ2N,EAAO,eAAiB5M,EAAOuL,cAAczL,GAC7C,MAED,IAAK,MACL,IAAK,QACJ8M,EAAO,gBAAkB5M,EAAOuL,cAAczL,GAC9C,MAED,IAAK,MACJ8M,EAAO,cAAgB5M,EAAOuL,cAAczL,GAC5C,MAED,IAAK,SACJ8M,EAAO,iBAAmB5M,EAAOuL,cAAczL,G,GAInD,GAGD,MAAM+M,EAAc,CAAC,QAAS,OAAQ,OAAQ,WAAY,WAAY,WAAY,YAAa,cAAe,UAAW,aAAc,QAAS,YAAa,UAAW,OAAQ,MAAO,QAAS,UAEhM,MAAM9N,EACL,cAAOQ,CAAQxB,EAAe+O,GAC7B,IAAK,IAAIC,EAAI,EAAGA,EAAIhP,EAAKiP,WAAWf,OAAQc,IAAK,CAChD,IAAIvN,EAAIzB,EAAKiP,WAAWD,GAEpBvN,EAAEyN,UAAYC,KAAKC,cACtBL,EAAYtN,E,CAEf,CAEA,gBAAOR,CAAUjB,EAAeqP,EAAkBC,EAAmB,KAAMC,EAAoB,SAC9F,IAAIC,EAAI,UAAInS,KAAK2C,EAAMqP,GAEvB,GAAIG,EACH,MAAS,QAALA,EACID,EACGT,EAAYxF,SAASkG,GACxBA,EAGD,IAAIA,IAGZ,IAAIC,EAAa,UAAIpS,KAAK2C,EAAM,cAEhC,OAAOyP,EAAa,cAAcA,WAAsBH,CACzD,CAEA,gBAAO/E,CAAUvK,EAAe1E,EAAwB,EAAA4O,YAAYwF,KACnE,OAAO,IAAAC,eAAc3P,EAAK8G,YAAaxL,EACxC,EAGD,MAAM2G,EACL,iBAAO8L,CAAWhM,EAAY1E,GAC7B,IAAIuQ,EAAM,UAAIvQ,KAAK0E,EAAG1E,GACtB,OAAOuQ,EAAM,cAAcA,UAAc,IAC1C,CAEA,kBAAOX,CAAYlL,EAAY1E,GAC9B,IAAI/B,EAAO,EAAA4O,YAAYwF,IAEvB,OAAQ,UAAIrS,KAAK0E,EAAG,SACnB,IAAK,MAAO,MACZ,IAAK,MAAOzG,EAAO,EAAA4O,YAAY0F,QAAS,MACxC,IAAK,OAAQ,MAAO,OAGrB,OAAO,UAAI3F,WAAWlI,EAAG1E,EAAM/B,EAChC,CAEA,oBAAOiS,CAAcxL,GACpB,OAAO,UAAIkI,WAAWlI,EAAG,IAC1B,CAEA,oBAAOyL,CAAczL,GAGpB,GAAY,OAFD,UAAI1E,KAAK0E,EAAG,OAGtB,MAAO,OAER,IAAIrE,EAAQsD,EAAQC,UAAUc,EAAG,SAGjC,MAAO,GAFI,UAAIkI,WAAWlI,EAAG,KAAM,EAAAmI,YAAY2F,iBAEd,QAATnS,EAAkB,EAAAF,MAAMG,YAAcD,GAC/D,CAEA,uBAAOgQ,CAAiB3L,GAEvB,MAAe,SADJ,UAAI1E,KAAK0E,EAAG,OACE,QAAU,MACpC,CAEA,0BAAOoE,CAAoBpE,GAC1B,MAAM6L,EAAM,UAAIvQ,KAAK0E,EAAG,OAOxB,MANgB,CACf,YAAa,WAAY,YAAa,WACtC,UAAW,WAAY,UAAW,WAClC,UAAW,UAAW,UAAW,WAGnBiM,QAAO,CAAC8B,EAAGd,IAAgB,KAAVpB,EAAIoB,KAAWf,KAAK,IACrD,CAEA,gBAAOnB,CAAU/K,GAChB,IAAIzG,EAAO,UAAI+B,KAAK0E,EAAG,OAEvB,OAAQzG,GACP,IAAK,QACL,IAAK,OAAQ,MAAO,OACpB,IAAK,SAAU,MAAO,SACtB,IAAK,MACL,IAAK,QAAS,MAAO,QACrB,IAAK,OAAQ,MAAO,UAGrB,OAAOA,CACR,CAEA,uBAAOwN,CAAiB/G,EAAYgO,GAAqB,GACxD,IAAIzU,EAAO,UAAI+B,KAAK0E,EAAG,OAEvB,OAAQzG,GACP,IAAK,YAAa,MAAO,MACzB,IAAK,cAAe,OAAOyU,EAAY,MAAQ,QAGhD,OAAOA,EAAY,KAAOzU,CAC3B,CAEA,2BAAOyR,CAAqBhL,GAC3B,IAAIzG,EAAO,UAAI+B,KAAK0E,EAAG,OAEvB,OAAQzG,GACP,IAAK,OACL,IAAK,WAAY,MAAO,WACxB,IAAK,MAAO,MAAO,MACnB,IAAK,SAAU,MAAO,SACtB,IAAK,SAAU,MAAO,SAGvB,OAAOA,CACR,CAEA,cAAO2Q,CAAQ+D,EAAWC,GACzB,OAAS,MAALD,EAAkBC,EACb,MAALA,EAAkBD,EAEf,QAAQA,OAAOC,IACvB,CAEA,yBAAOxE,CAAmB1J,GACzB,MAAM6L,EAAM,UAAIsC,QAAQnO,EAAG,MAAO,GAClC,IAAImE,EAAY,GAShB,OAPI,UAAI7D,SAASN,EAAG,aAAsB,GAAN6L,KAAe1H,GAAa,eAC5D,UAAI7D,SAASN,EAAG,YAAqB,GAAN6L,KAAe1H,GAAa,cAC3D,UAAI7D,SAASN,EAAG,gBAAyB,IAAN6L,KAAe1H,GAAa,eAC/D,UAAI7D,SAASN,EAAG,eAAwB,IAAN6L,KAAe1H,GAAa,cAC9D,UAAI7D,SAASN,EAAG,YAAqB,IAAN6L,KAAe1H,GAAa,cAC3D,UAAI7D,SAASN,EAAG,YAAqB,KAAN6L,KAAe1H,GAAa,aAExDA,EAAUiK,MAClB,E,wFCljDD,eACA,SAEA,MAAaC,UAAsB,EAAAC,KAG/B,QAAAzT,CAASE,GACLtC,KAAKmG,OAAQ,IAAA2P,gBAAexT,EAAMtC,KAAK8B,SAASlC,UACpD,EALJ,iB,uFCUA,0BAA+B0C,EAAe1C,GAC1C,MAAM0F,EAA+B,CAAC,EAEtC,IAAK,IAAIC,KAAM3F,EAAU6C,SAASH,GAC9B,OAAQiD,EAAGmB,WACP,IAAK,QAASpB,EAAOyQ,MAAQxQ,EAAG+G,YAAa,MAC7C,IAAK,cAAehH,EAAO0Q,YAAczQ,EAAG+G,YAAa,MACzD,IAAK,UAAWhH,EAAO2Q,QAAU1Q,EAAG+G,YAAa,MACjD,IAAK,UAAWhH,EAAO4Q,QAAU3Q,EAAG+G,YAAa,MACjD,IAAK,WAAYhH,EAAO6Q,SAAW5Q,EAAG+G,YAAa,MACnD,IAAK,WAAYhH,EAAO8Q,SAAW7Q,EAAG+G,YAAa,MACnD,IAAK,iBAAkBhH,EAAO+Q,eAAiB9Q,EAAG+G,YAAa,MAC/D,IAAK,WAAY/G,EAAG+G,cAAgBhH,EAAOgR,SAAWC,SAAShR,EAAG+G,cAI1E,OAAOhH,CACX,C,0FC9BA,eACA,SAEA,MAAakR,UAAwB,EAAAX,KAGjC,QAAAzT,CAASE,GACLtC,KAAKmG,OAAQ,IAAAsQ,kBAAiBnU,EAAMtC,KAAK8B,SAASlC,UACtD,EALJ,mB,yFCMA,4BAAiC0C,EAAeE,GAC/C,OAAOA,EAAIC,SAASH,EAAM,YAAYI,KAAIC,IACzC,MAAM+T,EAAa/T,EAAE+T,WAErB,MAAO,CACNC,SAAUnU,EAAIK,KAAKF,EAAG,SACtByE,KAAM5E,EAAIK,KAAKF,EAAG,QAClB7B,KAAM4V,EAAWE,SACjBC,MAAOH,EAAWpK,YAClB,GAEH,C,4FCpBA,eACA,SAEA,MAAawK,UAA0B,EAAAjB,KAGnC,QAAAzT,CAASE,GACLtC,KAAKmG,OAAQ,IAAA4Q,oBAAmBzU,EAAMtC,KAAK8B,SAASlC,UACxD,EALJ,qB,cCoDA,SAASoX,EAAeH,GACpB,QAAqB,IAAVA,EAEX,OAAON,SAASM,EACpB,C,6EA5CA,8BAAmCvU,EAAe1C,GAC9C,MAAM0F,EAAmC,CAAC,EAI1C,IAAK,IAAIC,KAAM3F,EAAU6C,SAASH,GAC9B,OAAQiD,EAAGmB,WACP,IAAK,WACDpB,EAAO2R,SAAW1R,EAAG+G,YACrB,MACJ,IAAK,QACDhH,EAAO4R,MAAQF,EAAezR,EAAG+G,aACjC,MACJ,IAAK,QACDhH,EAAO6R,MAAQH,EAAezR,EAAG+G,aACjC,MACJ,IAAK,aACDhH,EAAO8R,WAAaJ,EAAezR,EAAG+G,aACtC,MACJ,IAAK,cACDhH,EAAO+R,YAAc9R,EAAG+G,YACxB,MACJ,IAAK,QACDhH,EAAOgS,MAAQN,EAAezR,EAAG+G,aACjC,MACJ,IAAK,aACDhH,EAAOiS,WAAaP,EAAezR,EAAG+G,aACtC,MACJ,IAAK,UACDhH,EAAOkS,QAAUjS,EAAG+G,YACpB,MACJ,IAAK,aACDhH,EAAOmS,WAAalS,EAAG+G,YAKnC,OAAOhH,CACX,C,gHCpDA,eAaA,8BAAmCiB,EAAe/D,GAC9C,MAAO,CACH1B,KAAM,EAAAyC,QAAQmU,cACd9U,GAAIJ,EAAIK,KAAK0D,EAAM,MACnBa,KAAM5E,EAAIK,KAAK0D,EAAM,QACrBoR,SAAUnV,EAAImH,QAAQpD,EAAM,YAC5BqR,QAASpV,EAAImH,QAAQpD,EAAM,WAEnC,EAEA,4BAAiCA,EAAe/D,GAC5C,MAAO,CACH1B,KAAM,EAAAyC,QAAQsU,YACdjV,GAAIJ,EAAIK,KAAK0D,EAAM,MAE3B,C,qGC5BA,eAkBA,SAAgBuR,EAAYvR,EAAe/D,GACvC,MAAO,CACH1B,KAAM0B,EAAIK,KAAK0D,EAAM,OACrBrD,MAAOV,EAAIK,KAAK0D,EAAM,SACtBwR,KAAMvV,EAAIiN,WAAWlJ,EAAM,KAAM,EAAAmJ,YAAY2F,QAC7C9F,OAAQ/M,EAAIiN,WAAWlJ,EAAM,QAAS,EAAAmJ,YAAYsI,OAClDC,MAAOzV,EAAIqF,SAAStB,EAAM,SAC1B2R,OAAQ1V,EAAIqF,SAAStB,EAAM,UAEnC,CATA,gBAWA,wBAA6BA,EAAe/D,GACxC,IAAI8C,EAAkB,CAAC,EAEvB,IAAK,IAAI3C,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,OAAQpB,EAAOuO,KAAOiE,EAAYnV,EAAGH,GAAM,MAChD,IAAK,MAAO8C,EAAO6S,IAAML,EAAYnV,EAAGH,GAAM,MAC9C,IAAK,QAAS8C,EAAOwO,MAAQgE,EAAYnV,EAAGH,GAAM,MAClD,IAAK,SAAU8C,EAAO8S,OAASN,EAAYnV,EAAGH,GAItD,OAAO8C,CACX,C,oKCzCa,EAAA+S,GAAK,CACdC,OAAQ,+DACRC,UAAW,wDACXC,QAAS,2DACZC,cAAe,8DACfC,KAAM,8DAkBM,EAAAhJ,YAA+C,CACxDwF,IAAK,CAAEyD,IAAK,IAAMC,KAAM,MACxBjJ,IAAK,CAAEgJ,IAAK,EAAI,MAAOC,KAAM,MAC7BpG,SAAU,CAAEmG,IAAK,GAAKC,KAAM,MAC5BvD,OAAQ,CAAEsD,IAAK,KAAOC,KAAM,MAC5BZ,MAAO,CAAEW,IAAK,EAAGC,KAAM,MACvBxD,QAAS,CAAEuD,IAAK,IAAMC,KAAM,KAC5BC,WAAY,CAAEF,IAAK,EAAI,IAAKC,KAAM,IAClCE,OAAQ,CAAEH,IAAK,EAAI,MAAOC,KAAM,KAGpC,yBAA8BxF,EAAa2F,EAAyB,EAAArJ,YAAYwF,KAE5E,OAAW,MAAP9B,GAAe,iBAAiB4F,KAAK5F,GAC9BA,EAGP,IAAImD,SAASnD,GAAO2F,EAAMJ,KAAKvE,QAAQ,KAAK2E,EAAMH,MAC1D,EAEA,0BAA+B5D,EAAWiE,GAAe,GACrD,OAAQjE,GACJ,IAAK,IAEL,IAAK,KAEL,IAAK,OAAQ,OAAO,EAHpB,IAAK,IAEL,IAAK,MAEL,IAAK,QAAS,OAAO,EACrB,QAAS,OAAOiE,EAExB,EAEA,6BAAkC7F,GAC9B,OAAOA,EAAMmD,SAASnD,GAAO,IAAM,IACvC,EAEA,+BAAoC7M,EAAeJ,EAAyB3D,GACxE,GAAG+D,EAAKqI,cAAgB,EAAAyJ,GAAGC,OACvB,OAAO,EAEX,OAAO/R,EAAKG,WACR,IAAK,QACDP,EAAMjD,MAAQV,EAAIK,KAAK0D,EAAM,OAC7B,MAEJ,IAAK,KACDJ,EAAM+S,SAAW1W,EAAIiN,WAAWlJ,EAAM,MAAO,EAAAmJ,YAAY8C,UACzD,MAEJ,QACI,OAAO,EAGf,OAAO,CACX,C,uFC9EA,eAIA,MAAa2G,UAAqB,EAAAtD,KAG9B,WAAApW,CAAY2Z,EAAqBrZ,EAAc2K,GAC3C2O,MAAMD,EAAKrZ,GACXC,KAAKsZ,gBAAkB5O,CAC3B,CAIA,QAAAtI,CAASE,GACLtC,KAAKuZ,KAAOvZ,KAAKsZ,gBAAgBzT,kBAAkBvD,EACvD,EAZJ,gB,cCLA,IAAYiB,E,uFAAZ,SAAYA,GACR,sBACA,wBACA,YACA,gBACA,gCACA,gBACA,YACA,cACA,wBACA,oBACA,gBACA,cACA,YACA,kBACA,gCACA,4BACA,kBACA,kBACA,wCACH,sCACG,sBACA,oBACA,4BACA,8BACA,4BACH,0BACA,oBACA,sCACA,4BACA,4BACA,oCACA,8BACA,kCACA,0BACA,oBACA,wBACA,kCACA,8BACA,kCACA,kCACA,sCACA,oBACA,8BACA,kBACA,sCACA,sBACA,gCACA,wBACA,8BACA,kBACA,kBACA,8BACA,0BACA,sBACA,oBACA,2BACA,CAzDD,CAAYA,IAAO,UAAPA,EAAO,KAuEnB,yCAEI,KAAAmC,SAA8B,GAC9B,KAAAW,SAAoC,CAAC,CAOzC,E,yFCvEA,4BAAiCE,EAAe/D,GAC5C,MAAO,CACHwR,OAAQxR,EAAIiN,WAAWlJ,EAAM,UAC7B0N,MAAOzR,EAAIiN,WAAWlJ,EAAM,SAC5B2N,KAAM1R,EAAImH,QAAQpD,EAAM,QACxB4N,SAAU3R,EAAIK,KAAK0D,EAAM,YAEjC,C,yJChBA,eAEA,QACA,SAEA,SA2CA,SAAgBiF,EAAuBjF,EAAeJ,EAA4B3D,GAC9E,GAAI+D,EAAKqI,cAAgB,EAAAyJ,GAAGC,OACxB,OAAO,EAEX,IAAG,IAAAkB,qBAAoBjT,EAAMJ,EAAO3D,GAChC,OAAO,EAEX,OAAQ+D,EAAKG,WACT,IAAK,OACDP,EAAMsT,KAAOC,EAAUnT,EAAM/D,GAC7B,MAEJ,IAAK,SACD2D,EAAMwT,cAAe,IAAAvT,wBAAuBG,EAAM/D,GAClD,MAEJ,IAAK,QACD2D,EAAMyT,UAAYC,EAAetT,EAAM/D,GACvC,MAEJ,IAAK,UAED,OADA2D,EAAM2T,aAAc,IAAAC,kBAAiBxT,EAAM/D,IACpC,EAGX,IAAK,gBAED,OADA2D,EAAM6T,cAAgBxX,EAAIK,KAAK0D,EAAM,QAC9B,EAGX,IAAK,YACDJ,EAAM8T,UAAYzX,EAAIqF,SAAStB,EAAM,OAAO,GAC5C,MAEJ,IAAK,WACDJ,EAAM+T,SAAW1X,EAAIqF,SAAStB,EAAM,OAAO,GAC3C,MAEJ,IAAK,kBACDJ,EAAMgU,gBAAkB3X,EAAIqF,SAAStB,EAAM,OAAO,GAClD,MAEJ,IAAK,aACDJ,EAAMiU,aAAe5X,EAAImH,QAAQpD,EAAM,OACvC,MAEJ,IAAK,SACDJ,EAAMsF,UAAYjJ,EAAIK,KAAK0D,EAAM,OACjC,MAEJ,IAAK,MACDJ,EAAMiC,UAAW,IAAAC,oBAAmB9B,EAAM/D,GAC1C,MAEJ,QACI,OAAO,EAGf,OAAO,CACX,CAEA,SAAgBkX,EAAUnT,EAAe/D,GACrC,OAAOA,EAAIC,SAAS8D,EAAM,OACrB7D,KAAIC,IAAK,CACNsL,SAAUzL,EAAIiN,WAAW9M,EAAG,OAC5B0X,OAAQ7X,EAAIK,KAAKF,EAAG,UACpBkH,MAAOrH,EAAIK,KAAKF,EAAG,UAE/B,CAEA,SAAgBkX,EAAetT,EAAe/D,GAC1C,IAAI8C,EAA6B,CAAC,EAElC,IAAK,IAAI3C,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,QACDpB,EAAO1C,GAAKJ,EAAIK,KAAKF,EAAG,OACxB,MAEJ,IAAK,OACD2C,EAAOyE,MAAQvH,EAAImH,QAAQhH,EAAG,OAK1C,OAAO2C,CACX,CAhGA,oCAAyCiB,EAAe/D,GACpD,IAAI8C,EAA8B,CAAC,EAEnC,IAAI,IAAIC,KAAM/C,EAAIC,SAAS8D,GACvBiF,EAAuBjG,EAAID,EAAQ9C,GAGvC,OAAO8C,CACX,EAEA,2BA6DA,cASA,kB,gHCtHA,eAuBA,SAAgBgV,EAAiB/T,EAAeJ,EAAsB3D,GAClE,SAAI,IAAAgX,qBAAoBjT,EAAMJ,EAAO3D,EAIzC,CAfA,8BAAmC+D,EAAe/D,GAC9C,IAAI8C,EAAwB,CAAC,EAE7B,IAAI,IAAIC,KAAM/C,EAAIC,SAAS8D,GACvB+T,EAAiB/U,EAAID,EAAQ9C,GAGjC,OAAO8C,CACX,EAEA,oB,8GCxBA,eACA,SAyCA,IAAYiV,EAmFZ,SAASC,EAAajU,EAAe/D,GACjC,MAAO,CACHiY,gBAAiBjY,EAAImH,QAAQpD,EAAM,OACnCmU,MAAOlY,EAAIiN,WAAWlJ,EAAM,SAC5BoU,UAAWnY,EAAIqF,SAAStB,EAAM,OAC9BqU,WAAYpY,EAAIqF,SAAStB,EAAM,cAAc,GAC7CoK,QAASnO,EAAIC,SAAS8D,EAAM,OACvB7D,KAAIC,IAAK,CACNmO,MAAOtO,EAAIiN,WAAW9M,EAAG,KACzB+X,MAAOlY,EAAIiN,WAAW9M,EAAG,aAGzC,CAEA,SAASkY,EAAgBtU,EAAe/D,GACpC,MAAO,CACHsY,QAAStY,EAAIK,KAAK0D,EAAM,WACxBwU,UAAWvY,EAAIK,KAAK0D,EAAM,aAC1BkE,OAAQjI,EAAIK,KAAK0D,EAAM,OACvByD,MAAOxH,EAAImH,QAAQpD,EAAM,SAEjC,CAEA,SAASyU,EAA2BzU,EAAe/D,GAC/C,MAAO,CACHI,GAAIJ,EAAIK,KAAK0D,EAAM,MACnBzF,KAAM0B,EAAIK,KAAK0D,EAAM,QAE7B,EA/GA,SAAYgU,GACR,0BACA,sBACA,0BACA,sBACA,mBACH,CAND,CAAYA,IAAW,cAAXA,EAAW,KAyBvB,kCAAuChU,EAAe/D,EAAiB,WACnE,IAAIyY,EAA6B,CAAC,EAElC,IAAK,IAAItY,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,OACDuU,EAAQC,SAAW,CACfpK,MAAOtO,EAAIiN,WAAW9M,EAAG,KACzBwY,OAAQ3Y,EAAIiN,WAAW9M,EAAG,KAC1ByY,YAAa5Y,EAAIK,KAAKF,EAAG,WAE7B,MAEJ,IAAK,OACDsY,EAAQna,KAAO0B,EAAIK,KAAKF,EAAG,OAC3B,MAEJ,IAAK,QACDsY,EAAQI,YAAc,CAClBxH,KAAMrR,EAAIiN,WAAW9M,EAAG,QACxBmR,MAAOtR,EAAIiN,WAAW9M,EAAG,SACzBwV,IAAK3V,EAAIiN,WAAW9M,EAAG,OACvByV,OAAQ5V,EAAIiN,WAAW9M,EAAG,UAC1B2Y,OAAQ9Y,EAAIiN,WAAW9M,EAAG,UAC1B4Y,OAAQ/Y,EAAIiN,WAAW9M,EAAG,UAC1B6Y,OAAQhZ,EAAIiN,WAAW9M,EAAG,WAE9B,MAEJ,IAAK,OACDsY,EAAQtK,QAAU6J,EAAa7X,EAAGH,GAClC,MAEJ,IAAK,mBACAyY,EAAQQ,aAAeR,EAAQQ,WAAa,KAAK7V,KAAKoV,EAA2BrY,EAAGH,IACrF,MAEJ,IAAK,mBACAyY,EAAQS,aAAeT,EAAQS,WAAa,KAAK9V,KAAKoV,EAA2BrY,EAAGH,IACrF,MAEJ,IAAK,UACDyY,EAAQU,UAAYnZ,EAAIqF,SAASlF,EAAG,OAAO,GAC3C,MAEJ,IAAK,YACDsY,EAAQW,aAAc,IAAAC,cAAalZ,EAAGH,GACtC,MAEJ,IAAK,YACDyY,EAAQa,WAAajB,EAAgBlY,EAAGH,GAKpD,OAAOyY,CACX,C,+EC3HA,eACA,SACA,SAqBa,KAA0B,CACnCc,cAAc,EACd9W,aAAa,EACb+W,aAAa,EACbC,YAAY,EACZ/W,OAAO,EACPgX,cAAc,EACdxQ,UAAW,OACXyQ,WAAW,EACXta,oBAAoB,EACpBua,6BAA6B,EAC7BC,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACpBC,gBAAgB,EAChBC,cAAc,EACdC,eAAe,GAGhB,cAA2BC,EAAkBC,EAAgC,MACzE,MAAMC,EAAM,IAAK,QAAmBD,GACpC,OAAO,EAAAE,aAAatc,KAAKmc,EAAM,IAAI,EAAAI,eAAeF,GAAMA,EAC5D,EAEA,KAAO7b,eAA2B2b,EAAkBK,EAA4BC,EAA8B,KAAML,EAAgC,MAChJ,MAAMC,EAAM,IAAK,QAAmBD,GAC9BM,EAAW,IAAI,EAAAC,aAAaC,OAAOC,UACtCC,QAAY,EAAAR,aAAatc,KAAKmc,EAAM,IAAI,EAAAI,eAAeF,GAAMA,GAIhE,OAFHK,EAASK,OAAOD,EAAKN,EAAeC,EAAgBJ,GAE1CS,CACX,C,wFCvDA,eACA,SAEA,MAAaE,UAAsB,EAAA3H,KAG/B,QAAAzT,CAASE,GACLtC,KAAKsT,OAAQ,IAAAmK,YAAWnb,EAAMtC,KAAK8B,SAASlC,UAChD,EALJ,iB,mHCDA,MAAM8d,EAAmB,CACrBC,aAAc,UACdC,UAAW,OACXC,YAAa,SACbC,gBAAiB,cAoBrB,SAAgBjL,EAAUtM,EAAe/D,GACrC,IAAI8C,EAA0B,CAC1B8B,KAAM5E,EAAIK,KAAK0D,EAAM,QACrBwX,cAAe,IAGnB,IAAK,IAAIxY,KAAM/C,EAAIC,SAAS8D,GACxB,OAAQhB,EAAGmB,WACP,IAAK,SACDpB,EAAO0Y,OAASxb,EAAIK,KAAK0C,EAAI,OAC7B,MAEJ,IAAK,UACDD,EAAO2Y,QAAUzb,EAAIK,KAAK0C,EAAI,OAC9B,MAEJ,IAAK,eACL,IAAK,YACL,IAAK,cACL,IAAK,kBACDD,EAAOyY,cAAcnY,KAAKsY,EAAkB3Y,EAAI/C,IAK5D,OAAO8C,CACX,CAEA,SAAgB4Y,EAAkB3X,EAAe/D,GAC7C,MAAO,CACHI,GAAIJ,EAAIK,KAAK0D,EAAM,MACnB4X,IAAK3b,EAAIK,KAAK0D,EAAM,WACpBzF,KAAM4c,EAAiBnX,EAAKG,WAEpC,CAtCA,sBAA2BpE,EAAeE,GACtC,OAAOA,EAAIC,SAASH,GAAMI,KAAI6C,GAAMsN,EAAUtN,EAAI/C,IACtD,EAEA,cA4BA,qB,gGCtDA,eAEA,MAAa4b,UAAkB,EAAAC,mBAA/B,c,oBACI,KAAAvd,KAAgB,EAAAyC,QAAQ+a,MAC5B,EAFA,cAIA,MAAaC,UAAkB,EAAAF,mBAA/B,c,oBACI,KAAAvd,KAAgB,EAAAyC,QAAQib,MAC5B,EAFA,a,yHCLA,eAGA,SAEA,MAAsBC,UAAwE,EAAA5I,KAK1F,WAAApW,CAAY2Z,EAAqBrZ,EAAc2K,GAC3C2O,MAAMD,EAAKrZ,GACXC,KAAKsZ,gBAAkB5O,CAC3B,CAEA,QAAAtI,CAASE,GACLtC,KAAK0e,YAAc1e,KAAK2e,oBACxB3e,KAAK0e,YAAYhZ,SAAW1F,KAAKsZ,gBAAgB3T,kBAAkBrD,EACvE,EAbJ,yBAkBA,2BAAgCmc,EAClB,iBAAAE,GACN,OAAO,IAAI,EAAAP,SACf,GAGJ,2BAAgCK,EAClB,iBAAAE,GACN,OAAO,IAAI,EAAAJ,SACf,E,uFChCJ,eAQA,SACA,SAYMlG,EAEG,qCAwyCT,SAASuG,EACRC,EACA1Y,EACAT,GAEA,OAAOoZ,OAAgB5U,EAAW2U,EAAS1Y,EAAOT,EACnD,CAEA,SAASqZ,EACRF,EACA1Y,EACAT,GAEA,OAAOoZ,EAtzCF,6BAszC0BD,EAAS1Y,EAAOT,EAChD,CAEA,SAASoZ,EAAgBzG,EAAYwG,EAAiB1Y,EAAmCT,GACxF,IAAIJ,EAAS+S,EAAKgF,SAASyB,gBAAgBzG,EAAIwG,GAAWxB,SAASuB,cAAcC,GAGjF,OAFAG,OAAOC,OAAO3Z,EAAQa,GACtBT,GAAYwZ,EAAe5Z,EAAQI,GAC5BJ,CACR,CAEA,SAAS6Z,EAAkB5Y,GAC1BA,EAAK6Y,UAAY,EAClB,CAEA,SAASF,EAAe3Y,EAAeb,GACtCA,EAASwD,SAAQ3B,GAAKhB,EAAK8Y,aAAY,IAAAC,UAAS/X,GAAK8V,SAASkC,eAAehY,GAAKA,IACnF,CAEA,SAASiY,EAAmBC,GAC3B,OAAOb,EAAc,QAAS,CAAEQ,UAAWK,GAC5C,CAEA,SAASC,EAAcnZ,EAAmBoZ,GACzCpZ,EAAK8Y,YAAYhC,SAASuC,cAAcD,GACzC,CAn0CA,qBAwBC,WAAAlgB,CAAmBogB,GAAA,KAAAA,aAAAA,EAtBnB,KAAAnU,UAAoB,OAIpB,KAAAoU,SAAsC,CAAC,EACvC,KAAAC,YAAoB,KAEpB,KAAAC,oBAA+C,GAC/C,KAAAC,qBAA8C,KAC9C,KAAAC,mBAAgC,GAChC,KAAAC,oBAA+B,KAE/B,KAAAC,YAA2C,CAAC,EAC5C,KAAAC,WAA0C,CAAC,EAE3C,KAAAC,kBAA8B,GAC9B,KAAAC,qBAA8B,GAG9B,KAAAC,YAAqB,GACrB,KAAAC,YAAmB,EAmwCnB,KAAA7B,cAAgBA,CAhwChB,CAEA,MAAArB,CAAOF,EAAwBL,EAA4BC,EAA8B,KAAMtd,GAC9FK,KAAKqd,SAAWA,EAChBrd,KAAKL,QAAUA,EACfK,KAAK0L,UAAY/L,EAAQ+L,UACzB1L,KAAK0gB,aAAe/gB,EAAQwc,UAAY,IAAInc,KAAK0L,oBAAsB,QACvE1L,KAAK8f,SAAW,KAIhBX,EAFAlC,EAAiBA,GAAkBD,GAGnCmC,EAAkBnC,GAElB0C,EAAczC,EAAgB,oCAC9BA,EAAeoC,YAAYrf,KAAK2gB,sBAE5BtD,EAASuD,YACZlB,EAAczC,EAAgB,gCAC9Bjd,KAAK6gB,YAAYxD,EAASuD,UAAW3D,IAGX,MAAvBI,EAASyD,aACZ9gB,KAAK8f,SAAW9f,KAAK+gB,cAAc1D,EAASyD,WAAWxZ,QAEvDoY,EAAczC,EAAgB,0BAC9BA,EAAeoC,YAAYrf,KAAKghB,aAAa3D,EAASyD,WAAWxZ,UAG9D+V,EAAS4D,gBACZjhB,KAAKkhB,kBAAkB7D,EAAS4D,cAAcE,eAE9CzB,EAAczC,EAAgB,oCAC9BA,EAAeoC,YAAYrf,KAAKohB,gBAAgB/D,EAAS4D,cAAcE,cAAelE,KAInFI,EAASgE,gBACZrhB,KAAKogB,aAAc,IAAAkB,OAAMjE,EAASgE,cAAcE,OAAOpY,GAAKA,EAAEvG,MAG3Dya,EAASmE,eACZxhB,KAAKqgB,YAAa,IAAAiB,OAAMjE,EAASmE,aAAaD,OAAOpY,GAAKA,EAAEvG,MAGzDya,EAASoE,eACZzhB,KAAK0hB,eAAiBrE,EAASoE,aAAaE,UAAUC,iBAGlDjiB,EAAQqc,aAAeqB,EAASwE,eACpC7hB,KAAK8hB,gBAAgBzE,EAASwE,cAAe5E,GAE9C,IAAI8E,EAAkB/hB,KAAKgiB,eAAe3E,EAAS4E,aAAa1I,MAE5DvZ,KAAKL,QAAQwc,UAChBa,EAAcqC,YAAYrf,KAAKkiB,cAAcH,IAE7C7C,EAAelC,EAAe+E,GAG/B/hB,KAAKmiB,iBACN,CAEA,WAAAtB,CAAYD,EAAsB3D,GACjC,MAAMmF,EAAY,CAAC,EACbC,EAAazB,EAAU0B,OAAOD,WAEhCA,IACCA,EAAWE,YACdH,EAAU,0BAA4BC,EAAWE,UAAUC,eAGxDH,EAAWI,YACdL,EAAU,0BAA4BC,EAAWI,UAAUD,gBAI7D,MAAME,EAAc9B,EAAU0B,OAAOI,YAErC,GAAIA,EACH,IAAK,IAAKC,EAAG3N,KAAMgK,OAAO4D,QAAQF,EAAYG,QAC7CT,EAAU,UAAUO,WAAa,IAAI3N,IAIvC,MAAMyK,EAAUzf,KAAK8iB,cAAc,IAAI9iB,KAAK0L,YAAa0W,GACzDnF,EAAeoC,YAAYG,EAAmBC,GAC/C,CAEA,eAAAqC,CAAgBiB,EAA0B9F,GACzC,IAAK,IAAI5b,KAAK0hB,EAAUzP,MACvB,IAAK,IAAI0P,KAAO3hB,EAAE0c,cACjB/d,KAAKqd,SAAS4F,SAASD,EAAIpgB,GAAIogB,EAAI7E,KAAK+E,MAAKC,IAC5C,MAAMC,EAAY,CACjB,cAAe/hB,EAAE+F,KACjB,IAAO,OAAO+b,MAGC,QAAZH,EAAIliB,MAA8B,cAAZkiB,EAAIliB,OAC7BsiB,EAAU,eAAiB,QAGZ,UAAZJ,EAAIliB,MAAgC,cAAZkiB,EAAIliB,OAC/BsiB,EAAU,cAAgB,UAG3B1D,EAAczC,EAAgB,UAAU5b,EAAE+F,aAC1C,MAAMqY,EAAUzf,KAAK8iB,cAAc,aAAcM,GACjDnG,EAAeoC,YAAYG,EAAmBC,IAC9Czf,KAAKmiB,iBAAiB,GAI1B,CAEA,gBAAAkB,CAAiB3X,GAChB,OAAOA,EAAY,GAAG1L,KAAK0L,cAAa,IAAA4X,iBAAgB5X,KAAe1L,KAAK0L,SAC7E,CAEA,aAAAqV,CAAczZ,GACb,MAAMic,GAAY,IAAAjC,OAAMha,EAAOkM,QAAOrK,GAAa,MAARA,EAAEvG,MAAauG,GAAKA,EAAEvG,KAEjE,IAAK,MAAMiH,KAASvC,EAAOkM,QAAOrK,GAAKA,EAAE9B,UAAU,CAClD,IAAImc,EAAYD,EAAU1Z,EAAMxC,SAEhC,GAAImc,EAAW,CACd3Z,EAAM3B,gBAAiB,IAAAub,WAAU5Z,EAAM3B,eAAgBsb,EAAUtb,gBACjE2B,EAAMzB,UAAW,IAAAqb,WAAU5Z,EAAMzB,SAAUob,EAAUpb,UAErD,IAAK,MAAMsb,KAAcF,EAAUlc,OAAQ,CAC1C,MAAMqc,EAAc9Z,EAAMvC,OAAOiD,MAAKpB,GAAKA,EAAErG,QAAU4gB,EAAW5gB,SAE9D6gB,EACH3jB,KAAK4jB,oBAAoBF,EAAWjc,OAAQkc,EAAYlc,QAExDoC,EAAMvC,OAAO1B,KAAK,IAAK8d,EAAYjc,OAAQ,IAAKic,EAAWjc,S,OAIrDzH,KAAKL,QAAQuF,OACrBsD,QAAQC,KAAK,yBAAyBoB,EAAMxC,U,CAG9C,IAAK,IAAIwC,KAASvC,EACjBuC,EAAMga,QAAU7jB,KAAKqjB,iBAAiBxZ,EAAMjH,IAG7C,OAAO2gB,CACR,CAEA,iBAAArC,CAAkB4C,GACjB,IAAK,IAAIC,KAAOD,EAAWtQ,QAAOvM,GAAKA,EAAEgD,aAAa,CACrD,MAAMJ,EAAQ7J,KAAKgkB,UAAUD,EAAI9Z,YAE7BJ,GAAO3B,gBAAgB0R,YAC1B/P,EAAM3B,eAAe0R,UAAU7P,MAAQga,EAAIha,M,CAG9C,CAEA,cAAAka,CAAele,GACd,GAAIA,EAAQL,SACX,IAAK,IAAI/C,KAAKoD,EAAQL,SACrB/C,EAAEkJ,OAAS9F,EAEPpD,EAAE7B,MAAQ,EAAAyC,QAAQkN,MACrBzQ,KAAKkkB,aAAavhB,GAGlB3C,KAAKikB,eAAethB,EAIxB,CAEA,YAAAuhB,CAAanT,GACZ,IAAK,IAAIoT,KAAKpT,EAAMrL,SACnB,IAAK,IAAI6B,KAAK4c,EAAEze,SACf6B,EAAElB,SAAWrG,KAAK4jB,oBAAoB7S,EAAMC,UAAWzJ,EAAElB,SAAU,CAClE,cAAe,eAAgB,aAAc,gBAC7C,eAAgB,gBAAiB,cAAe,mBAGjDrG,KAAKikB,eAAe1c,EAGvB,CAEA,mBAAAqc,CAAoBnjB,EAA+B4T,EAAgC+P,EAAkB,MACpG,IAAK3jB,EACJ,OAAO4T,EAKR,IAAK,IAAI8J,KAHK,MAAV9J,IAAgBA,EAAS,CAAC,GACjB,MAAT+P,IAAeA,EAAQpF,OAAOqF,oBAAoB5jB,IAEtC2jB,GACX3jB,EAAM6jB,eAAenG,KAAS9J,EAAOiQ,eAAenG,KACvD9J,EAAO8J,GAAO1d,EAAM0d,IAGtB,OAAO9J,CACR,CAEA,aAAAkQ,CAAc7Y,EAAmBvF,GAChC,IAAII,EAAOvG,KAAK4e,cAAc,UAAW,CAAElT,cA2B3C,OAzBIvF,IACCA,EAAMkV,cACT9U,EAAKsD,MAAM2a,YAAcre,EAAMkV,YAAYxH,KAC3CtN,EAAKsD,MAAM4a,aAAete,EAAMkV,YAAYvH,MAC5CvN,EAAKsD,MAAM6a,WAAave,EAAMkV,YAAYlD,IAC1C5R,EAAKsD,MAAM8a,cAAgBxe,EAAMkV,YAAYjD,QAG1CjS,EAAM+U,WACJlb,KAAKL,QAAQsF,cACjBsB,EAAKsD,MAAMiH,MAAQ3K,EAAM+U,SAASpK,OAC9B9Q,KAAKL,QAAQoc,eACjBxV,EAAKsD,MAAM+a,UAAYze,EAAM+U,SAASC,SAGpChV,EAAMwK,SAAWxK,EAAMwK,QAAQ8J,kBAClClU,EAAKsD,MAAMgb,YAAc,GAAG1e,EAAMwK,QAAQ8J,kBAC1ClU,EAAKsD,MAAMib,UAAY3e,EAAMwK,QAAQ+J,MAEjCvU,EAAMwK,QAAQgK,YACjBpU,EAAKsD,MAAMkb,WAAa,qBAKpBxe,CACR,CAEA,cAAAyb,CAAe3E,GACd,MAAM/X,EAAS,GAEftF,KAAKikB,eAAe5G,GACpB,MAAM2H,EAAWhlB,KAAKilB,eAAe5H,EAAS3X,UAC9C,IAAIwf,EAAY,KAEhB,IAAK,IAAI1Q,EAAI,EAAG2Q,EAAIH,EAAStR,OAAQc,EAAI2Q,EAAG3Q,IAAK,CAChDxU,KAAKolB,mBAAqB,GAE1B,MAAMnK,EAAU+J,EAASxQ,GACnBrO,EAAQ8U,EAAQoK,WAAahI,EAASlX,MACtCmf,EAAiBtlB,KAAKukB,cAAcvkB,KAAK0L,UAAWvF,GAC1DnG,KAAKulB,kBAAkBlI,EAAShX,SAAUif,GAE1CtlB,KAAKL,QAAQ0c,eAAiBrc,KAAKwlB,mBAAmBrf,EAAMsV,WAAYtV,EACvEb,EAAOoO,OAAQwR,GAAa/e,EAAOmf,GAEpC,IAAIG,EAAiBzlB,KAAK4e,cAAc,WACxC5e,KAAK0lB,eAAezK,EAAQxY,SAAUgjB,GACtCH,EAAejG,YAAYoG,GAEvBzlB,KAAKL,QAAQ4c,iBAChBvc,KAAK2lB,YAAY3lB,KAAKolB,mBAAoBplB,KAAKogB,YAAakF,GAGzDtlB,KAAKL,QAAQ6c,gBAAkBhI,GAAK2Q,EAAI,GAC3CnlB,KAAK2lB,YAAY3lB,KAAKsgB,kBAAmBtgB,KAAKqgB,WAAYiF,GAG3DtlB,KAAKL,QAAQ2c,eAAiBtc,KAAKwlB,mBAAmBrf,EAAMuV,WAAYvV,EACvEb,EAAOoO,OAAQwR,GAAa/e,EAAOmf,GAEpChgB,EAAOM,KAAK0f,GACZJ,EAAY/e,C,CAGb,OAAOb,CACR,CAEA,kBAAAkgB,CAAmBI,EAA+Bzf,EAA0B0f,EAAcC,EAAyBC,GAClH,GAAKH,EAAL,CAEA,IAAI5C,GAAO7c,EAAMwV,WAAamK,EAAiBF,EAAKrb,MAAKpB,GAAe,SAAVA,EAAErI,OAAmB,QAC9E+kB,EAAO,GAAK,EAAID,EAAKrb,MAAKpB,GAAe,QAAVA,EAAErI,OAAkB,OACpD8kB,EAAKrb,MAAKpB,GAAe,WAAVA,EAAErI,OAEjBklB,EAAOhD,GAAOhjB,KAAKqd,SAAS4I,gBAAgBjD,EAAIpgB,GAAI5C,KAAKqd,SAAS4E,cAElE+D,IACHhmB,KAAK+f,YAAciG,EACdhmB,KAAKugB,qBAAqBzR,SAASkX,EAAKjmB,QAC5CC,KAAKikB,eAAe+B,EAAKtH,aACzB1e,KAAKugB,qBAAqB3a,KAAKogB,EAAKjmB,OAErCC,KAAK0lB,eAAe,CAACM,EAAKtH,aAAcqH,GACxC/lB,KAAK+f,YAAc,KAfH,CAiBlB,CAEA,kBAAAmG,CAAmB3f,GAClB,OAAIA,EAAKzF,MAAQ,EAAAyC,QAAQ0J,QAGO,yBAA3B1G,EAAkB2G,OACdlN,KAAKL,QAAQyc,4BAEa,QAA3B7V,EAAkB2G,MAC3B,CAEA,cAAA+X,CAAexiB,GACd,IAAI0jB,EAAU,CAAEd,UAAW,KAAM5iB,SAAU,IACvC6C,EAAS,CAAC6gB,GAEd,IAAK,IAAI5f,KAAQ9D,EAAU,CAC1B,GAAI8D,EAAKzF,MAAQ,EAAAyC,QAAQ0H,UAAW,CACnC,MAAM3C,EAAItI,KAAKgkB,UAAWzd,EAAsBkF,WAE5CnD,GAAGJ,gBAAgBiS,kBACtBgM,EAAQd,UAAYA,EACpBc,EAAU,CAAEd,UAAW,KAAM5iB,SAAU,IACvC6C,EAAOM,KAAKugB,G,CAMd,GAFAA,EAAQ1jB,SAASmD,KAAKW,GAElBA,EAAKzF,MAAQ,EAAAyC,QAAQ0H,UAAW,CACnC,MAAMmb,EAAI7f,EAEV,IAAI8e,EAAYe,EAAEzM,aACd0M,GAAe,EACfC,GAAe,EAenB,GAbItmB,KAAKL,QAAQsc,YAAcmK,EAAE1gB,WAChC2gB,EAAcD,EAAE1gB,SAAS6gB,WAAUpC,IAEX,IADvBmC,EAAcnC,EAAEze,UAAU6gB,UAAUvmB,KAAKkmB,mBAAmBM,KAAKxmB,SAAW,OAK1EqlB,IAA6B,GAAhBgB,KAChBF,EAAQd,UAAYA,EACpBc,EAAU,CAAEd,UAAW,KAAM5iB,SAAU,IACvC6C,EAAOM,KAAKugB,KAGO,GAAhBE,EAAmB,CACtB,IAAII,EAAWL,EAAE1gB,SAAS2gB,GACtBK,EAAWJ,EAAcG,EAAS/gB,SAASgO,OAAS,EAExD,GAAI2S,EAAcD,EAAE1gB,SAASgO,OAAS,GAAKgT,EAAU,CACpD,IAAIhhB,EAAWa,EAAKb,SAChBihB,EAAe,IAAKpgB,EAAMb,SAAUA,EAASkhB,MAAMP,IAIvD,GAHA9f,EAAKb,SAAWA,EAASkhB,MAAM,EAAGP,GAClCF,EAAQ1jB,SAASmD,KAAK+gB,GAElBD,EAAU,CACb,IAAIG,EAAcJ,EAAS/gB,SACvBohB,EAAS,IAAKL,EAAU/gB,SAAUmhB,EAAYD,MAAM,EAAGN,IAC3D/f,EAAKb,SAASE,KAAKkhB,GACnBL,EAAS/gB,SAAWmhB,EAAYD,MAAMN,E,KAO3C,IAAIS,EAAmB,KAEvB,IAAK,IAAIvS,EAAIlP,EAAOoO,OAAS,EAAGc,GAAK,EAAGA,IACZ,MAAvBlP,EAAOkP,GAAG6Q,UACb/f,EAAOkP,GAAG6Q,UAAY0B,EAEtBA,EAAmBzhB,EAAOkP,GAAG6Q,UAI/B,OAAO/f,CACR,CAEA,aAAA4c,CAAcxc,GACb,OAAO1F,KAAK4e,cAAc,MAAO,CAAElT,UAAW,GAAG1L,KAAK0L,qBAAuBhG,EAC9E,CAEA,kBAAAib,GACC,IAAIpZ,EAAIvH,KAAK0L,UAeb,OAAO8T,EAdS,MACfjY,iIACAA,qBAAqBA,4FACrBA,+CACOA,yHACAA,2DACAA,8BACPA,4CACAA,gBAAgBA,yCAChBA,2CACAA,kEACAA,sDAIF,CAmEA,eAAA6Z,CAAgB0C,EAA6B7G,GAC5C,IAAI+J,EAAY,GACZC,EAAgB,GAEpB,IAAK,IAAIlD,KAAOD,EAAY,CAC3B,IAAIpb,EAAW,KAAK1I,KAAKknB,eAAenD,EAAInhB,GAAImhB,EAAIha,SAChDod,EAAgB,OAEpB,GAAIpD,EAAIzZ,OAAQ,CACf,IAAI8c,EAAW,KAAKpnB,KAAK0L,aAAaqY,EAAIzZ,OAAOV,MAAMyd,cAEvDL,GAAahnB,KAAK8iB,cAAc,GAAGpa,WAAmB,CACrD,QAAW,MACX,QAAW,eACX,WAAc,OAAO0e,MACnBrD,EAAIzZ,OAAOT,OAEd7J,KAAKqd,SAASiK,mBAAmBvD,EAAIzZ,OAAOV,KAAKsZ,MAAKvG,IACrD,IAAItQ,EAAO,GAAGrM,KAAK0gB,kBAAkB0G,UAAiBzK,OACtDM,EAAeoC,YAAYG,EAAmBnT,GAAM,G,MAGjD,GAAI0X,EAAIvZ,UAAW,CACvB,IAAI+c,EAAUvnB,KAAKwnB,iBAAiBzD,EAAInhB,GAAImhB,EAAIha,OAChD,MAAM0d,EAAeF,EAAU,KAAOxD,EAAI/Z,MAAQ,GAC9C+Z,EAAIha,MAAQ,IACfid,GAAahnB,KAAK8iB,cAAc,KAAK9iB,KAAKknB,eAAenD,EAAInhB,GAAImhB,EAAIha,MAAQ,KAAM,CAClF,gBAAiB0d,KAInBR,EAAcrhB,KAAK6hB,GAEnBT,GAAahnB,KAAK8iB,cAAc,GAAGpa,WAAmB,CACrD,QAAW1I,KAAK0nB,mBAAmB3D,EAAIvZ,UAAWuZ,EAAI1Z,KAAM0Z,EAAInhB,GAAI5C,KAAK2nB,oBAAoB5D,EAAItZ,SACjG,oBAAqB8c,KAClBxD,EAAI3Z,Q,MAIR+c,EAAgBnnB,KAAK2nB,oBAAoB5D,EAAItZ,QAG9Cuc,GAAahnB,KAAK8iB,cAAcpa,EAAU,CACzC,QAAW,YACX,sBAAuB,SACvB,kBAAmBye,KAChBpD,EAAI5Z,Q,CAUT,OANI8c,EAAcvT,OAAS,IAC1BsT,GAAahnB,KAAK8iB,cAAc9iB,KAAK0gB,aAAc,CAClD,gBAAiBuG,EAAcxT,KAAK,QAI/B+L,EAAmBwH,EAC3B,CAEA,YAAAhG,CAAa1Z,GACZ,IAAI0f,EAAY,GAChB,MAAMzD,EAAYvjB,KAAK8f,SACjB8H,GAAe,IAAAtG,OAAMha,EAAOkM,QAAOlL,GAAKA,EAAEV,aAAYU,GAAKA,EAAExF,SAEnE,IAAK,MAAM+G,KAASvC,EAAQ,CAC3B,IAAIugB,EAAYhe,EAAMvC,OAEtB,GAAIuC,EAAM/B,OAAQ,CACjB,IAAIggB,EAAcje,EAAM/B,QAAUyb,EAAU1Z,EAAM/B,QAE9CggB,EACHD,EAAYA,EAAUE,OAAOD,EAAYxgB,QACjCtH,KAAKL,QAAQuF,OACrBsD,QAAQC,KAAK,2BAA2BoB,EAAM/B,S,CAGhD,IAAK,MAAMkgB,KAAYH,EAAW,CAEjC,IAAInf,EAAW,GAAGmB,EAAM/G,QAAU,MAAM+G,EAAMga,UAE1Cha,EAAM/G,QAAUklB,EAASllB,SAC5B4F,GAAY,IAAIsf,EAASllB,UAEtB8kB,EAAa/d,EAAM/G,SAAW+G,IACjCnB,EAAW,IAAI1I,KAAK0L,aAAa7B,EAAM/G,WAAa4F,GAErDse,GAAahnB,KAAK8iB,cAAcpa,EAAUsf,EAASvgB,O,EAIrD,OAAO+X,EAAmBwH,EAC3B,CAEA,WAAArB,CAAYsC,EAAmBC,EAAuCnC,GACrE,IAAIxE,EAAQ0G,EAAQvlB,KAAIE,GAAMslB,EAAStlB,KAAK4Q,QAAOrK,GAAKA,IAExD,GAAIoY,EAAM7N,OAAS,EAAG,CACrB,IAAIpO,EAAStF,KAAK4e,cAAc,KAAM,KAAM5e,KAAK0lB,eAAenE,IAChEwE,EAAK1G,YAAY/Z,E,CAEnB,CAEA,aAAA6iB,CAAc5hB,GACb,OAAQA,EAAKzF,MACZ,KAAK,EAAAyC,QAAQ0H,UACZ,OAAOjL,KAAKooB,gBAAgB7hB,GAE7B,KAAK,EAAAhD,QAAQmU,cACZ,OAAO1X,KAAKqoB,oBAAoB9hB,GAEjC,KAAK,EAAAhD,QAAQsU,YACZ,OAAO,KAER,KAAK,EAAAtU,QAAQ2I,IACZ,OAAOlM,KAAKsoB,UAAU/hB,GAEvB,KAAK,EAAAhD,QAAQkN,MACZ,OAAOzQ,KAAKuoB,YAAYhiB,GAEzB,KAAK,EAAAhD,QAAQmO,IACZ,OAAO1R,KAAKwoB,eAAejiB,GAE5B,KAAK,EAAAhD,QAAQwO,KACZ,OAAO/R,KAAKyoB,gBAAgBliB,GAE7B,KAAK,EAAAhD,QAAQuI,UACZ,OAAO9L,KAAK0oB,gBAAgBniB,GAE7B,KAAK,EAAAhD,QAAQyL,QACZ,OAAOhP,KAAK2oB,cAAcpiB,GAE3B,KAAK,EAAAhD,QAAQ6M,MACZ,OAAOpQ,KAAK4oB,YAAYriB,GAEzB,KAAK,EAAAhD,QAAQ6I,KAGb,KAAK,EAAA7I,QAAQ6I,KACZ,OAAOpM,KAAK6oB,WAAWtiB,GAExB,KAAK,EAAAhD,QAAQgJ,YACZ,OAAOvM,KAAK8oB,kBAAkBviB,GAE/B,KAAK,EAAAhD,QAAQ+J,IACZ,OAAOtN,KAAK+oB,UAAUxiB,GAEvB,KAAK,EAAAhD,QAAQ4J,OACZ,OAAOnN,KAAKgpB,aAAaziB,GAE1B,KAAK,EAAAhD,QAAQ0J,MACZ,OAAOjN,KAAKipB,YAAY1iB,GAEzB,KAAK,EAAAhD,QAAQib,OACZ,OAAOxe,KAAKkpB,gBAAgB3iB,EAAM,UAEnC,KAAK,EAAAhD,QAAQ+a,OACZ,OAAOte,KAAKkpB,gBAAgB3iB,EAAM,UAEnC,KAAK,EAAAhD,QAAQ4lB,SACb,KAAK,EAAA5lB,QAAQ6lB,QACZ,OAAOppB,KAAKkpB,gBAAgB3iB,EAAM,MAEnC,KAAK,EAAAhD,QAAQgK,kBACZ,OAAOvN,KAAKqpB,wBAAwB9iB,GAErC,KAAK,EAAAhD,QAAQiK,iBACZ,OAAOxN,KAAKspB,uBAAuB/iB,GAEpC,KAAK,EAAAhD,QAAQyJ,cACZ,OAAOhN,KAAK4e,cAAc,OAE3B,KAAK,EAAArb,QAAQgL,WACZ,OAAOvO,KAAKupB,iBAAiBhjB,GAE9B,KAAK,EAAAhD,QAAQimB,WACZ,OAAOxpB,KAAKypB,iBAAiBljB,GAE9B,KAAK,EAAAhD,QAAQC,QACZ,OAAOxD,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,OAAQ,CAAEsR,MAAOtR,IAEjE,KAAK,EAAA9U,QAAQE,iBACZ,OAAOzD,KAAKkpB,gBAAgB3iB,EAAM,QAEnC,KAAK,EAAAhD,QAAQG,YACZ,OAAO1D,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,SAEhD,KAAK,EAAA9U,QAAQU,QACZ,OAAOjE,KAAK0pB,kBAAkBnjB,EAAM8R,EACnC9R,EAAKsF,OAAO/K,MAAQ,EAAAyC,QAAQsB,aAAe,MAAQ,QAErD,KAAK,EAAAtB,QAAQM,aACb,KAAK,EAAAN,QAAQO,eACb,KAAK,EAAAP,QAAQI,YACb,KAAK,EAAAJ,QAAQmB,SACb,KAAK,EAAAnB,QAAQuB,OACZ,OAAO9E,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,QAEhD,KAAK,EAAA9U,QAAQyB,aACZ,OAAOhF,KAAK4pB,mBAAmBrjB,GAEhC,KAAK,EAAAhD,QAAQoB,cACZ,OAAO3E,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,UAEhD,KAAK,EAAA9U,QAAQqB,UACZ,OAAO5E,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,UAEhD,KAAK,EAAA9U,QAAQsB,aACZ,OAAO7E,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,OAEhD,KAAK,EAAA9U,QAAQQ,WACZ,OAAO/D,KAAK6pB,iBAAiBtjB,GAE9B,KAAK,EAAAhD,QAAQW,eACZ,OAAOlE,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,QAEhD,KAAK,EAAA9U,QAAQY,aACZ,OAAOnE,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,QAEhD,KAAK,EAAA9U,QAAQS,UACb,KAAK,EAAAT,QAAQc,iBACb,KAAK,EAAAd,QAAQe,eACZ,OAAOtE,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,MAEhD,KAAK,EAAA9U,QAAQK,gBACZ,OAAO5D,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,MAEhD,KAAK,EAAA9U,QAAQgB,aACZ,OAAOvE,KAAK8pB,mBAAmBvjB,GAEhC,KAAK,EAAAhD,QAAQuK,OACZ,OAAO9N,KAAK+pB,aAAaxjB,GAE1B,KAAK,EAAAhD,QAAQiB,QACZ,OAAOxE,KAAKgqB,cAAczjB,GAE3B,KAAK,EAAAhD,QAAQa,eACZ,OAAOpE,KAAKiqB,qBAAqB1jB,GAElC,KAAK,EAAAhD,QAAQwB,OACZ,OAAO/E,KAAKkqB,aAAa3jB,GAE1B,KAAK,EAAAhD,QAAQkB,iBACZ,OAAOzE,KAAKmqB,cAAc5jB,GAE3B,KAAK,EAAAhD,QAAQuH,SACZ,OAAO9K,KAAKoqB,eAAe7jB,GAE5B,KAAK,EAAAhD,QAAQyH,QACZ,OAAOhL,KAAKqqB,cAAc9jB,GAG5B,OAAO,IACR,CAEA,cAAA+jB,CAAe/jB,EAAsBwf,GACpC,OAAO/lB,KAAK0lB,eAAenf,EAAKb,SAAUqgB,EAC3C,CAEA,cAAAL,CAAe6E,EAAyBxE,GACvC,GAAa,MAATwE,EACH,OAAO,KAER,IAAIjlB,EAASilB,EAAMC,SAAQ7nB,GAAK3C,KAAKmoB,cAAcxlB,KAAI6Q,QAAO7Q,GAAU,MAALA,IAKnE,OAHIojB,GACH7G,EAAe6G,EAAMzgB,GAEfA,CACR,CAEA,eAAA4jB,CAAgB3iB,EAAsBsY,EAAsC1Y,GAC3E,OAAOnG,KAAK4e,cAAcC,EAAS1Y,EAAOnG,KAAKsqB,eAAe/jB,GAC/D,CAEA,iBAAAmjB,CAAkBnjB,EAAsB8R,EAAYwG,EAAiB1Y,GACpE,OAAO2Y,EAAgBzG,EAAIwG,EAAS1Y,EAAOnG,KAAKsqB,eAAe/jB,GAChE,CAEA,eAAA6hB,CAAgB7hB,GACf,IAAIjB,EAAStF,KAAK4e,cAAc,KAEhC,MAAM/U,EAAQ7J,KAAKgkB,UAAUzd,EAAKkF,WAClClF,EAAKkT,OAALlT,EAAKkT,KAAS5P,GAAO3B,gBAAgBuR,MAErCzZ,KAAKyqB,YAAYlkB,EAAMjB,GACvBtF,KAAKsqB,eAAe/jB,EAAMjB,GAC1BtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GACtCtF,KAAK0qB,uBAAuBplB,EAAOuE,MAAOtD,GAE1C,MAAMqT,EAAYrT,EAAKqT,WAAa/P,GAAO3B,gBAAgB0R,UAM3D,OAJIA,GACHtU,EAAOqlB,UAAUC,IAAI5qB,KAAKknB,eAAetN,EAAUhX,GAAIgX,EAAU7P,QAG3DzE,CACR,CAEA,mBAAAulB,CAAoBhhB,EAAY1D,GAC/BnG,KAAK0qB,uBAAuB7gB,EAAO1D,EACpC,CAEA,sBAAAukB,CAAuB7gB,EAAY1D,GACrB,MAATA,IAGAA,EAAMjD,QACT2G,EAAa,MAAI1D,EAAMjD,OAGpBiD,EAAM+S,WACTrP,EAAM,aAAe1D,EAAM+S,UAE7B,CAEA,eAAAwP,CAAgBniB,GACf,IAAIjB,EAAStF,KAAK4e,cAAc,KAKhC,GAHA5e,KAAKsqB,eAAe/jB,EAAMjB,GAC1BtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAElCiB,EAAK0F,KACR3G,EAAO2G,KAAO1F,EAAK0F,UACb,GAAG1F,EAAK3D,GAAI,CAClB,MAAMkoB,EAAM9qB,KAAKqd,SAAS4E,aAAalgB,KACrCwI,MAAKwgB,GAAMA,EAAGnoB,IAAM2D,EAAK3D,IAAwB,aAAlBmoB,EAAGhoB,aACpCuC,EAAO2G,KAAO6e,GAAKhoB,M,CAGpB,OAAOwC,CACR,CAEA,aAAAqjB,CAAcpiB,GACb,IAAIjB,EAAStF,KAAK4e,cAAc,OAShC,OAPAtZ,EAAOuE,MAAMmhB,QAAU,eACvB1lB,EAAOuE,MAAMoE,SAAW,WACxB3I,EAAOuE,MAAMohB,WAAa,MAE1BjrB,KAAKsqB,eAAe/jB,EAAMjB,GAC1BtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAE/BA,CACR,CAEA,WAAAsjB,CAAYriB,GACX,IAAIjB,EAAStF,KAAK4e,cAAc,OAUhC,OARA5e,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAElCtF,KAAKqd,UACRrd,KAAKqd,SAAS6N,kBAAkB3kB,EAAKqD,IAAK5J,KAAK+f,aAAamD,MAAK/Z,IAChE7D,EAAOsE,IAAMT,CAAC,IAIT7D,CACR,CAEA,UAAAujB,CAAWtiB,GACV,OAAOvG,KAAK6f,aAAaN,eAAehZ,EAAK8F,KAC9C,CAEA,iBAAAyc,CAAkBviB,GACjB,OAAOvG,KAAKL,QAAQ6c,eAAiBxc,KAAK6f,aAAaN,eAAehZ,EAAK8F,MAAQ,IACpF,CAEA,WAAA4c,CAAY1iB,GACX,MAAkB,gBAAdA,EAAK2G,MACDlN,KAAK4e,cAAc,MAGpB,IACR,CAEA,cAAAwL,CAAe7jB,GACd,OAAIvG,KAAKL,QAAQ+c,cACT1c,KAAKkpB,gBAAgB3iB,EAAM,OAE5BvG,KAAKsqB,eAAe/jB,EAC5B,CAEA,aAAA8jB,CAAc9jB,GACb,OAAIvG,KAAKL,QAAQ+c,cACT1c,KAAKkpB,gBAAgB3iB,EAAM,OAE5B,IACR,CAEA,YAAAyiB,CAAaziB,GACZ,IAAI2L,EAAOlS,KAAK4e,cAAc,QAG9B,OAFA1M,EAAKrI,MAAMshB,WAAa5kB,EAAK6G,KAC7B8E,EAAKkN,UAAY,MAAM7Y,EAAK8G,QACrB6E,CACR,CAEA,uBAAAmX,CAAwB9iB,GACvB,IAAIjB,EAAStF,KAAK4e,cAAc,OAGhC,OAFA5e,KAAKolB,mBAAmBxf,KAAKW,EAAK3D,IAClC0C,EAAOgH,YAAc,GAAGtM,KAAKolB,mBAAmB1R,SACzCpO,CACR,CAEA,sBAAAgkB,CAAuB/iB,GACtB,IAAIjB,EAAStF,KAAK4e,cAAc,OAGhC,OAFA5e,KAAKsgB,kBAAkB1a,KAAKW,EAAK3D,IACjC0C,EAAOgH,YAAc,GAAGtM,KAAKsgB,kBAAkB5M,SACxCpO,CACR,CAEA,SAAAyjB,CAAUxiB,GACT,IAAI6kB,EAAUprB,KAAK4e,cAAc,QAIjC,GAFAwM,EAAQhM,UAAY,SAEhBpf,KAAKL,QAAQuc,aAAc,CAC9BkP,EAAQ1f,UAAY1L,KAAKqrB,eACzB,IAAIC,EA2bP,SAA8C/kB,EAAsBzF,GAGnE,IAFA,IAAI+K,EAAStF,EAAKsF,OAED,MAAVA,GAAkBA,EAAO/K,MAAQA,GACvC+K,EAASA,EAAOA,OAEjB,OAAUA,CACX,CAlce0f,CAAyBhlB,EAAM,EAAAhD,QAAQ0H,YAAYwO,KAC/DzZ,KAAKwgB,YAAY5a,KAAK,CAAE0lB,QAAOpZ,KAAMkZ,G,CAGtC,OAAOA,CACR,CAEA,mBAAA/C,CAAoB9hB,GACnB,IAAIjB,EAAStF,KAAK4e,cAAc,QAEhC,OADAtZ,EAAO1C,GAAK2D,EAAKa,KACV9B,CACR,CAEA,SAAAgjB,CAAU/hB,GACT,GAAIA,EAAKqG,SACR,OAAO,KAER,MAAMtH,EAAStF,KAAK4e,cAAc,QAQlC,GANIrY,EAAK3D,KACR0C,EAAO1C,GAAK2D,EAAK3D,IAElB5C,KAAKyqB,YAAYlkB,EAAMjB,GACvBtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAElCiB,EAAK8H,cAAe,CACvB,MAAMmd,EAAUxrB,KAAK4e,cAAcrY,EAAK8H,eACxCrO,KAAKsqB,eAAe/jB,EAAMilB,GAC1BlmB,EAAO+Z,YAAYmM,E,MAGnBxrB,KAAKsqB,eAAe/jB,EAAMjB,GAG3B,OAAOA,CACR,CAEA,WAAAijB,CAAYhiB,GACX,IAAIjB,EAAStF,KAAK4e,cAAc,SAiBhC,OAfA5e,KAAKkgB,mBAAmBta,KAAK5F,KAAKmgB,qBAClCngB,KAAKggB,oBAAoBpa,KAAK5F,KAAKigB,sBACnCjgB,KAAKigB,qBAAuB,CAAC,EAC7BjgB,KAAKmgB,oBAAsB,CAAE9M,IAAK,EAAGxB,IAAK,GAEtCtL,EAAKoK,SACRrL,EAAO+Z,YAAYrf,KAAKyrB,mBAAmBllB,EAAKoK,UAEjD3Q,KAAKyqB,YAAYlkB,EAAMjB,GACvBtF,KAAKsqB,eAAe/jB,EAAMjB,GAC1BtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAEtCtF,KAAKigB,qBAAuBjgB,KAAKggB,oBAAoB0L,MACrD1rB,KAAKmgB,oBAAsBngB,KAAKkgB,mBAAmBwL,MAE5CpmB,CACR,CAEA,kBAAAmmB,CAAmB9a,GAClB,IAAIrL,EAAStF,KAAK4e,cAAc,YAEhC,IAAK,IAAIvL,KAAO1C,EAAS,CACxB,IAAIgb,EAAU3rB,KAAK4e,cAAc,OAE7BvL,EAAIvC,QACP6a,EAAQ9hB,MAAMiH,MAAQuC,EAAIvC,OAE3BxL,EAAO+Z,YAAYsM,E,CAGpB,OAAOrmB,CACR,CAEA,cAAAkjB,CAAejiB,GACd,IAAIjB,EAAStF,KAAK4e,cAAc,MAUhC,OARA5e,KAAKmgB,oBAAoB9M,IAAM,EAE/BrT,KAAKyqB,YAAYlkB,EAAMjB,GACvBtF,KAAKsqB,eAAe/jB,EAAMjB,GAC1BtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAEtCtF,KAAKmgB,oBAAoBtO,MAElBvM,CACR,CAEA,eAAAmjB,CAAgBliB,GACf,IAAIjB,EAAStF,KAAK4e,cAAc,MAEhC,MAAMT,EAAMne,KAAKmgB,oBAAoB9M,IAuBrC,OArBI9M,EAAK4L,cACkB,WAAtB5L,EAAK4L,eACRnS,KAAKigB,qBAAqB9B,GAAO7Y,EACjCA,EAAOsmB,QAAU,GACP5rB,KAAKigB,qBAAqB9B,KACpCne,KAAKigB,qBAAqB9B,GAAKyN,SAAW,EAC1CtmB,EAAOuE,MAAMmhB,QAAU,QAGxBhrB,KAAKigB,qBAAqB9B,GAAO,KAGlCne,KAAKyqB,YAAYlkB,EAAMjB,GACvBtF,KAAKsqB,eAAe/jB,EAAMjB,GAC1BtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAElCiB,EAAK2L,OACR5M,EAAOumB,QAAUtlB,EAAK2L,MAEvBlS,KAAKmgB,oBAAoB9M,KAAO/N,EAAOumB,QAEhCvmB,CACR,CAEA,gBAAAikB,CAAiBhjB,GAChB,IAAIjB,EAASsZ,EAAc,OAE3B,OADA5e,KAAKsqB,eAAe/jB,EAAMjB,GACnBA,CACR,CAEA,gBAAAmkB,CAAiBljB,GAChB,IAAIulB,EAAY/M,EAAiB,OAEjC+M,EAAUC,aAAa,QAASxlB,EAAKylB,cAErC,MAAM1mB,EAAStF,KAAKisB,sBAAsB1lB,GAgB1C,OAdIA,EAAK2lB,WAAWtpB,IACnB5C,KAAKqd,UAAU6N,kBAAkB3kB,EAAK2lB,UAAUtpB,GAAI5C,KAAK+f,aACvDmD,MAAK/Z,GAAK7D,EAAOymB,aAAa,OAAQ5iB,KAGzC2iB,EAAUzM,YAAY/Z,GAEtB6mB,uBAAsB,KACrB,MAAMC,EAAMN,EAAUnqB,kBAA0B0qB,UAEhDP,EAAUC,aAAa,QAAS,GAAGO,KAAKC,KAAKH,EAAGjjB,EAAKijB,EAAGtb,UACxDgb,EAAUC,aAAa,SAAU,GAAGO,KAAKC,KAAKH,EAAGI,EAAIJ,EAAGjR,UAAU,IAG5D2Q,CACR,CAEA,qBAAAG,CAAsB1lB,GACrB,MAAMjB,EAASyZ,EAAiBxY,EAAKsY,SACrCG,OAAO4D,QAAQrc,EAAK6d,OAAOlb,SAAQ,EAAEyZ,EAAG3N,KAAO1P,EAAOymB,aAAapJ,EAAG3N,KAEtE,IAAK,IAAIxG,KAASjI,EAAKb,SAClB8I,EAAM1N,MAAQ,EAAAyC,QAAQimB,WACzBlkB,EAAO+Z,YAAYrf,KAAKisB,sBAAsBzd,IAE9ClJ,EAAO+Z,gBAAe,IAAAoN,SAAQzsB,KAAKmoB,cAAc3Z,KAInD,OAAOlJ,CACR,CAEA,gBAAAukB,CAAiBtjB,GAChB,MAAMmmB,EAAOnmB,EAAKb,SAAS6E,MAAKhF,GAAMA,EAAGzE,MAAQ,EAAAyC,QAAQU,UAEzD,GAAIsC,EAAKJ,OAAO+H,WACf,OAAO4Q,EAAgBzG,EAAW,QAAS,KAAMrY,KAAK0lB,eAAe,CAACgH,KAGvE,MAAMC,EAASpmB,EAAKb,SAAS6E,MAAKhF,GAAMA,EAAGzE,MAAQ,EAAAyC,QAAQS,YAC3D,OAAO8a,EAAgBzG,EAAW,QAAS,KAAMrY,KAAK0lB,eAAe,CAACgH,EAAMC,IAC7E,CAEA,kBAAA7C,CAAmBvjB,GAClB,MAAMb,EAAW,GAMjB,OAJAA,EAASE,KAAKkZ,EAAgBzG,EAAW,KAAM,KAAM,CAAC9R,EAAKJ,MAAMgI,WAAa,OAC9EzI,EAASE,QAAQ5F,KAAK0lB,eAAenf,EAAKb,WAC1CA,EAASE,KAAKkZ,EAAgBzG,EAAW,KAAM,KAAM,CAAC9R,EAAKJ,MAAMiI,SAAW,OAErE0Q,EAAgBzG,EAAW,OAAQ,KAAM3S,EACjD,CAEA,aAAAskB,CAAczjB,GACb,MAAMb,EAAW,GACXknB,GAAU,IAAAtL,OAAM/a,EAAKb,UAAUyD,GAAKA,EAAErI,OAEtC+rB,EAAMD,EAAQ,EAAArpB,QAAQc,kBACtByoB,EAAMF,EAAQ,EAAArpB,QAAQe,gBACtByoB,EAAUF,EAAM/N,EAAgBzG,EAAW,KAAM,MAAM,IAAAoU,SAAQzsB,KAAKmoB,cAAc0E,KAAS,KAC3FG,EAAUF,EAAMhO,EAAgBzG,EAAW,KAAM,MAAM,IAAAoU,SAAQzsB,KAAKmoB,cAAc2E,KAAS,KAE3FG,EAAWnO,EAAgBzG,EAAW,KAAM,KAAM,CAAC9R,EAAKJ,OAAOkH,MAAQ,MAc7E,OAZI0f,GAAWC,EACdtnB,EAASE,KAAKkZ,EAAgBzG,EAAW,aAAc,KAAM,CAAC4U,EAAUD,EAASD,KACxEA,EACTrnB,EAASE,KAAKkZ,EAAgBzG,EAAW,QAAS,KAAM,CAAC4U,EAAUF,KAC1DC,EACTtnB,EAASE,KAAKkZ,EAAgBzG,EAAW,SAAU,KAAM,CAAC4U,EAAUD,KAEpEtnB,EAASE,KAAKqnB,GAGfvnB,EAASE,QAAQ5F,KAAK0lB,eAAekH,EAAQ,EAAArpB,QAAQU,SAASyB,WAEvDoZ,EAAgBzG,EAAW,OAAQ,KAAM3S,EACjD,CAEA,oBAAAukB,CAAqB1jB,GACpB,MAAMb,EAAW,GACXknB,GAAU,IAAAtL,OAAM/a,EAAKb,UAAUyD,GAAKA,EAAErI,OAEtC+rB,EAAMD,EAAQ,EAAArpB,QAAQc,kBACtByoB,EAAMF,EAAQ,EAAArpB,QAAQe,gBACtByoB,EAAUF,EAAM/N,EAAgBzG,EAAW,KAAM,MAAM,IAAAoU,SAAQzsB,KAAKmoB,cAAc0E,KAAS,KAC3FG,EAAUF,EAAMhO,EAAgBzG,EAAW,KAAM,MAAM,IAAAoU,SAAQzsB,KAAKmoB,cAAc2E,KAAS,KAC3FI,EAAWpO,EAAgBzG,EAAW,KAAM,MAKlD,OAHA3S,EAASE,KAAKkZ,EAAgBzG,EAAW,UAAW,KAAM,CAAC6U,EAAUF,EAASD,KAC9ErnB,EAASE,QAAQ5F,KAAK0lB,eAAekH,EAAQ,EAAArpB,QAAQU,SAASyB,WAEvDoZ,EAAgBzG,EAAW,OAAQ,KAAM3S,EACjD,CAEA,kBAAAkkB,CAAmBrjB,GAClB,MAAMsY,EAA+C,QAArCtY,EAAKJ,MAAM6H,sBAAkC,QAAU,SACjE1I,EAAStF,KAAK0pB,kBAAkBnjB,EAAM8R,EAAWwG,GAMvD,OAJItY,EAAKJ,MAAMkH,MACd/H,EAAO+Z,YAAYP,EAAgBzG,EAAW,KAAM,KAAM,CAAC9R,EAAKJ,MAAMkH,QAGhE/H,CACR,CAEA,YAAA4kB,CAAa3jB,GACZ,MAAMjB,EAAStF,KAAK0pB,kBAAkBnjB,EAAM8R,EAAW,QAEvD,OAAO9R,EAAKJ,MAAM8H,UACjB,IAAK,MAAO3I,EAAOuE,MAAMsjB,eAAiB,WAAY,MACtD,IAAK,SAAU7nB,EAAOuE,MAAMsjB,eAAiB,YAG9C,OAAO7nB,CACR,CAEA,YAAAykB,CAAaxjB,GACZ,MAAMjB,EAASwZ,EAAgBzG,EAAW,MAM1C,OAJArY,KAAKyqB,YAAYlkB,EAAMjB,GACvBtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GACtCtF,KAAKsqB,eAAe/jB,EAAMjB,GAEnBA,CACR,CAEA,aAAA6kB,CAAc5jB,GACb,MAAMjB,EAASwZ,EAAgBzG,EAAW,UAE1CrY,KAAKyqB,YAAYlkB,EAAMjB,GACvBtF,KAAKulB,kBAAkBhf,EAAKF,SAAUf,GAErBtF,KAAKsqB,eAAe/jB,GAErC,IAAK,IAAIiI,KAASxO,KAAKsqB,eAAe/jB,GACrCjB,EAAO+Z,YAAYP,EAAgBzG,EAAW,MAAO,KAAM,CAC1DyG,EAAgBzG,EAAW,MAAO,KAAM,CAAC7J,OAI3C,OAAOlJ,CACR,CAGA,iBAAAigB,CAAkB1b,EAA+BujB,GAChD,IAAK,IAAIzK,KAAK9Y,EACT8Y,EAAEziB,WAAW,KAChBktB,EAAMrB,aAAapJ,EAAEiE,MAAM,GAAI/c,EAAM8Y,IAErCyK,EAAMvjB,MAAM8Y,GAAK9Y,EAAM8Y,EAG1B,CAEA,WAAA8H,CAAYhqB,EAAuB2sB,GAC9B3sB,EAAMiL,YACT0hB,EAAM1hB,UAAYjL,EAAMiL,WAErBjL,EAAMgL,WACT2hB,EAAMzC,UAAUC,IAAI5qB,KAAKqjB,iBAAiB5iB,EAAMgL,WAClD,CAEA,SAAAuY,CAAUvY,GACT,OAAOA,GAAazL,KAAK8f,WAAWrU,EACrC,CAEA,cAAAyb,CAAetkB,EAAYyqB,GAC1B,MAAO,GAAGrtB,KAAK0L,iBAAiB9I,KAAMyqB,GACvC,CAEA,YAAAhC,GACC,MAAO,GAAGrrB,KAAK0L,oBAChB,CAEA,aAAAoX,CAAcwK,EAAmB7lB,EAAgCgY,EAAkB,MAClF,IAAIna,EAAS,GAAGgoB,UAEhB,IAAK,MAAMnP,KAAO1W,EACb0W,EAAIje,WAAW,OAGnBoF,GAAU,KAAK6Y,MAAQ1W,EAAO0W,WAM/B,OAHIsB,IACHna,GAAUma,GAEJna,EAAS,OACjB,CAEA,gBAAAkiB,CAAiB5kB,EAAYyqB,GAC5B,MAAO,GAAGrtB,KAAK0L,iBAAiB9I,KAAMyqB,GACvC,CAEA,kBAAA3F,CAAmBrb,EAAchC,EAAczH,EAAY2qB,GAW1D,MAAO,IALMlhB,EAAKmhB,QAAQ,SAASllB,IAClC,IAAI+kB,EAAM9W,SAASjO,EAAEmlB,UAAU,GAAI,IAAM,EACzC,MAAO,YAAYztB,KAAKwnB,iBAAiB5kB,EAAIyqB,OAASE,KAAa,MAPpD,CACf,IAAO,MACP,MAAS,QAQkBljB,IAAS,KACtC,CAEA,mBAAAsd,CAAoBld,GA2CnB,MA1Cc,CACbijB,KAAM,OACNpjB,OAAQ,OACRqjB,QAAS,UACTC,YAAa,cACbC,YAAa,cACbC,WAAY,cACZC,WAAY,cACZC,YAAa,uBAMbC,MAAO,WACPC,eAAgB,WAChBC,gBAAiB,wBACjBC,wBAAyB,wBACzBC,uBAAwB,sBACxBC,QAAS,mBACTC,iBAAkB,kBAClBC,qBAAsB,oBACtBC,0BAA2B,sBAC3BC,gBAAiB,qBACjBC,MAAO,iBACPC,eAAgB,iBAChBC,iBAAkB,oBAClBC,2BAA4B,cAC5BC,cAAe,kBACfC,YAAa,OACbC,eAAgB,uBAChBC,cAAe,uBACfC,eAAgB,wBAChBC,QAAS,SACTC,QAAS,SACTC,aAAc,aACdC,OAAQ,SACRC,kBAAmB,kBACnBC,0BAA2B,kBAC3BC,iBAAmB,eAGLjlB,IAAWA,CAC3B,CAEA,eAAA0X,GACMniB,KAAKL,QAAQuc,eAGlByT,aAAa3vB,KAAKygB,aAElBzgB,KAAKygB,YAAcmP,YAAW,KAC7B,MAAMC,GAAe,IAAAC,uBAErB,IAAK,IAAIC,KAAO/vB,KAAKwgB,aACpB,IAAAwP,eAAcD,EAAI7d,KAAM6d,EAAIzE,MAAOtrB,KAAK0hB,eAAgBmO,E,GAEvD,KACJ,E,4GChzCD,MAAMI,EAAsB,CAAErgB,IAAK,EAAGyK,OAAQ,OAAQxQ,MAAO,QAwF7D,SAASqmB,EAAcxc,GACtB,OAAOyc,WAAWzc,EACnB,CAvFA,+BAAoCoY,EAAyBzO,SAAS9D,MACrE,MAAM6W,EAAO/S,SAASuB,cAAc,OACpCwR,EAAKvmB,MAAMiH,MAAQ,QAEnBgb,EAAUzM,YAAY+Q,GACtB,MAAM9qB,EAAS,IAAM8qB,EAAKC,YAG1B,OAFAvE,EAAUwE,YAAYF,GAEf9qB,CACR,EAEA,yBAA8BiB,EAAmBkT,EAAsBiI,EAAwBmO,EAAuB,KAClH,MAAMzJ,EAAI7f,EAAKgqB,QAAQ,KAEjBC,EAAMjqB,EAAKkqB,wBACXC,EAAMtK,EAAEqK,wBACRE,EAAMC,iBAAiBxK,GAE1ByK,EAAWpX,GAAM/F,OAAS,EAAI+F,EAAK/W,KAAIouB,IAAK,CACjDlhB,IAAKsgB,EAAcY,EAAE7iB,UACrBoM,OAAQyW,EAAEzW,OACVxQ,MAAOinB,EAAEjnB,UACNknB,MAAK,CAACvb,EAAGC,IAAMD,EAAE5F,IAAM6F,EAAE7F,MAAO,CAACqgB,GAE/Be,EAAUH,EAASA,EAASnd,OAAS,GACrCud,EAAWP,EAAI5f,MAAQ+e,EACvB9X,EAAOmY,EAAcxO,GACxB,IAAI9R,EAAMohB,EAAQphB,IAAMmI,EAExB,GAAInI,EAAMqhB,EACN,KAAOrhB,EAAMqhB,GAAYJ,EAASnd,OAhC1B,GAgC4C9D,GAAOmI,EACvD8Y,EAASjrB,KAAK,IAAKqqB,EAAYrgB,IAAKA,IAI5C,MAAMshB,EAAaf,WAAWQ,EAAIO,YAC5BC,EAAUT,EAAI7c,KAAOqd,EACrBrd,GAAQ2c,EAAI3c,KAAOsd,GAAWtB,EAC9BE,EAAMc,EAAStmB,MAAKumB,GAAgB,SAAXA,EAAEjnB,OAAoBinB,EAAElhB,IAAMiE,IAE7D,GAAU,MAAPkc,EACC,OAEJ,IAAIjf,EAAgB,EAEpB,GAAiB,SAAbif,EAAIlmB,OAAiC,UAAbkmB,EAAIlmB,MAAmB,CACrD,MAAMgnB,EAAWO,MAAMC,KAAKjL,EAAEkL,iBAAiB,IAAI/qB,EAAKmF,cAClD6lB,EAAUV,EAASW,QAAQjrB,GAAQ,EAC7BkrB,EAAQpU,SAASqU,cACvBD,EAAME,SAASprB,EAAM,GAEvBgrB,EAAUV,EAASnd,OACtB+d,EAAMG,aAAaf,EAASU,IAE5BE,EAAMI,YAAYzL,GAGnB,MAAMzN,EAAmB,UAAboX,EAAIlmB,MAAoB,GAAM,EAC9BioB,EAASL,EAAMhB,wBACrBlhB,EAASuiB,EAAOje,KAAO8E,EAAMmZ,EAAOhhB,OAAS4f,EAAI7c,KAAOqd,GAE9DpgB,EAAQif,EAAIngB,IAAML,EAASsgB,C,MAErB/e,EAAQif,EAAIngB,IAAMiE,EAOtB,OAJAtN,EAAK6Y,UAAY,SACjB7Y,EAAKsD,MAAMsjB,eAAiB,UAC5B5mB,EAAKsD,MAAMkoB,YAAc,GAAGjhB,EAAMsD,QAAQ,OAElC2b,EAAI1V,QACR,IAAK,MACL,IAAK,YACD9T,EAAKsD,MAAMsjB,eAAiB,YAC5B5mB,EAAKsD,MAAMmoB,oBAAsB,SACjC,MAEJ,IAAK,SACL,IAAK,QACL,IAAK,aACDzrB,EAAKsD,MAAMsjB,eAAiB,YAGxC,C,iHC/FA,eAEA,MAAsB8E,GAAtB,gBAMA,4BAAiCA,EAAjC,c,oBACC,KAAAnxB,KAAO,EAAAyC,QAAQ4lB,QAChB,GAEA,2BAAgC8I,EAAhC,c,oBACC,KAAAnxB,KAAO,EAAAyC,QAAQ6lB,OAChB,E,sHCbA,eAEA,SAEA,MAAa8I,UAA4C,EAAArc,KAKrD,WAAApW,CAAY2Z,EAAqBrZ,EAAc2K,GAC3C2O,MAAMD,EAAKrZ,GACXC,KAAKsZ,gBAAkB5O,CAC3B,EARJ,iBAWA,8BAAmCwnB,EAC/B,WAAAzyB,CAAY2Z,EAAqBrZ,EAAc2K,GAC3C2O,MAAMD,EAAKrZ,EAAM2K,EACrB,CAEA,QAAAtI,CAASE,GACLtC,KAAKuhB,MAAQvhB,KAAKsZ,gBAAgBnU,WAAW7C,EAAM,WAAY,EAAA6vB,YACnE,GAGJ,6BAAkCD,EAC9B,WAAAzyB,CAAY2Z,EAAqBrZ,EAAc2K,GAC3C2O,MAAMD,EAAKrZ,EAAM2K,EACrB,CAEA,QAAAtI,CAASE,GACLtC,KAAKuhB,MAAQvhB,KAAKsZ,gBAAgBnU,WAAW7C,EAAM,UAAW,EAAA8vB,WAClE,E,wFChCJ,eAGA,SAEA,MAAaC,UAAsB,EAAAxc,KAG/B,WAAApW,CAAY2Z,EAAqBrZ,EAAc2K,GAC3C2O,MAAMD,EAAKrZ,GACXC,KAAKsZ,gBAAkB5O,CAC3B,CAQA,QAAAtI,CAASE,GACL0c,OAAOC,OAAOjf,MAAM,IAAAsyB,oBAAmBhwB,EAAMtC,KAAK8B,SAASlC,YAC3DI,KAAKmhB,cAAgBnhB,KAAKsZ,gBAAgBzQ,mBAAmBvG,EACjE,EAjBJ,iB,0NCLA,eACA,SAyEA,SAAgBuX,EAAetT,EAAe/D,GAC1C,IAAI8C,EAAoB,CACpB1C,GAAIJ,EAAIK,KAAK0D,EAAM,SACnBgsB,UAAW,IAGf,IAAK,IAAI5vB,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,gBACDpB,EAAOktB,WAAahwB,EAAIK,KAAKF,EAAG,OAChC,MACJ,IAAK,cACD2C,EAAOitB,UAAU3sB,KAAK6sB,EAA6B9vB,EAAGH,IAKlE,OAAO8C,CACX,CAEA,SAAgB2D,EAAuB1C,EAAe/D,GAClD,IAAI8C,EAA4B,CAC5B1C,GAAIJ,EAAIK,KAAK0D,EAAM,iBACnBmsB,OAAQ,IAGZ,IAAK,IAAI/vB,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,OACDpB,EAAO8B,KAAO5E,EAAIK,KAAKF,EAAG,OAC1B,MACJ,IAAK,iBACD2C,EAAOqtB,eAAiBnwB,EAAIK,KAAKF,EAAG,OACpC,MACJ,IAAK,eACD2C,EAAOstB,mBAAqBpwB,EAAIK,KAAKF,EAAG,OACxC,MACJ,IAAK,YACD2C,EAAOutB,UAAYrwB,EAAIK,KAAKF,EAAG,OAC/B,MACJ,IAAK,MACD2C,EAAOotB,OAAO9sB,KAAKkE,EAAoBnH,EAAGH,IAKtD,OAAO8C,CACX,CAEA,SAAgBwE,EAAoBvD,EAAe/D,GAC/C,IAAI8C,EAAyB,CACzByE,MAAOvH,EAAImH,QAAQpD,EAAM,SAG7B,IAAK,IAAI5D,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,QACDpB,EAAO0E,MAAQxH,EAAIK,KAAKF,EAAG,OAC3B,MACJ,IAAK,aACD2C,EAAOwtB,QAAUtwB,EAAImH,QAAQhH,EAAG,OAChC,MACJ,IAAK,SACD2C,EAAOmF,OAASjI,EAAIK,KAAKF,EAAG,OAC5B,MACJ,IAAK,UACD2C,EAAO+G,KAAO7J,EAAIK,KAAKF,EAAG,OAC1B,MACJ,IAAK,QACD2C,EAAOytB,cAAgBvwB,EAAIK,KAAKF,EAAG,OACnC,MACJ,IAAK,iBACD2C,EAAO0tB,gBAAkBxwB,EAAIK,KAAKF,EAAG,OACrC,MACJ,IAAK,SACD2C,EAAO2tB,eAAiBzwB,EAAIK,KAAKF,EAAG,OACpC,MACJ,IAAK,MACD2C,EAAO4C,gBAAiB,IAAAC,0BAAyBxF,EAAGH,GACpD,MACJ,IAAK,MACD8C,EAAO8C,UAAW,IAAAC,oBAAmB1F,EAAGH,GAKpD,OAAO8C,CACX,CAEA,SAAgBmtB,EAA6BlsB,EAAe/D,GACxD,IAAI8C,EAAiC,CACjCyE,MAAOvH,EAAImH,QAAQpD,EAAM,SAG7B,IAAK,IAAI5D,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,gBACDpB,EAAO0E,MAAQxH,EAAImH,QAAQhH,EAAG,OAC9B,MACJ,IAAK,MACD2C,EAAO4tB,eAAiBppB,EAAoBnH,EAAGH,GAK3D,OAAO8C,CACX,CAEA,SAAgB6tB,EAA4B5sB,EAAe/D,GAEvD,IAAIgH,EAAOhH,EAAIuD,QAAQQ,EAAM,QACzBkD,EAAQD,GAAQhH,EAAIuD,QAAQyD,EAAM,SAClCE,EAAYD,GAASjH,EAAIuD,QAAQ0D,EAAO,aAE5C,OAAOC,EAAY,CACf9G,GAAIJ,EAAIK,KAAK0D,EAAM,kBACnB6sB,YAAa5wB,EAAIK,KAAK6G,EAAW,MACjCG,MAAOrH,EAAIK,KAAK4G,EAAO,UACvB,IACR,CA/IA,8BAAmClD,EAAe/D,GAC9C,IAAI8C,EAAkC,CAClCwe,WAAY,GACZuP,mBAAoB,GACpBC,eAAgB,IAGpB,IAAK,IAAI3wB,KAAKH,EAAIC,SAAS8D,GACvB,OAAQ5D,EAAE+D,WACN,IAAK,MACDpB,EAAOwe,WAAWle,KAAKiU,EAAelX,EAAGH,IACzC,MACJ,IAAK,cACD8C,EAAO+tB,mBAAmBztB,KAAKqD,EAAuBtG,EAAGH,IACzD,MACJ,IAAK,eACD8C,EAAOguB,eAAe1tB,KAAKutB,EAA4BxwB,EAAGH,IAKtE,OAAO8C,CACX,EAEA,mBAoBA,2BA6BA,wBAwCA,iCAmBA,+B,0HCvLA,eAEA,0BAA+BiuB,EAAmB1xB,GAA8B,GAmBhF,IAAuB8a,EAlBf9a,IACA0xB,EAAYA,EAAU/F,QAAQ,aAAc,KAEhD+F,EAgB8B,SADX5W,EAfO4W,GAgBdC,WAAW,GAAgB7W,EAAK8Q,UAAU,GAAK9Q,EAd3D,MAAMrX,GAAS,IAAImuB,WAAYC,gBAAgBH,EAAW,mBACpDI,GAQiBrW,EARahY,EAS7BgY,EAAIsW,qBAAqB,eAAe,IAAItnB,aADvD,IAA2BgR,EANvB,GAAIqW,EACA,MAAM,IAAIE,MAAMF,GAEpB,OAAOruB,CACX,EAUA,8BAAmCiB,GAC/B,OAAO,IAAIutB,eAAgBC,kBAAkBxtB,EACjD,EAEA,MAAa1G,EACT,QAAA4C,CAAS8D,EAAeG,EAAoB,MACxC,MAAMpB,EAAS,GAEf,IAAK,IAAIkP,EAAI,EAAG2Q,EAAI5e,EAAKkO,WAAWf,OAAQc,EAAI2Q,EAAG3Q,IAAK,CACpD,IAAIjN,EAAIhB,EAAKkO,WAAWuf,KAAKxf,GAEX,GAAdjN,EAAEmN,UAA+B,MAAbhO,GAAsBa,EAAcb,WAAaA,GACrEpB,EAAOM,KAAK2B,E,CAGpB,OAAOjC,CACX,CAEA,OAAAS,CAAQQ,EAAeG,GACnB,IAAK,IAAI8N,EAAI,EAAG2Q,EAAI5e,EAAKkO,WAAWf,OAAQc,EAAI2Q,EAAG3Q,IAAK,CACpD,IAAIjN,EAAIhB,EAAKkO,WAAWuf,KAAKxf,GAE7B,GAAkB,GAAdjN,EAAEmN,UAAkBnN,EAAcb,WAAaA,EAC/C,OAAOa,C,CAGf,OAAO,IACX,CAEA,WAAAgC,CAAYhD,EAAeG,EAAmButB,GAC1C,IAAI1uB,EAAKvF,KAAK+F,QAAQQ,EAAMG,GAC5B,OAAOnB,EAAKvF,KAAK6C,KAAK0C,EAAI0uB,QAAiB/pB,CAC/C,CAEH,KAAAka,CAAM7d,GACL,OAAO6qB,MAAMC,KAAK9qB,EAAK2tB,WACxB,CAEG,IAAArxB,CAAK0D,EAAeG,GAChB,IAAK,IAAI8N,EAAI,EAAG2Q,EAAI5e,EAAK2tB,WAAWxgB,OAAQc,EAAI2Q,EAAG3Q,IAAK,CACpD,IAAIgB,EAAIjP,EAAK2tB,WAAWF,KAAKxf,GAE7B,GAAIgB,EAAE9O,WAAaA,EACf,OAAO8O,EAAEqB,K,CAGjB,OAAO,IACX,CAEA,OAAAlN,CAAQnE,EAAeqP,EAAkBoE,EAAuB,MAC5D,IAAI7F,EAAMpT,KAAK6C,KAAK2C,EAAMqP,GAC1B,OAAOzB,EAAMmD,SAASnD,GAAO6F,CACjC,CAEH,OAAAvD,CAAQlQ,EAAeqP,EAAkBoE,EAAuB,MACzD,IAAI7F,EAAMpT,KAAK6C,KAAK2C,EAAMqP,GAC1B,OAAOzB,EAAMmD,SAASnD,EAAK,IAAM6F,CACrC,CAEA,SAAAkb,CAAU3uB,EAAeqP,EAAkBoE,EAAuB,MAC9D,IAAI7F,EAAMpT,KAAK6C,KAAK2C,EAAMqP,GAC1B,OAAOzB,EAAM+c,WAAW/c,GAAO6F,CACnC,CAEA,QAAApR,CAASrC,EAAeqP,EAAkBoE,EAAwB,MAC9D,OAAO,IAAAmb,gBAAep0B,KAAK6C,KAAK2C,EAAMqP,GAAWoE,EACrD,CAEA,UAAAxJ,CAAWjK,EAAeqP,EAAkBkE,EAAyB,EAAArJ,YAAYwF,KAC7E,OAAO,IAAAC,eAAcnV,KAAK6C,KAAK2C,EAAMqP,GAAWkE,EACpD,EAlEJ,cAqEA,MAAMsb,EAAkB,IAAIx0B,EAE5B,UAAew0B,C,uFCnGf,eACA,SAEA,MAAaC,UAAqB,EAAAze,KAGjC,WAAApW,CAAY2Z,EAAqBrZ,GAChCsZ,MAAMD,EAAKrZ,EACZ,CAEA,QAAAqC,CAASE,GACRtC,KAAK2hB,UAAW,IAAA4S,eAAcjyB,EAAMtC,KAAK8B,SAASlC,UACnD,EATD,gB,cC2BA,SAAgB40B,EAAoBjuB,EAAe/D,GAClD,IAAI8C,EAAS,CACZmvB,eAAgB,IAGjB,IAAK,IAAIlvB,KAAM/C,EAAIC,SAAS8D,GAC3B,OAAOhB,EAAGmB,WACT,IAAK,SACJpB,EAAOovB,gBAAkBlyB,EAAIK,KAAK0C,EAAI,OACtC,MAED,IAAK,WACL,IAAK,UACJD,EAAOmvB,eAAe7uB,KAAKpD,EAAIK,KAAK0C,EAAI,OAKxC,OAAOD,CACX,C,8FAlCA,yBAA8BiB,EAAe/D,GAC5C,IAAI8C,EAAS,CAAC,EAEd,IAAK,IAAIC,KAAM/C,EAAIC,SAAS8D,GAC3B,OAAOhB,EAAGmB,WACT,IAAK,iBAAkBpB,EAAOsc,eAAiBpf,EAAIiN,WAAWlK,EAAI,OAAQ,MAC1E,IAAK,aAAcD,EAAOqvB,cAAgBH,EAAoBjvB,EAAI/C,GAAM,MACxE,IAAK,YAAa8C,EAAOsvB,aAAeJ,EAAoBjvB,EAAI/C,GAAM,MACtE,IAAK,kBAAmB8C,EAAOuvB,gBAAkBryB,EAAIqF,SAAStC,EAAI,OAIjE,OAAOD,CACX,EAEA,uB,qFC9BA,eAIA,MAAawvB,UAAmB,EAAAjf,KAK5B,WAAApW,CAAY2Z,EAAqBrZ,EAAc2K,GAC3C2O,MAAMD,EAAKrZ,GACXC,KAAKsZ,gBAAkB5O,CAC3B,CAEA,QAAAtI,CAASE,GACLtC,KAAKsH,OAAStH,KAAKsZ,gBAAgBxS,gBAAgBxE,EACvD,EAZJ,c,oFCJA,eACA,QAEA,MAAayyB,UAAkB,EAAAlf,KAG3B,WAAApW,CAAY2Z,EAAqBrZ,GAC7BsZ,MAAMD,EAAKrZ,EACf,CAEA,QAAAqC,CAASE,GACLtC,KAAKsiB,OAAQ,IAAA0S,YAAW1yB,EAAMtC,KAAK8B,SAASlC,UAChD,EATJ,a,kJCFA,MAAaq1B,GAoCb,SAAgBC,EAAiB3uB,EAAe/D,GAC5C,IAAI8C,EAAyB,CACzB8B,KAAM5E,EAAIK,KAAK0D,EAAM,QACrBsc,OAAQ,CAAC,GAGb,IAAK,IAAItd,KAAM/C,EAAIC,SAAS8D,GAAO,CAC/B,IAAI4uB,EAAU3yB,EAAIuD,QAAQR,EAAI,WAC1B6vB,EAAS5yB,EAAIuD,QAAQR,EAAI,UAEzB4vB,EACA7vB,EAAOud,OAAOtd,EAAGmB,WAAalE,EAAIK,KAAKsyB,EAAS,OAE3CC,IACL9vB,EAAOud,OAAOtd,EAAGmB,WAAalE,EAAIK,KAAKuyB,EAAQ,W,CAIvD,OAAO9vB,CACX,CAEA,SAAgB+vB,EAAgB9uB,EAAe/D,GAC3C,IAAI8C,EAAwB,CACxB8B,KAAM5E,EAAIK,KAAK0D,EAAM,SAGzB,IAAK,IAAIhB,KAAM/C,EAAIC,SAAS8D,GACxB,OAAQhB,EAAGmB,WACP,IAAK,YAAapB,EAAOid,UAAY+S,EAAc/vB,EAAI/C,GAAM,MAC7D,IAAK,YAAa8C,EAAOmd,UAAY6S,EAAc/vB,EAAI/C,GAI/D,OAAO8C,CACX,CAEA,SAAgBgwB,EAAc/uB,EAAe/D,GACzC,MAAO,CACHggB,cAAehgB,EAAI+G,YAAYhD,EAAM,QAAS,YAC9CgvB,WAAY/yB,EAAI+G,YAAYhD,EAAM,KAAM,YACxCivB,WAAYhzB,EAAI+G,YAAYhD,EAAM,KAAM,YAEhD,CA9EA,aAsBA,sBAA2BA,EAAe/D,GACtC,IAAI8C,EAAS,IAAI2vB,EACbQ,EAAgBjzB,EAAIuD,QAAQQ,EAAM,iBAEtC,IAAK,IAAIhB,KAAM/C,EAAIC,SAASgzB,GACxB,OAAOlwB,EAAGmB,WACN,IAAK,YAAapB,EAAOod,YAAcwS,EAAiB3vB,EAAI/C,GAAM,MAClE,IAAK,aAAc8C,EAAO+c,WAAagT,EAAgB9vB,EAAI/C,GAInE,OAAO8C,CACX,EAEA,qBAqBA,oBAeA,iB,cCpCA,SAAgBowB,EAAS1B,GACrB,OAAOA,GAAwB,iBAATA,IAAsB5C,MAAMuE,QAAQ3B,EAC9D,C,wMAxCA,2BAAgCtoB,GAC/B,OAAOA,GAAW8hB,QAAQ,SAAU,KAAKA,QAAQ,QAAS,OAAOnG,aAClE,EAEA,qBAA0BtnB,GACtB,IAAI61B,EAAK71B,EAAK81B,YAAY,KAAO,EAIjC,MAAO,CAHY,GAAND,EAAU,GAAK71B,EAAK0tB,UAAU,EAAGmI,GACzB,GAANA,EAAU71B,EAAOA,EAAK0tB,UAAUmI,GAGnD,EAEA,uBAA4B71B,EAAc2sB,GACtC,IACI,MAAMoJ,EAAS,eAEf,OADY,IAAIC,IAAIh2B,EAAM+1B,EAASpJ,GAAMsJ,WAC9BvI,UAAUqI,EAAOpiB,O,CAC9B,MACE,MAAO,GAAGgZ,IAAO3sB,G,CAEzB,EAEA,iBAA+Bk2B,EAAYC,GACvC,OAAOD,EAAME,QAAO,CAAC3gB,EAAGrM,KACpBqM,EAAE0gB,EAAG/sB,IAAMA,EACJqM,IACR,CAAC,EACR,EAEA,wBAA6B4gB,GAC5B,OAAO,IAAIn1B,SAAQ,CAACC,EAASm1B,KAC5B,MAAMC,EAAS,IAAIC,WACnBD,EAAOE,UAAY,IAAMt1B,EAAQo1B,EAAOhxB,QACxCgxB,EAAOG,QAAU,IAAMJ,IACvBC,EAAOI,cAAcN,EAAK,GAE5B,EAEA,aAIA,oBAAyBpC,GACrB,MAAuB,iBAATA,GAAqBA,aAAgB2C,MACvD,EAEA,qBAAgBlT,EAAU3gB,KAAW8zB,GACjC,IAAKA,EAAQljB,OACT,OAAO5Q,EAEX,MAAM+zB,EAASD,EAAQE,QAEvB,GAAIpB,EAAS5yB,IAAW4yB,EAASmB,GAC7B,IAAK,MAAM1Y,KAAO0Y,EACVnB,EAASmB,EAAO1Y,IAEhBsF,EADY3gB,EAAOqb,KAASrb,EAAOqb,GAAO,CAAC,GAC5B0Y,EAAO1Y,IAEtBrb,EAAOqb,GAAO0Y,EAAO1Y,GAKjC,OAAOsF,EAAU3gB,KAAW8zB,EAChC,EAEA,yBAA8BvqB,GAC7B,MAAM/G,EAAiC,CAAC,EAExC,IAAK,MAAMyxB,KAAQ1qB,EAAKpE,MAAM,KAAM,CACnC,MAAOkW,EAAK/K,GAAO2jB,EAAK9uB,MAAM,KAC9B3C,EAAO6Y,GAAO/K,C,CAGf,OAAO9N,CACR,EAEA,0BAA+BuE,GAC9B,OAAOmV,OAAO4D,QAAQ/Y,GAAOnH,KAAI,CAACigB,EAAG3N,IAAM,GAAG2N,MAAM3N,MAAKvB,KAAK,IAC/D,EAEA,mBAA2BL,GAC1B,OAAOge,MAAMuE,QAAQviB,GAAOA,EAAM,CAACA,EACpC,C,uGClFA,eACA,SACA,SAGA,MAAaoW,UAAmB,EAAAnL,mBAAhC,c,oBACC,KAAAvd,KAAgB,EAAAyC,QAAQimB,WAGxB,KAAApF,MAAgC,CAAC,CAMlC,EAwFA,SAAS4S,EAAYzxB,GACpB,MAAO,CACN,OAAU,UAAI1C,KAAK0C,EAAI,SACvB,eAAgB,UAAIkK,WAAWlK,EAAI,SAAU,EAAAmK,YAAYC,MAAQ,MAEnE,CAQA,SAASsnB,EAAW7jB,GACnB,OAAOA,EAAInL,MAAM,IAClB,CAjHA,eAYA,2BAAgBwG,EAAgBlI,EAAemE,GAC9C,IAAIpF,EAAS,IAAIkkB,EAEjB,OAAQjjB,EAAKG,WACZ,IAAK,OACJpB,EAAOuZ,QAAU,OACjBG,OAAOC,OAAO3Z,EAAO8e,MAAO,CAAEtT,MAAO,OAAQqK,OAAQ,SACrD,MAED,IAAK,OACJ7V,EAAOuZ,QAAU,UACjBG,OAAOC,OAAO3Z,EAAO8e,MAAO,CAAE8S,GAAI,MAAOC,GAAI,MAAOC,GAAI,MAAOC,GAAI,QACnE,MAED,IAAK,OACJ/xB,EAAOuZ,QAAU,OACjB,MAED,IAAK,QACJvZ,EAAOuZ,QAAU,IACjB,MAED,IAAK,UACJvZ,EAAOuZ,QAAU,gBACjBG,OAAOC,OAAO3Z,EAAO8e,MAAO,CAAEtT,MAAO,OAAQqK,OAAQ,SACrD,MAED,QACC,OAAO,KAGT,IAAK,MAAMmc,KAAM,UAAIlT,MAAM7d,GAC1B,OAAO+wB,EAAG5wB,WACT,IAAK,QACJpB,EAAO0mB,aAAesL,EAAGzgB,MACzB,MAED,IAAK,YACJvR,EAAO8e,MAAMmT,KAAOD,EAAGzgB,MACvB,MAED,IAAK,OACJ,MAAO2gB,EAAIC,GAAMR,EAAWK,EAAGzgB,OAC/BmI,OAAOC,OAAO3Z,EAAO8e,MAAO,CAAEoT,KAAIC,OAClC,MAED,IAAK,KACJ,MAAOC,EAAIC,GAAMV,EAAWK,EAAGzgB,OAC/BmI,OAAOC,OAAO3Z,EAAO8e,MAAO,CAAEsT,KAAIC,OAKrC,IAAK,MAAMpyB,KAAM,UAAI9C,SAAS8D,GAC7B,OAAQhB,EAAGmB,WACV,IAAK,SACJsY,OAAOC,OAAO3Z,EAAO8e,MAAO4S,EAAYzxB,IACxC,MAED,IAAK,OACJyZ,OAAOC,OAAO3Z,EAAO8e,MAkCjB,CAAC,GAjCL,MAED,IAAK,YACJ9e,EAAOuZ,QAAU,QACjBG,OAAOC,OAAO3Z,EAAO8e,MAAO,CAAEtT,MAAO,OAAQqK,OAAQ,SACrD7V,EAAO4mB,UAAY,CAClBtpB,GAAI,UAAIC,KAAK0C,EAAI,MACjBwQ,MAAO,UAAIlT,KAAK0C,EAAI,UAErB,MAED,IAAK,cACJD,EAAOI,SAASE,QAAQ8E,EAAO/E,kBAAkBJ,IACjD,MAED,QACC,MAAMiJ,EAAQC,EAAgBlJ,EAAImF,GAClC8D,GAASlJ,EAAOI,SAASE,KAAK4I,GAKjC,OAAOlJ,CACR,C,qGCnGA,eAEA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SAEMsyB,EAAe,CACpB,CAAE92B,KAAM,EAAAyB,kBAAkBs1B,eAAgB/0B,OAAQ,qBAClD,CAAEhC,KAAM,EAAAyB,kBAAkBu1B,mBAAoBh1B,OAAQ,oBACtD,CAAEhC,KAAM,EAAAyB,kBAAkBw1B,eAAgBj1B,OAAQ,qBAClD,CAAEhC,KAAM,EAAAyB,kBAAkBy1B,iBAAkBl1B,OAAQ,wBAGrD,MAAaga,EAAb,cAMC,KAAAmb,MAAgB,GAChB,KAAAC,SAAiC,CAAC,CA0JnC,CA7IC,iBAAa13B,CAAK41B,EAAkB1rB,EAAwB/K,GAC3D,IAAI8N,EAAI,IAAIqP,EAYZ,OAVArP,EAAE0qB,SAAWx4B,EACb8N,EAAE2qB,QAAU1tB,EACZ+C,EAAE3L,eAAiB,EAAAtC,eAAegB,KAAK41B,EAAMz2B,GAC7C8N,EAAE1L,WAAa0L,EAAE3L,SAASX,0BAEpBF,QAAQo3B,IAAIT,EAAal1B,KAAIooB,IAClC,MAAM3G,EAAI1W,EAAE1L,KAAKwI,MAAKpB,GAAKA,EAAErI,OAASgqB,EAAIhqB,QAASgqB,EACnD,OAAOrd,EAAE6qB,qBAAqBnU,EAAErhB,OAAQqhB,EAAErjB,KAAK,KAGzC2M,CACR,CAEA,IAAA5M,CAAKC,EAAO,QACX,OAAOd,KAAK8B,SAASjB,KAAKC,EAC3B,CAEQ,0BAAMw3B,CAAqBv4B,EAAce,GAChD,GAAId,KAAKk4B,SAASn4B,GACjB,OAAOC,KAAKk4B,SAASn4B,GAEtB,IAAKC,KAAK8B,SAAShC,IAAIC,GACtB,OAAO,KAER,IAAIimB,EAAa,KAEjB,OAAQllB,GACP,KAAK,EAAAyB,kBAAkBs1B,eACtB73B,KAAKiiB,aAAe+D,EAAO,IAAI,EAAA7M,aAAanZ,KAAK8B,SAAU/B,EAAMC,KAAKo4B,SACtE,MAED,KAAK,EAAA71B,kBAAkBg2B,UACtBv4B,KAAK6hB,cAAgBmE,EAAO,IAAI,EAAAxI,cAAcxd,KAAK8B,SAAU/B,GAC7D,MAED,KAAK,EAAAwC,kBAAkBi2B,UACtBx4B,KAAKihB,cAAgB+E,EAAO,IAAI,EAAAqM,cAAcryB,KAAK8B,SAAU/B,EAAMC,KAAKo4B,SACxE,MAED,KAAK,EAAA71B,kBAAkBk2B,OACtBz4B,KAAK8gB,WAAakF,EAAO,IAAI,EAAA8O,WAAW90B,KAAK8B,SAAU/B,EAAMC,KAAKo4B,SAClE,MAED,KAAK,EAAA71B,kBAAkBm2B,MACtB14B,KAAK4gB,UAAYoF,EAAO,IAAI,EAAA+O,UAAU/0B,KAAK8B,SAAU/B,GACrD,MAED,KAAK,EAAAwC,kBAAkBo2B,UACtB34B,KAAKqhB,cAAgB2E,EAAO,IAAI,EAAA4S,cAAc54B,KAAK8B,SAAU/B,EAAMC,KAAKo4B,SACxE,MAED,KAAK,EAAA71B,kBAAkBs2B,SACtB74B,KAAKwhB,aAAewE,EAAO,IAAI,EAAA8S,aAAa94B,KAAK8B,SAAU/B,EAAMC,KAAKo4B,SACtE,MAED,KAAK,EAAA71B,kBAAkBic,OACtBwH,EAAO,IAAI,EAAA+S,WAAW/4B,KAAK8B,SAAU/B,EAAMC,KAAKo4B,SAChD,MAED,KAAK,EAAA71B,kBAAkB+b,OACtB0H,EAAO,IAAI,EAAAgT,WAAWh5B,KAAK8B,SAAU/B,EAAMC,KAAKo4B,SAChD,MAED,KAAK,EAAA71B,kBAAkBw1B,eACtB/3B,KAAKi5B,cAAgBjT,EAAO,IAAI,EAAApQ,cAAc5V,KAAK8B,SAAU/B,GAC7D,MAED,KAAK,EAAAwC,kBAAkBu1B,mBACtB93B,KAAKk5B,kBAAoBlT,EAAO,IAAI,EAAAlP,kBAAkB9W,KAAK8B,SAAU/B,GACrE,MAED,KAAK,EAAAwC,kBAAkBy1B,iBACtBhS,EAAO,IAAI,EAAAxP,gBAAgBxW,KAAK8B,SAAU/B,GAC1C,MAED,KAAK,EAAAwC,kBAAkB42B,SACtBn5B,KAAKyhB,aAAeuE,EAAO,IAAI,EAAAsO,aAAat0B,KAAK8B,SAAU/B,GAI7D,GAAY,MAARimB,EACH,OAAO/kB,QAAQC,QAAQ,MAOxB,GALAlB,KAAKk4B,SAASn4B,GAAQimB,EACtBhmB,KAAKi4B,MAAMryB,KAAKogB,SAEVA,EAAKxlB,OAEPwlB,EAAKjkB,MAAM2R,OAAS,EAAG,CAC1B,MAAO0lB,IAAU,IAAA73B,WAAUykB,EAAKjmB,YAC1BkB,QAAQo3B,IAAIrS,EAAKjkB,KAAKW,KAAIooB,GAAO9qB,KAAKs4B,sBAAqB,IAAAe,aAAYvO,EAAIhoB,OAAQs2B,GAAStO,EAAIhqB,Q,CAGvG,OAAOklB,CACR,CAEA,uBAAMkF,CAAkBtoB,EAAYojB,GACnC,MAAM7c,QAAUnJ,KAAKs5B,aAAatT,GAAQhmB,KAAKiiB,aAAcrf,EAAI,QACjE,OAAO5C,KAAKu5B,UAAUpwB,EACvB,CAEA,wBAAMme,CAAmB1kB,GACxB,MAAMuG,QAAUnJ,KAAKs5B,aAAat5B,KAAKihB,cAAere,EAAI,QAC1D,OAAO5C,KAAKu5B,UAAUpwB,EACvB,CAEA,cAAM8Z,CAASrgB,EAAYub,GAC1B,MAAMhV,QAAUnJ,KAAKs5B,aAAat5B,KAAK6hB,cAAejf,EAAI,cAC1D,OAAOuG,EAAInJ,KAAKu5B,UAAU,IAAIC,KAAK,CAACC,EAAYtwB,EAAGgV,MAAUhV,CAC9D,CAEQ,SAAAowB,CAAUnD,GACjB,OAAKA,EAGDp2B,KAAKm4B,SAAS1b,cACV,IAAAid,cAAatD,GAGdL,IAAI4D,gBAAgBvD,GANnB,IAOT,CAEA,eAAAnQ,CAAgBrjB,EAAYg3B,EAAiB,MAC5C,IAAI9O,GAAO8O,EAAS73B,MAAQ/B,KAAK+B,MAAMwI,MAAK4Z,GAAKA,EAAEvhB,IAAMA,IACzD,MAAMw2B,EAASQ,GAAW,IAAAr4B,WAAUq4B,EAAS75B,MAAM,GAAK,GACxD,OAAO+qB,EAAM9qB,KAAKk4B,UAAS,IAAAmB,aAAYvO,EAAIhoB,OAAQs2B,IAAW,IAC/D,CAEA,WAAAS,CAAY7T,EAAYpjB,GACvB,MAAMkoB,EAAM9E,EAAKjkB,KAAKwI,MAAKpB,GAAKA,EAAEvG,IAAMA,KACjCw2B,IAAU,IAAA73B,WAAUykB,EAAKjmB,MAChC,OAAO+qB,GAAM,IAAAuO,aAAYvO,EAAIhoB,OAAQs2B,GAAU,IAChD,CAEQ,YAAAE,CAAatT,EAAYpjB,EAAYk3B,GAC5C,MAAM/5B,EAAOC,KAAK65B,YAAY7T,EAAMpjB,GACpC,OAAO7C,EAAOC,KAAK8B,SAAStB,KAAKT,EAAM+5B,GAAc74B,QAAQC,QAAQ,KACtE,EAGD,SAAgBu4B,EAAY9c,EAAkBod,GAC7C,MACMC,EAAUD,EAAQvM,QAAQ,SAAU,IACpCyM,EAAU,IAAI7I,MAFR,IAIZ,IAAK,IAAI5c,EAAI,EAAGA,EAJJ,GAIaA,IACxBylB,EALW,GAKGzlB,EAAI,GAAK+B,SAASyjB,EAAQ75B,OAAW,EAAJqU,EAAO,GAAI,IAE3D,IAAK,IAAIA,EAAI,EAAGA,EAAI,GAAIA,IACvBmI,EAAKnI,GAAKmI,EAAKnI,GAAKylB,EAAQzlB,EARjB,IAUZ,OAAOmI,CACR,CA/KA,iBAmKA,e,UC3LAud,EAAOC,QAAUC,C,GCDbC,EAA2B,CAAC,ECEhC,IAAIC,EDCJ,SAASC,EAAoBC,GAE5B,IAAIC,EAAeJ,EAAyBG,GAC5C,QAAqBtwB,IAAjBuwB,EACH,OAAOA,EAAaN,QAGrB,IAAID,EAASG,EAAyBG,GAAY,CAGjDL,QAAS,CAAC,GAOX,OAHAO,EAAoBF,GAAUN,EAAQA,EAAOC,QAASI,GAG/CL,EAAOC,OACf,CCnB0BI,CAAoB,K", "sources": ["webpack://docx-preview/./src/common/open-xml-package.ts", "webpack://docx-preview/./src/common/part.ts", "webpack://docx-preview/./src/common/relationship.ts", "webpack://docx-preview/./src/document-parser.ts", "webpack://docx-preview/./src/document-props/core-props-part.ts", "webpack://docx-preview/./src/document-props/core-props.ts", "webpack://docx-preview/./src/document-props/custom-props-part.ts", "webpack://docx-preview/./src/document-props/custom-props.ts", "webpack://docx-preview/./src/document-props/extended-props-part.ts", "webpack://docx-preview/./src/document-props/extended-props.ts", "webpack://docx-preview/./src/document/bookmarks.ts", "webpack://docx-preview/./src/document/border.ts", "webpack://docx-preview/./src/document/common.ts", "webpack://docx-preview/./src/document/document-part.ts", "webpack://docx-preview/./src/document/dom.ts", "webpack://docx-preview/./src/document/line-spacing.ts", "webpack://docx-preview/./src/document/paragraph.ts", "webpack://docx-preview/./src/document/run.ts", "webpack://docx-preview/./src/document/section.ts", "webpack://docx-preview/./src/docx-preview.ts", "webpack://docx-preview/./src/font-table/font-table.ts", "webpack://docx-preview/./src/font-table/fonts.ts", "webpack://docx-preview/./src/header-footer/elements.ts", "webpack://docx-preview/./src/header-footer/parts.ts", "webpack://docx-preview/./src/html-renderer.ts", "webpack://docx-preview/./src/javascript.ts", "webpack://docx-preview/./src/notes/elements.ts", "webpack://docx-preview/./src/notes/parts.ts", "webpack://docx-preview/./src/numbering/numbering-part.ts", "webpack://docx-preview/./src/numbering/numbering.ts", "webpack://docx-preview/./src/parser/xml-parser.ts", "webpack://docx-preview/./src/settings/settings-part.ts", "webpack://docx-preview/./src/settings/settings.ts", "webpack://docx-preview/./src/styles/styles-part.ts", "webpack://docx-preview/./src/theme/theme-part.ts", "webpack://docx-preview/./src/theme/theme.ts", "webpack://docx-preview/./src/utils.ts", "webpack://docx-preview/./src/vml/vml.ts", "webpack://docx-preview/./src/word-document.ts", "webpack://docx-preview/external module {\"root\":\"J<PERSON>Z<PERSON>\",\"commonjs\":\"jszip\",\"commonjs2\":\"jszip\",\"amd\":\"jszip\",\"module\":\"jszip\"}", "webpack://docx-preview/webpack/bootstrap", "webpack://docx-preview/webpack/startup"], "sourcesContent": ["import * as J<PERSON><PERSON><PERSON> from \"jszip\";\r\nimport { parseXmlString, XmlParser } from \"../parser/xml-parser\";\r\nimport { splitPath } from \"../utils\";\r\nimport { parseRelationships, Relationship } from \"./relationship\";\r\n\r\nexport interface OpenXmlPackageOptions {\r\n    trimXmlDeclaration: boolean,\r\n    keepOrigin: boolean,\r\n}\r\n\r\nexport class OpenXmlPackage {\r\n    xmlParser: XmlParser = new XmlParser();\r\n\r\n    constructor(private _zip: JSZip, public options: OpenXmlPackageOptions) {\r\n    }\r\n\r\n    get(path: string): any {\r\n        return this._zip.files[normalizePath(path)];\r\n    }\r\n\r\n    update(path: string, content: any) {\r\n        this._zip.file(path, content);\r\n    }\r\n\r\n    static async load(input: Blob | any, options: OpenXmlPackageOptions): Promise<OpenXmlPackage> {\r\n        const zip = await JSZip.loadAsync(input);\r\n\t\treturn new OpenXmlPackage(zip, options);\r\n    }\r\n\r\n    save(type: any = \"blob\"): Promise<any>  {\r\n        return this._zip.generateAsync({ type });\r\n    }\r\n\r\n    load(path: string, type: JSZip.OutputType = \"string\"): Promise<any> {\r\n        return this.get(path)?.async(type) ?? Promise.resolve(null);\r\n    }\r\n\r\n    async loadRelationships(path: string = null): Promise<Relationship[]> {\r\n        let relsPath = `_rels/.rels`;\r\n\r\n        if (path != null) {\r\n            const [f, fn] = splitPath(path);\r\n            relsPath = `${f}_rels/${fn}.rels`;\r\n        }\r\n\r\n        const txt = await this.load(relsPath);\r\n\t\treturn txt ? parseRelationships(this.parseXmlDocument(txt).firstElementChild, this.xmlParser) : null;\r\n    }\r\n\r\n    /** @internal */\r\n    parseXmlDocument(txt: string): Document {\r\n        return parseXmlString(txt, this.options.trimXmlDeclaration);\r\n    }\r\n}\r\n\r\nfunction normalizePath(path: string) {\r\n    return path.startsWith('/') ? path.substr(1) : path;\r\n}", "import { serializeXmlString } from \"../parser/xml-parser\";\r\nimport { OpenXmlPackage } from \"./open-xml-package\";\r\nimport { Relationship } from \"./relationship\";\r\n\r\nexport class Part {\r\n    protected _xmlDocument: Document;\r\n\r\n    rels: Relationship[];\r\n\r\n    constructor(protected _package: OpenXmlPackage, public path: string) {\r\n    }\r\n\r\n    async load(): Promise<any> {\r\n\t\tthis.rels = await this._package.loadRelationships(this.path);\r\n\r\n\t\tconst xmlText = await this._package.load(this.path);\r\n\t\tconst xmlDoc = this._package.parseXmlDocument(xmlText);\r\n\r\n\t\tif (this._package.options.keepOrigin) {\r\n\t\t\tthis._xmlDocument = xmlDoc;\r\n\t\t}\r\n\r\n\t\tthis.parseXml(xmlDoc.firstElementChild);\r\n    }\r\n\r\n    save() {\r\n        this._package.update(this.path, serializeXmlString(this._xmlDocument));\r\n    }\r\n\r\n    protected parseXml(root: Element) {\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface Relationship {\r\n    id: string,\r\n    type: RelationshipTypes | string,\r\n    target: string\r\n    targetMode: \"\" | \"External\" | string \r\n}\r\n\r\nexport enum RelationshipTypes {\r\n    OfficeDocument = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\",\r\n    FontTable = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable\",\r\n    Image = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image\",\r\n    Numbering = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering\",\r\n    Styles = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\",\r\n    StylesWithEffects = \"http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects\",\r\n    Theme = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme\",\r\n    Settings = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings\",\r\n    WebSettings = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings\",\r\n    Hyperlink = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink\",\r\n    Footnotes = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes\",\r\n\tEndnotes = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes\",\r\n    Footer = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer\",\r\n    Header = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/header\",\r\n    ExtendedProperties = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties\",\r\n    CoreProperties = \"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties\",\r\n\tCustomProperties = \"http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties\",\r\n}\r\n\r\nexport function parseRelationships(root: Element, xml: XmlParser): Relationship[] {\r\n    return xml.elements(root).map(e => <Relationship>{\r\n        id: xml.attr(e, \"Id\"),\r\n        type: xml.attr(e, \"Type\"),\r\n        target: xml.attr(e, \"Target\"),\r\n        targetMode: xml.attr(e, \"TargetMode\")\r\n    });\r\n}", "import {\r\n\tDomType, WmlTable, IDomNumbering,\r\n\tWmlHyperlink, IDomImage, OpenXmlElement, WmlTableColumn, WmlTableCell,\r\n\tWmlTableRow, NumberingPicBullet, WmlText, WmlSymbol, WmlBreak, WmlNoteReference\r\n} from './document/dom';\r\nimport { DocumentElement } from './document/document';\r\nimport { WmlParagraph, parseParagraphProperties, parseParagraphProperty } from './document/paragraph';\r\nimport { parseSectionProperties, SectionProperties } from './document/section';\r\nimport xml from './parser/xml-parser';\r\nimport { parseRunProperties, WmlRun } from './document/run';\r\nimport { parseBookmarkEnd, parseBookmarkStart } from './document/bookmarks';\r\nimport { IDomStyle, IDomSubStyle } from './document/style';\r\nimport { WmlFieldChar, WmlFieldSimple, WmlInstructionText } from './document/fields';\r\nimport { convertLength, LengthUsage, LengthUsageType } from './document/common';\r\nimport { parseVmlElement } from './vml/vml';\r\n\r\nexport var autos = {\r\n\tshd: \"inherit\",\r\n\tcolor: \"black\",\r\n\tborderColor: \"black\",\r\n\thighlight: \"transparent\"\r\n};\r\n\r\nconst supportedNamespaceURIs = [];\r\n\r\nconst mmlTagMap = {\r\n\t\"oMath\": DomType.MmlMath,\r\n\t\"oMathPara\": DomType.MmlMathParagraph,\r\n\t\"f\": DomType.MmlFraction,\r\n\t\"func\": DomType.MmlFunction,\r\n\t\"fName\": DomType.MmlFunctionName,\r\n\t\"num\": DomType.MmlNumerator,\r\n\t\"den\": DomType.MmlDenominator,\r\n\t\"rad\": DomType.MmlRadical,\r\n\t\"deg\": DomType.MmlDegree,\r\n\t\"e\": DomType.MmlBase,\r\n\t\"sSup\": DomType.MmlSuperscript,\r\n\t\"sSub\": DomType.MmlSubscript,\r\n\t\"sPre\": DomType.MmlPreSubSuper,\r\n\t\"sup\": DomType.MmlSuperArgument,\r\n\t\"sub\": DomType.MmlSubArgument,\r\n\t\"d\": DomType.MmlDelimiter,\r\n\t\"nary\": DomType.MmlNary,\r\n\t\"eqArr\": DomType.MmlEquationArray,\r\n\t\"lim\": DomType.MmlLimit,\r\n\t\"limLow\": DomType.MmlLimitLower,\r\n\t\"m\": DomType.MmlMatrix,\r\n\t\"mr\": DomType.MmlMatrixRow,\r\n\t\"box\": DomType.MmlBox,\r\n\t\"bar\": DomType.MmlBar,\r\n\t\"groupChr\": DomType.MmlGroupChar\r\n}\r\n\r\nexport interface DocumentParserOptions {\r\n\tignoreWidth: boolean;\r\n\tdebug: boolean;\r\n}\r\n\r\nexport class DocumentParser {\r\n\toptions: DocumentParserOptions;\r\n\r\n\tconstructor(options?: Partial<DocumentParserOptions>) {\r\n\t\tthis.options = {\r\n\t\t\tignoreWidth: false,\r\n\t\t\tdebug: false,\r\n\t\t\t...options\r\n\t\t};\r\n\t}\r\n\r\n\tparseNotes(xmlDoc: Element, elemName: string, elemClass: any): any[] {\r\n\t\tvar result = [];\r\n\r\n\t\tfor (let el of xml.elements(xmlDoc, elemName)) {\r\n\t\t\tconst node = new elemClass();\r\n\t\t\tnode.id = xml.attr(el, \"id\");\r\n\t\t\tnode.noteType = xml.attr(el, \"type\");\r\n\t\t\tnode.children = this.parseBodyElements(el);\r\n\t\t\tresult.push(node);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseDocumentFile(xmlDoc: Element): DocumentElement {\r\n\t\tvar xbody = xml.element(xmlDoc, \"body\");\r\n\t\tvar background = xml.element(xmlDoc, \"background\");\r\n\t\tvar sectPr = xml.element(xbody, \"sectPr\");\r\n\r\n\t\treturn {\r\n\t\t\ttype: DomType.Document,\r\n\t\t\tchildren: this.parseBodyElements(xbody),\r\n\t\t\tprops: sectPr ? parseSectionProperties(sectPr, xml) : {} as SectionProperties,\r\n\t\t\tcssStyle: background ? this.parseBackground(background) : {},\r\n\t\t};\r\n\t}\r\n\r\n\tparseBackground(elem: Element): any {\r\n\t\tvar result = {};\r\n\t\tvar color = xmlUtil.colorAttr(elem, \"color\");\r\n\r\n\t\tif (color) {\r\n\t\t\tresult[\"background-color\"] = color;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseBodyElements(element: Element): OpenXmlElement[] {\r\n\t\tvar children = [];\r\n\r\n\t\tfor (let elem of xml.elements(element)) {\r\n\t\t\tswitch (elem.localName) {\r\n\t\t\t\tcase \"p\":\r\n\t\t\t\t\tchildren.push(this.parseParagraph(elem));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tbl\":\r\n\t\t\t\t\tchildren.push(this.parseTable(elem));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sdt\":\r\n\t\t\t\t\tchildren.push(...this.parseSdt(elem, e => this.parseBodyElements(e)));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn children;\r\n\t}\r\n\r\n\tparseStylesFile(xstyles: Element): IDomStyle[] {\r\n\t\tvar result = [];\r\n\r\n\t\txmlUtil.foreach(xstyles, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"style\":\r\n\t\t\t\t\tresult.push(this.parseStyle(n));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"docDefaults\":\r\n\t\t\t\t\tresult.push(this.parseDefaultStyles(n));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseDefaultStyles(node: Element): IDomStyle {\r\n\t\tvar result = <IDomStyle>{\r\n\t\t\tid: null,\r\n\t\t\tname: null,\r\n\t\t\ttarget: null,\r\n\t\t\tbasedOn: null,\r\n\t\t\tstyles: []\r\n\t\t};\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"rPrDefault\":\r\n\t\t\t\t\tvar rPr = xml.element(c, \"rPr\");\r\n\r\n\t\t\t\t\tif (rPr)\r\n\t\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\t\ttarget: \"span\",\r\n\t\t\t\t\t\t\tvalues: this.parseDefaultProperties(rPr, {})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pPrDefault\":\r\n\t\t\t\t\tvar pPr = xml.element(c, \"pPr\");\r\n\r\n\t\t\t\t\tif (pPr)\r\n\t\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\t\ttarget: \"p\",\r\n\t\t\t\t\t\t\tvalues: this.parseDefaultProperties(pPr, {})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseStyle(node: Element): IDomStyle {\r\n\t\tvar result = <IDomStyle>{\r\n\t\t\tid: xml.attr(node, \"styleId\"),\r\n\t\t\tisDefault: xml.boolAttr(node, \"default\"),\r\n\t\t\tname: null,\r\n\t\t\ttarget: null,\r\n\t\t\tbasedOn: null,\r\n\t\t\tstyles: [],\r\n\t\t\tlinked: null\r\n\t\t};\r\n\r\n\t\tswitch (xml.attr(node, \"type\")) {\r\n\t\t\tcase \"paragraph\": result.target = \"p\"; break;\r\n\t\t\tcase \"table\": result.target = \"table\"; break;\r\n\t\t\tcase \"character\": result.target = \"span\"; break;\r\n\t\t\t//case \"numbering\": result.target = \"p\"; break;\r\n\t\t}\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"basedOn\":\r\n\t\t\t\t\tresult.basedOn = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"name\":\r\n\t\t\t\t\tresult.name = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"link\":\r\n\t\t\t\t\tresult.linked = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"next\":\r\n\t\t\t\t\tresult.next = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"aliases\":\r\n\t\t\t\t\tresult.aliases = xml.attr(n, \"val\").split(\",\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\ttarget: \"p\",\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tresult.paragraphProps = parseParagraphProperties(n, xml);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\ttarget: \"span\",\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tresult.runProps = parseRunProperties(n, xml);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblPr\":\r\n\t\t\t\tcase \"tcPr\":\r\n\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\ttarget: \"td\", //TODO: maybe move to processor\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblStylePr\":\r\n\t\t\t\t\tfor (let s of this.parseTableStyle(n))\r\n\t\t\t\t\t\tresult.styles.push(s);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rsid\":\r\n\t\t\t\tcase \"qFormat\":\r\n\t\t\t\tcase \"hidden\":\r\n\t\t\t\tcase \"semiHidden\":\r\n\t\t\t\tcase \"unhideWhenUsed\":\r\n\t\t\t\tcase \"autoRedefine\":\r\n\t\t\t\tcase \"uiPriority\":\r\n\t\t\t\t\t//TODO: ignore\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tthis.options.debug && console.warn(`DOCX: Unknown style element: ${n.localName}`);\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableStyle(node: Element): IDomSubStyle[] {\r\n\t\tvar result = [];\r\n\r\n\t\tvar type = xml.attr(node, \"type\");\r\n\t\tvar selector = \"\";\r\n\t\tvar modificator = \"\";\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"firstRow\":\r\n\t\t\t\tmodificator = \".first-row\";\r\n\t\t\t\tselector = \"tr.first-row td\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"lastRow\":\r\n\t\t\t\tmodificator = \".last-row\";\r\n\t\t\t\tselector = \"tr.last-row td\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"firstCol\":\r\n\t\t\t\tmodificator = \".first-col\";\r\n\t\t\t\tselector = \"td.first-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"lastCol\":\r\n\t\t\t\tmodificator = \".last-col\";\r\n\t\t\t\tselector = \"td.last-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band1Vert\":\r\n\t\t\t\tmodificator = \":not(.no-vband)\";\r\n\t\t\t\tselector = \"td.odd-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band2Vert\":\r\n\t\t\t\tmodificator = \":not(.no-vband)\";\r\n\t\t\t\tselector = \"td.even-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band1Horz\":\r\n\t\t\t\tmodificator = \":not(.no-hband)\";\r\n\t\t\t\tselector = \"tr.odd-row\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band2Horz\":\r\n\t\t\t\tmodificator = \":not(.no-hband)\";\r\n\t\t\t\tselector = \"tr.even-row\";\r\n\t\t\t\tbreak;\r\n\t\t\tdefault: return [];\r\n\t\t}\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\ttarget: `${selector} p`,\r\n\t\t\t\t\t\tmod: modificator,\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\ttarget: `${selector} span`,\r\n\t\t\t\t\t\tmod: modificator,\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblPr\":\r\n\t\t\t\tcase \"tcPr\":\r\n\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\ttarget: selector, //TODO: maybe move to processor\r\n\t\t\t\t\t\tmod: modificator,\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseNumberingFile(xnums: Element): IDomNumbering[] {\r\n\t\tvar result = [];\r\n\t\tvar mapping = {};\r\n\t\tvar bullets = [];\r\n\r\n\t\txmlUtil.foreach(xnums, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"abstractNum\":\r\n\t\t\t\t\tthis.parseAbstractNumbering(n, bullets)\r\n\t\t\t\t\t\t.forEach(x => result.push(x));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"numPicBullet\":\r\n\t\t\t\t\tbullets.push(this.parseNumberingPicBullet(n));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"num\":\r\n\t\t\t\t\tvar numId = xml.attr(n, \"numId\");\r\n\t\t\t\t\tvar abstractNumId = xml.elementAttr(n, \"abstractNumId\", \"val\");\r\n\t\t\t\t\tmapping[abstractNumId] = numId;\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tresult.forEach(x => x.id = mapping[x.id]);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseNumberingPicBullet(elem: Element): NumberingPicBullet {\r\n\t\tvar pict = xml.element(elem, \"pict\");\r\n\t\tvar shape = pict && xml.element(pict, \"shape\");\r\n\t\tvar imagedata = shape && xml.element(shape, \"imagedata\");\r\n\r\n\t\treturn imagedata ? {\r\n\t\t\tid: xml.intAttr(elem, \"numPicBulletId\"),\r\n\t\t\tsrc: xml.attr(imagedata, \"id\"),\r\n\t\t\tstyle: xml.attr(shape, \"style\")\r\n\t\t} : null;\r\n\t}\r\n\r\n\tparseAbstractNumbering(node: Element, bullets: any[]): IDomNumbering[] {\r\n\t\tvar result = [];\r\n\t\tvar id = xml.attr(node, \"abstractNumId\");\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"lvl\":\r\n\t\t\t\t\tresult.push(this.parseNumberingLevel(id, n, bullets));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseNumberingLevel(id: string, node: Element, bullets: any[]): IDomNumbering {\r\n\t\tvar result: IDomNumbering = {\r\n\t\t\tid: id,\r\n\t\t\tlevel: xml.intAttr(node, \"ilvl\"),\r\n\t\t\tstart: 1,\r\n\t\t\tpStyleName: undefined,\r\n\t\t\tpStyle: {},\r\n\t\t\trStyle: {},\r\n\t\t\tsuff: \"tab\"\r\n\t\t};\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"start\":\r\n\t\t\t\t\tresult.start = xml.intAttr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tthis.parseDefaultProperties(n, result.pStyle);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tthis.parseDefaultProperties(n, result.rStyle);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lvlPicBulletId\":\r\n\t\t\t\t\tvar id = xml.intAttr(n, \"val\");\r\n\t\t\t\t\tresult.bullet = bullets.find(x => x.id == id);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lvlText\":\r\n\t\t\t\t\tresult.levelText = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pStyle\":\r\n\t\t\t\t\tresult.pStyleName = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"numFmt\":\r\n\t\t\t\t\tresult.format = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"suff\":\r\n\t\t\t\t\tresult.suff = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseSdt(node: Element, parser: Function): OpenXmlElement[] {\r\n\t\tconst sdtContent = xml.element(node, \"sdtContent\");\r\n\t\treturn sdtContent ? parser(sdtContent) : [];\r\n\t}\r\n\r\n\tparseInserted(node: Element, parentParser: Function): OpenXmlElement {\r\n\t\treturn <OpenXmlElement>{ \r\n\t\t\ttype: DomType.Inserted, \r\n\t\t\tchildren: parentParser(node)?.children ?? []\r\n\t\t};\r\n\t}\r\n\r\n\tparseDeleted(node: Element, parentParser: Function): OpenXmlElement {\r\n\t\treturn <OpenXmlElement>{ \r\n\t\t\ttype: DomType.Deleted, \r\n\t\t\tchildren: parentParser(node)?.children ?? []\r\n\t\t};\r\n\t}\r\n\r\n\tparseParagraph(node: Element): OpenXmlElement {\r\n\t\tvar result = <WmlParagraph>{ type: DomType.Paragraph, children: [] };\r\n\r\n\t\tfor (let el of xml.elements(node)) {\r\n\t\t\tswitch (el.localName) {\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tthis.parseParagraphProperties(el, result);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"r\":\r\n\t\t\t\t\tresult.children.push(this.parseRun(el, result));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"hyperlink\":\r\n\t\t\t\t\tresult.children.push(this.parseHyperlink(el, result));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bookmarkStart\":\r\n\t\t\t\t\tresult.children.push(parseBookmarkStart(el, xml));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bookmarkEnd\":\r\n\t\t\t\t\tresult.children.push(parseBookmarkEnd(el, xml));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"oMath\":\r\n\t\t\t\tcase \"oMathPara\":\r\n\t\t\t\t\tresult.children.push(this.parseMathElement(el));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sdt\":\r\n\t\t\t\t\tresult.children.push(...this.parseSdt(el, e => this.parseParagraph(e).children));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"ins\":\r\n\t\t\t\t\tresult.children.push(this.parseInserted(el, e => this.parseParagraph(e)));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"del\":\r\n\t\t\t\t\tresult.children.push(this.parseDeleted(el, e => this.parseParagraph(e)));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseParagraphProperties(elem: Element, paragraph: WmlParagraph) {\r\n\t\tthis.parseDefaultProperties(elem, paragraph.cssStyle = {}, null, c => {\r\n\t\t\tif (parseParagraphProperty(c, paragraph, xml))\r\n\t\t\t\treturn true;\r\n\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"pStyle\":\r\n\t\t\t\t\tparagraph.styleName = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"cnfStyle\":\r\n\t\t\t\t\tparagraph.className = values.classNameOfCnfStyle(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"framePr\":\r\n\t\t\t\t\tthis.parseFrame(c, paragraph);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\t//TODO ignore\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseFrame(node: Element, paragraph: WmlParagraph) {\r\n\t\tvar dropCap = xml.attr(node, \"dropCap\");\r\n\r\n\t\tif (dropCap == \"drop\")\r\n\t\t\tparagraph.cssStyle[\"float\"] = \"left\";\r\n\t}\r\n\r\n\tparseHyperlink(node: Element, parent?: OpenXmlElement): WmlHyperlink {\r\n\t\tvar result: WmlHyperlink = <WmlHyperlink>{ type: DomType.Hyperlink, parent: parent, children: [] };\r\n\t\tvar anchor = xml.attr(node, \"anchor\");\r\n\t\tvar relId = xml.attr(node, \"id\");\r\n\r\n\t\tif (anchor)\r\n\t\t\tresult.href = \"#\" + anchor;\r\n\r\n\t\tif (relId)\r\n\t\t\tresult.id = relId;\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"r\":\r\n\t\t\t\t\tresult.children.push(this.parseRun(c, result));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseRun(node: Element, parent?: OpenXmlElement): WmlRun {\r\n\t\tvar result: WmlRun = <WmlRun>{ type: DomType.Run, parent: parent, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tc = this.checkAlternateContent(c);\r\n\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"t\":\r\n\t\t\t\t\tresult.children.push(<WmlText>{\r\n\t\t\t\t\t\ttype: DomType.Text,\r\n\t\t\t\t\t\ttext: c.textContent\r\n\t\t\t\t\t});//.replace(\" \", \"\\u00A0\"); // TODO\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"delText\":\r\n\t\t\t\t\tresult.children.push(<WmlText>{\r\n\t\t\t\t\t\ttype: DomType.DeletedText,\r\n\t\t\t\t\t\ttext: c.textContent\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"fldSimple\":\r\n\t\t\t\t\tresult.children.push(<WmlFieldSimple>{\r\n\t\t\t\t\t\ttype: DomType.SimpleField,\r\n\t\t\t\t\t\tinstruction: xml.attr(c, \"instr\"),\r\n\t\t\t\t\t\tlock: xml.boolAttr(c, \"lock\", false),\r\n\t\t\t\t\t\tdirty: xml.boolAttr(c, \"dirty\", false)\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"instrText\":\r\n\t\t\t\t\tresult.fieldRun = true;\r\n\t\t\t\t\tresult.children.push(<WmlInstructionText>{\r\n\t\t\t\t\t\ttype: DomType.Instruction,\r\n\t\t\t\t\t\ttext: c.textContent\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"fldChar\":\r\n\t\t\t\t\tresult.fieldRun = true;\r\n\t\t\t\t\tresult.children.push(<WmlFieldChar>{\r\n\t\t\t\t\t\ttype: DomType.ComplexField,\r\n\t\t\t\t\t\tcharType: xml.attr(c, \"fldCharType\"),\r\n\t\t\t\t\t\tlock: xml.boolAttr(c, \"lock\", false),\r\n\t\t\t\t\t\tdirty: xml.boolAttr(c, \"dirty\", false)\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"noBreakHyphen\":\r\n\t\t\t\t\tresult.children.push({ type: DomType.NoBreakHyphen });\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"br\":\r\n\t\t\t\t\tresult.children.push(<WmlBreak>{\r\n\t\t\t\t\t\ttype: DomType.Break,\r\n\t\t\t\t\t\tbreak: xml.attr(c, \"type\") || \"textWrapping\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lastRenderedPageBreak\":\r\n\t\t\t\t\tresult.children.push(<WmlBreak>{\r\n\t\t\t\t\t\ttype: DomType.Break,\r\n\t\t\t\t\t\tbreak: \"lastRenderedPageBreak\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sym\":\r\n\t\t\t\t\tresult.children.push(<WmlSymbol>{\r\n\t\t\t\t\t\ttype: DomType.Symbol,\r\n\t\t\t\t\t\tfont: xml.attr(c, \"font\"),\r\n\t\t\t\t\t\tchar: xml.attr(c, \"char\")\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tab\":\r\n\t\t\t\t\tresult.children.push({ type: DomType.Tab });\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"footnoteReference\":\r\n\t\t\t\t\tresult.children.push(<WmlNoteReference>{\r\n\t\t\t\t\t\ttype: DomType.FootnoteReference,\r\n\t\t\t\t\t\tid: xml.attr(c, \"id\")\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"endnoteReference\":\r\n\t\t\t\t\tresult.children.push(<WmlNoteReference>{\r\n\t\t\t\t\t\ttype: DomType.EndnoteReference,\r\n\t\t\t\t\t\tid: xml.attr(c, \"id\")\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"drawing\":\r\n\t\t\t\t\tlet d = this.parseDrawing(c);\r\n\r\n\t\t\t\t\tif (d)\r\n\t\t\t\t\t\tresult.children = [d];\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pict\":\r\n\t\t\t\t\tresult.children.push(this.parseVmlPicture(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tthis.parseRunProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseMathElement(elem: Element): OpenXmlElement {\r\n\t\tconst propsTag = `${elem.localName}Pr`;\r\n\t\tconst result = { type: mmlTagMap[elem.localName], children: [] } as OpenXmlElement;\r\n\r\n\t\tfor (const el of xml.elements(elem)) {\r\n\t\t\tconst childType = mmlTagMap[el.localName];\r\n\r\n\t\t\tif (childType) {\r\n\t\t\t\tresult.children.push(this.parseMathElement(el));\r\n\t\t\t} else if (el.localName == \"r\") {\r\n\t\t\t\tvar run = this.parseRun(el);\r\n\t\t\t\trun.type = DomType.MmlRun;\r\n\t\t\t\tresult.children.push(run);\r\n\t\t\t} else if (el.localName == propsTag) {\r\n\t\t\t\tresult.props = this.parseMathProperies(el);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseMathProperies(elem: Element): Record<string, any> {\r\n\t\tconst result: Record<string, any> = {};\r\n\r\n\t\tfor (const el of xml.elements(elem)) {\r\n\t\t\tswitch (el.localName) {\r\n\t\t\t\tcase \"chr\": result.char = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"vertJc\": result.verticalJustification = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"pos\": result.position = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"degHide\": result.hideDegree = xml.boolAttr(el, \"val\"); break;\r\n\t\t\t\tcase \"begChr\": result.beginChar = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"endChr\": result.endChar = xml.attr(el, \"val\"); break;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseRunProperties(elem: Element, run: WmlRun) {\r\n\t\tthis.parseDefaultProperties(elem, run.cssStyle = {}, null, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"rStyle\":\r\n\t\t\t\t\trun.styleName = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vertAlign\":\r\n\t\t\t\t\trun.verticalAlign = values.valueOfVertAlign(c, true);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseVmlPicture(elem: Element): OpenXmlElement {\r\n\t\tconst result = { type: DomType.VmlPicture, children: [] };\r\n\r\n\t\tfor (const el of xml.elements(elem)) {\r\n\t\t\tconst child = parseVmlElement(el, this);\r\n\t\t\tchild && result.children.push(child);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tcheckAlternateContent(elem: Element): Element {\r\n\t\tif (elem.localName != 'AlternateContent')\r\n\t\t\treturn elem;\r\n\r\n\t\tvar choice = xml.element(elem, \"Choice\");\r\n\r\n\t\tif (choice) {\r\n\t\t\tvar requires = xml.attr(choice, \"Requires\");\r\n\t\t\tvar namespaceURI = elem.lookupNamespaceURI(requires);\r\n\r\n\t\t\tif (supportedNamespaceURIs.includes(namespaceURI))\r\n\t\t\t\treturn choice.firstElementChild;\r\n\t\t}\r\n\r\n\t\treturn xml.element(elem, \"Fallback\")?.firstElementChild;\r\n\t}\r\n\r\n\tparseDrawing(node: Element): OpenXmlElement {\r\n\t\tfor (var n of xml.elements(node)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"inline\":\r\n\t\t\t\tcase \"anchor\":\r\n\t\t\t\t\treturn this.parseDrawingWrapper(n);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tparseDrawingWrapper(node: Element): OpenXmlElement {\r\n\t\tvar result = <OpenXmlElement>{ type: DomType.Drawing, children: [], cssStyle: {} };\r\n\t\tvar isAnchor = node.localName == \"anchor\";\r\n\r\n\t\t//TODO\r\n\t\t// result.style[\"margin-left\"] = xml.sizeAttr(node, \"distL\", SizeType.Emu);\r\n\t\t// result.style[\"margin-top\"] = xml.sizeAttr(node, \"distT\", SizeType.Emu);\r\n\t\t// result.style[\"margin-right\"] = xml.sizeAttr(node, \"distR\", SizeType.Emu);\r\n\t\t// result.style[\"margin-bottom\"] = xml.sizeAttr(node, \"distB\", SizeType.Emu);\r\n\r\n\t\tlet wrapType: \"wrapTopAndBottom\" | \"wrapNone\" | null = null;\r\n\t\tlet simplePos = xml.boolAttr(node, \"simplePos\");\r\n\r\n\t\tlet posX = { relative: \"page\", align: \"left\", offset: \"0\" };\r\n\t\tlet posY = { relative: \"page\", align: \"top\", offset: \"0\" };\r\n\r\n\t\tfor (var n of xml.elements(node)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"simplePos\":\r\n\t\t\t\t\tif (simplePos) {\r\n\t\t\t\t\t\tposX.offset = xml.lengthAttr(n, \"x\", LengthUsage.Emu);\r\n\t\t\t\t\t\tposY.offset = xml.lengthAttr(n, \"y\", LengthUsage.Emu);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"extent\":\r\n\t\t\t\t\tresult.cssStyle[\"width\"] = xml.lengthAttr(n, \"cx\", LengthUsage.Emu);\r\n\t\t\t\t\tresult.cssStyle[\"height\"] = xml.lengthAttr(n, \"cy\", LengthUsage.Emu);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"positionH\":\r\n\t\t\t\tcase \"positionV\":\r\n\t\t\t\t\tif (!simplePos) {\r\n\t\t\t\t\t\tlet pos = n.localName == \"positionH\" ? posX : posY;\r\n\t\t\t\t\t\tvar alignNode = xml.element(n, \"align\");\r\n\t\t\t\t\t\tvar offsetNode = xml.element(n, \"posOffset\");\r\n\r\n\t\t\t\t\t\tpos.relative = xml.attr(n, \"relativeFrom\") ?? pos.relative;\r\n\r\n\t\t\t\t\t\tif (alignNode)\r\n\t\t\t\t\t\t\tpos.align = alignNode.textContent;\r\n\r\n\t\t\t\t\t\tif (offsetNode)\r\n\t\t\t\t\t\t\tpos.offset = xmlUtil.sizeValue(offsetNode, LengthUsage.Emu);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"wrapTopAndBottom\":\r\n\t\t\t\t\twrapType = \"wrapTopAndBottom\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"wrapNone\":\r\n\t\t\t\t\twrapType = \"wrapNone\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"graphic\":\r\n\t\t\t\t\tvar g = this.parseGraphic(n);\r\n\r\n\t\t\t\t\tif (g)\r\n\t\t\t\t\t\tresult.children.push(g);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (wrapType == \"wrapTopAndBottom\") {\r\n\t\t\tresult.cssStyle['display'] = 'block';\r\n\r\n\t\t\tif (posX.align) {\r\n\t\t\t\tresult.cssStyle['text-align'] = posX.align;\r\n\t\t\t\tresult.cssStyle['width'] = \"100%\";\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (wrapType == \"wrapNone\") {\r\n\t\t\tresult.cssStyle['display'] = 'block';\r\n\t\t\tresult.cssStyle['position'] = 'relative';\r\n\t\t\tresult.cssStyle[\"width\"] = \"0px\";\r\n\t\t\tresult.cssStyle[\"height\"] = \"0px\";\r\n\r\n\t\t\tif (posX.offset)\r\n\t\t\t\tresult.cssStyle[\"left\"] = posX.offset;\r\n\t\t\tif (posY.offset)\r\n\t\t\t\tresult.cssStyle[\"top\"] = posY.offset;\r\n\t\t}\r\n\t\telse if (isAnchor && (posX.align == 'left' || posX.align == 'right')) {\r\n\t\t\tresult.cssStyle[\"float\"] = posX.align;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseGraphic(elem: Element): OpenXmlElement {\r\n\t\tvar graphicData = xml.element(elem, \"graphicData\");\r\n\r\n\t\tfor (let n of xml.elements(graphicData)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"pic\":\r\n\t\t\t\t\treturn this.parsePicture(n);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\tparsePicture(elem: Element): IDomImage {\r\n\t\tvar result = <IDomImage>{ type: DomType.Image, src: \"\", cssStyle: {} };\r\n\t\tvar blipFill = xml.element(elem, \"blipFill\");\r\n\t\tvar blip = xml.element(blipFill, \"blip\");\r\n\r\n\t\tresult.src = xml.attr(blip, \"embed\");\r\n\r\n\t\tvar spPr = xml.element(elem, \"spPr\");\r\n\t\tvar xfrm = xml.element(spPr, \"xfrm\");\r\n\r\n\t\tresult.cssStyle[\"position\"] = \"relative\";\r\n\r\n\t\tfor (var n of xml.elements(xfrm)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"ext\":\r\n\t\t\t\t\tresult.cssStyle[\"width\"] = xml.lengthAttr(n, \"cx\", LengthUsage.Emu);\r\n\t\t\t\t\tresult.cssStyle[\"height\"] = xml.lengthAttr(n, \"cy\", LengthUsage.Emu);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"off\":\r\n\t\t\t\t\tresult.cssStyle[\"left\"] = xml.lengthAttr(n, \"x\", LengthUsage.Emu);\r\n\t\t\t\t\tresult.cssStyle[\"top\"] = xml.lengthAttr(n, \"y\", LengthUsage.Emu);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTable(node: Element): WmlTable {\r\n\t\tvar result: WmlTable = { type: DomType.Table, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tr\":\r\n\t\t\t\t\tresult.children.push(this.parseTableRow(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblGrid\":\r\n\t\t\t\t\tresult.columns = this.parseTableColumns(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblPr\":\r\n\t\t\t\t\tthis.parseTableProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableColumns(node: Element): WmlTableColumn[] {\r\n\t\tvar result = [];\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"gridCol\":\r\n\t\t\t\t\tresult.push({ width: xml.lengthAttr(n, \"w\") });\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableProperties(elem: Element, table: WmlTable) {\r\n\t\ttable.cssStyle = {};\r\n\t\ttable.cellStyle = {};\r\n\r\n\t\tthis.parseDefaultProperties(elem, table.cssStyle, table.cellStyle, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tblStyle\":\r\n\t\t\t\t\ttable.styleName = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblLook\":\r\n\t\t\t\t\ttable.className = values.classNameOftblLook(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblpPr\":\r\n\t\t\t\t\tthis.parseTablePosition(c, table);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblStyleColBandSize\":\r\n\t\t\t\t\ttable.colBandSize = xml.intAttr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblStyleRowBandSize\":\r\n\t\t\t\t\ttable.rowBandSize = xml.intAttr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\r\n\t\tswitch (table.cssStyle[\"text-align\"]) {\r\n\t\t\tcase \"center\":\r\n\t\t\t\tdelete table.cssStyle[\"text-align\"];\r\n\t\t\t\ttable.cssStyle[\"margin-left\"] = \"auto\";\r\n\t\t\t\ttable.cssStyle[\"margin-right\"] = \"auto\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"right\":\r\n\t\t\t\tdelete table.cssStyle[\"text-align\"];\r\n\t\t\t\ttable.cssStyle[\"margin-left\"] = \"auto\";\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\tparseTablePosition(node: Element, table: WmlTable) {\r\n\t\tvar topFromText = xml.lengthAttr(node, \"topFromText\");\r\n\t\tvar bottomFromText = xml.lengthAttr(node, \"bottomFromText\");\r\n\t\tvar rightFromText = xml.lengthAttr(node, \"rightFromText\");\r\n\t\tvar leftFromText = xml.lengthAttr(node, \"leftFromText\");\r\n\r\n\t\ttable.cssStyle[\"float\"] = 'left';\r\n\t\ttable.cssStyle[\"margin-bottom\"] = values.addSize(table.cssStyle[\"margin-bottom\"], bottomFromText);\r\n\t\ttable.cssStyle[\"margin-left\"] = values.addSize(table.cssStyle[\"margin-left\"], leftFromText);\r\n\t\ttable.cssStyle[\"margin-right\"] = values.addSize(table.cssStyle[\"margin-right\"], rightFromText);\r\n\t\ttable.cssStyle[\"margin-top\"] = values.addSize(table.cssStyle[\"margin-top\"], topFromText);\r\n\t}\r\n\r\n\tparseTableRow(node: Element): WmlTableRow {\r\n\t\tvar result: WmlTableRow = { type: DomType.Row, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tc\":\r\n\t\t\t\t\tresult.children.push(this.parseTableCell(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"trPr\":\r\n\t\t\t\t\tthis.parseTableRowProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableRowProperties(elem: Element, row: WmlTableRow) {\r\n\t\trow.cssStyle = this.parseDefaultProperties(elem, {}, null, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"cnfStyle\":\r\n\t\t\t\t\trow.className = values.classNameOfCnfStyle(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblHeader\":\r\n\t\t\t\t\trow.isHeader = xml.boolAttr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseTableCell(node: Element): OpenXmlElement {\r\n\t\tvar result: WmlTableCell = { type: DomType.Cell, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tbl\":\r\n\t\t\t\t\tresult.children.push(this.parseTable(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"p\":\r\n\t\t\t\t\tresult.children.push(this.parseParagraph(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tcPr\":\r\n\t\t\t\t\tthis.parseTableCellProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableCellProperties(elem: Element, cell: WmlTableCell) {\r\n\t\tcell.cssStyle = this.parseDefaultProperties(elem, {}, null, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"gridSpan\":\r\n\t\t\t\t\tcell.span = xml.intAttr(c, \"val\", null);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vMerge\":\r\n\t\t\t\t\tcell.verticalMerge = xml.attr(c, \"val\") ?? \"continue\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"cnfStyle\":\r\n\t\t\t\t\tcell.className = values.classNameOfCnfStyle(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseDefaultProperties(elem: Element, style: Record<string, string> = null, childStyle: Record<string, string> = null, handler: (prop: Element) => boolean = null): Record<string, string> {\r\n\t\tstyle = style || {};\r\n\r\n\t\txmlUtil.foreach(elem, c => {\r\n\t\t\tif (handler?.(c))\r\n\t\t\t\treturn;\r\n\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"jc\":\r\n\t\t\t\t\tstyle[\"text-align\"] = values.valueOfJc(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"textAlignment\":\r\n\t\t\t\t\tstyle[\"vertical-align\"] = values.valueOfTextAlignment(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"color\":\r\n\t\t\t\t\tstyle[\"color\"] = xmlUtil.colorAttr(c, \"val\", null, autos.color);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sz\":\r\n\t\t\t\t\tstyle[\"font-size\"] = style[\"min-height\"] = xml.lengthAttr(c, \"val\", LengthUsage.FontSize);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"shd\":\r\n\t\t\t\t\tstyle[\"background-color\"] = xmlUtil.colorAttr(c, \"fill\", null, autos.shd);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"highlight\":\r\n\t\t\t\t\tstyle[\"background-color\"] = xmlUtil.colorAttr(c, \"val\", null, autos.highlight);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vertAlign\":\r\n\t\t\t\t\t//TODO\r\n\t\t\t\t\t// style.verticalAlign = values.valueOfVertAlign(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"position\":\r\n\t\t\t\t\tstyle.verticalAlign = xml.lengthAttr(c, \"val\", LengthUsage.FontSize);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tcW\":\r\n\t\t\t\t\tif (this.options.ignoreWidth)\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblW\":\r\n\t\t\t\t\tstyle[\"width\"] = values.valueOfSize(c, \"w\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"trHeight\":\r\n\t\t\t\t\tthis.parseTrHeight(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"strike\":\r\n\t\t\t\t\tstyle[\"text-decoration\"] = xml.boolAttr(c, \"val\", true) ? \"line-through\" : \"none\"\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"b\":\r\n\t\t\t\t\tstyle[\"font-weight\"] = xml.boolAttr(c, \"val\", true) ? \"bold\" : \"normal\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"i\":\r\n\t\t\t\t\tstyle[\"font-style\"] = xml.boolAttr(c, \"val\", true) ? \"italic\" : \"normal\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"caps\":\r\n\t\t\t\t\tstyle[\"text-transform\"] = xml.boolAttr(c, \"val\", true) ? \"uppercase\" : \"none\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"smallCaps\":\r\n\t\t\t\t\tstyle[\"text-transform\"] = xml.boolAttr(c, \"val\", true) ? \"lowercase\" : \"none\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"u\":\r\n\t\t\t\t\tthis.parseUnderline(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"ind\":\r\n\t\t\t\tcase \"tblInd\":\r\n\t\t\t\t\tthis.parseIndentation(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rFonts\":\r\n\t\t\t\t\tthis.parseFont(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblBorders\":\r\n\t\t\t\t\tthis.parseBorderProperties(c, childStyle || style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblCellSpacing\":\r\n\t\t\t\t\tstyle[\"border-spacing\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tstyle[\"border-collapse\"] = \"separate\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pBdr\":\r\n\t\t\t\t\tthis.parseBorderProperties(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bdr\":\r\n\t\t\t\t\tstyle[\"border\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tcBorders\":\r\n\t\t\t\t\tthis.parseBorderProperties(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vanish\":\r\n\t\t\t\t\tif (xml.boolAttr(c, \"val\", true))\r\n\t\t\t\t\t\tstyle[\"display\"] = \"none\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"kern\":\r\n\t\t\t\t\t//TODO\r\n\t\t\t\t\t//style['letter-spacing'] = xml.lengthAttr(elem, 'val', LengthUsage.FontSize);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"noWrap\":\r\n\t\t\t\t\t//TODO\r\n\t\t\t\t\t//style[\"white-space\"] = \"nowrap\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblCellMar\":\r\n\t\t\t\tcase \"tcMar\":\r\n\t\t\t\t\tthis.parseMarginProperties(c, childStyle || style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblLayout\":\r\n\t\t\t\t\tstyle[\"table-layout\"] = values.valueOfTblLayout(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vAlign\":\r\n\t\t\t\t\tstyle[\"vertical-align\"] = values.valueOfTextAlignment(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"spacing\":\r\n\t\t\t\t\tif (elem.localName == \"pPr\")\r\n\t\t\t\t\t\tthis.parseSpacing(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"wordWrap\":\r\n\t\t\t\t\tif (xml.boolAttr(c, \"val\")) //TODO: test with examples\r\n\t\t\t\t\t\tstyle[\"overflow-wrap\"] = \"break-word\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"suppressAutoHyphens\":\r\n\t\t\t\t\tstyle[\"hyphens\"] = xml.boolAttr(c, \"val\", true) ? \"none\" : \"auto\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lang\":\r\n\t\t\t\t\tstyle[\"$lang\"] = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bCs\":\r\n\t\t\t\tcase \"iCs\":\r\n\t\t\t\tcase \"szCs\":\r\n\t\t\t\tcase \"tabs\": //ignore - tabs is parsed by other parser\r\n\t\t\t\tcase \"outlineLvl\": //TODO\r\n\t\t\t\tcase \"contextualSpacing\": //TODO\r\n\t\t\t\tcase \"tblStyleColBandSize\": //TODO\r\n\t\t\t\tcase \"tblStyleRowBandSize\": //TODO\r\n\t\t\t\tcase \"webHidden\": //TODO - maybe web-hidden should be implemented\r\n\t\t\t\tcase \"pageBreakBefore\": //TODO - maybe ignore \r\n\t\t\t\tcase \"suppressLineNumbers\": //TODO - maybe ignore\r\n\t\t\t\tcase \"keepLines\": //TODO - maybe ignore\r\n\t\t\t\tcase \"keepNext\": //TODO - maybe ignore\r\n\t\t\t\tcase \"widowControl\": //TODO - maybe ignore \r\n\t\t\t\tcase \"bidi\": //TODO - maybe ignore\r\n\t\t\t\tcase \"rtl\": //TODO - maybe ignore\r\n\t\t\t\tcase \"noProof\": //ignore spellcheck\r\n\t\t\t\t\t//TODO ignore\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tif (this.options.debug)\r\n\t\t\t\t\t\tconsole.warn(`DOCX: Unknown document element: ${elem.localName}.${c.localName}`);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn style;\r\n\t}\r\n\r\n\tparseUnderline(node: Element, style: Record<string, string>) {\r\n\t\tvar val = xml.attr(node, \"val\");\r\n\r\n\t\tif (val == null)\r\n\t\t\treturn;\r\n\r\n\t\tswitch (val) {\r\n\t\t\tcase \"dash\":\r\n\t\t\tcase \"dashDotDotHeavy\":\r\n\t\t\tcase \"dashDotHeavy\":\r\n\t\t\tcase \"dashedHeavy\":\r\n\t\t\tcase \"dashLong\":\r\n\t\t\tcase \"dashLongHeavy\":\r\n\t\t\tcase \"dotDash\":\r\n\t\t\tcase \"dotDotDash\":\r\n\t\t\t\tstyle[\"text-decoration-style\"] = \"dashed\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"dotted\":\r\n\t\t\tcase \"dottedHeavy\":\r\n\t\t\t\tstyle[\"text-decoration-style\"] = \"dotted\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"double\":\r\n\t\t\t\tstyle[\"text-decoration-style\"] = \"double\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"single\":\r\n\t\t\tcase \"thick\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"wave\":\r\n\t\t\tcase \"wavyDouble\":\r\n\t\t\tcase \"wavyHeavy\":\r\n\t\t\t\tstyle[\"text-decoration-style\"] = \"wavy\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"words\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"none\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"none\";\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\tvar col = xmlUtil.colorAttr(node, \"color\");\r\n\r\n\t\tif (col)\r\n\t\t\tstyle[\"text-decoration-color\"] = col;\r\n\t}\r\n\r\n\tparseFont(node: Element, style: Record<string, string>) {\r\n\t\tvar ascii = xml.attr(node, \"ascii\");\r\n\t\tvar asciiTheme = values.themeValue(node, \"asciiTheme\");\r\n\r\n\t\tvar fonts = [ascii, asciiTheme].filter(x => x).join(', ');\r\n\r\n\t\tif (fonts.length > 0)\r\n\t\t\tstyle[\"font-family\"] = fonts;\r\n\t}\r\n\r\n\tparseIndentation(node: Element, style: Record<string, string>) {\r\n\t\tvar firstLine = xml.lengthAttr(node, \"firstLine\");\r\n\t\tvar hanging = xml.lengthAttr(node, \"hanging\");\r\n\t\tvar left = xml.lengthAttr(node, \"left\");\r\n\t\tvar start = xml.lengthAttr(node, \"start\");\r\n\t\tvar right = xml.lengthAttr(node, \"right\");\r\n\t\tvar end = xml.lengthAttr(node, \"end\");\r\n\r\n\t\tif (firstLine) style[\"text-indent\"] = firstLine;\r\n\t\tif (hanging) style[\"text-indent\"] = `-${hanging}`;\r\n\t\tif (left || start) style[\"margin-left\"] = left || start;\r\n\t\tif (right || end) style[\"margin-right\"] = right || end;\r\n\t}\r\n\r\n\tparseSpacing(node: Element, style: Record<string, string>) {\r\n\t\tvar before = xml.lengthAttr(node, \"before\");\r\n\t\tvar after = xml.lengthAttr(node, \"after\");\r\n\t\tvar line = xml.intAttr(node, \"line\", null);\r\n\t\tvar lineRule = xml.attr(node, \"lineRule\");\r\n\r\n\t\tif (before) style[\"margin-top\"] = before;\r\n\t\tif (after) style[\"margin-bottom\"] = after;\r\n\r\n\t\tif (line !== null) {\r\n\t\t\tswitch (lineRule) {\r\n\t\t\t\tcase \"auto\":\r\n\t\t\t\t\tstyle[\"line-height\"] = `${(line / 240).toFixed(2)}`;\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"atLeast\":\r\n\t\t\t\t\tstyle[\"line-height\"] = `calc(100% + ${line / 20}pt)`;\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tstyle[\"line-height\"] = style[\"min-height\"] = `${line / 20}pt`\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tparseMarginProperties(node: Element, output: Record<string, string>) {\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"left\":\r\n\t\t\t\t\toutput[\"padding-left\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"right\":\r\n\t\t\t\t\toutput[\"padding-right\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"top\":\r\n\t\t\t\t\toutput[\"padding-top\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\toutput[\"padding-bottom\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\r\n\tparseTrHeight(node: Element, output: Record<string, string>) {\r\n\t\tswitch (xml.attr(node, \"hRule\")) {\r\n\t\t\tcase \"exact\":\r\n\t\t\t\toutput[\"height\"] = xml.lengthAttr(node, \"val\");\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"atLeast\":\r\n\t\t\tdefault:\r\n\t\t\t\toutput[\"height\"] = xml.lengthAttr(node, \"val\");\r\n\t\t\t\t// min-height doesn't work for tr\r\n\t\t\t\t//output[\"min-height\"] = xml.sizeAttr(node, \"val\");  \r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\tparseBorderProperties(node: Element, output: Record<string, string>) {\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"start\":\r\n\t\t\t\tcase \"left\":\r\n\t\t\t\t\toutput[\"border-left\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"end\":\r\n\t\t\t\tcase \"right\":\r\n\t\t\t\t\toutput[\"border-right\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"top\":\r\n\t\t\t\t\toutput[\"border-top\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\toutput[\"border-bottom\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n}\r\n\r\nconst knownColors = ['black', 'blue', 'cyan', 'darkBlue', 'darkCyan', 'darkGray', 'darkGreen', 'darkMagenta', 'darkRed', 'darkYellow', 'green', 'lightGray', 'magenta', 'none', 'red', 'white', 'yellow'];\r\n\r\nclass xmlUtil {\r\n\tstatic foreach(node: Element, cb: (n: Element) => void) {\r\n\t\tfor (var i = 0; i < node.childNodes.length; i++) {\r\n\t\t\tlet n = node.childNodes[i];\r\n\r\n\t\t\tif (n.nodeType == Node.ELEMENT_NODE)\r\n\t\t\t\tcb(<Element>n);\r\n\t\t}\r\n\t}\r\n\r\n\tstatic colorAttr(node: Element, attrName: string, defValue: string = null, autoColor: string = 'black') {\r\n\t\tvar v = xml.attr(node, attrName);\r\n\r\n\t\tif (v) {\r\n\t\t\tif (v == \"auto\") {\r\n\t\t\t\treturn autoColor;\r\n\t\t\t} else if (knownColors.includes(v)) {\r\n\t\t\t\treturn v;\r\n\t\t\t}\r\n\r\n\t\t\treturn `#${v}`;\r\n\t\t}\r\n\r\n\t\tvar themeColor = xml.attr(node, \"themeColor\");\r\n\r\n\t\treturn themeColor ? `var(--docx-${themeColor}-color)` : defValue;\r\n\t}\r\n\r\n\tstatic sizeValue(node: Element, type: LengthUsageType = LengthUsage.Dxa) {\r\n\t\treturn convertLength(node.textContent, type);\r\n\t}\r\n}\r\n\r\nclass values {\r\n\tstatic themeValue(c: Element, attr: string) {\r\n\t\tvar val = xml.attr(c, attr);\r\n\t\treturn val ? `var(--docx-${val}-font)` : null;\r\n\t}\r\n\r\n\tstatic valueOfSize(c: Element, attr: string) {\r\n\t\tvar type = LengthUsage.Dxa;\r\n\r\n\t\tswitch (xml.attr(c, \"type\")) {\r\n\t\t\tcase \"dxa\": break;\r\n\t\t\tcase \"pct\": type = LengthUsage.Percent; break;\r\n\t\t\tcase \"auto\": return \"auto\";\r\n\t\t}\r\n\r\n\t\treturn xml.lengthAttr(c, attr, type);\r\n\t}\r\n\r\n\tstatic valueOfMargin(c: Element) {\r\n\t\treturn xml.lengthAttr(c, \"w\");\r\n\t}\r\n\r\n\tstatic valueOfBorder(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tif (type == \"nil\")\r\n\t\t\treturn \"none\";\r\n\r\n\t\tvar color = xmlUtil.colorAttr(c, \"color\");\r\n\t\tvar size = xml.lengthAttr(c, \"sz\", LengthUsage.Border);\r\n\r\n\t\treturn `${size} solid ${color == \"auto\" ? autos.borderColor : color}`;\r\n\t}\r\n\r\n\tstatic valueOfTblLayout(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\t\treturn type == \"fixed\" ? \"fixed\" : \"auto\";\r\n\t}\r\n\r\n\tstatic classNameOfCnfStyle(c: Element) {\r\n\t\tconst val = xml.attr(c, \"val\");\r\n\t\tconst classes = [\r\n\t\t\t'first-row', 'last-row', 'first-col', 'last-col',\r\n\t\t\t'odd-col', 'even-col', 'odd-row', 'even-row',\r\n\t\t\t'ne-cell', 'nw-cell', 'se-cell', 'sw-cell'\r\n\t\t];\r\n\r\n\t\treturn classes.filter((_, i) => val[i] == '1').join(' ');\r\n\t}\r\n\r\n\tstatic valueOfJc(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"start\":\r\n\t\t\tcase \"left\": return \"left\";\r\n\t\t\tcase \"center\": return \"center\";\r\n\t\t\tcase \"end\":\r\n\t\t\tcase \"right\": return \"right\";\r\n\t\t\tcase \"both\": return \"justify\";\r\n\t\t}\r\n\r\n\t\treturn type;\r\n\t}\r\n\r\n\tstatic valueOfVertAlign(c: Element, asTagName: boolean = false) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"subscript\": return \"sub\";\r\n\t\t\tcase \"superscript\": return asTagName ? \"sup\" : \"super\";\r\n\t\t}\r\n\r\n\t\treturn asTagName ? null : type;\r\n\t}\r\n\r\n\tstatic valueOfTextAlignment(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"auto\":\r\n\t\t\tcase \"baseline\": return \"baseline\";\r\n\t\t\tcase \"top\": return \"top\";\r\n\t\t\tcase \"center\": return \"middle\";\r\n\t\t\tcase \"bottom\": return \"bottom\";\r\n\t\t}\r\n\r\n\t\treturn type;\r\n\t}\r\n\r\n\tstatic addSize(a: string, b: string): string {\r\n\t\tif (a == null) return b;\r\n\t\tif (b == null) return a;\r\n\r\n\t\treturn `calc(${a} + ${b})`; //TODO\r\n\t}\r\n\r\n\tstatic classNameOftblLook(c: Element) {\r\n\t\tconst val = xml.hexAttr(c, \"val\", 0);\r\n\t\tlet className = \"\";\r\n\r\n\t\tif (xml.boolAttr(c, \"firstRow\") || (val & 0x0020)) className += \" first-row\";\r\n\t\tif (xml.boolAttr(c, \"lastRow\") || (val & 0x0040)) className += \" last-row\";\r\n\t\tif (xml.boolAttr(c, \"firstColumn\") || (val & 0x0080)) className += \" first-col\";\r\n\t\tif (xml.boolAttr(c, \"lastColumn\") || (val & 0x0100)) className += \" last-col\";\r\n\t\tif (xml.boolAttr(c, \"noHBand\") || (val & 0x0200)) className += \" no-hband\";\r\n\t\tif (xml.boolAttr(c, \"noVBand\") || (val & 0x0400)) className += \" no-vband\";\r\n\r\n\t\treturn className.trim();\r\n\t}\r\n}", "import { Part } from \"../common/part\";\r\nimport { CorePropsDeclaration, parseCoreProps } from \"./core-props\";\r\n\r\nexport class CorePropsPart extends Part {\r\n    props: CorePropsDeclaration;\r\n\r\n    parseXml(root: Element) {\r\n        this.props = parseCoreProps(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface CorePropsDeclaration {\r\n    title: string,\r\n    description: string,\r\n    subject: string,\r\n    creator: string,\r\n    keywords: string,\r\n    language: string,\r\n    lastModifiedBy: string,\r\n    revision: number,\r\n}\r\n\r\nexport function parseCoreProps(root: Element, xmlParser: XmlParser): CorePropsDeclaration {\r\n    const result = <CorePropsDeclaration>{};\r\n\r\n    for (let el of xmlParser.elements(root)) {\r\n        switch (el.localName) {\r\n            case \"title\": result.title = el.textContent; break;\r\n            case \"description\": result.description = el.textContent; break;\r\n            case \"subject\": result.subject = el.textContent; break;\r\n            case \"creator\": result.creator = el.textContent; break;\r\n            case \"keywords\": result.keywords = el.textContent; break;\r\n            case \"language\": result.language = el.textContent; break;\r\n            case \"lastModifiedBy\": result.lastModifiedBy = el.textContent; break;\r\n            case \"revision\": el.textContent && (result.revision = parseInt(el.textContent)); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}", "import { Part } from \"../common/part\";\r\nimport { CustomProperty, parseCustomProps } from \"./custom-props\";\r\n\r\nexport class CustomPropsPart extends Part {\r\n    props: CustomProperty[];\r\n\r\n    parseXml(root: Element) {\r\n        this.props = parseCustomProps(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface CustomProperty {\r\n\tformatId: string;\r\n\tname: string;\r\n\ttype: string;\r\n\tvalue: string;\r\n}\r\n\r\nexport function parseCustomProps(root: Element, xml: XmlParser): CustomProperty[] {\r\n\treturn xml.elements(root, \"property\").map(e => {\r\n\t\tconst firstChild = e.firstChild;\r\n\r\n\t\treturn {\r\n\t\t\tformatId: xml.attr(e, \"fmtid\"),\r\n\t\t\tname: xml.attr(e, \"name\"),\r\n\t\t\ttype: firstChild.nodeName,\r\n\t\t\tvalue: firstChild.textContent\r\n\t\t};\r\n\t});\r\n}", "import { Part } from \"../common/part\";\r\nimport { ExtendedPropsDeclaration, parseExtendedProps } from \"./extended-props\";\r\n\r\nexport class ExtendedPropsPart extends Part {\r\n    props: ExtendedPropsDeclaration;\r\n\r\n    parseXml(root: Element) {\r\n        this.props = parseExtendedProps(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface ExtendedPropsDeclaration {\r\n    template: string,\r\n    totalTime: number,\r\n    pages: number,\r\n    words: number,\r\n    characters: number,\r\n    application: string,\r\n    lines: number,\r\n    paragraphs: number,\r\n    company: string,\r\n    appVersion: string\r\n}\r\n\r\nexport function parseExtendedProps(root: Element, xmlParser: XmlParser): ExtendedPropsDeclaration {\r\n    const result = <ExtendedPropsDeclaration>{\r\n\r\n    };\r\n\r\n    for (let el of xmlParser.elements(root)) {\r\n        switch (el.localName) {\r\n            case \"Template\":\r\n                result.template = el.textContent;\r\n                break;\r\n            case \"Pages\":\r\n                result.pages = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Words\":\r\n                result.words = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Characters\":\r\n                result.characters = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Application\":\r\n                result.application = el.textContent;\r\n                break;\r\n            case \"Lines\":\r\n                result.lines = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Paragraphs\":\r\n                result.paragraphs = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Company\":\r\n                result.company = el.textContent;\r\n                break;\r\n            case \"AppVersion\":\r\n                result.appVersion = el.textContent;\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nfunction safeParseToInt(value: string): number {\r\n    if (typeof value === 'undefined')\r\n        return;\r\n    return parseInt(value);\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { DomType, OpenXmlElement } from \"./dom\";\r\n\r\nexport interface WmlBookmarkStart extends OpenXmlElement {\r\n    id: string;\r\n    name: string;\r\n    colFirst: number;\r\n    colLast: number;\r\n}\r\n\r\nexport interface WmlBookmarkEnd extends OpenXmlElement {\r\n    id: string;\r\n}\r\n\r\nexport function parseBookmarkStart(elem: Element, xml: XmlParser): WmlBookmarkStart {\r\n    return {\r\n        type: DomType.BookmarkStart,\r\n        id: xml.attr(elem, \"id\"),\r\n        name: xml.attr(elem, \"name\"),\r\n        colFirst: xml.intAttr(elem, \"colFirst\"),\r\n        colLast: xml.intAttr(elem, \"colLast\")\r\n    }\r\n}\r\n\r\nexport function parseBookmarkEnd(elem: Element, xml: XmlParser): WmlBookmarkEnd {\r\n    return {\r\n        type: DomType.BookmarkEnd,\r\n        id: xml.attr(elem, \"id\")\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { Length, LengthUsage } from \"./common\";\r\n\r\nexport interface Border {\r\n    color: string;\r\n    type: string;\r\n    size: Length;\r\n    frame: boolean;\r\n    shadow: boolean;\r\n    offset: Length;\r\n}\r\n\r\nexport interface Borders {\r\n    top: Border;\r\n    left: Border;\r\n    right: Border;\r\n    bottom: Border;\r\n}\r\n\r\nexport function parseBorder(elem: Element, xml: XmlParser): Border {\r\n    return {\r\n        type: xml.attr(elem, \"val\"),\r\n        color: xml.attr(elem, \"color\"),\r\n        size: xml.lengthAttr(elem, \"sz\", LengthUsage.Border),\r\n        offset: xml.lengthAttr(elem, \"space\", LengthUsage.Point),\r\n        frame: xml.boolAttr(elem, 'frame'),\r\n        shadow: xml.boolAttr(elem, 'shadow')\r\n    };\r\n}\r\n\r\nexport function parseBorders(elem: Element, xml: XmlParser): Borders {\r\n    var result = <Borders>{};\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"left\": result.left = parseBorder(e, xml); break;\r\n            case \"top\": result.top = parseBorder(e, xml); break;\r\n            case \"right\": result.right = parseBorder(e, xml); break;\r\n            case \"bottom\": result.bottom = parseBorder(e, xml); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport const ns = {\r\n    wordml: \"http://schemas.openxmlformats.org/wordprocessingml/2006/main\",\r\n    drawingml: \"http://schemas.openxmlformats.org/drawingml/2006/main\",\r\n    picture: \"http://schemas.openxmlformats.org/drawingml/2006/picture\",\r\n\tcompatibility: \"http://schemas.openxmlformats.org/markup-compatibility/2006\",\r\n\tmath: \"http://schemas.openxmlformats.org/officeDocument/2006/math\"\r\n}\r\n\r\nexport type LengthType = \"px\" | \"pt\" | \"%\" | \"\";\r\nexport type Length = string;\r\n\r\nexport interface Font {\r\n    name: string;\r\n    family: string;\r\n}\r\n\r\nexport interface CommonProperties {\r\n    fontSize: Length;\r\n    color: string;\r\n}\r\n\r\nexport type LengthUsageType = { mul: number, unit: LengthType };\r\n\r\nexport const LengthUsage: Record<string, LengthUsageType> = {\r\n    Dxa: { mul: 0.05, unit: \"pt\" }, //twips\r\n    Emu: { mul: 1 / 12700, unit: \"pt\" },\r\n    FontSize: { mul: 0.5, unit: \"pt\" },\r\n    Border: { mul: 0.125, unit: \"pt\" },\r\n    Point: { mul: 1, unit: \"pt\" },\r\n    Percent: { mul: 0.02, unit: \"%\" },\r\n    LineHeight: { mul: 1 / 240, unit: \"\" },\r\n    VmlEmu: { mul: 1 / 12700, unit: \"\" },\r\n}\r\n\r\nexport function convertLength(val: string, usage: LengthUsageType = LengthUsage.Dxa): string {\r\n    //\"simplified\" docx documents use pt's as units\r\n    if (val == null || /.+(p[xt]|[%])$/.test(val)) {\r\n        return val;\r\n    }\r\n\r\n\treturn `${(parseInt(val) * usage.mul).toFixed(2)}${usage.unit}`;\r\n}\r\n\r\nexport function convertBoolean(v: string, defaultValue = false): boolean {\r\n    switch (v) {\r\n        case \"1\": return true;\r\n        case \"0\": return false;\r\n        case \"on\": return true;\r\n        case \"off\": return false;\r\n        case \"true\": return true;\r\n        case \"false\": return false;\r\n        default: return defaultValue;\r\n    }\r\n}\r\n\r\nexport function convertPercentage(val: string): number {\r\n    return val ? parseInt(val) / 100 : null;\r\n}\r\n\r\nexport function parseCommonProperty(elem: Element, props: CommonProperties, xml: XmlParser): boolean {\r\n    if(elem.namespaceURI != ns.wordml)\r\n        return false;\r\n\r\n    switch(elem.localName) {\r\n        case \"color\": \r\n            props.color = xml.attr(elem, \"val\");\r\n            break;\r\n\r\n        case \"sz\":\r\n            props.fontSize = xml.lengthAttr(elem, \"val\", LengthUsage.FontSize);\r\n            break;\r\n\r\n        default:\r\n            return false;\r\n    }\r\n\r\n    return true;\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { DocumentElement } from \"./document\";\r\n\r\nexport class DocumentPart extends Part {\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n    \r\n    body: DocumentElement\r\n\r\n    parseXml(root: Element) {\r\n        this.body = this._documentParser.parseDocumentFile(root);\r\n    }\r\n}", "export enum DomType {\r\n    Document = \"document\",\r\n    Paragraph = \"paragraph\",\r\n    Run = \"run\",\r\n    Break = \"break\",\r\n    NoBreakHyphen = \"noBreakHyphen\",\r\n    Table = \"table\",\r\n    Row = \"row\",\r\n    Cell = \"cell\",\r\n    Hyperlink = \"hyperlink\",\r\n    Drawing = \"drawing\",\r\n    Image = \"image\",\r\n    Text = \"text\",\r\n    Tab = \"tab\",\r\n    Symbol = \"symbol\",\r\n    BookmarkStart = \"bookmarkStart\",\r\n    BookmarkEnd = \"bookmarkEnd\",\r\n    Footer = \"footer\",\r\n    Header = \"header\",\r\n    FootnoteReference = \"footnoteReference\", \r\n\tEndnoteReference = \"endnoteReference\",\r\n    Footnote = \"footnote\",\r\n    Endnote = \"endnote\",\r\n    SimpleField = \"simpleField\",\r\n    ComplexField = \"complexField\",\r\n    Instruction = \"instruction\",\r\n\tVmlPicture = \"vmlPicture\",\r\n\tMmlMath = \"mmlMath\",\r\n\tMmlMathParagraph = \"mmlMathParagraph\",\r\n\tMmlFraction = \"mmlFraction\",\r\n\tMmlFunction = \"mmlFunction\",\r\n\tMmlFunctionName = \"mmlFunctionName\",\r\n\tMmlNumerator = \"mmlNumerator\",\r\n\tMmlDenominator = \"mmlDenominator\",\r\n\tMmlRadical = \"mmlRadical\",\r\n\tMmlBase = \"mmlBase\",\r\n\tMmlDegree = \"mmlDegree\",\r\n\tMmlSuperscript = \"mmlSuperscript\",\r\n\tMmlSubscript = \"mmlSubscript\",\r\n\tMmlPreSubSuper = \"mmlPreSubSuper\",\r\n\tMmlSubArgument = \"mmlSubArgument\",\r\n\tMmlSuperArgument = \"mmlSuperArgument\",\r\n\tMmlNary = \"mmlNary\",\r\n\tMmlDelimiter = \"mmlDelimiter\",\r\n\tMmlRun = \"mmlRun\",\r\n\tMmlEquationArray = \"mmlEquationArray\",\r\n\tMmlLimit = \"mmlLimit\",\r\n\tMmlLimitLower = \"mmlLimitLower\",\r\n\tMmlMatrix = \"mmlMatrix\",\r\n\tMmlMatrixRow = \"mmlMatrixRow\",\r\n\tMmlBox = \"mmlBox\",\r\n\tMmlBar = \"mmlBar\",\r\n\tMmlGroupChar = \"mmlGroupChar\",\r\n\tVmlElement = \"vmlElement\",\r\n\tInserted = \"inserted\",\r\n\tDeleted = \"deleted\",\r\n\tDeletedText = \"deletedText\"\r\n}\r\n\r\nexport interface OpenXmlElement {\r\n    type: DomType;\r\n    children?: OpenXmlElement[];\r\n    cssStyle?: Record<string, string>;\r\n    props?: Record<string, any>;\r\n    \r\n\tstyleName?: string; //style name\r\n\tclassName?: string; //class mods\r\n\r\n    parent?: OpenXmlElement;\r\n}\r\n\r\nexport abstract class OpenXmlElementBase implements OpenXmlElement {\r\n    type: DomType;\r\n    children?: OpenXmlElement[] = [];\r\n    cssStyle?: Record<string, string> = {};\r\n    props?: Record<string, any>;\r\n\r\n    className?: string;\r\n    styleName?: string;\r\n\r\n    parent?: OpenXmlElement;\r\n}\r\n\r\nexport interface WmlHyperlink extends OpenXmlElement {\r\n\tid?: string;\r\n    href?: string;\r\n}\r\n\r\nexport interface WmlNoteReference extends OpenXmlElement {\r\n    id: string;\r\n}\r\n\r\nexport interface WmlBreak extends OpenXmlElement{\r\n    break: \"page\" | \"lastRenderedPageBreak\" | \"textWrapping\";\r\n}\r\n\r\nexport interface WmlText extends OpenXmlElement{\r\n    text: string;\r\n}\r\n\r\nexport interface WmlSymbol extends OpenXmlElement {\r\n    font: string;\r\n    char: string;\r\n}\r\n\r\nexport interface WmlTable extends OpenXmlElement {\r\n    columns?: WmlTableColumn[];\r\n    cellStyle?: Record<string, string>;\r\n\r\n\tcolBandSize?: number;\r\n\trowBandSize?: number;\r\n}\r\n\r\nexport interface WmlTableRow extends OpenXmlElement {\r\n\tisHeader?: boolean;\r\n}\r\n\r\nexport interface WmlTableCell extends OpenXmlElement {\r\n\tverticalMerge?: 'restart' | 'continue' | string;\r\n    span?: number;\r\n}\r\n\r\nexport interface IDomImage extends OpenXmlElement {\r\n    src: string;\r\n}\r\n\r\nexport interface WmlTableColumn {\r\n    width?: string;\r\n}\r\n\r\nexport interface IDomNumbering {\r\n    id: string;\r\n    level: number;\r\n    start: number;\r\n    pStyleName: string;\r\n    pStyle: Record<string, string>;\r\n    rStyle: Record<string, string>;\r\n    levelText?: string;\r\n    suff: string;\r\n    format?: string;\r\n    bullet?: NumberingPicBullet;\r\n}\r\n\r\nexport interface NumberingPicBullet {\r\n    id: number;\r\n    src: string;\r\n    style?: string;\r\n}\r\n", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { Length } from \"./common\";\r\n\r\nexport interface LineSpacing {\r\n    after: Length;\r\n    before: Length;\r\n    line: number;\r\n    lineRule: \"atLeast\" | \"exactly\" | \"auto\";\r\n}\r\n\r\nexport function parseLineSpacing(elem: Element, xml: XmlParser): LineSpacing {\r\n    return {\r\n        before: xml.lengthAttr(elem, \"before\"),\r\n        after: xml.lengthAttr(elem, \"after\"),\r\n        line: xml.intAttr(elem, \"line\"),\r\n        lineRule: xml.attr(elem, \"lineRule\")\r\n    } as LineSpacing;\r\n}", "import { OpenXmlElement } from \"./dom\";\r\nimport { CommonProperties, Length, ns, parseCommonProperty } from \"./common\";\r\nimport { Borders } from \"./border\";\r\nimport { parseSectionProperties, SectionProperties } from \"./section\";\r\nimport { LineSpacing, parseLineSpacing } from \"./line-spacing\";\r\nimport { XmlParser } from \"../parser/xml-parser\";\r\nimport { parseRunProperties, RunProperties } from \"./run\";\r\n\r\nexport interface WmlParagraph extends OpenXmlElement, ParagraphProperties {\r\n}\r\n\r\nexport interface ParagraphProperties extends CommonProperties {\r\n    sectionProps: SectionProperties;\r\n    tabs: ParagraphTab[];\r\n    numbering: ParagraphNumbering;\r\n\r\n    border: Borders;\r\n    textAlignment: \"auto\" | \"baseline\" | \"bottom\" | \"center\" | \"top\" | string;\r\n    lineSpacing: LineSpacing;\r\n    keepLines: boolean;\r\n    keepNext: boolean;\r\n    pageBreakBefore: boolean;\r\n    outlineLevel: number;\r\n\tstyleName?: string;\r\n\r\n    runProps: RunProperties;\r\n}\r\n\r\nexport interface ParagraphTab {\r\n    style: \"bar\" | \"center\" | \"clear\" | \"decimal\" | \"end\" | \"num\" | \"start\" | \"left\" | \"right\";\r\n    leader: \"none\" | \"dot\" | \"heavy\" | \"hyphen\" | \"middleDot\" | \"underscore\";\r\n    position: Length;\r\n}\r\n\r\nexport interface ParagraphNumbering {\r\n    id: string;\r\n    level: number;\r\n}\r\n\r\nexport function parseParagraphProperties(elem: Element, xml: XmlParser): ParagraphProperties {\r\n    let result = <ParagraphProperties>{};\r\n\r\n    for(let el of xml.elements(elem)) {\r\n        parseParagraphProperty(el, result, xml);\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseParagraphProperty(elem: Element, props: ParagraphProperties, xml: XmlParser) {\r\n    if (elem.namespaceURI != ns.wordml)\r\n        return false;\r\n\r\n    if(parseCommonProperty(elem, props, xml))\r\n        return true;\r\n\r\n    switch (elem.localName) {\r\n        case \"tabs\":\r\n            props.tabs = parseTabs(elem, xml);\r\n            break;\r\n\r\n        case \"sectPr\":\r\n            props.sectionProps = parseSectionProperties(elem, xml);\r\n            break;\r\n\r\n        case \"numPr\":\r\n            props.numbering = parseNumbering(elem, xml);\r\n            break;\r\n        \r\n        case \"spacing\":\r\n            props.lineSpacing = parseLineSpacing(elem, xml);\r\n            return false; // TODO\r\n            break;\r\n\r\n        case \"textAlignment\":\r\n            props.textAlignment = xml.attr(elem, \"val\");\r\n            return false; //TODO\r\n            break;\r\n\r\n        case \"keepLines\":\r\n            props.keepLines = xml.boolAttr(elem, \"val\", true);\r\n            break;\r\n    \r\n        case \"keepNext\":\r\n            props.keepNext = xml.boolAttr(elem, \"val\", true);\r\n            break;\r\n        \r\n        case \"pageBreakBefore\":\r\n            props.pageBreakBefore = xml.boolAttr(elem, \"val\", true);\r\n            break;\r\n        \r\n        case \"outlineLvl\":\r\n            props.outlineLevel = xml.intAttr(elem, \"val\");\r\n            break;\r\n\r\n        case \"pStyle\":\r\n            props.styleName = xml.attr(elem, \"val\");\r\n            break;\r\n\r\n        case \"rPr\":\r\n            props.runProps = parseRunProperties(elem, xml);\r\n            break;\r\n        \r\n        default:\r\n            return false;\r\n    }\r\n\r\n    return true;\r\n}\r\n\r\nexport function parseTabs(elem: Element, xml: XmlParser): ParagraphTab[] {\r\n    return xml.elements(elem, \"tab\")\r\n        .map(e => <ParagraphTab>{\r\n            position: xml.lengthAttr(e, \"pos\"),\r\n            leader: xml.attr(e, \"leader\"),\r\n            style: xml.attr(e, \"val\")\r\n        });\r\n}\r\n\r\nexport function parseNumbering(elem: Element, xml: XmlParser): ParagraphNumbering {\r\n    var result = <ParagraphNumbering>{};\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"numId\":\r\n                result.id = xml.attr(e, \"val\");\r\n                break;\r\n\r\n            case \"ilvl\":\r\n                result.level = xml.intAttr(e, \"val\");\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { CommonProperties, parseCommonProperty } from \"./common\";\r\nimport { OpenXmlElement } from \"./dom\";\r\n\r\nexport interface WmlRun extends OpenXmlElement, RunProperties {\r\n    id?: string;\r\n    verticalAlign?: string;\r\n\tfieldRun?: boolean;  \r\n}\r\n\r\nexport interface RunProperties extends CommonProperties {\r\n\r\n}\r\n\r\nexport function parseRunProperties(elem: Element, xml: XmlParser): RunProperties {\r\n    let result = <RunProperties>{};\r\n\r\n    for(let el of xml.elements(elem)) {\r\n        parseRunProperty(el, result, xml);\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseRunProperty(elem: Element, props: RunProperties, xml: XmlParser) {\r\n    if (parseCommonProperty(elem, props, xml))\r\n        return true;\r\n\r\n    return false;\r\n}", "import globalXmlParser, { XmlParser } from \"../parser/xml-parser\";\r\nimport { Borders, parseBorders } from \"./border\";\r\nimport { Length } from \"./common\";\r\n\r\nexport interface Column {\r\n    space: Length;\r\n    width: Length;\r\n}\r\n\r\nexport interface Columns {\r\n    space: Length;\r\n    numberOfColumns: number;\r\n    separator: boolean;\r\n    equalWidth: boolean;\r\n    columns: Column[];\r\n}\r\n\r\nexport interface PageSize {\r\n    width: Length, \r\n    height: Length, \r\n    orientation: \"landscape\" | string \r\n}\r\n\r\nexport interface PageNumber {\r\n    start: number;\r\n    chapSep: \"colon\" | \"emDash\" | \"endash\" | \"hyphen\" | \"period\" | string;\r\n    chapStyle: string;\r\n    format: \"none\" | \"cardinalText\" | \"decimal\" | \"decimalEnclosedCircle\" | \"decimalEnclosedFullstop\" \r\n        | \"decimalEnclosedParen\" | \"decimalZero\" | \"lowerLetter\" | \"lowerRoman\"\r\n        | \"ordinalText\" | \"upperLetter\" | \"upperRoman\" | string;\r\n}\r\n\r\nexport interface PageMargins {\r\n    top: Length;\r\n    right: Length;\r\n    bottom: Length;\r\n    left: Length;\r\n    header: Length;\r\n    footer: Length;\r\n    gutter: Length;\r\n}\r\n\r\nexport enum SectionType {\r\n    Continuous = \"continuous\",\r\n    NextPage = \"nextPage\", \r\n    NextColumn = \"nextColumn\",\r\n    EvenPage = \"evenPage\",\r\n    OddPage = \"oddPage\",\r\n}\r\n\r\nexport interface FooterHeaderReference {\r\n    id: string;\r\n    type: string | \"first\" | \"even\" | \"default\";\r\n}\r\n\r\nexport interface SectionProperties {\r\n    type: SectionType | string;\r\n    pageSize: PageSize,\r\n    pageMargins: PageMargins,\r\n    pageBorders: Borders;\r\n    pageNumber: PageNumber;\r\n    columns: Columns;\r\n    footerRefs: FooterHeaderReference[];\r\n    headerRefs: FooterHeaderReference[];\r\n    titlePage: boolean;\r\n}\r\n\r\nexport function parseSectionProperties(elem: Element, xml: XmlParser = globalXmlParser): SectionProperties {\r\n    var section = <SectionProperties>{};\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"pgSz\":\r\n                section.pageSize = {\r\n                    width: xml.lengthAttr(e, \"w\"),\r\n                    height: xml.lengthAttr(e, \"h\"),\r\n                    orientation: xml.attr(e, \"orient\")\r\n                }\r\n                break;\r\n\r\n            case \"type\":\r\n                section.type = xml.attr(e, \"val\");\r\n                break;\r\n\r\n            case \"pgMar\":\r\n                section.pageMargins = {\r\n                    left: xml.lengthAttr(e, \"left\"),\r\n                    right: xml.lengthAttr(e, \"right\"),\r\n                    top: xml.lengthAttr(e, \"top\"),\r\n                    bottom: xml.lengthAttr(e, \"bottom\"),\r\n                    header: xml.lengthAttr(e, \"header\"),\r\n                    footer: xml.lengthAttr(e, \"footer\"),\r\n                    gutter: xml.lengthAttr(e, \"gutter\"),\r\n                };\r\n                break;\r\n\r\n            case \"cols\":\r\n                section.columns = parseColumns(e, xml);\r\n                break;\r\n\r\n            case \"headerReference\":\r\n                (section.headerRefs ?? (section.headerRefs = [])).push(parseFooterHeaderReference(e, xml)); \r\n                break;\r\n            \r\n            case \"footerReference\":\r\n                (section.footerRefs ?? (section.footerRefs = [])).push(parseFooterHeaderReference(e, xml)); \r\n                break;\r\n\r\n            case \"titlePg\":\r\n                section.titlePage = xml.boolAttr(e, \"val\", true);\r\n                break;\r\n\r\n            case \"pgBorders\":\r\n                section.pageBorders = parseBorders(e, xml);\r\n                break;\r\n\r\n            case \"pgNumType\":\r\n                section.pageNumber = parsePageNumber(e, xml);\r\n                break;\r\n        }\r\n    }\r\n\r\n    return section;\r\n}\r\n\r\nfunction parseColumns(elem: Element, xml: XmlParser): Columns {\r\n    return {\r\n        numberOfColumns: xml.intAttr(elem, \"num\"),\r\n        space: xml.lengthAttr(elem, \"space\"),\r\n        separator: xml.boolAttr(elem, \"sep\"),\r\n        equalWidth: xml.boolAttr(elem, \"equalWidth\", true),\r\n        columns: xml.elements(elem, \"col\")\r\n            .map(e => <Column>{\r\n                width: xml.lengthAttr(e, \"w\"),\r\n                space: xml.lengthAttr(e, \"space\")\r\n            })\r\n    };\r\n}\r\n\r\nfunction parsePageNumber(elem: Element, xml: XmlParser): PageNumber {\r\n    return {\r\n        chapSep: xml.attr(elem, \"chapSep\"),\r\n        chapStyle: xml.attr(elem, \"chapStyle\"),\r\n        format: xml.attr(elem, \"fmt\"),\r\n        start: xml.intAttr(elem, \"start\")\r\n    };\r\n}\r\n\r\nfunction parseFooterHeaderReference(elem: Element, xml: XmlParser): FooterHeaderReference {\r\n    return {\r\n        id: xml.attr(elem, \"id\"),\r\n        type: xml.attr(elem, \"type\"),\r\n    }\r\n}", "import { WordDocument } from './word-document';\r\nimport { DocumentParser } from './document-parser';\r\nimport { HtmlRenderer } from './html-renderer';\r\n\r\nexport interface Options {\r\n    inWrapper: boolean;\r\n    ignoreWidth: boolean;\r\n    ignoreHeight: boolean;\r\n    ignoreFonts: boolean;\r\n    breakPages: boolean;\r\n    debug: boolean;\r\n    experimental: boolean;\r\n    className: string;\r\n    trimXmlDeclaration: boolean;\r\n    renderHeaders: boolean;\r\n    renderFooters: boolean;\r\n    renderFootnotes: boolean;\r\n\trenderEndnotes: boolean;\r\n    ignoreLastRenderedPageBreak: boolean;\r\n\tuseBase64URL: boolean;\r\n\trenderChanges: boolean;\r\n}\r\n\r\nexport const defaultOptions: Options = {\r\n    ignoreHeight: false,\r\n    ignoreWidth: false,\r\n    ignoreFonts: false,\r\n    breakPages: true,\r\n    debug: false,\r\n    experimental: false,\r\n    className: \"docx\",\r\n    inWrapper: true,\r\n    trimXmlDeclaration: true,\r\n    ignoreLastRenderedPageBreak: true,\r\n    renderHeaders: true,\r\n    renderFooters: true,\r\n    renderFootnotes: true,\r\n\trenderEndnotes: true,\r\n\tuseBase64URL: false,\r\n\trenderChanges: false\r\n}\r\n\r\nexport function praseAsync(data: Blob | any, userOptions: Partial<Options> = null): Promise<any>  {\r\n    const ops = { ...defaultOptions, ...userOptions };\r\n    return WordDocument.load(data, new DocumentParser(ops), ops);\r\n}\r\n\r\nexport async function renderAsync(data: Blob | any, bodyContainer: HTMLElement, styleContainer: HTMLElement = null, userOptions: Partial<Options> = null): Promise<any> {\r\n    const ops = { ...defaultOptions, ...userOptions };\r\n    const renderer = new HtmlRenderer(window.document);\r\n\tconst doc = await WordDocument.load(data, new DocumentParser(ops), ops)\r\n\r\n\trenderer.render(doc, bodyContainer, styleContainer, ops);\r\n\t\r\n    return doc;\r\n}", "import { Part } from \"../common/part\";\r\nimport { FontDeclaration, parseFonts } from \"./fonts\";\r\n\r\nexport class FontTablePart extends Part {\r\n    fonts: FontDeclaration[];\r\n\r\n    parseXml(root: Element) {\r\n        this.fonts = parseFonts(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nconst embedFontTypeMap = {\r\n    embedRegular: 'regular',\r\n    embedBold: 'bold',\r\n    embedItalic: 'italic',\r\n    embedBoldItalic: 'boldItalic',\r\n}\r\n\r\nexport interface FontDeclaration {\r\n    name: string,\r\n    altName: string,\r\n    family: string,\r\n    embedFontRefs: EmbedFontRef[];\r\n}\r\n\r\nexport interface EmbedFontRef {\r\n    id: string;\r\n    key: string;\r\n    type: 'regular' | 'bold' | 'italic' | 'boldItalic';\r\n}\r\n\r\nexport function parseFonts(root: Element, xml: XmlParser): FontDeclaration[] {\r\n    return xml.elements(root).map(el => parseFont(el, xml));\r\n}\r\n\r\nexport function parseFont(elem: Element, xml: XmlParser): FontDeclaration {\r\n    let result = <FontDeclaration>{\r\n        name: xml.attr(elem, \"name\"),\r\n        embedFontRefs: []\r\n    };\r\n\r\n    for (let el of xml.elements(elem)) {\r\n        switch (el.localName) {\r\n            case \"family\":\r\n                result.family = xml.attr(el, \"val\");\r\n                break;\r\n\r\n            case \"altName\":\r\n                result.altName = xml.attr(el, \"val\");\r\n                break;\r\n\r\n            case \"embedRegular\":\r\n            case \"embedBold\":\r\n            case \"embedItalic\":\r\n            case \"embedBoldItalic\":\r\n                result.embedFontRefs.push(parseEmbedFontRef(el, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseEmbedFontRef(elem: Element, xml: XmlParser): EmbedFontRef {\r\n    return { \r\n        id: xml.attr(elem, \"id\"), \r\n        key: xml.attr(elem, \"fontKey\"),\r\n        type: embedFontTypeMap[elem.localName]\r\n    };\r\n}", "import { OpenXmlElementBase, DomType } from \"../document/dom\";\r\n\r\nexport class WmlHeader extends OpenXmlElementBase {\r\n    type: DomType = DomType.Header;\r\n}\r\n\r\nexport class WmlFooter extends OpenXmlElementBase {\r\n    type: DomType = DomType.Footer;\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { OpenXmlElement } from \"../document/dom\";\r\nimport { WmlHeader, WmlFooter } from \"./elements\";\r\n\r\nexport abstract class BaseHeaderFooterPart<T extends OpenXmlElement = OpenXmlElement> extends Part {\r\n    rootElement: T;\r\n\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.rootElement = this.createRootElement();\r\n        this.rootElement.children = this._documentParser.parseBodyElements(root);\r\n    }\r\n\r\n    protected abstract createRootElement(): T;\r\n}\r\n\r\nexport class HeaderPart extends BaseHeaderFooterPart<WmlHeader> {\r\n    protected createRootElement(): WmlHeader {\r\n        return new WmlHeader();\r\n    }\r\n}\r\n\r\nexport class FooterPart extends BaseHeaderFooterPart<WmlFooter> {\r\n    protected createRootElement(): WmlFooter {\r\n        return new WmlFooter();\r\n    }\r\n}", "import { WordDocument } from './word-document';\r\nimport {\r\n\tDomType, WmlTable, IDomNumbering,\r\n\tWmlHyperlink, IDomImage, OpenXmlElement, WmlTableColumn, WmlTableCell, WmlText, WmlSymbol, WmlBreak, WmlNoteReference\r\n} from './document/dom';\r\nimport { CommonProperties } from './document/common';\r\nimport { Options } from './docx-preview';\r\nimport { DocumentElement } from './document/document';\r\nimport { WmlParagraph } from './document/paragraph';\r\nimport { asArray, escapeClassName, isString, keyBy, mergeDeep } from './utils';\r\nimport { computePixelToPoint, updateTabStop } from './javascript';\r\nimport { FontTablePart } from './font-table/font-table';\r\nimport { FooterHeaderReference, SectionProperties } from './document/section';\r\nimport { WmlRun, RunProperties } from './document/run';\r\nimport { WmlBookmarkStart } from './document/bookmarks';\r\nimport { IDomStyle } from './document/style';\r\nimport { WmlBaseNote, WmlFootnote } from './notes/elements';\r\nimport { ThemePart } from './theme/theme-part';\r\nimport { BaseHeaderFooterPart } from './header-footer/parts';\r\nimport { Part } from './common/part';\r\nimport { VmlElement } from './vml/vml';\r\n\r\nconst ns = {\r\n\tsvg: \"http://www.w3.org/2000/svg\",\r\n\tmathML: \"http://www.w3.org/1998/Math/MathML\"\r\n}\r\n\r\ninterface CellPos {\r\n\tcol: number;\r\n\trow: number;\r\n}\r\n\r\ntype CellVerticalMergeType = Record<number, HTMLTableCellElement>;\r\n\r\nexport class HtmlRenderer {\r\n\r\n\tclassName: string = \"docx\";\r\n\trootSelector: string;\r\n\tdocument: WordDocument;\r\n\toptions: Options;\r\n\tstyleMap: Record<string, IDomStyle> = {};\r\n\tcurrentPart: Part = null;\r\n\r\n\ttableVerticalMerges: CellVerticalMergeType[] = [];\r\n\tcurrentVerticalMerge: CellVerticalMergeType = null;\r\n\ttableCellPositions: CellPos[] = [];\r\n\tcurrentCellPosition: CellPos = null;\r\n\r\n\tfootnoteMap: Record<string, WmlFootnote> = {};\r\n\tendnoteMap: Record<string, WmlFootnote> = {};\r\n\tcurrentFootnoteIds: string[];\r\n\tcurrentEndnoteIds: string[] = [];\r\n\tusedHederFooterParts: any[] = [];\r\n\r\n\tdefaultTabSize: string;\r\n\tcurrentTabs: any[] = [];\r\n\ttabsTimeout: any = 0;\r\n\r\n\tconstructor(public htmlDocument: Document) {\r\n\t}\r\n\r\n\trender(document: WordDocument, bodyContainer: HTMLElement, styleContainer: HTMLElement = null, options: Options) {\r\n\t\tthis.document = document;\r\n\t\tthis.options = options;\r\n\t\tthis.className = options.className;\r\n\t\tthis.rootSelector = options.inWrapper ? `.${this.className}-wrapper` : ':root';\r\n\t\tthis.styleMap = null;\r\n\r\n\t\tstyleContainer = styleContainer || bodyContainer;\r\n\r\n\t\tremoveAllElements(styleContainer);\r\n\t\tremoveAllElements(bodyContainer);\r\n\r\n\t\tappendComment(styleContainer, \"docxjs library predefined styles\");\r\n\t\tstyleContainer.appendChild(this.renderDefaultStyle());\r\n\r\n\t\tif (document.themePart) {\r\n\t\t\tappendComment(styleContainer, \"docxjs document theme values\");\r\n\t\t\tthis.renderTheme(document.themePart, styleContainer);\r\n\t\t}\r\n\r\n\t\tif (document.stylesPart != null) {\r\n\t\t\tthis.styleMap = this.processStyles(document.stylesPart.styles);\r\n\r\n\t\t\tappendComment(styleContainer, \"docxjs document styles\");\r\n\t\t\tstyleContainer.appendChild(this.renderStyles(document.stylesPart.styles));\r\n\t\t}\r\n\r\n\t\tif (document.numberingPart) {\r\n\t\t\tthis.prodessNumberings(document.numberingPart.domNumberings);\r\n\r\n\t\t\tappendComment(styleContainer, \"docxjs document numbering styles\");\r\n\t\t\tstyleContainer.appendChild(this.renderNumbering(document.numberingPart.domNumberings, styleContainer));\r\n\t\t\t//styleContainer.appendChild(this.renderNumbering2(document.numberingPart, styleContainer));\r\n\t\t}\r\n\r\n\t\tif (document.footnotesPart) {\r\n\t\t\tthis.footnoteMap = keyBy(document.footnotesPart.notes, x => x.id);\r\n\t\t}\r\n\r\n\t\tif (document.endnotesPart) {\r\n\t\t\tthis.endnoteMap = keyBy(document.endnotesPart.notes, x => x.id);\r\n\t\t}\r\n\r\n\t\tif (document.settingsPart) {\r\n\t\t\tthis.defaultTabSize = document.settingsPart.settings?.defaultTabStop;\r\n\t\t}\r\n\r\n\t\tif (!options.ignoreFonts && document.fontTablePart)\r\n\t\t\tthis.renderFontTable(document.fontTablePart, styleContainer);\r\n\r\n\t\tvar sectionElements = this.renderSections(document.documentPart.body);\r\n\r\n\t\tif (this.options.inWrapper) {\r\n\t\t\tbodyContainer.appendChild(this.renderWrapper(sectionElements));\r\n\t\t} else {\r\n\t\t\tappendChildren(bodyContainer, sectionElements);\r\n\t\t}\r\n\r\n\t\tthis.refreshTabStops();\r\n\t}\r\n\r\n\trenderTheme(themePart: ThemePart, styleContainer: HTMLElement) {\r\n\t\tconst variables = {};\r\n\t\tconst fontScheme = themePart.theme?.fontScheme;\r\n\r\n\t\tif (fontScheme) {\r\n\t\t\tif (fontScheme.majorFont) {\r\n\t\t\t\tvariables['--docx-majorHAnsi-font'] = fontScheme.majorFont.latinTypeface;\r\n\t\t\t}\r\n\r\n\t\t\tif (fontScheme.minorFont) {\r\n\t\t\t\tvariables['--docx-minorHAnsi-font'] = fontScheme.minorFont.latinTypeface;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst colorScheme = themePart.theme?.colorScheme;\r\n\r\n\t\tif (colorScheme) {\r\n\t\t\tfor (let [k, v] of Object.entries(colorScheme.colors)) {\r\n\t\t\t\tvariables[`--docx-${k}-color`] = `#${v}`;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst cssText = this.styleToString(`.${this.className}`, variables);\r\n\t\tstyleContainer.appendChild(createStyleElement(cssText));\r\n\t}\r\n\r\n\trenderFontTable(fontsPart: FontTablePart, styleContainer: HTMLElement) {\r\n\t\tfor (let f of fontsPart.fonts) {\r\n\t\t\tfor (let ref of f.embedFontRefs) {\r\n\t\t\t\tthis.document.loadFont(ref.id, ref.key).then(fontData => {\r\n\t\t\t\t\tconst cssValues = {\r\n\t\t\t\t\t\t'font-family': f.name,\r\n\t\t\t\t\t\t'src': `url(${fontData})`\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tif (ref.type == \"bold\" || ref.type == \"boldItalic\") {\r\n\t\t\t\t\t\tcssValues['font-weight'] = 'bold';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (ref.type == \"italic\" || ref.type == \"boldItalic\") {\r\n\t\t\t\t\t\tcssValues['font-style'] = 'italic';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tappendComment(styleContainer, `docxjs ${f.name} font`);\r\n\t\t\t\t\tconst cssText = this.styleToString(\"@font-face\", cssValues);\r\n\t\t\t\t\tstyleContainer.appendChild(createStyleElement(cssText));\r\n\t\t\t\t\tthis.refreshTabStops();\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprocessStyleName(className: string): string {\r\n\t\treturn className ? `${this.className}_${escapeClassName(className)}` : this.className;\r\n\t}\r\n\r\n\tprocessStyles(styles: IDomStyle[]) {\r\n\t\tconst stylesMap = keyBy(styles.filter(x => x.id != null), x => x.id);\r\n\r\n\t\tfor (const style of styles.filter(x => x.basedOn)) {\r\n\t\t\tvar baseStyle = stylesMap[style.basedOn];\r\n\r\n\t\t\tif (baseStyle) {\r\n\t\t\t\tstyle.paragraphProps = mergeDeep(style.paragraphProps, baseStyle.paragraphProps);\r\n\t\t\t\tstyle.runProps = mergeDeep(style.runProps, baseStyle.runProps);\r\n\r\n\t\t\t\tfor (const baseValues of baseStyle.styles) {\r\n\t\t\t\t\tconst styleValues = style.styles.find(x => x.target == baseValues.target);\r\n\r\n\t\t\t\t\tif (styleValues) {\r\n\t\t\t\t\t\tthis.copyStyleProperties(baseValues.values, styleValues.values);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstyle.styles.push({ ...baseValues, values: { ...baseValues.values } });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse if (this.options.debug)\r\n\t\t\t\tconsole.warn(`Can't find base style ${style.basedOn}`);\r\n\t\t}\r\n\r\n\t\tfor (let style of styles) {\r\n\t\t\tstyle.cssName = this.processStyleName(style.id);\r\n\t\t}\r\n\r\n\t\treturn stylesMap;\r\n\t}\r\n\r\n\tprodessNumberings(numberings: IDomNumbering[]) {\r\n\t\tfor (let num of numberings.filter(n => n.pStyleName)) {\r\n\t\t\tconst style = this.findStyle(num.pStyleName);\r\n\r\n\t\t\tif (style?.paragraphProps?.numbering) {\r\n\t\t\t\tstyle.paragraphProps.numbering.level = num.level;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprocessElement(element: OpenXmlElement) {\r\n\t\tif (element.children) {\r\n\t\t\tfor (var e of element.children) {\r\n\t\t\t\te.parent = element;\r\n\r\n\t\t\t\tif (e.type == DomType.Table) {\r\n\t\t\t\t\tthis.processTable(e);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tthis.processElement(e);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprocessTable(table: WmlTable) {\r\n\t\tfor (var r of table.children) {\r\n\t\t\tfor (var c of r.children) {\r\n\t\t\t\tc.cssStyle = this.copyStyleProperties(table.cellStyle, c.cssStyle, [\r\n\t\t\t\t\t\"border-left\", \"border-right\", \"border-top\", \"border-bottom\",\r\n\t\t\t\t\t\"padding-left\", \"padding-right\", \"padding-top\", \"padding-bottom\"\r\n\t\t\t\t]);\r\n\r\n\t\t\t\tthis.processElement(c);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tcopyStyleProperties(input: Record<string, string>, output: Record<string, string>, attrs: string[] = null): Record<string, string> {\r\n\t\tif (!input)\r\n\t\t\treturn output;\r\n\r\n\t\tif (output == null) output = {};\r\n\t\tif (attrs == null) attrs = Object.getOwnPropertyNames(input);\r\n\r\n\t\tfor (var key of attrs) {\r\n\t\t\tif (input.hasOwnProperty(key) && !output.hasOwnProperty(key))\r\n\t\t\t\toutput[key] = input[key];\r\n\t\t}\r\n\r\n\t\treturn output;\r\n\t}\r\n\r\n\tcreateSection(className: string, props: SectionProperties) {\r\n\t\tvar elem = this.createElement(\"section\", { className });\r\n\r\n\t\tif (props) {\r\n\t\t\tif (props.pageMargins) {\r\n\t\t\t\telem.style.paddingLeft = props.pageMargins.left;\r\n\t\t\t\telem.style.paddingRight = props.pageMargins.right;\r\n\t\t\t\telem.style.paddingTop = props.pageMargins.top;\r\n\t\t\t\telem.style.paddingBottom = props.pageMargins.bottom;\r\n\t\t\t}\r\n\r\n\t\t\tif (props.pageSize) {\r\n\t\t\t\tif (!this.options.ignoreWidth)\r\n\t\t\t\t\telem.style.width = props.pageSize.width;\r\n\t\t\t\tif (!this.options.ignoreHeight)\r\n\t\t\t\t\telem.style.minHeight = props.pageSize.height;\r\n\t\t\t}\r\n\r\n\t\t\tif (props.columns && props.columns.numberOfColumns) {\r\n\t\t\t\telem.style.columnCount = `${props.columns.numberOfColumns}`;\r\n\t\t\t\telem.style.columnGap = props.columns.space;\r\n\r\n\t\t\t\tif (props.columns.separator) {\r\n\t\t\t\t\telem.style.columnRule = \"1px solid black\";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn elem;\r\n\t}\r\n\r\n\trenderSections(document: DocumentElement): HTMLElement[] {\r\n\t\tconst result = [];\r\n\r\n\t\tthis.processElement(document);\r\n\t\tconst sections = this.splitBySection(document.children);\r\n\t\tlet prevProps = null;\r\n\r\n\t\tfor (let i = 0, l = sections.length; i < l; i++) {\r\n\t\t\tthis.currentFootnoteIds = [];\r\n\r\n\t\t\tconst section = sections[i];\r\n\t\t\tconst props = section.sectProps || document.props;\r\n\t\t\tconst sectionElement = this.createSection(this.className, props);\r\n\t\t\tthis.renderStyleValues(document.cssStyle, sectionElement);\r\n\r\n\t\t\tthis.options.renderHeaders && this.renderHeaderFooter(props.headerRefs, props,\r\n\t\t\t\tresult.length, prevProps != props, sectionElement);\r\n\r\n\t\t\tvar contentElement = this.createElement(\"article\");\r\n\t\t\tthis.renderElements(section.elements, contentElement);\r\n\t\t\tsectionElement.appendChild(contentElement);\r\n\r\n\t\t\tif (this.options.renderFootnotes) {\r\n\t\t\t\tthis.renderNotes(this.currentFootnoteIds, this.footnoteMap, sectionElement);\r\n\t\t\t}\r\n\r\n\t\t\tif (this.options.renderEndnotes && i == l - 1) {\r\n\t\t\t\tthis.renderNotes(this.currentEndnoteIds, this.endnoteMap, sectionElement);\r\n\t\t\t}\r\n\r\n\t\t\tthis.options.renderFooters && this.renderHeaderFooter(props.footerRefs, props,\r\n\t\t\t\tresult.length, prevProps != props, sectionElement);\r\n\r\n\t\t\tresult.push(sectionElement);\r\n\t\t\tprevProps = props;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderHeaderFooter(refs: FooterHeaderReference[], props: SectionProperties, page: number, firstOfSection: boolean, into: HTMLElement) {\r\n\t\tif (!refs) return;\r\n\r\n\t\tvar ref = (props.titlePage && firstOfSection ? refs.find(x => x.type == \"first\") : null)\r\n\t\t\t?? (page % 2 == 1 ? refs.find(x => x.type == \"even\") : null)\r\n\t\t\t?? refs.find(x => x.type == \"default\");\r\n\r\n\t\tvar part = ref && this.document.findPartByRelId(ref.id, this.document.documentPart) as BaseHeaderFooterPart;\r\n\r\n\t\tif (part) {\r\n\t\t\tthis.currentPart = part;\r\n\t\t\tif (!this.usedHederFooterParts.includes(part.path)) {\r\n\t\t\t\tthis.processElement(part.rootElement);\r\n\t\t\t\tthis.usedHederFooterParts.push(part.path);\r\n\t\t\t}\r\n\t\t\tthis.renderElements([part.rootElement], into);\r\n\t\t\tthis.currentPart = null;\r\n\t\t}\r\n\t}\r\n\r\n\tisPageBreakElement(elem: OpenXmlElement): boolean {\r\n\t\tif (elem.type != DomType.Break)\r\n\t\t\treturn false;\r\n\r\n\t\tif ((elem as WmlBreak).break == \"lastRenderedPageBreak\")\r\n\t\t\treturn !this.options.ignoreLastRenderedPageBreak;\r\n\r\n\t\treturn (elem as WmlBreak).break == \"page\";\r\n\t}\r\n\r\n\tsplitBySection(elements: OpenXmlElement[]): { sectProps: SectionProperties, elements: OpenXmlElement[] }[] {\r\n\t\tvar current = { sectProps: null, elements: [] };\r\n\t\tvar result = [current];\r\n\r\n\t\tfor (let elem of elements) {\r\n\t\t\tif (elem.type == DomType.Paragraph) {\r\n\t\t\t\tconst s = this.findStyle((elem as WmlParagraph).styleName);\r\n\r\n\t\t\t\tif (s?.paragraphProps?.pageBreakBefore) {\r\n\t\t\t\t\tcurrent.sectProps = sectProps;\r\n\t\t\t\t\tcurrent = { sectProps: null, elements: [] };\r\n\t\t\t\t\tresult.push(current);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tcurrent.elements.push(elem);\r\n\r\n\t\t\tif (elem.type == DomType.Paragraph) {\r\n\t\t\t\tconst p = elem as WmlParagraph;\r\n\r\n\t\t\t\tvar sectProps = p.sectionProps;\r\n\t\t\t\tvar pBreakIndex = -1;\r\n\t\t\t\tvar rBreakIndex = -1;\r\n\r\n\t\t\t\tif (this.options.breakPages && p.children) {\r\n\t\t\t\t\tpBreakIndex = p.children.findIndex(r => {\r\n\t\t\t\t\t\trBreakIndex = r.children?.findIndex(this.isPageBreakElement.bind(this)) ?? -1;\r\n\t\t\t\t\t\treturn rBreakIndex != -1;\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (sectProps || pBreakIndex != -1) {\r\n\t\t\t\t\tcurrent.sectProps = sectProps;\r\n\t\t\t\t\tcurrent = { sectProps: null, elements: [] };\r\n\t\t\t\t\tresult.push(current);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (pBreakIndex != -1) {\r\n\t\t\t\t\tlet breakRun = p.children[pBreakIndex];\r\n\t\t\t\t\tlet splitRun = rBreakIndex < breakRun.children.length - 1;\r\n\r\n\t\t\t\t\tif (pBreakIndex < p.children.length - 1 || splitRun) {\r\n\t\t\t\t\t\tvar children = elem.children;\r\n\t\t\t\t\t\tvar newParagraph = { ...elem, children: children.slice(pBreakIndex) };\r\n\t\t\t\t\t\telem.children = children.slice(0, pBreakIndex);\r\n\t\t\t\t\t\tcurrent.elements.push(newParagraph);\r\n\r\n\t\t\t\t\t\tif (splitRun) {\r\n\t\t\t\t\t\t\tlet runChildren = breakRun.children;\r\n\t\t\t\t\t\t\tlet newRun = { ...breakRun, children: runChildren.slice(0, rBreakIndex) };\r\n\t\t\t\t\t\t\telem.children.push(newRun);\r\n\t\t\t\t\t\t\tbreakRun.children = runChildren.slice(rBreakIndex);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet currentSectProps = null;\r\n\r\n\t\tfor (let i = result.length - 1; i >= 0; i--) {\r\n\t\t\tif (result[i].sectProps == null) {\r\n\t\t\t\tresult[i].sectProps = currentSectProps;\r\n\t\t\t} else {\r\n\t\t\t\tcurrentSectProps = result[i].sectProps\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderWrapper(children: HTMLElement[]) {\r\n\t\treturn this.createElement(\"div\", { className: `${this.className}-wrapper` }, children);\r\n\t}\r\n\r\n\trenderDefaultStyle() {\r\n\t\tvar c = this.className;\r\n\t\tvar styleText = `\r\n.${c}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } \r\n.${c}-wrapper>section.${c} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }\r\n.${c} { color: black; hyphens: auto; }\r\nsection.${c} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }\r\nsection.${c}>article { margin-bottom: auto; z-index: 1; }\r\nsection.${c}>footer { z-index: 1; }\r\n.${c} table { border-collapse: collapse; }\r\n.${c} table td, .${c} table th { vertical-align: top; }\r\n.${c} p { margin: 0pt; min-height: 1em; }\r\n.${c} span { white-space: pre-wrap; overflow-wrap: break-word; }\r\n.${c} a { color: inherit; text-decoration: inherit; }\r\n`;\r\n\r\n\t\treturn createStyleElement(styleText);\r\n\t}\r\n\r\n\t// renderNumbering2(numberingPart: NumberingPartProperties, container: HTMLElement): HTMLElement {\r\n\t//     let css = \"\";\r\n\t//     const numberingMap = keyBy(numberingPart.abstractNumberings, x => x.id);\r\n\t//     const bulletMap = keyBy(numberingPart.bulletPictures, x => x.id);\r\n\t//     const topCounters = [];\r\n\r\n\t//     for(let num of numberingPart.numberings) {\r\n\t//         const absNum = numberingMap[num.abstractId];\r\n\r\n\t//         for(let lvl of absNum.levels) {\r\n\t//             const className = this.numberingClass(num.id, lvl.level);\r\n\t//             let listStyleType = \"none\";\r\n\r\n\t//             if(lvl.text && lvl.format == 'decimal') {\r\n\t//                 const counter = this.numberingCounter(num.id, lvl.level);\r\n\r\n\t//                 if (lvl.level > 0) {\r\n\t//                     css += this.styleToString(`p.${this.numberingClass(num.id, lvl.level - 1)}`, {\r\n\t//                         \"counter-reset\": counter\r\n\t//                     });\r\n\t//                 } else {\r\n\t//                     topCounters.push(counter);\r\n\t//                 }\r\n\r\n\t//                 css += this.styleToString(`p.${className}:before`, {\r\n\t//                     \"content\": this.levelTextToContent(lvl.text, num.id),\r\n\t//                     \"counter-increment\": counter\r\n\t//                 });\r\n\t//             } else if(lvl.bulletPictureId) {\r\n\t//                 let pict = bulletMap[lvl.bulletPictureId];\r\n\t//                 let variable = `--${this.className}-${pict.referenceId}`.toLowerCase();\r\n\r\n\t//                 css += this.styleToString(`p.${className}:before`, {\r\n\t//                     \"content\": \"' '\",\r\n\t//                     \"display\": \"inline-block\",\r\n\t//                     \"background\": `var(${variable})`\r\n\t//                 }, pict.style);\r\n\r\n\t//                 this.document.loadNumberingImage(pict.referenceId).then(data => {\r\n\t//                     var text = `.${this.className}-wrapper { ${variable}: url(${data}) }`;\r\n\t//                     container.appendChild(createStyleElement(text));\r\n\t//                 });\r\n\t//             } else {\r\n\t//                 listStyleType = this.numFormatToCssValue(lvl.format);\r\n\t//             }\r\n\r\n\t//             css += this.styleToString(`p.${className}`, {\r\n\t//                 \"display\": \"list-item\",\r\n\t//                 \"list-style-position\": \"inside\",\r\n\t//                 \"list-style-type\": listStyleType,\r\n\t//                 //TODO\r\n\t//                 //...num.style\r\n\t//             });\r\n\t//         }\r\n\t//     }\r\n\r\n\t//     if (topCounters.length > 0) {\r\n\t//         css += this.styleToString(`.${this.className}-wrapper`, {\r\n\t//             \"counter-reset\": topCounters.join(\" \")\r\n\t//         });\r\n\t//     }\r\n\r\n\t//     return createStyleElement(css);\r\n\t// }\r\n\r\n\trenderNumbering(numberings: IDomNumbering[], styleContainer: HTMLElement) {\r\n\t\tvar styleText = \"\";\r\n\t\tvar resetCounters = [];\r\n\r\n\t\tfor (var num of numberings) {\r\n\t\t\tvar selector = `p.${this.numberingClass(num.id, num.level)}`;\r\n\t\t\tvar listStyleType = \"none\";\r\n\r\n\t\t\tif (num.bullet) {\r\n\t\t\t\tlet valiable = `--${this.className}-${num.bullet.src}`.toLowerCase();\r\n\r\n\t\t\t\tstyleText += this.styleToString(`${selector}:before`, {\r\n\t\t\t\t\t\"content\": \"' '\",\r\n\t\t\t\t\t\"display\": \"inline-block\",\r\n\t\t\t\t\t\"background\": `var(${valiable})`\r\n\t\t\t\t}, num.bullet.style);\r\n\r\n\t\t\t\tthis.document.loadNumberingImage(num.bullet.src).then(data => {\r\n\t\t\t\t\tvar text = `${this.rootSelector} { ${valiable}: url(${data}) }`;\r\n\t\t\t\t\tstyleContainer.appendChild(createStyleElement(text));\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\telse if (num.levelText) {\r\n\t\t\t\tlet counter = this.numberingCounter(num.id, num.level);\r\n\t\t\t\tconst counterReset = counter + \" \" + (num.start - 1);\r\n\t\t\t\tif (num.level > 0) {\r\n\t\t\t\t\tstyleText += this.styleToString(`p.${this.numberingClass(num.id, num.level - 1)}`, {\r\n\t\t\t\t\t\t\"counter-reset\": counterReset\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// reset all level counters with start value\r\n\t\t\t\tresetCounters.push(counterReset);\r\n\r\n\t\t\t\tstyleText += this.styleToString(`${selector}:before`, {\r\n\t\t\t\t\t\"content\": this.levelTextToContent(num.levelText, num.suff, num.id, this.numFormatToCssValue(num.format)),\r\n\t\t\t\t\t\"counter-increment\": counter,\r\n\t\t\t\t\t...num.rStyle,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tlistStyleType = this.numFormatToCssValue(num.format);\r\n\t\t\t}\r\n\r\n\t\t\tstyleText += this.styleToString(selector, {\r\n\t\t\t\t\"display\": \"list-item\",\r\n\t\t\t\t\"list-style-position\": \"inside\",\r\n\t\t\t\t\"list-style-type\": listStyleType,\r\n\t\t\t\t...num.pStyle\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\tif (resetCounters.length > 0) {\r\n\t\t\tstyleText += this.styleToString(this.rootSelector, {\r\n\t\t\t\t\"counter-reset\": resetCounters.join(\" \")\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn createStyleElement(styleText);\r\n\t}\r\n\r\n\trenderStyles(styles: IDomStyle[]): HTMLElement {\r\n\t\tvar styleText = \"\";\r\n\t\tconst stylesMap = this.styleMap;\r\n\t\tconst defautStyles = keyBy(styles.filter(s => s.isDefault), s => s.target);\r\n\r\n\t\tfor (const style of styles) {\r\n\t\t\tvar subStyles = style.styles;\r\n\r\n\t\t\tif (style.linked) {\r\n\t\t\t\tvar linkedStyle = style.linked && stylesMap[style.linked];\r\n\r\n\t\t\t\tif (linkedStyle)\r\n\t\t\t\t\tsubStyles = subStyles.concat(linkedStyle.styles);\r\n\t\t\t\telse if (this.options.debug)\r\n\t\t\t\t\tconsole.warn(`Can't find linked style ${style.linked}`);\r\n\t\t\t}\r\n\r\n\t\t\tfor (const subStyle of subStyles) {\r\n\t\t\t\t//TODO temporary disable modificators until test it well\r\n\t\t\t\tvar selector = `${style.target ?? ''}.${style.cssName}`; //${subStyle.mod ?? ''} \r\n\r\n\t\t\t\tif (style.target != subStyle.target)\r\n\t\t\t\t\tselector += ` ${subStyle.target}`;\r\n\r\n\t\t\t\tif (defautStyles[style.target] == style)\r\n\t\t\t\t\tselector = `.${this.className} ${style.target}, ` + selector;\r\n\r\n\t\t\t\tstyleText += this.styleToString(selector, subStyle.values);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn createStyleElement(styleText);\r\n\t}\r\n\r\n\trenderNotes(noteIds: string[], notesMap: Record<string, WmlBaseNote>, into: HTMLElement) {\r\n\t\tvar notes = noteIds.map(id => notesMap[id]).filter(x => x);\r\n\r\n\t\tif (notes.length > 0) {\r\n\t\t\tvar result = this.createElement(\"ol\", null, this.renderElements(notes));\r\n\t\t\tinto.appendChild(result);\r\n\t\t}\r\n\t}\r\n\r\n\trenderElement(elem: OpenXmlElement): Node | Node[] {\r\n\t\tswitch (elem.type) {\r\n\t\t\tcase DomType.Paragraph:\r\n\t\t\t\treturn this.renderParagraph(elem as WmlParagraph);\r\n\r\n\t\t\tcase DomType.BookmarkStart:\r\n\t\t\t\treturn this.renderBookmarkStart(elem as WmlBookmarkStart);\r\n\r\n\t\t\tcase DomType.BookmarkEnd:\r\n\t\t\t\treturn null; //ignore bookmark end\r\n\r\n\t\t\tcase DomType.Run:\r\n\t\t\t\treturn this.renderRun(elem as WmlRun);\r\n\r\n\t\t\tcase DomType.Table:\r\n\t\t\t\treturn this.renderTable(elem);\r\n\r\n\t\t\tcase DomType.Row:\r\n\t\t\t\treturn this.renderTableRow(elem);\r\n\r\n\t\t\tcase DomType.Cell:\r\n\t\t\t\treturn this.renderTableCell(elem);\r\n\r\n\t\t\tcase DomType.Hyperlink:\r\n\t\t\t\treturn this.renderHyperlink(elem);\r\n\r\n\t\t\tcase DomType.Drawing:\r\n\t\t\t\treturn this.renderDrawing(elem);\r\n\r\n\t\t\tcase DomType.Image:\r\n\t\t\t\treturn this.renderImage(elem as IDomImage);\r\n\r\n\t\t\tcase DomType.Text:\r\n\t\t\t\treturn this.renderText(elem as WmlText);\r\n\r\n\t\t\tcase DomType.Text:\r\n\t\t\t\treturn this.renderText(elem as WmlText);\r\n\r\n\t\t\tcase DomType.DeletedText:\r\n\t\t\t\treturn this.renderDeletedText(elem as WmlText);\r\n\t\r\n\t\t\tcase DomType.Tab:\r\n\t\t\t\treturn this.renderTab(elem);\r\n\r\n\t\t\tcase DomType.Symbol:\r\n\t\t\t\treturn this.renderSymbol(elem as WmlSymbol);\r\n\r\n\t\t\tcase DomType.Break:\r\n\t\t\t\treturn this.renderBreak(elem as WmlBreak);\r\n\r\n\t\t\tcase DomType.Footer:\r\n\t\t\t\treturn this.renderContainer(elem, \"footer\");\r\n\r\n\t\t\tcase DomType.Header:\r\n\t\t\t\treturn this.renderContainer(elem, \"header\");\r\n\r\n\t\t\tcase DomType.Footnote:\r\n\t\t\tcase DomType.Endnote:\r\n\t\t\t\treturn this.renderContainer(elem, \"li\");\r\n\r\n\t\t\tcase DomType.FootnoteReference:\r\n\t\t\t\treturn this.renderFootnoteReference(elem as WmlNoteReference);\r\n\r\n\t\t\tcase DomType.EndnoteReference:\r\n\t\t\t\treturn this.renderEndnoteReference(elem as WmlNoteReference);\r\n\r\n\t\t\tcase DomType.NoBreakHyphen:\r\n\t\t\t\treturn this.createElement(\"wbr\");\r\n\r\n\t\t\tcase DomType.VmlPicture:\r\n\t\t\t\treturn this.renderVmlPicture(elem);\r\n\r\n\t\t\tcase DomType.VmlElement:\r\n\t\t\t\treturn this.renderVmlElement(elem as VmlElement);\r\n\t\r\n\t\t\tcase DomType.MmlMath:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"math\", { xmlns: ns.mathML });\r\n\t\r\n\t\t\tcase DomType.MmlMathParagraph:\r\n\t\t\t\treturn this.renderContainer(elem, \"span\");\r\n\r\n\t\t\tcase DomType.MmlFraction:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mfrac\");\r\n\r\n\t\t\tcase DomType.MmlBase:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \r\n\t\t\t\t\telem.parent.type == DomType.MmlMatrixRow ? \"mtd\" : \"mrow\");\r\n\r\n\t\t\tcase DomType.MmlNumerator:\r\n\t\t\tcase DomType.MmlDenominator:\r\n\t\t\tcase DomType.MmlFunction:\r\n\t\t\tcase DomType.MmlLimit:\r\n\t\t\tcase DomType.MmlBox:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mrow\");\r\n\r\n\t\t\tcase DomType.MmlGroupChar:\r\n\t\t\t\treturn this.renderMmlGroupChar(elem);\r\n\r\n\t\t\tcase DomType.MmlLimitLower:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"munder\");\r\n\r\n\t\t\tcase DomType.MmlMatrix:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mtable\");\r\n\r\n\t\t\tcase DomType.MmlMatrixRow:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mtr\");\r\n\t\r\n\t\t\tcase DomType.MmlRadical:\r\n\t\t\t\treturn this.renderMmlRadical(elem);\r\n\r\n\t\t\tcase DomType.MmlSuperscript:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"msup\");\r\n\r\n\t\t\tcase DomType.MmlSubscript:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"msub\");\r\n\r\n\t\t\tcase DomType.MmlDegree:\r\n\t\t\tcase DomType.MmlSuperArgument:\r\n\t\t\tcase DomType.MmlSubArgument:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mn\");\r\n\r\n\t\t\tcase DomType.MmlFunctionName:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"ms\");\r\n\t\r\n\t\t\tcase DomType.MmlDelimiter:\r\n\t\t\t\treturn this.renderMmlDelimiter(elem);\r\n\r\n\t\t\tcase DomType.MmlRun:\r\n\t\t\t\treturn this.renderMmlRun(elem);\r\n\r\n\t\t\tcase DomType.MmlNary:\r\n\t\t\t\treturn this.renderMmlNary(elem);\r\n\r\n\t\t\tcase DomType.MmlPreSubSuper:\r\n\t\t\t\treturn this.renderMmlPreSubSuper(elem);\r\n\r\n\t\t\tcase DomType.MmlBar:\r\n\t\t\t\treturn this.renderMmlBar(elem);\r\n\t\r\n\t\t\tcase DomType.MmlEquationArray:\r\n\t\t\t\treturn this.renderMllList(elem);\r\n\r\n\t\t\tcase DomType.Inserted:\r\n\t\t\t\treturn this.renderInserted(elem);\r\n\r\n\t\t\tcase DomType.Deleted:\r\n\t\t\t\treturn this.renderDeleted(elem);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\trenderChildren(elem: OpenXmlElement, into?: Element): Node[] {\r\n\t\treturn this.renderElements(elem.children, into);\r\n\t}\r\n\r\n\trenderElements(elems: OpenXmlElement[], into?: Element): Node[] {\r\n\t\tif (elems == null)\r\n\t\t\treturn null;\r\n\r\n\t\tvar result = elems.flatMap(e => this.renderElement(e)).filter(e => e != null);\r\n\r\n\t\tif (into)\r\n\t\t\tappendChildren(into, result);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderContainer(elem: OpenXmlElement, tagName: keyof HTMLElementTagNameMap, props?: Record<string, any>) {\r\n\t\treturn this.createElement(tagName, props, this.renderChildren(elem));\r\n\t}\r\n\r\n\trenderContainerNS(elem: OpenXmlElement, ns: string, tagName: string, props?: Record<string, any>) {\r\n\t\treturn createElementNS(ns, tagName, props, this.renderChildren(elem));\r\n\t}\r\n\r\n\trenderParagraph(elem: WmlParagraph) {\r\n\t\tvar result = this.createElement(\"p\");\r\n\r\n\t\tconst style = this.findStyle(elem.styleName);\r\n\t\telem.tabs ??= style?.paragraphProps?.tabs;  //TODO\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\t\tthis.renderCommonProperties(result.style, elem);\r\n\r\n\t\tconst numbering = elem.numbering ?? style?.paragraphProps?.numbering;\r\n\r\n\t\tif (numbering) {\r\n\t\t\tresult.classList.add(this.numberingClass(numbering.id, numbering.level));\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderRunProperties(style: any, props: RunProperties) {\r\n\t\tthis.renderCommonProperties(style, props);\r\n\t}\r\n\r\n\trenderCommonProperties(style: any, props: CommonProperties) {\r\n\t\tif (props == null)\r\n\t\t\treturn;\r\n\r\n\t\tif (props.color) {\r\n\t\t\tstyle[\"color\"] = props.color;\r\n\t\t}\r\n\r\n\t\tif (props.fontSize) {\r\n\t\t\tstyle[\"font-size\"] = props.fontSize;\r\n\t\t}\r\n\t}\r\n\r\n\trenderHyperlink(elem: WmlHyperlink) {\r\n\t\tvar result = this.createElement(\"a\");\r\n\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (elem.href) {\r\n\t\t\tresult.href = elem.href;\r\n\t\t} else if(elem.id) {\r\n\t\t\tconst rel = this.document.documentPart.rels\r\n\t\t\t\t.find(it => it.id == elem.id && it.targetMode === \"External\");\r\n\t\t\tresult.href = rel?.target;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderDrawing(elem: OpenXmlElement) {\r\n\t\tvar result = this.createElement(\"div\");\r\n\r\n\t\tresult.style.display = \"inline-block\";\r\n\t\tresult.style.position = \"relative\";\r\n\t\tresult.style.textIndent = \"0px\";\r\n\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderImage(elem: IDomImage) {\r\n\t\tlet result = this.createElement(\"img\");\r\n\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (this.document) {\r\n\t\t\tthis.document.loadDocumentImage(elem.src, this.currentPart).then(x => {\r\n\t\t\t\tresult.src = x;\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderText(elem: WmlText) {\r\n\t\treturn this.htmlDocument.createTextNode(elem.text);\r\n\t}\r\n\r\n\trenderDeletedText(elem: WmlText) {\r\n\t\treturn this.options.renderEndnotes ? this.htmlDocument.createTextNode(elem.text) : null;\r\n\t}\r\n\r\n\trenderBreak(elem: WmlBreak) {\r\n\t\tif (elem.break == \"textWrapping\") {\r\n\t\t\treturn this.createElement(\"br\");\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\trenderInserted(elem: OpenXmlElement): Node | Node[] {\r\n\t\tif (this.options.renderChanges)\r\n\t\t\treturn this.renderContainer(elem, \"ins\");\r\n\r\n\t\treturn this.renderChildren(elem);\r\n\t}\r\n\r\n\trenderDeleted(elem: OpenXmlElement): Node {\r\n\t\tif (this.options.renderChanges)\r\n\t\t\treturn this.renderContainer(elem, \"del\");\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\trenderSymbol(elem: WmlSymbol) {\r\n\t\tvar span = this.createElement(\"span\");\r\n\t\tspan.style.fontFamily = elem.font;\r\n\t\tspan.innerHTML = `&#x${elem.char};`\r\n\t\treturn span;\r\n\t}\r\n\r\n\trenderFootnoteReference(elem: WmlNoteReference) {\r\n\t\tvar result = this.createElement(\"sup\");\r\n\t\tthis.currentFootnoteIds.push(elem.id);\r\n\t\tresult.textContent = `${this.currentFootnoteIds.length}`;\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderEndnoteReference(elem: WmlNoteReference) {\r\n\t\tvar result = this.createElement(\"sup\");\r\n\t\tthis.currentEndnoteIds.push(elem.id);\r\n\t\tresult.textContent = `${this.currentEndnoteIds.length}`;\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTab(elem: OpenXmlElement) {\r\n\t\tvar tabSpan = this.createElement(\"span\");\r\n\r\n\t\ttabSpan.innerHTML = \"&emsp;\";//\"&nbsp;\";\r\n\r\n\t\tif (this.options.experimental) {\r\n\t\t\ttabSpan.className = this.tabStopClass();\r\n\t\t\tvar stops = findParent<WmlParagraph>(elem, DomType.Paragraph)?.tabs;\r\n\t\t\tthis.currentTabs.push({ stops, span: tabSpan });\r\n\t\t}\r\n\r\n\t\treturn tabSpan;\r\n\t}\r\n\r\n\trenderBookmarkStart(elem: WmlBookmarkStart): HTMLElement {\r\n\t\tvar result = this.createElement(\"span\");\r\n\t\tresult.id = elem.name;\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderRun(elem: WmlRun) {\r\n\t\tif (elem.fieldRun)\r\n\t\t\treturn null;\r\n\r\n\t\tconst result = this.createElement(\"span\");\r\n\r\n\t\tif (elem.id)\r\n\t\t\tresult.id = elem.id;\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (elem.verticalAlign) {\r\n\t\t\tconst wrapper = this.createElement(elem.verticalAlign as any);\r\n\t\t\tthis.renderChildren(elem, wrapper);\r\n\t\t\tresult.appendChild(wrapper);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.renderChildren(elem, result);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTable(elem: WmlTable) {\r\n\t\tlet result = this.createElement(\"table\");\r\n\r\n\t\tthis.tableCellPositions.push(this.currentCellPosition);\r\n\t\tthis.tableVerticalMerges.push(this.currentVerticalMerge);\r\n\t\tthis.currentVerticalMerge = {};\r\n\t\tthis.currentCellPosition = { col: 0, row: 0 };\r\n\r\n\t\tif (elem.columns)\r\n\t\t\tresult.appendChild(this.renderTableColumns(elem.columns));\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tthis.currentVerticalMerge = this.tableVerticalMerges.pop();\r\n\t\tthis.currentCellPosition = this.tableCellPositions.pop();\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTableColumns(columns: WmlTableColumn[]) {\r\n\t\tlet result = this.createElement(\"colgroup\");\r\n\r\n\t\tfor (let col of columns) {\r\n\t\t\tlet colElem = this.createElement(\"col\");\r\n\r\n\t\t\tif (col.width)\r\n\t\t\t\tcolElem.style.width = col.width;\r\n\r\n\t\t\tresult.appendChild(colElem);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTableRow(elem: OpenXmlElement) {\r\n\t\tlet result = this.createElement(\"tr\");\r\n\r\n\t\tthis.currentCellPosition.col = 0;\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tthis.currentCellPosition.row++;\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTableCell(elem: WmlTableCell) {\r\n\t\tlet result = this.createElement(\"td\");\r\n\r\n\t\tconst key = this.currentCellPosition.col;\r\n\r\n\t\tif (elem.verticalMerge) {\r\n\t\t\tif (elem.verticalMerge == \"restart\") {\r\n\t\t\t\tthis.currentVerticalMerge[key] = result;\r\n\t\t\t\tresult.rowSpan = 1;\r\n\t\t\t} else if (this.currentVerticalMerge[key]) {\r\n\t\t\t\tthis.currentVerticalMerge[key].rowSpan += 1;\r\n\t\t\t\tresult.style.display = \"none\";\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.currentVerticalMerge[key] = null;\r\n\t\t}\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (elem.span)\r\n\t\t\tresult.colSpan = elem.span;\r\n\r\n\t\tthis.currentCellPosition.col += result.colSpan;\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderVmlPicture(elem: OpenXmlElement) {\r\n\t\tvar result = createElement(\"div\");\r\n\t\tthis.renderChildren(elem, result);\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderVmlElement(elem: VmlElement): SVGElement {\r\n\t\tvar container = createSvgElement(\"svg\");\r\n\r\n\t\tcontainer.setAttribute(\"style\", elem.cssStyleText);\r\n\r\n\t\tconst result = this.renderVmlChildElement(elem);\r\n\r\n\t\tif (elem.imageHref?.id) {\r\n\t\t\tthis.document?.loadDocumentImage(elem.imageHref.id, this.currentPart)\r\n\t\t\t\t.then(x => result.setAttribute(\"href\", x));\r\n\t\t}\r\n\r\n\t\tcontainer.appendChild(result);\r\n\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst bb = (container.firstElementChild as any).getBBox();\r\n\r\n\t\t\tcontainer.setAttribute(\"width\", `${Math.ceil(bb.x +  bb.width)}`);\r\n\t\t\tcontainer.setAttribute(\"height\", `${Math.ceil(bb.y + bb.height)}`);\r\n\t\t});\r\n\r\n\t\treturn container;\r\n\t}\r\n\r\n\trenderVmlChildElement(elem: VmlElement): any {\r\n\t\tconst result = createSvgElement(elem.tagName as any);\r\n\t\tObject.entries(elem.attrs).forEach(([k, v]) => result.setAttribute(k, v));\r\n\r\n\t\tfor (let child of elem.children) {\r\n\t\t\tif (child.type == DomType.VmlElement) {\r\n\t\t\t\tresult.appendChild(this.renderVmlChildElement(child as VmlElement));\r\n\t\t\t} else {\r\n\t\t\t\tresult.appendChild(...asArray(this.renderElement(child as any)));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMmlRadical(elem: OpenXmlElement): HTMLElement {\r\n\t\tconst base = elem.children.find(el => el.type == DomType.MmlBase);\r\n\r\n\t\tif (elem.props?.hideDegree) {\r\n\t\t\treturn createElementNS(ns.mathML, \"msqrt\", null, this.renderElements([base]));\r\n\t\t}\r\n\r\n\t\tconst degree = elem.children.find(el => el.type == DomType.MmlDegree);\r\n\t\treturn createElementNS(ns.mathML, \"mroot\", null, this.renderElements([base, degree]));\r\n\t}\r\n\r\n\trenderMmlDelimiter(elem: OpenXmlElement): HTMLElement {\t\t\r\n\t\tconst children = [];\r\n\r\n\t\tchildren.push(createElementNS(ns.mathML, \"mo\", null, [elem.props.beginChar ?? '(']));\r\n\t\tchildren.push(...this.renderElements(elem.children));\r\n\t\tchildren.push(createElementNS(ns.mathML, \"mo\", null, [elem.props.endChar ?? ')']));\r\n\r\n\t\treturn createElementNS(ns.mathML, \"mrow\", null, children);\r\n\t}\r\n\r\n\trenderMmlNary(elem: OpenXmlElement): HTMLElement {\t\t\r\n\t\tconst children = [];\r\n\t\tconst grouped = keyBy(elem.children, x => x.type);\r\n\r\n\t\tconst sup = grouped[DomType.MmlSuperArgument];\r\n\t\tconst sub = grouped[DomType.MmlSubArgument];\r\n\t\tconst supElem = sup ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sup))) : null;\r\n\t\tconst subElem = sub ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sub))) : null;\r\n\r\n\t\tconst charElem = createElementNS(ns.mathML, \"mo\", null, [elem.props?.char ?? '\\u222B']);\r\n\r\n\t\tif (supElem || subElem) {\r\n\t\t\tchildren.push(createElementNS(ns.mathML, \"munderover\", null, [charElem, subElem, supElem]));\r\n\t\t} else if(supElem) {\r\n\t\t\tchildren.push(createElementNS(ns.mathML, \"mover\", null, [charElem, supElem]));\r\n\t\t} else if(subElem) {\r\n\t\t\tchildren.push(createElementNS(ns.mathML, \"munder\", null, [charElem, subElem]));\r\n\t\t} else {\r\n\t\t\tchildren.push(charElem);\r\n\t\t}\r\n\r\n\t\tchildren.push(...this.renderElements(grouped[DomType.MmlBase].children));\r\n\r\n\t\treturn createElementNS(ns.mathML, \"mrow\", null, children);\r\n\t}\r\n\r\n\trenderMmlPreSubSuper(elem: OpenXmlElement) {\r\n\t\tconst children = [];\r\n\t\tconst grouped = keyBy(elem.children, x => x.type);\r\n\r\n\t\tconst sup = grouped[DomType.MmlSuperArgument];\r\n\t\tconst sub = grouped[DomType.MmlSubArgument];\r\n\t\tconst supElem = sup ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sup))) : null;\r\n\t\tconst subElem = sub ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sub))) : null;\r\n\t\tconst stubElem = createElementNS(ns.mathML, \"mo\", null);\r\n\r\n\t\tchildren.push(createElementNS(ns.mathML, \"msubsup\", null, [stubElem, subElem, supElem]));\r\n\t\tchildren.push(...this.renderElements(grouped[DomType.MmlBase].children));\r\n\r\n\t\treturn createElementNS(ns.mathML, \"mrow\", null, children);\r\n\t}\r\n\r\n\trenderMmlGroupChar(elem: OpenXmlElement) {\r\n\t\tconst tagName = elem.props.verticalJustification === \"bot\" ? \"mover\" : \"munder\";\r\n\t\tconst result = this.renderContainerNS(elem, ns.mathML, tagName);\r\n\r\n\t\tif (elem.props.char) {\r\n\t\t\tresult.appendChild(createElementNS(ns.mathML, \"mo\", null, [elem.props.char]));\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMmlBar(elem: OpenXmlElement) {\r\n\t\tconst result = this.renderContainerNS(elem, ns.mathML, \"mrow\");\r\n\r\n\t\tswitch(elem.props.position) {\r\n\t\t\tcase \"top\": result.style.textDecoration = \"overline\"; break\r\n\t\t\tcase \"bottom\": result.style.textDecoration = \"underline\"; break\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMmlRun(elem: OpenXmlElement) {\r\n\t\tconst result = createElementNS(ns.mathML, \"ms\");\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMllList(elem: OpenXmlElement) {\r\n\t\tconst result = createElementNS(ns.mathML, \"mtable\");\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tconst childern = this.renderChildren(elem);\r\n\r\n\t\tfor (let child of this.renderChildren(elem)) {\r\n\t\t\tresult.appendChild(createElementNS(ns.mathML, \"mtr\", null, [\r\n\t\t\t\tcreateElementNS(ns.mathML, \"mtd\", null, [child])\r\n\t\t\t]));\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\r\n\trenderStyleValues(style: Record<string, string>, ouput: HTMLElement) {\r\n\t\tfor (let k in style) {\r\n\t\t\tif (k.startsWith(\"$\")) {\r\n\t\t\t\touput.setAttribute(k.slice(1), style[k]);\r\n\t\t\t} else {\r\n\t\t\t\touput.style[k] = style[k];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\trenderClass(input: OpenXmlElement, ouput: HTMLElement) {\r\n\t\tif (input.className)\r\n\t\t\touput.className = input.className;\r\n\r\n\t\tif (input.styleName)\r\n\t\t\touput.classList.add(this.processStyleName(input.styleName));\r\n\t}\r\n\r\n\tfindStyle(styleName: string) {\r\n\t\treturn styleName && this.styleMap?.[styleName];\r\n\t}\r\n\r\n\tnumberingClass(id: string, lvl: number) {\r\n\t\treturn `${this.className}-num-${id}-${lvl}`;\r\n\t}\r\n\r\n\ttabStopClass() {\r\n\t\treturn `${this.className}-tab-stop`;\r\n\t}\r\n\r\n\tstyleToString(selectors: string, values: Record<string, string>, cssText: string = null) {\r\n\t\tlet result = `${selectors} {\\r\\n`;\r\n\r\n\t\tfor (const key in values) {\r\n\t\t\tif (key.startsWith('$'))\r\n\t\t\t\tcontinue;\r\n\t\t\t\r\n\t\t\tresult += `  ${key}: ${values[key]};\\r\\n`;\r\n\t\t}\r\n\r\n\t\tif (cssText)\r\n\t\t\tresult += cssText;\r\n\r\n\t\treturn result + \"}\\r\\n\";\r\n\t}\r\n\r\n\tnumberingCounter(id: string, lvl: number) {\r\n\t\treturn `${this.className}-num-${id}-${lvl}`;\r\n\t}\r\n\r\n\tlevelTextToContent(text: string, suff: string, id: string, numformat: string) {\r\n\t\tconst suffMap = {\r\n\t\t\t\"tab\": \"\\\\9\",\r\n\t\t\t\"space\": \"\\\\a0\",\r\n\t\t};\r\n\r\n\t\tvar result = text.replace(/%\\d*/g, s => {\r\n\t\t\tlet lvl = parseInt(s.substring(1), 10) - 1;\r\n\t\t\treturn `\"counter(${this.numberingCounter(id, lvl)}, ${numformat})\"`;\r\n\t\t});\r\n\r\n\t\treturn `\"${result}${suffMap[suff] ?? \"\"}\"`;\r\n\t}\r\n\r\n\tnumFormatToCssValue(format: string) {\r\n\t\tvar mapping = {\r\n\t\t\tnone: \"none\",\r\n\t\t\tbullet: \"disc\",\r\n\t\t\tdecimal: \"decimal\",\r\n\t\t\tlowerLetter: \"lower-alpha\",\r\n\t\t\tupperLetter: \"upper-alpha\",\r\n\t\t\tlowerRoman: \"lower-roman\",\r\n\t\t\tupperRoman: \"upper-roman\",\r\n\t\t\tdecimalZero: \"decimal-leading-zero\", // 01,02,03,...\r\n\t\t\t// ordinal: \"\", // 1st, 2nd, 3rd,...\r\n\t\t\t// ordinalText: \"\", //First, Second, Third, ...\r\n\t\t\t// cardinalText: \"\", //One,Two Three,...\r\n\t\t\t// numberInDash: \"\", //-1-,-2-,-3-, ...\r\n\t\t\t// hex: \"upper-hexadecimal\",\r\n\t\t\taiueo: \"katakana\",\r\n\t\t\taiueoFullWidth: \"katakana\",\r\n\t\t\tchineseCounting: \"simp-chinese-informal\",\r\n\t\t\tchineseCountingThousand: \"simp-chinese-informal\",\r\n\t\t\tchineseLegalSimplified: \"simp-chinese-formal\", // 中文大写\r\n\t\t\tchosung: \"hangul-consonant\",\r\n\t\t\tideographDigital: \"cjk-ideographic\",\r\n\t\t\tideographTraditional: \"cjk-heavenly-stem\", // 十天干\r\n\t\t\tideographLegalTraditional: \"trad-chinese-formal\",\r\n\t\t\tideographZodiac: \"cjk-earthly-branch\", // 十二地支\r\n\t\t\tiroha: \"katakana-iroha\",\r\n\t\t\tirohaFullWidth: \"katakana-iroha\",\r\n\t\t\tjapaneseCounting: \"japanese-informal\",\r\n\t\t\tjapaneseDigitalTenThousand: \"cjk-decimal\",\r\n\t\t\tjapaneseLegal: \"japanese-formal\",\r\n\t\t\tthaiNumbers: \"thai\",\r\n\t\t\tkoreanCounting: \"korean-hangul-formal\",\r\n\t\t\tkoreanDigital: \"korean-hangul-formal\",\r\n\t\t\tkoreanDigital2: \"korean-hanja-informal\",\r\n\t\t\thebrew1: \"hebrew\",\r\n\t\t\thebrew2: \"hebrew\",\r\n\t\t\thindiNumbers: \"devanagari\",\r\n\t\t\tganada: \"hangul\",\r\n\t\t\ttaiwaneseCounting: \"cjk-ideographic\",\r\n\t\t\ttaiwaneseCountingThousand: \"cjk-ideographic\",\r\n\t\t\ttaiwaneseDigital:  \"cjk-decimal\",\r\n\t\t};\r\n\r\n\t\treturn mapping[format] ?? format;\r\n\t}\r\n\r\n\trefreshTabStops() {\r\n\t\tif (!this.options.experimental)\r\n\t\t\treturn;\r\n\r\n\t\tclearTimeout(this.tabsTimeout);\r\n\r\n\t\tthis.tabsTimeout = setTimeout(() => {\r\n\t\t\tconst pixelToPoint = computePixelToPoint();\r\n\r\n\t\t\tfor (let tab of this.currentTabs) {\r\n\t\t\t\tupdateTabStop(tab.span, tab.stops, this.defaultTabSize, pixelToPoint);\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tcreateElement = createElement;\r\n}\r\n\r\ntype ChildType = Node | string;\r\n\r\nfunction createElement<T extends keyof HTMLElementTagNameMap>(\r\n\ttagName: T,\r\n\tprops?: Partial<Record<keyof HTMLElementTagNameMap[T], any>>,\r\n\tchildren?: ChildType[]\r\n): HTMLElementTagNameMap[T] {\r\n\treturn createElementNS(undefined, tagName, props, children);\r\n}\r\n\r\nfunction createSvgElement<T extends keyof SVGElementTagNameMap>(\r\n\ttagName: T,\r\n\tprops?: Partial<Record<keyof SVGElementTagNameMap[T], any>>,\r\n\tchildren?: ChildType[]\r\n): SVGElementTagNameMap[T] {\r\n\treturn createElementNS(ns.svg, tagName, props, children);\r\n}\r\n\r\nfunction createElementNS(ns: string, tagName: string, props?: Partial<Record<any, any>>, children?: ChildType[]): any {\r\n\tvar result = ns ? document.createElementNS(ns, tagName) : document.createElement(tagName);\r\n\tObject.assign(result, props);\r\n\tchildren && appendChildren(result, children);\r\n\treturn result;\r\n}\r\n\r\nfunction removeAllElements(elem: HTMLElement) {\r\n\telem.innerHTML = '';\r\n}\r\n\r\nfunction appendChildren(elem: Element, children: (Node | string)[]) {\r\n\tchildren.forEach(c => elem.appendChild(isString(c) ? document.createTextNode(c) : c));\r\n}\r\n\r\nfunction createStyleElement(cssText: string) {\r\n\treturn createElement(\"style\", { innerHTML: cssText });\r\n}\r\n\r\nfunction appendComment(elem: HTMLElement, comment: string) {\r\n\telem.appendChild(document.createComment(comment));\r\n}\r\n\r\nfunction findParent<T extends OpenXmlElement>(elem: OpenXmlElement, type: DomType): T {\r\n\tvar parent = elem.parent;\r\n\r\n\twhile (parent != null && parent.type != type)\r\n\t\tparent = parent.parent;\r\n\r\n\treturn <T>parent;\r\n}\r\n", "import { Length } from \"./document/common\";\r\nimport { ParagraphTab } from \"./document/paragraph\";\r\n\r\ninterface TabStop {\r\n\tpos: number;\r\n\tleader: string;\r\n\tstyle: string;\r\n}\r\n\r\nconst defaultTab: TabStop = { pos: 0, leader: \"none\", style: \"left\" };\r\nconst maxTabs = 50;\r\n\r\nexport function computePixelToPoint(container: HTMLElement = document.body) {\r\n\tconst temp = document.createElement(\"div\");\r\n\ttemp.style.width = '100pt';\r\n\t\r\n\tcontainer.appendChild(temp);\r\n\tconst result = 100 / temp.offsetWidth;\r\n\tcontainer.removeChild(temp);\r\n\r\n\treturn result\r\n}\r\n\r\nexport function updateTabStop(elem: HTMLElement, tabs: ParagraphTab[], defaultTabSize: Length, pixelToPoint: number = 72 / 96) {\r\n    const p = elem.closest(\"p\");\r\n\r\n    const ebb = elem.getBoundingClientRect();\r\n    const pbb = p.getBoundingClientRect();\r\n    const pcs = getComputedStyle(p);\r\n\r\n\tconst tabStops = tabs?.length > 0 ? tabs.map(t => ({\r\n\t\tpos: lengthToPoint(t.position),\r\n\t\tleader: t.leader,\r\n\t\tstyle: t.style\r\n\t})).sort((a, b) => a.pos - b.pos) : [defaultTab];\r\n\r\n\tconst lastTab = tabStops[tabStops.length - 1];\r\n\tconst pWidthPt = pbb.width * pixelToPoint;\r\n\tconst size = lengthToPoint(defaultTabSize);\r\n    let pos = lastTab.pos + size;\r\n\r\n    if (pos < pWidthPt) {\r\n        for (; pos < pWidthPt && tabStops.length < maxTabs; pos += size) {\r\n            tabStops.push({ ...defaultTab, pos: pos });\r\n        }\r\n    }\r\n\r\n    const marginLeft = parseFloat(pcs.marginLeft);\r\n    const pOffset = pbb.left + marginLeft;\r\n    const left = (ebb.left - pOffset) * pixelToPoint;\r\n    const tab = tabStops.find(t => t.style != \"clear\" && t.pos > left);\r\n\r\n    if(tab == null)\r\n        return;\r\n\r\n    let width: number = 1;\r\n\r\n    if (tab.style == \"right\" || tab.style == \"center\") {\r\n\t\tconst tabStops = Array.from(p.querySelectorAll(`.${elem.className}`));\r\n\t\tconst nextIdx = tabStops.indexOf(elem) + 1;\r\n        const range = document.createRange();\r\n        range.setStart(elem, 1);\r\n\r\n\t\tif (nextIdx < tabStops.length) {\r\n\t\t\trange.setEndBefore(tabStops[nextIdx]);\r\n\t\t} else {\r\n\t\t\trange.setEndAfter(p);\r\n\t\t}\r\n\r\n\t\tconst mul = tab.style == \"center\" ? 0.5 : 1;\r\n        const nextBB = range.getBoundingClientRect();\r\n\t\tconst offset = nextBB.left + mul * nextBB.width - (pbb.left - marginLeft);\r\n\r\n\t\twidth = tab.pos - offset * pixelToPoint;\r\n    } else {\r\n        width = tab.pos - left;\r\n    }\r\n\r\n    elem.innerHTML = \"&nbsp;\";\r\n    elem.style.textDecoration = \"inherit\";\r\n    elem.style.wordSpacing = `${width.toFixed(0)}pt`;\r\n\r\n    switch (tab.leader) {\r\n        case \"dot\":\r\n        case \"middleDot\":\r\n            elem.style.textDecoration = \"underline\";\r\n            elem.style.textDecorationStyle = \"dotted\";\r\n            break;\r\n\r\n        case \"hyphen\":\r\n        case \"heavy\":\r\n        case \"underscore\":\r\n            elem.style.textDecoration = \"underline\";\r\n            break;\r\n    }\r\n}\r\n\r\nfunction lengthToPoint(length: Length): number {\r\n\treturn parseFloat(length);\r\n}", "import { OpenXmlElementBase, DomType } from \"../document/dom\";\r\n\r\nexport abstract class WmlBaseNote implements OpenXmlElementBase {\r\n    type: DomType;\r\n    id: string;\r\n\tnoteType: string;\r\n}\r\n\r\nexport class WmlFootnote extends WmlBaseNote {\r\n\ttype = DomType.Footnote\r\n}\r\n\r\nexport class WmlEndnote extends WmlBaseNote {\r\n\ttype = DomType.Endnote\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { WmlBaseNote, WmlEndnote, WmlFootnote } from \"./elements\";\r\n\r\nexport class BaseNotePart<T extends WmlBaseNote> extends Part {\r\n    protected _documentParser: DocumentParser;\r\n\r\n    notes: T[]\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n}\r\n\r\nexport class FootnotesPart extends BaseNotePart<WmlFootnote> {\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path, parser);\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.notes = this._documentParser.parseNotes(root, \"footnote\", WmlFootnote);\r\n    }\r\n}\r\n\r\nexport class EndnotesPart extends BaseNotePart<WmlEndnote> {\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path, parser);\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.notes = this._documentParser.parseNotes(root, \"endnote\", WmlEndnote);\r\n    }\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { IDomNumbering } from \"../document/dom\";\r\nimport { AbstractNumbering, Numbering, NumberingBulletPicture, NumberingPartProperties, parseNumberingPart } from \"./numbering\";\r\n\r\nexport class NumberingPart extends Part implements NumberingPartProperties {\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n\r\n    numberings: Numbering[];\r\n    abstractNumberings: AbstractNumbering[];\r\n    bulletPictures: NumberingBulletPicture[];\r\n    \r\n    domNumberings: IDomNumbering[];\r\n\r\n    parseXml(root: Element) {\r\n        Object.assign(this, parseNumberingPart(root, this._package.xmlParser));\r\n        this.domNumberings = this._documentParser.parseNumberingFile(root);  \r\n    }\r\n}", "import { NumberingPicBullet } from \"../document/dom\";\r\nimport { ParagraphProperties, parseParagraphProperties } from \"../document/paragraph\";\r\nimport { parseRunProperties, RunProperties } from \"../document/run\";\r\nimport { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface NumberingPartProperties {\r\n    numberings: Numbering[];\r\n    abstractNumberings: AbstractNumbering[];\r\n    bulletPictures: NumberingBulletPicture[];\r\n}\r\n\r\nexport interface Numbering {\r\n    id: string;\r\n    abstractId: string;\r\n    overrides: NumberingLevelOverride[];\r\n}\r\n\r\nexport interface NumberingLevelOverride {\r\n    level: number;\r\n    start: number;\r\n    numberingLevel: NumberingLevel;\r\n}\r\n\r\nexport interface AbstractNumbering {\r\n    id: string;\r\n    name: string;\r\n    multiLevelType: \"singleLevel\" | \"multiLevel\" | \"hybridMultilevel\" | string;\r\n    levels: NumberingLevel[];\r\n    numberingStyleLink: string;\r\n    styleLink: string;\r\n}\r\n\r\nexport interface NumberingLevel {\r\n    level: number;\r\n    start: string;\r\n    restart: number;\r\n    format: 'lowerRoman' | 'lowerLetter' | string;\r\n    text: string;\r\n    justification: string;\r\n    bulletPictureId: string;\r\n    paragraphStyle: string;\r\n    paragraphProps: ParagraphProperties;\r\n    runProps: RunProperties;\r\n}\r\n\r\nexport interface NumberingBulletPicture {\r\n    id: string;\r\n    referenceId: string;\r\n    style: string;\r\n}\r\n\r\nexport function parseNumberingPart(elem: Element, xml: XmlParser): NumberingPartProperties {\r\n    let result: NumberingPartProperties = {\r\n        numberings: [],\r\n        abstractNumberings: [],\r\n        bulletPictures: []\r\n    }\r\n    \r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"num\":\r\n                result.numberings.push(parseNumbering(e, xml));\r\n                break;\r\n            case \"abstractNum\":\r\n                result.abstractNumberings.push(parseAbstractNumbering(e, xml));\r\n                break;\r\n            case \"numPicBullet\":\r\n                result.bulletPictures.push(parseNumberingBulletPicture(e, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumbering(elem: Element, xml: XmlParser): Numbering {\r\n    let result = <Numbering>{\r\n        id: xml.attr(elem, 'numId'),\r\n        overrides: []\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"abstractNumId\":\r\n                result.abstractId = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlOverride\":\r\n                result.overrides.push(parseNumberingLevelOverrride(e, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseAbstractNumbering(elem: Element, xml: XmlParser): AbstractNumbering {\r\n    let result = <AbstractNumbering>{\r\n        id: xml.attr(elem, 'abstractNumId'),\r\n        levels: []\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"name\":\r\n                result.name = xml.attr(e, \"val\");\r\n                break;\r\n            case \"multiLevelType\":\r\n                result.multiLevelType = xml.attr(e, \"val\");\r\n                break;\r\n            case \"numStyleLink\":\r\n                result.numberingStyleLink = xml.attr(e, \"val\");\r\n                break;\r\n            case \"styleLink\":\r\n                result.styleLink = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvl\":\r\n                result.levels.push(parseNumberingLevel(e, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumberingLevel(elem: Element, xml: XmlParser): NumberingLevel {\r\n    let result = <NumberingLevel>{\r\n        level: xml.intAttr(elem, 'ilvl')\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"start\":\r\n                result.start = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlRestart\":\r\n                result.restart = xml.intAttr(e, \"val\");\r\n                break;\r\n            case \"numFmt\":\r\n                result.format = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlText\":\r\n                result.text = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlJc\":\r\n                result.justification = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlPicBulletId\":\r\n                result.bulletPictureId = xml.attr(e, \"val\");\r\n                break;\r\n            case \"pStyle\":\r\n                result.paragraphStyle = xml.attr(e, \"val\");\r\n                break;\r\n            case \"pPr\":\r\n                result.paragraphProps = parseParagraphProperties(e, xml);\r\n                break;\r\n            case \"rPr\":\r\n                result.runProps = parseRunProperties(e, xml);\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumberingLevelOverrride(elem: Element, xml: XmlParser): NumberingLevelOverride {\r\n    let result = <NumberingLevelOverride>{\r\n        level: xml.intAttr(elem, 'ilvl')\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"startOverride\":\r\n                result.start = xml.intAttr(e, \"val\");\r\n                break;\r\n            case \"lvl\":\r\n                result.numberingLevel = parseNumberingLevel(e, xml);\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumberingBulletPicture(elem: Element, xml: XmlParser): NumberingBulletPicture {\r\n    //TODO\r\n    var pict = xml.element(elem, \"pict\");\r\n    var shape = pict && xml.element(pict, \"shape\");\r\n    var imagedata = shape && xml.element(shape, \"imagedata\");\r\n\r\n    return imagedata ? {\r\n        id: xml.attr(elem, \"numPicBulletId\"),\r\n        referenceId: xml.attr(imagedata, \"id\"),\r\n        style: xml.attr(shape, \"style\")\r\n    } : null;\r\n}", "import { Length,  LengthUsage, LengthUsageType, convertLength, convertBoolean  } from \"../document/common\";\r\n\r\nexport function parseXmlString(xmlString: string, trimXmlDeclaration: boolean = false): Document {\r\n    if (trimXmlDeclaration)\r\n        xmlString = xmlString.replace(/<[?].*[?]>/, \"\");\r\n        \r\n    xmlString = removeUTF8BOM(xmlString);\r\n    \r\n    const result = new DOMParser().parseFromString(xmlString, \"application/xml\");  \r\n    const errorText = hasXmlParserError(result);\r\n\r\n    if (errorText)\r\n        throw new Error(errorText);\r\n\r\n    return result;\r\n}\r\n\r\nfunction hasXmlParserError(doc: Document) {\r\n    return doc.getElementsByTagName(\"parsererror\")[0]?.textContent;\r\n}\r\n\r\nfunction removeUTF8BOM(data: string) {\r\n    return data.charCodeAt(0) === 0xFEFF ? data.substring(1) : data;\r\n}\r\n\r\nexport function serializeXmlString(elem: Node): string {\r\n    return new XMLSerializer().serializeToString(elem);\r\n}\r\n\r\nexport class XmlParser {\r\n    elements(elem: Element, localName: string = null): Element[] {\r\n        const result = [];\r\n\r\n        for (let i = 0, l = elem.childNodes.length; i < l; i++) {\r\n            let c = elem.childNodes.item(i);\r\n\r\n            if (c.nodeType == 1 && (localName == null || (c as Element).localName == localName))\r\n                result.push(c);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    element(elem: Element, localName: string): Element {\r\n        for (let i = 0, l = elem.childNodes.length; i < l; i++) {\r\n            let c = elem.childNodes.item(i);\r\n\r\n            if (c.nodeType == 1 && (c as Element).localName == localName)\r\n                return c as Element;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    elementAttr(elem: Element, localName: string, attrLocalName: string): string {\r\n        var el = this.element(elem, localName);\r\n        return el ? this.attr(el, attrLocalName) : undefined;\r\n    }\r\n\r\n\tattrs(elem: Element) {\r\n\t\treturn Array.from(elem.attributes);\r\n\t}\r\n\r\n    attr(elem: Element, localName: string): string {\r\n        for (let i = 0, l = elem.attributes.length; i < l; i++) {\r\n            let a = elem.attributes.item(i);\r\n\r\n            if (a.localName == localName)\r\n                return a.value;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    intAttr(node: Element, attrName: string, defaultValue: number = null): number {\r\n        var val = this.attr(node, attrName);\r\n        return val ? parseInt(val) : defaultValue;\r\n    }\r\n\r\n\thexAttr(node: Element, attrName: string, defaultValue: number = null): number {\r\n        var val = this.attr(node, attrName);\r\n        return val ? parseInt(val, 16) : defaultValue;\r\n    }\r\n\r\n    floatAttr(node: Element, attrName: string, defaultValue: number = null): number {\r\n        var val = this.attr(node, attrName);\r\n        return val ? parseFloat(val) : defaultValue;\r\n    }\r\n\r\n    boolAttr(node: Element, attrName: string, defaultValue: boolean = null) {\r\n        return convertBoolean(this.attr(node, attrName), defaultValue);\r\n    }\r\n\r\n    lengthAttr(node: Element, attrName: string, usage: LengthUsageType = LengthUsage.Dxa): Length {\r\n        return convertLength(this.attr(node, attrName), usage);\r\n    }\r\n}\r\n\r\nconst globalXmlParser = new XmlParser();\r\n\r\nexport default globalXmlParser;", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { WmlSettings, parseSettings } from \"./settings\";\r\n\r\nexport class SettingsPart extends Part {\r\n\tsettings: WmlSettings;\r\n\r\n\tconstructor(pkg: OpenXmlPackage, path: string) {\r\n\t\tsuper(pkg, path);\r\n\t}\r\n\r\n\tparseXml(root: Element) {\r\n\t\tthis.settings = parseSettings(root, this._package.xmlParser);\r\n\t}\r\n}", "import { DocumentParser } from \"../document-parser\";\r\nimport { Length } from \"../document/common\";\r\nimport { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface WmlSettings {\r\n\tdefaultTabStop: Length;\r\n\tfootnoteProps: NoteProperties;\r\n\tendnoteProps: NoteProperties;\r\n\tautoHyphenation: boolean;\r\n}\r\n\r\nexport interface NoteProperties {\r\n\tnummeringFormat: string;\r\n\tdefaultNoteIds: string[];\r\n}\r\n\r\nexport function parseSettings(elem: Element, xml: XmlParser) {\r\n\tvar result = {} as WmlSettings; \r\n\r\n\tfor (let el of xml.elements(elem)) {\r\n\t\tswitch(el.localName) {\r\n\t\t\tcase \"defaultTabStop\": result.defaultTabStop = xml.lengthAttr(el, \"val\"); break;\r\n\t\t\tcase \"footnotePr\": result.footnoteProps = parseNoteProperties(el, xml); break;\r\n\t\t\tcase \"endnotePr\": result.endnoteProps = parseNoteProperties(el, xml); break;\r\n\t\t\tcase \"autoHyphenation\": result.autoHyphenation = xml.boolAttr(el, \"val\"); break;\r\n\t\t}\r\n\t}\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNoteProperties(elem: Element, xml: XmlParser) {\r\n\tvar result = {\r\n\t\tdefaultNoteIds: []\r\n\t} as NoteProperties; \r\n\r\n\tfor (let el of xml.elements(elem)) {\r\n\t\tswitch(el.localName) {\r\n\t\t\tcase \"numFmt\": \r\n\t\t\t\tresult.nummeringFormat = xml.attr(el, \"val\");\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"footnote\": \r\n\t\t\tcase \"endnote\": \r\n\t\t\t\tresult.defaultNoteIds.push(xml.attr(el, \"id\"));\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n    return result;\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { IDomStyle } from \"../document/style\";\r\n\r\nexport class StylesPart extends Part {\r\n    styles: IDomStyle[];\r\n\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.styles = this._documentParser.parseStylesFile(root);     \r\n    }\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DmlTheme, parseTheme } from \"./theme\";\r\n\r\nexport class ThemePart extends Part {\r\n    theme: DmlTheme;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string) {\r\n        super(pkg, path);\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.theme = parseTheme(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport class DmlTheme {\r\n    colorScheme: DmlColorScheme;\r\n    fontScheme: DmlFontScheme;\r\n}\r\n\r\nexport interface DmlColorScheme {\r\n    name: string;\r\n    colors: Record<string, string>;\r\n}\r\n\r\nexport interface DmlFontScheme {\r\n    name: string;\r\n    majorFont: DmlFormInfo,\r\n    minorFont: DmlFormInfo\r\n}\r\n\r\nexport interface DmlFormInfo {\r\n    latinTypeface: string;\r\n    eaTypeface: string;\r\n    csTypeface: string;\r\n}\r\n\r\nexport function parseTheme(elem: Element, xml: XmlParser) {\r\n    var result = new DmlTheme();\r\n    var themeElements = xml.element(elem, \"themeElements\");\r\n\r\n    for (let el of xml.elements(themeElements)) {\r\n        switch(el.localName) {\r\n            case \"clrScheme\": result.colorScheme = parseColorScheme(el, xml); break;\r\n            case \"fontScheme\": result.fontScheme = parseFontScheme(el, xml); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseColorScheme(elem: Element, xml: XmlParser) {\r\n    var result: DmlColorScheme = { \r\n        name: xml.attr(elem, \"name\"),\r\n        colors: {}\r\n    };\r\n\r\n    for (let el of xml.elements(elem)) {\r\n        var srgbClr = xml.element(el, \"srgbClr\");\r\n        var sysClr = xml.element(el, \"sysClr\");\r\n\r\n        if (srgbClr) {\r\n            result.colors[el.localName] = xml.attr(srgbClr, \"val\");\r\n        }\r\n        else if (sysClr) {\r\n            result.colors[el.localName] = xml.attr(sysClr, \"lastClr\");\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseFontScheme(elem: Element, xml: XmlParser) {\r\n    var result: DmlFontScheme = { \r\n        name: xml.attr(elem, \"name\"),\r\n    } as DmlFontScheme;\r\n\r\n    for (let el of xml.elements(elem)) {\r\n        switch (el.localName) {\r\n            case \"majorFont\": result.majorFont = parseFontInfo(el, xml); break;\r\n            case \"minorFont\": result.minorFont = parseFontInfo(el, xml); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseFontInfo(elem: Element, xml: XmlParser): DmlFormInfo {\r\n    return {\r\n        latinTypeface: xml.elementAttr(elem, \"latin\", \"typeface\"),\r\n        eaTypeface: xml.elementAttr(elem, \"ea\", \"typeface\"),\r\n        csTypeface: xml.elementAttr(elem, \"cs\", \"typeface\"),\r\n    };\r\n}", "export function escapeClassName(className: string) {\r\n\treturn className?.replace(/[ .]+/g, '-').replace(/[&]+/g, 'and').toLowerCase();\r\n}\r\n\r\nexport function splitPath(path: string): [string, string] {\r\n    let si = path.lastIndexOf('/') + 1;\r\n    let folder = si == 0 ? \"\" : path.substring(0, si);\r\n    let fileName = si == 0 ? path : path.substring(si);\r\n\r\n    return [folder, fileName];\r\n}\r\n\r\nexport function resolvePath(path: string, base: string): string {\r\n    try {\r\n        const prefix = \"http://docx/\";\r\n        const url = new URL(path, prefix + base).toString();\r\n        return url.substring(prefix.length);\r\n    } catch {\r\n        return `${base}${path}`;\r\n    }\r\n}\r\n\r\nexport function keyBy<T = any>(array: T[], by: (x: T) => any): Record<any, T> {\r\n    return array.reduce((a, x) => {\r\n        a[by(x)] = x;\r\n        return a;\r\n    }, {});\r\n}\r\n\r\nexport function blobToBase64(blob: Blob): Promise<string> {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tconst reader = new FileReader();\r\n\t\treader.onloadend = () => resolve(reader.result as string);\r\n\t\treader.onerror = () => reject();\r\n\t\treader.readAsDataURL(blob);\r\n\t});\r\n}\r\n\r\nexport function isObject(item) {\r\n    return item && typeof item === 'object' && !Array.isArray(item);\r\n}\r\n\r\nexport function isString(item: unknown): item is string {\r\n    return typeof item === 'string' || item instanceof String;\r\n}\r\n\r\nexport function mergeDeep(target, ...sources) {\r\n    if (!sources.length) \r\n        return target;\r\n    \r\n    const source = sources.shift();\r\n\r\n    if (isObject(target) && isObject(source)) {\r\n        for (const key in source) {\r\n            if (isObject(source[key])) {\r\n                const val = target[key] ?? (target[key] = {});\r\n                mergeDeep(val, source[key]);\r\n            } else {\r\n                target[key] = source[key];\r\n            }\r\n        }\r\n    }\r\n\r\n    return mergeDeep(target, ...sources);\r\n}\r\n\r\nexport function parseCssRules(text: string): Record<string, string> {\r\n\tconst result: Record<string, string> = {};\r\n\r\n\tfor (const rule of text.split(';')) {\r\n\t\tconst [key, val] = rule.split(':');\r\n\t\tresult[key] = val;\r\n\t}\r\n\r\n\treturn result\r\n}\r\n\r\nexport function formatCssRules(style: Record<string, string>): string {\r\n\treturn Object.entries(style).map((k, v) => `${k}: ${v}`).join(';');\r\n}\r\n\r\nexport function asArray<T>(val: T | T[]): T[] {\r\n\treturn Array.isArray(val) ? val : [val];\r\n}", "import { DocumentParser } from '../document-parser';\r\nimport { convertLength, LengthUsage } from '../document/common';\r\nimport { OpenXmlElementBase, DomType } from '../document/dom';\r\nimport xml from '../parser/xml-parser';\r\nimport { formatCssRules, parseCssRules } from '../utils';\r\n\r\nexport class VmlElement extends OpenXmlElementBase {\r\n\ttype: DomType = DomType.VmlElement;\r\n\ttagName: string;\r\n\tcssStyleText?: string;\r\n\tattrs: Record<string, string> = {};\r\n\twrapType?: string;\r\n\timageHref?: {\r\n\t\tid: string,\r\n\t\ttitle: string\r\n\t}\r\n}\r\n\r\nexport function parseVmlElement(elem: Element, parser: DocumentParser): VmlElement {\r\n\tvar result = new VmlElement();\r\n\r\n\tswitch (elem.localName) {\r\n\t\tcase \"rect\":\r\n\t\t\tresult.tagName = \"rect\"; \r\n\t\t\tObject.assign(result.attrs, { width: '100%', height: '100%' });\r\n\t\t\tbreak;\r\n\r\n\t\tcase \"oval\":\r\n\t\t\tresult.tagName = \"ellipse\"; \r\n\t\t\tObject.assign(result.attrs, { cx: \"50%\", cy: \"50%\", rx: \"50%\", ry: \"50%\" });\r\n\t\t\tbreak;\r\n\t\r\n\t\tcase \"line\":\r\n\t\t\tresult.tagName = \"line\"; \r\n\t\t\tbreak;\r\n\r\n\t\tcase \"shape\":\r\n\t\t\tresult.tagName = \"g\"; \r\n\t\t\tbreak;\r\n\r\n\t\tcase \"textbox\":\r\n\t\t\tresult.tagName = \"foreignObject\"; \r\n\t\t\tObject.assign(result.attrs, { width: '100%', height: '100%' });\r\n\t\t\tbreak;\r\n\t\r\n\t\tdefault:\r\n\t\t\treturn null;\r\n\t}\r\n\r\n\tfor (const at of xml.attrs(elem)) {\r\n\t\tswitch(at.localName) {\r\n\t\t\tcase \"style\": \r\n\t\t\t\tresult.cssStyleText = at.value;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"fillcolor\": \r\n\t\t\t\tresult.attrs.fill = at.value; \r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"from\":\r\n\t\t\t\tconst [x1, y1] = parsePoint(at.value);\r\n\t\t\t\tObject.assign(result.attrs, { x1, y1 });\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"to\":\r\n\t\t\t\tconst [x2, y2] = parsePoint(at.value);\r\n\t\t\t\tObject.assign(result.attrs, { x2, y2 });\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\tfor (const el of xml.elements(elem)) {\r\n\t\tswitch (el.localName) {\r\n\t\t\tcase \"stroke\": \r\n\t\t\t\tObject.assign(result.attrs, parseStroke(el));\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"fill\": \r\n\t\t\t\tObject.assign(result.attrs, parseFill(el));\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"imagedata\":\r\n\t\t\t\tresult.tagName = \"image\";\r\n\t\t\t\tObject.assign(result.attrs, { width: '100%', height: '100%' });\r\n\t\t\t\tresult.imageHref = {\r\n\t\t\t\t\tid: xml.attr(el, \"id\"),\r\n\t\t\t\t\ttitle: xml.attr(el, \"title\"),\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"txbxContent\": \r\n\t\t\t\tresult.children.push(...parser.parseBodyElements(el));\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tconst child = parseVmlElement(el, parser);\r\n\t\t\t\tchild && result.children.push(child);\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\treturn result;\r\n}\r\n\r\nfunction parseStroke(el: Element): Record<string, string> {\r\n\treturn {\r\n\t\t'stroke': xml.attr(el, \"color\"),\r\n\t\t'stroke-width': xml.lengthAttr(el, \"weight\", LengthUsage.Emu) ?? '1px'\r\n\t};\r\n}\r\n\r\nfunction parseFill(el: Element): Record<string, string> {\r\n\treturn {\r\n\t\t//'fill': xml.attr(el, \"color2\")\r\n\t};\r\n}\r\n\r\nfunction parsePoint(val: string): string[] {\r\n\treturn val.split(\",\");\r\n}\r\n\r\nfunction convertPath(path: string): string {\r\n\treturn path.replace(/([mlxe])|([-\\d]+)|([,])/g, (m) => {\r\n\t\tif (/[-\\d]/.test(m)) return convertLength(m,  LengthUsage.VmlEmu);\r\n\t\tif (/[ml,]/.test(m)) return m;\r\n\r\n\t\treturn '';\r\n\t});\r\n}", "import { OutputType } from \"jszip\";\r\n\r\nimport { DocumentParser } from './document-parser';\r\nimport { Relationship, RelationshipTypes } from './common/relationship';\r\nimport { Part } from './common/part';\r\nimport { FontTablePart } from './font-table/font-table';\r\nimport { OpenXmlPackage } from './common/open-xml-package';\r\nimport { DocumentPart } from './document/document-part';\r\nimport { blobToBase64, resolvePath, splitPath } from './utils';\r\nimport { NumberingPart } from './numbering/numbering-part';\r\nimport { StylesPart } from './styles/styles-part';\r\nimport { FooterPart, HeaderPart } from \"./header-footer/parts\";\r\nimport { ExtendedPropsPart } from \"./document-props/extended-props-part\";\r\nimport { CorePropsPart } from \"./document-props/core-props-part\";\r\nimport { ThemePart } from \"./theme/theme-part\";\r\nimport { EndnotesPart, FootnotesPart } from \"./notes/parts\";\r\nimport { SettingsPart } from \"./settings/settings-part\";\r\nimport { CustomPropsPart } from \"./document-props/custom-props-part\";\r\n\r\nconst topLevelRels = [\r\n\t{ type: RelationshipTypes.OfficeDocument, target: \"word/document.xml\" },\r\n\t{ type: RelationshipTypes.ExtendedProperties, target: \"docProps/app.xml\" },\r\n\t{ type: RelationshipTypes.CoreProperties, target: \"docProps/core.xml\" },\r\n\t{ type: RelationshipTypes.CustomProperties, target: \"docProps/custom.xml\" },\r\n];\r\n\r\nexport class WordDocument {\r\n\tprivate _package: OpenXmlPackage;\r\n\tprivate _parser: DocumentParser;\r\n\tprivate _options: any;\r\n\r\n\trels: Relationship[];\r\n\tparts: Part[] = [];\r\n\tpartsMap: Record<string, Part> = {};\r\n\r\n\tdocumentPart: DocumentPart;\r\n\tfontTablePart: FontTablePart;\r\n\tnumberingPart: NumberingPart;\r\n\tstylesPart: StylesPart;\r\n\tfootnotesPart: FootnotesPart;\r\n\tendnotesPart: EndnotesPart;\r\n\tthemePart: ThemePart;\r\n\tcorePropsPart: CorePropsPart;\r\n\textendedPropsPart: ExtendedPropsPart;\r\n\tsettingsPart: SettingsPart;\r\n\r\n\tstatic async load(blob: Blob | any, parser: DocumentParser, options: any): Promise<WordDocument> {\r\n\t\tvar d = new WordDocument();\r\n\r\n\t\td._options = options;\r\n\t\td._parser = parser;\r\n\t\td._package = await OpenXmlPackage.load(blob, options);\r\n\t\td.rels = await d._package.loadRelationships();\r\n\r\n\t\tawait Promise.all(topLevelRels.map(rel => {\r\n\t\t\tconst r = d.rels.find(x => x.type === rel.type) ?? rel; //fallback                    \r\n\t\t\treturn d.loadRelationshipPart(r.target, r.type);\r\n\t\t}));\r\n\r\n\t\treturn d;\r\n\t}\r\n\r\n\tsave(type = \"blob\"): Promise<any> {\r\n\t\treturn this._package.save(type);\r\n\t}\r\n\r\n\tprivate async loadRelationshipPart(path: string, type: string): Promise<Part> {\r\n\t\tif (this.partsMap[path])\r\n\t\t\treturn this.partsMap[path];\r\n\r\n\t\tif (!this._package.get(path))\r\n\t\t\treturn null;\r\n\r\n\t\tlet part: Part = null;\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase RelationshipTypes.OfficeDocument:\r\n\t\t\t\tthis.documentPart = part = new DocumentPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.FontTable:\r\n\t\t\t\tthis.fontTablePart = part = new FontTablePart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Numbering:\r\n\t\t\t\tthis.numberingPart = part = new NumberingPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Styles:\r\n\t\t\t\tthis.stylesPart = part = new StylesPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Theme:\r\n\t\t\t\tthis.themePart = part = new ThemePart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Footnotes:\r\n\t\t\t\tthis.footnotesPart = part = new FootnotesPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Endnotes:\r\n\t\t\t\tthis.endnotesPart = part = new EndnotesPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Footer:\r\n\t\t\t\tpart = new FooterPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Header:\r\n\t\t\t\tpart = new HeaderPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.CoreProperties:\r\n\t\t\t\tthis.corePropsPart = part = new CorePropsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.ExtendedProperties:\r\n\t\t\t\tthis.extendedPropsPart = part = new ExtendedPropsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.CustomProperties:\r\n\t\t\t\tpart = new CustomPropsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tcase RelationshipTypes.Settings:\r\n\t\t\t\tthis.settingsPart = part = new SettingsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\tif (part == null)\r\n\t\t\treturn Promise.resolve(null);\r\n\r\n\t\tthis.partsMap[path] = part;\r\n\t\tthis.parts.push(part);\r\n\r\n\t\tawait part.load();\r\n\r\n\t\tif (part.rels?.length > 0) {\r\n\t\t\tconst [folder] = splitPath(part.path);\r\n\t\t\tawait Promise.all(part.rels.map(rel => this.loadRelationshipPart(resolvePath(rel.target, folder), rel.type)));\r\n\t\t}\r\n\r\n\t\treturn part;\r\n\t}\r\n\r\n\tasync loadDocumentImage(id: string, part?: Part): Promise<string> {\r\n\t\tconst x = await this.loadResource(part ?? this.documentPart, id, \"blob\");\r\n\t\treturn this.blobToURL(x);\r\n\t}\r\n\r\n\tasync loadNumberingImage(id: string): Promise<string> {\r\n\t\tconst x = await this.loadResource(this.numberingPart, id, \"blob\");\r\n\t\treturn this.blobToURL(x);\r\n\t}\r\n\r\n\tasync loadFont(id: string, key: string): Promise<string> {\r\n\t\tconst x = await this.loadResource(this.fontTablePart, id, \"uint8array\");\r\n\t\treturn x ? this.blobToURL(new Blob([deobfuscate(x, key)])) : x;\r\n\t}\r\n\r\n\tprivate blobToURL(blob: Blob): string | Promise<string> {\r\n\t\tif (!blob)\r\n\t\t\treturn null;\r\n\r\n\t\tif (this._options.useBase64URL) {\r\n\t\t\treturn blobToBase64(blob);\r\n\t\t}\r\n\r\n\t\treturn URL.createObjectURL(blob);\r\n\t}\r\n\r\n\tfindPartByRelId(id: string, basePart: Part = null) {\r\n\t\tvar rel = (basePart.rels ?? this.rels).find(r => r.id == id);\r\n\t\tconst folder = basePart ? splitPath(basePart.path)[0] : '';\r\n\t\treturn rel ? this.partsMap[resolvePath(rel.target, folder)] : null;\r\n\t}\r\n\r\n\tgetPathById(part: Part, id: string): string {\r\n\t\tconst rel = part.rels.find(x => x.id == id);\r\n\t\tconst [folder] = splitPath(part.path);\r\n\t\treturn rel ? resolvePath(rel.target, folder) : null;\r\n\t}\r\n\r\n\tprivate loadResource(part: Part, id: string, outputType: OutputType) {\r\n\t\tconst path = this.getPathById(part, id);\r\n\t\treturn path ? this._package.load(path, outputType) : Promise.resolve(null);\r\n\t}\r\n}\r\n\r\nexport function deobfuscate(data: Uint8Array, guidKey: string): Uint8Array {\r\n\tconst len = 16;\r\n\tconst trimmed = guidKey.replace(/{|}|-/g, \"\");\r\n\tconst numbers = new Array(len);\r\n\r\n\tfor (let i = 0; i < len; i++)\r\n\t\tnumbers[len - i - 1] = parseInt(trimmed.substr(i * 2, 2), 16);\r\n\r\n\tfor (let i = 0; i < 32; i++)\r\n\t\tdata[i] = data[i] ^ numbers[i % len]\r\n\r\n\treturn data;\r\n}", "var x = y => { var x = {}; __webpack_require__.d(x, y); return x; }\nvar y = x => () => x\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_jszip__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(667);\n"], "names": ["OpenXmlPackage", "constructor", "_zip", "options", "xmlParser", "XmlParser", "get", "path", "this", "files", "startsWith", "substr", "normalizePath", "update", "content", "file", "load", "input", "zip", "JSZip", "loadAsync", "save", "type", "generateAsync", "async", "Promise", "resolve", "loadRelationships", "rels<PERSON><PERSON>", "f", "fn", "splitPath", "txt", "parseRelationships", "parseXmlDocument", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseXmlString", "trimXmlDeclaration", "_package", "rels", "xmlText", "xmlDoc", "<PERSON><PERSON><PERSON><PERSON>", "_xmlDocument", "parseXml", "serializeXmlString", "root", "RelationshipTypes", "xml", "elements", "map", "e", "id", "attr", "target", "targetMode", "autos", "shd", "color", "borderColor", "highlight", "supportedNamespaceURIs", "mmlTagMap", "DomType", "MmlMath", "MmlMathParagraph", "MmlFraction", "MmlFunction", "MmlFunctionName", "MmlNumerator", "MmlDenominator", "MmlRadical", "MmlDegree", "MmlBase", "MmlSuperscript", "MmlSubscript", "MmlPreSubSuper", "MmlSuperArgument", "MmlSubArgument", "MmlDelimiter", "MmlNary", "MmlEquationArray", "MmlLimit", "MmlLimitLower", "MmlMatrix", "MmlMatrixRow", "MmlBox", "MmlBar", "MmlGroupChar", "<PERSON><PERSON><PERSON><PERSON>", "debug", "parseNotes", "elemName", "elemClass", "result", "el", "node", "noteType", "children", "parseBodyElements", "push", "parseDocumentFile", "xbody", "element", "background", "sectPr", "Document", "props", "parseSectionProperties", "cssStyle", "parseBackground", "elem", "xmlUtil", "colorAttr", "localName", "parseParagraph", "parseTable", "parseSdt", "parseStylesFile", "xstyles", "foreach", "n", "parseStyle", "parseDefaultStyles", "name", "basedOn", "styles", "c", "rPr", "values", "parseDefaultProperties", "pPr", "isDefault", "boolAttr", "linked", "next", "aliases", "split", "paragraphProps", "parseParagraphProperties", "runProps", "parseRunProperties", "s", "parseTableStyle", "console", "warn", "selector", "modificator", "mod", "parseNumberingFile", "xnums", "mapping", "bullets", "parseAbstractNumbering", "for<PERSON>ach", "x", "parseNumberingPicBullet", "numId", "abstractNumId", "elementAttr", "pict", "shape", "imagedata", "intAttr", "src", "style", "parseNumberingLevel", "level", "start", "pStyleName", "undefined", "pStyle", "rStyle", "suff", "bullet", "find", "levelText", "format", "parser", "sdtContent", "parseInserted", "parent<PERSON><PERSON><PERSON>", "Inserted", "parseDeleted", "Deleted", "Paragraph", "parseRun", "parseHyperlink", "parseBookmarkStart", "parseBookmarkEnd", "parseMathElement", "paragraph", "parseParagraphProperty", "styleName", "className", "classNameOfCnfStyle", "parseFrame", "parent", "Hyperlink", "anchor", "relId", "href", "Run", "checkAlternateContent", "Text", "text", "textContent", "DeletedText", "SimpleField", "instruction", "lock", "dirty", "fieldRun", "Instruction", "ComplexField", "charType", "NoBreakHyphen", "Break", "break", "Symbol", "font", "char", "Tab", "FootnoteReference", "EndnoteReference", "d", "parseDrawing", "parseVmlPicture", "propsTag", "run", "MmlRun", "parseMathProperies", "verticalJustification", "position", "hideDegree", "beginChar", "endChar", "verticalAlign", "valueOfVertAlign", "VmlPicture", "child", "parseVmlElement", "choice", "requires", "namespaceURI", "lookupNamespaceURI", "includes", "parseDrawingWrapper", "Drawing", "isAnchor", "wrapType", "simplePos", "posX", "relative", "align", "offset", "posY", "lengthAttr", "LengthUsage", "<PERSON><PERSON>", "pos", "alignNode", "offsetNode", "sizeValue", "g", "parseGraphic", "graphicData", "parsePicture", "Image", "blipFill", "blip", "spPr", "xfrm", "Table", "parseTableRow", "columns", "parseTableColumns", "parseTableProperties", "width", "table", "cellStyle", "classNameOftblLook", "parseTablePosition", "colBandSize", "rowBandSize", "topFromText", "bottomFromText", "rightFromText", "leftFromText", "addSize", "Row", "parseTableCell", "parseTableRowProperties", "row", "<PERSON><PERSON><PERSON><PERSON>", "Cell", "parseTableCellProperties", "cell", "span", "verticalMerge", "childStyle", "handler", "valueOfJc", "valueOfTextAlignment", "FontSize", "valueOfSize", "parseTrHeight", "parseUnderline", "parseIndentation", "parseFont", "parseBorderProperties", "valueOfMargin", "valueOfBorder", "parseMarginProperties", "valueOfTblLayout", "parseSpacing", "val", "col", "fonts", "themeValue", "filter", "join", "length", "firstLine", "hanging", "left", "right", "end", "before", "after", "line", "lineRule", "toFixed", "output", "knownColors", "cb", "i", "childNodes", "nodeType", "Node", "ELEMENT_NODE", "attrName", "defValue", "autoColor", "v", "themeColor", "Dxa", "convertLength", "Percent", "Border", "_", "asTagName", "a", "b", "hexAttr", "trim", "CorePropsPart", "Part", "parseCoreProps", "title", "description", "subject", "creator", "keywords", "language", "lastModifiedBy", "revision", "parseInt", "CustomPropsPart", "parseCustomProps", "<PERSON><PERSON><PERSON><PERSON>", "formatId", "nodeName", "value", "ExtendedPropsPart", "parseExtendedProps", "safeParseToInt", "template", "pages", "words", "characters", "application", "lines", "paragraphs", "company", "appVersion", "BookmarkStart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colLast", "BookmarkEnd", "parseBorder", "size", "Point", "frame", "shadow", "top", "bottom", "ns", "wordml", "drawingml", "picture", "compatibility", "math", "mul", "unit", "LineHeight", "VmlEmu", "usage", "test", "defaultValue", "fontSize", "DocumentPart", "pkg", "super", "_documentParser", "body", "parseCommonProperty", "tabs", "parseTabs", "sectionProps", "numbering", "parseNumbering", "lineSpacing", "parseLineSpacing", "textAlignment", "keepLines", "keepNext", "pageBreakBefore", "outlineLevel", "leader", "parseRunProperty", "SectionType", "parseColumns", "numberOfColumns", "space", "separator", "equalWidth", "parsePageNumber", "chapSep", "chapStyle", "parseFooterHeaderReference", "section", "pageSize", "height", "orientation", "<PERSON><PERSON><PERSON><PERSON>", "header", "footer", "gutter", "headerRefs", "footerRefs", "titlePage", "pageBorders", "parseBorders", "pageNumber", "ignoreHeight", "ignoreFonts", "breakPages", "experimental", "inWrapper", "ignoreLastRenderedPageBreak", "renderHeaders", "renderFooters", "renderFootnotes", "renderEndnotes", "useBase64URL", "renderChanges", "data", "userOptions", "ops", "WordDocument", "DocumentParser", "bodyContainer", "styleContainer", "renderer", "Html<PERSON><PERSON><PERSON>", "window", "document", "doc", "render", "FontTablePart", "parseFonts", "embedFontTypeMap", "embedRegular", "embedBold", "embedItalic", "embedBoldItalic", "embedFontRefs", "family", "altName", "parseEmbedFontRef", "key", "WmlHeader", "OpenXmlElementBase", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Footer", "BaseHeaderFooterPart", "rootElement", "createRootElement", "createElement", "tagName", "createElementNS", "createSvgElement", "Object", "assign", "append<PERSON><PERSON><PERSON><PERSON>", "removeAllElements", "innerHTML", "append<PERSON><PERSON><PERSON>", "isString", "createTextNode", "createStyleElement", "cssText", "appendComment", "comment", "createComment", "htmlDocument", "styleMap", "currentPart", "tableVerticalMerges", "currentVerticalMerge", "tableCellPositions", "currentCellPosition", "footnoteMap", "endnoteMap", "currentEndnoteIds", "usedHederFooterParts", "currentTabs", "tabsTimeout", "rootSelector", "renderDefaultStyle", "themePart", "renderTheme", "stylesPart", "processStyles", "renderStyles", "numberingPart", "prodessNumberings", "domNumberings", "renderNumbering", "footnotesPart", "keyBy", "notes", "endnotesPart", "settingsPart", "defaultTabSize", "settings", "defaultTabStop", "fontTablePart", "renderFontTable", "sectionElements", "renderSections", "documentPart", "renderWrapper", "refreshTabStops", "variables", "fontScheme", "theme", "majorFont", "latinTypeface", "minorFont", "colorScheme", "k", "entries", "colors", "styleToString", "fontsPart", "ref", "loadFont", "then", "fontData", "cssValues", "processStyleName", "escapeClassName", "stylesMap", "baseStyle", "mergeDeep", "baseValues", "styleValues", "copyStyleProperties", "cssName", "numberings", "num", "findStyle", "processElement", "processTable", "r", "attrs", "getOwnPropertyNames", "hasOwnProperty", "createSection", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "minHeight", "columnCount", "columnGap", "columnRule", "sections", "splitBySection", "prevProps", "l", "currentFootnoteIds", "sectProps", "sectionElement", "renderStyleValues", "renderHeaderFooter", "contentElement", "renderElements", "renderNotes", "refs", "page", "firstOfSection", "into", "part", "findPartByRelId", "isPageBreakElement", "current", "p", "pBreakIndex", "rBreakIndex", "findIndex", "bind", "breakRun", "splitRun", "newParagraph", "slice", "run<PERSON><PERSON><PERSON><PERSON>", "newRun", "currentSectProps", "styleText", "resetCounters", "numberingClass", "listStyleType", "valiable", "toLowerCase", "loadNumberingImage", "counter", "numberingCounter", "counterReset", "levelTextToContent", "numFormatToCssValue", "defautStyles", "subStyles", "linkedStyle", "concat", "subStyle", "noteIds", "notesMap", "renderElement", "renderParagraph", "renderBookmarkStart", "renderRun", "renderTable", "renderTableRow", "renderTableCell", "renderHyperlink", "renderDrawing", "renderImage", "renderText", "renderDeletedText", "renderTab", "renderSymbol", "renderBreak", "renderContainer", "Footnote", "Endnote", "renderFootnoteReference", "renderEndnoteReference", "renderVmlPicture", "VmlElement", "renderVmlElement", "renderContainerNS", "xmlns", "renderMmlGroupChar", "renderMmlRadical", "renderMmlDelimiter", "renderMmlRun", "renderMmlNary", "renderMmlPreSubSuper", "renderMmlBar", "renderMllList", "renderInserted", "renderDeleted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elems", "flatMap", "renderClass", "renderCommonProperties", "classList", "add", "renderRunProperties", "rel", "it", "display", "textIndent", "loadDocumentImage", "fontFamily", "tabSpan", "tabStopClass", "stops", "findParent", "wrapper", "renderTableColumns", "pop", "colElem", "rowSpan", "colSpan", "container", "setAttribute", "cssStyleText", "renderVmlChildElement", "imageHref", "requestAnimationFrame", "bb", "getBBox", "Math", "ceil", "y", "asArray", "base", "degree", "grouped", "sup", "sub", "supElem", "subElem", "char<PERSON><PERSON>", "stubElem", "textDecoration", "ou<PERSON>", "lvl", "selectors", "numformat", "replace", "substring", "none", "decimal", "lowerLetter", "upperLetter", "lowerRoman", "upperRoman", "decimalZero", "aiueo", "aiueo<PERSON><PERSON>", "chineseCounting", "chineseCountingThousand", "chineseLegalSimplified", "chosung", "ideographDigital", "ideographTraditional", "ideographLegalTraditional", "ideographZodiac", "i<PERSON><PERSON>", "iroha<PERSON><PERSON>", "japaneseCounting", "japaneseDigitalTenThousand", "japaneseLegal", "thaiNumbers", "koreanCounting", "koreanDigital", "koreanDigital2", "hebrew1", "hebrew2", "hindiNumbers", "ganada", "taiwaneseCounting", "taiwaneseCountingThousand", "taiwaneseDigital", "clearTimeout", "setTimeout", "pixelToPoint", "computePixelToPoint", "tab", "updateTabStop", "defaultTab", "lengthToPoint", "parseFloat", "temp", "offsetWidth", "<PERSON><PERSON><PERSON><PERSON>", "closest", "ebb", "getBoundingClientRect", "pbb", "pcs", "getComputedStyle", "tabStops", "t", "sort", "lastTab", "pWidthPt", "marginLeft", "pOffset", "Array", "from", "querySelectorAll", "nextIdx", "indexOf", "range", "createRange", "setStart", "setEndBefore", "setEndAfter", "nextBB", "wordSpacing", "textDecorationStyle", "WmlBaseNote", "BaseNotePart", "WmlFootnote", "WmlEndnote", "NumberingPart", "parseNumberingPart", "overrides", "abstractId", "parseNumberingLevelOverrride", "levels", "multiLevelType", "numberingStyleLink", "styleLink", "restart", "justification", "bulletPictureId", "paragraphStyle", "numberingLevel", "parseNumberingBulletPicture", "referenceId", "abstractNumberings", "bulletPictures", "xmlString", "charCodeAt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "errorText", "getElementsByTagName", "Error", "XMLSerializer", "serializeToString", "item", "attrLocalName", "attributes", "floatAttr", "convertBoolean", "globalXmlParser", "SettingsPart", "parseSettings", "parseNoteProperties", "defaultNoteIds", "nummeringFormat", "footnoteProps", "endnoteProps", "autoHyphenation", "<PERSON><PERSON><PERSON>", "ThemePart", "parseTheme", "DmlTheme", "parseColorScheme", "srgbClr", "sysClr", "parseFontScheme", "parseFontInfo", "eaTypeface", "csTypeface", "themeElements", "isObject", "isArray", "si", "lastIndexOf", "prefix", "URL", "toString", "array", "by", "reduce", "blob", "reject", "reader", "FileReader", "onloadend", "onerror", "readAsDataURL", "String", "sources", "source", "shift", "rule", "parseStroke", "parsePoint", "cx", "cy", "rx", "ry", "at", "fill", "x1", "y1", "x2", "y2", "topLevelRels", "OfficeDocument", "ExtendedProperties", "CoreProperties", "CustomProperties", "parts", "partsMap", "_options", "_parser", "all", "loadRelationshipPart", "FontTable", "Numbering", "Styles", "Theme", "Footnotes", "FootnotesPart", "Endnotes", "EndnotesPart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "corePropsPart", "extendedPropsPart", "Settings", "folder", "<PERSON><PERSON><PERSON>", "loadResource", "blobToURL", "Blob", "deobfuscate", "blobToBase64", "createObjectURL", "basePart", "getPathById", "outputType", "g<PERSON><PERSON><PERSON>", "trimmed", "numbers", "module", "exports", "__WEBPACK_EXTERNAL_MODULE_jszip__", "__webpack_module_cache__", "__webpack_exports__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__"], "sourceRoot": ""}