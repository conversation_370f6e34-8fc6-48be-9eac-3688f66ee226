/**
 * 型号完成进度图表
 * 用于展示各个型号的完成进度情况
 */

// 声明全局变量
window.modelProgressChart = null;
window.fullScreenModelChart = null;
window.originalModelData = null;

// 初始化型号完成进度图表
window.initModelProgressChart = function () {
    // 初始化echarts实例
    var chartOptions = {
        backgroundColor: "#162b4e",
        darkMode: true
    };

    // 使用window.modelProgressChart确保全局访问
    window.modelProgressChart = echarts.init(document.getElementById("modelProgressChart"), null, chartOptions);

    // 自适应窗口大小
    window.addEventListener('resize', function () {
        if (window.modelProgressChart) {
            window.modelProgressChart.resize();
        }
        if (window.fullScreenModelChart) {
            window.fullScreenModelChart.resize();
        }
    });
    
    // 添加放大按钮
    var chartContainer = document.getElementById("modelProgressChart");
    var zoomButton = document.createElement("div");
    zoomButton.id = "modelChartZoomButton";
    zoomButton.className = "zoom-button";
    zoomButton.innerHTML = '<i class="layui-icon layui-icon-screen-full"></i>';
    zoomButton.style.position = "absolute";
    zoomButton.style.top = "10px";
    zoomButton.style.right = "10px";
    zoomButton.style.zIndex = "100";
    zoomButton.style.backgroundColor = "rgba(22, 43, 78, 0.7)";
    zoomButton.style.color = "#fff";
    zoomButton.style.width = "30px";
    zoomButton.style.height = "30px";
    zoomButton.style.borderRadius = "4px";
    zoomButton.style.display = "flex";
    zoomButton.style.alignItems = "center";
    zoomButton.style.justifyContent = "center";
    zoomButton.style.cursor = "pointer";
    zoomButton.style.fontSize = "18px";
    zoomButton.style.boxShadow = "0 2px 5px rgba(0, 0, 0, 0.2)";
    
    // 添加悬停效果
    zoomButton.onmouseover = function() {
        this.style.backgroundColor = "rgba(58, 132, 243, 0.8)";
    };
    zoomButton.onmouseout = function() {
        this.style.backgroundColor = "rgba(22, 43, 78, 0.7)";
    };
    
    // 点击事件 - 打开全屏图表
    zoomButton.onclick = function() {
        openFullScreenChart();
    };
    
    chartContainer.style.position = "relative";
    chartContainer.appendChild(zoomButton);
};

// 绘制型号完成进度图表
window.drawModelProgressChart = function (data) {
    if (!window.modelProgressChart) return;
    
    // 保存原始数据以便全屏模式使用
    window.originalModelData = JSON.parse(JSON.stringify(data));

    // 判断是否为单一型号模式
    var isSingleModelMode = data.length === 1;
    
    // 控制放大按钮的显示与隐藏
    var zoomButton = document.getElementById("modelChartZoomButton");
    if (zoomButton) {
        if (isSingleModelMode) {
            zoomButton.style.display = "none"; // 单一型号模式隐藏放大按钮
        } else {
            zoomButton.style.display = "flex"; // 多型号模式显示放大按钮
        }
    }

    if (isSingleModelMode) {
        // 单个型号模式 - 绘制饼图
        drawSingleModelPieChart(data[0]);
    } else {
        // 多个型号模式 - 绘制柱状图
        drawMultiModelBarChart(data);
    }
};

// 打开全屏图表
function openFullScreenChart() {
    if (!window.originalModelData || window.originalModelData.length === 0) {
        layer.msg('暂无数据可显示', {icon: 2});
        return;
    }
    
    // 创建全屏图表的HTML内容
    var fullScreenContent = 
        '<div class="full-screen-chart-container" style="width: 100%; height: 100%; position: relative;">' +
            '<div class="filter-options" style="position: absolute; top: 10px; right: 10px; z-index: 100; display: flex; gap: 10px;">' +
                '<div class="layui-form">' +
                    '<div class="layui-form-item" style="margin-bottom: 0;">' +
                        '<div class="layui-input-block" style="margin-left: 0; display: flex; background: rgba(22, 43, 78, 0.7); padding: 5px 10px; border-radius: 4px;">' +
                            '<input type="radio" name="filterType" value="all" title="全部" checked lay-filter="chartFilter">' +
                            '<input type="radio" name="filterType" value="inProgress" title="未完成" lay-filter="chartFilter">' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
            '<div id="fullScreenModelChart" style="width: 100%; height: 100%;"></div>' +
        '</div>';
    
    // 打开全屏弹窗
    layer.open({
        type: 1,
        title: '型号完成进度详情',
        area: ['90%', '80%'],
        content: fullScreenContent,
        shade: 0.8,
        success: function(layero, index) {
            // 初始化全屏图表
            var chartOptions = {
                backgroundColor: "#162b4e",
                darkMode: true
            };
            
            window.fullScreenModelChart = echarts.init(document.getElementById("fullScreenModelChart"), null, chartOptions);
            
            // 初始化表单元素
            layui.form.render();
            
            // 绘制全部数据的图表
            drawFullScreenBarChart(window.originalModelData);
            
            // 监听单选按钮变化
            layui.form.on('radio(chartFilter)', function(data) {
                var filterType = data.value;
                if (filterType === 'all') {
                    // 显示全部数据
                    drawFullScreenBarChart(window.originalModelData);
                } else if (filterType === 'inProgress') {
                    // 筛选未完成的型号
                    var filteredData = window.originalModelData.filter(function(item) {
                        return item.completed < item.total;
                    });
                    drawFullScreenBarChart(filteredData);
                }
            });
        },
        end: function() {
            // 清理全屏图表实例
            if (window.fullScreenModelChart) {
                window.fullScreenModelChart.dispose();
                window.fullScreenModelChart = null;
            }
        }
    });
}

// 绘制全屏柱状图
function drawFullScreenBarChart(data) {
    if (!window.fullScreenModelChart || !data || data.length === 0) return;
    
    // 根据完成进度从大到小排序
    data.sort(function (a, b) {
        return b.progress - a.progress;
    });

    // 提取数据
    var modelNames = data.map(function (item) {
        return item.name;
    });
    var progressValues = data.map(function (item) {
        return (item.progress * 100).toFixed(2);
    });

    var option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                var item = params[0];
                var dataItem = data[item.dataIndex];
                var progressColor;
                
                // 根据进度返回对应的颜色，与柱状图颜色保持一致
                if (item.value >= 80) {
                    progressColor = '#52C41A';
                } else if (item.value >= 50) {
                    progressColor = '#2B5AED';
                } else if (item.value >= 30) {
                    progressColor = '#FA8C16';
                } else if (item.value >= 10) {
                    progressColor = '#722ED1';
                } else {
                    progressColor = '#F5222D';
                }
                
                // 构建美化的HTML内容
                var html = 
                    '<div style="padding: 8px 0; font-weight: 600; font-size: 16px; color: #fff;">' + 
                        item.name + 
                    '</div>' +
                    '<div style="padding: 0;">' +
                        '<div style="display: flex; justify-content: space-between; align-items: center; margin: 8px 0;">' +
                            '<span style="font-size: 13px; color: #eee;">完成进度:</span>' +
                            '<span style="font-size: 14px; font-weight: 600; color: ' + progressColor + ';">' + item.value + '%</span>' +
                        '</div>' +
                        '<div style="height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px; margin: 8px 0;">' +
                            '<div style="height: 6px; width: ' + item.value + '%; background: ' + progressColor + '; border-radius: 3px;"></div>' +
                        '</div>' +
                        '<div style="display: flex; justify-content: space-between; margin: 10px 0 5px 0;">' +
                            '<span style="font-size: 13px; color: #eee;">已完成产品:</span>' +
                            '<span style="font-size: 14px; color: #fff;">' + dataItem.completed + '</span>' +
                        '</div>' +
                        '<div style="display: flex; justify-content: space-between; margin: 5px 0;">' +
                            '<span style="font-size: 13px; color: #eee;">总产品数:</span>' +
                            '<span style="font-size: 14px; color: #fff;">' + dataItem.total + '</span>' +
                        '</div>' +
                    '</div>';
                
                return html;
            },
            backgroundColor: 'rgba(22, 43, 78, 0.9)',
            borderColor: '#1D3461',
            borderWidth: 1,
            borderRadius: 4,
            padding: [10, 15],
            textStyle: {
                color: '#ffffff'
            },
            extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);'
        },
        grid: {
            top: '60',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            max: 110,
            position: 'top',
            axisLabel: {
                color: '#ffffff',
                formatter: function (value) {
                    return value > 100 ? '' : value + '%';
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            }
        },
        yAxis: {
            type: 'category',
            data: modelNames,
            inverse: true,
            axisLabel: {
                color: '#ffffff'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisTick: {
                show: false
            }
        },
        series: [
            {
                name: '完成进度',
                type: 'bar',
                data: progressValues,
                itemStyle: {
                    // 使用现代互联网大厂风格的渐变色
                    color: function (params) {
                        var progress = parseFloat(params.value);
                        var colorObj;
                        
                        if (progress >= 80) {
                            // 高进度 - 绿色渐变
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#52C41A' },
                                { offset: 1, color: '#95DE64' }
                            ]);
                        } else if (progress >= 50) {
                            // 中高进度 - 蓝色渐变 (类似Facebook/微软)
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#2B5AED' },
                                { offset: 1, color: '#5B8FF9' }
                            ]);
                        } else if (progress >= 30) {
                            // 中等进度 - 橙色渐变 (类似阿里巴巴)
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#FA8C16' },
                                { offset: 1, color: '#FFC069' }
                            ]);
                        } else if (progress >= 10) {
                            // 低中进度 - 紫色渐变 (类似字节跳动)
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#722ED1' },
                                { offset: 1, color: '#B37FEB' }
                            ]);
                        } else {
                            // 低进度 - 红色渐变
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#F5222D' },
                                { offset: 1, color: '#FF7875' }
                            ]);
                        }
                        
                        return colorObj;
                    },
                    borderRadius: [0, 4, 4, 0]
                },
                barWidth: '60%',
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{c}%',
                    color: '#ffffff',
                    fontSize: 12
                },
                emphasis: {
                    label: {
                        show: true,
                        color: '#ffffff'
                    }
                }
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                show: true,
                yAxisIndex: [0],
                width: 15,
                right: 0,
                start: 0,
                end: (15 / modelNames.length) * 100, // 显示前15个型号
                filterMode: 'filter',
                brushSelect: false,
                handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#3A84F3'
                },
                textStyle: {
                    color: '#ffffff'
                },
                borderColor: '#1D3461',
                backgroundColor: 'rgba(22, 43, 78, 0.5)',
                fillerColor: 'rgba(58, 132, 243, 0.2)',
                showDetail: false
            }
        ]
    };

    window.fullScreenModelChart.setOption(option, true);
    
    // 添加点击事件
    window.fullScreenModelChart.off('click');
    window.fullScreenModelChart.on('click', function (params) {
        var modelName = params.name;
        var modelId = data[params.dataIndex].id;

        // 获取所有产品
        twxAjax('Thing.Fn.TestEvaluation', 'GetProductListV2', {
            modelId: modelId,
        }, true, function (res) {
            if (res.success) {
                generateProductTable(res.data, modelName + ' 产品列表');
            } else {
                layer.msg(res.msg, { icon: 2 });
            }
        });
    });
}

// 绘制多个型号的柱状图
function drawMultiModelBarChart(data) {
    // 根据完成进度从大到小排序
    data.sort(function (a, b) {
        return b.progress - a.progress;
    });

    // 提取数据
    var modelNames = data.map(function (item) {
        return item.name;
    });
    var progressValues = data.map(function (item) {
        return (item.progress * 100).toFixed(2);
    });

    var option = {
        title: {
            text: '型号完成进度',
            left: 'center',
            textStyle: {
                color: '#ffffff',
                fontSize: 18
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                var item = params[0];
                var dataItem = data[item.dataIndex];
                var progressColor;
                
                // 根据进度返回对应的颜色，与柱状图颜色保持一致
                if (item.value >= 80) {
                    progressColor = '#52C41A';
                } else if (item.value >= 50) {
                    progressColor = '#2B5AED';
                } else if (item.value >= 30) {
                    progressColor = '#FA8C16';
                } else if (item.value >= 10) {
                    progressColor = '#722ED1';
                } else {
                    progressColor = '#F5222D';
                }
                
                // 构建美化的HTML内容
                var html = 
                    '<div style="padding: 8px 0; font-weight: 600; font-size: 16px; color: #fff;">' + 
                        item.name + 
                    '</div>' +
                    '<div style="padding: 0;">' +
                        '<div style="display: flex; justify-content: space-between; align-items: center; margin: 8px 0;">' +
                            '<span style="font-size: 13px; color: #eee;">完成进度:</span>' +
                            '<span style="font-size: 14px; font-weight: 600; color: ' + progressColor + ';">' + item.value + '%</span>' +
                        '</div>' +
                        '<div style="height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px; margin: 8px 0;">' +
                            '<div style="height: 6px; width: ' + item.value + '%; background: ' + progressColor + '; border-radius: 3px;"></div>' +
                        '</div>' +
                        '<div style="display: flex; justify-content: space-between; margin: 10px 0 5px 0;">' +
                            '<span style="font-size: 13px; color: #eee;">已完成产品:</span>' +
                            '<span style="font-size: 14px; color: #fff;">' + dataItem.completed + '</span>' +
                        '</div>' +
                        '<div style="display: flex; justify-content: space-between; margin: 5px 0;">' +
                            '<span style="font-size: 13px; color: #eee;">总产品数:</span>' +
                            '<span style="font-size: 14px; color: #fff;">' + dataItem.total + '</span>' +
                        '</div>' +
                    '</div>';
                
                return html;
            },
            backgroundColor: 'rgba(22, 43, 78, 0.9)',
            borderColor: '#1D3461',
            borderWidth: 1,
            borderRadius: 4,
            padding: [10, 15],
            textStyle: {
                color: '#ffffff'
            },
            extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);'
        },
        grid: {
            top: '60',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            max: 110,
            position: 'top',
            axisLabel: {
                color: '#ffffff',
                formatter: function (value) {
                    return value > 100 ? '' : value + '%';
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            }
        },
        yAxis: {
            type: 'category',
            data: modelNames,
            inverse: true,
            axisLabel: {
                color: '#ffffff'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(255, 255, 255, 0.3)'
                }
            },
            axisTick: {
                show: false
            }
        },
        series: [
            {
                name: '完成进度',
                type: 'bar',
                data: progressValues,
                itemStyle: {
                    // 使用现代互联网大厂风格的渐变色
                    color: function (params) {
                        var progress = parseFloat(params.value);
                        var colorObj;
                        
                        if (progress >= 80) {
                            // 高进度 - 绿色渐变
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#52C41A' },
                                { offset: 1, color: '#95DE64' }
                            ]);
                        } else if (progress >= 50) {
                            // 中高进度 - 蓝色渐变 (类似Facebook/微软)
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#2B5AED' },
                                { offset: 1, color: '#5B8FF9' }
                            ]);
                        } else if (progress >= 30) {
                            // 中等进度 - 橙色渐变 (类似阿里巴巴)
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#FA8C16' },
                                { offset: 1, color: '#FFC069' }
                            ]);
                        } else if (progress >= 10) {
                            // 低中进度 - 紫色渐变 (类似字节跳动)
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#722ED1' },
                                { offset: 1, color: '#B37FEB' }
                            ]);
                        } else {
                            // 低进度 - 红色渐变
                            colorObj = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#F5222D' },
                                { offset: 1, color: '#FF7875' }
                            ]);
                        }
                        
                        return colorObj;
                    },
                    borderRadius: [0, 4, 4, 0]
                },
                barWidth: '60%',
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{c}%',
                    color: '#ffffff',
                    fontSize: 12
                },
                emphasis: {
                    label: {
                        show: true,
                        color: '#ffffff'
                    }
                }
            }
        ],
        dataZoom: [
            {
                type: 'slider',
                show: true,
                yAxisIndex: [0],
                width: 15,
                right: 0,
                start: 0,
                end: (10 / modelNames.length) * 100, // 显示前10个型号
                filterMode: 'filter',
                brushSelect: false,
                handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#3A84F3'
                },
                textStyle: {
                    color: '#ffffff'
                },
                borderColor: '#1D3461',
                backgroundColor: 'rgba(22, 43, 78, 0.5)',
                fillerColor: 'rgba(58, 132, 243, 0.2)',
                showDetail: false
            }
        ]
    };

    window.modelProgressChart.setOption(option, true);

    // 添加点击事件
    window.modelProgressChart.off('click');
    window.modelProgressChart.on('click', function (params) {
        var modelName = params.name;
        var modelId = data[params.dataIndex].id;

        // 获取所有产品
        twxAjax('Thing.Fn.TestEvaluation', 'GetProductListV2', {
            modelId: modelId,
        }, true, function (res) {
            if (res.success) {
                generateProductTable(res.data, modelName + ' 产品列表');
            } else {
                layer.msg(res.msg, { icon: 2 });
            }
        });
    });
}

// 绘制单个型号的饼图
function drawSingleModelPieChart(modelData) {
    // 构建饼图数据
    var pieData = [
        {
            name: '已完成',
            value: modelData.completed,
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    { offset: 0, color: '#52C41A' },
                    { offset: 1, color: '#95DE64' }
                ])
            }
        },
        {
            name: '未完成',
            value: modelData.total - modelData.completed,
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    { offset: 0, color: '#F5222D' },
                    { offset: 1, color: '#FF7875' }
                ])
            }
        }
    ];

    var option = {
        title: {
            text: modelData.name + ' - 完成进度',
            left: 'center',
            textStyle: {
                color: '#ffffff',
                fontSize: 18
            },
            subtext: '完成率: ' + (modelData.progress * 100).toFixed(2) + '%',
            subtextStyle: {
                color: '#ffffff',
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                if (params.name === '已完成') {
                    return '<div style="padding: 4px 0; font-weight: 600; color: #fff;">' +
                        params.seriesName + '</div>' +
                        '<div style="display: flex; justify-content: space-between; margin: 5px 0;">' +
                        '<span style="font-size: 13px; margin-right: 15px; color: #eee;">' + params.name + ':</span>' +
                        '<span style="font-size: 14px; color: #52C41A;">' + params.value + ' 个产品</span>' +
                        '</div>';
                } else {
                    return '<div style="padding: 4px 0; font-weight: 600; color: #fff;">' +
                        params.seriesName + '</div>' +
                        '<div style="display: flex; justify-content: space-between; margin: 5px 0;">' +
                        '<span style="font-size: 13px; margin-right: 15px; color: #eee;">' + params.name + ':</span>' +
                        '<span style="font-size: 14px; color: #F5222D;">' + params.value + ' 个产品</span>' +
                        '</div>';
                }
            },
            backgroundColor: 'rgba(22, 43, 78, 0.9)',
            borderColor: '#1D3461',
            borderWidth: 1,
            borderRadius: 4,
            padding: [10, 15],
            textStyle: {
                color: '#ffffff'
            },
            extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);'
        },
        // 明确设置grid以控制图表区域
        grid: {
            top: '60',
            left: '3%',
            right: '3%',
            bottom: '3%',
            containLabel: false
        },
        // 禁用x轴和y轴显示
        xAxis: {
            show: false
        },
        yAxis: {
            show: false
        },
        // 禁用dataZoom
        dataZoom: [],
        series: [
            {
                name: modelData.name,
                type: 'pie',
                radius: ['35%', '70%'],
                center: ['50%', '55%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#162b4e',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    position: 'outside',
                    formatter: '{b}: {c} ({d}%)',
                    color: '#ffffff'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '16',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: true,
                    length: 15,
                    length2: 10,
                    lineStyle: {
                        color: '#ffffff',
                        opacity: 0.5
                    }
                },
                data: pieData
            }
        ]
    };

    window.modelProgressChart.setOption(option, true);
    
    // 为饼图添加点击事件
    window.modelProgressChart.off('click');
    window.modelProgressChart.on('click', function (params) {
        var modelId = modelData.id;
        var modelName = modelData.name;
        var isCompleted = params.name === '已完成';
        var status = isCompleted ? '已完成' : '未完成';
        
        // 获取产品列表，使用GetProductListV2接口
        twxAjax('Thing.Fn.TestEvaluation', 'GetProductListV2', {
            modelId: modelId,
            subsystemName: '', // 不按分系统筛选
            status: status     // 直接传入"已完成"或"未完成"
        }, true, function (res) {
            if (res.success) {
                generateProductTable(res.data, modelName + ' - ' + status + '产品列表');
            } else {
                layer.msg(res.msg, { icon: 2 });
            }
        });
    });
}
