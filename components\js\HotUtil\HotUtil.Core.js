/**
 * HotUtil.Core.js - Handsontable工具类 (核心模块)
 *
 * 包含HotUtil对象定义、初始属性和核心工具函数。
 * 此文件必须在所有其他HotUtil模块之前加载。
 *
 * 版本升级记录：
 * - 2025-07-11: 升级适配Handsontable 16.0.1版本
 *
 * <AUTHOR>
 * @version 16.0.1-compatible
 */
var HotUtil = {
    /**
     * 当前节点的工作类型
     */
    workType: '',
    workTypes: [],
    postTypes: [],
    currentTreeNodeId: '',
    /**
     * 当前表格的保存时间，用于乐观锁检查
     */
    currentSaveTime: '',
    /**
     * 保存状态管理
     */
    _autoSaveInProgress: false,  // 自动保存状态
    _manualSaveInProgress: false, // 手动保存状态
};

/**
 * 初始化工作类型和岗位数据
 */
HotUtil.initWorkTypes = function () {
    twxAjax("Thing.Fn.SystemDic", 'getDictionaryDataByName', {
        name: '工时工作类型'
    }, true, function (res) {
        HotUtil.workTypes = res.rows;
    });
    twxAjax("Thing.Fn.SystemDic", 'getDictionaryDataByName', {
        name: '工时岗位'
    }, true, function (res) {
        HotUtil.postTypes = res.rows;
    });
};

/**
 * 注入密级数据
 */
HotUtil.injectSecurity = function () {
    twxAjax("Thing.Fn.SystemDic", 'getDictionaryDataByName', {
        name: '密级'
    }, true, function (res) {
        window.securitys = res.rows;
    }, function (xhr, textStatus, errorThrown) {
        // layer.alert('请求密级出错！', {
        // 	icon: 2
        // });
    });
};

/**
 * 根据密级Key获取名称
 * @param {string} securityKey
 * @returns {string}
 */
HotUtil.getSecurityName = function (securityKey) {
    var securityName = "";
    for (var i = 0; i < securitys.length; i++) {
        if (securityKey == securitys[i].KEY) {
            securityName = securitys[i].NAME;
            break;
        }
    }
    return securityName;
};

/**
 * 获取表头的开始值和结束值
 * @param {string} tableHeader
 * @returns {{min: number, max: number}}
 */
HotUtil.getTableHeader = function (tableHeader) {
    var min = 0;
    var max = 0;
    if (tableHeader.indexOf("-") > -1) {
        min = Number(tableHeader.split("-")[0]);
        max = Number(tableHeader.split("-")[1]);
    } else {
        max = Number(tableHeader);
    }
    return {
        min: min,
        max: max
    };
};

/**
 * 判断当前节点是否是pdf节点
 * @param {Object} treeNode
 * @returns {boolean}
 */
HotUtil.isPdf = function (treeNode) {
    var isPdf = false;
    if ((treeNode.TYPE == 'b' || treeNode.TYPE.indexOf('table') > -1) && (treeNode.FILE_FORMAT == 'pdf' ||
        treeNode.FILE_FORMAT == 'PDF')) {
        isPdf = true;
    }
    return isPdf;
};

/**
 * 在执行操作前检查乐观锁状态和编辑状态（异步版本）
 * @param {Object} treeNode 当前节点
 * @param {String} actionName 操作名称（用于错误提示）
 * @param {Function} callback 回调函数 callback(result) result: {success: boolean, errorMsg: string}
 */
HotUtil.checkOptimisticLockBeforeAction = function (treeNode, actionName, callback) {
    // 增强参数验证
    if (!treeNode || !treeNode.ID) {
        console.error('乐观锁检查：无效的节点参数');
        callback({
            success: false,
            errorMsg: "节点信息无效，无法执行" + (actionName || "操作")
        });
        return;
    }

    // 验证回调函数
    if (typeof callback !== 'function') {
        console.error('乐观锁检查：缺少回调函数');
        return;
    }

    // 如果没有currentSaveTime，说明可能是新数据或页面刚加载，允许继续
    if (!HotUtil.currentSaveTime) {
        callback({ success: true, errorMsg: "" });
        return;
    }

    // 显示检查状态提示
    var checkingMsg = layer.msg('正在检查数据状态...', {
        icon: 16,
        shade: 0.3,
        time: 0 // 不自动关闭
    });

    // 使用异步方式调用增强的乐观锁检查（同时检查编辑状态）
    twxAjax(THING, 'CheckOptimisticLockAndEditStatus', {
        id: treeNode.ID,
        expectedSaveTime: HotUtil.currentSaveTime,
        currentUser: sessionStorage.getItem("fullname") + '[' + sessionStorage.getItem('username') + ']'
    }, true, function (res) {
        // 关闭检查状态提示
        layer.close(checkingMsg);

        try {
            if (res && res.success) {
                callback({ success: true, errorMsg: "" });
            } else {
                var errorMsg = "";
                if (res && res.editConflict) {
                    errorMsg = "表格正在被用户 " + res.currentEditor + " 编辑中，无法" + actionName + "。请稍后再试。";
                } else if (res && res.conflict) {
                    errorMsg = "数据已被其他用户修改，无法" + actionName + "。请刷新页面获取最新数据后重试。";
                } else {
                    errorMsg = (res && res.msg) || "数据状态检查失败";
                }
                callback({ success: false, errorMsg: errorMsg });
            }
        } catch (e) {
            console.error('处理乐观锁检查结果时出错:', e);
            callback({
                success: false,
                errorMsg: "数据状态检查出错: " + e.message
            });
        }
    }, function (xhr, textStatus, errorThrown) {
        // 关闭检查状态提示
        layer.close(checkingMsg);

        // 如果新服务不存在，回退到原来的检查方式
        if (textStatus === 'error' && xhr.status === 404) {
            console.warn('CheckOptimisticLockAndEditStatus 服务不存在，回退到原来的乐观锁检查');
            HotUtil._fallbackOptimisticLockCheck(treeNode, actionName, callback);
        } else {
            console.error('乐观锁检查请求失败:', textStatus, errorThrown);
            callback({
                success: false,
                errorMsg: "无法连接服务器检查数据状态: " + (textStatus || "未知错误")
            });
        }
    });
};

/**
 * 回退到原来的乐观锁检查方式（向后兼容）
 * @param {Object} treeNode 当前节点
 * @param {String} actionName 操作名称
 * @param {Function} callback 回调函数
 */
HotUtil._fallbackOptimisticLockCheck = function (treeNode, actionName, callback) {
    // 使用原来的乐观锁检查服务
    twxAjax(THING, 'CheckOptimisticLock', {
        id: treeNode.ID,
        expectedSaveTime: HotUtil.currentSaveTime
    }, true, function (res) {
        try {
            if (res && res.success) {
                callback({ success: true, errorMsg: "" });
            } else {
                var errorMsg = "";
                if (res && res.conflict) {
                    errorMsg = "数据已被其他用户修改，无法" + actionName + "。请刷新页面获取最新数据后重试。";
                } else {
                    errorMsg = (res && res.msg) || "数据状态检查失败";
                }
                callback({ success: false, errorMsg: errorMsg });
            }
        } catch (e) {
            console.error('处理回退乐观锁检查结果时出错:', e);
            callback({
                success: false,
                errorMsg: "数据状态检查出错: " + e.message
            });
        }
    }, function (xhr, textStatus, errorThrown) {
        console.error('回退乐观锁检查请求失败:', textStatus, errorThrown);
        callback({
            success: false,
            errorMsg: "无法连接服务器检查数据状态: " + (textStatus || "未知错误")
        });
    });
};


// Initial calls
HotUtil.injectSecurity();
HotUtil.initWorkTypes();