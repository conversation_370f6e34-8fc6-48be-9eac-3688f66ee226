<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../../img/favicon.ico">
	<link rel="stylesheet" href="../../../plugins/layui/css/layui.css" media="all">
	<!-- <link href="../../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css"> -->

	<!-- <link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->

	<link rel="stylesheet" href="../../../css/icon.css">

	<!-- <script src="../../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
	<script src="../../../plugins/layui/layui.js"></script>

	<link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../../plugins/easyui/jquery.min.js"></script>
	<script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<!-- <script>
        sessionStorage.setItem('secLevel','1');
        sessionStorage.setItem('username','adm');
        sessionStorage.setItem('roleid','4,29');
    </script> -->

	<script src="../../js/config/twxconfig.js"></script>
	<script src="../../js/util.js"></script>

	<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/contextMenu.css">
	<script type="text/javascript" src="../../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript" src="../../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
	<script type="text/javascript" src="../../dataTree/tree.js"></script>
	<script type="text/javascript" src="../../dataTree/bom_tree.js"></script>
	<!-- <script type="text/javascript" src="../../../plugins/loading/jquery.loading.min.js"></script> -->

	<!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
	<script type="text/javascript" src="../../js/logUtil.js"></script>
	<script type="text/javascript">
		layui.use(['layer', 'form'], function() {
			//此处省略一万字
			//.........................

			var form = layui.form;
			form.render(); //没有写这个，操作后没有效果
			loadKeyTypeSelect();
			form.on('select(dataType)', function(record) {
				var value = record.value;

				// twxAjax("Thing.Fn.KeyAttrAnalyze", "getProductByTableName", {
				// 	tableName: record.value
				// }, false, function(data) {

				// 	if (data == undefined ||data.rows.length==0) {
				// 		$("#productSelections").empty();
				// 		layer.alert("该类型没有配置[名称]参数，请配置后重试！", {
				// 			icon: 2
				// 		});
				// 	} else {
				// 		$("#productSelections").empty();
				// 		for (var i = 0; i < data.rows.length; i++) {
				// 			if (i % 5 == 0) {
				// 				var targetObj = $("#productSelections").append('<br/><input type="checkbox" lay-filter="product" name="product" title="'+data.rows[i].PN+'" value="'+data.rows[i].PN+'">');
				// 				//form.render();
				// 			} else {
				// 				var targetObj = $("#productSelections").append('<input type="checkbox" lay-filter="product" name="product" title="'+data.rows[i].PN+'" value="'+data.rows[i].PN+'">');
				// 				//form.render();
				// 			}

				// 		}
				// 	}
				// 	form.render();
				// });

				twxAjax("Thing.DB.Oracle", "RunQuery", {
					sql: "SELECT * FROM PARAM_CONFIG where TABLE_NAME='" + record.value + "'"
				}, false, function(data) {
					if (data == undefined || data.rows == undefined || data.rows.length == 0) {

						layer.alert("该类型没有配置参数，请配置后重试！", {
							icon: 2
						});
					} else {
						$("#paramSelections").empty();
						for (var i = 0; i < data.rows.length; i++) {
							if (i % 5 == 0) {
								var targetObj = $("#paramSelections").append('<br/><input type="checkbox" lay-filter="param" name="param" title="' + data.rows[i].PARAM_NAME + '" value="' + data.rows[i].COL_NAME + '">');
								//form.render();
							} else {
								var targetObj = $("#paramSelections").append('<input type="checkbox" lay-filter="param" name="param" title="' + data.rows[i].PARAM_NAME + '" value="' + data.rows[i].COL_NAME + '">');
								//form.render();
							}

						}
						form.render();
					}
				});
			});


		});

		$(function() {


			// twxAjax('Thing.DB.Oracle', 'RunQuery', {
			// 	sql: "SELECT * FROM TABLE_CONFIG"
			// }, true, function(data) {
			// console.log(data);
			// var rows = data.rows;
			// if (rows.length > 0) {
			// 	for (var i = 0; i < rows.length; i++) {
			// 		var row = rows[i];
			// 		$('#modelSelections').append("<input class='easyui-checkbox' name='model' value='"+row.ID+"' label='"+row.TREE_NAME+":' />");
			// 	}

			// } else {
			// 	showMsg('暂无数据！');
			// }

			// });


		})

		function renderKeyItemAnalyzeTable() {
			var columns = [
				[

				]
			];
			var cb_success = function(data) {
				var datas = data;
				var cols = datas.cols;
				for (var x = 0; x < cols.length; x++) {
					var colInfo = cols[x];
					columns[0].push({
						field: colInfo.key,
						title: colInfo.name,
						width: 150,
						align: 'center'
					});

				}
				if (!tableLoadFlag[tableId]) {
					$('#' + tableId).datagrid({
						data: datas.datarows,
						fitColumns: true,
						toolbar: '#tb',
						fit: true,
						columns: columns,
						emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>暂无数据...</font></div>',
						pagination: true,
						loadMsg: '正在加载数据...',
						singleSelect: true,
						rownumbers: true,
						striped: true
					});
					tableLoadFlag[tableId] = true;
				}

			};

			// var parmas = $('#dataType').combobox('getText');
			// var allDataJson = twxAjax('Thing.Fn.KeyAttrAnalyze', 'getAllKeyAttrInfo', {
			// 	treeId: parmas.treeId,
			// 	dataType: $('#dataType').combobox('getText')
			// }, true, cb_success);




		}

		function renderKeyItemAnalyzeTable(record) {
			var columns = [
				[

				]
			];

			var cb_success = function(data) {
				var datas = data;
				var cols = datas.cols;
				if (cols == null) {
					layer.closeAll();
					layer.alert(datas.errorMsg, {
						icon: 2
					});
				}
				for (var x = 0; x < cols.length; x++) {
					var colInfo = cols[x];
					columns[0].push({
						field: colInfo.key,
						title: colInfo.name,
						width: 150,
						align: 'center'
					});

				}

				$('#' + tableId).datagrid({
					data: datas.datarows,
					fitColumns: true,
					toolbar: '#tb',
					fit: true,
					columns: columns,
					emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>暂无数据...</font></div>',
					pagination: true,
					loadMsg: '正在加载数据...',
					singleSelect: true,
					rownumbers: true,
					striped: true
				});
				layer.closeAll();

			};

			//请求失败的回调
			var cb_error = function(xhr, textStatus, errorThrown) {
				layer.closeAll();
				layer.alert("该节点无数据！", {
					icon: 2
				});
			};

			var treeId = 1;
			if ($.fn.zTree.getZTreeObj("dpTree") != null) {
				var selNodes = $.fn.zTree.getZTreeObj("dpTree").getSelectedNodes();
				if (selNodes.length > 0) {
					treeId = selNodes[0].TREEID;
				}
			}
			var Iindex = layer.msg('正在加载数据,请不要关闭页面,请稍等......', {
				icon: 16,
				shade: 0.01,
				time: 0
			});
			if (record != null) {
				var allDataJson = twxAjax('Thing.Fn.KeyAttrAnalyze', 'getAllKeyAttrInfo', {
					treeId: treeId,
					dataType: record.name
				}, true, cb_success, cb_error);
			}





		}

		//加载质量数据类型下拉框
		var loadKeyTypeSelect = function() {
			var treeId = 1;

			var cb_success = function(data) {

				if (data.rows.length > 0) {
					// $('#dataType').combobox({
					// 	data: data.rows,
					// 	valueField: 'TABLE_NAME',
					// 	textField: 'TREE_NAME',
					// 	editable: false,
					// 	width: 300,
					// 	panelHeight: 400,
					// onSelect: function(record) {

					// }
					// });
					// $('#dataType').combobox("select", data.rows[0].PN);
					for (var i = 0; i < data.rows.length; i++) {
						var $a = $('<option value="' + data.rows[i].TABLE_NAME + '">' + data.rows[i].TREE_NAME + '</option>');
						$("#dataType").append($a);


					}
					layui.form.render();
				} else {
					showMsg('暂无数据！');
				}
			};
			//请求失败的回调
			var cb_error = function(xhr, textStatus, errorThrown) {
				layer.alert("加载质量数据类型下拉框出错！", {
					icon: 2
				});
			};


			twxAjax("Thing.DB.Oracle", "RunQuery", {
				sql: 'select * from TABLE_CONFIG'
			}, false, cb_success, cb_error);

		}

		function GetCheckboxValues(Name) {
			var result = [];
			$("[name='" + Name + "']:checkbox").each(function() {
				if ($(this).is(":checked")) {
					result.push($(this).attr("value"));
				}
			});
			return result.join(",");
		}

		function GetCheckboxTitles(Name) {
			var result = [];
			$("[name='" + Name + "']:checkbox").each(function() {
				if ($(this).is(":checked")) {
					result.push($(this).attr("title"));
				}
			});
			return result.join(",");
		}

		function doSearch() {
			var parameters = GetCheckboxValues("param");
			var paramTitles = GetCheckboxTitles("param");

			var datatype = $("#dataType").val();
			var columns = [
				[

				]
			];
			var cb_success = function(data) {
				var datas = data;
				var cols = datas.cols;
				if (cols == null) {
					layer.closeAll();
					layer.alert(datas.errorMsg, {
						icon: 2
					});
				}
				for (var x = 0; x < cols.length; x++) {
					var colInfo = cols[x];
					columns[0].push({
						field: colInfo.key,
						title: colInfo.name,
						width: 150,
						align: 'center'
					});

				}

				$('#KeyItemAnalyze_list_table').datagrid({
					data: datas.datarows,
					toolbar: '#tb',
					fit: true,
					columns: columns,
					emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>暂无数据...</font></div>',
					pagination: true,
					loadMsg: '正在加载数据...',
					singleSelect: true,
					rownumbers: true,
					striped: true
				});
				layer.closeAll();

			};

			//请求失败的回调
			var cb_error = function(xhr, textStatus, errorThrown) {
				layer.closeAll();
				layer.alert("该节点无数据！", {
					icon: 2
				});
			};
			twxAjax("Thing.Fn.KeyAttrAnalyze", "getKeyItemAnalyticsByParams", {
				parameters: parameters,
				paramTitles: paramTitles,
				products: document.getElementById("prods").value,
				dataType: datatype
			}, false, cb_success, cb_error);

		}

		function doHide() {
			if ($("#more-container").is(":hidden")) {
				$("#more-container").show(); //如果元素为隐藏,则将它显现
				$("#more-btn").html('<i class="layui-icon layui-icon-subtraction"></i>收起')
			} else {
				$("#more-container").hide(); //如果元素为显现,则将其隐藏
				$("#more-btn").html('<i class="layui-icon layui-icon-addition"></i>展开')
			}
			return false;
		}

		function openProductSearch() {
			var datatype = $("#dataType").val();
			layer.open({
				type: 2,
				title: '选择产品',
				area: ['95%', '90%'],
				content: './keyItemAnalysisProductSearch.html?datatype=' + datatype,
				btn: ['确定', '取消'] //只是为了演示
				,
				btnAlign: 'c' //按钮居中
				,
				yes: function(index, layero) {
					//1-获取
					document.getElementById("prods").value="";
					var iframeWin = window[layero.find('iframe')[0]['name']];
					var inputArray = iframeWin.document.getElementsByName("product");
					var productValue = "";
					for(var i=0;i<inputArray.length;i++){
						var ele = inputArray[i];
						console.log(ele.value);
						productValue += ele.value+",";
					}
					if(productValue.indexOf(",")>-1){
						productValue = productValue.substring(0,productValue.length-1);
					}
					document.getElementById("prods").value=productValue;
					layer.close(index);
				}
			});
		}
	</script>
</head>
<body>
<br />
<input id="prods" type="hidden" />
<div id="more-container" style="overflow-y: scroll;">
	<form class="layui-form" action="">
		<div class="layui-form-item">
			<label class="layui-form-label">类别：</label>
			<div class="layui-input-block">
				<select lay-filter="dataType" id="dataType" name="dept" style="width:200px;">
					<option value="">请选择类别</option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">产品：</label>
			<button type="button" class="layui-btn layui-btn-primary" style="margin-left: 10%;" onclick="openProductSearch()">选择产品</button>
			<div id="productSelections" class="layui-input-block">

			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">参数：</label>
			<div id="paramSelections" class="layui-input-block">

			</div>
		</div>
		<button type="button" class="layui-btn layui-btn-primary" style="margin-left: 10%;" onclick="doSearch()">查询</button>
	</form>

</div>
<button class="layui-btn layui-btn-normal" lay-filter="more" id="more-btn" onclick="doHide()"><i class="layui-icon layui-icon-addition"></i>折叠</button>
<br /><br />
<div id="KeyItemAnalyze_list_table" data-options="border:false"></div>

</body>
</html>
