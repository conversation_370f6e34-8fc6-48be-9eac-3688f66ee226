/**
 * HotUtil.Editor.Core.js - Handsontable 编辑器工具库 (核心与生命周期模块)
 *
 * 负责编辑器的打开、关闭、状态管理和顶层操作。
 * 此文件应在所有其他 Editor 模块之后加载。
 */

/**
 * 使表格进入编辑状态
 * @param {Object} treeNode
 * @param {number} isUpdate
 * @param {Function} successFn
 */
HotUtil.openEdit = function (treeNode, isUpdate, successFn) {
    twxAjax(THING, 'OpenEdit', {
        id: treeNode.ID,
        isUpdate: isUpdate,
        user: sessionStorage.getItem("fullname") + '[' + sessionStorage.getItem('username') + ']',
    }, true, function (res) {
        if (res.success) {
            successFn();
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (err) {
        layer.alert('进入编辑失败！', {
            icon: 2
        });
    });
};

/**
 * 使表格关闭编辑状态
 * @param {Object} treeNode
 * @param {Function} successFn
 */
HotUtil.closeEdit = function (treeNode, successFn) {
    twxAjax(THING, 'CloseEdit', {
        id: treeNode.ID
    }, true, function (res) {
        if (res.success) {
            successFn();
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (err) {
        layer.alert('退出编辑失败！', {
            icon: 2
        });
    });
};

/**
 * 编辑表格
 * @param {Object} treeNode
 */
HotUtil.editTable = function (treeNode) {
    // 在编辑表格操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "编辑表格", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '编辑失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        // 乐观锁检查通过，设置编辑状态后继续执行编辑表格逻辑
        HotUtil.openEdit(treeNode, 1, function () {
            HotUtil._executeEditTable(treeNode);
        });
    });
};

/**
 * 执行编辑表格的具体逻辑（从原 editTable 函数中提取）
 * @param {Object} treeNode
 */
HotUtil._executeEditTable = function (treeNode) {
    var log = {};
    log.operation = '编辑表格';
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;
    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上编辑表格";
    layer.open({
        title: '编辑：' + treeNode.NAME,
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        zIndex: 1000,
        shadeClose: false,
        resize: false, //不允许拉伸
        area: [$('body').css('width'), $('body').css('height')],
        content: '<div id="handsontable" style="width:100%;height:100%;overflow:auto;"></div>',
        cancel: function (index, layero) {
            HotUtil.closeEdit(treeNode, function () {
                if (window.hot) {
                    window.hot.destroy();
                }
                reloadTable(treeNode);
            });
        },
        btn: ['保存', '取消'],
        yes: function () {
            // 防止重复点击手动保存按钮
            if (HotUtil._manualSaveInProgress) {
                layer.msg('正在保存中，请勿重复操作', { icon: 0 });
                return false;
            }

            HotUtil.closeEdit(treeNode, function () {
                // 手动保存：显示保存状态提示，允许与自动保存并发
                HotUtil.saveTableData(treeNode, true, true, true);
                if (window.hot) {
                    window.hot.destroy();
                }
            });
        },
        btn2: function () {
            HotUtil.closeEdit(treeNode, function () {
                if (window.hot) {
                    window.hot.destroy();
                }
                reloadTable(treeNode);
                return true;
            });
        },
        success: function (layero, index) {
            var saveData = treeNode.SAVE_DATA;
            //重新请求表格数据 避免同时编辑的时候产生冲突

            var afterChange = function (change, source) {
                // 增强错误处理：避免在初始化过程中触发保存
                if (source != 'loadData' && source != 'populateFromArray' && source != 'auto') {
                    // 检查Handsontable是否完全初始化
                    if (window.hot && window.hot.isDestroyed !== true) {
                        // 自动保存：静默保存，不显示保存状态提示
                        HotUtil.saveTableData(treeNode, false, false, false);
                    } else {
                        console.log('afterChange: Handsontable未完全初始化，跳过保存操作');
                    }
                }
            }
            var cb_success = function (res) {
                if (res.success) {
                    saveData = res.data.SAVE_DATA || "";
                    HotUtil.renderHot(saveData, treeNode, res.data.TABLE_HEADER,
                        afterChange);
                    log.reqResult = 1;
                } else {
                    log.reqResult = 0;
                }
                addConfirmLog(log);
            };
            var cb_error = function (xhr) {
                layer.alert('加载表格失败!', {
                    icon: 2
                });
            };
            twxAjax(THING, "QueryNodeById", {
                id: treeNode.ID
            }, true, cb_success, cb_error);
        }
    });
};


/**
 * 执行更新表头行的具体逻辑
 * @param {Object} treeNode
 */
HotUtil._executeUpdateHeaderRow = function (treeNode) {
    var tpl = '<form class="layui-form" action="" lay-filter="header-form">\
                    <div class="layui-form-item">\
                        <div class="layui-inline">\
                            <label class="layui-form-label">行数</label>\
                                <div class="layui-input-inline" style="width: 100px;">\
                                    <input type="number" name="header_min" lay-verify="required|number" autocomplete="off" class="layui-input">\
                                </div>\
                                <div class="layui-form-mid">-</div>\
                                <div class="layui-input-inline" style="width: 100px;">\
                                    <input type="number" name="header_max" lay-verify="required|number" autocomplete="off" class="layui-input">\
                                </div>\
                        </div>\
                    </div>\
                    <div class="layui-form-item" style="display:none;">\
                        <div class="layui-input-block">\
                            <div class="layui-footer">\
                                <button class="layui-btn" id="submit-header" lay-submit="" lay-filter="submit-header">确认</button>\
                            </div>\
                        </div>\
                    </div>\
                </form>';
    layer.open({
        title: '设置表格表头行',
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false, //不允许拉伸
        area: ['380px', '180px'],
        content: '<div id="headerContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确认', '取消'],
        yes: function () {
            $('#submit-header').click();
        },
        btn2: function () {
            return true;
        },
        success: function () {
            $("#headerContent").append(tpl);
        }
    });
    form.render(null, 'header-form');
    form.on('submit(submit-header)', function (data) {
        if (/^\+?[0-9][0-9]*$/.test(data.field.header_min) && /^\+?[0-9][0-9]*$/.test(data.field
            .header_max)) {
            if (Number(data.field.header_min) <= Number(data.field.header_max)) {
                //日志记录
                var log = {};
                log.operation = "设置表头行";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;
                var cb_success = function (res) {
                    if (res.success) {
                        log.reqResult = 1;
                        layer.closeAll();
                        layer.msg(res.msg);
                        reloadTable(treeNode);
                    } else {
                        log.reqResult = 0;
                        layer.alert(res.msg, {
                            icon: 2
                        });
                    }
                    addConfirmLog(log);
                };
                var cb_error = function () {
                    layer.closeAll();
                    layer.msg('设置失败！');
                };
                var headerRow = data.field.header_min + "-" + data.field.header_max;
                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" +
                    "设置表头行为（" + headerRow + "）";
                twxAjax(THING, 'UpdateTableHeader', {
                    id: treeNode.ID,
                    header: headerRow
                }, true, cb_success, cb_error);
            } else {
                layer.msg('起始值比结束值小！')
            }
        } else {
            layer.msg('请输入正整数！')
        }
        return false;
    });
};