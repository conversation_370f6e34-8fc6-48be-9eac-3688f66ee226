var treeId = 'dpTree';
var THING = 'Thing.Fn.QualityReportTpl';
var ztreeObj;
var curDragNodes, autoExpandNode;
//tree setting
var treeSetting = {
    view: {
        dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
        showLine: true, //是否显示节点之间的连线
        fontCss: {
            'color': 'black'
        }, //字体样式函数
        selectedMulti: false, //设置是否允许同时选中多个节点,
        txtSelectedEnable: true,
        showTitle: true,
        nameIsHTML: true
    },
    async: {
        enable: true,
        url: getTreeUrl(THING, "QueryNodeByPid", ""),
        type: "post",
        autoParam: ["ID"],
        contentType: "application/json;charset=utf-8",
        dataType: 'json',
        dataFilter: function (treeId, parentNode, responseData) {
            if (responseData.success) {
                var datas = responseData.data;
                if (datas.length > 0) {
                    datas = dealDataIcons(datas);
                    datas = dealDataNodeName(datas);
                }
                return datas;
            } else {
                layer.alert(responseData.msg, {
                    icon: 2
                });
            }
        }
    },
    check: {
        enable: false
    },
    edit: {
        enable: true,
        editNameSelectAll: false,
        showRemoveBtn: false,
        showRenameBtn: false,
        removeTitle: "删除",
        renameTitle: "重命名",
        drag: {
            autoExpandTrigger: true,
            prev: dropPrev,
            inner: dropInner,
            next: dropNext
        }
    },
    data: {
        simpleData: { //简单数据模式
            enable: true,
            idKey: "ID",
            pIdKey: "PID",
            rootPId: 0
        },
        key: {
            name: 'TEXT',
            title: '',
            isParent: "ISPARENT"
        }
    },
    callback: {
        // beforeRename: function zTreeBeforeRename(treeId, treeNode, newName, isCancel) {
        // 	return false;
        // },
        beforeDrag: beforeDrag,
        beforeDrop: beforeDrop,
        beforeDragOpen: beforeDragOpen,
        onDrag: onDrag,
        onDrop: onDrop,
        beforeEditName: function (treeId, treeNode) {
            treeNode.NODENAME = getNodeName(treeNode.NODENAME);
            return true;
        },
        // beforeRename:function(){
        // 	return false;
        // },
        // onRename: function(event, treeId, treeNode, isCancel) {
        // 	var parentId = treeNode.PARENTID;
        // 	var nodeName = treeNode.NODENAME;
        // 	twxAjax('Thing.Fn.DataPackage', 'updateNodeName', {
        // 		treeId: treeNode.TREEID,
        // 		newName: nodeName
        // 	}, true, function(data) {
        // 		loadTree();
        // 	});

        // },
        onRemove: function (event, treeId, treeNode) {
        }
    }
};

function dropPrev(treeId, nodes, targetNode) {
    var pNode = targetNode.getParentNode();
    if (pNode && pNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            var curPNode = curDragNodes[i].getParentNode();
            if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function dropInner(treeId, nodes, targetNode) {
    if (targetNode && targetNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            if (!targetNode && curDragNodes[i].dropRoot === false) {
                return false;
            } else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
                .childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function dropNext(treeId, nodes, targetNode) {
    var pNode = targetNode.getParentNode();
    if (pNode && pNode.dropInner === false) {
        return false;
    } else {
        for (var i = 0, l = curDragNodes.length; i < l; i++) {
            var curPNode = curDragNodes[i].getParentNode();
            if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
                return false;
            }
        }
    }
    return true;
}

function beforeDrag(treeId, treeNodes) {
    for (var i = 0, l = treeNodes.length; i < l; i++) {
        if (treeNodes[i].drag === false) {
            curDragNodes = null;
            return false;
        } else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
            curDragNodes = null;
            return false;
        }
    }
    curDragNodes = treeNodes;
    return true;
}

function beforeDragOpen(treeId, treeNode) {
    autoExpandNode = treeNode;
    return true;
}

function beforeDrop(treeId, treeNodes, targetNode, moveType, isCopy) {
    return true;
}

function onDrag(event, treeId, treeNodes) {
}


function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
    if (targetNode != null) {
        var sourceNodeSort = treeNodes[0].SORT;
        var sourceNodeId = treeNodes[0].ID;
        var sourceNodePId = treeNodes[0].PID;
        var sourceNodeName = treeNodes[0].NAME;

        var targetNodeSort = targetNode.SORT;
        var targetNodeId = targetNode.ID;
        var targetNodeName = targetNode.NAME;
        var type = "上面";
        if (sourceNodeSort < targetNodeSort) {
            type = '下面';
        }
        var parentNode = treeNodes[0].getParentNode();
        var allNode = parentNode.children;
        var arr = [];
        for (var i = 1; i <= allNode.length; i++) {
            arr.push(allNode[i - 1].ID + ":" + i);
        }
        var str = arr.join(",");
        twxAjax(THING, 'DragNode', {
            str: str
        }, true, function (res) {
            if (res.success) {
                reloadTree(sourceNodePId, sourceNodeId);
            } else {
                layer.alert(res.msg);
            }
        }, function (data) {
            layer.alert("拖动失败！");
        });
    }
}


/**
 * 添加节点
 * @param obj
 */
function addZTreeNode(obj) {
    var treeObj = $.fn.zTree.getZTreeObj(treeId);
    var parentZNode = treeObj.getSelectedNodes(); //获取父节点
    var newNode = obj;
    newNode.nodeFlg = 1; // 可以自定义节点标识
    newNode = treeObj.addNodes(parentZNode[0], newNode, true);
}

/**
 * 修改子节点
 * @param obj
 */
function editZTreeNode(obj) {
    var zTree = $.fn.zTree.getZTreeObj(treeId);
    var nodes = zTree.getSelectedNodes();
    for (var i = 0; i < nodes.length; i++) {
        nodes[i].name = obj;
        zTree.updateNode(nodes[i]);
    }
}

/**
 *  删除子节点 --选中节点
 * @param obj
 */
function removeZTreeNodeBySelect() {
    var zTree = $.fn.zTree.getZTreeObj(treeId);
    var nodes = zTree.getSelectedNodes(); //获取选中节点
    for (var i = 0; i < nodes.length; i++) {
        zTree.removeNode(nodes[i]);
    }
}

/**
 *  删除子节点 --勾选节点
 * @param obj
 */
function removeZTreeNodeByChecked() {
    var zTree = $.fn.zTree.getZTreeObj(treeId);
    var nodes = zTree.getCheckedNodes(true); //获取勾选节点
    for (var i = 0; i < nodes.length; i++) {
        zTree.removeNode(nodes[i]);
    }
}

/**
 *  根据节点id 批量删除子节点
 * @param obj
 */
function removeZTreeNodebPi(obj) {
    var idnodes = obj.split(",");
    var zTree = $.fn.zTree.getZTreeObj(treeId);
    var nodes = zTree.getSelectedNodes();
    for (var i = 0; i < nodes.length; i++) {
        var nodes = zTree.getNodeByParam("id", nodes[i]);
        zTree.removeNode(nodes);
    }
}

/**
 * 选择节点
 * @param obj
 */
function selectzTreeNode(obj) {
    var zTree = $.fn.zTree.getZTreeObj(treeId);
    var node = zTree.getNodeByParam("id", obj);
    if (node != null) {
        zTree.selectNode(node, true); //指定选中ID的节点
    }
}

/**
 * 对数据进行处理
 * @param {} datas
 */
function dealDataIcons(datas) {
    var imagePrefix = '../dataTree/';
    for (var i = 0; i < datas.length; i++) {
        var dtype = datas[i].TYPE;
        if (dtype === 'root') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/root.png";
        } else if (dtype === 'folder') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/folder.png";
        } else if (dtype === 'product') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/卫星.png";
        } else if (dtype === 'phase') {
            datas[i].drag = false;
            if (datas[i].CODE === 'C') {
                datas[i].icon = imagePrefix + "images/phase.png";
            } else if (datas[i].CODE === 'Z') {
                datas[i].icon = imagePrefix + "images/phase_z.png";
            } else {
                datas[i].icon = imagePrefix + "images/phase.png";
            }
        } else if (dtype === 'dir') {
            datas[i].drag = false;
            datas[i].icon = imagePrefix + "images/dir.png";
        } else if (dtype === 'leaf') {
            datas[i].drag = false;
            var status = 'start';
            if (datas[i].NODESTATUS != undefined && datas[i].NODESTATUS != "undefined") {
                status = datas[i].NODESTATUS;
            }
            datas[i].icon = imagePrefix + "images/" + status + ".png";
            datas[i].childOuter = false;
        } else if (dtype === 'report') {
            datas[i].childOuter = false;
            datas[i].icon = imagePrefix + "images/report.png";
        } else if (dtype.indexOf('table') > -1) {

            datas[i].icon = imagePrefix + "images/table.png";
            if (dtype.indexOf('table_3') > -1) {
                datas[i].dropInner = false;
            } else {
                datas[i].childOuter = false;
            }

        }
    }
    return datas;
}

//处理节点数据名称
function dealDataNodeName(datas) {
    for (var i = 0; i < datas.length; i++) {
        var dtype = datas[i].TYPE;
        if (dtype === 'folder' || dtype === 'dir' || dtype === 'leaf') {
            datas[i].TEXT = dealPrefix(datas[i].SORT) + "-" + datas[i].NAME;
        } else if (dtype === 'report' || dtype.indexOf('table') > -1) {
            var tableNum = datas[i].TABLE_NUM == "请添加序号" ? '<span style="color:red;">请添加序号</span>' : datas[i].TABLE_NUM;
            var tableName = datas[i].NAME == "请填写表名称" ? '<span style="color:red;">请填写表名称</span>' : datas[i].NAME;
            datas[i].TEXT = tableNum + ':' + tableName;
        } else {
            datas[i].TEXT = datas[i].NAME;
        }
    }
    return datas;
}

//去除显示节点前的序号
function getNodeName(name) {
    if (name.indexOf("-") > -1) {
        var arr = name.split("-");
        var arr2 = [];
        for (var i = 1; i < arr.length; i++) {
            arr2.push(arr[i]);
        }
        return arr2.join("-")
    }
    return name;
}

//获取显示节点前的序号
function getNodeNum(name) {
    if (name.indexOf("-") > -1) {
        var arr = name.split("-");
        return arr[0] + "-";
    }
    return "";
}
