{"version": 3, "file": "docx-preview.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;ACVA,wDAA+B;AAC/B,mGAAiE;AACjE,sEAAqC;AACrC,iGAAkE;AAOlE,MAAa,cAAc;IAGvB,YAAoB,IAAW,EAAS,OAA8B;QAAlD,SAAI,GAAJ,IAAI,CAAO;QAAS,YAAO,GAAP,OAAO,CAAuB;QAFtE,cAAS,GAAc,IAAI,sBAAS,EAAE,CAAC;IAGvC,CAAC;IAED,GAAG,CAAC,IAAY;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,IAAY,EAAE,OAAY;QAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAiB,EAAE,OAA8B;QAC/D,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/C,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,OAAY,MAAM;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,CAAC,IAAY,EAAE,OAAyB,QAAQ;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAe,IAAI;QACvC,IAAI,QAAQ,GAAG,aAAa,CAAC;QAE7B,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,qBAAS,EAAC,IAAI,CAAC,CAAC;YAChC,QAAQ,GAAG,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC;SACrC;QAED,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,GAAG,CAAC,CAAC,CAAC,qCAAkB,EAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnG,CAAC;IAGD,gBAAgB,CAAC,GAAW;QACxB,OAAO,+BAAc,EAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAChE,CAAC;CACJ;AA3CD,wCA2CC;AAED,SAAS,aAAa,CAAC,IAAY;IAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACxD,CAAC;;;;;;;;;;;;;;ACzDD,mGAA0D;AAI1D,MAAa,IAAI;IAKb,YAAsB,QAAwB,EAAS,IAAY;QAA7C,aAAQ,GAAR,QAAQ,CAAgB;QAAS,SAAI,GAAJ,IAAI,CAAQ;IACnE,CAAC;IAED,KAAK,CAAC,IAAI;QACZ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE;YACrC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;SAC3B;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IACtC,CAAC;IAED,IAAI;QACA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,mCAAkB,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3E,CAAC;IAES,QAAQ,CAAC,IAAa;IAChC,CAAC;CACJ;AA3BD,oBA2BC;;;;;;;;;;;;;;ACtBD,IAAY,iBAkBX;AAlBD,WAAY,iBAAiB;IACzB,0HAAqG;IACrG,gHAA2F;IAC3F,wGAAmF;IACnF,gHAA2F;IAC3F,0GAAqF;IACrF,mHAA8F;IAC9F,wGAAmF;IACnF,8GAAyF;IACzF,oHAA+F;IAC/F,gHAA2F;IAC3F,gHAA2F;IAC9F,8GAAyF;IACtF,0GAAqF;IACrF,0GAAqF;IACrF,mIAA8G;IAC9G,6HAAwG;IAC3G,iIAA4G;AAC7G,CAAC,EAlBW,iBAAiB,iCAAjB,iBAAiB,QAkB5B;AAED,SAAgB,kBAAkB,CAAC,IAAa,EAAE,GAAc;IAC5D,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAc;QAC7C,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;QACrB,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;QACzB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC;QAC7B,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC;KACxC,EAAC,CAAC;AACP,CAAC;AAPD,gDAOC;;;;;;;;;;;;;;ACpCD,iFAIwB;AAExB,mGAAsG;AACtG,6FAA+E;AAC/E,kGAAsC;AACtC,iFAA4D;AAC5D,mGAA4E;AAG5E,0FAAgF;AAChF,uEAA4C;AAEjC,aAAK,GAAG;IAClB,GAAG,EAAE,SAAS;IACd,KAAK,EAAE,OAAO;IACd,WAAW,EAAE,OAAO;IACpB,SAAS,EAAE,aAAa;CACxB,CAAC;AAEF,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAElC,MAAM,SAAS,GAAG;IACjB,OAAO,EAAE,aAAO,CAAC,OAAO;IACxB,WAAW,EAAE,aAAO,CAAC,gBAAgB;IACrC,GAAG,EAAE,aAAO,CAAC,WAAW;IACxB,MAAM,EAAE,aAAO,CAAC,WAAW;IAC3B,OAAO,EAAE,aAAO,CAAC,eAAe;IAChC,KAAK,EAAE,aAAO,CAAC,YAAY;IAC3B,KAAK,EAAE,aAAO,CAAC,cAAc;IAC7B,KAAK,EAAE,aAAO,CAAC,UAAU;IACzB,KAAK,EAAE,aAAO,CAAC,SAAS;IACxB,GAAG,EAAE,aAAO,CAAC,OAAO;IACpB,MAAM,EAAE,aAAO,CAAC,cAAc;IAC9B,MAAM,EAAE,aAAO,CAAC,YAAY;IAC5B,MAAM,EAAE,aAAO,CAAC,cAAc;IAC9B,KAAK,EAAE,aAAO,CAAC,gBAAgB;IAC/B,KAAK,EAAE,aAAO,CAAC,cAAc;IAC7B,GAAG,EAAE,aAAO,CAAC,YAAY;IACzB,MAAM,EAAE,aAAO,CAAC,OAAO;IACvB,OAAO,EAAE,aAAO,CAAC,gBAAgB;IACjC,KAAK,EAAE,aAAO,CAAC,QAAQ;IACvB,QAAQ,EAAE,aAAO,CAAC,aAAa;IAC/B,GAAG,EAAE,aAAO,CAAC,SAAS;IACtB,IAAI,EAAE,aAAO,CAAC,YAAY;IAC1B,KAAK,EAAE,aAAO,CAAC,MAAM;IACrB,KAAK,EAAE,aAAO,CAAC,MAAM;IACrB,UAAU,EAAE,aAAO,CAAC,YAAY;CAChC;AAOD,MAAa,cAAc;IAG1B,YAAY,OAAwC;QACnD,IAAI,CAAC,OAAO,GAAG;YACd,WAAW,EAAE,KAAK;YAClB,KAAK,EAAE,KAAK;YACZ,GAAG,OAAO;SACV,CAAC;IACH,CAAC;IAED,UAAU,CAAC,MAAe,EAAE,QAAgB,EAAE,SAAc;QAC3D,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,KAAK,IAAI,EAAE,IAAI,oBAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;YAC9C,MAAM,IAAI,GAAG,IAAI,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,EAAE,GAAG,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAC7B,IAAI,CAAC,QAAQ,GAAG,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,iBAAiB,CAAC,MAAe;QAChC,IAAI,KAAK,GAAG,oBAAG,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,UAAU,GAAG,oBAAG,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACnD,IAAI,MAAM,GAAG,oBAAG,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE1C,OAAO;YACN,IAAI,EAAE,aAAO,CAAC,QAAQ;YACtB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YACvC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,oCAAsB,EAAC,MAAM,EAAE,oBAAG,CAAC,CAAC,CAAC,CAAC,EAAuB;YAC7E,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;SAC5D,CAAC;IACH,CAAC;IAED,eAAe,CAAC,IAAa;QAC5B,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE;YACV,MAAM,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;SACnC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,iBAAiB,CAAC,OAAgB;QACjC,IAAI,QAAQ,GAAG,EAAE,CAAC;QAElB,KAAK,IAAI,IAAI,IAAI,oBAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACvC,QAAQ,IAAI,CAAC,SAAS,EAAE;gBACvB,KAAK,GAAG;oBACP,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;oBACzC,MAAM;gBAEP,KAAK,KAAK;oBACT,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;oBACrC,MAAM;gBAEP,KAAK,KAAK;oBACT,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtE,MAAM;aACP;SACD;QAED,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED,eAAe,CAAC,OAAgB;QAC/B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE;YAC5B,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,OAAO;oBACX,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChC,MAAM;gBAEP,KAAK,aAAa;oBACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,kBAAkB,CAAC,IAAa;QAC/B,IAAI,MAAM,GAAc;YACvB,EAAE,EAAE,IAAI;YACR,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,EAAE;SACV,CAAC;QAEF,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,YAAY;oBAChB,IAAI,GAAG,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAEhC,IAAI,GAAG;wBACN,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;4BAClB,MAAM,EAAE,MAAM;4BACd,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,EAAE,CAAC;yBAC5C,CAAC,CAAC;oBACJ,MAAM;gBAEP,KAAK,YAAY;oBAChB,IAAI,GAAG,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAEhC,IAAI,GAAG;wBACN,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;4BAClB,MAAM,EAAE,GAAG;4BACX,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,EAAE,CAAC;yBAC5C,CAAC,CAAC;oBACJ,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,UAAU,CAAC,IAAa;QACvB,IAAI,MAAM,GAAc;YACvB,EAAE,EAAE,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;YAC7B,SAAS,EAAE,oBAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC;YACxC,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,IAAI;SACZ,CAAC;QAEF,QAAQ,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;YAC/B,KAAK,WAAW;gBAAE,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;gBAAC,MAAM;YAC7C,KAAK,OAAO;gBAAE,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC;gBAAC,MAAM;YAC7C,KAAK,WAAW;gBAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;gBAAC,MAAM;SAEhD;QAED,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,SAAS;oBACb,MAAM,CAAC,OAAO,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACpC,MAAM;gBAEP,KAAK,MAAM;oBACV,MAAM,CAAC,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACjC,MAAM;gBAEP,KAAK,MAAM;oBACV,MAAM,CAAC,MAAM,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACnC,MAAM;gBAEP,KAAK,MAAM;oBACV,MAAM,CAAC,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACjC,MAAM;gBAEP,KAAK,SAAS;oBACb,MAAM,CAAC,OAAO,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAC/C,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;wBAClB,MAAM,EAAE,GAAG;wBACX,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM,CAAC,cAAc,GAAG,wCAAwB,EAAC,CAAC,EAAE,oBAAG,CAAC,CAAC;oBACzD,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;wBAClB,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM,CAAC,QAAQ,GAAG,4BAAkB,EAAC,CAAC,EAAE,oBAAG,CAAC,CAAC;oBAC7C,MAAM;gBAEP,KAAK,OAAO,CAAC;gBACb,KAAK,MAAM;oBACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;wBAClB,MAAM,EAAE,IAAI;wBACZ,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,YAAY;oBAChB,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;wBACpC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACvB,MAAM;gBAEP,KAAK,MAAM,CAAC;gBACZ,KAAK,SAAS,CAAC;gBACf,KAAK,QAAQ,CAAC;gBACd,KAAK,YAAY,CAAC;gBAClB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,cAAc,CAAC;gBACpB,KAAK,YAAY;oBAEhB,MAAM;gBAEP;oBACC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;aACnF;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,eAAe,CAAC,IAAa;QAC5B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,QAAQ,IAAI,EAAE;YACb,KAAK,UAAU;gBACd,WAAW,GAAG,YAAY,CAAC;gBAC3B,QAAQ,GAAG,iBAAiB,CAAC;gBAC7B,MAAM;YACP,KAAK,SAAS;gBACb,WAAW,GAAG,WAAW,CAAC;gBAC1B,QAAQ,GAAG,gBAAgB,CAAC;gBAC5B,MAAM;YACP,KAAK,UAAU;gBACd,WAAW,GAAG,YAAY,CAAC;gBAC3B,QAAQ,GAAG,cAAc,CAAC;gBAC1B,MAAM;YACP,KAAK,SAAS;gBACb,WAAW,GAAG,WAAW,CAAC;gBAC1B,QAAQ,GAAG,aAAa,CAAC;gBACzB,MAAM;YACP,KAAK,WAAW;gBACf,WAAW,GAAG,iBAAiB,CAAC;gBAChC,QAAQ,GAAG,YAAY,CAAC;gBACxB,MAAM;YACP,KAAK,WAAW;gBACf,WAAW,GAAG,iBAAiB,CAAC;gBAChC,QAAQ,GAAG,aAAa,CAAC;gBACzB,MAAM;YACP,KAAK,WAAW;gBACf,WAAW,GAAG,iBAAiB,CAAC;gBAChC,QAAQ,GAAG,YAAY,CAAC;gBACxB,MAAM;YACP,KAAK,WAAW;gBACf,WAAW,GAAG,iBAAiB,CAAC;gBAChC,QAAQ,GAAG,aAAa,CAAC;gBACzB,MAAM;YACP,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,KAAK;oBACT,MAAM,CAAC,IAAI,CAAC;wBACX,MAAM,EAAE,GAAG,QAAQ,IAAI;wBACvB,GAAG,EAAE,WAAW;wBAChB,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,IAAI,CAAC;wBACX,MAAM,EAAE,GAAG,QAAQ,OAAO;wBAC1B,GAAG,EAAE,WAAW;wBAChB,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,OAAO,CAAC;gBACb,KAAK,MAAM;oBACV,MAAM,CAAC,IAAI,CAAC;wBACX,MAAM,EAAE,QAAQ;wBAChB,GAAG,EAAE,WAAW;wBAChB,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,kBAAkB,CAAC,KAAc;QAChC,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;YAC1B,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,aAAa;oBACjB,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,OAAO,CAAC;yBACrC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/B,MAAM;gBAEP,KAAK,cAAc;oBAClB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,MAAM;gBAEP,KAAK,KAAK;oBACT,IAAI,KAAK,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;oBACjC,IAAI,aAAa,GAAG,oBAAG,CAAC,WAAW,CAAC,CAAC,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;oBAC/D,OAAO,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;oBAC/B,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IACf,CAAC;IAED,uBAAuB,CAAC,IAAa;QACpC,IAAI,IAAI,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACrC,IAAI,KAAK,GAAG,IAAI,IAAI,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/C,IAAI,SAAS,GAAG,KAAK,IAAI,oBAAG,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAEzD,OAAO,SAAS,CAAC,CAAC,CAAC;YAClB,EAAE,EAAE,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC;YACvC,GAAG,EAAE,oBAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;YAC9B,KAAK,EAAE,oBAAG,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;SAC/B,CAAC,CAAC,CAAC,IAAI,CAAC;IACV,CAAC;IAED,sBAAsB,CAAC,IAAa,EAAE,OAAc;QACnD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,EAAE,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAEzC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,KAAK;oBACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;oBACtD,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,mBAAmB,CAAC,EAAU,EAAE,IAAa,EAAE,OAAc;QAC5D,IAAI,MAAM,GAAkB;YAC3B,EAAE,EAAE,EAAE;YACN,KAAK,EAAE,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;YAChC,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,SAAS;YACrB,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,KAAK;SACX,CAAC;QAEF,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,OAAO;oBACX,MAAM,CAAC,KAAK,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACrC,MAAM;gBAEP,KAAK,KAAK;oBACT,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC9C,MAAM;gBAEP,KAAK,KAAK;oBACT,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC9C,MAAM;gBAEP,KAAK,gBAAgB;oBACpB,IAAI,EAAE,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC/B,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC9C,MAAM;gBAEP,KAAK,SAAS;oBACb,MAAM,CAAC,SAAS,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACtC,MAAM;gBAEP,KAAK,QAAQ;oBACZ,MAAM,CAAC,UAAU,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACvC,MAAM;gBAEP,KAAK,QAAQ;oBACZ,MAAM,CAAC,MAAM,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACnC,MAAM;gBAEP,KAAK,MAAM;oBACV,MAAM,CAAC,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACjC,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,QAAQ,CAAC,IAAa,EAAE,MAAgB;QACvC,MAAM,UAAU,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QACnD,OAAO,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7C,CAAC;IAED,aAAa,CAAC,IAAa,EAAE,YAAsB;QAClD,OAAuB;YACtB,IAAI,EAAE,aAAO,CAAC,QAAQ;YACtB,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE;SAC5C,CAAC;IACH,CAAC;IAED,YAAY,CAAC,IAAa,EAAE,YAAsB;QACjD,OAAuB;YACtB,IAAI,EAAE,aAAO,CAAC,OAAO;YACrB,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE;SAC5C,CAAC;IACH,CAAC;IAED,cAAc,CAAC,IAAa;QAC3B,IAAI,MAAM,GAAiB,EAAE,IAAI,EAAE,aAAO,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAErE,KAAK,IAAI,EAAE,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAClC,QAAQ,EAAE,CAAC,SAAS,EAAE;gBACrB,KAAK,KAAK;oBACT,IAAI,CAAC,wBAAwB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;oBAC1C,MAAM;gBAEP,KAAK,GAAG;oBACP,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;oBAChD,MAAM;gBAEP,KAAK,WAAW;oBACf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;oBACtD,MAAM;gBAEP,KAAK,eAAe;oBACnB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkB,EAAC,EAAE,EAAE,oBAAG,CAAC,CAAC,CAAC;oBAClD,MAAM;gBAEP,KAAK,aAAa;oBACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gCAAgB,EAAC,EAAE,EAAE,oBAAG,CAAC,CAAC,CAAC;oBAChD,MAAM;gBAEP,KAAK,OAAO,CAAC;gBACb,KAAK,WAAW;oBACf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;oBAChD,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACjF,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1E,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzE,MAAM;aACP;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,wBAAwB,CAAC,IAAa,EAAE,SAAuB;QAC9D,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;YACpE,IAAI,sCAAsB,EAAC,CAAC,EAAE,SAAS,EAAE,oBAAG,CAAC;gBAC5C,OAAO,IAAI,CAAC;YAEb,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,QAAQ;oBACZ,SAAS,CAAC,SAAS,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACzC,MAAM;gBAEP,KAAK,UAAU;oBACd,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;oBACpD,MAAM;gBAEP,KAAK,SAAS;oBACb,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;oBAC9B,MAAM;gBAEP,KAAK,KAAK;oBAET,MAAM;gBAEP;oBACC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,IAAa,EAAE,SAAuB;QAChD,IAAI,OAAO,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAExC,IAAI,OAAO,IAAI,MAAM;YACpB,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;IACvC,CAAC;IAED,cAAc,CAAC,IAAa,EAAE,MAAuB;QACpD,IAAI,MAAM,GAA+B,EAAE,IAAI,EAAE,aAAO,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QACnG,IAAI,MAAM,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACtC,IAAI,KAAK,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEjC,IAAI,MAAM;YACT,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC;QAE5B,IAAI,KAAK;YACR,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC;QAEnB,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,GAAG;oBACP,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBAC/C,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,QAAQ,CAAC,IAAa,EAAE,MAAuB;QAC9C,IAAI,MAAM,GAAmB,EAAE,IAAI,EAAE,aAAO,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAEjF,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAElC,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,GAAG;oBACP,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAU;wBAC7B,IAAI,EAAE,aAAO,CAAC,IAAI;wBAClB,IAAI,EAAE,CAAC,CAAC,WAAW;qBACnB,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,SAAS;oBACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAU;wBAC7B,IAAI,EAAE,aAAO,CAAC,WAAW;wBACzB,IAAI,EAAE,CAAC,CAAC,WAAW;qBACnB,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,WAAW;oBACf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAiB;wBACpC,IAAI,EAAE,aAAO,CAAC,WAAW;wBACzB,WAAW,EAAE,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;wBACjC,IAAI,EAAE,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;wBACpC,KAAK,EAAE,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC;qBACtC,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,WAAW;oBACf,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACvB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAqB;wBACxC,IAAI,EAAE,aAAO,CAAC,WAAW;wBACzB,IAAI,EAAE,CAAC,CAAC,WAAW;qBACnB,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,SAAS;oBACb,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACvB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAe;wBAClC,IAAI,EAAE,aAAO,CAAC,YAAY;wBAC1B,QAAQ,EAAE,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,CAAC;wBACpC,IAAI,EAAE,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;wBACpC,KAAK,EAAE,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC;qBACtC,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,eAAe;oBACnB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAO,CAAC,aAAa,EAAE,CAAC,CAAC;oBACtD,MAAM;gBAEP,KAAK,IAAI;oBACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAW;wBAC9B,IAAI,EAAE,aAAO,CAAC,KAAK;wBACnB,KAAK,EAAE,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,cAAc;qBAC5C,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,uBAAuB;oBAC3B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAW;wBAC9B,IAAI,EAAE,aAAO,CAAC,KAAK;wBACnB,KAAK,EAAE,uBAAuB;qBAC9B,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAY;wBAC/B,IAAI,EAAE,aAAO,CAAC,MAAM;wBACpB,IAAI,EAAE,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;wBACzB,IAAI,EAAE,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;qBACzB,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAO,CAAC,GAAG,EAAE,CAAC,CAAC;oBAC5C,MAAM;gBAEP,KAAK,mBAAmB;oBACvB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAmB;wBACtC,IAAI,EAAE,aAAO,CAAC,iBAAiB;wBAC/B,EAAE,EAAE,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;qBACrB,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,kBAAkB;oBACtB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAmB;wBACtC,IAAI,EAAE,aAAO,CAAC,gBAAgB;wBAC9B,EAAE,EAAE,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;qBACrB,CAAC,CAAC;oBACH,MAAM;gBAEP,KAAK,SAAS;oBACb,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBAE7B,IAAI,CAAC;wBACJ,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;oBACvB,MAAM;gBAEP,KAAK,MAAM;oBACV,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,MAAM;gBAEP,KAAK,KAAK;oBACT,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;oBACnC,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,gBAAgB,CAAC,IAAa;QAC7B,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC;QACvC,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAoB,CAAC;QAEnF,KAAK,MAAM,EAAE,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,SAAS,GAAG,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;YAE1C,IAAI,SAAS,EAAE;gBACd,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;aAChD;iBAAM,IAAI,EAAE,CAAC,SAAS,IAAI,GAAG,EAAE;gBAC/B,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC5B,GAAG,CAAC,IAAI,GAAG,aAAO,CAAC,MAAM,CAAC;gBAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC1B;iBAAM,IAAI,EAAE,CAAC,SAAS,IAAI,QAAQ,EAAE;gBACpC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;aAC3C;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,kBAAkB,CAAC,IAAa;QAC/B,MAAM,MAAM,GAAwB,EAAE,CAAC;QAEvC,KAAK,MAAM,EAAE,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACpC,QAAQ,EAAE,CAAC,SAAS,EAAE;gBACrB,KAAK,KAAK;oBAAE,MAAM,CAAC,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAAC,MAAM;gBACrD,KAAK,QAAQ;oBAAE,MAAM,CAAC,qBAAqB,GAAG,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAAC,MAAM;gBACzE,KAAK,KAAK;oBAAE,MAAM,CAAC,QAAQ,GAAG,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAAC,MAAM;gBACzD,KAAK,SAAS;oBAAE,MAAM,CAAC,UAAU,GAAG,oBAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAAC,MAAM;gBACnE,KAAK,QAAQ;oBAAE,MAAM,CAAC,SAAS,GAAG,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAAC,MAAM;gBAC7D,KAAK,QAAQ;oBAAE,MAAM,CAAC,OAAO,GAAG,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBAAC,MAAM;aAC3D;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,kBAAkB,CAAC,IAAa,EAAE,GAAW;QAC5C,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;YAC9D,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,QAAQ;oBACZ,GAAG,CAAC,SAAS,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACnC,MAAM;gBAEP,KAAK,WAAW;oBACf,GAAG,CAAC,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBACrD,MAAM;gBAEP;oBACC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,eAAe,CAAC,IAAa;QAC5B,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,aAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAE1D,KAAK,MAAM,EAAE,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,KAAK,GAAG,yBAAe,EAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACxC,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACrC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,qBAAqB,CAAC,IAAa;QAClC,IAAI,IAAI,CAAC,SAAS,IAAI,kBAAkB;YACvC,OAAO,IAAI,CAAC;QAEb,IAAI,MAAM,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAEzC,IAAI,MAAM,EAAE;YACX,IAAI,QAAQ,GAAG,oBAAG,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAC5C,IAAI,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAErD,IAAI,sBAAsB,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAChD,OAAO,MAAM,CAAC,iBAAiB,CAAC;SACjC;QAED,OAAO,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,iBAAiB,CAAC;IACzD,CAAC;IAED,YAAY,CAAC,IAAa;QACzB,KAAK,IAAI,CAAC,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACjC,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,QAAQ,CAAC;gBACd,KAAK,QAAQ;oBACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;aACpC;SACD;IACF,CAAC;IAED,mBAAmB,CAAC,IAAa;QAChC,IAAI,MAAM,GAAmB,EAAE,IAAI,EAAE,aAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QACnF,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC;QAQ1C,IAAI,QAAQ,GAA2C,IAAI,CAAC;QAC5D,IAAI,SAAS,GAAG,oBAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEhD,IAAI,IAAI,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;QAC5D,IAAI,IAAI,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;QAE3D,KAAK,IAAI,CAAC,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACjC,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,WAAW;oBACf,IAAI,SAAS,EAAE;wBACd,IAAI,CAAC,MAAM,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;wBACtD,IAAI,CAAC,MAAM,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;qBACtD;oBACD,MAAM;gBAEP,KAAK,QAAQ;oBACZ,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;oBACpE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;oBACrE,MAAM;gBAEP,KAAK,WAAW,CAAC;gBACjB,KAAK,WAAW;oBACf,IAAI,CAAC,SAAS,EAAE;wBACf,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;wBACnD,IAAI,SAAS,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;wBACxC,IAAI,UAAU,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;wBAE7C,GAAG,CAAC,QAAQ,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,cAAc,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;wBAE3D,IAAI,SAAS;4BACZ,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC;wBAEnC,IAAI,UAAU;4BACb,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;qBAC7D;oBACD,MAAM;gBAEP,KAAK,kBAAkB;oBACtB,QAAQ,GAAG,kBAAkB,CAAC;oBAC9B,MAAM;gBAEP,KAAK,UAAU;oBACd,QAAQ,GAAG,UAAU,CAAC;oBACtB,MAAM;gBAEP,KAAK,SAAS;oBACb,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBAE7B,IAAI,CAAC;wBACJ,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACzB,MAAM;aACP;SACD;QAED,IAAI,QAAQ,IAAI,kBAAkB,EAAE;YACnC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;YAErC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACf,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;aAClC;SACD;aACI,IAAI,QAAQ,IAAI,UAAU,EAAE;YAChC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;YACjC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;YAElC,IAAI,IAAI,CAAC,MAAM;gBACd,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;YACvC,IAAI,IAAI,CAAC,MAAM;gBACd,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;SACtC;aACI,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,EAAE;YACrE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;SACtC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,YAAY,CAAC,IAAa;QACzB,IAAI,WAAW,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAEnD,KAAK,IAAI,CAAC,IAAI,oBAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACxC,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,KAAK;oBACT,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;aAC7B;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,YAAY,CAAC,IAAa;QACzB,IAAI,MAAM,GAAc,EAAE,IAAI,EAAE,aAAO,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QACvE,IAAI,QAAQ,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC7C,IAAI,IAAI,GAAG,oBAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEzC,MAAM,CAAC,GAAG,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAErC,IAAI,IAAI,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACrC,IAAI,IAAI,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAErC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;QAEzC,KAAK,IAAI,CAAC,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACjC,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;oBACpE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;oBACrE,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;oBAClE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,oBAAW,CAAC,GAAG,CAAC,CAAC;oBACjE,MAAM;aACP;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,UAAU,CAAC,IAAa;QACvB,IAAI,MAAM,GAAa,EAAE,IAAI,EAAE,aAAO,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAE7D,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,IAAI;oBACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,MAAM;gBAEP,KAAK,SAAS;oBACb,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;oBAC3C,MAAM;gBAEP,KAAK,OAAO;oBACX,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;oBACrC,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,iBAAiB,CAAC,IAAa;QAC9B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,SAAS;oBACb,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC/C,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,oBAAoB,CAAC,IAAa,EAAE,KAAe;QAClD,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;QAErB,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE;YACtE,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,UAAU;oBACd,KAAK,CAAC,SAAS,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACrC,MAAM;gBAEP,KAAK,SAAS;oBACb,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;oBAC/C,MAAM;gBAEP,KAAK,QAAQ;oBACZ,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAClC,MAAM;gBAEP,KAAK,qBAAqB;oBACzB,KAAK,CAAC,WAAW,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC1C,MAAM;gBAEP,KAAK,qBAAqB;oBACzB,KAAK,CAAC,WAAW,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC1C,MAAM;gBAEP;oBACC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;QAEH,QAAQ,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACrC,KAAK,QAAQ;gBACZ,OAAO,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBACpC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC;gBACvC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC;gBACxC,MAAM;YAEP,KAAK,OAAO;gBACX,OAAO,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBACpC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC;gBACvC,MAAM;SACP;IACF,CAAC;IAED,kBAAkB,CAAC,IAAa,EAAE,KAAe;QAChD,IAAI,WAAW,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACtD,IAAI,cAAc,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAC5D,IAAI,aAAa,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAC1D,IAAI,YAAY,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAExD,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;QACjC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,cAAc,CAAC,CAAC;QAClG,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,YAAY,CAAC,CAAC;QAC5F,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,aAAa,CAAC,CAAC;QAC/F,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,WAAW,CAAC,CAAC;IAC1F,CAAC;IAED,aAAa,CAAC,IAAa;QAC1B,IAAI,MAAM,GAAgB,EAAE,IAAI,EAAE,aAAO,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAE9D,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,IAAI;oBACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,MAAM;gBAEP,KAAK,MAAM;oBACV,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;oBACxC,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,uBAAuB,CAAC,IAAa,EAAE,GAAgB;QACtD,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;YAC9D,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,UAAU;oBACd,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;oBAC9C,MAAM;gBAEP,KAAK,WAAW;oBACf,GAAG,CAAC,QAAQ,GAAG,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACtC,MAAM;gBAEP;oBACC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,cAAc,CAAC,IAAa;QAC3B,IAAI,MAAM,GAAiB,EAAE,IAAI,EAAE,aAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAEhE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,KAAK;oBACT,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzC,MAAM;gBAEP,KAAK,GAAG;oBACP,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,MAAM;gBAEP,KAAK,MAAM;oBACV,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;oBACzC,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IACf,CAAC;IAED,wBAAwB,CAAC,IAAa,EAAE,IAAkB;QACzD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;YAC/D,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,UAAU;oBACd,IAAI,CAAC,IAAI,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;oBACxC,MAAM;gBAEP,KAAK,QAAQ;oBACZ,IAAI,CAAC,aAAa,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,UAAU,CAAC;oBACtD,MAAM;gBAEP,KAAK,UAAU;oBACd,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;oBAC/C,MAAM;gBAEP;oBACC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,sBAAsB,CAAC,IAAa,EAAE,QAAgC,IAAI,EAAE,aAAqC,IAAI,EAAE,UAAsC,IAAI;QAChK,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;QAEpB,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC;gBACf,OAAO;YAER,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,IAAI;oBACR,KAAK,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC1C,MAAM;gBAEP,KAAK,eAAe;oBACnB,KAAK,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBACzD,MAAM;gBAEP,KAAK,OAAO;oBACX,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,aAAK,CAAC,KAAK,CAAC,CAAC;oBAChE,MAAM;gBAEP,KAAK,IAAI;oBACR,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,oBAAW,CAAC,QAAQ,CAAC,CAAC;oBAC1F,MAAM;gBAEP,KAAK,KAAK;oBACT,KAAK,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,aAAK,CAAC,GAAG,CAAC,CAAC;oBAC1E,MAAM;gBAEP,KAAK,WAAW;oBACf,KAAK,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,aAAK,CAAC,SAAS,CAAC,CAAC;oBAC/E,MAAM;gBAEP,KAAK,WAAW;oBAGf,MAAM;gBAEP,KAAK,UAAU;oBACd,KAAK,CAAC,aAAa,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,oBAAW,CAAC,QAAQ,CAAC,CAAC;oBACrE,MAAM;gBAEP,KAAK,KAAK;oBACT,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW;wBAC3B,MAAM;gBAER,KAAK,MAAM;oBACV,KAAK,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC5C,MAAM;gBAEP,KAAK,UAAU;oBACd,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC7B,MAAM;gBAEP,KAAK,QAAQ;oBACZ,KAAK,CAAC,iBAAiB,CAAC,GAAG,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM;oBACjF,MAAM;gBAEP,KAAK,GAAG;oBACP,KAAK,CAAC,aAAa,CAAC,GAAG,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;oBACxE,MAAM;gBAEP,KAAK,GAAG;oBACP,KAAK,CAAC,YAAY,CAAC,GAAG,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;oBACzE,MAAM;gBAEP,KAAK,MAAM;oBACV,KAAK,CAAC,gBAAgB,CAAC,GAAG,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;oBAC9E,MAAM;gBAEP,KAAK,WAAW;oBACf,KAAK,CAAC,gBAAgB,CAAC,GAAG,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;oBAC9E,MAAM;gBAEP,KAAK,GAAG;oBACP,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC9B,MAAM;gBAEP,KAAK,KAAK,CAAC;gBACX,KAAK,QAAQ;oBACZ,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAChC,MAAM;gBAEP,KAAK,QAAQ;oBACZ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACzB,MAAM;gBAEP,KAAK,YAAY;oBAChB,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,UAAU,IAAI,KAAK,CAAC,CAAC;oBACnD,MAAM;gBAEP,KAAK,gBAAgB;oBACpB,KAAK,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAClD,KAAK,CAAC,iBAAiB,CAAC,GAAG,UAAU,CAAC;oBACtC,MAAM;gBAEP,KAAK,MAAM;oBACV,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACrC,MAAM;gBAEP,KAAK,KAAK;oBACT,KAAK,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAC1C,MAAM;gBAEP,KAAK,WAAW;oBACf,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACrC,MAAM;gBAEP,KAAK,QAAQ;oBACZ,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;wBAC/B,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;oBAC3B,MAAM;gBAEP,KAAK,MAAM;oBAGV,MAAM;gBAEP,KAAK,QAAQ;oBAGZ,MAAM;gBAEP,KAAK,YAAY,CAAC;gBAClB,KAAK,OAAO;oBACX,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,UAAU,IAAI,KAAK,CAAC,CAAC;oBACnD,MAAM;gBAEP,KAAK,WAAW;oBACf,KAAK,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;oBACnD,MAAM;gBAEP,KAAK,QAAQ;oBACZ,KAAK,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBACzD,MAAM;gBAEP,KAAK,SAAS;oBACb,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK;wBAC1B,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC7B,MAAM;gBAEP,KAAK,UAAU;oBACd,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC;wBACzB,KAAK,CAAC,eAAe,CAAC,GAAG,YAAY,CAAC;oBACvC,MAAM;gBAEP,KAAK,qBAAqB;oBACzB,KAAK,CAAC,SAAS,CAAC,GAAG,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;oBAClE,MAAM;gBAEP,KAAK,MAAM;oBACV,KAAK,CAAC,OAAO,CAAC,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACpC,MAAM;gBAEP,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK,CAAC;gBACX,KAAK,MAAM,CAAC;gBACZ,KAAK,MAAM,CAAC;gBACZ,KAAK,YAAY,CAAC;gBAClB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,WAAW,CAAC;gBACjB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,WAAW,CAAC;gBACjB,KAAK,UAAU,CAAC;gBAChB,KAAK,cAAc,CAAC;gBACpB,KAAK,MAAM,CAAC;gBACZ,KAAK,KAAK,CAAC;gBACX,KAAK,SAAS;oBAEb,MAAM;gBAEP;oBACC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK;wBACrB,OAAO,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;oBAClF,MAAM;aACP;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACd,CAAC;IAED,cAAc,CAAC,IAAa,EAAE,KAA6B;QAC1D,IAAI,GAAG,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAEhC,IAAI,GAAG,IAAI,IAAI;YACd,OAAO;QAER,QAAQ,GAAG,EAAE;YACZ,KAAK,MAAM,CAAC;YACZ,KAAK,iBAAiB,CAAC;YACvB,KAAK,cAAc,CAAC;YACpB,KAAK,aAAa,CAAC;YACnB,KAAK,UAAU,CAAC;YAChB,KAAK,eAAe,CAAC;YACrB,KAAK,SAAS,CAAC;YACf,KAAK,YAAY;gBAChB,KAAK,CAAC,iBAAiB,CAAC,GAAG,kBAAkB,CAAC;gBAC9C,MAAM;YAEP,KAAK,QAAQ,CAAC;YACd,KAAK,aAAa;gBACjB,KAAK,CAAC,iBAAiB,CAAC,GAAG,kBAAkB,CAAC;gBAC9C,MAAM;YAEP,KAAK,QAAQ;gBACZ,KAAK,CAAC,iBAAiB,CAAC,GAAG,kBAAkB,CAAC;gBAC9C,MAAM;YAEP,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO;gBACX,KAAK,CAAC,iBAAiB,CAAC,GAAG,WAAW,CAAC;gBACvC,MAAM;YAEP,KAAK,MAAM,CAAC;YACZ,KAAK,YAAY,CAAC;YAClB,KAAK,WAAW;gBACf,KAAK,CAAC,iBAAiB,CAAC,GAAG,gBAAgB,CAAC;gBAC5C,MAAM;YAEP,KAAK,OAAO;gBACX,KAAK,CAAC,iBAAiB,CAAC,GAAG,WAAW,CAAC;gBACvC,MAAM;YAEP,KAAK,MAAM;gBACV,KAAK,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC;gBAClC,MAAM;SACP;QAED,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAE3C,IAAI,GAAG;YACN,KAAK,CAAC,uBAAuB,CAAC,GAAG,GAAG,CAAC;IACvC,CAAC;IAED,SAAS,CAAC,IAAa,EAAE,KAA6B;QACrD,IAAI,KAAK,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACpC,IAAI,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAEvD,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1D,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;YACnB,KAAK,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED,gBAAgB,CAAC,IAAa,EAAE,KAA6B;QAC5D,IAAI,SAAS,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAClD,IAAI,OAAO,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC9C,IAAI,IAAI,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,KAAK,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,KAAK,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,GAAG,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAEtC,IAAI,SAAS;YAAE,KAAK,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;QAChD,IAAI,OAAO;YAAE,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC;QAClD,IAAI,IAAI,IAAI,KAAK;YAAE,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;QACxD,IAAI,KAAK,IAAI,GAAG;YAAE,KAAK,CAAC,cAAc,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC;IACxD,CAAC;IAED,YAAY,CAAC,IAAa,EAAE,KAA6B;QACxD,IAAI,MAAM,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC5C,IAAI,KAAK,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,IAAI,GAAG,oBAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC3C,IAAI,QAAQ,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAE1C,IAAI,MAAM;YAAE,KAAK,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;QACzC,IAAI,KAAK;YAAE,KAAK,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC;QAE1C,IAAI,IAAI,KAAK,IAAI,EAAE;YAClB,QAAQ,QAAQ,EAAE;gBACjB,KAAK,MAAM;oBACV,KAAK,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;oBACpD,MAAM;gBAEP,KAAK,SAAS;oBACb,KAAK,CAAC,aAAa,CAAC,GAAG,eAAe,IAAI,GAAG,EAAE,KAAK,CAAC;oBACrD,MAAM;gBAEP;oBACC,KAAK,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,IAAI;oBAC7D,MAAM;aACP;SACD;IACF,CAAC;IAED,qBAAqB,CAAC,IAAa,EAAE,MAA8B;QAClE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,MAAM;oBACV,MAAM,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBACjD,MAAM;gBAEP,KAAK,OAAO;oBACX,MAAM,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAClD,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAChD,MAAM;gBAEP,KAAK,QAAQ;oBACZ,MAAM,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBACnD,MAAM;aACP;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,IAAa,EAAE,MAA8B;QAC1D,QAAQ,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YAChC,KAAK,OAAO;gBACX,MAAM,CAAC,QAAQ,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC/C,MAAM;YAEP,KAAK,SAAS,CAAC;YACf;gBACC,MAAM,CAAC,QAAQ,CAAC,GAAG,oBAAG,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAG/C,MAAM;SACP;IACF,CAAC;IAED,qBAAqB,CAAC,IAAa,EAAE,MAA8B;QAClE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;YACzB,QAAQ,CAAC,CAAC,SAAS,EAAE;gBACpB,KAAK,OAAO,CAAC;gBACb,KAAK,MAAM;oBACV,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAChD,MAAM;gBAEP,KAAK,KAAK,CAAC;gBACX,KAAK,OAAO;oBACX,MAAM,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBACjD,MAAM;gBAEP,KAAK,KAAK;oBACT,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAC/C,MAAM;gBAEP,KAAK,QAAQ;oBACZ,MAAM,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAClD,MAAM;aACP;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CACD;AAt2CD,wCAs2CC;AAED,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AAE1M,MAAM,OAAO;IACZ,MAAM,CAAC,OAAO,CAAC,IAAa,EAAE,EAAwB;QACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAE3B,IAAI,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY;gBAClC,EAAE,CAAU,CAAC,CAAC,CAAC;SAChB;IACF,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,IAAa,EAAE,QAAgB,EAAE,WAAmB,IAAI,EAAE,YAAoB,OAAO;QACrG,IAAI,CAAC,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAEjC,IAAI,CAAC,EAAE;YACN,IAAI,CAAC,IAAI,MAAM,EAAE;gBAChB,OAAO,SAAS,CAAC;aACjB;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACnC,OAAO,CAAC,CAAC;aACT;YAED,OAAO,IAAI,CAAC,EAAE,CAAC;SACf;QAED,IAAI,UAAU,GAAG,oBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAE9C,OAAO,UAAU,CAAC,CAAC,CAAC,cAAc,UAAU,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;IAClE,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,IAAa,EAAE,OAAwB,oBAAW,CAAC,GAAG;QACtE,OAAO,0BAAa,EAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;CACD;AAED,MAAM,MAAM;IACX,MAAM,CAAC,UAAU,CAAC,CAAU,EAAE,IAAY;QACzC,IAAI,GAAG,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC5B,OAAO,GAAG,CAAC,CAAC,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,CAAU,EAAE,IAAY;QAC1C,IAAI,IAAI,GAAG,oBAAW,CAAC,GAAG,CAAC;QAE3B,QAAQ,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;YAC5B,KAAK,KAAK,CAAC,CAAC,MAAM;YAClB,KAAK,KAAK;gBAAE,IAAI,GAAG,oBAAW,CAAC,OAAO,CAAC;gBAAC,MAAM;YAC9C,KAAK,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC;SAC3B;QAED,OAAO,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,CAAU;QAC9B,OAAO,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,CAAU;QAC9B,IAAI,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAE9B,IAAI,IAAI,IAAI,KAAK;YAChB,OAAO,MAAM,CAAC;QAEf,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,IAAI,GAAG,oBAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAW,CAAC,MAAM,CAAC,CAAC;QAEvD,OAAO,GAAG,IAAI,UAAU,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,aAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IACvE,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,CAAU;QACjC,IAAI,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC9B,OAAO,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,CAAU;QACpC,MAAM,GAAG,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC/B,MAAM,OAAO,GAAG;YACf,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU;YAChD,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU;YAC5C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;SAC1C,CAAC;QAEF,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,CAAU;QAC1B,IAAI,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAE9B,QAAQ,IAAI,EAAE;YACb,KAAK,OAAO,CAAC;YACb,KAAK,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC;YAC3B,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,KAAK,KAAK,CAAC;YACX,KAAK,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC;YAC7B,KAAK,MAAM,CAAC,CAAC,OAAO,SAAS,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,CAAU,EAAE,YAAqB,KAAK;QAC7D,IAAI,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAE9B,QAAQ,IAAI,EAAE;YACb,KAAK,WAAW,CAAC,CAAC,OAAO,KAAK,CAAC;YAC/B,KAAK,aAAa,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;SACvD;QAED,OAAO,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,CAAU;QACrC,IAAI,IAAI,GAAG,oBAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAE9B,QAAQ,IAAI,EAAE;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,UAAU,CAAC,CAAC,OAAO,UAAU,CAAC;YACnC,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC;YACzB,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;SAC/B;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,CAAS,EAAE,CAAS;QAClC,IAAI,CAAC,IAAI,IAAI;YAAE,OAAO,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,IAAI;YAAE,OAAO,CAAC,CAAC;QAExB,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;IAC5B,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,CAAU;QACnC,MAAM,GAAG,GAAG,oBAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;YAAE,SAAS,IAAI,YAAY,CAAC;QAC7E,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;YAAE,SAAS,IAAI,WAAW,CAAC;QAC3E,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;YAAE,SAAS,IAAI,YAAY,CAAC;QAChF,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;YAAE,SAAS,IAAI,WAAW,CAAC;QAC9E,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;YAAE,SAAS,IAAI,WAAW,CAAC;QAC3E,IAAI,oBAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;YAAE,SAAS,IAAI,WAAW,CAAC;QAE3E,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;CACD;;;;;;;;;;;;;;ACnjDD,iFAAsC;AACtC,mGAAoE;AAEpE,MAAa,aAAc,SAAQ,WAAI;IAGnC,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,KAAK,GAAG,+BAAc,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC/D,CAAC;CACJ;AAND,sCAMC;;;;;;;;;;;;;;ACID,SAAgB,cAAc,CAAC,IAAa,EAAE,SAAoB;IAC9D,MAAM,MAAM,GAAyB,EAAE,CAAC;IAExC,KAAK,IAAI,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACrC,QAAQ,EAAE,CAAC,SAAS,EAAE;YAClB,KAAK,OAAO;gBAAE,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC;gBAAC,MAAM;YACnD,KAAK,aAAa;gBAAE,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC;gBAAC,MAAM;YAC/D,KAAK,SAAS;gBAAE,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC;gBAAC,MAAM;YACvD,KAAK,SAAS;gBAAE,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC;gBAAC,MAAM;YACvD,KAAK,UAAU;gBAAE,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzD,KAAK,UAAU;gBAAE,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzD,KAAK,gBAAgB;gBAAE,MAAM,CAAC,cAAc,GAAG,EAAE,CAAC,WAAW,CAAC;gBAAC,MAAM;YACrE,KAAK,UAAU;gBAAE,EAAE,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;gBAAC,MAAM;SAC1F;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAjBD,wCAiBC;;;;;;;;;;;;;;AC9BD,iFAAsC;AACtC,yGAAkE;AAElE,MAAa,eAAgB,SAAQ,WAAI;IAGrC,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,KAAK,GAAG,mCAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACjE,CAAC;CACJ;AAND,0CAMC;;;;;;;;;;;;;;ACAD,SAAgB,gBAAgB,CAAC,IAAa,EAAE,GAAc;IAC7D,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QAC7C,MAAM,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;QAEhC,OAAO;YACN,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;YAC9B,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;YACzB,IAAI,EAAE,UAAU,CAAC,QAAQ;YACzB,KAAK,EAAE,UAAU,CAAC,WAAW;SAC7B,CAAC;IACH,CAAC,CAAC,CAAC;AACJ,CAAC;AAXD,4CAWC;;;;;;;;;;;;;;ACpBD,iFAAsC;AACtC,+GAAgF;AAEhF,MAAa,iBAAkB,SAAQ,WAAI;IAGvC,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,KAAK,GAAG,uCAAkB,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACnE,CAAC;CACJ;AAND,8CAMC;;;;;;;;;;;;;;ACMD,SAAgB,kBAAkB,CAAC,IAAa,EAAE,SAAoB;IAClE,MAAM,MAAM,GAA6B,EAExC,CAAC;IAEF,KAAK,IAAI,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACrC,QAAQ,EAAE,CAAC,SAAS,EAAE;YAClB,KAAK,UAAU;gBACX,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC;gBACjC,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBAC9C,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBAC9C,MAAM;YACV,KAAK,YAAY;gBACb,MAAM,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,aAAa;gBACd,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC;gBACpC,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBAC9C,MAAM;YACV,KAAK,YAAY;gBACb,MAAM,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,SAAS;gBACV,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC;gBAChC,MAAM;YACV,KAAK,YAAY;gBACb,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC;gBACnC,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAtCD,gDAsCC;AAED,SAAS,cAAc,CAAC,KAAa;IACjC,IAAI,OAAO,KAAK,KAAK,WAAW;QAC5B,OAAO;IACX,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;;;;;;;;;;;;;;AC1DD,wEAAgD;AAahD,SAAgB,kBAAkB,CAAC,IAAa,EAAE,GAAc;IAC5D,OAAO;QACH,IAAI,EAAE,aAAO,CAAC,aAAa;QAC3B,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;QACxB,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;QAC5B,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC;QACvC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC;KACxC;AACL,CAAC;AARD,gDAQC;AAED,SAAgB,gBAAgB,CAAC,IAAa,EAAE,GAAc;IAC1D,OAAO;QACH,IAAI,EAAE,aAAO,CAAC,WAAW;QACzB,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;KAC3B;AACL,CAAC;AALD,4CAKC;;;;;;;;;;;;;;AC5BD,iFAA+C;AAkB/C,SAAgB,WAAW,CAAC,IAAa,EAAE,GAAc;IACrD,OAAO;QACH,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;QAC3B,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;QAC9B,IAAI,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,oBAAW,CAAC,MAAM,CAAC;QACpD,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,oBAAW,CAAC,KAAK,CAAC;QACxD,KAAK,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;QAClC,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC;KACvC,CAAC;AACN,CAAC;AATD,kCASC;AAED,SAAgB,YAAY,CAAC,IAAa,EAAE,GAAc;IACtD,IAAI,MAAM,GAAY,EAAE,CAAC;IAEzB,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,MAAM;gBAAE,MAAM,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;YACtD,KAAK,KAAK;gBAAE,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;YACpD,KAAK,OAAO;gBAAE,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;YACxD,KAAK,QAAQ;gBAAE,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;SAC7D;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAbD,oCAaC;;;;;;;;;;;;;;ACzCY,UAAE,GAAG;IACd,MAAM,EAAE,8DAA8D;IACtE,SAAS,EAAE,uDAAuD;IAClE,OAAO,EAAE,0DAA0D;IACtE,aAAa,EAAE,6DAA6D;IAC5E,IAAI,EAAE,4DAA4D;CAClE;AAiBY,mBAAW,GAAoC;IACxD,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;IAC9B,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE;IACnC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;IAClC,MAAM,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE;IAClC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;IAC7B,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE;IACjC,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;IACtC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE;CACvC;AAED,SAAgB,aAAa,CAAC,GAAW,EAAE,QAAyB,mBAAW,CAAC,GAAG;IAE/E,IAAI,GAAG,IAAI,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC3C,OAAO,GAAG,CAAC;KACd;IAEJ,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AACjE,CAAC;AAPD,sCAOC;AAED,SAAgB,cAAc,CAAC,CAAS,EAAE,YAAY,GAAG,KAAK;IAC1D,QAAQ,CAAC,EAAE;QACP,KAAK,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC;QACtB,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC;QACvB,KAAK,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC;QACvB,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC;QACzB,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;QACzB,KAAK,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC;QAC3B,OAAO,CAAC,CAAC,OAAO,YAAY,CAAC;KAChC;AACL,CAAC;AAVD,wCAUC;AAED,SAAgB,iBAAiB,CAAC,GAAW;IACzC,OAAO,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;AAC5C,CAAC;AAFD,8CAEC;AAED,SAAgB,mBAAmB,CAAC,IAAa,EAAE,KAAuB,EAAE,GAAc;IACtF,IAAG,IAAI,CAAC,YAAY,IAAI,UAAE,CAAC,MAAM;QAC7B,OAAO,KAAK,CAAC;IAEjB,QAAO,IAAI,CAAC,SAAS,EAAE;QACnB,KAAK,OAAO;YACR,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACpC,MAAM;QAEV,KAAK,IAAI;YACL,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,mBAAW,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM;QAEV;YACI,OAAO,KAAK,CAAC;KACpB;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAlBD,kDAkBC;;;;;;;;;;;;;;AC9ED,iFAAsC;AAItC,MAAa,YAAa,SAAQ,WAAI;IAGlC,YAAY,GAAmB,EAAE,IAAY,EAAE,MAAsB;QACjE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IAClC,CAAC;IAID,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;CACJ;AAbD,oCAaC;;;;;;;;;;;;;;AClBD,IAAY,OAyDX;AAzDD,WAAY,OAAO;IACf,gCAAqB;IACrB,kCAAuB;IACvB,sBAAW;IACX,0BAAe;IACf,0CAA+B;IAC/B,0BAAe;IACf,sBAAW;IACX,wBAAa;IACb,kCAAuB;IACvB,8BAAmB;IACnB,0BAAe;IACf,wBAAa;IACb,sBAAW;IACX,4BAAiB;IACjB,0CAA+B;IAC/B,sCAA2B;IAC3B,4BAAiB;IACjB,4BAAiB;IACjB,kDAAuC;IAC1C,gDAAqC;IAClC,gCAAqB;IACrB,8BAAmB;IACnB,sCAA2B;IAC3B,wCAA6B;IAC7B,sCAA2B;IAC9B,oCAAyB;IACzB,8BAAmB;IACnB,gDAAqC;IACrC,sCAA2B;IAC3B,sCAA2B;IAC3B,8CAAmC;IACnC,wCAA6B;IAC7B,4CAAiC;IACjC,oCAAyB;IACzB,8BAAmB;IACnB,kCAAuB;IACvB,4CAAiC;IACjC,wCAA6B;IAC7B,4CAAiC;IACjC,4CAAiC;IACjC,gDAAqC;IACrC,8BAAmB;IACnB,wCAA6B;IAC7B,4BAAiB;IACjB,gDAAqC;IACrC,gCAAqB;IACrB,0CAA+B;IAC/B,kCAAuB;IACvB,wCAA6B;IAC7B,4BAAiB;IACjB,4BAAiB;IACjB,wCAA6B;IAC7B,oCAAyB;IACzB,gCAAqB;IACrB,8BAAmB;IACnB,sCAA2B;AAC5B,CAAC,EAzDW,OAAO,uBAAP,OAAO,QAyDlB;AAcD,MAAsB,kBAAkB;IAAxC;QAEI,aAAQ,GAAsB,EAAE,CAAC;QACjC,aAAQ,GAA4B,EAAE,CAAC;IAO3C,CAAC;CAAA;AAVD,gDAUC;;;;;;;;;;;;;;ACvED,SAAgB,gBAAgB,CAAC,IAAa,EAAE,GAAc;IAC1D,OAAO;QACH,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;QACtC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;QACpC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;QAC/B,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;KACxB,CAAC;AACrB,CAAC;AAPD,4CAOC;;;;;;;;;;;;;;AChBD,iFAA6E;AAE7E,oFAAsE;AACtE,mGAA+D;AAE/D,wEAA0D;AAiC1D,SAAgB,wBAAwB,CAAC,IAAa,EAAE,GAAc;IAClE,IAAI,MAAM,GAAwB,EAAE,CAAC;IAErC,KAAI,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,sBAAsB,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;KAC3C;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AARD,4DAQC;AAED,SAAgB,sBAAsB,CAAC,IAAa,EAAE,KAA0B,EAAE,GAAc;IAC5F,IAAI,IAAI,CAAC,YAAY,IAAI,WAAE,CAAC,MAAM;QAC9B,OAAO,KAAK,CAAC;IAEjB,IAAG,gCAAmB,EAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;QACpC,OAAO,IAAI,CAAC;IAEhB,QAAQ,IAAI,CAAC,SAAS,EAAE;QACpB,KAAK,MAAM;YACP,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAClC,MAAM;QAEV,KAAK,QAAQ;YACT,KAAK,CAAC,YAAY,GAAG,oCAAsB,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACvD,MAAM;QAEV,KAAK,OAAO;YACR,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC5C,MAAM;QAEV,KAAK,SAAS;YACV,KAAK,CAAC,WAAW,GAAG,mCAAgB,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAChD,OAAO,KAAK,CAAC;YACb,MAAM;QAEV,KAAK,eAAe;YAChB,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC;YACb,MAAM;QAEV,KAAK,WAAW;YACZ,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAClD,MAAM;QAEV,KAAK,UAAU;YACX,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACjD,MAAM;QAEV,KAAK,iBAAiB;YAClB,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACxD,MAAM;QAEV,KAAK,YAAY;YACb,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM;QAEV,KAAK,QAAQ;YACT,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACxC,MAAM;QAEV,KAAK,KAAK;YACN,KAAK,CAAC,QAAQ,GAAG,4BAAkB,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC/C,MAAM;QAEV;YACI,OAAO,KAAK,CAAC;KACpB;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AA3DD,wDA2DC;AAED,SAAgB,SAAS,CAAC,IAAa,EAAE,GAAc;IACnD,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;SAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAc;QACpB,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC;QAClC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC;QAC7B,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC;KAC5B,EAAC,CAAC;AACX,CAAC;AAPD,8BAOC;AAED,SAAgB,cAAc,CAAC,IAAa,EAAE,GAAc;IACxD,IAAI,MAAM,GAAuB,EAAE,CAAC;IAEpC,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,OAAO;gBACR,MAAM,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC/B,MAAM;YAEV,KAAK,MAAM;gBACP,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACrC,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAhBD,wCAgBC;;;;;;;;;;;;;;ACtID,iFAAiE;AAajE,SAAgB,kBAAkB,CAAC,IAAa,EAAE,GAAc;IAC5D,IAAI,MAAM,GAAkB,EAAE,CAAC;IAE/B,KAAI,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,gBAAgB,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;KACrC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AARD,gDAQC;AAED,SAAgB,gBAAgB,CAAC,IAAa,EAAE,KAAoB,EAAE,GAAc;IAChF,IAAI,gCAAmB,EAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;QACrC,OAAO,IAAI,CAAC;IAEhB,OAAO,KAAK,CAAC;AACjB,CAAC;AALD,4CAKC;;;;;;;;;;;;;;AC7BD,mGAAkE;AAClE,iFAAiD;AAyCjD,IAAY,WAMX;AAND,WAAY,WAAW;IACnB,wCAAyB;IACzB,oCAAqB;IACrB,wCAAyB;IACzB,oCAAqB;IACrB,kCAAmB;AACvB,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AAmBD,SAAgB,sBAAsB,CAAC,IAAa,EAAE,MAAiB,oBAAe;IAClF,IAAI,OAAO,GAAsB,EAAE,CAAC;IAEpC,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,MAAM;gBACP,OAAO,CAAC,QAAQ,GAAG;oBACf,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC;oBAC7B,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC;oBAC9B,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC;iBACrC;gBACD,MAAM;YAEV,KAAK,MAAM;gBACP,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAClC,MAAM;YAEV,KAAK,OAAO;gBACR,OAAO,CAAC,WAAW,GAAG;oBAClB,IAAI,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC;oBAC/B,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC;oBACjC,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC;oBAC7B,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC;oBACnC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC;oBACnC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC;oBACnC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC;iBACtC,CAAC;gBACF,MAAM;YAEV,KAAK,MAAM;gBACP,OAAO,CAAC,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACvC,MAAM;YAEV,KAAK,iBAAiB;gBAClB,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3F,MAAM;YAEV,KAAK,iBAAiB;gBAClB,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC3F,MAAM;YAEV,KAAK,SAAS;gBACV,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBACjD,MAAM;YAEV,KAAK,WAAW;gBACZ,OAAO,CAAC,WAAW,GAAG,yBAAY,EAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC3C,MAAM;YAEV,KAAK,WAAW;gBACZ,OAAO,CAAC,UAAU,GAAG,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC7C,MAAM;SACb;KACJ;IAED,OAAO,OAAO,CAAC;AACnB,CAAC;AAxDD,wDAwDC;AAED,SAAS,YAAY,CAAC,IAAa,EAAE,GAAc;IAC/C,OAAO;QACH,eAAe,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;QACzC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;QACpC,SAAS,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;QACpC,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC;QAClD,OAAO,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;aAC7B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAQ;YACd,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC;YAC7B,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC;SACpC,EAAC;KACT,CAAC;AACN,CAAC;AAED,SAAS,eAAe,CAAC,IAAa,EAAE,GAAc;IAClD,OAAO;QACH,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;QAClC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC;QACtC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;QAC7B,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;KACpC,CAAC;AACN,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAa,EAAE,GAAc;IAC7D,OAAO;QACH,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;QACxB,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;KAC/B;AACL,CAAC;;;;;;;;;;;;;;ACzJD,6FAA+C;AAC/C,mGAAmD;AACnD,6FAA+C;AAqBlC,sBAAc,GAAY;IACnC,YAAY,EAAE,KAAK;IACnB,WAAW,EAAE,KAAK;IAClB,WAAW,EAAE,KAAK;IAClB,UAAU,EAAE,IAAI;IAChB,KAAK,EAAE,KAAK;IACZ,YAAY,EAAE,KAAK;IACnB,SAAS,EAAE,MAAM;IACjB,SAAS,EAAE,IAAI;IACf,kBAAkB,EAAE,IAAI;IACxB,2BAA2B,EAAE,IAAI;IACjC,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE,IAAI;IACxB,cAAc,EAAE,IAAI;IACpB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,KAAK;CACpB;AAED,SAAgB,UAAU,CAAC,IAAgB,EAAE,WAA8B;IACvE,MAAM,GAAG,GAAG,EAAE,GAAG,sBAAc,EAAE,GAAG,WAAW,EAAE,CAAC;IAClD,OAAO,4BAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,gCAAc,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AACjE,CAAC;AAHD,gCAGC;AAED,SAAgB,cAAc,CAAC,QAAa,EAAE,aAA0B,EAAE,cAA4B,EAAE,WAA8B;IAClI,MAAM,GAAG,GAAG,EAAE,GAAG,sBAAc,EAAE,GAAG,WAAW,EAAE,CAAC;IAClD,MAAM,QAAQ,GAAG,IAAI,4BAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACtD,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC;AAC/D,CAAC;AAJD,wCAIC;AAEM,KAAK,UAAU,WAAW,CAAC,IAAgB,EAAE,aAA0B,EAAE,cAA4B,EAAE,WAA8B;IAC3I,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAChD,cAAc,CAAC,GAAG,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;IAC7D,OAAO,GAAG,CAAC;AACf,CAAC;AAJD,kCAIC;;;;;;;;;;;;;;ACzDD,iFAAsC;AACtC,gFAAsD;AAEtD,MAAa,aAAc,SAAQ,WAAI;IAGnC,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,KAAK,GAAG,sBAAU,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;CACJ;AAND,sCAMC;;;;;;;;;;;;;;ACPD,MAAM,gBAAgB,GAAG;IACrB,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,MAAM;IACjB,WAAW,EAAE,QAAQ;IACrB,eAAe,EAAE,YAAY;CAChC;AAeD,SAAgB,UAAU,CAAC,IAAa,EAAE,GAAc;IACpD,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5D,CAAC;AAFD,gCAEC;AAED,SAAgB,SAAS,CAAC,IAAa,EAAE,GAAc;IACnD,IAAI,MAAM,GAAoB;QAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;QAC5B,aAAa,EAAE,EAAE;KACpB,CAAC;IAEF,KAAK,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/B,QAAQ,EAAE,CAAC,SAAS,EAAE;YAClB,KAAK,QAAQ;gBACT,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBACpC,MAAM;YAEV,KAAK,SAAS;gBACV,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBACrC,MAAM;YAEV,KAAK,cAAc,CAAC;YACpB,KAAK,WAAW,CAAC;YACjB,KAAK,aAAa,CAAC;YACnB,KAAK,iBAAiB;gBAClB,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;gBACtD,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AA1BD,8BA0BC;AAED,SAAgB,iBAAiB,CAAC,IAAa,EAAE,GAAc;IAC3D,OAAO;QACH,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;QACxB,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;QAC9B,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC;KACzC,CAAC;AACN,CAAC;AAND,8CAMC;;;;;;;;;;;;;;AC5DD,kFAA8D;AAE9D,MAAa,SAAU,SAAQ,wBAAkB;IAAjD;;QACI,SAAI,GAAY,aAAO,CAAC,MAAM,CAAC;IACnC,CAAC;CAAA;AAFD,8BAEC;AAED,MAAa,SAAU,SAAQ,wBAAkB;IAAjD;;QACI,SAAI,GAAY,aAAO,CAAC,MAAM,CAAC;IACnC,CAAC;CAAA;AAFD,8BAEC;;;;;;;;;;;;;;ACPD,iFAAsC;AAGtC,4FAAkD;AAElD,MAAsB,oBAAgE,SAAQ,WAAI;IAK9F,YAAY,GAAmB,EAAE,IAAY,EAAE,MAAsB;QACjE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IAClC,CAAC;IAED,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7E,CAAC;CAGJ;AAhBD,oDAgBC;AAED,MAAa,UAAW,SAAQ,oBAA+B;IACjD,iBAAiB;QACvB,OAAO,IAAI,oBAAS,EAAE,CAAC;IAC3B,CAAC;CACJ;AAJD,gCAIC;AAED,MAAa,UAAW,SAAQ,oBAA+B;IACjD,iBAAiB;QACvB,OAAO,IAAI,oBAAS,EAAE,CAAC;IAC3B,CAAC;CACJ;AAJD,gCAIC;;;;;;;;;;;;;;ACjCD,iFAGwB;AAKxB,qEAA+E;AAC/E,oFAAkE;AAYlE,MAAM,EAAE,GAAG;IACV,GAAG,EAAE,4BAA4B;IACjC,MAAM,EAAE,oCAAoC;CAC5C;AASD,MAAa,YAAY;IAwBxB,YAAmB,YAAsB;QAAtB,iBAAY,GAAZ,YAAY,CAAU;QAtBzC,cAAS,GAAW,MAAM,CAAC;QAI3B,aAAQ,GAA8B,EAAE,CAAC;QACzC,gBAAW,GAAS,IAAI,CAAC;QAEzB,wBAAmB,GAA4B,EAAE,CAAC;QAClD,yBAAoB,GAA0B,IAAI,CAAC;QACnD,uBAAkB,GAAc,EAAE,CAAC;QACnC,wBAAmB,GAAY,IAAI,CAAC;QAEpC,gBAAW,GAAgC,EAAE,CAAC;QAC9C,eAAU,GAAgC,EAAE,CAAC;QAE7C,sBAAiB,GAAa,EAAE,CAAC;QACjC,yBAAoB,GAAU,EAAE,CAAC;QAGjC,gBAAW,GAAU,EAAE,CAAC;QACxB,gBAAW,GAAQ,CAAC,CAAC;QA+wCrB,kBAAa,GAAG,aAAa,CAAC;IA5wC9B,CAAC;IAED,MAAM,CAAC,QAAsB,EAAE,aAA0B,EAAE,iBAA8B,IAAI,EAAE,OAAgB;QAC9G,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC;QAC/E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,cAAc,GAAG,cAAc,IAAI,aAAa,CAAC;QAEjD,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAClC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAEjC,aAAa,CAAC,cAAc,EAAE,kCAAkC,CAAC,CAAC;QAClE,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAEtD,IAAI,QAAQ,CAAC,SAAS,EAAE;YACvB,aAAa,CAAC,cAAc,EAAE,8BAA8B,CAAC,CAAC;YAC9D,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;SACrD;QAED,IAAI,QAAQ,CAAC,UAAU,IAAI,IAAI,EAAE;YAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAE/D,aAAa,CAAC,cAAc,EAAE,wBAAwB,CAAC,CAAC;YACxD,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;SAC1E;QAED,IAAI,QAAQ,CAAC,aAAa,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YAE7D,aAAa,CAAC,cAAc,EAAE,kCAAkC,CAAC,CAAC;YAClE,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,CAAC;SAEvG;QAED,IAAI,QAAQ,CAAC,aAAa,EAAE;YAC3B,IAAI,CAAC,WAAW,GAAG,iBAAK,EAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SAClE;QAED,IAAI,QAAQ,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,iBAAK,EAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SAChE;QAED,IAAI,QAAQ,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,cAAc,CAAC;SACrE;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,QAAQ,CAAC,aAAa;YACjD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QAE9D,IAAI,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAEtE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC3B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC;SAC/D;aAAM;YACN,cAAc,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IACxB,CAAC;IAED,WAAW,CAAC,SAAoB,EAAE,cAA2B;QAC5D,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC;QAE/C,IAAI,UAAU,EAAE;YACf,IAAI,UAAU,CAAC,SAAS,EAAE;gBACzB,SAAS,CAAC,wBAAwB,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,aAAa,CAAC;aACzE;YAED,IAAI,UAAU,CAAC,SAAS,EAAE;gBACzB,SAAS,CAAC,wBAAwB,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,aAAa,CAAC;aACzE;SACD;QAED,MAAM,WAAW,GAAG,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC;QAEjD,IAAI,WAAW,EAAE;YAChB,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBACtD,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;aACzC;SACD;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC;QACpE,cAAc,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,eAAe,CAAC,SAAwB,EAAE,cAA2B;QACpE,KAAK,IAAI,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE;YAC9B,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC,aAAa,EAAE;gBAChC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBACvD,MAAM,SAAS,GAAG;wBACjB,aAAa,EAAE,CAAC,CAAC,IAAI;wBACrB,KAAK,EAAE,OAAO,QAAQ,GAAG;qBACzB,CAAC;oBAEF,IAAI,GAAG,CAAC,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,YAAY,EAAE;wBACnD,SAAS,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC;qBAClC;oBAED,IAAI,GAAG,CAAC,IAAI,IAAI,QAAQ,IAAI,GAAG,CAAC,IAAI,IAAI,YAAY,EAAE;wBACrD,SAAS,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC;qBACnC;oBAED,aAAa,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC;oBACvD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;oBAC5D,cAAc,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;oBACxD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;aACH;SACD;IACF,CAAC;IAED,gBAAgB,CAAC,SAAiB;QACjC,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,2BAAe,EAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;IACvF,CAAC;IAED,aAAa,CAAC,MAAmB;QAChC,MAAM,SAAS,GAAG,iBAAK,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAErE,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;YAClD,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEzC,IAAI,SAAS,EAAE;gBACd,KAAK,CAAC,cAAc,GAAG,qBAAS,EAAC,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;gBACjF,KAAK,CAAC,QAAQ,GAAG,qBAAS,EAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAE/D,KAAK,MAAM,UAAU,IAAI,SAAS,CAAC,MAAM,EAAE;oBAC1C,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;oBAE1E,IAAI,WAAW,EAAE;wBAChB,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;qBAChE;yBAAM;wBACN,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;qBACvE;iBACD;aACD;iBACI,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK;gBAC1B,OAAO,CAAC,IAAI,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACxD;QAED,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;YACzB,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SAChD;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,iBAAiB,CAAC,UAA2B;QAC5C,KAAK,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAE7C,IAAI,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE;gBACrC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;aACjD;SACD;IACF,CAAC;IAED,cAAc,CAAC,OAAuB;QACrC,IAAI,OAAO,CAAC,QAAQ,EAAE;YACrB,KAAK,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAC/B,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC;gBAEnB,IAAI,CAAC,CAAC,IAAI,IAAI,aAAO,CAAC,KAAK,EAAE;oBAC5B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;iBACrB;qBACI;oBACJ,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;iBACvB;aACD;SACD;IACF,CAAC;IAED,YAAY,CAAC,KAAe;QAC3B,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE;YAC7B,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;gBACzB,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE;oBAClE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe;oBAC5D,cAAc,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB;iBAChE,CAAC,CAAC;gBAEH,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;aACvB;SACD;IACF,CAAC;IAED,mBAAmB,CAAC,KAA6B,EAAE,MAA8B,EAAE,QAAkB,IAAI;QACxG,IAAI,CAAC,KAAK;YACT,OAAO,MAAM,CAAC;QAEf,IAAI,MAAM,IAAI,IAAI;YAAE,MAAM,GAAG,EAAE,CAAC;QAChC,IAAI,KAAK,IAAI,IAAI;YAAE,KAAK,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAE7D,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;YACtB,IAAI,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC;gBAC3D,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;SAC1B;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,aAAa,CAAC,SAAiB,EAAE,KAAwB;QACxD,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAExD,IAAI,KAAK,EAAE;YACV,IAAI,KAAK,CAAC,WAAW,EAAE;gBACtB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;gBAChD,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;gBAClD,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC;gBAC9C,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;aACpD;YAED,IAAI,KAAK,CAAC,QAAQ,EAAE;gBACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;oBAC5B,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY;oBAC7B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;aAC9C;YAED,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE;gBACnD,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC5D,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;gBAE3C,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE;oBAC5B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,iBAAiB,CAAC;iBAC1C;aACD;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,cAAc,CAAC,QAAyB;QACvC,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;YAE7B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC;YAClD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAE1D,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,EAC5E,MAAM,CAAC,MAAM,EAAE,SAAS,IAAI,KAAK,EAAE,cAAc,CAAC,CAAC;YAEpD,IAAI,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YACtD,cAAc,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YAE3C,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;aAC5E;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;aAC1E;YAED,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,EAC5E,MAAM,CAAC,MAAM,EAAE,SAAS,IAAI,KAAK,EAAE,cAAc,CAAC,CAAC;YAEpD,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5B,SAAS,GAAG,KAAK,CAAC;SAClB;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,kBAAkB,CAAC,IAA6B,EAAE,KAAwB,EAAE,IAAY,EAAE,cAAuB,EAAE,IAAiB;QACnI,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,IAAI,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;eACpF,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;eACzD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,SAAS,CAAC,CAAC;QAExC,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAyB,CAAC;QAE5G,IAAI,IAAI,EAAE;YACT,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACtC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC1C;YACD,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAkB,CAAC;YAE5E,IAAI,KAAK,EAAE,WAAW,EAAE;gBACvB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,aAAO,CAAC,MAAM,EAAE;oBAC7C,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,KAAK,CAAC,WAAW,CAAC,MAAM,MAAM,KAAK,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC;oBACpF,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,KAAK,CAAC,WAAW,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;iBACpF;qBACI,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,aAAO,CAAC,MAAM,EAAE;oBAClD,EAAE,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,KAAK,CAAC,WAAW,CAAC,MAAM,MAAM,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;oBAC1F,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,KAAK,CAAC,WAAW,CAAC,MAAM,MAAM,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;iBACvF;aACD;YAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SACxB;IACF,CAAC;IAED,kBAAkB,CAAC,IAAoB;QACtC,IAAI,IAAI,CAAC,IAAI,IAAI,aAAO,CAAC,KAAK;YAC7B,OAAO,KAAK,CAAC;QAEd,IAAK,IAAiB,CAAC,KAAK,IAAI,uBAAuB;YACtD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC;QAElD,OAAQ,IAAiB,CAAC,KAAK,IAAI,MAAM,CAAC;IAC3C,CAAC;IAED,cAAc,CAAC,QAA0B;QACxC,IAAI,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;QAChD,IAAI,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC;QAEvB,KAAK,IAAI,IAAI,IAAI,QAAQ,EAAE;YAC1B,IAAI,IAAI,CAAC,IAAI,IAAI,aAAO,CAAC,SAAS,EAAE;gBACnC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAE,IAAqB,CAAC,SAAS,CAAC,CAAC;gBAE3D,IAAI,CAAC,EAAE,cAAc,EAAE,eAAe,EAAE;oBACvC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;oBAC9B,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrB;aACD;YAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5B,IAAI,IAAI,CAAC,IAAI,IAAI,aAAO,CAAC,SAAS,EAAE;gBACnC,MAAM,CAAC,GAAG,IAAoB,CAAC;gBAE/B,IAAI,SAAS,GAAG,CAAC,CAAC,YAAY,CAAC;gBAC/B,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;gBACrB,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;gBAErB,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,QAAQ,EAAE;oBAC1C,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;wBACtC,WAAW,GAAG,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC9E,OAAO,WAAW,IAAI,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC;iBACH;gBAED,IAAI,SAAS,IAAI,WAAW,IAAI,CAAC,CAAC,EAAE;oBACnC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;oBAC9B,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrB;gBAED,IAAI,WAAW,IAAI,CAAC,CAAC,EAAE;oBACtB,IAAI,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBACvC,IAAI,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;oBAE1D,IAAI,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,EAAE;wBACpD,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;wBAC7B,IAAI,YAAY,GAAG,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;wBACtE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;wBAC/C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAEpC,IAAI,QAAQ,EAAE;4BACb,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC;4BACpC,IAAI,MAAM,GAAG,EAAE,GAAG,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC;4BAC1E,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAC3B,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;yBACnD;qBACD;iBACD;aACD;SACD;QAED,IAAI,gBAAgB,GAAG,IAAI,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,EAAE;gBAChC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,gBAAgB,CAAC;aACvC;iBAAM;gBACN,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;aACtC;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,aAAa,CAAC,QAAuB;QACpC,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,SAAS,UAAU,EAAE,EAAE,QAAQ,CAAC,CAAC;IACxF,CAAC;IAED,kBAAkB;QACjB,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QACvB,IAAI,SAAS,GAAG;GACf,CAAC;GACD,CAAC,oBAAoB,CAAC;GACtB,CAAC;UACM,CAAC;UACD,CAAC;UACD,CAAC;GACR,CAAC;GACD,CAAC,eAAe,CAAC;GACjB,CAAC;GACD,CAAC;GACD,CAAC;CACH,CAAC;QAEA,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAmED,eAAe,CAAC,UAA2B,EAAE,cAA2B;QACvE,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,aAAa,GAAG,EAAE,CAAC;QAEvB,KAAK,IAAI,GAAG,IAAI,UAAU,EAAE;YAC3B,IAAI,QAAQ,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7D,IAAI,aAAa,GAAG,MAAM,CAAC;YAE3B,IAAI,GAAG,CAAC,MAAM,EAAE;gBACf,IAAI,QAAQ,GAAG,KAAK,IAAI,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;gBAErE,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,QAAQ,SAAS,EAAE;oBACrD,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,cAAc;oBACzB,YAAY,EAAE,OAAO,QAAQ,GAAG;iBAChC,EAAE,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAErB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC5D,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,MAAM,QAAQ,SAAS,IAAI,KAAK,CAAC;oBAChE,cAAc,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC,CAAC;aACH;iBACI,IAAI,GAAG,CAAC,SAAS,EAAE;gBACvB,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBACvD,MAAM,YAAY,GAAG,OAAO,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACrD,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE;oBAClB,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAClF,eAAe,EAAE,YAAY;qBAC7B,CAAC,CAAC;iBACH;gBAED,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAEjC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,QAAQ,SAAS,EAAE;oBACrD,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACzG,mBAAmB,EAAE,OAAO;oBAC5B,GAAG,GAAG,CAAC,MAAM;iBACb,CAAC,CAAC;aACH;iBACI;gBACJ,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aACrD;YAED,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;gBACzC,SAAS,EAAE,WAAW;gBACtB,qBAAqB,EAAE,QAAQ;gBAC/B,iBAAiB,EAAE,aAAa;gBAChC,GAAG,GAAG,CAAC,MAAM;aACb,CAAC,CAAC;SACH;QAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE;gBAClD,eAAe,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC;aACxC,CAAC,CAAC;SACH;QAED,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAED,YAAY,CAAC,MAAmB;QAC/B,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,MAAM,YAAY,GAAG,iBAAK,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAE3E,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC3B,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;YAE7B,IAAI,KAAK,CAAC,MAAM,EAAE;gBACjB,IAAI,WAAW,GAAG,KAAK,CAAC,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAE1D,IAAI,WAAW;oBACd,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;qBAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK;oBAC1B,OAAO,CAAC,IAAI,CAAC,2BAA2B,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;aACzD;YAED,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAEjC,IAAI,QAAQ,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAExD,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM;oBAClC,QAAQ,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBAEnC,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK;oBACtC,QAAQ,GAAG,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,QAAQ,CAAC;gBAE9D,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;aAC3D;SACD;QAED,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAED,WAAW,CAAC,OAAiB,EAAE,QAAqC,EAAE,IAAiB;QACtF,IAAI,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAE3D,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SACzB;IACF,CAAC;IAED,aAAa,CAAC,IAAoB;QACjC,QAAQ,IAAI,CAAC,IAAI,EAAE;YAClB,KAAK,aAAO,CAAC,SAAS;gBACrB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAoB,CAAC,CAAC;YAEnD,KAAK,aAAO,CAAC,aAAa;gBACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAwB,CAAC,CAAC;YAE3D,KAAK,aAAO,CAAC,WAAW;gBACvB,OAAO,IAAI,CAAC;YAEb,KAAK,aAAO,CAAC,GAAG;gBACf,OAAO,IAAI,CAAC,SAAS,CAAC,IAAc,CAAC,CAAC;YAEvC,KAAK,aAAO,CAAC,KAAK;gBACjB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAE/B,KAAK,aAAO,CAAC,GAAG;gBACf,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAElC,KAAK,aAAO,CAAC,IAAI;gBAChB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEnC,KAAK,aAAO,CAAC,SAAS;gBACrB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEnC,KAAK,aAAO,CAAC,OAAO;gBACnB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAEjC,KAAK,aAAO,CAAC,KAAK;gBACjB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAiB,CAAC,CAAC;YAE5C,KAAK,aAAO,CAAC,IAAI;gBAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAe,CAAC,CAAC;YAEzC,KAAK,aAAO,CAAC,IAAI;gBAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAe,CAAC,CAAC;YAEzC,KAAK,aAAO,CAAC,WAAW;gBACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAe,CAAC,CAAC;YAEhD,KAAK,aAAO,CAAC,GAAG;gBACf,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAE7B,KAAK,aAAO,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAiB,CAAC,CAAC;YAE7C,KAAK,aAAO,CAAC,KAAK;gBACjB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAgB,CAAC,CAAC;YAE3C,KAAK,aAAO,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAE7C,KAAK,aAAO,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAE7C,KAAK,aAAO,CAAC,QAAQ,CAAC;YACtB,KAAK,aAAO,CAAC,OAAO;gBACnB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAEzC,KAAK,aAAO,CAAC,iBAAiB;gBAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAwB,CAAC,CAAC;YAE/D,KAAK,aAAO,CAAC,gBAAgB;gBAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAwB,CAAC,CAAC;YAE9D,KAAK,aAAO,CAAC,aAAa;gBACzB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAElC,KAAK,aAAO,CAAC,UAAU;gBACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAEpC,KAAK,aAAO,CAAC,UAAU;gBACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAkB,CAAC,CAAC;YAElD,KAAK,aAAO,CAAC,OAAO;gBACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;YAE9E,KAAK,aAAO,CAAC,gBAAgB;gBAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAE3C,KAAK,aAAO,CAAC,WAAW;gBACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEzD,KAAK,aAAO,CAAC,OAAO;gBACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,aAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAE7D,KAAK,aAAO,CAAC,YAAY,CAAC;YAC1B,KAAK,aAAO,CAAC,cAAc,CAAC;YAC5B,KAAK,aAAO,CAAC,WAAW,CAAC;YACzB,KAAK,aAAO,CAAC,QAAQ,CAAC;YACtB,KAAK,aAAO,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAExD,KAAK,aAAO,CAAC,YAAY;gBACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEtC,KAAK,aAAO,CAAC,aAAa;gBACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAE1D,KAAK,aAAO,CAAC,SAAS;gBACrB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAE1D,KAAK,aAAO,CAAC,YAAY;gBACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAEvD,KAAK,aAAO,CAAC,UAAU;gBACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAEpC,KAAK,aAAO,CAAC,cAAc;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAExD,KAAK,aAAO,CAAC,YAAY;gBACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAExD,KAAK,aAAO,CAAC,SAAS,CAAC;YACvB,KAAK,aAAO,CAAC,gBAAgB,CAAC;YAC9B,KAAK,aAAO,CAAC,cAAc;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAEtD,KAAK,aAAO,CAAC,eAAe;gBAC3B,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAEtD,KAAK,aAAO,CAAC,YAAY;gBACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEtC,KAAK,aAAO,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAEhC,KAAK,aAAO,CAAC,OAAO;gBACnB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAEjC,KAAK,aAAO,CAAC,cAAc;gBAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAExC,KAAK,aAAO,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAEhC,KAAK,aAAO,CAAC,gBAAgB;gBAC5B,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAEjC,KAAK,aAAO,CAAC,QAAQ;gBACpB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAElC,KAAK,aAAO,CAAC,OAAO;gBACnB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SACjC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,cAAc,CAAC,IAAoB,EAAE,IAAc;QAClD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,cAAc,CAAC,KAAuB,EAAE,IAAc;QACrD,IAAI,KAAK,IAAI,IAAI;YAChB,OAAO,IAAI,CAAC;QAEb,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;QAE9E,IAAI,IAAI;YACP,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAE9B,OAAO,MAAM,CAAC;IACf,CAAC;IAED,eAAe,CAAC,IAAoB,EAAE,OAAoC,EAAE,KAA2B;QACtG,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,iBAAiB,CAAC,IAAoB,EAAE,EAAU,EAAE,OAAe,EAAE,KAA2B;QAC/F,OAAO,eAAe,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,eAAe,CAAC,IAAkB;QACjC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAErC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,KAAT,IAAI,CAAC,IAAI,GAAK,KAAK,EAAE,cAAc,EAAE,IAAI,EAAC;QAE1C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEhD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,KAAK,EAAE,cAAc,EAAE,SAAS,CAAC;QAErE,IAAI,SAAS,EAAE;YACd,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SACzE;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,mBAAmB,CAAC,KAAU,EAAE,KAAoB;QACnD,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,sBAAsB,CAAC,KAAU,EAAE,KAAuB;QACzD,IAAI,KAAK,IAAI,IAAI;YAChB,OAAO;QAER,IAAI,KAAK,CAAC,KAAK,EAAE;YAChB,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;SAC7B;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE;YACnB,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;SACpC;IACF,CAAC;IAED,eAAe,CAAC,IAAkB;QACjC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAErC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,IAAI,EAAE;YACd,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;SACxB;aAAM,IAAG,IAAI,CAAC,EAAE,EAAE;YAClB,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI;iBACzC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAI,GAAG,GAAG,EAAE,MAAM,CAAC;SAC1B;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,aAAa,CAAC,IAAoB;QACjC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEvC,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC;QACtC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;QACnC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;QAEhC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,OAAO,MAAM,CAAC;IACf,CAAC;IAED,WAAW,CAAC,IAAe;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEvC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBACpE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;SACH;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,UAAU,CAAC,IAAa;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED,iBAAiB,CAAC,IAAa;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACzF,CAAC;IAED,WAAW,CAAC,IAAc;QACzB,IAAI,IAAI,CAAC,KAAK,IAAI,cAAc,EAAE;YACjC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAChC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,cAAc,CAAC,IAAoB;QAClC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa;YAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAE1C,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,aAAa,CAAC,IAAoB;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa;YAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAE1C,OAAO,IAAI,CAAC;IACb,CAAC;IAED,YAAY,CAAC,IAAe;QAC3B,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,GAAG;QACnC,OAAO,IAAI,CAAC;IACb,CAAC;IAED,uBAAuB,CAAC,IAAsB;QAC7C,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,MAAM,CAAC,WAAW,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QACzD,OAAO,MAAM,CAAC;IACf,CAAC;IAED,sBAAsB,CAAC,IAAsB;QAC5C,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM,CAAC,WAAW,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;QACxD,OAAO,MAAM,CAAC;IACf,CAAC;IAED,SAAS,CAAC,IAAoB;QAC7B,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE7B,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC9B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACxC,IAAI,KAAK,GAAG,UAAU,CAAe,IAAI,EAAE,aAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC;YACpE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;SAChD;QAED,OAAO,OAAO,CAAC;IAChB,CAAC;IAED,mBAAmB,CAAC,IAAsB;QACzC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,OAAO,MAAM,CAAC;IACf,CAAC;IAED,SAAS,CAAC,IAAY;QACrB,IAAI,IAAI,CAAC,QAAQ;YAChB,OAAO,IAAI,CAAC;QAEb,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,EAAE;YACV,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAErB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAoB,CAAC,CAAC;YAC9D,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACnC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC5B;aACI;YACJ,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SAClC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,WAAW,CAAC,IAAc;QACzB,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACvD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACzD,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO;YACf,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAE3D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;QAC3D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC;QAEzD,OAAO,MAAM,CAAC;IACf,CAAC;IAED,kBAAkB,CAAC,OAAyB;QAC3C,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAE5C,KAAK,IAAI,GAAG,IAAI,OAAO,EAAE;YACxB,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAExC,IAAI,GAAG,CAAC,KAAK;gBACZ,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YAEjC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC5B;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,cAAc,CAAC,IAAoB;QAClC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,CAAC;QAEjC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;QAE/B,OAAO,MAAM,CAAC;IACf,CAAC;IAED,eAAe,CAAC,IAAkB;QACjC,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEtC,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;QAEzC,IAAI,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE;gBACpC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;gBACxC,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;aACnB;iBAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE;gBAC1C,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC;gBAC5C,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;aAC9B;SACD;aAAM;YACN,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;SACtC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,IAAI;YACZ,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAE5B,IAAI,CAAC,mBAAmB,CAAC,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC;QAE/C,OAAO,MAAM,CAAC;IACf,CAAC;IAED,gBAAgB,CAAC,IAAoB;QACpC,IAAI,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC;IACf,CAAC;IAED,gBAAgB,CAAC,IAAgB;QAChC,IAAI,SAAS,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAExC,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAEnD,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE;YACvB,IAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC;iBACnE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;SAC5C;QAED,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAE9B,qBAAqB,CAAC,GAAG,EAAE;YAC1B,MAAM,EAAE,GAAI,SAAS,CAAC,iBAAyB,CAAC,OAAO,EAAE,CAAC;YAE1D,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAI,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAClE,SAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,qBAAqB,CAAC,IAAgB;QACrC,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAc,CAAC,CAAC;QACrD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAE1E,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,KAAK,CAAC,IAAI,IAAI,aAAO,CAAC,UAAU,EAAE;gBACrC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAmB,CAAC,CAAC,CAAC;aACpE;iBAAM;gBACN,MAAM,CAAC,WAAW,CAAC,GAAG,mBAAO,EAAC,IAAI,CAAC,aAAa,CAAC,KAAY,CAAC,CAAC,CAAC,CAAC;aACjE;SACD;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,gBAAgB,CAAC,IAAoB;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,aAAO,CAAC,OAAO,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE;YAC3B,OAAO,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC9E;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,aAAO,CAAC,SAAS,CAAC,CAAC;QACtE,OAAO,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,kBAAkB,CAAC,IAAoB;QACtC,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QACrF,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrD,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QAEnF,OAAO,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED,aAAa,CAAC,IAAoB;QACjC,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,MAAM,OAAO,GAAG,iBAAK,EAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAElD,MAAM,GAAG,GAAG,OAAO,CAAC,aAAO,CAAC,gBAAgB,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,OAAO,CAAC,aAAO,CAAC,cAAc,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAO,EAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtG,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAO,EAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEtG,MAAM,QAAQ,GAAG,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC;QAExF,IAAI,OAAO,IAAI,OAAO,EAAE;YACvB,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;SAC5F;aAAM,IAAG,OAAO,EAAE;YAClB,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;SAC9E;aAAM,IAAG,OAAO,EAAE;YAClB,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;SAC/E;aAAM;YACN,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxB;QAED,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,aAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEzE,OAAO,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED,oBAAoB,CAAC,IAAoB;QACxC,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,MAAM,OAAO,GAAG,iBAAK,EAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAElD,MAAM,GAAG,GAAG,OAAO,CAAC,aAAO,CAAC,gBAAgB,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,OAAO,CAAC,aAAO,CAAC,cAAc,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAO,EAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtG,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAO,EAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtG,MAAM,QAAQ,GAAG,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAExD,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QACzF,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,aAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEzE,OAAO,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED,kBAAkB,CAAC,IAAoB;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,KAAK,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;QAChF,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACpB,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC9E;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,YAAY,CAAC,IAAoB;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE/D,QAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YAC3B,KAAK,KAAK;gBAAE,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,UAAU,CAAC;gBAAC,MAAK;YAC3D,KAAK,QAAQ;gBAAE,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,WAAW,CAAC;gBAAC,MAAK;SAC/D;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAED,YAAY,CAAC,IAAoB;QAChC,MAAM,MAAM,GAAG,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEhD,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElC,OAAO,MAAM,CAAC;IACf,CAAC;IAED,aAAa,CAAC,IAAoB;QACjC,MAAM,MAAM,GAAG,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAEpD,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9C,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE3C,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC1D,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;aAChD,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAGD,iBAAiB,CAAC,KAA6B,EAAE,KAAkB;QAClE,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE;YACpB,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACtB,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;iBAAM;gBACN,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aAC1B;SACD;IACF,CAAC;IAED,WAAW,CAAC,KAAqB,EAAE,KAAkB;QACpD,IAAI,KAAK,CAAC,SAAS;YAClB,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAEnC,IAAI,KAAK,CAAC,SAAS;YAClB,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,SAAS,CAAC,SAAiB;QAC1B,OAAO,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAED,cAAc,CAAC,EAAU,EAAE,GAAW;QACrC,OAAO,GAAG,IAAI,CAAC,SAAS,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC;IAC7C,CAAC;IAED,YAAY;QACX,OAAO,GAAG,IAAI,CAAC,SAAS,WAAW,CAAC;IACrC,CAAC;IAED,aAAa,CAAC,SAAiB,EAAE,MAA8B,EAAE,UAAkB,IAAI;QACtF,IAAI,MAAM,GAAG,GAAG,SAAS,QAAQ,CAAC;QAElC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;YACzB,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;gBACtB,SAAS;YAEV,MAAM,IAAI,KAAK,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;SAC1C;QAED,IAAI,OAAO;YACV,MAAM,IAAI,OAAO,CAAC;QAEnB,OAAO,MAAM,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,gBAAgB,CAAC,EAAU,EAAE,GAAW;QACvC,OAAO,GAAG,IAAI,CAAC,SAAS,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC;IAC7C,CAAC;IAED,kBAAkB,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU,EAAE,SAAiB;QAC3E,MAAM,OAAO,GAAG;YACf,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,MAAM;SACf,CAAC;QAEF,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE;YACtC,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;YAC3C,OAAO,YAAY,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,SAAS,IAAI,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;IAC5C,CAAC;IAED,mBAAmB,CAAC,MAAc;QACjC,IAAI,OAAO,GAAG;YACb,IAAI,EAAE,MAAM;YACZ,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,SAAS;YAClB,WAAW,EAAE,aAAa;YAC1B,WAAW,EAAE,aAAa;YAC1B,UAAU,EAAE,aAAa;YACzB,UAAU,EAAE,aAAa;YACzB,WAAW,EAAE,sBAAsB;YAMnC,KAAK,EAAE,UAAU;YACjB,cAAc,EAAE,UAAU;YAC1B,eAAe,EAAE,uBAAuB;YACxC,uBAAuB,EAAE,uBAAuB;YAChD,sBAAsB,EAAE,qBAAqB;YAC7C,OAAO,EAAE,kBAAkB;YAC3B,gBAAgB,EAAE,iBAAiB;YACnC,oBAAoB,EAAE,mBAAmB;YACzC,yBAAyB,EAAE,qBAAqB;YAChD,eAAe,EAAE,oBAAoB;YACrC,KAAK,EAAE,gBAAgB;YACvB,cAAc,EAAE,gBAAgB;YAChC,gBAAgB,EAAE,mBAAmB;YACrC,0BAA0B,EAAE,aAAa;YACzC,aAAa,EAAE,iBAAiB;YAChC,WAAW,EAAE,MAAM;YACnB,cAAc,EAAE,sBAAsB;YACtC,aAAa,EAAE,sBAAsB;YACrC,cAAc,EAAE,uBAAuB;YACvC,OAAO,EAAE,QAAQ;YACjB,OAAO,EAAE,QAAQ;YACjB,YAAY,EAAE,YAAY;YAC1B,MAAM,EAAE,QAAQ;YAChB,iBAAiB,EAAE,iBAAiB;YACpC,yBAAyB,EAAE,iBAAiB;YAC5C,gBAAgB,EAAG,aAAa;SAChC,CAAC;QAEF,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;IAClC,CAAC;IAED,eAAe;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY;YAC7B,OAAO;QAER,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE/B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE;YAClC,MAAM,YAAY,GAAG,oCAAmB,GAAE,CAAC;YAE3C,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE;gBACjC,8BAAa,EAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;aACtE;QACF,CAAC,EAAE,GAAG,CAAC,CAAC;IACT,CAAC;CAGD;AAtyCD,oCAsyCC;AAID,SAAS,aAAa,CACrB,OAAU,EACV,KAA4D,EAC5D,QAAsB;IAEtB,OAAO,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7D,CAAC;AAED,SAAS,gBAAgB,CACxB,OAAU,EACV,KAA2D,EAC3D,QAAsB;IAEtB,OAAO,eAAe,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,eAAe,CAAC,EAAU,EAAE,OAAe,EAAE,KAAiC,EAAE,QAAsB;IAC9G,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC1F,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC7B,QAAQ,IAAI,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC7C,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAiB;IAC3C,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACrB,CAAC;AAED,SAAS,cAAc,CAAC,IAAa,EAAE,QAA2B;IACjE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAe;IAC1C,OAAO,aAAa,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,aAAa,CAAC,IAAiB,EAAE,OAAe;IACxD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,UAAU,CAA2B,IAAoB,EAAE,IAAa;IAChF,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAEzB,OAAO,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QAC3C,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAExB,OAAU,MAAM,CAAC;AAClB,CAAC;;;;;;;;;;;;;;ACj3CD,MAAM,UAAU,GAAY,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtE,MAAM,OAAO,GAAG,EAAE,CAAC;AAEnB,SAAgB,mBAAmB,CAAC,YAAyB,QAAQ,CAAC,IAAI;IACzE,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3C,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;IAE3B,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC5B,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;IACtC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAE5B,OAAO,MAAM;AACd,CAAC;AATD,kDASC;AAED,SAAgB,aAAa,CAAC,IAAiB,EAAE,IAAoB,EAAE,cAAsB,EAAE,eAAuB,EAAE,GAAG,EAAE;IACzH,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAE5B,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACzC,MAAM,GAAG,GAAG,CAAC,CAAC,qBAAqB,EAAE,CAAC;IACtC,MAAM,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAEnC,MAAM,QAAQ,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClD,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC9B,MAAM,EAAE,CAAC,CAAC,MAAM;QAChB,KAAK,EAAE,CAAC,CAAC,KAAK;KACd,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IAEjD,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC;IAC1C,MAAM,IAAI,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;IAE7B,IAAI,GAAG,GAAG,QAAQ,EAAE;QAChB,OAAO,GAAG,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,OAAO,EAAE,GAAG,IAAI,IAAI,EAAE;YAC7D,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;SAC9C;KACJ;IAED,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9C,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC;IACtC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,YAAY,CAAC;IACjD,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,OAAO,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;IAEnE,IAAG,GAAG,IAAI,IAAI;QACV,OAAO;IAEX,IAAI,KAAK,GAAW,CAAC,CAAC;IAEtB,IAAI,GAAG,CAAC,KAAK,IAAI,OAAO,IAAI,GAAG,CAAC,KAAK,IAAI,QAAQ,EAAE;QACrD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QACrC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE9B,IAAI,OAAO,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC9B,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;SACtC;aAAM;YACN,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SACrB;QAED,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC;QAE1E,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,YAAY,CAAC;KACrC;SAAM;QACH,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;KAC1B;IAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC1B,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC;IACtC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAEjD,QAAQ,GAAG,CAAC,MAAM,EAAE;QAChB,KAAK,KAAK,CAAC;QACX,KAAK,WAAW;YACZ,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,WAAW,CAAC;YACxC,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,QAAQ,CAAC;YAC1C,MAAM;QAEV,KAAK,QAAQ,CAAC;QACd,KAAK,OAAO,CAAC;QACb,KAAK,YAAY;YACb,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,WAAW,CAAC;YACxC,MAAM;KACb;AACL,CAAC;AAxED,sCAwEC;AAED,SAAS,aAAa,CAAC,MAAc;IACpC,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC;;;;;;;;;;;;;;ACnGD,kFAA8D;AAE9D,MAAsB,WAAW;CAIhC;AAJD,kCAIC;AAED,MAAa,WAAY,SAAQ,WAAW;IAA5C;;QACC,SAAI,GAAG,aAAO,CAAC,QAAQ;IACxB,CAAC;CAAA;AAFD,kCAEC;AAED,MAAa,UAAW,SAAQ,WAAW;IAA3C;;QACC,SAAI,GAAG,aAAO,CAAC,OAAO;IACvB,CAAC;CAAA;AAFD,gCAEC;;;;;;;;;;;;;;ACbD,iFAAsC;AAEtC,oFAAkE;AAElE,MAAa,YAAoC,SAAQ,WAAI;IAKzD,YAAY,GAAmB,EAAE,IAAY,EAAE,MAAsB;QACjE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IAClC,CAAC;CACJ;AATD,oCASC;AAED,MAAa,aAAc,SAAQ,YAAyB;IACxD,YAAY,GAAmB,EAAE,IAAY,EAAE,MAAsB;QACjE,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,sBAAW,CAAC,CAAC;IAChF,CAAC;CACJ;AARD,sCAQC;AAED,MAAa,YAAa,SAAQ,YAAwB;IACtD,YAAY,GAAmB,EAAE,IAAY,EAAE,MAAsB;QACjE,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,qBAAU,CAAC,CAAC;IAC9E,CAAC;CACJ;AARD,oCAQC;;;;;;;;;;;;;;ACjCD,iFAAsC;AAGtC,2FAAgI;AAEhI,MAAa,aAAc,SAAQ,WAAI;IAGnC,YAAY,GAAmB,EAAE,IAAY,EAAE,MAAsB;QACjE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IAClC,CAAC;IAQD,QAAQ,CAAC,IAAa;QAClB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,kCAAkB,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;CACJ;AAlBD,sCAkBC;;;;;;;;;;;;;;ACvBD,oGAAsF;AACtF,kFAAoE;AAiDpE,SAAgB,kBAAkB,CAAC,IAAa,EAAE,GAAc;IAC5D,IAAI,MAAM,GAA4B;QAClC,UAAU,EAAE,EAAE;QACd,kBAAkB,EAAE,EAAE;QACtB,cAAc,EAAE,EAAE;KACrB;IAED,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,KAAK;gBACN,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC/C,MAAM;YACV,KAAK,aAAa;gBACd,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC/D,MAAM;YACV,KAAK,cAAc;gBACf,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAChE,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAtBD,gDAsBC;AAED,SAAgB,cAAc,CAAC,IAAa,EAAE,GAAc;IACxD,IAAI,MAAM,GAAc;QACpB,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;QAC3B,SAAS,EAAE,EAAE;KAChB,CAAC;IAEF,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,eAAe;gBAChB,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,aAAa;gBACd,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC5D,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAlBD,wCAkBC;AAED,SAAgB,sBAAsB,CAAC,IAAa,EAAE,GAAc;IAChE,IAAI,MAAM,GAAsB;QAC5B,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC;QACnC,MAAM,EAAE,EAAE;KACb,CAAC;IAEF,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,MAAM;gBACP,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACjC,MAAM;YACV,KAAK,gBAAgB;gBACjB,MAAM,CAAC,cAAc,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC3C,MAAM;YACV,KAAK,cAAc;gBACf,MAAM,CAAC,kBAAkB,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC/C,MAAM;YACV,KAAK,WAAW;gBACZ,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACtC,MAAM;YACV,KAAK,KAAK;gBACN,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;gBAChD,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AA3BD,wDA2BC;AAED,SAAgB,mBAAmB,CAAC,IAAa,EAAE,GAAc;IAC7D,IAAI,MAAM,GAAmB;QACzB,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;KACnC,CAAC;IAEF,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,OAAO;gBACR,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAClC,MAAM;YACV,KAAK,YAAY;gBACb,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,QAAQ;gBACT,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACnC,MAAM;YACV,KAAK,SAAS;gBACV,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACjC,MAAM;YACV,KAAK,OAAO;gBACR,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC1C,MAAM;YACV,KAAK,gBAAgB;gBACjB,MAAM,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC5C,MAAM;YACV,KAAK,QAAQ;gBACT,MAAM,CAAC,cAAc,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC3C,MAAM;YACV,KAAK,KAAK;gBACN,MAAM,CAAC,cAAc,GAAG,wCAAwB,EAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACzD,MAAM;YACV,KAAK,KAAK;gBACN,MAAM,CAAC,QAAQ,GAAG,4BAAkB,EAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC7C,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAtCD,kDAsCC;AAED,SAAgB,4BAA4B,CAAC,IAAa,EAAE,GAAc;IACtE,IAAI,MAAM,GAA2B;QACjC,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;KACnC,CAAC;IAEF,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,QAAQ,CAAC,CAAC,SAAS,EAAE;YACjB,KAAK,eAAe;gBAChB,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACrC,MAAM;YACV,KAAK,KAAK;gBACN,MAAM,CAAC,cAAc,GAAG,mBAAmB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACpD,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAjBD,oEAiBC;AAED,SAAgB,2BAA2B,CAAC,IAAa,EAAE,GAAc;IAErE,IAAI,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrC,IAAI,KAAK,GAAG,IAAI,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/C,IAAI,SAAS,GAAG,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IAEzD,OAAO,SAAS,CAAC,CAAC,CAAC;QACf,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC;QACpC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;QACtC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;KAClC,CAAC,CAAC,CAAC,IAAI,CAAC;AACb,CAAC;AAXD,kEAWC;;;;;;;;;;;;;;AClMD,2FAA2G;AAE3G,SAAgB,cAAc,CAAC,SAAiB,EAAE,qBAA8B,KAAK;IACjF,IAAI,kBAAkB;QAClB,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IAEpD,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;IAErC,MAAM,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAC7E,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAE5C,IAAI,SAAS;QACT,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;IAE/B,OAAO,MAAM,CAAC;AAClB,CAAC;AAbD,wCAaC;AAED,SAAS,iBAAiB,CAAC,GAAa;IACpC,OAAO,GAAG,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC;AACnE,CAAC;AAED,SAAS,aAAa,CAAC,IAAY;IAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpE,CAAC;AAED,SAAgB,kBAAkB,CAAC,IAAU;IACzC,OAAO,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACvD,CAAC;AAFD,gDAEC;AAED,MAAa,SAAS;IAClB,QAAQ,CAAC,IAAa,EAAE,YAAoB,IAAI;QAC5C,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACpD,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,IAAK,CAAa,CAAC,SAAS,IAAI,SAAS,CAAC;gBAC/E,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACtB;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,IAAa,EAAE,SAAiB;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACpD,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,IAAK,CAAa,CAAC,SAAS,IAAI,SAAS;gBACxD,OAAO,CAAY,CAAC;SAC3B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,WAAW,CAAC,IAAa,EAAE,SAAiB,EAAE,aAAqB;QAC/D,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACvC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACzD,CAAC;IAEJ,KAAK,CAAC,IAAa;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAEE,IAAI,CAAC,IAAa,EAAE,SAAiB;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACpD,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS;gBACxB,OAAO,CAAC,CAAC,KAAK,CAAC;SACtB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,CAAC,IAAa,EAAE,QAAgB,EAAE,eAAuB,IAAI;QAChE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpC,OAAO,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAC9C,CAAC;IAEJ,OAAO,CAAC,IAAa,EAAE,QAAgB,EAAE,eAAuB,IAAI;QAC7D,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpC,OAAO,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAClD,CAAC;IAED,SAAS,CAAC,IAAa,EAAE,QAAgB,EAAE,eAAuB,IAAI;QAClE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpC,OAAO,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAChD,CAAC;IAED,QAAQ,CAAC,IAAa,EAAE,QAAgB,EAAE,eAAwB,IAAI;QAClE,OAAO,2BAAc,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,YAAY,CAAC,CAAC;IACnE,CAAC;IAED,UAAU,CAAC,IAAa,EAAE,QAAgB,EAAE,QAAyB,oBAAW,CAAC,GAAG;QAChF,OAAO,0BAAa,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;CACJ;AAnED,8BAmEC;AAED,MAAM,eAAe,GAAG,IAAI,SAAS,EAAE,CAAC;AAExC,qBAAe,eAAe,CAAC;;;;;;;;;;;;;;ACnG/B,iFAAsC;AACtC,uFAAwD;AAExD,MAAa,YAAa,SAAQ,WAAI;IAGrC,YAAY,GAAmB,EAAE,IAAY;QAC5C,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAClB,CAAC;IAED,QAAQ,CAAC,IAAa;QACrB,IAAI,CAAC,QAAQ,GAAG,4BAAa,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;CACD;AAVD,oCAUC;;;;;;;;;;;;;;ACED,SAAgB,aAAa,CAAC,IAAa,EAAE,GAAc;IAC1D,IAAI,MAAM,GAAG,EAAiB,CAAC;IAE/B,KAAK,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClC,QAAO,EAAE,CAAC,SAAS,EAAE;YACpB,KAAK,gBAAgB;gBAAE,MAAM,CAAC,cAAc,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBAAC,MAAM;YAChF,KAAK,YAAY;gBAAE,MAAM,CAAC,aAAa,GAAG,mBAAmB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;YAC9E,KAAK,WAAW;gBAAE,MAAM,CAAC,YAAY,GAAG,mBAAmB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;YAC5E,KAAK,iBAAiB;gBAAE,MAAM,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBAAC,MAAM;SAChF;KACD;IAEE,OAAO,MAAM,CAAC;AAClB,CAAC;AAbD,sCAaC;AAED,SAAgB,mBAAmB,CAAC,IAAa,EAAE,GAAc;IAChE,IAAI,MAAM,GAAG;QACZ,cAAc,EAAE,EAAE;KACA,CAAC;IAEpB,KAAK,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClC,QAAO,EAAE,CAAC,SAAS,EAAE;YACpB,KAAK,QAAQ;gBACZ,MAAM,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC7C,MAAM;YAEP,KAAK,UAAU,CAAC;YAChB,KAAK,SAAS;gBACb,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC/C,MAAM;SACP;KACD;IAEE,OAAO,MAAM,CAAC;AAClB,CAAC;AAnBD,kDAmBC;;;;;;;;;;;;;;ACjDD,iFAAsC;AAItC,MAAa,UAAW,SAAQ,WAAI;IAKhC,YAAY,GAAmB,EAAE,IAAY,EAAE,MAAsB;QACjE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IAClC,CAAC;IAED,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;CACJ;AAbD,gCAaC;;;;;;;;;;;;;;ACjBD,iFAAsC;AACtC,2EAA+C;AAE/C,MAAa,SAAU,SAAQ,WAAI;IAG/B,YAAY,GAAmB,EAAE,IAAY;QACzC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACrB,CAAC;IAED,QAAQ,CAAC,IAAa;QAClB,IAAI,CAAC,KAAK,GAAG,sBAAU,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;CACJ;AAVD,8BAUC;;;;;;;;;;;;;;ACZD,MAAa,QAAQ;CAGpB;AAHD,4BAGC;AAmBD,SAAgB,UAAU,CAAC,IAAa,EAAE,GAAc;IACpD,IAAI,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC5B,IAAI,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAEvD,KAAK,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QACxC,QAAO,EAAE,CAAC,SAAS,EAAE;YACjB,KAAK,WAAW;gBAAE,MAAM,CAAC,WAAW,GAAG,gBAAgB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;YACxE,KAAK,YAAY;gBAAE,MAAM,CAAC,UAAU,GAAG,eAAe,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;SAC1E;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAZD,gCAYC;AAED,SAAgB,gBAAgB,CAAC,IAAa,EAAE,GAAc;IAC1D,IAAI,MAAM,GAAmB;QACzB,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;QAC5B,MAAM,EAAE,EAAE;KACb,CAAC;IAEF,KAAK,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAI,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACzC,IAAI,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAEvC,IAAI,OAAO,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC1D;aACI,IAAI,MAAM,EAAE;YACb,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SAC7D;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAnBD,4CAmBC;AAED,SAAgB,eAAe,CAAC,IAAa,EAAE,GAAc;IACzD,IAAI,MAAM,GAAkB;QACxB,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;KACd,CAAC;IAEnB,KAAK,IAAI,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/B,QAAQ,EAAE,CAAC,SAAS,EAAE;YAClB,KAAK,WAAW;gBAAE,MAAM,CAAC,SAAS,GAAG,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;YACnE,KAAK,WAAW;gBAAE,MAAM,CAAC,SAAS,GAAG,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBAAC,MAAM;SACtE;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAbD,0CAaC;AAED,SAAgB,aAAa,CAAC,IAAa,EAAE,GAAc;IACvD,OAAO;QACH,aAAa,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC;QACzD,UAAU,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC;QACnD,UAAU,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC;KACtD,CAAC;AACN,CAAC;AAND,sCAMC;;;;;;;;;;;;;;AChFD,SAAgB,eAAe,CAAC,SAAiB;IAChD,OAAO,SAAS,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;AAChF,CAAC;AAFD,0CAEC;AAED,SAAgB,SAAS,CAAC,IAAY;IAClC,IAAI,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnC,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAClD,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAEnD,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9B,CAAC;AAND,8BAMC;AAED,SAAgB,WAAW,CAAC,IAAY,EAAE,IAAY;IAClD,IAAI;QACA,MAAM,MAAM,GAAG,cAAc,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpD,OAAO,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KACvC;IAAC,MAAM;QACJ,OAAO,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;KAC3B;AACL,CAAC;AARD,kCAQC;AAED,SAAgB,KAAK,CAAU,KAAU,EAAE,EAAiB;IACxD,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACb,OAAO,CAAC,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;AALD,sBAKC;AAED,SAAgB,YAAY,CAAC,IAAU;IACtC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACtC,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,MAAgB,CAAC,CAAC;QAC1D,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;QAChC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;AACJ,CAAC;AAPD,oCAOC;AAED,SAAgB,QAAQ,CAAC,IAAI;IACzB,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpE,CAAC;AAFD,4BAEC;AAED,SAAgB,QAAQ,CAAC,IAAa;IAClC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,YAAY,MAAM,CAAC;AAC9D,CAAC;AAFD,4BAEC;AAED,SAAgB,SAAS,CAAC,MAAM,EAAE,GAAG,OAAO;IACxC,IAAI,CAAC,OAAO,CAAC,MAAM;QACf,OAAO,MAAM,CAAC;IAElB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;IAE/B,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;QACtC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;YACtB,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;gBACvB,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9C,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/B;iBAAM;gBACH,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;aAC7B;SACJ;KACJ;IAED,OAAO,SAAS,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC;AACzC,CAAC;AAlBD,8BAkBC;AAED,SAAgB,aAAa,CAAC,IAAY;IACzC,MAAM,MAAM,GAA2B,EAAE,CAAC;IAE1C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QACnC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KAClB;IAED,OAAO,MAAM;AACd,CAAC;AATD,sCASC;AAED,SAAgB,cAAc,CAAC,KAA6B;IAC3D,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpE,CAAC;AAFD,wCAEC;AAED,SAAgB,OAAO,CAAI,GAAY;IACtC,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACzC,CAAC;AAFD,0BAEC;;;;;;;;;;;;;;AClFD,2FAAgE;AAChE,kFAA8D;AAC9D,mGAAuC;AAGvC,MAAa,UAAW,SAAQ,wBAAkB;IAAlD;;QACC,SAAI,GAAY,aAAO,CAAC,UAAU,CAAC;QAGnC,UAAK,GAA2B,EAAE,CAAC;IAMpC,CAAC;CAAA;AAVD,gCAUC;AAED,SAAgB,eAAe,CAAC,IAAa,EAAE,MAAsB;IACpE,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;IAE9B,QAAQ,IAAI,CAAC,SAAS,EAAE;QACvB,KAAK,MAAM;YACV,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;YACxB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YAC/D,MAAM;QAEP,KAAK,MAAM;YACV,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;YAC3B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5E,MAAM;QAEP,KAAK,MAAM;YACV,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;YACxB,MAAM;QAEP,KAAK,OAAO;YACX,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;YACrB,MAAM;QAEP,KAAK,SAAS;YACb,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YAC/D,MAAM;QAEP;YACC,OAAO,IAAI,CAAC;KACb;IAED,KAAK,MAAM,EAAE,IAAI,oBAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACjC,QAAO,EAAE,CAAC,SAAS,EAAE;YACpB,KAAK,OAAO;gBACX,MAAM,CAAC,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC;gBAC/B,MAAM;YAEP,KAAK,WAAW;gBACf,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC;gBAC7B,MAAM;YAEP,KAAK,MAAM;gBACV,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACxC,MAAM;YAEP,KAAK,IAAI;gBACR,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACxC,MAAM;SACP;KACD;IAED,KAAK,MAAM,EAAE,IAAI,oBAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACpC,QAAQ,EAAE,CAAC,SAAS,EAAE;YACrB,KAAK,QAAQ;gBACZ,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7C,MAAM;YAEP,KAAK,MAAM;gBACV,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3C,MAAM;YAEP,KAAK,WAAW;gBACf,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;gBACzB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC/D,MAAM,CAAC,SAAS,GAAG;oBAClB,EAAE,EAAE,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;oBACtB,KAAK,EAAE,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC;iBAC5B;gBACD,MAAM;YAEP,KAAK,aAAa;gBACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtD,MAAM;YAEP;gBACC,MAAM,KAAK,GAAG,eAAe,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;gBAC1C,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM;SACP;KACD;IAED,OAAO,MAAM,CAAC;AACf,CAAC;AApFD,0CAoFC;AAED,SAAS,WAAW,CAAC,EAAW;IAC/B,OAAO;QACN,QAAQ,EAAE,oBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC;QAC/B,cAAc,EAAE,oBAAG,CAAC,UAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,oBAAW,CAAC,GAAG,CAAC,IAAI,KAAK;KACtE,CAAC;AACH,CAAC;AAED,SAAS,SAAS,CAAC,EAAW;IAC7B,OAAO,EAEN,CAAC;AACH,CAAC;AAED,SAAS,UAAU,CAAC,GAAW;IAC9B,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAChC,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAE,EAAE;QACrD,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YAAE,OAAO,0BAAa,EAAC,CAAC,EAAG,oBAAW,CAAC,MAAM,CAAC,CAAC;QAClE,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YAAE,OAAO,CAAC,CAAC;QAE9B,OAAO,EAAE,CAAC;IACX,CAAC,CAAC,CAAC;AACJ,CAAC;;;;;;;;;;;;;;AC7HD,wGAAwE;AAExE,0GAAwD;AACxD,oHAA2D;AAC3D,+GAAwD;AACxD,qEAA+D;AAC/D,oHAA2D;AAC3D,qGAAkD;AAClD,iGAA+D;AAC/D,6IAAyE;AACzE,iIAAiE;AACjE,gGAA+C;AAC/C,iFAA4D;AAC5D,+GAAwD;AACxD,uIAAqE;AAErE,MAAM,YAAY,GAAG;IACpB,EAAE,IAAI,EAAE,gCAAiB,CAAC,cAAc,EAAE,MAAM,EAAE,mBAAmB,EAAE;IACvE,EAAE,IAAI,EAAE,gCAAiB,CAAC,kBAAkB,EAAE,MAAM,EAAE,kBAAkB,EAAE;IAC1E,EAAE,IAAI,EAAE,gCAAiB,CAAC,cAAc,EAAE,MAAM,EAAE,mBAAmB,EAAE;IACvE,EAAE,IAAI,EAAE,gCAAiB,CAAC,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,EAAE;CAC3E,CAAC;AAEF,MAAa,YAAY;IAAzB;QAMC,UAAK,GAAW,EAAE,CAAC;QACnB,aAAQ,GAAyB,EAAE,CAAC;IA0JrC,CAAC;IA7IA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAgB,EAAE,MAAsB,EAAE,OAAY;QACvE,IAAI,CAAC,GAAG,IAAI,YAAY,EAAE,CAAC;QAE3B,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC;QACrB,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC,CAAC,QAAQ,GAAG,MAAM,iCAAc,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAE9C,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;YACvD,OAAO,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC;QAEJ,OAAO,CAAC,CAAC;IACV,CAAC;IAED,IAAI,CAAC,IAAI,GAAG,MAAM;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAY,EAAE,IAAY;QAC5D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;YAC3B,OAAO,IAAI,CAAC;QAEb,IAAI,IAAI,GAAS,IAAI,CAAC;QAEtB,QAAQ,IAAI,EAAE;YACb,KAAK,gCAAiB,CAAC,cAAc;gBACpC,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,4BAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC/E,MAAM;YAEP,KAAK,gCAAiB,CAAC,SAAS;gBAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,0BAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACnE,MAAM;YAEP,KAAK,gCAAiB,CAAC,SAAS;gBAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,8BAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBACjF,MAAM;YAEP,KAAK,gCAAiB,CAAC,MAAM;gBAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI,wBAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3E,MAAM;YAEP,KAAK,gCAAiB,CAAC,KAAK;gBAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,sBAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC3D,MAAM;YAEP,KAAK,gCAAiB,CAAC,SAAS;gBAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,qBAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBACjF,MAAM;YAEP,KAAK,gCAAiB,CAAC,QAAQ;gBAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,oBAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC/E,MAAM;YAEP,KAAK,gCAAiB,CAAC,MAAM;gBAC5B,IAAI,GAAG,IAAI,kBAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzD,MAAM;YAEP,KAAK,gCAAiB,CAAC,MAAM;gBAC5B,IAAI,GAAG,IAAI,kBAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzD,MAAM;YAEP,KAAK,gCAAiB,CAAC,cAAc;gBACpC,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,+BAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACnE,MAAM;YAEP,KAAK,gCAAiB,CAAC,kBAAkB;gBACxC,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,IAAI,uCAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC3E,MAAM;YAEP,KAAK,gCAAiB,CAAC,gBAAgB;gBACtC,IAAI,GAAG,IAAI,mCAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAChD,MAAM;YAEP,KAAK,gCAAiB,CAAC,QAAQ;gBAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,4BAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACjE,MAAM;SACP;QAED,IAAI,IAAI,IAAI,IAAI;YACf,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAElB,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,GAAG,qBAAS,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,uBAAW,EAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC9G;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU,EAAE,IAAW;QAC9C,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,EAAU;QAClC,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU,EAAE,GAAW;QACrC,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;QACxE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAEO,SAAS,CAAC,IAAU;QAC3B,IAAI,CAAC,IAAI;YACR,OAAO,IAAI,CAAC;QAEb,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;YAC/B,OAAO,wBAAY,EAAC,IAAI,CAAC,CAAC;SAC1B;QAED,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,eAAe,CAAC,EAAU,EAAE,WAAiB,IAAI;QAChD,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,qBAAS,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3D,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAW,EAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACpE,CAAC;IAED,WAAW,CAAC,IAAU,EAAE,EAAU;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,GAAG,qBAAS,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,OAAO,GAAG,CAAC,CAAC,CAAC,uBAAW,EAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrD,CAAC;IAEO,YAAY,CAAC,IAAU,EAAE,EAAU,EAAE,UAAsB;QAClE,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5E,CAAC;CACD;AAjKD,oCAiKC;AAED,SAAgB,WAAW,CAAC,IAAgB,EAAE,OAAe;IAC5D,MAAM,GAAG,GAAG,EAAE,CAAC;IACf,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC9C,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;QAC3B,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;QAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;IAErC,OAAO,IAAI,CAAC;AACb,CAAC;AAZD,kCAYC;;;;;;;;;;;ACzMD;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UEtBA;UACA;UACA;UACA", "sources": ["webpack://docx/webpack/universalModuleDefinition", "webpack://docx/./src/common/open-xml-package.ts", "webpack://docx/./src/common/part.ts", "webpack://docx/./src/common/relationship.ts", "webpack://docx/./src/document-parser.ts", "webpack://docx/./src/document-props/core-props-part.ts", "webpack://docx/./src/document-props/core-props.ts", "webpack://docx/./src/document-props/custom-props-part.ts", "webpack://docx/./src/document-props/custom-props.ts", "webpack://docx/./src/document-props/extended-props-part.ts", "webpack://docx/./src/document-props/extended-props.ts", "webpack://docx/./src/document/bookmarks.ts", "webpack://docx/./src/document/border.ts", "webpack://docx/./src/document/common.ts", "webpack://docx/./src/document/document-part.ts", "webpack://docx/./src/document/dom.ts", "webpack://docx/./src/document/line-spacing.ts", "webpack://docx/./src/document/paragraph.ts", "webpack://docx/./src/document/run.ts", "webpack://docx/./src/document/section.ts", "webpack://docx/./src/docx-preview.ts", "webpack://docx/./src/font-table/font-table.ts", "webpack://docx/./src/font-table/fonts.ts", "webpack://docx/./src/header-footer/elements.ts", "webpack://docx/./src/header-footer/parts.ts", "webpack://docx/./src/html-renderer.ts", "webpack://docx/./src/javascript.ts", "webpack://docx/./src/notes/elements.ts", "webpack://docx/./src/notes/parts.ts", "webpack://docx/./src/numbering/numbering-part.ts", "webpack://docx/./src/numbering/numbering.ts", "webpack://docx/./src/parser/xml-parser.ts", "webpack://docx/./src/settings/settings-part.ts", "webpack://docx/./src/settings/settings.ts", "webpack://docx/./src/styles/styles-part.ts", "webpack://docx/./src/theme/theme-part.ts", "webpack://docx/./src/theme/theme.ts", "webpack://docx/./src/utils.ts", "webpack://docx/./src/vml/vml.ts", "webpack://docx/./src/word-document.ts", "webpack://docx/external umd {\"root\":\"J<PERSON><PERSON><PERSON>\",\"commonjs\":\"jszip\",\"commonjs2\":\"jszip\",\"amd\":\"jszip\",\"module\":\"jszip\"}", "webpack://docx/webpack/bootstrap", "webpack://docx/webpack/before-startup", "webpack://docx/webpack/startup", "webpack://docx/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jszip\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"docx\", [\"jszip\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"docx\"] = factory(require(\"jszip\"));\n\telse\n\t\troot[\"docx\"] = factory(root[\"JSZip\"]);\n})(globalThis, (__WEBPACK_EXTERNAL_MODULE_jszip__) => {\nreturn ", "import * as J<PERSON><PERSON><PERSON> from \"jszip\";\r\nimport { parseXmlString, XmlParser } from \"../parser/xml-parser\";\r\nimport { splitPath } from \"../utils\";\r\nimport { parseRelationships, Relationship } from \"./relationship\";\r\n\r\nexport interface OpenXmlPackageOptions {\r\n    trimXmlDeclaration: boolean,\r\n    keepOrigin: boolean,\r\n}\r\n\r\nexport class OpenXmlPackage {\r\n    xmlParser: XmlParser = new XmlParser();\r\n\r\n    constructor(private _zip: JSZip, public options: OpenXmlPackageOptions) {\r\n    }\r\n\r\n    get(path: string): any {\r\n        return this._zip.files[normalizePath(path)];\r\n    }\r\n\r\n    update(path: string, content: any) {\r\n        this._zip.file(path, content);\r\n    }\r\n\r\n    static async load(input: Blob | any, options: OpenXmlPackageOptions): Promise<OpenXmlPackage> {\r\n        const zip = await JSZip.loadAsync(input);\r\n\t\treturn new OpenXmlPackage(zip, options);\r\n    }\r\n\r\n    save(type: any = \"blob\"): Promise<any>  {\r\n        return this._zip.generateAsync({ type });\r\n    }\r\n\r\n    load(path: string, type: JSZip.OutputType = \"string\"): Promise<any> {\r\n        return this.get(path)?.async(type) ?? Promise.resolve(null);\r\n    }\r\n\r\n    async loadRelationships(path: string = null): Promise<Relationship[]> {\r\n        let relsPath = `_rels/.rels`;\r\n\r\n        if (path != null) {\r\n            const [f, fn] = splitPath(path);\r\n            relsPath = `${f}_rels/${fn}.rels`;\r\n        }\r\n\r\n        const txt = await this.load(relsPath);\r\n\t\treturn txt ? parseRelationships(this.parseXmlDocument(txt).firstElementChild, this.xmlParser) : null;\r\n    }\r\n\r\n    /** @internal */\r\n    parseXmlDocument(txt: string): Document {\r\n        return parseXmlString(txt, this.options.trimXmlDeclaration);\r\n    }\r\n}\r\n\r\nfunction normalizePath(path: string) {\r\n    return path.startsWith('/') ? path.substr(1) : path;\r\n}", "import { serializeXmlString } from \"../parser/xml-parser\";\r\nimport { OpenXmlPackage } from \"./open-xml-package\";\r\nimport { Relationship } from \"./relationship\";\r\n\r\nexport class Part {\r\n    protected _xmlDocument: Document;\r\n\r\n    rels: Relationship[];\r\n\r\n    constructor(protected _package: OpenXmlPackage, public path: string) {\r\n    }\r\n\r\n    async load(): Promise<any> {\r\n\t\tthis.rels = await this._package.loadRelationships(this.path);\r\n\r\n\t\tconst xmlText = await this._package.load(this.path);\r\n\t\tconst xmlDoc = this._package.parseXmlDocument(xmlText);\r\n\r\n\t\tif (this._package.options.keepOrigin) {\r\n\t\t\tthis._xmlDocument = xmlDoc;\r\n\t\t}\r\n\r\n\t\tthis.parseXml(xmlDoc.firstElementChild);\r\n    }\r\n\r\n    save() {\r\n        this._package.update(this.path, serializeXmlString(this._xmlDocument));\r\n    }\r\n\r\n    protected parseXml(root: Element) {\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface Relationship {\r\n    id: string,\r\n    type: RelationshipTypes | string,\r\n    target: string\r\n    targetMode: \"\" | \"External\" | string \r\n}\r\n\r\nexport enum RelationshipTypes {\r\n    OfficeDocument = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\",\r\n    FontTable = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable\",\r\n    Image = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image\",\r\n    Numbering = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering\",\r\n    Styles = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\",\r\n    StylesWithEffects = \"http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects\",\r\n    Theme = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme\",\r\n    Settings = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings\",\r\n    WebSettings = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings\",\r\n    Hyperlink = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink\",\r\n    Footnotes = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes\",\r\n\tEndnotes = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes\",\r\n    Footer = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer\",\r\n    Header = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/header\",\r\n    ExtendedProperties = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties\",\r\n    CoreProperties = \"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties\",\r\n\tCustomProperties = \"http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties\",\r\n}\r\n\r\nexport function parseRelationships(root: Element, xml: XmlParser): Relationship[] {\r\n    return xml.elements(root).map(e => <Relationship>{\r\n        id: xml.attr(e, \"Id\"),\r\n        type: xml.attr(e, \"Type\"),\r\n        target: xml.attr(e, \"Target\"),\r\n        targetMode: xml.attr(e, \"TargetMode\")\r\n    });\r\n}", "import {\r\n\tDomType, WmlTable, IDomNumbering,\r\n\tWmlHyperlink, IDomImage, OpenXmlElement, WmlTableColumn, WmlTableCell,\r\n\tWmlTableRow, NumberingPicBullet, WmlText, WmlSymbol, WmlBreak, WmlNoteReference\r\n} from './document/dom';\r\nimport { DocumentElement } from './document/document';\r\nimport { WmlParagraph, parseParagraphProperties, parseParagraphProperty } from './document/paragraph';\r\nimport { parseSectionProperties, SectionProperties } from './document/section';\r\nimport xml from './parser/xml-parser';\r\nimport { parseRunProperties, WmlRun } from './document/run';\r\nimport { parseBookmarkEnd, parseBookmarkStart } from './document/bookmarks';\r\nimport { IDomStyle, IDomSubStyle } from './document/style';\r\nimport { WmlFieldChar, WmlFieldSimple, WmlInstructionText } from './document/fields';\r\nimport { convertLength, LengthUsage, LengthUsageType } from './document/common';\r\nimport { parseVmlElement } from './vml/vml';\r\n\r\nexport var autos = {\r\n\tshd: \"inherit\",\r\n\tcolor: \"black\",\r\n\tborderColor: \"black\",\r\n\thighlight: \"transparent\"\r\n};\r\n\r\nconst supportedNamespaceURIs = [];\r\n\r\nconst mmlTagMap = {\r\n\t\"oMath\": DomType.MmlMath,\r\n\t\"oMathPara\": DomType.MmlMathParagraph,\r\n\t\"f\": DomType.MmlFraction,\r\n\t\"func\": DomType.MmlFunction,\r\n\t\"fName\": DomType.MmlFunctionName,\r\n\t\"num\": DomType.MmlNumerator,\r\n\t\"den\": DomType.MmlDenominator,\r\n\t\"rad\": DomType.MmlRadical,\r\n\t\"deg\": DomType.MmlDegree,\r\n\t\"e\": DomType.MmlBase,\r\n\t\"sSup\": DomType.MmlSuperscript,\r\n\t\"sSub\": DomType.MmlSubscript,\r\n\t\"sPre\": DomType.MmlPreSubSuper,\r\n\t\"sup\": DomType.MmlSuperArgument,\r\n\t\"sub\": DomType.MmlSubArgument,\r\n\t\"d\": DomType.MmlDelimiter,\r\n\t\"nary\": DomType.MmlNary,\r\n\t\"eqArr\": DomType.MmlEquationArray,\r\n\t\"lim\": DomType.MmlLimit,\r\n\t\"limLow\": DomType.MmlLimitLower,\r\n\t\"m\": DomType.MmlMatrix,\r\n\t\"mr\": DomType.MmlMatrixRow,\r\n\t\"box\": DomType.MmlBox,\r\n\t\"bar\": DomType.MmlBar,\r\n\t\"groupChr\": DomType.MmlGroupChar\r\n}\r\n\r\nexport interface DocumentParserOptions {\r\n\tignoreWidth: boolean;\r\n\tdebug: boolean;\r\n}\r\n\r\nexport class DocumentParser {\r\n\toptions: DocumentParserOptions;\r\n\r\n\tconstructor(options?: Partial<DocumentParserOptions>) {\r\n\t\tthis.options = {\r\n\t\t\tignoreWidth: false,\r\n\t\t\tdebug: false,\r\n\t\t\t...options\r\n\t\t};\r\n\t}\r\n\r\n\tparseNotes(xmlDoc: Element, elemName: string, elemClass: any): any[] {\r\n\t\tvar result = [];\r\n\r\n\t\tfor (let el of xml.elements(xmlDoc, elemName)) {\r\n\t\t\tconst node = new elemClass();\r\n\t\t\tnode.id = xml.attr(el, \"id\");\r\n\t\t\tnode.noteType = xml.attr(el, \"type\");\r\n\t\t\tnode.children = this.parseBodyElements(el);\r\n\t\t\tresult.push(node);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseDocumentFile(xmlDoc: Element): DocumentElement {\r\n\t\tvar xbody = xml.element(xmlDoc, \"body\");\r\n\t\tvar background = xml.element(xmlDoc, \"background\");\r\n\t\tvar sectPr = xml.element(xbody, \"sectPr\");\r\n\r\n\t\treturn {\r\n\t\t\ttype: DomType.Document,\r\n\t\t\tchildren: this.parseBodyElements(xbody),\r\n\t\t\tprops: sectPr ? parseSectionProperties(sectPr, xml) : {} as SectionProperties,\r\n\t\t\tcssStyle: background ? this.parseBackground(background) : {},\r\n\t\t};\r\n\t}\r\n\r\n\tparseBackground(elem: Element): any {\r\n\t\tvar result = {};\r\n\t\tvar color = xmlUtil.colorAttr(elem, \"color\");\r\n\r\n\t\tif (color) {\r\n\t\t\tresult[\"background-color\"] = color;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseBodyElements(element: Element): OpenXmlElement[] {\r\n\t\tvar children = [];\r\n\r\n\t\tfor (let elem of xml.elements(element)) {\r\n\t\t\tswitch (elem.localName) {\r\n\t\t\t\tcase \"p\":\r\n\t\t\t\t\tchildren.push(this.parseParagraph(elem));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tbl\":\r\n\t\t\t\t\tchildren.push(this.parseTable(elem));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sdt\":\r\n\t\t\t\t\tchildren.push(...this.parseSdt(elem, e => this.parseBodyElements(e)));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn children;\r\n\t}\r\n\r\n\tparseStylesFile(xstyles: Element): IDomStyle[] {\r\n\t\tvar result = [];\r\n\r\n\t\txmlUtil.foreach(xstyles, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"style\":\r\n\t\t\t\t\tresult.push(this.parseStyle(n));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"docDefaults\":\r\n\t\t\t\t\tresult.push(this.parseDefaultStyles(n));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseDefaultStyles(node: Element): IDomStyle {\r\n\t\tvar result = <IDomStyle>{\r\n\t\t\tid: null,\r\n\t\t\tname: null,\r\n\t\t\ttarget: null,\r\n\t\t\tbasedOn: null,\r\n\t\t\tstyles: []\r\n\t\t};\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"rPrDefault\":\r\n\t\t\t\t\tvar rPr = xml.element(c, \"rPr\");\r\n\r\n\t\t\t\t\tif (rPr)\r\n\t\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\t\ttarget: \"span\",\r\n\t\t\t\t\t\t\tvalues: this.parseDefaultProperties(rPr, {})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pPrDefault\":\r\n\t\t\t\t\tvar pPr = xml.element(c, \"pPr\");\r\n\r\n\t\t\t\t\tif (pPr)\r\n\t\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\t\ttarget: \"p\",\r\n\t\t\t\t\t\t\tvalues: this.parseDefaultProperties(pPr, {})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseStyle(node: Element): IDomStyle {\r\n\t\tvar result = <IDomStyle>{\r\n\t\t\tid: xml.attr(node, \"styleId\"),\r\n\t\t\tisDefault: xml.boolAttr(node, \"default\"),\r\n\t\t\tname: null,\r\n\t\t\ttarget: null,\r\n\t\t\tbasedOn: null,\r\n\t\t\tstyles: [],\r\n\t\t\tlinked: null\r\n\t\t};\r\n\r\n\t\tswitch (xml.attr(node, \"type\")) {\r\n\t\t\tcase \"paragraph\": result.target = \"p\"; break;\r\n\t\t\tcase \"table\": result.target = \"table\"; break;\r\n\t\t\tcase \"character\": result.target = \"span\"; break;\r\n\t\t\t//case \"numbering\": result.target = \"p\"; break;\r\n\t\t}\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"basedOn\":\r\n\t\t\t\t\tresult.basedOn = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"name\":\r\n\t\t\t\t\tresult.name = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"link\":\r\n\t\t\t\t\tresult.linked = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"next\":\r\n\t\t\t\t\tresult.next = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"aliases\":\r\n\t\t\t\t\tresult.aliases = xml.attr(n, \"val\").split(\",\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\ttarget: \"p\",\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tresult.paragraphProps = parseParagraphProperties(n, xml);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\ttarget: \"span\",\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tresult.runProps = parseRunProperties(n, xml);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblPr\":\r\n\t\t\t\tcase \"tcPr\":\r\n\t\t\t\t\tresult.styles.push({\r\n\t\t\t\t\t\ttarget: \"td\", //TODO: maybe move to processor\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblStylePr\":\r\n\t\t\t\t\tfor (let s of this.parseTableStyle(n))\r\n\t\t\t\t\t\tresult.styles.push(s);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rsid\":\r\n\t\t\t\tcase \"qFormat\":\r\n\t\t\t\tcase \"hidden\":\r\n\t\t\t\tcase \"semiHidden\":\r\n\t\t\t\tcase \"unhideWhenUsed\":\r\n\t\t\t\tcase \"autoRedefine\":\r\n\t\t\t\tcase \"uiPriority\":\r\n\t\t\t\t\t//TODO: ignore\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tthis.options.debug && console.warn(`DOCX: Unknown style element: ${n.localName}`);\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableStyle(node: Element): IDomSubStyle[] {\r\n\t\tvar result = [];\r\n\r\n\t\tvar type = xml.attr(node, \"type\");\r\n\t\tvar selector = \"\";\r\n\t\tvar modificator = \"\";\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"firstRow\":\r\n\t\t\t\tmodificator = \".first-row\";\r\n\t\t\t\tselector = \"tr.first-row td\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"lastRow\":\r\n\t\t\t\tmodificator = \".last-row\";\r\n\t\t\t\tselector = \"tr.last-row td\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"firstCol\":\r\n\t\t\t\tmodificator = \".first-col\";\r\n\t\t\t\tselector = \"td.first-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"lastCol\":\r\n\t\t\t\tmodificator = \".last-col\";\r\n\t\t\t\tselector = \"td.last-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band1Vert\":\r\n\t\t\t\tmodificator = \":not(.no-vband)\";\r\n\t\t\t\tselector = \"td.odd-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band2Vert\":\r\n\t\t\t\tmodificator = \":not(.no-vband)\";\r\n\t\t\t\tselector = \"td.even-col\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band1Horz\":\r\n\t\t\t\tmodificator = \":not(.no-hband)\";\r\n\t\t\t\tselector = \"tr.odd-row\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"band2Horz\":\r\n\t\t\t\tmodificator = \":not(.no-hband)\";\r\n\t\t\t\tselector = \"tr.even-row\";\r\n\t\t\t\tbreak;\r\n\t\t\tdefault: return [];\r\n\t\t}\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\ttarget: `${selector} p`,\r\n\t\t\t\t\t\tmod: modificator,\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\ttarget: `${selector} span`,\r\n\t\t\t\t\t\tmod: modificator,\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblPr\":\r\n\t\t\t\tcase \"tcPr\":\r\n\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\ttarget: selector, //TODO: maybe move to processor\r\n\t\t\t\t\t\tmod: modificator,\r\n\t\t\t\t\t\tvalues: this.parseDefaultProperties(n, {})\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseNumberingFile(xnums: Element): IDomNumbering[] {\r\n\t\tvar result = [];\r\n\t\tvar mapping = {};\r\n\t\tvar bullets = [];\r\n\r\n\t\txmlUtil.foreach(xnums, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"abstractNum\":\r\n\t\t\t\t\tthis.parseAbstractNumbering(n, bullets)\r\n\t\t\t\t\t\t.forEach(x => result.push(x));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"numPicBullet\":\r\n\t\t\t\t\tbullets.push(this.parseNumberingPicBullet(n));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"num\":\r\n\t\t\t\t\tvar numId = xml.attr(n, \"numId\");\r\n\t\t\t\t\tvar abstractNumId = xml.elementAttr(n, \"abstractNumId\", \"val\");\r\n\t\t\t\t\tmapping[abstractNumId] = numId;\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tresult.forEach(x => x.id = mapping[x.id]);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseNumberingPicBullet(elem: Element): NumberingPicBullet {\r\n\t\tvar pict = xml.element(elem, \"pict\");\r\n\t\tvar shape = pict && xml.element(pict, \"shape\");\r\n\t\tvar imagedata = shape && xml.element(shape, \"imagedata\");\r\n\r\n\t\treturn imagedata ? {\r\n\t\t\tid: xml.intAttr(elem, \"numPicBulletId\"),\r\n\t\t\tsrc: xml.attr(imagedata, \"id\"),\r\n\t\t\tstyle: xml.attr(shape, \"style\")\r\n\t\t} : null;\r\n\t}\r\n\r\n\tparseAbstractNumbering(node: Element, bullets: any[]): IDomNumbering[] {\r\n\t\tvar result = [];\r\n\t\tvar id = xml.attr(node, \"abstractNumId\");\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"lvl\":\r\n\t\t\t\t\tresult.push(this.parseNumberingLevel(id, n, bullets));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseNumberingLevel(id: string, node: Element, bullets: any[]): IDomNumbering {\r\n\t\tvar result: IDomNumbering = {\r\n\t\t\tid: id,\r\n\t\t\tlevel: xml.intAttr(node, \"ilvl\"),\r\n\t\t\tstart: 1,\r\n\t\t\tpStyleName: undefined,\r\n\t\t\tpStyle: {},\r\n\t\t\trStyle: {},\r\n\t\t\tsuff: \"tab\"\r\n\t\t};\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"start\":\r\n\t\t\t\t\tresult.start = xml.intAttr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tthis.parseDefaultProperties(n, result.pStyle);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tthis.parseDefaultProperties(n, result.rStyle);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lvlPicBulletId\":\r\n\t\t\t\t\tvar id = xml.intAttr(n, \"val\");\r\n\t\t\t\t\tresult.bullet = bullets.find(x => x.id == id);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lvlText\":\r\n\t\t\t\t\tresult.levelText = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pStyle\":\r\n\t\t\t\t\tresult.pStyleName = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"numFmt\":\r\n\t\t\t\t\tresult.format = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"suff\":\r\n\t\t\t\t\tresult.suff = xml.attr(n, \"val\");\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseSdt(node: Element, parser: Function): OpenXmlElement[] {\r\n\t\tconst sdtContent = xml.element(node, \"sdtContent\");\r\n\t\treturn sdtContent ? parser(sdtContent) : [];\r\n\t}\r\n\r\n\tparseInserted(node: Element, parentParser: Function): OpenXmlElement {\r\n\t\treturn <OpenXmlElement>{ \r\n\t\t\ttype: DomType.Inserted, \r\n\t\t\tchildren: parentParser(node)?.children ?? []\r\n\t\t};\r\n\t}\r\n\r\n\tparseDeleted(node: Element, parentParser: Function): OpenXmlElement {\r\n\t\treturn <OpenXmlElement>{ \r\n\t\t\ttype: DomType.Deleted, \r\n\t\t\tchildren: parentParser(node)?.children ?? []\r\n\t\t};\r\n\t}\r\n\r\n\tparseParagraph(node: Element): OpenXmlElement {\r\n\t\tvar result = <WmlParagraph>{ type: DomType.Paragraph, children: [] };\r\n\r\n\t\tfor (let el of xml.elements(node)) {\r\n\t\t\tswitch (el.localName) {\r\n\t\t\t\tcase \"pPr\":\r\n\t\t\t\t\tthis.parseParagraphProperties(el, result);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"r\":\r\n\t\t\t\t\tresult.children.push(this.parseRun(el, result));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"hyperlink\":\r\n\t\t\t\t\tresult.children.push(this.parseHyperlink(el, result));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bookmarkStart\":\r\n\t\t\t\t\tresult.children.push(parseBookmarkStart(el, xml));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bookmarkEnd\":\r\n\t\t\t\t\tresult.children.push(parseBookmarkEnd(el, xml));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"oMath\":\r\n\t\t\t\tcase \"oMathPara\":\r\n\t\t\t\t\tresult.children.push(this.parseMathElement(el));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sdt\":\r\n\t\t\t\t\tresult.children.push(...this.parseSdt(el, e => this.parseParagraph(e).children));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"ins\":\r\n\t\t\t\t\tresult.children.push(this.parseInserted(el, e => this.parseParagraph(e)));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"del\":\r\n\t\t\t\t\tresult.children.push(this.parseDeleted(el, e => this.parseParagraph(e)));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseParagraphProperties(elem: Element, paragraph: WmlParagraph) {\r\n\t\tthis.parseDefaultProperties(elem, paragraph.cssStyle = {}, null, c => {\r\n\t\t\tif (parseParagraphProperty(c, paragraph, xml))\r\n\t\t\t\treturn true;\r\n\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"pStyle\":\r\n\t\t\t\t\tparagraph.styleName = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"cnfStyle\":\r\n\t\t\t\t\tparagraph.className = values.classNameOfCnfStyle(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"framePr\":\r\n\t\t\t\t\tthis.parseFrame(c, paragraph);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\t//TODO ignore\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseFrame(node: Element, paragraph: WmlParagraph) {\r\n\t\tvar dropCap = xml.attr(node, \"dropCap\");\r\n\r\n\t\tif (dropCap == \"drop\")\r\n\t\t\tparagraph.cssStyle[\"float\"] = \"left\";\r\n\t}\r\n\r\n\tparseHyperlink(node: Element, parent?: OpenXmlElement): WmlHyperlink {\r\n\t\tvar result: WmlHyperlink = <WmlHyperlink>{ type: DomType.Hyperlink, parent: parent, children: [] };\r\n\t\tvar anchor = xml.attr(node, \"anchor\");\r\n\t\tvar relId = xml.attr(node, \"id\");\r\n\r\n\t\tif (anchor)\r\n\t\t\tresult.href = \"#\" + anchor;\r\n\r\n\t\tif (relId)\r\n\t\t\tresult.id = relId;\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"r\":\r\n\t\t\t\t\tresult.children.push(this.parseRun(c, result));\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseRun(node: Element, parent?: OpenXmlElement): WmlRun {\r\n\t\tvar result: WmlRun = <WmlRun>{ type: DomType.Run, parent: parent, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tc = this.checkAlternateContent(c);\r\n\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"t\":\r\n\t\t\t\t\tresult.children.push(<WmlText>{\r\n\t\t\t\t\t\ttype: DomType.Text,\r\n\t\t\t\t\t\ttext: c.textContent\r\n\t\t\t\t\t});//.replace(\" \", \"\\u00A0\"); // TODO\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"delText\":\r\n\t\t\t\t\tresult.children.push(<WmlText>{\r\n\t\t\t\t\t\ttype: DomType.DeletedText,\r\n\t\t\t\t\t\ttext: c.textContent\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"fldSimple\":\r\n\t\t\t\t\tresult.children.push(<WmlFieldSimple>{\r\n\t\t\t\t\t\ttype: DomType.SimpleField,\r\n\t\t\t\t\t\tinstruction: xml.attr(c, \"instr\"),\r\n\t\t\t\t\t\tlock: xml.boolAttr(c, \"lock\", false),\r\n\t\t\t\t\t\tdirty: xml.boolAttr(c, \"dirty\", false)\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"instrText\":\r\n\t\t\t\t\tresult.fieldRun = true;\r\n\t\t\t\t\tresult.children.push(<WmlInstructionText>{\r\n\t\t\t\t\t\ttype: DomType.Instruction,\r\n\t\t\t\t\t\ttext: c.textContent\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"fldChar\":\r\n\t\t\t\t\tresult.fieldRun = true;\r\n\t\t\t\t\tresult.children.push(<WmlFieldChar>{\r\n\t\t\t\t\t\ttype: DomType.ComplexField,\r\n\t\t\t\t\t\tcharType: xml.attr(c, \"fldCharType\"),\r\n\t\t\t\t\t\tlock: xml.boolAttr(c, \"lock\", false),\r\n\t\t\t\t\t\tdirty: xml.boolAttr(c, \"dirty\", false)\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"noBreakHyphen\":\r\n\t\t\t\t\tresult.children.push({ type: DomType.NoBreakHyphen });\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"br\":\r\n\t\t\t\t\tresult.children.push(<WmlBreak>{\r\n\t\t\t\t\t\ttype: DomType.Break,\r\n\t\t\t\t\t\tbreak: xml.attr(c, \"type\") || \"textWrapping\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lastRenderedPageBreak\":\r\n\t\t\t\t\tresult.children.push(<WmlBreak>{\r\n\t\t\t\t\t\ttype: DomType.Break,\r\n\t\t\t\t\t\tbreak: \"lastRenderedPageBreak\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sym\":\r\n\t\t\t\t\tresult.children.push(<WmlSymbol>{\r\n\t\t\t\t\t\ttype: DomType.Symbol,\r\n\t\t\t\t\t\tfont: xml.attr(c, \"font\"),\r\n\t\t\t\t\t\tchar: xml.attr(c, \"char\")\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tab\":\r\n\t\t\t\t\tresult.children.push({ type: DomType.Tab });\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"footnoteReference\":\r\n\t\t\t\t\tresult.children.push(<WmlNoteReference>{\r\n\t\t\t\t\t\ttype: DomType.FootnoteReference,\r\n\t\t\t\t\t\tid: xml.attr(c, \"id\")\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"endnoteReference\":\r\n\t\t\t\t\tresult.children.push(<WmlNoteReference>{\r\n\t\t\t\t\t\ttype: DomType.EndnoteReference,\r\n\t\t\t\t\t\tid: xml.attr(c, \"id\")\r\n\t\t\t\t\t});\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"drawing\":\r\n\t\t\t\t\tlet d = this.parseDrawing(c);\r\n\r\n\t\t\t\t\tif (d)\r\n\t\t\t\t\t\tresult.children = [d];\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pict\":\r\n\t\t\t\t\tresult.children.push(this.parseVmlPicture(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rPr\":\r\n\t\t\t\t\tthis.parseRunProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseMathElement(elem: Element): OpenXmlElement {\r\n\t\tconst propsTag = `${elem.localName}Pr`;\r\n\t\tconst result = { type: mmlTagMap[elem.localName], children: [] } as OpenXmlElement;\r\n\r\n\t\tfor (const el of xml.elements(elem)) {\r\n\t\t\tconst childType = mmlTagMap[el.localName];\r\n\r\n\t\t\tif (childType) {\r\n\t\t\t\tresult.children.push(this.parseMathElement(el));\r\n\t\t\t} else if (el.localName == \"r\") {\r\n\t\t\t\tvar run = this.parseRun(el);\r\n\t\t\t\trun.type = DomType.MmlRun;\r\n\t\t\t\tresult.children.push(run);\r\n\t\t\t} else if (el.localName == propsTag) {\r\n\t\t\t\tresult.props = this.parseMathProperies(el);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseMathProperies(elem: Element): Record<string, any> {\r\n\t\tconst result: Record<string, any> = {};\r\n\r\n\t\tfor (const el of xml.elements(elem)) {\r\n\t\t\tswitch (el.localName) {\r\n\t\t\t\tcase \"chr\": result.char = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"vertJc\": result.verticalJustification = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"pos\": result.position = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"degHide\": result.hideDegree = xml.boolAttr(el, \"val\"); break;\r\n\t\t\t\tcase \"begChr\": result.beginChar = xml.attr(el, \"val\"); break;\r\n\t\t\t\tcase \"endChr\": result.endChar = xml.attr(el, \"val\"); break;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseRunProperties(elem: Element, run: WmlRun) {\r\n\t\tthis.parseDefaultProperties(elem, run.cssStyle = {}, null, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"rStyle\":\r\n\t\t\t\t\trun.styleName = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vertAlign\":\r\n\t\t\t\t\trun.verticalAlign = values.valueOfVertAlign(c, true);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseVmlPicture(elem: Element): OpenXmlElement {\r\n\t\tconst result = { type: DomType.VmlPicture, children: [] };\r\n\r\n\t\tfor (const el of xml.elements(elem)) {\r\n\t\t\tconst child = parseVmlElement(el, this);\r\n\t\t\tchild && result.children.push(child);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tcheckAlternateContent(elem: Element): Element {\r\n\t\tif (elem.localName != 'AlternateContent')\r\n\t\t\treturn elem;\r\n\r\n\t\tvar choice = xml.element(elem, \"Choice\");\r\n\r\n\t\tif (choice) {\r\n\t\t\tvar requires = xml.attr(choice, \"Requires\");\r\n\t\t\tvar namespaceURI = elem.lookupNamespaceURI(requires);\r\n\r\n\t\t\tif (supportedNamespaceURIs.includes(namespaceURI))\r\n\t\t\t\treturn choice.firstElementChild;\r\n\t\t}\r\n\r\n\t\treturn xml.element(elem, \"Fallback\")?.firstElementChild;\r\n\t}\r\n\r\n\tparseDrawing(node: Element): OpenXmlElement {\r\n\t\tfor (var n of xml.elements(node)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"inline\":\r\n\t\t\t\tcase \"anchor\":\r\n\t\t\t\t\treturn this.parseDrawingWrapper(n);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tparseDrawingWrapper(node: Element): OpenXmlElement {\r\n\t\tvar result = <OpenXmlElement>{ type: DomType.Drawing, children: [], cssStyle: {} };\r\n\t\tvar isAnchor = node.localName == \"anchor\";\r\n\r\n\t\t//TODO\r\n\t\t// result.style[\"margin-left\"] = xml.sizeAttr(node, \"distL\", SizeType.Emu);\r\n\t\t// result.style[\"margin-top\"] = xml.sizeAttr(node, \"distT\", SizeType.Emu);\r\n\t\t// result.style[\"margin-right\"] = xml.sizeAttr(node, \"distR\", SizeType.Emu);\r\n\t\t// result.style[\"margin-bottom\"] = xml.sizeAttr(node, \"distB\", SizeType.Emu);\r\n\r\n\t\tlet wrapType: \"wrapTopAndBottom\" | \"wrapNone\" | null = null;\r\n\t\tlet simplePos = xml.boolAttr(node, \"simplePos\");\r\n\r\n\t\tlet posX = { relative: \"page\", align: \"left\", offset: \"0\" };\r\n\t\tlet posY = { relative: \"page\", align: \"top\", offset: \"0\" };\r\n\r\n\t\tfor (var n of xml.elements(node)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"simplePos\":\r\n\t\t\t\t\tif (simplePos) {\r\n\t\t\t\t\t\tposX.offset = xml.lengthAttr(n, \"x\", LengthUsage.Emu);\r\n\t\t\t\t\t\tposY.offset = xml.lengthAttr(n, \"y\", LengthUsage.Emu);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"extent\":\r\n\t\t\t\t\tresult.cssStyle[\"width\"] = xml.lengthAttr(n, \"cx\", LengthUsage.Emu);\r\n\t\t\t\t\tresult.cssStyle[\"height\"] = xml.lengthAttr(n, \"cy\", LengthUsage.Emu);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"positionH\":\r\n\t\t\t\tcase \"positionV\":\r\n\t\t\t\t\tif (!simplePos) {\r\n\t\t\t\t\t\tlet pos = n.localName == \"positionH\" ? posX : posY;\r\n\t\t\t\t\t\tvar alignNode = xml.element(n, \"align\");\r\n\t\t\t\t\t\tvar offsetNode = xml.element(n, \"posOffset\");\r\n\r\n\t\t\t\t\t\tpos.relative = xml.attr(n, \"relativeFrom\") ?? pos.relative;\r\n\r\n\t\t\t\t\t\tif (alignNode)\r\n\t\t\t\t\t\t\tpos.align = alignNode.textContent;\r\n\r\n\t\t\t\t\t\tif (offsetNode)\r\n\t\t\t\t\t\t\tpos.offset = xmlUtil.sizeValue(offsetNode, LengthUsage.Emu);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"wrapTopAndBottom\":\r\n\t\t\t\t\twrapType = \"wrapTopAndBottom\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"wrapNone\":\r\n\t\t\t\t\twrapType = \"wrapNone\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"graphic\":\r\n\t\t\t\t\tvar g = this.parseGraphic(n);\r\n\r\n\t\t\t\t\tif (g)\r\n\t\t\t\t\t\tresult.children.push(g);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (wrapType == \"wrapTopAndBottom\") {\r\n\t\t\tresult.cssStyle['display'] = 'block';\r\n\r\n\t\t\tif (posX.align) {\r\n\t\t\t\tresult.cssStyle['text-align'] = posX.align;\r\n\t\t\t\tresult.cssStyle['width'] = \"100%\";\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (wrapType == \"wrapNone\") {\r\n\t\t\tresult.cssStyle['display'] = 'block';\r\n\t\t\tresult.cssStyle['position'] = 'relative';\r\n\t\t\tresult.cssStyle[\"width\"] = \"0px\";\r\n\t\t\tresult.cssStyle[\"height\"] = \"0px\";\r\n\r\n\t\t\tif (posX.offset)\r\n\t\t\t\tresult.cssStyle[\"left\"] = posX.offset;\r\n\t\t\tif (posY.offset)\r\n\t\t\t\tresult.cssStyle[\"top\"] = posY.offset;\r\n\t\t}\r\n\t\telse if (isAnchor && (posX.align == 'left' || posX.align == 'right')) {\r\n\t\t\tresult.cssStyle[\"float\"] = posX.align;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseGraphic(elem: Element): OpenXmlElement {\r\n\t\tvar graphicData = xml.element(elem, \"graphicData\");\r\n\r\n\t\tfor (let n of xml.elements(graphicData)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"pic\":\r\n\t\t\t\t\treturn this.parsePicture(n);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\tparsePicture(elem: Element): IDomImage {\r\n\t\tvar result = <IDomImage>{ type: DomType.Image, src: \"\", cssStyle: {} };\r\n\t\tvar blipFill = xml.element(elem, \"blipFill\");\r\n\t\tvar blip = xml.element(blipFill, \"blip\");\r\n\r\n\t\tresult.src = xml.attr(blip, \"embed\");\r\n\r\n\t\tvar spPr = xml.element(elem, \"spPr\");\r\n\t\tvar xfrm = xml.element(spPr, \"xfrm\");\r\n\r\n\t\tresult.cssStyle[\"position\"] = \"relative\";\r\n\r\n\t\tfor (var n of xml.elements(xfrm)) {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"ext\":\r\n\t\t\t\t\tresult.cssStyle[\"width\"] = xml.lengthAttr(n, \"cx\", LengthUsage.Emu);\r\n\t\t\t\t\tresult.cssStyle[\"height\"] = xml.lengthAttr(n, \"cy\", LengthUsage.Emu);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"off\":\r\n\t\t\t\t\tresult.cssStyle[\"left\"] = xml.lengthAttr(n, \"x\", LengthUsage.Emu);\r\n\t\t\t\t\tresult.cssStyle[\"top\"] = xml.lengthAttr(n, \"y\", LengthUsage.Emu);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTable(node: Element): WmlTable {\r\n\t\tvar result: WmlTable = { type: DomType.Table, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tr\":\r\n\t\t\t\t\tresult.children.push(this.parseTableRow(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblGrid\":\r\n\t\t\t\t\tresult.columns = this.parseTableColumns(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblPr\":\r\n\t\t\t\t\tthis.parseTableProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableColumns(node: Element): WmlTableColumn[] {\r\n\t\tvar result = [];\r\n\r\n\t\txmlUtil.foreach(node, n => {\r\n\t\t\tswitch (n.localName) {\r\n\t\t\t\tcase \"gridCol\":\r\n\t\t\t\t\tresult.push({ width: xml.lengthAttr(n, \"w\") });\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableProperties(elem: Element, table: WmlTable) {\r\n\t\ttable.cssStyle = {};\r\n\t\ttable.cellStyle = {};\r\n\r\n\t\tthis.parseDefaultProperties(elem, table.cssStyle, table.cellStyle, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tblStyle\":\r\n\t\t\t\t\ttable.styleName = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblLook\":\r\n\t\t\t\t\ttable.className = values.classNameOftblLook(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblpPr\":\r\n\t\t\t\t\tthis.parseTablePosition(c, table);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblStyleColBandSize\":\r\n\t\t\t\t\ttable.colBandSize = xml.intAttr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblStyleRowBandSize\":\r\n\t\t\t\t\ttable.rowBandSize = xml.intAttr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\r\n\t\tswitch (table.cssStyle[\"text-align\"]) {\r\n\t\t\tcase \"center\":\r\n\t\t\t\tdelete table.cssStyle[\"text-align\"];\r\n\t\t\t\ttable.cssStyle[\"margin-left\"] = \"auto\";\r\n\t\t\t\ttable.cssStyle[\"margin-right\"] = \"auto\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"right\":\r\n\t\t\t\tdelete table.cssStyle[\"text-align\"];\r\n\t\t\t\ttable.cssStyle[\"margin-left\"] = \"auto\";\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\tparseTablePosition(node: Element, table: WmlTable) {\r\n\t\tvar topFromText = xml.lengthAttr(node, \"topFromText\");\r\n\t\tvar bottomFromText = xml.lengthAttr(node, \"bottomFromText\");\r\n\t\tvar rightFromText = xml.lengthAttr(node, \"rightFromText\");\r\n\t\tvar leftFromText = xml.lengthAttr(node, \"leftFromText\");\r\n\r\n\t\ttable.cssStyle[\"float\"] = 'left';\r\n\t\ttable.cssStyle[\"margin-bottom\"] = values.addSize(table.cssStyle[\"margin-bottom\"], bottomFromText);\r\n\t\ttable.cssStyle[\"margin-left\"] = values.addSize(table.cssStyle[\"margin-left\"], leftFromText);\r\n\t\ttable.cssStyle[\"margin-right\"] = values.addSize(table.cssStyle[\"margin-right\"], rightFromText);\r\n\t\ttable.cssStyle[\"margin-top\"] = values.addSize(table.cssStyle[\"margin-top\"], topFromText);\r\n\t}\r\n\r\n\tparseTableRow(node: Element): WmlTableRow {\r\n\t\tvar result: WmlTableRow = { type: DomType.Row, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tc\":\r\n\t\t\t\t\tresult.children.push(this.parseTableCell(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"trPr\":\r\n\t\t\t\t\tthis.parseTableRowProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableRowProperties(elem: Element, row: WmlTableRow) {\r\n\t\trow.cssStyle = this.parseDefaultProperties(elem, {}, null, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"cnfStyle\":\r\n\t\t\t\t\trow.className = values.classNameOfCnfStyle(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblHeader\":\r\n\t\t\t\t\trow.isHeader = xml.boolAttr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseTableCell(node: Element): OpenXmlElement {\r\n\t\tvar result: WmlTableCell = { type: DomType.Cell, children: [] };\r\n\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"tbl\":\r\n\t\t\t\t\tresult.children.push(this.parseTable(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"p\":\r\n\t\t\t\t\tresult.children.push(this.parseParagraph(c));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tcPr\":\r\n\t\t\t\t\tthis.parseTableCellProperties(c, result);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tparseTableCellProperties(elem: Element, cell: WmlTableCell) {\r\n\t\tcell.cssStyle = this.parseDefaultProperties(elem, {}, null, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"gridSpan\":\r\n\t\t\t\t\tcell.span = xml.intAttr(c, \"val\", null);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vMerge\":\r\n\t\t\t\t\tcell.verticalMerge = xml.attr(c, \"val\") ?? \"continue\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"cnfStyle\":\r\n\t\t\t\t\tcell.className = values.classNameOfCnfStyle(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t});\r\n\t}\r\n\r\n\tparseDefaultProperties(elem: Element, style: Record<string, string> = null, childStyle: Record<string, string> = null, handler: (prop: Element) => boolean = null): Record<string, string> {\r\n\t\tstyle = style || {};\r\n\r\n\t\txmlUtil.foreach(elem, c => {\r\n\t\t\tif (handler?.(c))\r\n\t\t\t\treturn;\r\n\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"jc\":\r\n\t\t\t\t\tstyle[\"text-align\"] = values.valueOfJc(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"textAlignment\":\r\n\t\t\t\t\tstyle[\"vertical-align\"] = values.valueOfTextAlignment(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"color\":\r\n\t\t\t\t\tstyle[\"color\"] = xmlUtil.colorAttr(c, \"val\", null, autos.color);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"sz\":\r\n\t\t\t\t\tstyle[\"font-size\"] = style[\"min-height\"] = xml.lengthAttr(c, \"val\", LengthUsage.FontSize);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"shd\":\r\n\t\t\t\t\tstyle[\"background-color\"] = xmlUtil.colorAttr(c, \"fill\", null, autos.shd);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"highlight\":\r\n\t\t\t\t\tstyle[\"background-color\"] = xmlUtil.colorAttr(c, \"val\", null, autos.highlight);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vertAlign\":\r\n\t\t\t\t\t//TODO\r\n\t\t\t\t\t// style.verticalAlign = values.valueOfVertAlign(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"position\":\r\n\t\t\t\t\tstyle.verticalAlign = xml.lengthAttr(c, \"val\", LengthUsage.FontSize);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tcW\":\r\n\t\t\t\t\tif (this.options.ignoreWidth)\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblW\":\r\n\t\t\t\t\tstyle[\"width\"] = values.valueOfSize(c, \"w\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"trHeight\":\r\n\t\t\t\t\tthis.parseTrHeight(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"strike\":\r\n\t\t\t\t\tstyle[\"text-decoration\"] = xml.boolAttr(c, \"val\", true) ? \"line-through\" : \"none\"\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"b\":\r\n\t\t\t\t\tstyle[\"font-weight\"] = xml.boolAttr(c, \"val\", true) ? \"bold\" : \"normal\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"i\":\r\n\t\t\t\t\tstyle[\"font-style\"] = xml.boolAttr(c, \"val\", true) ? \"italic\" : \"normal\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"caps\":\r\n\t\t\t\t\tstyle[\"text-transform\"] = xml.boolAttr(c, \"val\", true) ? \"uppercase\" : \"none\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"smallCaps\":\r\n\t\t\t\t\tstyle[\"text-transform\"] = xml.boolAttr(c, \"val\", true) ? \"lowercase\" : \"none\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"u\":\r\n\t\t\t\t\tthis.parseUnderline(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"ind\":\r\n\t\t\t\tcase \"tblInd\":\r\n\t\t\t\t\tthis.parseIndentation(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"rFonts\":\r\n\t\t\t\t\tthis.parseFont(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblBorders\":\r\n\t\t\t\t\tthis.parseBorderProperties(c, childStyle || style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblCellSpacing\":\r\n\t\t\t\t\tstyle[\"border-spacing\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tstyle[\"border-collapse\"] = \"separate\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"pBdr\":\r\n\t\t\t\t\tthis.parseBorderProperties(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bdr\":\r\n\t\t\t\t\tstyle[\"border\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tcBorders\":\r\n\t\t\t\t\tthis.parseBorderProperties(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vanish\":\r\n\t\t\t\t\tif (xml.boolAttr(c, \"val\", true))\r\n\t\t\t\t\t\tstyle[\"display\"] = \"none\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"kern\":\r\n\t\t\t\t\t//TODO\r\n\t\t\t\t\t//style['letter-spacing'] = xml.lengthAttr(elem, 'val', LengthUsage.FontSize);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"noWrap\":\r\n\t\t\t\t\t//TODO\r\n\t\t\t\t\t//style[\"white-space\"] = \"nowrap\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblCellMar\":\r\n\t\t\t\tcase \"tcMar\":\r\n\t\t\t\t\tthis.parseMarginProperties(c, childStyle || style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"tblLayout\":\r\n\t\t\t\t\tstyle[\"table-layout\"] = values.valueOfTblLayout(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"vAlign\":\r\n\t\t\t\t\tstyle[\"vertical-align\"] = values.valueOfTextAlignment(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"spacing\":\r\n\t\t\t\t\tif (elem.localName == \"pPr\")\r\n\t\t\t\t\t\tthis.parseSpacing(c, style);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"wordWrap\":\r\n\t\t\t\t\tif (xml.boolAttr(c, \"val\")) //TODO: test with examples\r\n\t\t\t\t\t\tstyle[\"overflow-wrap\"] = \"break-word\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"suppressAutoHyphens\":\r\n\t\t\t\t\tstyle[\"hyphens\"] = xml.boolAttr(c, \"val\", true) ? \"none\" : \"auto\";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"lang\":\r\n\t\t\t\t\tstyle[\"$lang\"] = xml.attr(c, \"val\");\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bCs\":\r\n\t\t\t\tcase \"iCs\":\r\n\t\t\t\tcase \"szCs\":\r\n\t\t\t\tcase \"tabs\": //ignore - tabs is parsed by other parser\r\n\t\t\t\tcase \"outlineLvl\": //TODO\r\n\t\t\t\tcase \"contextualSpacing\": //TODO\r\n\t\t\t\tcase \"tblStyleColBandSize\": //TODO\r\n\t\t\t\tcase \"tblStyleRowBandSize\": //TODO\r\n\t\t\t\tcase \"webHidden\": //TODO - maybe web-hidden should be implemented\r\n\t\t\t\tcase \"pageBreakBefore\": //TODO - maybe ignore \r\n\t\t\t\tcase \"suppressLineNumbers\": //TODO - maybe ignore\r\n\t\t\t\tcase \"keepLines\": //TODO - maybe ignore\r\n\t\t\t\tcase \"keepNext\": //TODO - maybe ignore\r\n\t\t\t\tcase \"widowControl\": //TODO - maybe ignore \r\n\t\t\t\tcase \"bidi\": //TODO - maybe ignore\r\n\t\t\t\tcase \"rtl\": //TODO - maybe ignore\r\n\t\t\t\tcase \"noProof\": //ignore spellcheck\r\n\t\t\t\t\t//TODO ignore\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tif (this.options.debug)\r\n\t\t\t\t\t\tconsole.warn(`DOCX: Unknown document element: ${elem.localName}.${c.localName}`);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn style;\r\n\t}\r\n\r\n\tparseUnderline(node: Element, style: Record<string, string>) {\r\n\t\tvar val = xml.attr(node, \"val\");\r\n\r\n\t\tif (val == null)\r\n\t\t\treturn;\r\n\r\n\t\tswitch (val) {\r\n\t\t\tcase \"dash\":\r\n\t\t\tcase \"dashDotDotHeavy\":\r\n\t\t\tcase \"dashDotHeavy\":\r\n\t\t\tcase \"dashedHeavy\":\r\n\t\t\tcase \"dashLong\":\r\n\t\t\tcase \"dashLongHeavy\":\r\n\t\t\tcase \"dotDash\":\r\n\t\t\tcase \"dotDotDash\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline dashed\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"dotted\":\r\n\t\t\tcase \"dottedHeavy\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline dotted\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"double\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline double\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"single\":\r\n\t\t\tcase \"thick\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"wave\":\r\n\t\t\tcase \"wavyDouble\":\r\n\t\t\tcase \"wavyHeavy\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline wavy\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"words\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"underline\";\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"none\":\r\n\t\t\t\tstyle[\"text-decoration\"] = \"none\";\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\tvar col = xmlUtil.colorAttr(node, \"color\");\r\n\r\n\t\tif (col)\r\n\t\t\tstyle[\"text-decoration-color\"] = col;\r\n\t}\r\n\r\n\tparseFont(node: Element, style: Record<string, string>) {\r\n\t\tvar ascii = xml.attr(node, \"ascii\");\r\n\t\tvar asciiTheme = values.themeValue(node, \"asciiTheme\");\r\n\r\n\t\tvar fonts = [ascii, asciiTheme].filter(x => x).join(', ');\r\n\r\n\t\tif (fonts.length > 0)\r\n\t\t\tstyle[\"font-family\"] = fonts;\r\n\t}\r\n\r\n\tparseIndentation(node: Element, style: Record<string, string>) {\r\n\t\tvar firstLine = xml.lengthAttr(node, \"firstLine\");\r\n\t\tvar hanging = xml.lengthAttr(node, \"hanging\");\r\n\t\tvar left = xml.lengthAttr(node, \"left\");\r\n\t\tvar start = xml.lengthAttr(node, \"start\");\r\n\t\tvar right = xml.lengthAttr(node, \"right\");\r\n\t\tvar end = xml.lengthAttr(node, \"end\");\r\n\r\n\t\tif (firstLine) style[\"text-indent\"] = firstLine;\r\n\t\tif (hanging) style[\"text-indent\"] = `-${hanging}`;\r\n\t\tif (left || start) style[\"margin-left\"] = left || start;\r\n\t\tif (right || end) style[\"margin-right\"] = right || end;\r\n\t}\r\n\r\n\tparseSpacing(node: Element, style: Record<string, string>) {\r\n\t\tvar before = xml.lengthAttr(node, \"before\");\r\n\t\tvar after = xml.lengthAttr(node, \"after\");\r\n\t\tvar line = xml.intAttr(node, \"line\", null);\r\n\t\tvar lineRule = xml.attr(node, \"lineRule\");\r\n\r\n\t\tif (before) style[\"margin-top\"] = before;\r\n\t\tif (after) style[\"margin-bottom\"] = after;\r\n\r\n\t\tif (line !== null) {\r\n\t\t\tswitch (lineRule) {\r\n\t\t\t\tcase \"auto\":\r\n\t\t\t\t\tstyle[\"line-height\"] = `${(line / 240).toFixed(2)}`;\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"atLeast\":\r\n\t\t\t\t\tstyle[\"line-height\"] = `calc(100% + ${line / 20}pt)`;\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tstyle[\"line-height\"] = style[\"min-height\"] = `${line / 20}pt`\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tparseMarginProperties(node: Element, output: Record<string, string>) {\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"left\":\r\n\t\t\t\t\toutput[\"padding-left\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"right\":\r\n\t\t\t\t\toutput[\"padding-right\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"top\":\r\n\t\t\t\t\toutput[\"padding-top\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\toutput[\"padding-bottom\"] = values.valueOfMargin(c);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\r\n\tparseTrHeight(node: Element, output: Record<string, string>) {\r\n\t\tswitch (xml.attr(node, \"hRule\")) {\r\n\t\t\tcase \"exact\":\r\n\t\t\t\toutput[\"height\"] = xml.lengthAttr(node, \"val\");\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"atLeast\":\r\n\t\t\tdefault:\r\n\t\t\t\toutput[\"height\"] = xml.lengthAttr(node, \"val\");\r\n\t\t\t\t// min-height doesn't work for tr\r\n\t\t\t\t//output[\"min-height\"] = xml.sizeAttr(node, \"val\");  \r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\tparseBorderProperties(node: Element, output: Record<string, string>) {\r\n\t\txmlUtil.foreach(node, c => {\r\n\t\t\tswitch (c.localName) {\r\n\t\t\t\tcase \"start\":\r\n\t\t\t\tcase \"left\":\r\n\t\t\t\t\toutput[\"border-left\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"end\":\r\n\t\t\t\tcase \"right\":\r\n\t\t\t\t\toutput[\"border-right\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"top\":\r\n\t\t\t\t\toutput[\"border-top\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\toutput[\"border-bottom\"] = values.valueOfBorder(c);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n}\r\n\r\nconst knownColors = ['black', 'blue', 'cyan', 'darkBlue', 'darkCyan', 'darkGray', 'darkGreen', 'darkMagenta', 'darkRed', 'darkYellow', 'green', 'lightGray', 'magenta', 'none', 'red', 'white', 'yellow'];\r\n\r\nclass xmlUtil {\r\n\tstatic foreach(node: Element, cb: (n: Element) => void) {\r\n\t\tfor (var i = 0; i < node.childNodes.length; i++) {\r\n\t\t\tlet n = node.childNodes[i];\r\n\r\n\t\t\tif (n.nodeType == Node.ELEMENT_NODE)\r\n\t\t\t\tcb(<Element>n);\r\n\t\t}\r\n\t}\r\n\r\n\tstatic colorAttr(node: Element, attrName: string, defValue: string = null, autoColor: string = 'black') {\r\n\t\tvar v = xml.attr(node, attrName);\r\n\r\n\t\tif (v) {\r\n\t\t\tif (v == \"auto\") {\r\n\t\t\t\treturn autoColor;\r\n\t\t\t} else if (knownColors.includes(v)) {\r\n\t\t\t\treturn v;\r\n\t\t\t}\r\n\r\n\t\t\treturn `#${v}`;\r\n\t\t}\r\n\r\n\t\tvar themeColor = xml.attr(node, \"themeColor\");\r\n\r\n\t\treturn themeColor ? `var(--docx-${themeColor}-color)` : defValue;\r\n\t}\r\n\r\n\tstatic sizeValue(node: Element, type: LengthUsageType = LengthUsage.Dxa) {\r\n\t\treturn convertLength(node.textContent, type);\r\n\t}\r\n}\r\n\r\nclass values {\r\n\tstatic themeValue(c: Element, attr: string) {\r\n\t\tvar val = xml.attr(c, attr);\r\n\t\treturn val ? `var(--docx-${val}-font)` : null;\r\n\t}\r\n\r\n\tstatic valueOfSize(c: Element, attr: string) {\r\n\t\tvar type = LengthUsage.Dxa;\r\n\r\n\t\tswitch (xml.attr(c, \"type\")) {\r\n\t\t\tcase \"dxa\": break;\r\n\t\t\tcase \"pct\": type = LengthUsage.Percent; break;\r\n\t\t\tcase \"auto\": return \"auto\";\r\n\t\t}\r\n\r\n\t\treturn xml.lengthAttr(c, attr, type);\r\n\t}\r\n\r\n\tstatic valueOfMargin(c: Element) {\r\n\t\treturn xml.lengthAttr(c, \"w\");\r\n\t}\r\n\r\n\tstatic valueOfBorder(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tif (type == \"nil\")\r\n\t\t\treturn \"none\";\r\n\r\n\t\tvar color = xmlUtil.colorAttr(c, \"color\");\r\n\t\tvar size = xml.lengthAttr(c, \"sz\", LengthUsage.Border);\r\n\r\n\t\treturn `${size} solid ${color == \"auto\" ? autos.borderColor : color}`;\r\n\t}\r\n\r\n\tstatic valueOfTblLayout(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\t\treturn type == \"fixed\" ? \"fixed\" : \"auto\";\r\n\t}\r\n\r\n\tstatic classNameOfCnfStyle(c: Element) {\r\n\t\tconst val = xml.attr(c, \"val\");\r\n\t\tconst classes = [\r\n\t\t\t'first-row', 'last-row', 'first-col', 'last-col',\r\n\t\t\t'odd-col', 'even-col', 'odd-row', 'even-row',\r\n\t\t\t'ne-cell', 'nw-cell', 'se-cell', 'sw-cell'\r\n\t\t];\r\n\r\n\t\treturn classes.filter((_, i) => val[i] == '1').join(' ');\r\n\t}\r\n\r\n\tstatic valueOfJc(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"start\":\r\n\t\t\tcase \"left\": return \"left\";\r\n\t\t\tcase \"center\": return \"center\";\r\n\t\t\tcase \"end\":\r\n\t\t\tcase \"right\": return \"right\";\r\n\t\t\tcase \"both\": return \"justify\";\r\n\t\t}\r\n\r\n\t\treturn type;\r\n\t}\r\n\r\n\tstatic valueOfVertAlign(c: Element, asTagName: boolean = false) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"subscript\": return \"sub\";\r\n\t\t\tcase \"superscript\": return asTagName ? \"sup\" : \"super\";\r\n\t\t}\r\n\r\n\t\treturn asTagName ? null : type;\r\n\t}\r\n\r\n\tstatic valueOfTextAlignment(c: Element) {\r\n\t\tvar type = xml.attr(c, \"val\");\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase \"auto\":\r\n\t\t\tcase \"baseline\": return \"baseline\";\r\n\t\t\tcase \"top\": return \"top\";\r\n\t\t\tcase \"center\": return \"middle\";\r\n\t\t\tcase \"bottom\": return \"bottom\";\r\n\t\t}\r\n\r\n\t\treturn type;\r\n\t}\r\n\r\n\tstatic addSize(a: string, b: string): string {\r\n\t\tif (a == null) return b;\r\n\t\tif (b == null) return a;\r\n\r\n\t\treturn `calc(${a} + ${b})`; //TODO\r\n\t}\r\n\r\n\tstatic classNameOftblLook(c: Element) {\r\n\t\tconst val = xml.hexAttr(c, \"val\", 0);\r\n\t\tlet className = \"\";\r\n\r\n\t\tif (xml.boolAttr(c, \"firstRow\") || (val & 0x0020)) className += \" first-row\";\r\n\t\tif (xml.boolAttr(c, \"lastRow\") || (val & 0x0040)) className += \" last-row\";\r\n\t\tif (xml.boolAttr(c, \"firstColumn\") || (val & 0x0080)) className += \" first-col\";\r\n\t\tif (xml.boolAttr(c, \"lastColumn\") || (val & 0x0100)) className += \" last-col\";\r\n\t\tif (xml.boolAttr(c, \"noHBand\") || (val & 0x0200)) className += \" no-hband\";\r\n\t\tif (xml.boolAttr(c, \"noVBand\") || (val & 0x0400)) className += \" no-vband\";\r\n\r\n\t\treturn className.trim();\r\n\t}\r\n}", "import { Part } from \"../common/part\";\r\nimport { CorePropsDeclaration, parseCoreProps } from \"./core-props\";\r\n\r\nexport class CorePropsPart extends Part {\r\n    props: CorePropsDeclaration;\r\n\r\n    parseXml(root: Element) {\r\n        this.props = parseCoreProps(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface CorePropsDeclaration {\r\n    title: string,\r\n    description: string,\r\n    subject: string,\r\n    creator: string,\r\n    keywords: string,\r\n    language: string,\r\n    lastModifiedBy: string,\r\n    revision: number,\r\n}\r\n\r\nexport function parseCoreProps(root: Element, xmlParser: XmlParser): CorePropsDeclaration {\r\n    const result = <CorePropsDeclaration>{};\r\n\r\n    for (let el of xmlParser.elements(root)) {\r\n        switch (el.localName) {\r\n            case \"title\": result.title = el.textContent; break;\r\n            case \"description\": result.description = el.textContent; break;\r\n            case \"subject\": result.subject = el.textContent; break;\r\n            case \"creator\": result.creator = el.textContent; break;\r\n            case \"keywords\": result.keywords = el.textContent; break;\r\n            case \"language\": result.language = el.textContent; break;\r\n            case \"lastModifiedBy\": result.lastModifiedBy = el.textContent; break;\r\n            case \"revision\": el.textContent && (result.revision = parseInt(el.textContent)); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}", "import { Part } from \"../common/part\";\r\nimport { CustomProperty, parseCustomProps } from \"./custom-props\";\r\n\r\nexport class CustomPropsPart extends Part {\r\n    props: CustomProperty[];\r\n\r\n    parseXml(root: Element) {\r\n        this.props = parseCustomProps(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface CustomProperty {\r\n\tformatId: string;\r\n\tname: string;\r\n\ttype: string;\r\n\tvalue: string;\r\n}\r\n\r\nexport function parseCustomProps(root: Element, xml: XmlParser): CustomProperty[] {\r\n\treturn xml.elements(root, \"property\").map(e => {\r\n\t\tconst firstChild = e.firstChild;\r\n\r\n\t\treturn {\r\n\t\t\tformatId: xml.attr(e, \"fmtid\"),\r\n\t\t\tname: xml.attr(e, \"name\"),\r\n\t\t\ttype: firstChild.nodeName,\r\n\t\t\tvalue: firstChild.textContent\r\n\t\t};\r\n\t});\r\n}", "import { Part } from \"../common/part\";\r\nimport { ExtendedPropsDeclaration, parseExtendedProps } from \"./extended-props\";\r\n\r\nexport class ExtendedPropsPart extends Part {\r\n    props: ExtendedPropsDeclaration;\r\n\r\n    parseXml(root: Element) {\r\n        this.props = parseExtendedProps(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface ExtendedPropsDeclaration {\r\n    template: string,\r\n    totalTime: number,\r\n    pages: number,\r\n    words: number,\r\n    characters: number,\r\n    application: string,\r\n    lines: number,\r\n    paragraphs: number,\r\n    company: string,\r\n    appVersion: string\r\n}\r\n\r\nexport function parseExtendedProps(root: Element, xmlParser: XmlParser): ExtendedPropsDeclaration {\r\n    const result = <ExtendedPropsDeclaration>{\r\n\r\n    };\r\n\r\n    for (let el of xmlParser.elements(root)) {\r\n        switch (el.localName) {\r\n            case \"Template\":\r\n                result.template = el.textContent;\r\n                break;\r\n            case \"Pages\":\r\n                result.pages = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Words\":\r\n                result.words = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Characters\":\r\n                result.characters = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Application\":\r\n                result.application = el.textContent;\r\n                break;\r\n            case \"Lines\":\r\n                result.lines = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Paragraphs\":\r\n                result.paragraphs = safeParseToInt(el.textContent);\r\n                break;\r\n            case \"Company\":\r\n                result.company = el.textContent;\r\n                break;\r\n            case \"AppVersion\":\r\n                result.appVersion = el.textContent;\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nfunction safeParseToInt(value: string): number {\r\n    if (typeof value === 'undefined')\r\n        return;\r\n    return parseInt(value);\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { DomType, OpenXmlElement } from \"./dom\";\r\n\r\nexport interface WmlBookmarkStart extends OpenXmlElement {\r\n    id: string;\r\n    name: string;\r\n    colFirst: number;\r\n    colLast: number;\r\n}\r\n\r\nexport interface WmlBookmarkEnd extends OpenXmlElement {\r\n    id: string;\r\n}\r\n\r\nexport function parseBookmarkStart(elem: Element, xml: XmlParser): WmlBookmarkStart {\r\n    return {\r\n        type: DomType.BookmarkStart,\r\n        id: xml.attr(elem, \"id\"),\r\n        name: xml.attr(elem, \"name\"),\r\n        colFirst: xml.intAttr(elem, \"colFirst\"),\r\n        colLast: xml.intAttr(elem, \"colLast\")\r\n    }\r\n}\r\n\r\nexport function parseBookmarkEnd(elem: Element, xml: XmlParser): WmlBookmarkEnd {\r\n    return {\r\n        type: DomType.BookmarkEnd,\r\n        id: xml.attr(elem, \"id\")\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { Length, LengthUsage } from \"./common\";\r\n\r\nexport interface Border {\r\n    color: string;\r\n    type: string;\r\n    size: Length;\r\n    frame: boolean;\r\n    shadow: boolean;\r\n    offset: Length;\r\n}\r\n\r\nexport interface Borders {\r\n    top: Border;\r\n    left: Border;\r\n    right: Border;\r\n    bottom: Border;\r\n}\r\n\r\nexport function parseBorder(elem: Element, xml: XmlParser): Border {\r\n    return {\r\n        type: xml.attr(elem, \"val\"),\r\n        color: xml.attr(elem, \"color\"),\r\n        size: xml.lengthAttr(elem, \"sz\", LengthUsage.Border),\r\n        offset: xml.lengthAttr(elem, \"space\", LengthUsage.Point),\r\n        frame: xml.boolAttr(elem, 'frame'),\r\n        shadow: xml.boolAttr(elem, 'shadow')\r\n    };\r\n}\r\n\r\nexport function parseBorders(elem: Element, xml: XmlParser): Borders {\r\n    var result = <Borders>{};\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"left\": result.left = parseBorder(e, xml); break;\r\n            case \"top\": result.top = parseBorder(e, xml); break;\r\n            case \"right\": result.right = parseBorder(e, xml); break;\r\n            case \"bottom\": result.bottom = parseBorder(e, xml); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport const ns = {\r\n    wordml: \"http://schemas.openxmlformats.org/wordprocessingml/2006/main\",\r\n    drawingml: \"http://schemas.openxmlformats.org/drawingml/2006/main\",\r\n    picture: \"http://schemas.openxmlformats.org/drawingml/2006/picture\",\r\n\tcompatibility: \"http://schemas.openxmlformats.org/markup-compatibility/2006\",\r\n\tmath: \"http://schemas.openxmlformats.org/officeDocument/2006/math\"\r\n}\r\n\r\nexport type LengthType = \"px\" | \"pt\" | \"%\" | \"\";\r\nexport type Length = string;\r\n\r\nexport interface Font {\r\n    name: string;\r\n    family: string;\r\n}\r\n\r\nexport interface CommonProperties {\r\n    fontSize: Length;\r\n    color: string;\r\n}\r\n\r\nexport type LengthUsageType = { mul: number, unit: LengthType };\r\n\r\nexport const LengthUsage: Record<string, LengthUsageType> = {\r\n    Dxa: { mul: 0.05, unit: \"pt\" }, //twips\r\n    Emu: { mul: 1 / 12700, unit: \"pt\" },\r\n    FontSize: { mul: 0.5, unit: \"pt\" },\r\n    Border: { mul: 0.125, unit: \"pt\" },\r\n    Point: { mul: 1, unit: \"pt\" },\r\n    Percent: { mul: 0.02, unit: \"%\" },\r\n    LineHeight: { mul: 1 / 240, unit: \"\" },\r\n    VmlEmu: { mul: 1 / 12700, unit: \"\" },\r\n}\r\n\r\nexport function convertLength(val: string, usage: LengthUsageType = LengthUsage.Dxa): string {\r\n    //\"simplified\" docx documents use pt's as units\r\n    if (val == null || /.+(p[xt]|[%])$/.test(val)) {\r\n        return val;\r\n    }\r\n\r\n\treturn `${(parseInt(val) * usage.mul).toFixed(2)}${usage.unit}`;\r\n}\r\n\r\nexport function convertBoolean(v: string, defaultValue = false): boolean {\r\n    switch (v) {\r\n        case \"1\": return true;\r\n        case \"0\": return false;\r\n        case \"on\": return true;\r\n        case \"off\": return false;\r\n        case \"true\": return true;\r\n        case \"false\": return false;\r\n        default: return defaultValue;\r\n    }\r\n}\r\n\r\nexport function convertPercentage(val: string): number {\r\n    return val ? parseInt(val) / 100 : null;\r\n}\r\n\r\nexport function parseCommonProperty(elem: Element, props: CommonProperties, xml: XmlParser): boolean {\r\n    if(elem.namespaceURI != ns.wordml)\r\n        return false;\r\n\r\n    switch(elem.localName) {\r\n        case \"color\": \r\n            props.color = xml.attr(elem, \"val\");\r\n            break;\r\n\r\n        case \"sz\":\r\n            props.fontSize = xml.lengthAttr(elem, \"val\", LengthUsage.FontSize);\r\n            break;\r\n\r\n        default:\r\n            return false;\r\n    }\r\n\r\n    return true;\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { DocumentElement } from \"./document\";\r\n\r\nexport class DocumentPart extends Part {\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n    \r\n    body: DocumentElement\r\n\r\n    parseXml(root: Element) {\r\n        this.body = this._documentParser.parseDocumentFile(root);\r\n    }\r\n}", "export enum DomType {\r\n    Document = \"document\",\r\n    Paragraph = \"paragraph\",\r\n    Run = \"run\",\r\n    Break = \"break\",\r\n    NoBreakHyphen = \"noBreakHyphen\",\r\n    Table = \"table\",\r\n    Row = \"row\",\r\n    Cell = \"cell\",\r\n    Hyperlink = \"hyperlink\",\r\n    Drawing = \"drawing\",\r\n    Image = \"image\",\r\n    Text = \"text\",\r\n    Tab = \"tab\",\r\n    Symbol = \"symbol\",\r\n    BookmarkStart = \"bookmarkStart\",\r\n    BookmarkEnd = \"bookmarkEnd\",\r\n    Footer = \"footer\",\r\n    Header = \"header\",\r\n    FootnoteReference = \"footnoteReference\", \r\n\tEndnoteReference = \"endnoteReference\",\r\n    Footnote = \"footnote\",\r\n    Endnote = \"endnote\",\r\n    SimpleField = \"simpleField\",\r\n    ComplexField = \"complexField\",\r\n    Instruction = \"instruction\",\r\n\tVmlPicture = \"vmlPicture\",\r\n\tMmlMath = \"mmlMath\",\r\n\tMmlMathParagraph = \"mmlMathParagraph\",\r\n\tMmlFraction = \"mmlFraction\",\r\n\tMmlFunction = \"mmlFunction\",\r\n\tMmlFunctionName = \"mmlFunctionName\",\r\n\tMmlNumerator = \"mmlNumerator\",\r\n\tMmlDenominator = \"mmlDenominator\",\r\n\tMmlRadical = \"mmlRadical\",\r\n\tMmlBase = \"mmlBase\",\r\n\tMmlDegree = \"mmlDegree\",\r\n\tMmlSuperscript = \"mmlSuperscript\",\r\n\tMmlSubscript = \"mmlSubscript\",\r\n\tMmlPreSubSuper = \"mmlPreSubSuper\",\r\n\tMmlSubArgument = \"mmlSubArgument\",\r\n\tMmlSuperArgument = \"mmlSuperArgument\",\r\n\tMmlNary = \"mmlNary\",\r\n\tMmlDelimiter = \"mmlDelimiter\",\r\n\tMmlRun = \"mmlRun\",\r\n\tMmlEquationArray = \"mmlEquationArray\",\r\n\tMmlLimit = \"mmlLimit\",\r\n\tMmlLimitLower = \"mmlLimitLower\",\r\n\tMmlMatrix = \"mmlMatrix\",\r\n\tMmlMatrixRow = \"mmlMatrixRow\",\r\n\tMmlBox = \"mmlBox\",\r\n\tMmlBar = \"mmlBar\",\r\n\tMmlGroupChar = \"mmlGroupChar\",\r\n\tVmlElement = \"vmlElement\",\r\n\tInserted = \"inserted\",\r\n\tDeleted = \"deleted\",\r\n\tDeletedText = \"deletedText\"\r\n}\r\n\r\nexport interface OpenXmlElement {\r\n    type: DomType;\r\n    children?: OpenXmlElement[];\r\n    cssStyle?: Record<string, string>;\r\n    props?: Record<string, any>;\r\n    \r\n\tstyleName?: string; //style name\r\n\tclassName?: string; //class mods\r\n\r\n    parent?: OpenXmlElement;\r\n}\r\n\r\nexport abstract class OpenXmlElementBase implements OpenXmlElement {\r\n    type: DomType;\r\n    children?: OpenXmlElement[] = [];\r\n    cssStyle?: Record<string, string> = {};\r\n    props?: Record<string, any>;\r\n\r\n    className?: string;\r\n    styleName?: string;\r\n\r\n    parent?: OpenXmlElement;\r\n}\r\n\r\nexport interface WmlHyperlink extends OpenXmlElement {\r\n\tid?: string;\r\n    href?: string;\r\n}\r\n\r\nexport interface WmlNoteReference extends OpenXmlElement {\r\n    id: string;\r\n}\r\n\r\nexport interface WmlBreak extends OpenXmlElement{\r\n    break: \"page\" | \"lastRenderedPageBreak\" | \"textWrapping\";\r\n}\r\n\r\nexport interface WmlText extends OpenXmlElement{\r\n    text: string;\r\n}\r\n\r\nexport interface WmlSymbol extends OpenXmlElement {\r\n    font: string;\r\n    char: string;\r\n}\r\n\r\nexport interface WmlTable extends OpenXmlElement {\r\n    columns?: WmlTableColumn[];\r\n    cellStyle?: Record<string, string>;\r\n\r\n\tcolBandSize?: number;\r\n\trowBandSize?: number;\r\n}\r\n\r\nexport interface WmlTableRow extends OpenXmlElement {\r\n\tisHeader?: boolean;\r\n}\r\n\r\nexport interface WmlTableCell extends OpenXmlElement {\r\n\tverticalMerge?: 'restart' | 'continue' | string;\r\n    span?: number;\r\n}\r\n\r\nexport interface IDomImage extends OpenXmlElement {\r\n    src: string;\r\n}\r\n\r\nexport interface WmlTableColumn {\r\n    width?: string;\r\n}\r\n\r\nexport interface IDomNumbering {\r\n    id: string;\r\n    level: number;\r\n    start: number;\r\n    pStyleName: string;\r\n    pStyle: Record<string, string>;\r\n    rStyle: Record<string, string>;\r\n    levelText?: string;\r\n    suff: string;\r\n    format?: string;\r\n    bullet?: NumberingPicBullet;\r\n}\r\n\r\nexport interface NumberingPicBullet {\r\n    id: number;\r\n    src: string;\r\n    style?: string;\r\n}\r\n", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { Length } from \"./common\";\r\n\r\nexport interface LineSpacing {\r\n    after: Length;\r\n    before: Length;\r\n    line: number;\r\n    lineRule: \"atLeast\" | \"exactly\" | \"auto\";\r\n}\r\n\r\nexport function parseLineSpacing(elem: Element, xml: XmlParser): LineSpacing {\r\n    return {\r\n        before: xml.lengthAttr(elem, \"before\"),\r\n        after: xml.lengthAttr(elem, \"after\"),\r\n        line: xml.intAttr(elem, \"line\"),\r\n        lineRule: xml.attr(elem, \"lineRule\")\r\n    } as LineSpacing;\r\n}", "import { OpenXmlElement } from \"./dom\";\r\nimport { CommonProperties, Length, ns, parseCommonProperty } from \"./common\";\r\nimport { Borders } from \"./border\";\r\nimport { parseSectionProperties, SectionProperties } from \"./section\";\r\nimport { LineSpacing, parseLineSpacing } from \"./line-spacing\";\r\nimport { XmlParser } from \"../parser/xml-parser\";\r\nimport { parseRunProperties, RunProperties } from \"./run\";\r\n\r\nexport interface WmlParagraph extends OpenXmlElement, ParagraphProperties {\r\n}\r\n\r\nexport interface ParagraphProperties extends CommonProperties {\r\n    sectionProps: SectionProperties;\r\n    tabs: ParagraphTab[];\r\n    numbering: ParagraphNumbering;\r\n\r\n    border: Borders;\r\n    textAlignment: \"auto\" | \"baseline\" | \"bottom\" | \"center\" | \"top\" | string;\r\n    lineSpacing: LineSpacing;\r\n    keepLines: boolean;\r\n    keepNext: boolean;\r\n    pageBreakBefore: boolean;\r\n    outlineLevel: number;\r\n\tstyleName?: string;\r\n\r\n    runProps: RunProperties;\r\n}\r\n\r\nexport interface ParagraphTab {\r\n    style: \"bar\" | \"center\" | \"clear\" | \"decimal\" | \"end\" | \"num\" | \"start\" | \"left\" | \"right\";\r\n    leader: \"none\" | \"dot\" | \"heavy\" | \"hyphen\" | \"middleDot\" | \"underscore\";\r\n    position: Length;\r\n}\r\n\r\nexport interface ParagraphNumbering {\r\n    id: string;\r\n    level: number;\r\n}\r\n\r\nexport function parseParagraphProperties(elem: Element, xml: XmlParser): ParagraphProperties {\r\n    let result = <ParagraphProperties>{};\r\n\r\n    for(let el of xml.elements(elem)) {\r\n        parseParagraphProperty(el, result, xml);\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseParagraphProperty(elem: Element, props: ParagraphProperties, xml: XmlParser) {\r\n    if (elem.namespaceURI != ns.wordml)\r\n        return false;\r\n\r\n    if(parseCommonProperty(elem, props, xml))\r\n        return true;\r\n\r\n    switch (elem.localName) {\r\n        case \"tabs\":\r\n            props.tabs = parseTabs(elem, xml);\r\n            break;\r\n\r\n        case \"sectPr\":\r\n            props.sectionProps = parseSectionProperties(elem, xml);\r\n            break;\r\n\r\n        case \"numPr\":\r\n            props.numbering = parseNumbering(elem, xml);\r\n            break;\r\n        \r\n        case \"spacing\":\r\n            props.lineSpacing = parseLineSpacing(elem, xml);\r\n            return false; // TODO\r\n            break;\r\n\r\n        case \"textAlignment\":\r\n            props.textAlignment = xml.attr(elem, \"val\");\r\n            return false; //TODO\r\n            break;\r\n\r\n        case \"keepLines\":\r\n            props.keepLines = xml.boolAttr(elem, \"val\", true);\r\n            break;\r\n    \r\n        case \"keepNext\":\r\n            props.keepNext = xml.boolAttr(elem, \"val\", true);\r\n            break;\r\n        \r\n        case \"pageBreakBefore\":\r\n            props.pageBreakBefore = xml.boolAttr(elem, \"val\", true);\r\n            break;\r\n        \r\n        case \"outlineLvl\":\r\n            props.outlineLevel = xml.intAttr(elem, \"val\");\r\n            break;\r\n\r\n        case \"pStyle\":\r\n            props.styleName = xml.attr(elem, \"val\");\r\n            break;\r\n\r\n        case \"rPr\":\r\n            props.runProps = parseRunProperties(elem, xml);\r\n            break;\r\n        \r\n        default:\r\n            return false;\r\n    }\r\n\r\n    return true;\r\n}\r\n\r\nexport function parseTabs(elem: Element, xml: XmlParser): ParagraphTab[] {\r\n    return xml.elements(elem, \"tab\")\r\n        .map(e => <ParagraphTab>{\r\n            position: xml.lengthAttr(e, \"pos\"),\r\n            leader: xml.attr(e, \"leader\"),\r\n            style: xml.attr(e, \"val\")\r\n        });\r\n}\r\n\r\nexport function parseNumbering(elem: Element, xml: XmlParser): ParagraphNumbering {\r\n    var result = <ParagraphNumbering>{};\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"numId\":\r\n                result.id = xml.attr(e, \"val\");\r\n                break;\r\n\r\n            case \"ilvl\":\r\n                result.level = xml.intAttr(e, \"val\");\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\nimport { CommonProperties, parseCommonProperty } from \"./common\";\r\nimport { OpenXmlElement } from \"./dom\";\r\n\r\nexport interface WmlRun extends OpenXmlElement, RunProperties {\r\n    id?: string;\r\n    verticalAlign?: string;\r\n\tfieldRun?: boolean;  \r\n}\r\n\r\nexport interface RunProperties extends CommonProperties {\r\n\r\n}\r\n\r\nexport function parseRunProperties(elem: Element, xml: XmlParser): RunProperties {\r\n    let result = <RunProperties>{};\r\n\r\n    for(let el of xml.elements(elem)) {\r\n        parseRunProperty(el, result, xml);\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseRunProperty(elem: Element, props: RunProperties, xml: XmlParser) {\r\n    if (parseCommonProperty(elem, props, xml))\r\n        return true;\r\n\r\n    return false;\r\n}", "import globalXmlParser, { XmlParser } from \"../parser/xml-parser\";\r\nimport { Borders, parseBorders } from \"./border\";\r\nimport { Length } from \"./common\";\r\n\r\nexport interface Column {\r\n    space: Length;\r\n    width: Length;\r\n}\r\n\r\nexport interface Columns {\r\n    space: Length;\r\n    numberOfColumns: number;\r\n    separator: boolean;\r\n    equalWidth: boolean;\r\n    columns: Column[];\r\n}\r\n\r\nexport interface PageSize {\r\n    width: Length, \r\n    height: Length, \r\n    orientation: \"landscape\" | string \r\n}\r\n\r\nexport interface PageNumber {\r\n    start: number;\r\n    chapSep: \"colon\" | \"emDash\" | \"endash\" | \"hyphen\" | \"period\" | string;\r\n    chapStyle: string;\r\n    format: \"none\" | \"cardinalText\" | \"decimal\" | \"decimalEnclosedCircle\" | \"decimalEnclosedFullstop\" \r\n        | \"decimalEnclosedParen\" | \"decimalZero\" | \"lowerLetter\" | \"lowerRoman\"\r\n        | \"ordinalText\" | \"upperLetter\" | \"upperRoman\" | string;\r\n}\r\n\r\nexport interface PageMargins {\r\n    top: Length;\r\n    right: Length;\r\n    bottom: Length;\r\n    left: Length;\r\n    header: Length;\r\n    footer: Length;\r\n    gutter: Length;\r\n}\r\n\r\nexport enum SectionType {\r\n    Continuous = \"continuous\",\r\n    NextPage = \"nextPage\", \r\n    NextColumn = \"nextColumn\",\r\n    EvenPage = \"evenPage\",\r\n    OddPage = \"oddPage\",\r\n}\r\n\r\nexport interface FooterHeaderReference {\r\n    id: string;\r\n    type: string | \"first\" | \"even\" | \"default\";\r\n}\r\n\r\nexport interface SectionProperties {\r\n    type: SectionType | string;\r\n    pageSize: PageSize,\r\n    pageMargins: PageMargins,\r\n    pageBorders: Borders;\r\n    pageNumber: PageNumber;\r\n    columns: Columns;\r\n    footerRefs: FooterHeaderReference[];\r\n    headerRefs: FooterHeaderReference[];\r\n    titlePage: boolean;\r\n}\r\n\r\nexport function parseSectionProperties(elem: Element, xml: XmlParser = globalXmlParser): SectionProperties {\r\n    var section = <SectionProperties>{};\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"pgSz\":\r\n                section.pageSize = {\r\n                    width: xml.lengthAttr(e, \"w\"),\r\n                    height: xml.lengthAttr(e, \"h\"),\r\n                    orientation: xml.attr(e, \"orient\")\r\n                }\r\n                break;\r\n\r\n            case \"type\":\r\n                section.type = xml.attr(e, \"val\");\r\n                break;\r\n\r\n            case \"pgMar\":\r\n                section.pageMargins = {\r\n                    left: xml.lengthAttr(e, \"left\"),\r\n                    right: xml.lengthAttr(e, \"right\"),\r\n                    top: xml.lengthAttr(e, \"top\"),\r\n                    bottom: xml.lengthAttr(e, \"bottom\"),\r\n                    header: xml.lengthAttr(e, \"header\"),\r\n                    footer: xml.lengthAttr(e, \"footer\"),\r\n                    gutter: xml.lengthAttr(e, \"gutter\"),\r\n                };\r\n                break;\r\n\r\n            case \"cols\":\r\n                section.columns = parseColumns(e, xml);\r\n                break;\r\n\r\n            case \"headerReference\":\r\n                (section.headerRefs ?? (section.headerRefs = [])).push(parseFooterHeaderReference(e, xml)); \r\n                break;\r\n            \r\n            case \"footerReference\":\r\n                (section.footerRefs ?? (section.footerRefs = [])).push(parseFooterHeaderReference(e, xml)); \r\n                break;\r\n\r\n            case \"titlePg\":\r\n                section.titlePage = xml.boolAttr(e, \"val\", true);\r\n                break;\r\n\r\n            case \"pgBorders\":\r\n                section.pageBorders = parseBorders(e, xml);\r\n                break;\r\n\r\n            case \"pgNumType\":\r\n                section.pageNumber = parsePageNumber(e, xml);\r\n                break;\r\n        }\r\n    }\r\n\r\n    return section;\r\n}\r\n\r\nfunction parseColumns(elem: Element, xml: XmlParser): Columns {\r\n    return {\r\n        numberOfColumns: xml.intAttr(elem, \"num\"),\r\n        space: xml.lengthAttr(elem, \"space\"),\r\n        separator: xml.boolAttr(elem, \"sep\"),\r\n        equalWidth: xml.boolAttr(elem, \"equalWidth\", true),\r\n        columns: xml.elements(elem, \"col\")\r\n            .map(e => <Column>{\r\n                width: xml.lengthAttr(e, \"w\"),\r\n                space: xml.lengthAttr(e, \"space\")\r\n            })\r\n    };\r\n}\r\n\r\nfunction parsePageNumber(elem: Element, xml: XmlParser): PageNumber {\r\n    return {\r\n        chapSep: xml.attr(elem, \"chapSep\"),\r\n        chapStyle: xml.attr(elem, \"chapStyle\"),\r\n        format: xml.attr(elem, \"fmt\"),\r\n        start: xml.intAttr(elem, \"start\")\r\n    };\r\n}\r\n\r\nfunction parseFooterHeaderReference(elem: Element, xml: XmlParser): FooterHeaderReference {\r\n    return {\r\n        id: xml.attr(elem, \"id\"),\r\n        type: xml.attr(elem, \"type\"),\r\n    }\r\n}", "import { WordDocument } from './word-document';\r\nimport { DocumentParser } from './document-parser';\r\nimport { HtmlRenderer } from './html-renderer';\r\n\r\nexport interface Options {\r\n    inWrapper: boolean;\r\n    ignoreWidth: boolean;\r\n    ignoreHeight: boolean;\r\n    ignoreFonts: boolean;\r\n    breakPages: boolean;\r\n    debug: boolean;\r\n    experimental: boolean;\r\n    className: string;\r\n    trimXmlDeclaration: boolean;\r\n    renderHeaders: boolean;\r\n    renderFooters: boolean;\r\n    renderFootnotes: boolean;\r\n\trenderEndnotes: boolean;\r\n    ignoreLastRenderedPageBreak: boolean;\r\n\tuseBase64URL: boolean;\r\n\trenderChanges: boolean;\r\n}\r\n\r\nexport const defaultOptions: Options = {\r\n    ignoreHeight: false,\r\n    ignoreWidth: false,\r\n    ignoreFonts: false,\r\n    breakPages: true,\r\n    debug: false,\r\n    experimental: false,\r\n    className: \"docx\",\r\n    inWrapper: true,\r\n    trimXmlDeclaration: true,\r\n    ignoreLastRenderedPageBreak: true,\r\n    renderHeaders: true,\r\n    renderFooters: true,\r\n    renderFootnotes: true,\r\n\trenderEndnotes: true,\r\n\tuseBase64URL: false,\r\n\trenderChanges: false\r\n}\r\n\r\nexport function praseAsync(data: Blob | any, userOptions?: Partial<Options>): Promise<any>  {\r\n    const ops = { ...defaultOptions, ...userOptions };\r\n    return WordDocument.load(data, new DocumentParser(ops), ops);\r\n}\r\n\r\nexport function renderDocument(document: any, bodyContainer: HTMLElement, styleContainer?: HTMLElement, userOptions?: Partial<Options>) {\r\n    const ops = { ...defaultOptions, ...userOptions };\r\n    const renderer = new HtmlRenderer(window.document);\r\n\trenderer.render(document, bodyContainer, styleContainer, ops);\r\n}\r\n\r\nexport async function renderAsync(data: Blob | any, bodyContainer: HTMLElement, styleContainer?: HTMLElement, userOptions?: Partial<Options>): Promise<any> {\r\n\tconst doc = await praseAsync(data, userOptions);\r\n\trenderDocument(doc, bodyContainer, styleContainer, userOptions);\r\n    return doc;\r\n}", "import { Part } from \"../common/part\";\r\nimport { FontDeclaration, parseFonts } from \"./fonts\";\r\n\r\nexport class FontTablePart extends Part {\r\n    fonts: FontDeclaration[];\r\n\r\n    parseXml(root: Element) {\r\n        this.fonts = parseFonts(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nconst embedFontTypeMap = {\r\n    embedRegular: 'regular',\r\n    embedBold: 'bold',\r\n    embedItalic: 'italic',\r\n    embedBoldItalic: 'boldItalic',\r\n}\r\n\r\nexport interface FontDeclaration {\r\n    name: string,\r\n    altName: string,\r\n    family: string,\r\n    embedFontRefs: EmbedFontRef[];\r\n}\r\n\r\nexport interface EmbedFontRef {\r\n    id: string;\r\n    key: string;\r\n    type: 'regular' | 'bold' | 'italic' | 'boldItalic';\r\n}\r\n\r\nexport function parseFonts(root: Element, xml: XmlParser): FontDeclaration[] {\r\n    return xml.elements(root).map(el => parseFont(el, xml));\r\n}\r\n\r\nexport function parseFont(elem: Element, xml: XmlParser): FontDeclaration {\r\n    let result = <FontDeclaration>{\r\n        name: xml.attr(elem, \"name\"),\r\n        embedFontRefs: []\r\n    };\r\n\r\n    for (let el of xml.elements(elem)) {\r\n        switch (el.localName) {\r\n            case \"family\":\r\n                result.family = xml.attr(el, \"val\");\r\n                break;\r\n\r\n            case \"altName\":\r\n                result.altName = xml.attr(el, \"val\");\r\n                break;\r\n\r\n            case \"embedRegular\":\r\n            case \"embedBold\":\r\n            case \"embedItalic\":\r\n            case \"embedBoldItalic\":\r\n                result.embedFontRefs.push(parseEmbedFontRef(el, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseEmbedFontRef(elem: Element, xml: XmlParser): EmbedFontRef {\r\n    return { \r\n        id: xml.attr(elem, \"id\"), \r\n        key: xml.attr(elem, \"fontKey\"),\r\n        type: embedFontTypeMap[elem.localName]\r\n    };\r\n}", "import { OpenXmlElementBase, DomType } from \"../document/dom\";\r\n\r\nexport class WmlHeader extends OpenXmlElementBase {\r\n    type: DomType = DomType.Header;\r\n}\r\n\r\nexport class WmlFooter extends OpenXmlElementBase {\r\n    type: DomType = DomType.Footer;\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { OpenXmlElement } from \"../document/dom\";\r\nimport { WmlHeader, WmlFooter } from \"./elements\";\r\n\r\nexport abstract class BaseHeaderFooterPart<T extends OpenXmlElement = OpenXmlElement> extends Part {\r\n    rootElement: T;\r\n\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.rootElement = this.createRootElement();\r\n        this.rootElement.children = this._documentParser.parseBodyElements(root);\r\n    }\r\n\r\n    protected abstract createRootElement(): T;\r\n}\r\n\r\nexport class HeaderPart extends BaseHeaderFooterPart<WmlHeader> {\r\n    protected createRootElement(): WmlHeader {\r\n        return new WmlHeader();\r\n    }\r\n}\r\n\r\nexport class FooterPart extends BaseHeaderFooterPart<WmlFooter> {\r\n    protected createRootElement(): WmlFooter {\r\n        return new WmlFooter();\r\n    }\r\n}", "import { WordDocument } from './word-document';\r\nimport {\r\n\tDomType, WmlTable, IDomNumbering,\r\n\tWmlHyperlink, IDomImage, OpenXmlElement, WmlTableColumn, WmlTableCell, WmlText, WmlSymbol, WmlBreak, WmlNoteReference\r\n} from './document/dom';\r\nimport { CommonProperties } from './document/common';\r\nimport { Options } from './docx-preview';\r\nimport { DocumentElement } from './document/document';\r\nimport { WmlParagraph } from './document/paragraph';\r\nimport { asArray, escapeClassName, isString, keyBy, mergeDeep } from './utils';\r\nimport { computePixelToPoint, updateTabStop } from './javascript';\r\nimport { FontTablePart } from './font-table/font-table';\r\nimport { FooterHeaderReference, SectionProperties } from './document/section';\r\nimport { WmlRun, RunProperties } from './document/run';\r\nimport { WmlBookmarkStart } from './document/bookmarks';\r\nimport { IDomStyle } from './document/style';\r\nimport { WmlBaseNote, WmlFootnote } from './notes/elements';\r\nimport { ThemePart } from './theme/theme-part';\r\nimport { BaseHeaderFooterPart } from './header-footer/parts';\r\nimport { Part } from './common/part';\r\nimport { VmlElement } from './vml/vml';\r\n\r\nconst ns = {\r\n\tsvg: \"http://www.w3.org/2000/svg\",\r\n\tmathML: \"http://www.w3.org/1998/Math/MathML\"\r\n}\r\n\r\ninterface CellPos {\r\n\tcol: number;\r\n\trow: number;\r\n}\r\n\r\ntype CellVerticalMergeType = Record<number, HTMLTableCellElement>;\r\n\r\nexport class HtmlRenderer {\r\n\r\n\tclassName: string = \"docx\";\r\n\trootSelector: string;\r\n\tdocument: WordDocument;\r\n\toptions: Options;\r\n\tstyleMap: Record<string, IDomStyle> = {};\r\n\tcurrentPart: Part = null;\r\n\r\n\ttableVerticalMerges: CellVerticalMergeType[] = [];\r\n\tcurrentVerticalMerge: CellVerticalMergeType = null;\r\n\ttableCellPositions: CellPos[] = [];\r\n\tcurrentCellPosition: CellPos = null;\r\n\r\n\tfootnoteMap: Record<string, WmlFootnote> = {};\r\n\tendnoteMap: Record<string, WmlFootnote> = {};\r\n\tcurrentFootnoteIds: string[];\r\n\tcurrentEndnoteIds: string[] = [];\r\n\tusedHederFooterParts: any[] = [];\r\n\r\n\tdefaultTabSize: string;\r\n\tcurrentTabs: any[] = [];\r\n\ttabsTimeout: any = 0;\r\n\r\n\tconstructor(public htmlDocument: Document) {\r\n\t}\r\n\r\n\trender(document: WordDocument, bodyContainer: HTMLElement, styleContainer: HTMLElement = null, options: Options) {\r\n\t\tthis.document = document;\r\n\t\tthis.options = options;\r\n\t\tthis.className = options.className;\r\n\t\tthis.rootSelector = options.inWrapper ? `.${this.className}-wrapper` : ':root';\r\n\t\tthis.styleMap = null;\r\n\r\n\t\tstyleContainer = styleContainer || bodyContainer;\r\n\r\n\t\tremoveAllElements(styleContainer);\r\n\t\tremoveAllElements(bodyContainer);\r\n\r\n\t\tappendComment(styleContainer, \"docxjs library predefined styles\");\r\n\t\tstyleContainer.appendChild(this.renderDefaultStyle());\r\n\r\n\t\tif (document.themePart) {\r\n\t\t\tappendComment(styleContainer, \"docxjs document theme values\");\r\n\t\t\tthis.renderTheme(document.themePart, styleContainer);\r\n\t\t}\r\n\r\n\t\tif (document.stylesPart != null) {\r\n\t\t\tthis.styleMap = this.processStyles(document.stylesPart.styles);\r\n\r\n\t\t\tappendComment(styleContainer, \"docxjs document styles\");\r\n\t\t\tstyleContainer.appendChild(this.renderStyles(document.stylesPart.styles));\r\n\t\t}\r\n\r\n\t\tif (document.numberingPart) {\r\n\t\t\tthis.prodessNumberings(document.numberingPart.domNumberings);\r\n\r\n\t\t\tappendComment(styleContainer, \"docxjs document numbering styles\");\r\n\t\t\tstyleContainer.appendChild(this.renderNumbering(document.numberingPart.domNumberings, styleContainer));\r\n\t\t\t//styleContainer.appendChild(this.renderNumbering2(document.numberingPart, styleContainer));\r\n\t\t}\r\n\r\n\t\tif (document.footnotesPart) {\r\n\t\t\tthis.footnoteMap = keyBy(document.footnotesPart.notes, x => x.id);\r\n\t\t}\r\n\r\n\t\tif (document.endnotesPart) {\r\n\t\t\tthis.endnoteMap = keyBy(document.endnotesPart.notes, x => x.id);\r\n\t\t}\r\n\r\n\t\tif (document.settingsPart) {\r\n\t\t\tthis.defaultTabSize = document.settingsPart.settings?.defaultTabStop;\r\n\t\t}\r\n\r\n\t\tif (!options.ignoreFonts && document.fontTablePart)\r\n\t\t\tthis.renderFontTable(document.fontTablePart, styleContainer);\r\n\r\n\t\tvar sectionElements = this.renderSections(document.documentPart.body);\r\n\r\n\t\tif (this.options.inWrapper) {\r\n\t\t\tbodyContainer.appendChild(this.renderWrapper(sectionElements));\r\n\t\t} else {\r\n\t\t\tappendChildren(bodyContainer, sectionElements);\r\n\t\t}\r\n\r\n\t\tthis.refreshTabStops();\r\n\t}\r\n\r\n\trenderTheme(themePart: ThemePart, styleContainer: HTMLElement) {\r\n\t\tconst variables = {};\r\n\t\tconst fontScheme = themePart.theme?.fontScheme;\r\n\r\n\t\tif (fontScheme) {\r\n\t\t\tif (fontScheme.majorFont) {\r\n\t\t\t\tvariables['--docx-majorHAnsi-font'] = fontScheme.majorFont.latinTypeface;\r\n\t\t\t}\r\n\r\n\t\t\tif (fontScheme.minorFont) {\r\n\t\t\t\tvariables['--docx-minorHAnsi-font'] = fontScheme.minorFont.latinTypeface;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst colorScheme = themePart.theme?.colorScheme;\r\n\r\n\t\tif (colorScheme) {\r\n\t\t\tfor (let [k, v] of Object.entries(colorScheme.colors)) {\r\n\t\t\t\tvariables[`--docx-${k}-color`] = `#${v}`;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst cssText = this.styleToString(`.${this.className}`, variables);\r\n\t\tstyleContainer.appendChild(createStyleElement(cssText));\r\n\t}\r\n\r\n\trenderFontTable(fontsPart: FontTablePart, styleContainer: HTMLElement) {\r\n\t\tfor (let f of fontsPart.fonts) {\r\n\t\t\tfor (let ref of f.embedFontRefs) {\r\n\t\t\t\tthis.document.loadFont(ref.id, ref.key).then(fontData => {\r\n\t\t\t\t\tconst cssValues = {\r\n\t\t\t\t\t\t'font-family': f.name,\r\n\t\t\t\t\t\t'src': `url(${fontData})`\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tif (ref.type == \"bold\" || ref.type == \"boldItalic\") {\r\n\t\t\t\t\t\tcssValues['font-weight'] = 'bold';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (ref.type == \"italic\" || ref.type == \"boldItalic\") {\r\n\t\t\t\t\t\tcssValues['font-style'] = 'italic';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tappendComment(styleContainer, `docxjs ${f.name} font`);\r\n\t\t\t\t\tconst cssText = this.styleToString(\"@font-face\", cssValues);\r\n\t\t\t\t\tstyleContainer.appendChild(createStyleElement(cssText));\r\n\t\t\t\t\tthis.refreshTabStops();\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprocessStyleName(className: string): string {\r\n\t\treturn className ? `${this.className}_${escapeClassName(className)}` : this.className;\r\n\t}\r\n\r\n\tprocessStyles(styles: IDomStyle[]) {\r\n\t\tconst stylesMap = keyBy(styles.filter(x => x.id != null), x => x.id);\r\n\r\n\t\tfor (const style of styles.filter(x => x.basedOn)) {\r\n\t\t\tvar baseStyle = stylesMap[style.basedOn];\r\n\r\n\t\t\tif (baseStyle) {\r\n\t\t\t\tstyle.paragraphProps = mergeDeep(style.paragraphProps, baseStyle.paragraphProps);\r\n\t\t\t\tstyle.runProps = mergeDeep(style.runProps, baseStyle.runProps);\r\n\r\n\t\t\t\tfor (const baseValues of baseStyle.styles) {\r\n\t\t\t\t\tconst styleValues = style.styles.find(x => x.target == baseValues.target);\r\n\r\n\t\t\t\t\tif (styleValues) {\r\n\t\t\t\t\t\tthis.copyStyleProperties(baseValues.values, styleValues.values);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstyle.styles.push({ ...baseValues, values: { ...baseValues.values } });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse if (this.options.debug)\r\n\t\t\t\tconsole.warn(`Can't find base style ${style.basedOn}`);\r\n\t\t}\r\n\r\n\t\tfor (let style of styles) {\r\n\t\t\tstyle.cssName = this.processStyleName(style.id);\r\n\t\t}\r\n\r\n\t\treturn stylesMap;\r\n\t}\r\n\r\n\tprodessNumberings(numberings: IDomNumbering[]) {\r\n\t\tfor (let num of numberings.filter(n => n.pStyleName)) {\r\n\t\t\tconst style = this.findStyle(num.pStyleName);\r\n\r\n\t\t\tif (style?.paragraphProps?.numbering) {\r\n\t\t\t\tstyle.paragraphProps.numbering.level = num.level;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprocessElement(element: OpenXmlElement) {\r\n\t\tif (element.children) {\r\n\t\t\tfor (var e of element.children) {\r\n\t\t\t\te.parent = element;\r\n\r\n\t\t\t\tif (e.type == DomType.Table) {\r\n\t\t\t\t\tthis.processTable(e);\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tthis.processElement(e);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprocessTable(table: WmlTable) {\r\n\t\tfor (var r of table.children) {\r\n\t\t\tfor (var c of r.children) {\r\n\t\t\t\tc.cssStyle = this.copyStyleProperties(table.cellStyle, c.cssStyle, [\r\n\t\t\t\t\t\"border-left\", \"border-right\", \"border-top\", \"border-bottom\",\r\n\t\t\t\t\t\"padding-left\", \"padding-right\", \"padding-top\", \"padding-bottom\"\r\n\t\t\t\t]);\r\n\r\n\t\t\t\tthis.processElement(c);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tcopyStyleProperties(input: Record<string, string>, output: Record<string, string>, attrs: string[] = null): Record<string, string> {\r\n\t\tif (!input)\r\n\t\t\treturn output;\r\n\r\n\t\tif (output == null) output = {};\r\n\t\tif (attrs == null) attrs = Object.getOwnPropertyNames(input);\r\n\r\n\t\tfor (var key of attrs) {\r\n\t\t\tif (input.hasOwnProperty(key) && !output.hasOwnProperty(key))\r\n\t\t\t\toutput[key] = input[key];\r\n\t\t}\r\n\r\n\t\treturn output;\r\n\t}\r\n\r\n\tcreateSection(className: string, props: SectionProperties) {\r\n\t\tvar elem = this.createElement(\"section\", { className });\r\n\r\n\t\tif (props) {\r\n\t\t\tif (props.pageMargins) {\r\n\t\t\t\telem.style.paddingLeft = props.pageMargins.left;\r\n\t\t\t\telem.style.paddingRight = props.pageMargins.right;\r\n\t\t\t\telem.style.paddingTop = props.pageMargins.top;\r\n\t\t\t\telem.style.paddingBottom = props.pageMargins.bottom;\r\n\t\t\t}\r\n\r\n\t\t\tif (props.pageSize) {\r\n\t\t\t\tif (!this.options.ignoreWidth)\r\n\t\t\t\t\telem.style.width = props.pageSize.width;\r\n\t\t\t\tif (!this.options.ignoreHeight)\r\n\t\t\t\t\telem.style.minHeight = props.pageSize.height;\r\n\t\t\t}\r\n\r\n\t\t\tif (props.columns && props.columns.numberOfColumns) {\r\n\t\t\t\telem.style.columnCount = `${props.columns.numberOfColumns}`;\r\n\t\t\t\telem.style.columnGap = props.columns.space;\r\n\r\n\t\t\t\tif (props.columns.separator) {\r\n\t\t\t\t\telem.style.columnRule = \"1px solid black\";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn elem;\r\n\t}\r\n\r\n\trenderSections(document: DocumentElement): HTMLElement[] {\r\n\t\tconst result = [];\r\n\r\n\t\tthis.processElement(document);\r\n\t\tconst sections = this.splitBySection(document.children);\r\n\t\tlet prevProps = null;\r\n\r\n\t\tfor (let i = 0, l = sections.length; i < l; i++) {\r\n\t\t\tthis.currentFootnoteIds = [];\r\n\r\n\t\t\tconst section = sections[i];\r\n\t\t\tconst props = section.sectProps || document.props;\r\n\t\t\tconst sectionElement = this.createSection(this.className, props);\r\n\t\t\tthis.renderStyleValues(document.cssStyle, sectionElement);\r\n\r\n\t\t\tthis.options.renderHeaders && this.renderHeaderFooter(props.headerRefs, props,\r\n\t\t\t\tresult.length, prevProps != props, sectionElement);\r\n\r\n\t\t\tvar contentElement = this.createElement(\"article\");\r\n\t\t\tthis.renderElements(section.elements, contentElement);\r\n\t\t\tsectionElement.appendChild(contentElement);\r\n\r\n\t\t\tif (this.options.renderFootnotes) {\r\n\t\t\t\tthis.renderNotes(this.currentFootnoteIds, this.footnoteMap, sectionElement);\r\n\t\t\t}\r\n\r\n\t\t\tif (this.options.renderEndnotes && i == l - 1) {\r\n\t\t\t\tthis.renderNotes(this.currentEndnoteIds, this.endnoteMap, sectionElement);\r\n\t\t\t}\r\n\r\n\t\t\tthis.options.renderFooters && this.renderHeaderFooter(props.footerRefs, props,\r\n\t\t\t\tresult.length, prevProps != props, sectionElement);\r\n\r\n\t\t\tresult.push(sectionElement);\r\n\t\t\tprevProps = props;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderHeaderFooter(refs: FooterHeaderReference[], props: SectionProperties, page: number, firstOfSection: boolean, into: HTMLElement) {\r\n\t\tif (!refs) return;\r\n\r\n\t\tvar ref = (props.titlePage && firstOfSection ? refs.find(x => x.type == \"first\") : null)\r\n\t\t\t?? (page % 2 == 1 ? refs.find(x => x.type == \"even\") : null)\r\n\t\t\t?? refs.find(x => x.type == \"default\");\r\n\r\n\t\tvar part = ref && this.document.findPartByRelId(ref.id, this.document.documentPart) as BaseHeaderFooterPart;\r\n\r\n\t\tif (part) {\r\n\t\t\tthis.currentPart = part;\r\n\t\t\tif (!this.usedHederFooterParts.includes(part.path)) {\r\n\t\t\t\tthis.processElement(part.rootElement);\r\n\t\t\t\tthis.usedHederFooterParts.push(part.path);\r\n\t\t\t}\r\n\t\t\tconst [el] = this.renderElements([part.rootElement], into) as HTMLElement[];\r\n\r\n\t\t\tif (props?.pageMargins) {\r\n\t\t\t\tif (part.rootElement.type === DomType.Header) {\r\n\t\t\t\t\tel.style.marginTop = `calc(${props.pageMargins.header} - ${props.pageMargins.top})`;\r\n\t\t\t\t\tel.style.minHeight = `calc(${props.pageMargins.top} - ${props.pageMargins.header})`;\r\n\t\t\t\t}\r\n\t\t\t\telse if (part.rootElement.type === DomType.Footer) {\r\n\t\t\t\t\tel.style.marginBottom = `calc(${props.pageMargins.footer} - ${props.pageMargins.bottom})`;\r\n\t\t\t\t\tel.style.minHeight = `calc(${props.pageMargins.bottom} - ${props.pageMargins.footer})`;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis.currentPart = null;\r\n\t\t}\r\n\t}\r\n\r\n\tisPageBreakElement(elem: OpenXmlElement): boolean {\r\n\t\tif (elem.type != DomType.Break)\r\n\t\t\treturn false;\r\n\r\n\t\tif ((elem as WmlBreak).break == \"lastRenderedPageBreak\")\r\n\t\t\treturn !this.options.ignoreLastRenderedPageBreak;\r\n\r\n\t\treturn (elem as WmlBreak).break == \"page\";\r\n\t}\r\n\r\n\tsplitBySection(elements: OpenXmlElement[]): { sectProps: SectionProperties, elements: OpenXmlElement[] }[] {\r\n\t\tvar current = { sectProps: null, elements: [] };\r\n\t\tvar result = [current];\r\n\r\n\t\tfor (let elem of elements) {\r\n\t\t\tif (elem.type == DomType.Paragraph) {\r\n\t\t\t\tconst s = this.findStyle((elem as WmlParagraph).styleName);\r\n\r\n\t\t\t\tif (s?.paragraphProps?.pageBreakBefore) {\r\n\t\t\t\t\tcurrent.sectProps = sectProps;\r\n\t\t\t\t\tcurrent = { sectProps: null, elements: [] };\r\n\t\t\t\t\tresult.push(current);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tcurrent.elements.push(elem);\r\n\r\n\t\t\tif (elem.type == DomType.Paragraph) {\r\n\t\t\t\tconst p = elem as WmlParagraph;\r\n\r\n\t\t\t\tvar sectProps = p.sectionProps;\r\n\t\t\t\tvar pBreakIndex = -1;\r\n\t\t\t\tvar rBreakIndex = -1;\r\n\r\n\t\t\t\tif (this.options.breakPages && p.children) {\r\n\t\t\t\t\tpBreakIndex = p.children.findIndex(r => {\r\n\t\t\t\t\t\trBreakIndex = r.children?.findIndex(this.isPageBreakElement.bind(this)) ?? -1;\r\n\t\t\t\t\t\treturn rBreakIndex != -1;\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (sectProps || pBreakIndex != -1) {\r\n\t\t\t\t\tcurrent.sectProps = sectProps;\r\n\t\t\t\t\tcurrent = { sectProps: null, elements: [] };\r\n\t\t\t\t\tresult.push(current);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (pBreakIndex != -1) {\r\n\t\t\t\t\tlet breakRun = p.children[pBreakIndex];\r\n\t\t\t\t\tlet splitRun = rBreakIndex < breakRun.children.length - 1;\r\n\r\n\t\t\t\t\tif (pBreakIndex < p.children.length - 1 || splitRun) {\r\n\t\t\t\t\t\tvar children = elem.children;\r\n\t\t\t\t\t\tvar newParagraph = { ...elem, children: children.slice(pBreakIndex) };\r\n\t\t\t\t\t\telem.children = children.slice(0, pBreakIndex);\r\n\t\t\t\t\t\tcurrent.elements.push(newParagraph);\r\n\r\n\t\t\t\t\t\tif (splitRun) {\r\n\t\t\t\t\t\t\tlet runChildren = breakRun.children;\r\n\t\t\t\t\t\t\tlet newRun = { ...breakRun, children: runChildren.slice(0, rBreakIndex) };\r\n\t\t\t\t\t\t\telem.children.push(newRun);\r\n\t\t\t\t\t\t\tbreakRun.children = runChildren.slice(rBreakIndex);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet currentSectProps = null;\r\n\r\n\t\tfor (let i = result.length - 1; i >= 0; i--) {\r\n\t\t\tif (result[i].sectProps == null) {\r\n\t\t\t\tresult[i].sectProps = currentSectProps;\r\n\t\t\t} else {\r\n\t\t\t\tcurrentSectProps = result[i].sectProps\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderWrapper(children: HTMLElement[]) {\r\n\t\treturn this.createElement(\"div\", { className: `${this.className}-wrapper` }, children);\r\n\t}\r\n\r\n\trenderDefaultStyle() {\r\n\t\tvar c = this.className;\r\n\t\tvar styleText = `\r\n.${c}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } \r\n.${c}-wrapper>section.${c} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }\r\n.${c} { color: black; hyphens: auto; text-underline-position: from-font; }\r\nsection.${c} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }\r\nsection.${c}>article { margin-bottom: auto; z-index: 1; }\r\nsection.${c}>footer { z-index: 1; }\r\n.${c} table { border-collapse: collapse; }\r\n.${c} table td, .${c} table th { vertical-align: top; }\r\n.${c} p { margin: 0pt; min-height: 1em; }\r\n.${c} span { white-space: pre-wrap; overflow-wrap: break-word; }\r\n.${c} a { color: inherit; text-decoration: inherit; }\r\n`;\r\n\r\n\t\treturn createStyleElement(styleText);\r\n\t}\r\n\r\n\t// renderNumbering2(numberingPart: NumberingPartProperties, container: HTMLElement): HTMLElement {\r\n\t//     let css = \"\";\r\n\t//     const numberingMap = keyBy(numberingPart.abstractNumberings, x => x.id);\r\n\t//     const bulletMap = keyBy(numberingPart.bulletPictures, x => x.id);\r\n\t//     const topCounters = [];\r\n\r\n\t//     for(let num of numberingPart.numberings) {\r\n\t//         const absNum = numberingMap[num.abstractId];\r\n\r\n\t//         for(let lvl of absNum.levels) {\r\n\t//             const className = this.numberingClass(num.id, lvl.level);\r\n\t//             let listStyleType = \"none\";\r\n\r\n\t//             if(lvl.text && lvl.format == 'decimal') {\r\n\t//                 const counter = this.numberingCounter(num.id, lvl.level);\r\n\r\n\t//                 if (lvl.level > 0) {\r\n\t//                     css += this.styleToString(`p.${this.numberingClass(num.id, lvl.level - 1)}`, {\r\n\t//                         \"counter-reset\": counter\r\n\t//                     });\r\n\t//                 } else {\r\n\t//                     topCounters.push(counter);\r\n\t//                 }\r\n\r\n\t//                 css += this.styleToString(`p.${className}:before`, {\r\n\t//                     \"content\": this.levelTextToContent(lvl.text, num.id),\r\n\t//                     \"counter-increment\": counter\r\n\t//                 });\r\n\t//             } else if(lvl.bulletPictureId) {\r\n\t//                 let pict = bulletMap[lvl.bulletPictureId];\r\n\t//                 let variable = `--${this.className}-${pict.referenceId}`.toLowerCase();\r\n\r\n\t//                 css += this.styleToString(`p.${className}:before`, {\r\n\t//                     \"content\": \"' '\",\r\n\t//                     \"display\": \"inline-block\",\r\n\t//                     \"background\": `var(${variable})`\r\n\t//                 }, pict.style);\r\n\r\n\t//                 this.document.loadNumberingImage(pict.referenceId).then(data => {\r\n\t//                     var text = `.${this.className}-wrapper { ${variable}: url(${data}) }`;\r\n\t//                     container.appendChild(createStyleElement(text));\r\n\t//                 });\r\n\t//             } else {\r\n\t//                 listStyleType = this.numFormatToCssValue(lvl.format);\r\n\t//             }\r\n\r\n\t//             css += this.styleToString(`p.${className}`, {\r\n\t//                 \"display\": \"list-item\",\r\n\t//                 \"list-style-position\": \"inside\",\r\n\t//                 \"list-style-type\": listStyleType,\r\n\t//                 //TODO\r\n\t//                 //...num.style\r\n\t//             });\r\n\t//         }\r\n\t//     }\r\n\r\n\t//     if (topCounters.length > 0) {\r\n\t//         css += this.styleToString(`.${this.className}-wrapper`, {\r\n\t//             \"counter-reset\": topCounters.join(\" \")\r\n\t//         });\r\n\t//     }\r\n\r\n\t//     return createStyleElement(css);\r\n\t// }\r\n\r\n\trenderNumbering(numberings: IDomNumbering[], styleContainer: HTMLElement) {\r\n\t\tvar styleText = \"\";\r\n\t\tvar resetCounters = [];\r\n\r\n\t\tfor (var num of numberings) {\r\n\t\t\tvar selector = `p.${this.numberingClass(num.id, num.level)}`;\r\n\t\t\tvar listStyleType = \"none\";\r\n\r\n\t\t\tif (num.bullet) {\r\n\t\t\t\tlet valiable = `--${this.className}-${num.bullet.src}`.toLowerCase();\r\n\r\n\t\t\t\tstyleText += this.styleToString(`${selector}:before`, {\r\n\t\t\t\t\t\"content\": \"' '\",\r\n\t\t\t\t\t\"display\": \"inline-block\",\r\n\t\t\t\t\t\"background\": `var(${valiable})`\r\n\t\t\t\t}, num.bullet.style);\r\n\r\n\t\t\t\tthis.document.loadNumberingImage(num.bullet.src).then(data => {\r\n\t\t\t\t\tvar text = `${this.rootSelector} { ${valiable}: url(${data}) }`;\r\n\t\t\t\t\tstyleContainer.appendChild(createStyleElement(text));\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\telse if (num.levelText) {\r\n\t\t\t\tlet counter = this.numberingCounter(num.id, num.level);\r\n\t\t\t\tconst counterReset = counter + \" \" + (num.start - 1);\r\n\t\t\t\tif (num.level > 0) {\r\n\t\t\t\t\tstyleText += this.styleToString(`p.${this.numberingClass(num.id, num.level - 1)}`, {\r\n\t\t\t\t\t\t\"counter-reset\": counterReset\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// reset all level counters with start value\r\n\t\t\t\tresetCounters.push(counterReset);\r\n\r\n\t\t\t\tstyleText += this.styleToString(`${selector}:before`, {\r\n\t\t\t\t\t\"content\": this.levelTextToContent(num.levelText, num.suff, num.id, this.numFormatToCssValue(num.format)),\r\n\t\t\t\t\t\"counter-increment\": counter,\r\n\t\t\t\t\t...num.rStyle,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tlistStyleType = this.numFormatToCssValue(num.format);\r\n\t\t\t}\r\n\r\n\t\t\tstyleText += this.styleToString(selector, {\r\n\t\t\t\t\"display\": \"list-item\",\r\n\t\t\t\t\"list-style-position\": \"inside\",\r\n\t\t\t\t\"list-style-type\": listStyleType,\r\n\t\t\t\t...num.pStyle\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\tif (resetCounters.length > 0) {\r\n\t\t\tstyleText += this.styleToString(this.rootSelector, {\r\n\t\t\t\t\"counter-reset\": resetCounters.join(\" \")\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn createStyleElement(styleText);\r\n\t}\r\n\r\n\trenderStyles(styles: IDomStyle[]): HTMLElement {\r\n\t\tvar styleText = \"\";\r\n\t\tconst stylesMap = this.styleMap;\r\n\t\tconst defautStyles = keyBy(styles.filter(s => s.isDefault), s => s.target);\r\n\r\n\t\tfor (const style of styles) {\r\n\t\t\tvar subStyles = style.styles;\r\n\r\n\t\t\tif (style.linked) {\r\n\t\t\t\tvar linkedStyle = style.linked && stylesMap[style.linked];\r\n\r\n\t\t\t\tif (linkedStyle)\r\n\t\t\t\t\tsubStyles = subStyles.concat(linkedStyle.styles);\r\n\t\t\t\telse if (this.options.debug)\r\n\t\t\t\t\tconsole.warn(`Can't find linked style ${style.linked}`);\r\n\t\t\t}\r\n\r\n\t\t\tfor (const subStyle of subStyles) {\r\n\t\t\t\t//TODO temporary disable modificators until test it well\r\n\t\t\t\tvar selector = `${style.target ?? ''}.${style.cssName}`; //${subStyle.mod ?? ''} \r\n\r\n\t\t\t\tif (style.target != subStyle.target)\r\n\t\t\t\t\tselector += ` ${subStyle.target}`;\r\n\r\n\t\t\t\tif (defautStyles[style.target] == style)\r\n\t\t\t\t\tselector = `.${this.className} ${style.target}, ` + selector;\r\n\r\n\t\t\t\tstyleText += this.styleToString(selector, subStyle.values);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn createStyleElement(styleText);\r\n\t}\r\n\r\n\trenderNotes(noteIds: string[], notesMap: Record<string, WmlBaseNote>, into: HTMLElement) {\r\n\t\tvar notes = noteIds.map(id => notesMap[id]).filter(x => x);\r\n\r\n\t\tif (notes.length > 0) {\r\n\t\t\tvar result = this.createElement(\"ol\", null, this.renderElements(notes));\r\n\t\t\tinto.appendChild(result);\r\n\t\t}\r\n\t}\r\n\r\n\trenderElement(elem: OpenXmlElement): Node | Node[] {\r\n\t\tswitch (elem.type) {\r\n\t\t\tcase DomType.Paragraph:\r\n\t\t\t\treturn this.renderParagraph(elem as WmlParagraph);\r\n\r\n\t\t\tcase DomType.BookmarkStart:\r\n\t\t\t\treturn this.renderBookmarkStart(elem as WmlBookmarkStart);\r\n\r\n\t\t\tcase DomType.BookmarkEnd:\r\n\t\t\t\treturn null; //ignore bookmark end\r\n\r\n\t\t\tcase DomType.Run:\r\n\t\t\t\treturn this.renderRun(elem as WmlRun);\r\n\r\n\t\t\tcase DomType.Table:\r\n\t\t\t\treturn this.renderTable(elem);\r\n\r\n\t\t\tcase DomType.Row:\r\n\t\t\t\treturn this.renderTableRow(elem);\r\n\r\n\t\t\tcase DomType.Cell:\r\n\t\t\t\treturn this.renderTableCell(elem);\r\n\r\n\t\t\tcase DomType.Hyperlink:\r\n\t\t\t\treturn this.renderHyperlink(elem);\r\n\r\n\t\t\tcase DomType.Drawing:\r\n\t\t\t\treturn this.renderDrawing(elem);\r\n\r\n\t\t\tcase DomType.Image:\r\n\t\t\t\treturn this.renderImage(elem as IDomImage);\r\n\r\n\t\t\tcase DomType.Text:\r\n\t\t\t\treturn this.renderText(elem as WmlText);\r\n\r\n\t\t\tcase DomType.Text:\r\n\t\t\t\treturn this.renderText(elem as WmlText);\r\n\r\n\t\t\tcase DomType.DeletedText:\r\n\t\t\t\treturn this.renderDeletedText(elem as WmlText);\r\n\t\r\n\t\t\tcase DomType.Tab:\r\n\t\t\t\treturn this.renderTab(elem);\r\n\r\n\t\t\tcase DomType.Symbol:\r\n\t\t\t\treturn this.renderSymbol(elem as WmlSymbol);\r\n\r\n\t\t\tcase DomType.Break:\r\n\t\t\t\treturn this.renderBreak(elem as WmlBreak);\r\n\r\n\t\t\tcase DomType.Footer:\r\n\t\t\t\treturn this.renderContainer(elem, \"footer\");\r\n\r\n\t\t\tcase DomType.Header:\r\n\t\t\t\treturn this.renderContainer(elem, \"header\");\r\n\r\n\t\t\tcase DomType.Footnote:\r\n\t\t\tcase DomType.Endnote:\r\n\t\t\t\treturn this.renderContainer(elem, \"li\");\r\n\r\n\t\t\tcase DomType.FootnoteReference:\r\n\t\t\t\treturn this.renderFootnoteReference(elem as WmlNoteReference);\r\n\r\n\t\t\tcase DomType.EndnoteReference:\r\n\t\t\t\treturn this.renderEndnoteReference(elem as WmlNoteReference);\r\n\r\n\t\t\tcase DomType.NoBreakHyphen:\r\n\t\t\t\treturn this.createElement(\"wbr\");\r\n\r\n\t\t\tcase DomType.VmlPicture:\r\n\t\t\t\treturn this.renderVmlPicture(elem);\r\n\r\n\t\t\tcase DomType.VmlElement:\r\n\t\t\t\treturn this.renderVmlElement(elem as VmlElement);\r\n\t\r\n\t\t\tcase DomType.MmlMath:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"math\", { xmlns: ns.mathML });\r\n\t\r\n\t\t\tcase DomType.MmlMathParagraph:\r\n\t\t\t\treturn this.renderContainer(elem, \"span\");\r\n\r\n\t\t\tcase DomType.MmlFraction:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mfrac\");\r\n\r\n\t\t\tcase DomType.MmlBase:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \r\n\t\t\t\t\telem.parent.type == DomType.MmlMatrixRow ? \"mtd\" : \"mrow\");\r\n\r\n\t\t\tcase DomType.MmlNumerator:\r\n\t\t\tcase DomType.MmlDenominator:\r\n\t\t\tcase DomType.MmlFunction:\r\n\t\t\tcase DomType.MmlLimit:\r\n\t\t\tcase DomType.MmlBox:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mrow\");\r\n\r\n\t\t\tcase DomType.MmlGroupChar:\r\n\t\t\t\treturn this.renderMmlGroupChar(elem);\r\n\r\n\t\t\tcase DomType.MmlLimitLower:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"munder\");\r\n\r\n\t\t\tcase DomType.MmlMatrix:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mtable\");\r\n\r\n\t\t\tcase DomType.MmlMatrixRow:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mtr\");\r\n\t\r\n\t\t\tcase DomType.MmlRadical:\r\n\t\t\t\treturn this.renderMmlRadical(elem);\r\n\r\n\t\t\tcase DomType.MmlSuperscript:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"msup\");\r\n\r\n\t\t\tcase DomType.MmlSubscript:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"msub\");\r\n\r\n\t\t\tcase DomType.MmlDegree:\r\n\t\t\tcase DomType.MmlSuperArgument:\r\n\t\t\tcase DomType.MmlSubArgument:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"mn\");\r\n\r\n\t\t\tcase DomType.MmlFunctionName:\r\n\t\t\t\treturn this.renderContainerNS(elem, ns.mathML, \"ms\");\r\n\t\r\n\t\t\tcase DomType.MmlDelimiter:\r\n\t\t\t\treturn this.renderMmlDelimiter(elem);\r\n\r\n\t\t\tcase DomType.MmlRun:\r\n\t\t\t\treturn this.renderMmlRun(elem);\r\n\r\n\t\t\tcase DomType.MmlNary:\r\n\t\t\t\treturn this.renderMmlNary(elem);\r\n\r\n\t\t\tcase DomType.MmlPreSubSuper:\r\n\t\t\t\treturn this.renderMmlPreSubSuper(elem);\r\n\r\n\t\t\tcase DomType.MmlBar:\r\n\t\t\t\treturn this.renderMmlBar(elem);\r\n\t\r\n\t\t\tcase DomType.MmlEquationArray:\r\n\t\t\t\treturn this.renderMllList(elem);\r\n\r\n\t\t\tcase DomType.Inserted:\r\n\t\t\t\treturn this.renderInserted(elem);\r\n\r\n\t\t\tcase DomType.Deleted:\r\n\t\t\t\treturn this.renderDeleted(elem);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\trenderChildren(elem: OpenXmlElement, into?: Element): Node[] {\r\n\t\treturn this.renderElements(elem.children, into);\r\n\t}\r\n\r\n\trenderElements(elems: OpenXmlElement[], into?: Element): Node[] {\r\n\t\tif (elems == null)\r\n\t\t\treturn null;\r\n\r\n\t\tvar result = elems.flatMap(e => this.renderElement(e)).filter(e => e != null);\r\n\r\n\t\tif (into)\r\n\t\t\tappendChildren(into, result);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderContainer(elem: OpenXmlElement, tagName: keyof HTMLElementTagNameMap, props?: Record<string, any>) {\r\n\t\treturn this.createElement(tagName, props, this.renderChildren(elem));\r\n\t}\r\n\r\n\trenderContainerNS(elem: OpenXmlElement, ns: string, tagName: string, props?: Record<string, any>) {\r\n\t\treturn createElementNS(ns, tagName, props, this.renderChildren(elem));\r\n\t}\r\n\r\n\trenderParagraph(elem: WmlParagraph) {\r\n\t\tvar result = this.createElement(\"p\");\r\n\r\n\t\tconst style = this.findStyle(elem.styleName);\r\n\t\telem.tabs ??= style?.paragraphProps?.tabs;  //TODO\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\t\tthis.renderCommonProperties(result.style, elem);\r\n\r\n\t\tconst numbering = elem.numbering ?? style?.paragraphProps?.numbering;\r\n\r\n\t\tif (numbering) {\r\n\t\t\tresult.classList.add(this.numberingClass(numbering.id, numbering.level));\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderRunProperties(style: any, props: RunProperties) {\r\n\t\tthis.renderCommonProperties(style, props);\r\n\t}\r\n\r\n\trenderCommonProperties(style: any, props: CommonProperties) {\r\n\t\tif (props == null)\r\n\t\t\treturn;\r\n\r\n\t\tif (props.color) {\r\n\t\t\tstyle[\"color\"] = props.color;\r\n\t\t}\r\n\r\n\t\tif (props.fontSize) {\r\n\t\t\tstyle[\"font-size\"] = props.fontSize;\r\n\t\t}\r\n\t}\r\n\r\n\trenderHyperlink(elem: WmlHyperlink) {\r\n\t\tvar result = this.createElement(\"a\");\r\n\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (elem.href) {\r\n\t\t\tresult.href = elem.href;\r\n\t\t} else if(elem.id) {\r\n\t\t\tconst rel = this.document.documentPart.rels\r\n\t\t\t\t.find(it => it.id == elem.id && it.targetMode === \"External\");\r\n\t\t\tresult.href = rel?.target;\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderDrawing(elem: OpenXmlElement) {\r\n\t\tvar result = this.createElement(\"div\");\r\n\r\n\t\tresult.style.display = \"inline-block\";\r\n\t\tresult.style.position = \"relative\";\r\n\t\tresult.style.textIndent = \"0px\";\r\n\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderImage(elem: IDomImage) {\r\n\t\tlet result = this.createElement(\"img\");\r\n\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (this.document) {\r\n\t\t\tthis.document.loadDocumentImage(elem.src, this.currentPart).then(x => {\r\n\t\t\t\tresult.src = x;\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderText(elem: WmlText) {\r\n\t\treturn this.htmlDocument.createTextNode(elem.text);\r\n\t}\r\n\r\n\trenderDeletedText(elem: WmlText) {\r\n\t\treturn this.options.renderEndnotes ? this.htmlDocument.createTextNode(elem.text) : null;\r\n\t}\r\n\r\n\trenderBreak(elem: WmlBreak) {\r\n\t\tif (elem.break == \"textWrapping\") {\r\n\t\t\treturn this.createElement(\"br\");\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\trenderInserted(elem: OpenXmlElement): Node | Node[] {\r\n\t\tif (this.options.renderChanges)\r\n\t\t\treturn this.renderContainer(elem, \"ins\");\r\n\r\n\t\treturn this.renderChildren(elem);\r\n\t}\r\n\r\n\trenderDeleted(elem: OpenXmlElement): Node {\r\n\t\tif (this.options.renderChanges)\r\n\t\t\treturn this.renderContainer(elem, \"del\");\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\trenderSymbol(elem: WmlSymbol) {\r\n\t\tvar span = this.createElement(\"span\");\r\n\t\tspan.style.fontFamily = elem.font;\r\n\t\tspan.innerHTML = `&#x${elem.char};`\r\n\t\treturn span;\r\n\t}\r\n\r\n\trenderFootnoteReference(elem: WmlNoteReference) {\r\n\t\tvar result = this.createElement(\"sup\");\r\n\t\tthis.currentFootnoteIds.push(elem.id);\r\n\t\tresult.textContent = `${this.currentFootnoteIds.length}`;\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderEndnoteReference(elem: WmlNoteReference) {\r\n\t\tvar result = this.createElement(\"sup\");\r\n\t\tthis.currentEndnoteIds.push(elem.id);\r\n\t\tresult.textContent = `${this.currentEndnoteIds.length}`;\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTab(elem: OpenXmlElement) {\r\n\t\tvar tabSpan = this.createElement(\"span\");\r\n\r\n\t\ttabSpan.innerHTML = \"&emsp;\";//\"&nbsp;\";\r\n\r\n\t\tif (this.options.experimental) {\r\n\t\t\ttabSpan.className = this.tabStopClass();\r\n\t\t\tvar stops = findParent<WmlParagraph>(elem, DomType.Paragraph)?.tabs;\r\n\t\t\tthis.currentTabs.push({ stops, span: tabSpan });\r\n\t\t}\r\n\r\n\t\treturn tabSpan;\r\n\t}\r\n\r\n\trenderBookmarkStart(elem: WmlBookmarkStart): HTMLElement {\r\n\t\tvar result = this.createElement(\"span\");\r\n\t\tresult.id = elem.name;\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderRun(elem: WmlRun) {\r\n\t\tif (elem.fieldRun)\r\n\t\t\treturn null;\r\n\r\n\t\tconst result = this.createElement(\"span\");\r\n\r\n\t\tif (elem.id)\r\n\t\t\tresult.id = elem.id;\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (elem.verticalAlign) {\r\n\t\t\tconst wrapper = this.createElement(elem.verticalAlign as any);\r\n\t\t\tthis.renderChildren(elem, wrapper);\r\n\t\t\tresult.appendChild(wrapper);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tthis.renderChildren(elem, result);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTable(elem: WmlTable) {\r\n\t\tlet result = this.createElement(\"table\");\r\n\r\n\t\tthis.tableCellPositions.push(this.currentCellPosition);\r\n\t\tthis.tableVerticalMerges.push(this.currentVerticalMerge);\r\n\t\tthis.currentVerticalMerge = {};\r\n\t\tthis.currentCellPosition = { col: 0, row: 0 };\r\n\r\n\t\tif (elem.columns)\r\n\t\t\tresult.appendChild(this.renderTableColumns(elem.columns));\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tthis.currentVerticalMerge = this.tableVerticalMerges.pop();\r\n\t\tthis.currentCellPosition = this.tableCellPositions.pop();\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTableColumns(columns: WmlTableColumn[]) {\r\n\t\tlet result = this.createElement(\"colgroup\");\r\n\r\n\t\tfor (let col of columns) {\r\n\t\t\tlet colElem = this.createElement(\"col\");\r\n\r\n\t\t\tif (col.width)\r\n\t\t\t\tcolElem.style.width = col.width;\r\n\r\n\t\t\tresult.appendChild(colElem);\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTableRow(elem: OpenXmlElement) {\r\n\t\tlet result = this.createElement(\"tr\");\r\n\r\n\t\tthis.currentCellPosition.col = 0;\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tthis.currentCellPosition.row++;\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderTableCell(elem: WmlTableCell) {\r\n\t\tlet result = this.createElement(\"td\");\r\n\r\n\t\tconst key = this.currentCellPosition.col;\r\n\r\n\t\tif (elem.verticalMerge) {\r\n\t\t\tif (elem.verticalMerge == \"restart\") {\r\n\t\t\t\tthis.currentVerticalMerge[key] = result;\r\n\t\t\t\tresult.rowSpan = 1;\r\n\t\t\t} else if (this.currentVerticalMerge[key]) {\r\n\t\t\t\tthis.currentVerticalMerge[key].rowSpan += 1;\r\n\t\t\t\tresult.style.display = \"none\";\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.currentVerticalMerge[key] = null;\r\n\t\t}\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tif (elem.span)\r\n\t\t\tresult.colSpan = elem.span;\r\n\r\n\t\tthis.currentCellPosition.col += result.colSpan;\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderVmlPicture(elem: OpenXmlElement) {\r\n\t\tvar result = createElement(\"div\");\r\n\t\tthis.renderChildren(elem, result);\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderVmlElement(elem: VmlElement): SVGElement {\r\n\t\tvar container = createSvgElement(\"svg\");\r\n\r\n\t\tcontainer.setAttribute(\"style\", elem.cssStyleText);\r\n\r\n\t\tconst result = this.renderVmlChildElement(elem);\r\n\r\n\t\tif (elem.imageHref?.id) {\r\n\t\t\tthis.document?.loadDocumentImage(elem.imageHref.id, this.currentPart)\r\n\t\t\t\t.then(x => result.setAttribute(\"href\", x));\r\n\t\t}\r\n\r\n\t\tcontainer.appendChild(result);\r\n\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst bb = (container.firstElementChild as any).getBBox();\r\n\r\n\t\t\tcontainer.setAttribute(\"width\", `${Math.ceil(bb.x +  bb.width)}`);\r\n\t\t\tcontainer.setAttribute(\"height\", `${Math.ceil(bb.y + bb.height)}`);\r\n\t\t});\r\n\r\n\t\treturn container;\r\n\t}\r\n\r\n\trenderVmlChildElement(elem: VmlElement): any {\r\n\t\tconst result = createSvgElement(elem.tagName as any);\r\n\t\tObject.entries(elem.attrs).forEach(([k, v]) => result.setAttribute(k, v));\r\n\r\n\t\tfor (let child of elem.children) {\r\n\t\t\tif (child.type == DomType.VmlElement) {\r\n\t\t\t\tresult.appendChild(this.renderVmlChildElement(child as VmlElement));\r\n\t\t\t} else {\r\n\t\t\t\tresult.appendChild(...asArray(this.renderElement(child as any)));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMmlRadical(elem: OpenXmlElement): HTMLElement {\r\n\t\tconst base = elem.children.find(el => el.type == DomType.MmlBase);\r\n\r\n\t\tif (elem.props?.hideDegree) {\r\n\t\t\treturn createElementNS(ns.mathML, \"msqrt\", null, this.renderElements([base]));\r\n\t\t}\r\n\r\n\t\tconst degree = elem.children.find(el => el.type == DomType.MmlDegree);\r\n\t\treturn createElementNS(ns.mathML, \"mroot\", null, this.renderElements([base, degree]));\r\n\t}\r\n\r\n\trenderMmlDelimiter(elem: OpenXmlElement): HTMLElement {\t\t\r\n\t\tconst children = [];\r\n\r\n\t\tchildren.push(createElementNS(ns.mathML, \"mo\", null, [elem.props.beginChar ?? '(']));\r\n\t\tchildren.push(...this.renderElements(elem.children));\r\n\t\tchildren.push(createElementNS(ns.mathML, \"mo\", null, [elem.props.endChar ?? ')']));\r\n\r\n\t\treturn createElementNS(ns.mathML, \"mrow\", null, children);\r\n\t}\r\n\r\n\trenderMmlNary(elem: OpenXmlElement): HTMLElement {\t\t\r\n\t\tconst children = [];\r\n\t\tconst grouped = keyBy(elem.children, x => x.type);\r\n\r\n\t\tconst sup = grouped[DomType.MmlSuperArgument];\r\n\t\tconst sub = grouped[DomType.MmlSubArgument];\r\n\t\tconst supElem = sup ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sup))) : null;\r\n\t\tconst subElem = sub ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sub))) : null;\r\n\r\n\t\tconst charElem = createElementNS(ns.mathML, \"mo\", null, [elem.props?.char ?? '\\u222B']);\r\n\r\n\t\tif (supElem || subElem) {\r\n\t\t\tchildren.push(createElementNS(ns.mathML, \"munderover\", null, [charElem, subElem, supElem]));\r\n\t\t} else if(supElem) {\r\n\t\t\tchildren.push(createElementNS(ns.mathML, \"mover\", null, [charElem, supElem]));\r\n\t\t} else if(subElem) {\r\n\t\t\tchildren.push(createElementNS(ns.mathML, \"munder\", null, [charElem, subElem]));\r\n\t\t} else {\r\n\t\t\tchildren.push(charElem);\r\n\t\t}\r\n\r\n\t\tchildren.push(...this.renderElements(grouped[DomType.MmlBase].children));\r\n\r\n\t\treturn createElementNS(ns.mathML, \"mrow\", null, children);\r\n\t}\r\n\r\n\trenderMmlPreSubSuper(elem: OpenXmlElement) {\r\n\t\tconst children = [];\r\n\t\tconst grouped = keyBy(elem.children, x => x.type);\r\n\r\n\t\tconst sup = grouped[DomType.MmlSuperArgument];\r\n\t\tconst sub = grouped[DomType.MmlSubArgument];\r\n\t\tconst supElem = sup ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sup))) : null;\r\n\t\tconst subElem = sub ? createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sub))) : null;\r\n\t\tconst stubElem = createElementNS(ns.mathML, \"mo\", null);\r\n\r\n\t\tchildren.push(createElementNS(ns.mathML, \"msubsup\", null, [stubElem, subElem, supElem]));\r\n\t\tchildren.push(...this.renderElements(grouped[DomType.MmlBase].children));\r\n\r\n\t\treturn createElementNS(ns.mathML, \"mrow\", null, children);\r\n\t}\r\n\r\n\trenderMmlGroupChar(elem: OpenXmlElement) {\r\n\t\tconst tagName = elem.props.verticalJustification === \"bot\" ? \"mover\" : \"munder\";\r\n\t\tconst result = this.renderContainerNS(elem, ns.mathML, tagName);\r\n\r\n\t\tif (elem.props.char) {\r\n\t\t\tresult.appendChild(createElementNS(ns.mathML, \"mo\", null, [elem.props.char]));\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMmlBar(elem: OpenXmlElement) {\r\n\t\tconst result = this.renderContainerNS(elem, ns.mathML, \"mrow\");\r\n\r\n\t\tswitch(elem.props.position) {\r\n\t\t\tcase \"top\": result.style.textDecoration = \"overline\"; break\r\n\t\t\tcase \"bottom\": result.style.textDecoration = \"underline\"; break\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMmlRun(elem: OpenXmlElement) {\r\n\t\tconst result = createElementNS(ns.mathML, \"ms\");\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\t\tthis.renderChildren(elem, result);\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\trenderMllList(elem: OpenXmlElement) {\r\n\t\tconst result = createElementNS(ns.mathML, \"mtable\");\r\n\r\n\t\tthis.renderClass(elem, result);\r\n\t\tthis.renderStyleValues(elem.cssStyle, result);\r\n\r\n\t\tconst childern = this.renderChildren(elem);\r\n\r\n\t\tfor (let child of this.renderChildren(elem)) {\r\n\t\t\tresult.appendChild(createElementNS(ns.mathML, \"mtr\", null, [\r\n\t\t\t\tcreateElementNS(ns.mathML, \"mtd\", null, [child])\r\n\t\t\t]));\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\r\n\trenderStyleValues(style: Record<string, string>, ouput: HTMLElement) {\r\n\t\tfor (let k in style) {\r\n\t\t\tif (k.startsWith(\"$\")) {\r\n\t\t\t\touput.setAttribute(k.slice(1), style[k]);\r\n\t\t\t} else {\r\n\t\t\t\touput.style[k] = style[k];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\trenderClass(input: OpenXmlElement, ouput: HTMLElement) {\r\n\t\tif (input.className)\r\n\t\t\touput.className = input.className;\r\n\r\n\t\tif (input.styleName)\r\n\t\t\touput.classList.add(this.processStyleName(input.styleName));\r\n\t}\r\n\r\n\tfindStyle(styleName: string) {\r\n\t\treturn styleName && this.styleMap?.[styleName];\r\n\t}\r\n\r\n\tnumberingClass(id: string, lvl: number) {\r\n\t\treturn `${this.className}-num-${id}-${lvl}`;\r\n\t}\r\n\r\n\ttabStopClass() {\r\n\t\treturn `${this.className}-tab-stop`;\r\n\t}\r\n\r\n\tstyleToString(selectors: string, values: Record<string, string>, cssText: string = null) {\r\n\t\tlet result = `${selectors} {\\r\\n`;\r\n\r\n\t\tfor (const key in values) {\r\n\t\t\tif (key.startsWith('$'))\r\n\t\t\t\tcontinue;\r\n\t\t\t\r\n\t\t\tresult += `  ${key}: ${values[key]};\\r\\n`;\r\n\t\t}\r\n\r\n\t\tif (cssText)\r\n\t\t\tresult += cssText;\r\n\r\n\t\treturn result + \"}\\r\\n\";\r\n\t}\r\n\r\n\tnumberingCounter(id: string, lvl: number) {\r\n\t\treturn `${this.className}-num-${id}-${lvl}`;\r\n\t}\r\n\r\n\tlevelTextToContent(text: string, suff: string, id: string, numformat: string) {\r\n\t\tconst suffMap = {\r\n\t\t\t\"tab\": \"\\\\9\",\r\n\t\t\t\"space\": \"\\\\a0\",\r\n\t\t};\r\n\r\n\t\tvar result = text.replace(/%\\d*/g, s => {\r\n\t\t\tlet lvl = parseInt(s.substring(1), 10) - 1;\r\n\t\t\treturn `\"counter(${this.numberingCounter(id, lvl)}, ${numformat})\"`;\r\n\t\t});\r\n\r\n\t\treturn `\"${result}${suffMap[suff] ?? \"\"}\"`;\r\n\t}\r\n\r\n\tnumFormatToCssValue(format: string) {\r\n\t\tvar mapping = {\r\n\t\t\tnone: \"none\",\r\n\t\t\tbullet: \"disc\",\r\n\t\t\tdecimal: \"decimal\",\r\n\t\t\tlowerLetter: \"lower-alpha\",\r\n\t\t\tupperLetter: \"upper-alpha\",\r\n\t\t\tlowerRoman: \"lower-roman\",\r\n\t\t\tupperRoman: \"upper-roman\",\r\n\t\t\tdecimalZero: \"decimal-leading-zero\", // 01,02,03,...\r\n\t\t\t// ordinal: \"\", // 1st, 2nd, 3rd,...\r\n\t\t\t// ordinalText: \"\", //First, Second, Third, ...\r\n\t\t\t// cardinalText: \"\", //One,Two Three,...\r\n\t\t\t// numberInDash: \"\", //-1-,-2-,-3-, ...\r\n\t\t\t// hex: \"upper-hexadecimal\",\r\n\t\t\taiueo: \"katakana\",\r\n\t\t\taiueoFullWidth: \"katakana\",\r\n\t\t\tchineseCounting: \"simp-chinese-informal\",\r\n\t\t\tchineseCountingThousand: \"simp-chinese-informal\",\r\n\t\t\tchineseLegalSimplified: \"simp-chinese-formal\", // 中文大写\r\n\t\t\tchosung: \"hangul-consonant\",\r\n\t\t\tideographDigital: \"cjk-ideographic\",\r\n\t\t\tideographTraditional: \"cjk-heavenly-stem\", // 十天干\r\n\t\t\tideographLegalTraditional: \"trad-chinese-formal\",\r\n\t\t\tideographZodiac: \"cjk-earthly-branch\", // 十二地支\r\n\t\t\tiroha: \"katakana-iroha\",\r\n\t\t\tirohaFullWidth: \"katakana-iroha\",\r\n\t\t\tjapaneseCounting: \"japanese-informal\",\r\n\t\t\tjapaneseDigitalTenThousand: \"cjk-decimal\",\r\n\t\t\tjapaneseLegal: \"japanese-formal\",\r\n\t\t\tthaiNumbers: \"thai\",\r\n\t\t\tkoreanCounting: \"korean-hangul-formal\",\r\n\t\t\tkoreanDigital: \"korean-hangul-formal\",\r\n\t\t\tkoreanDigital2: \"korean-hanja-informal\",\r\n\t\t\thebrew1: \"hebrew\",\r\n\t\t\thebrew2: \"hebrew\",\r\n\t\t\thindiNumbers: \"devanagari\",\r\n\t\t\tganada: \"hangul\",\r\n\t\t\ttaiwaneseCounting: \"cjk-ideographic\",\r\n\t\t\ttaiwaneseCountingThousand: \"cjk-ideographic\",\r\n\t\t\ttaiwaneseDigital:  \"cjk-decimal\",\r\n\t\t};\r\n\r\n\t\treturn mapping[format] ?? format;\r\n\t}\r\n\r\n\trefreshTabStops() {\r\n\t\tif (!this.options.experimental)\r\n\t\t\treturn;\r\n\r\n\t\tclearTimeout(this.tabsTimeout);\r\n\r\n\t\tthis.tabsTimeout = setTimeout(() => {\r\n\t\t\tconst pixelToPoint = computePixelToPoint();\r\n\r\n\t\t\tfor (let tab of this.currentTabs) {\r\n\t\t\t\tupdateTabStop(tab.span, tab.stops, this.defaultTabSize, pixelToPoint);\r\n\t\t\t}\r\n\t\t}, 500);\r\n\t}\r\n\r\n\tcreateElement = createElement;\r\n}\r\n\r\ntype ChildType = Node | string;\r\n\r\nfunction createElement<T extends keyof HTMLElementTagNameMap>(\r\n\ttagName: T,\r\n\tprops?: Partial<Record<keyof HTMLElementTagNameMap[T], any>>,\r\n\tchildren?: ChildType[]\r\n): HTMLElementTagNameMap[T] {\r\n\treturn createElementNS(undefined, tagName, props, children);\r\n}\r\n\r\nfunction createSvgElement<T extends keyof SVGElementTagNameMap>(\r\n\ttagName: T,\r\n\tprops?: Partial<Record<keyof SVGElementTagNameMap[T], any>>,\r\n\tchildren?: ChildType[]\r\n): SVGElementTagNameMap[T] {\r\n\treturn createElementNS(ns.svg, tagName, props, children);\r\n}\r\n\r\nfunction createElementNS(ns: string, tagName: string, props?: Partial<Record<any, any>>, children?: ChildType[]): any {\r\n\tvar result = ns ? document.createElementNS(ns, tagName) : document.createElement(tagName);\r\n\tObject.assign(result, props);\r\n\tchildren && appendChildren(result, children);\r\n\treturn result;\r\n}\r\n\r\nfunction removeAllElements(elem: HTMLElement) {\r\n\telem.innerHTML = '';\r\n}\r\n\r\nfunction appendChildren(elem: Element, children: (Node | string)[]) {\r\n\tchildren.forEach(c => elem.appendChild(isString(c) ? document.createTextNode(c) : c));\r\n}\r\n\r\nfunction createStyleElement(cssText: string) {\r\n\treturn createElement(\"style\", { innerHTML: cssText });\r\n}\r\n\r\nfunction appendComment(elem: HTMLElement, comment: string) {\r\n\telem.appendChild(document.createComment(comment));\r\n}\r\n\r\nfunction findParent<T extends OpenXmlElement>(elem: OpenXmlElement, type: DomType): T {\r\n\tvar parent = elem.parent;\r\n\r\n\twhile (parent != null && parent.type != type)\r\n\t\tparent = parent.parent;\r\n\r\n\treturn <T>parent;\r\n}\r\n", "import { Length } from \"./document/common\";\r\nimport { ParagraphTab } from \"./document/paragraph\";\r\n\r\ninterface TabStop {\r\n\tpos: number;\r\n\tleader: string;\r\n\tstyle: string;\r\n}\r\n\r\nconst defaultTab: TabStop = { pos: 0, leader: \"none\", style: \"left\" };\r\nconst maxTabs = 50;\r\n\r\nexport function computePixelToPoint(container: HTMLElement = document.body) {\r\n\tconst temp = document.createElement(\"div\");\r\n\ttemp.style.width = '100pt';\r\n\t\r\n\tcontainer.appendChild(temp);\r\n\tconst result = 100 / temp.offsetWidth;\r\n\tcontainer.removeChild(temp);\r\n\r\n\treturn result\r\n}\r\n\r\nexport function updateTabStop(elem: HTMLElement, tabs: ParagraphTab[], defaultTabSize: Length, pixelToPoint: number = 72 / 96) {\r\n    const p = elem.closest(\"p\");\r\n\r\n    const ebb = elem.getBoundingClientRect();\r\n    const pbb = p.getBoundingClientRect();\r\n    const pcs = getComputedStyle(p);\r\n\r\n\tconst tabStops = tabs?.length > 0 ? tabs.map(t => ({\r\n\t\tpos: lengthToPoint(t.position),\r\n\t\tleader: t.leader,\r\n\t\tstyle: t.style\r\n\t})).sort((a, b) => a.pos - b.pos) : [defaultTab];\r\n\r\n\tconst lastTab = tabStops[tabStops.length - 1];\r\n\tconst pWidthPt = pbb.width * pixelToPoint;\r\n\tconst size = lengthToPoint(defaultTabSize);\r\n    let pos = lastTab.pos + size;\r\n\r\n    if (pos < pWidthPt) {\r\n        for (; pos < pWidthPt && tabStops.length < maxTabs; pos += size) {\r\n            tabStops.push({ ...defaultTab, pos: pos });\r\n        }\r\n    }\r\n\r\n    const marginLeft = parseFloat(pcs.marginLeft);\r\n    const pOffset = pbb.left + marginLeft;\r\n    const left = (ebb.left - pOffset) * pixelToPoint;\r\n    const tab = tabStops.find(t => t.style != \"clear\" && t.pos > left);\r\n\r\n    if(tab == null)\r\n        return;\r\n\r\n    let width: number = 1;\r\n\r\n    if (tab.style == \"right\" || tab.style == \"center\") {\r\n\t\tconst tabStops = Array.from(p.querySelectorAll(`.${elem.className}`));\r\n\t\tconst nextIdx = tabStops.indexOf(elem) + 1;\r\n        const range = document.createRange();\r\n        range.setStart(elem, 1);\r\n\r\n\t\tif (nextIdx < tabStops.length) {\r\n\t\t\trange.setEndBefore(tabStops[nextIdx]);\r\n\t\t} else {\r\n\t\t\trange.setEndAfter(p);\r\n\t\t}\r\n\r\n\t\tconst mul = tab.style == \"center\" ? 0.5 : 1;\r\n        const nextBB = range.getBoundingClientRect();\r\n\t\tconst offset = nextBB.left + mul * nextBB.width - (pbb.left - marginLeft);\r\n\r\n\t\twidth = tab.pos - offset * pixelToPoint;\r\n    } else {\r\n        width = tab.pos - left;\r\n    }\r\n\r\n    elem.innerHTML = \"&nbsp;\";\r\n    elem.style.textDecoration = \"inherit\";\r\n    elem.style.wordSpacing = `${width.toFixed(0)}pt`;\r\n\r\n    switch (tab.leader) {\r\n        case \"dot\":\r\n        case \"middleDot\":\r\n            elem.style.textDecoration = \"underline\";\r\n            elem.style.textDecorationStyle = \"dotted\";\r\n            break;\r\n\r\n        case \"hyphen\":\r\n        case \"heavy\":\r\n        case \"underscore\":\r\n            elem.style.textDecoration = \"underline\";\r\n            break;\r\n    }\r\n}\r\n\r\nfunction lengthToPoint(length: Length): number {\r\n\treturn parseFloat(length);\r\n}", "import { OpenXmlElementBase, DomType } from \"../document/dom\";\r\n\r\nexport abstract class WmlBaseNote implements OpenXmlElementBase {\r\n    type: DomType;\r\n    id: string;\r\n\tnoteType: string;\r\n}\r\n\r\nexport class WmlFootnote extends WmlBaseNote {\r\n\ttype = DomType.Footnote\r\n}\r\n\r\nexport class WmlEndnote extends WmlBaseNote {\r\n\ttype = DomType.Endnote\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { WmlBaseNote, WmlEndnote, WmlFootnote } from \"./elements\";\r\n\r\nexport class BaseNotePart<T extends WmlBaseNote> extends Part {\r\n    protected _documentParser: DocumentParser;\r\n\r\n    notes: T[]\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n}\r\n\r\nexport class FootnotesPart extends BaseNotePart<WmlFootnote> {\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path, parser);\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.notes = this._documentParser.parseNotes(root, \"footnote\", WmlFootnote);\r\n    }\r\n}\r\n\r\nexport class EndnotesPart extends BaseNotePart<WmlEndnote> {\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path, parser);\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.notes = this._documentParser.parseNotes(root, \"endnote\", WmlEndnote);\r\n    }\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { IDomNumbering } from \"../document/dom\";\r\nimport { AbstractNumbering, Numbering, NumberingBulletPicture, NumberingPartProperties, parseNumberingPart } from \"./numbering\";\r\n\r\nexport class NumberingPart extends Part implements NumberingPartProperties {\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n\r\n    numberings: Numbering[];\r\n    abstractNumberings: AbstractNumbering[];\r\n    bulletPictures: NumberingBulletPicture[];\r\n    \r\n    domNumberings: IDomNumbering[];\r\n\r\n    parseXml(root: Element) {\r\n        Object.assign(this, parseNumberingPart(root, this._package.xmlParser));\r\n        this.domNumberings = this._documentParser.parseNumberingFile(root);  \r\n    }\r\n}", "import { NumberingPicBullet } from \"../document/dom\";\r\nimport { ParagraphProperties, parseParagraphProperties } from \"../document/paragraph\";\r\nimport { parseRunProperties, RunProperties } from \"../document/run\";\r\nimport { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface NumberingPartProperties {\r\n    numberings: Numbering[];\r\n    abstractNumberings: AbstractNumbering[];\r\n    bulletPictures: NumberingBulletPicture[];\r\n}\r\n\r\nexport interface Numbering {\r\n    id: string;\r\n    abstractId: string;\r\n    overrides: NumberingLevelOverride[];\r\n}\r\n\r\nexport interface NumberingLevelOverride {\r\n    level: number;\r\n    start: number;\r\n    numberingLevel: NumberingLevel;\r\n}\r\n\r\nexport interface AbstractNumbering {\r\n    id: string;\r\n    name: string;\r\n    multiLevelType: \"singleLevel\" | \"multiLevel\" | \"hybridMultilevel\" | string;\r\n    levels: NumberingLevel[];\r\n    numberingStyleLink: string;\r\n    styleLink: string;\r\n}\r\n\r\nexport interface NumberingLevel {\r\n    level: number;\r\n    start: string;\r\n    restart: number;\r\n    format: 'lowerRoman' | 'lowerLetter' | string;\r\n    text: string;\r\n    justification: string;\r\n    bulletPictureId: string;\r\n    paragraphStyle: string;\r\n    paragraphProps: ParagraphProperties;\r\n    runProps: RunProperties;\r\n}\r\n\r\nexport interface NumberingBulletPicture {\r\n    id: string;\r\n    referenceId: string;\r\n    style: string;\r\n}\r\n\r\nexport function parseNumberingPart(elem: Element, xml: XmlParser): NumberingPartProperties {\r\n    let result: NumberingPartProperties = {\r\n        numberings: [],\r\n        abstractNumberings: [],\r\n        bulletPictures: []\r\n    }\r\n    \r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"num\":\r\n                result.numberings.push(parseNumbering(e, xml));\r\n                break;\r\n            case \"abstractNum\":\r\n                result.abstractNumberings.push(parseAbstractNumbering(e, xml));\r\n                break;\r\n            case \"numPicBullet\":\r\n                result.bulletPictures.push(parseNumberingBulletPicture(e, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumbering(elem: Element, xml: XmlParser): Numbering {\r\n    let result = <Numbering>{\r\n        id: xml.attr(elem, 'numId'),\r\n        overrides: []\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"abstractNumId\":\r\n                result.abstractId = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlOverride\":\r\n                result.overrides.push(parseNumberingLevelOverrride(e, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseAbstractNumbering(elem: Element, xml: XmlParser): AbstractNumbering {\r\n    let result = <AbstractNumbering>{\r\n        id: xml.attr(elem, 'abstractNumId'),\r\n        levels: []\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"name\":\r\n                result.name = xml.attr(e, \"val\");\r\n                break;\r\n            case \"multiLevelType\":\r\n                result.multiLevelType = xml.attr(e, \"val\");\r\n                break;\r\n            case \"numStyleLink\":\r\n                result.numberingStyleLink = xml.attr(e, \"val\");\r\n                break;\r\n            case \"styleLink\":\r\n                result.styleLink = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvl\":\r\n                result.levels.push(parseNumberingLevel(e, xml));\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumberingLevel(elem: Element, xml: XmlParser): NumberingLevel {\r\n    let result = <NumberingLevel>{\r\n        level: xml.intAttr(elem, 'ilvl')\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"start\":\r\n                result.start = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlRestart\":\r\n                result.restart = xml.intAttr(e, \"val\");\r\n                break;\r\n            case \"numFmt\":\r\n                result.format = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlText\":\r\n                result.text = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlJc\":\r\n                result.justification = xml.attr(e, \"val\");\r\n                break;\r\n            case \"lvlPicBulletId\":\r\n                result.bulletPictureId = xml.attr(e, \"val\");\r\n                break;\r\n            case \"pStyle\":\r\n                result.paragraphStyle = xml.attr(e, \"val\");\r\n                break;\r\n            case \"pPr\":\r\n                result.paragraphProps = parseParagraphProperties(e, xml);\r\n                break;\r\n            case \"rPr\":\r\n                result.runProps = parseRunProperties(e, xml);\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumberingLevelOverrride(elem: Element, xml: XmlParser): NumberingLevelOverride {\r\n    let result = <NumberingLevelOverride>{\r\n        level: xml.intAttr(elem, 'ilvl')\r\n    };\r\n\r\n    for (let e of xml.elements(elem)) {\r\n        switch (e.localName) {\r\n            case \"startOverride\":\r\n                result.start = xml.intAttr(e, \"val\");\r\n                break;\r\n            case \"lvl\":\r\n                result.numberingLevel = parseNumberingLevel(e, xml);\r\n                break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNumberingBulletPicture(elem: Element, xml: XmlParser): NumberingBulletPicture {\r\n    //TODO\r\n    var pict = xml.element(elem, \"pict\");\r\n    var shape = pict && xml.element(pict, \"shape\");\r\n    var imagedata = shape && xml.element(shape, \"imagedata\");\r\n\r\n    return imagedata ? {\r\n        id: xml.attr(elem, \"numPicBulletId\"),\r\n        referenceId: xml.attr(imagedata, \"id\"),\r\n        style: xml.attr(shape, \"style\")\r\n    } : null;\r\n}", "import { Length,  LengthUsage, LengthUsageType, convertLength, convertBoolean  } from \"../document/common\";\r\n\r\nexport function parseXmlString(xmlString: string, trimXmlDeclaration: boolean = false): Document {\r\n    if (trimXmlDeclaration)\r\n        xmlString = xmlString.replace(/<[?].*[?]>/, \"\");\r\n        \r\n    xmlString = removeUTF8BOM(xmlString);\r\n    \r\n    const result = new DOMParser().parseFromString(xmlString, \"application/xml\");  \r\n    const errorText = hasXmlParserError(result);\r\n\r\n    if (errorText)\r\n        throw new Error(errorText);\r\n\r\n    return result;\r\n}\r\n\r\nfunction hasXmlParserError(doc: Document) {\r\n    return doc.getElementsByTagName(\"parsererror\")[0]?.textContent;\r\n}\r\n\r\nfunction removeUTF8BOM(data: string) {\r\n    return data.charCodeAt(0) === 0xFEFF ? data.substring(1) : data;\r\n}\r\n\r\nexport function serializeXmlString(elem: Node): string {\r\n    return new XMLSerializer().serializeToString(elem);\r\n}\r\n\r\nexport class XmlParser {\r\n    elements(elem: Element, localName: string = null): Element[] {\r\n        const result = [];\r\n\r\n        for (let i = 0, l = elem.childNodes.length; i < l; i++) {\r\n            let c = elem.childNodes.item(i);\r\n\r\n            if (c.nodeType == 1 && (localName == null || (c as Element).localName == localName))\r\n                result.push(c);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    element(elem: Element, localName: string): Element {\r\n        for (let i = 0, l = elem.childNodes.length; i < l; i++) {\r\n            let c = elem.childNodes.item(i);\r\n\r\n            if (c.nodeType == 1 && (c as Element).localName == localName)\r\n                return c as Element;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    elementAttr(elem: Element, localName: string, attrLocalName: string): string {\r\n        var el = this.element(elem, localName);\r\n        return el ? this.attr(el, attrLocalName) : undefined;\r\n    }\r\n\r\n\tattrs(elem: Element) {\r\n\t\treturn Array.from(elem.attributes);\r\n\t}\r\n\r\n    attr(elem: Element, localName: string): string {\r\n        for (let i = 0, l = elem.attributes.length; i < l; i++) {\r\n            let a = elem.attributes.item(i);\r\n\r\n            if (a.localName == localName)\r\n                return a.value;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    intAttr(node: Element, attrName: string, defaultValue: number = null): number {\r\n        var val = this.attr(node, attrName);\r\n        return val ? parseInt(val) : defaultValue;\r\n    }\r\n\r\n\thexAttr(node: Element, attrName: string, defaultValue: number = null): number {\r\n        var val = this.attr(node, attrName);\r\n        return val ? parseInt(val, 16) : defaultValue;\r\n    }\r\n\r\n    floatAttr(node: Element, attrName: string, defaultValue: number = null): number {\r\n        var val = this.attr(node, attrName);\r\n        return val ? parseFloat(val) : defaultValue;\r\n    }\r\n\r\n    boolAttr(node: Element, attrName: string, defaultValue: boolean = null) {\r\n        return convertBoolean(this.attr(node, attrName), defaultValue);\r\n    }\r\n\r\n    lengthAttr(node: Element, attrName: string, usage: LengthUsageType = LengthUsage.Dxa): Length {\r\n        return convertLength(this.attr(node, attrName), usage);\r\n    }\r\n}\r\n\r\nconst globalXmlParser = new XmlParser();\r\n\r\nexport default globalXmlParser;", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { WmlSettings, parseSettings } from \"./settings\";\r\n\r\nexport class SettingsPart extends Part {\r\n\tsettings: WmlSettings;\r\n\r\n\tconstructor(pkg: OpenXmlPackage, path: string) {\r\n\t\tsuper(pkg, path);\r\n\t}\r\n\r\n\tparseXml(root: Element) {\r\n\t\tthis.settings = parseSettings(root, this._package.xmlParser);\r\n\t}\r\n}", "import { DocumentParser } from \"../document-parser\";\r\nimport { Length } from \"../document/common\";\r\nimport { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport interface WmlSettings {\r\n\tdefaultTabStop: Length;\r\n\tfootnoteProps: NoteProperties;\r\n\tendnoteProps: NoteProperties;\r\n\tautoHyphenation: boolean;\r\n}\r\n\r\nexport interface NoteProperties {\r\n\tnummeringFormat: string;\r\n\tdefaultNoteIds: string[];\r\n}\r\n\r\nexport function parseSettings(elem: Element, xml: XmlParser) {\r\n\tvar result = {} as WmlSettings; \r\n\r\n\tfor (let el of xml.elements(elem)) {\r\n\t\tswitch(el.localName) {\r\n\t\t\tcase \"defaultTabStop\": result.defaultTabStop = xml.lengthAttr(el, \"val\"); break;\r\n\t\t\tcase \"footnotePr\": result.footnoteProps = parseNoteProperties(el, xml); break;\r\n\t\t\tcase \"endnotePr\": result.endnoteProps = parseNoteProperties(el, xml); break;\r\n\t\t\tcase \"autoHyphenation\": result.autoHyphenation = xml.boolAttr(el, \"val\"); break;\r\n\t\t}\r\n\t}\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseNoteProperties(elem: Element, xml: XmlParser) {\r\n\tvar result = {\r\n\t\tdefaultNoteIds: []\r\n\t} as NoteProperties; \r\n\r\n\tfor (let el of xml.elements(elem)) {\r\n\t\tswitch(el.localName) {\r\n\t\t\tcase \"numFmt\": \r\n\t\t\t\tresult.nummeringFormat = xml.attr(el, \"val\");\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"footnote\": \r\n\t\t\tcase \"endnote\": \r\n\t\t\t\tresult.defaultNoteIds.push(xml.attr(el, \"id\"));\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n    return result;\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DocumentParser } from \"../document-parser\";\r\nimport { IDomStyle } from \"../document/style\";\r\n\r\nexport class StylesPart extends Part {\r\n    styles: IDomStyle[];\r\n\r\n    private _documentParser: DocumentParser;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string, parser: DocumentParser) {\r\n        super(pkg, path);\r\n        this._documentParser = parser;\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.styles = this._documentParser.parseStylesFile(root);     \r\n    }\r\n}", "import { OpenXmlPackage } from \"../common/open-xml-package\";\r\nimport { Part } from \"../common/part\";\r\nimport { DmlTheme, parseTheme } from \"./theme\";\r\n\r\nexport class ThemePart extends Part {\r\n    theme: DmlTheme;\r\n\r\n    constructor(pkg: OpenXmlPackage, path: string) {\r\n        super(pkg, path);\r\n    }\r\n\r\n    parseXml(root: Element) {\r\n        this.theme = parseTheme(root, this._package.xmlParser);\r\n    }\r\n}", "import { XmlParser } from \"../parser/xml-parser\";\r\n\r\nexport class DmlTheme {\r\n    colorScheme: DmlColorScheme;\r\n    fontScheme: DmlFontScheme;\r\n}\r\n\r\nexport interface DmlColorScheme {\r\n    name: string;\r\n    colors: Record<string, string>;\r\n}\r\n\r\nexport interface DmlFontScheme {\r\n    name: string;\r\n    majorFont: DmlFormInfo,\r\n    minorFont: DmlFormInfo\r\n}\r\n\r\nexport interface DmlFormInfo {\r\n    latinTypeface: string;\r\n    eaTypeface: string;\r\n    csTypeface: string;\r\n}\r\n\r\nexport function parseTheme(elem: Element, xml: XmlParser) {\r\n    var result = new DmlTheme();\r\n    var themeElements = xml.element(elem, \"themeElements\");\r\n\r\n    for (let el of xml.elements(themeElements)) {\r\n        switch(el.localName) {\r\n            case \"clrScheme\": result.colorScheme = parseColorScheme(el, xml); break;\r\n            case \"fontScheme\": result.fontScheme = parseFontScheme(el, xml); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseColorScheme(elem: Element, xml: XmlParser) {\r\n    var result: DmlColorScheme = { \r\n        name: xml.attr(elem, \"name\"),\r\n        colors: {}\r\n    };\r\n\r\n    for (let el of xml.elements(elem)) {\r\n        var srgbClr = xml.element(el, \"srgbClr\");\r\n        var sysClr = xml.element(el, \"sysClr\");\r\n\r\n        if (srgbClr) {\r\n            result.colors[el.localName] = xml.attr(srgbClr, \"val\");\r\n        }\r\n        else if (sysClr) {\r\n            result.colors[el.localName] = xml.attr(sysClr, \"lastClr\");\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseFontScheme(elem: Element, xml: XmlParser) {\r\n    var result: DmlFontScheme = { \r\n        name: xml.attr(elem, \"name\"),\r\n    } as DmlFontScheme;\r\n\r\n    for (let el of xml.elements(elem)) {\r\n        switch (el.localName) {\r\n            case \"majorFont\": result.majorFont = parseFontInfo(el, xml); break;\r\n            case \"minorFont\": result.minorFont = parseFontInfo(el, xml); break;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nexport function parseFontInfo(elem: Element, xml: XmlParser): DmlFormInfo {\r\n    return {\r\n        latinTypeface: xml.elementAttr(elem, \"latin\", \"typeface\"),\r\n        eaTypeface: xml.elementAttr(elem, \"ea\", \"typeface\"),\r\n        csTypeface: xml.elementAttr(elem, \"cs\", \"typeface\"),\r\n    };\r\n}", "export function escapeClassName(className: string) {\r\n\treturn className?.replace(/[ .]+/g, '-').replace(/[&]+/g, 'and').toLowerCase();\r\n}\r\n\r\nexport function splitPath(path: string): [string, string] {\r\n    let si = path.lastIndexOf('/') + 1;\r\n    let folder = si == 0 ? \"\" : path.substring(0, si);\r\n    let fileName = si == 0 ? path : path.substring(si);\r\n\r\n    return [folder, fileName];\r\n}\r\n\r\nexport function resolvePath(path: string, base: string): string {\r\n    try {\r\n        const prefix = \"http://docx/\";\r\n        const url = new URL(path, prefix + base).toString();\r\n        return url.substring(prefix.length);\r\n    } catch {\r\n        return `${base}${path}`;\r\n    }\r\n}\r\n\r\nexport function keyBy<T = any>(array: T[], by: (x: T) => any): Record<any, T> {\r\n    return array.reduce((a, x) => {\r\n        a[by(x)] = x;\r\n        return a;\r\n    }, {});\r\n}\r\n\r\nexport function blobToBase64(blob: Blob): Promise<string> {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tconst reader = new FileReader();\r\n\t\treader.onloadend = () => resolve(reader.result as string);\r\n\t\treader.onerror = () => reject();\r\n\t\treader.readAsDataURL(blob);\r\n\t});\r\n}\r\n\r\nexport function isObject(item) {\r\n    return item && typeof item === 'object' && !Array.isArray(item);\r\n}\r\n\r\nexport function isString(item: unknown): item is string {\r\n    return typeof item === 'string' || item instanceof String;\r\n}\r\n\r\nexport function mergeDeep(target, ...sources) {\r\n    if (!sources.length) \r\n        return target;\r\n    \r\n    const source = sources.shift();\r\n\r\n    if (isObject(target) && isObject(source)) {\r\n        for (const key in source) {\r\n            if (isObject(source[key])) {\r\n                const val = target[key] ?? (target[key] = {});\r\n                mergeDeep(val, source[key]);\r\n            } else {\r\n                target[key] = source[key];\r\n            }\r\n        }\r\n    }\r\n\r\n    return mergeDeep(target, ...sources);\r\n}\r\n\r\nexport function parseCssRules(text: string): Record<string, string> {\r\n\tconst result: Record<string, string> = {};\r\n\r\n\tfor (const rule of text.split(';')) {\r\n\t\tconst [key, val] = rule.split(':');\r\n\t\tresult[key] = val;\r\n\t}\r\n\r\n\treturn result\r\n}\r\n\r\nexport function formatCssRules(style: Record<string, string>): string {\r\n\treturn Object.entries(style).map((k, v) => `${k}: ${v}`).join(';');\r\n}\r\n\r\nexport function asArray<T>(val: T | T[]): T[] {\r\n\treturn Array.isArray(val) ? val : [val];\r\n}", "import { DocumentParser } from '../document-parser';\r\nimport { convertLength, LengthUsage } from '../document/common';\r\nimport { OpenXmlElementBase, DomType } from '../document/dom';\r\nimport xml from '../parser/xml-parser';\r\nimport { formatCssRules, parseCssRules } from '../utils';\r\n\r\nexport class VmlElement extends OpenXmlElementBase {\r\n\ttype: DomType = DomType.VmlElement;\r\n\ttagName: string;\r\n\tcssStyleText?: string;\r\n\tattrs: Record<string, string> = {};\r\n\twrapType?: string;\r\n\timageHref?: {\r\n\t\tid: string,\r\n\t\ttitle: string\r\n\t}\r\n}\r\n\r\nexport function parseVmlElement(elem: Element, parser: DocumentParser): VmlElement {\r\n\tvar result = new VmlElement();\r\n\r\n\tswitch (elem.localName) {\r\n\t\tcase \"rect\":\r\n\t\t\tresult.tagName = \"rect\"; \r\n\t\t\tObject.assign(result.attrs, { width: '100%', height: '100%' });\r\n\t\t\tbreak;\r\n\r\n\t\tcase \"oval\":\r\n\t\t\tresult.tagName = \"ellipse\"; \r\n\t\t\tObject.assign(result.attrs, { cx: \"50%\", cy: \"50%\", rx: \"50%\", ry: \"50%\" });\r\n\t\t\tbreak;\r\n\t\r\n\t\tcase \"line\":\r\n\t\t\tresult.tagName = \"line\"; \r\n\t\t\tbreak;\r\n\r\n\t\tcase \"shape\":\r\n\t\t\tresult.tagName = \"g\"; \r\n\t\t\tbreak;\r\n\r\n\t\tcase \"textbox\":\r\n\t\t\tresult.tagName = \"foreignObject\"; \r\n\t\t\tObject.assign(result.attrs, { width: '100%', height: '100%' });\r\n\t\t\tbreak;\r\n\t\r\n\t\tdefault:\r\n\t\t\treturn null;\r\n\t}\r\n\r\n\tfor (const at of xml.attrs(elem)) {\r\n\t\tswitch(at.localName) {\r\n\t\t\tcase \"style\": \r\n\t\t\t\tresult.cssStyleText = at.value;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"fillcolor\": \r\n\t\t\t\tresult.attrs.fill = at.value; \r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"from\":\r\n\t\t\t\tconst [x1, y1] = parsePoint(at.value);\r\n\t\t\t\tObject.assign(result.attrs, { x1, y1 });\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"to\":\r\n\t\t\t\tconst [x2, y2] = parsePoint(at.value);\r\n\t\t\t\tObject.assign(result.attrs, { x2, y2 });\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\tfor (const el of xml.elements(elem)) {\r\n\t\tswitch (el.localName) {\r\n\t\t\tcase \"stroke\": \r\n\t\t\t\tObject.assign(result.attrs, parseStroke(el));\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"fill\": \r\n\t\t\t\tObject.assign(result.attrs, parseFill(el));\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"imagedata\":\r\n\t\t\t\tresult.tagName = \"image\";\r\n\t\t\t\tObject.assign(result.attrs, { width: '100%', height: '100%' });\r\n\t\t\t\tresult.imageHref = {\r\n\t\t\t\t\tid: xml.attr(el, \"id\"),\r\n\t\t\t\t\ttitle: xml.attr(el, \"title\"),\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"txbxContent\": \r\n\t\t\t\tresult.children.push(...parser.parseBodyElements(el));\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tconst child = parseVmlElement(el, parser);\r\n\t\t\t\tchild && result.children.push(child);\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\r\n\treturn result;\r\n}\r\n\r\nfunction parseStroke(el: Element): Record<string, string> {\r\n\treturn {\r\n\t\t'stroke': xml.attr(el, \"color\"),\r\n\t\t'stroke-width': xml.lengthAttr(el, \"weight\", LengthUsage.Emu) ?? '1px'\r\n\t};\r\n}\r\n\r\nfunction parseFill(el: Element): Record<string, string> {\r\n\treturn {\r\n\t\t//'fill': xml.attr(el, \"color2\")\r\n\t};\r\n}\r\n\r\nfunction parsePoint(val: string): string[] {\r\n\treturn val.split(\",\");\r\n}\r\n\r\nfunction convertPath(path: string): string {\r\n\treturn path.replace(/([mlxe])|([-\\d]+)|([,])/g, (m) => {\r\n\t\tif (/[-\\d]/.test(m)) return convertLength(m,  LengthUsage.VmlEmu);\r\n\t\tif (/[ml,]/.test(m)) return m;\r\n\r\n\t\treturn '';\r\n\t});\r\n}", "import { OutputType } from \"jszip\";\r\n\r\nimport { DocumentParser } from './document-parser';\r\nimport { Relationship, RelationshipTypes } from './common/relationship';\r\nimport { Part } from './common/part';\r\nimport { FontTablePart } from './font-table/font-table';\r\nimport { OpenXmlPackage } from './common/open-xml-package';\r\nimport { DocumentPart } from './document/document-part';\r\nimport { blobToBase64, resolvePath, splitPath } from './utils';\r\nimport { NumberingPart } from './numbering/numbering-part';\r\nimport { StylesPart } from './styles/styles-part';\r\nimport { FooterPart, HeaderPart } from \"./header-footer/parts\";\r\nimport { ExtendedPropsPart } from \"./document-props/extended-props-part\";\r\nimport { CorePropsPart } from \"./document-props/core-props-part\";\r\nimport { ThemePart } from \"./theme/theme-part\";\r\nimport { EndnotesPart, FootnotesPart } from \"./notes/parts\";\r\nimport { SettingsPart } from \"./settings/settings-part\";\r\nimport { CustomPropsPart } from \"./document-props/custom-props-part\";\r\n\r\nconst topLevelRels = [\r\n\t{ type: RelationshipTypes.OfficeDocument, target: \"word/document.xml\" },\r\n\t{ type: RelationshipTypes.ExtendedProperties, target: \"docProps/app.xml\" },\r\n\t{ type: RelationshipTypes.CoreProperties, target: \"docProps/core.xml\" },\r\n\t{ type: RelationshipTypes.CustomProperties, target: \"docProps/custom.xml\" },\r\n];\r\n\r\nexport class WordDocument {\r\n\tprivate _package: OpenXmlPackage;\r\n\tprivate _parser: DocumentParser;\r\n\tprivate _options: any;\r\n\r\n\trels: Relationship[];\r\n\tparts: Part[] = [];\r\n\tpartsMap: Record<string, Part> = {};\r\n\r\n\tdocumentPart: DocumentPart;\r\n\tfontTablePart: FontTablePart;\r\n\tnumberingPart: NumberingPart;\r\n\tstylesPart: StylesPart;\r\n\tfootnotesPart: FootnotesPart;\r\n\tendnotesPart: EndnotesPart;\r\n\tthemePart: ThemePart;\r\n\tcorePropsPart: CorePropsPart;\r\n\textendedPropsPart: ExtendedPropsPart;\r\n\tsettingsPart: SettingsPart;\r\n\r\n\tstatic async load(blob: Blob | any, parser: DocumentParser, options: any): Promise<WordDocument> {\r\n\t\tvar d = new WordDocument();\r\n\r\n\t\td._options = options;\r\n\t\td._parser = parser;\r\n\t\td._package = await OpenXmlPackage.load(blob, options);\r\n\t\td.rels = await d._package.loadRelationships();\r\n\r\n\t\tawait Promise.all(topLevelRels.map(rel => {\r\n\t\t\tconst r = d.rels.find(x => x.type === rel.type) ?? rel; //fallback                    \r\n\t\t\treturn d.loadRelationshipPart(r.target, r.type);\r\n\t\t}));\r\n\r\n\t\treturn d;\r\n\t}\r\n\r\n\tsave(type = \"blob\"): Promise<any> {\r\n\t\treturn this._package.save(type);\r\n\t}\r\n\r\n\tprivate async loadRelationshipPart(path: string, type: string): Promise<Part> {\r\n\t\tif (this.partsMap[path])\r\n\t\t\treturn this.partsMap[path];\r\n\r\n\t\tif (!this._package.get(path))\r\n\t\t\treturn null;\r\n\r\n\t\tlet part: Part = null;\r\n\r\n\t\tswitch (type) {\r\n\t\t\tcase RelationshipTypes.OfficeDocument:\r\n\t\t\t\tthis.documentPart = part = new DocumentPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.FontTable:\r\n\t\t\t\tthis.fontTablePart = part = new FontTablePart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Numbering:\r\n\t\t\t\tthis.numberingPart = part = new NumberingPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Styles:\r\n\t\t\t\tthis.stylesPart = part = new StylesPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Theme:\r\n\t\t\t\tthis.themePart = part = new ThemePart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Footnotes:\r\n\t\t\t\tthis.footnotesPart = part = new FootnotesPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Endnotes:\r\n\t\t\t\tthis.endnotesPart = part = new EndnotesPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Footer:\r\n\t\t\t\tpart = new FooterPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.Header:\r\n\t\t\t\tpart = new HeaderPart(this._package, path, this._parser);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.CoreProperties:\r\n\t\t\t\tthis.corePropsPart = part = new CorePropsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.ExtendedProperties:\r\n\t\t\t\tthis.extendedPropsPart = part = new ExtendedPropsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase RelationshipTypes.CustomProperties:\r\n\t\t\t\tpart = new CustomPropsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tcase RelationshipTypes.Settings:\r\n\t\t\t\tthis.settingsPart = part = new SettingsPart(this._package, path);\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\tif (part == null)\r\n\t\t\treturn Promise.resolve(null);\r\n\r\n\t\tthis.partsMap[path] = part;\r\n\t\tthis.parts.push(part);\r\n\r\n\t\tawait part.load();\r\n\r\n\t\tif (part.rels?.length > 0) {\r\n\t\t\tconst [folder] = splitPath(part.path);\r\n\t\t\tawait Promise.all(part.rels.map(rel => this.loadRelationshipPart(resolvePath(rel.target, folder), rel.type)));\r\n\t\t}\r\n\r\n\t\treturn part;\r\n\t}\r\n\r\n\tasync loadDocumentImage(id: string, part?: Part): Promise<string> {\r\n\t\tconst x = await this.loadResource(part ?? this.documentPart, id, \"blob\");\r\n\t\treturn this.blobToURL(x);\r\n\t}\r\n\r\n\tasync loadNumberingImage(id: string): Promise<string> {\r\n\t\tconst x = await this.loadResource(this.numberingPart, id, \"blob\");\r\n\t\treturn this.blobToURL(x);\r\n\t}\r\n\r\n\tasync loadFont(id: string, key: string): Promise<string> {\r\n\t\tconst x = await this.loadResource(this.fontTablePart, id, \"uint8array\");\r\n\t\treturn x ? this.blobToURL(new Blob([deobfuscate(x, key)])) : x;\r\n\t}\r\n\r\n\tprivate blobToURL(blob: Blob): string | Promise<string> {\r\n\t\tif (!blob)\r\n\t\t\treturn null;\r\n\r\n\t\tif (this._options.useBase64URL) {\r\n\t\t\treturn blobToBase64(blob);\r\n\t\t}\r\n\r\n\t\treturn URL.createObjectURL(blob);\r\n\t}\r\n\r\n\tfindPartByRelId(id: string, basePart: Part = null) {\r\n\t\tvar rel = (basePart.rels ?? this.rels).find(r => r.id == id);\r\n\t\tconst folder = basePart ? splitPath(basePart.path)[0] : '';\r\n\t\treturn rel ? this.partsMap[resolvePath(rel.target, folder)] : null;\r\n\t}\r\n\r\n\tgetPathById(part: Part, id: string): string {\r\n\t\tconst rel = part.rels.find(x => x.id == id);\r\n\t\tconst [folder] = splitPath(part.path);\r\n\t\treturn rel ? resolvePath(rel.target, folder) : null;\r\n\t}\r\n\r\n\tprivate loadResource(part: Part, id: string, outputType: OutputType) {\r\n\t\tconst path = this.getPathById(part, id);\r\n\t\treturn path ? this._package.load(path, outputType) : Promise.resolve(null);\r\n\t}\r\n}\r\n\r\nexport function deobfuscate(data: Uint8Array, guidKey: string): Uint8Array {\r\n\tconst len = 16;\r\n\tconst trimmed = guidKey.replace(/{|}|-/g, \"\");\r\n\tconst numbers = new Array(len);\r\n\r\n\tfor (let i = 0; i < len; i++)\r\n\t\tnumbers[len - i - 1] = parseInt(trimmed.substr(i * 2, 2), 16);\r\n\r\n\tfor (let i = 0; i < 32; i++)\r\n\t\tdata[i] = data[i] ^ numbers[i % len]\r\n\r\n\treturn data;\r\n}", "module.exports = __WEBPACK_EXTERNAL_MODULE_jszip__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(\"./src/docx-preview.ts\");\n", ""], "names": [], "sourceRoot": ""}