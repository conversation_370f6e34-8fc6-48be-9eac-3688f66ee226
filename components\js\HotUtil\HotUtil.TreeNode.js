/**
 * HotUtil.TreeNode.js - Handsontable工具类 (树节点管理模块)
 *
 * 负责树节点的增删改查、移动和导入等操作。
 */

/**
 * 从文件名中提取密级信息
 * @param {string} fileName - 文件名
 * @returns {{cleanFileName: string, securityKey: string, securityName: string}}
 */
HotUtil.extractSecurityFromFileName = function (fileName) {
    var result = {
        cleanFileName: fileName,
        securityKey: "1", // 默认为内部
        securityName: "内部"
    };

    // 支持的密级括号格式：()、（）、[]、【】
    var patterns = [
        /\(([^)]+)\)/g,     // 英文括号
        /（([^）]+)）/g,      // 中文括号
        /\[([^\]]+)\]/g,    // 英文方括号
        /【([^】]+)】/g       // 中文方括号
    ];

    var securityMap = {};
    // 构建密级映射表
    if (window.securitys) {
        for (var i = 0; i < window.securitys.length; i++) {
            securityMap[window.securitys[i].NAME] = window.securitys[i].KEY;
        }
    }

    var foundSecurity = null;
    var securityText = "";

    // 遍历所有模式查找密级
    for (var p = 0; p < patterns.length; p++) {
        var matches;
        while ((matches = patterns[p].exec(fileName)) !== null) {
            var content = matches[1].trim();
            // 检查是否为已知密级
            if (securityMap.hasOwnProperty(content)) {
                foundSecurity = securityMap[content];
                securityText = content;
                // 从文件名中移除密级标识
                result.cleanFileName = fileName.replace(matches[0], '').trim();
                break;
            }
        }
        if (foundSecurity) break;
    }

    if (foundSecurity !== null) {
        result.securityKey = foundSecurity;
        result.securityName = securityText;
    }

    return result;
};

/**
 * 校验节点名称是否重复
 * @param {string} parentId
 * @param {string} nodeName
 * @param {string} [oldNodeName]
 * @returns {boolean}
 */
HotUtil.checkNodeNameIsRepeat = function (parentId, nodeName, oldNodeName) {
    var flag = false;
    var cb_success = function (res) {
        if (res.success) {
            var ds = res.data;
            for (var i = 0; i < ds.length; i++) {
                var d = ds[i];
                if (nodeName == d.NAME) {
                    flag = true;
                    break;
                }
            }
            if (nodeName == oldNodeName) {
                flag = false;
            }
        } else {
            layer.alert(res.msg);
        }
    };
    twxAjax(THING, 'QueryChildrenByPid', {
        pid: parentId
    }, false, cb_success); //同步请求校验
    return flag;
};

/**
 * 校验同一节点下子节点表序号是否重复
 * @param {string} parentId
 * @param {string} tableNum
 * @param {string} [oldTableNum]
 * @returns {boolean}
 */
HotUtil.checkTableNumIsRepeat = function (parentId, tableNum, oldTableNum) {
    var flag = false;
    var cb_success = function (res) {
        if (res.success) {
            var ds = res.data;
            for (var i = 0; i < ds.length; i++) {
                var d = ds[i];
                if (tableNum == d.TABLE_NUM) {
                    flag = true;
                    break;
                }
            }
            if (tableNum == oldTableNum) {
                flag = false;
            }
        } else {
            layer.alert(res.msg);
        }
    };
    twxAjax(THING, 'QueryChildrenByPid', {
        pid: parentId
    }, false, cb_success); //同步请求校验
    return flag;
};

/**
 * 编辑表格节点
 * @param {Object} treeNode
 * @param {string} pageType
 */
HotUtil.editTableNode = function (treeNode, pageType) {
    var log = {};
    var type = treeNode.TYPE;
    var oldSecurity = treeNode.SECURITY;
    var typeInfo = HotUtil.getTypeInfo(type);
    var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
        level = typeInfo.level,
        isTableNum = typeInfo.isTableNum;
    var nameTpl = '<div class="layui-form-item">\
                                        <label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
                                        <div class="layui-input-block">\
                                            <input type="text" name="newName" value="' + treeNode.NAME +
        '" lay-verify="required" required autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '名称" class="layui-input">\
                                        </div>\
                                    </div>';
    var tableNumTpl = '<div class="layui-form-item">\
                                        <label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '序号:</label>\
                                        <div class="layui-input-block">\
                                            <input type="text" name="newTableNum" value="' + treeNode.TABLE_NUM +
        '" lay-verify="required" required autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '序号" class="layui-input">\
                                        </div>\
                                    </div>';
    var securityOptions = "";
    for (var i = 0; i < securitys.length; i++) {
        if (securitys[i].KEY == oldSecurity) {
            securityOptions += '<option selected value="' + securitys[i].KEY + '">' + securitys[i].NAME +
                '</option>';
        } else {
            securityOptions += '<option value="' + securitys[i].KEY + '">' + securitys[i].NAME +
                '</option>';
        }
    }

    var securityTpl = '<div class="layui-form-item">\
                                        <label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '密级:</label>\
                                        <div class="layui-input-block">\
                                            <select name="security" id="security">' + securityOptions + '</select>\
                                        </div>\
                                    </div>';
    if (!isTableNum) {
        tableNumTpl = "";
        securityTpl = "";
    }

    //日志记录
    log.operation = '编辑' + nodeTypeNameLabel;
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;

    var updateTpl =
        '<form class="layui-form" action="" lay-filter="editNodeForm">\
            ' + tableNumTpl + nameTpl + securityTpl + '\
            <div class="layui-form-item" style="display:none;">\
                <div class="layui-input-block">\
                    <div class="layui-footer">\
                        <button class="layui-btn" id="editNodeSubmit" lay-submit="" lay-filter="editNodeSubmit">确认</button>\
                        <button type="reset" id="editNodeReset" class="layui-btn layui-btn-primary">重置</button>\
                    </div>\
                </div>\
            </div>\
        </form>';
    layer.open({
        title: '编辑' + nodeTypeNameLabel,
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false, //不允许拉伸
        area: ['600px', isTableNum ? '280px' : '170px'],
        content: '<div id="editNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确认', '重置', '关闭'],
        yes: function () {
            $('#editNodeSubmit').click();
        },
        btn2: function () {
            $('#editNodeReset').click();
            return false;
        },
        btn3: function () {
            return true;
        },
        success: function (layero, userLayerIndex, that) {
            $(layero).find('.layui-layer-content').css("overflow", "visible");
            $("#editNodeContent").append(updateTpl);
        }
    });

    form.render(null, 'editNodeForm');
    form.on('submit(editNodeSubmit)', function (data) {

        var parentId = treeNode.PID;
        var param = {};
        if (HotUtil.checkNodeNameIsRepeat(parentId, data.field.newName, treeNode.NAME)) {
            layer.alert("该节点名称已经存在！");
            return false;
        }
        if (isTableNum) {
            if (HotUtil.checkTableNumIsRepeat(parentId, data.field.newTableNum, treeNode
                .TABLE_NUM)) {
                layer.alert("该节点表序号已经存在！");
                return false;
            }
            param.tableNum = data.field.newTableNum || "";
            param.security = data.field.security;
        }
        param.name = data.field.newName;

        param.id = treeNode.ID;
        //是否有锁定编辑的权限
        var allfuns = sessionStorage.getItem('funcids');
        var funcArr = allfuns.split(',');
        if (contains(funcArr, 'func-' + pageType + '-lock-edit')) {
            param.hasLockEdit = "true";
        } else {
            param.hasLockEdit = "false";
        }
        var content = "将节点【" + treeNode.NAME + "（" + treeNode.ID + "）】更改为";
        if (isTableNum) {
            content += "（" + nodeTypeNameLabel + "序号：" + param.tableNum + "，" + nodeTypeNameLabel +
                "名称：" + param.name + "，" + nodeTypeNameLabel + "密级：" + HotUtil.getSecurityName(param
                    .security) + "）";
        } else {
            content += "（" + nodeTypeNameLabel + "名称：" + param.name + "）";
        }
        log.content = content;
        var cb_success = function (res) {
            if (res.success) {
                layer.closeAll();
                layer.msg(res.msg);
                reloadTree(treeNode.PID, treeNode.ID);
                log.reqResult = 1;
            } else {
                log.reqResult = 0;
                layer.alert(res.msg, {
                    icon: 2
                });
            }
            addConfirmLog(log);
        };
        var cb_error = function () {
            layer.closeAll();
            layer.msg('修改失败!');
        };
        twxAjax(THING, 'UpdateTableNode', param, true, cb_success, cb_error);
        return false;
    });
};

/**
 * 删除表格节点
 * @param {Object} treeNode
 */
HotUtil.deleteTableNode = function (treeNode) {
    var log = {};
    //日志记录
    log.operation = "删除节点";
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;
    log.content = "删除节点【" + treeNode.NAME + "（" + treeNode.ID + "）】";
    var msg = "确认删除 节点 -- " + treeNode.NAME + " 吗？";
    if (treeNode.ISPARENT) {
        msg = "该节点下有子节点,确认删除吗?"
    }
    layer.confirm(msg, {
        icon: 3,
        title: '提示'
    }, function (index) {
        var cb_success = function (res) {
            if (res.success) {
                layer.closeAll();
                layer.msg(res.msg);
                reloadTree(treeNode.PID);
                log.reqResult = 1;
            } else {
                log.reqResult = 0;
                layer.alert(res.msg, {
                    icon: 2
                });
            }
            addConfirmLog(log);
        };
        var cb_error = function () {
            layer.msg('删除失败！');
        }
        twxAjax(THING, 'DeleteNode', {
            id: treeNode.ID
        }, true, cb_success, cb_error);
    });
};

/**
 * 导入表格节点
 * @param {Object} treeNode
 * @param {string} type
 */
HotUtil.importTableNode = function (treeNode, type) {
    if (device.ie && device.ie < 10) {
        layer.alert("请使用chrome浏览器！", {
            icon: 2
        });
    } else {
        var nodeTypeNameLabel, level;
        var typeInfo = HotUtil.getTypeInfo(type);
        var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
            level = typeInfo.level,
            isTableNum = typeInfo.isTableNum;
        //日志记录
        var log = {};
        log.operation = "导入" + nodeTypeNameLabel;
        log.tablePid = treeNode.PID;
        log.tableId = treeNode.ID;

        var fileFlag = false;
        var uploadInst;
        layer.open({
            title: "导入" + nodeTypeNameLabel,
            type: 1,
            area: ['460px', "390px"],
            content: '<div id="importTableContent" style="padding: 15px 0px 0px 0px;"></div>',
            anim: false,
            openDuration: 200,
            isOutAnim: false,
            closeDuration: 200,
            resize: false,
            btn: ['确定', '取消'],
            yes: function () {
                if (!fileFlag) {
                    layer.alert('请选择需要导入的excel文件!', {
                        icon: 2
                    });
                    return false;
                }
                var tableName = $("#b-tableName").val();

                if (HotUtil.checkNodeNameIsRepeat(treeNode.ID, tableName)) {
                    layer.alert("该" + nodeTypeNameLabel + "名称已经存在！", {
                        icon: 2
                    });
                    return false;
                }

                var tableNum = $("#b-tableNum").val();
                if (tableNum == "") {
                    layer.alert('请输入' + nodeTypeNameLabel + '的序号!', {
                        icon: 2
                    });
                    return false;
                }

                if (HotUtil.checkTableNumIsRepeat(treeNode.ID, tableNum)) {
                    layer.alert("该" + nodeTypeNameLabel + "序号已经存在！", {
                        icon: 2
                    });
                    return false;
                }
                var security = $("#b-security").val() || "1"; // 获取选择的密级，默认为内部
                uploadInst.config.url = fileHandlerUrl + '/online/import/table?thing=' +
                    THING + '&pid=' + treeNode.ID + '&treeId=' + treeNode.TREE_ID + '&type=' +
                    type + '&level=' + level + '&tableName=' + tableName + '&tableNum=' +
                    tableNum + '&security=' + security + '&saveUser=' + sessionStorage.getItem("username");
                // 获取密级名称用于日志
                var securityName = "内部"; // 默认值
                if (window.securitys) {
                    for (var i = 0; i < window.securitys.length; i++) {
                        if (window.securitys[i].KEY === security) {
                            securityName = window.securitys[i].NAME;
                            break;
                        }
                    }
                }
                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" +
                    "导入了" + nodeTypeNameLabel + "，具体内容为（" + nodeTypeNameLabel +
                    "序号：" + tableNum + "，" + nodeTypeNameLabel +
                    "名称：" + tableName + "，" + nodeTypeNameLabel + "密级：" + securityName + "）";
                $('#uploadStart').click();
            },
            btn2: function () {
                return true;
            },
            success: function () {
                // 构建密级下拉选项
                var securityOptions = "";
                for (var i = 0; i < securitys.length; i++) {
                    // 默认选中"内部"密级
                    if (securitys[i].KEY == "1") {
                        securityOptions += '<option selected value="' + securitys[i].KEY + '">' + securitys[i].NAME + '</option>';
                    } else {
                        securityOptions += '<option value="' + securitys[i].KEY + '">' + securitys[i].NAME + '</option>';
                    }
                }

                var tpl = '<form class="layui-form" lay-filter="importTableForm">\
                                <div class="layui-form-item layui-hide">\
                                    <label class="fieldlabel layui-form-label">' + nodeTypeNameLabel +
                    '序号:</label>\
                                    <div class="layui-input-block">\
                                        <input type="text" name="tableNum" id="b-tableNum" lay-verify="required" autocomplete="off" placeholder="请输入' +
                    nodeTypeNameLabel + '序号" style="width:330px" class="layui-input">\
                                    </div>\
                                </div>\
                                <div class="layui-form-item layui-hide">\
                                    <label class="fieldlabel layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
                                    <div class="layui-input-block">\
                                        <input type="text" name="name" id="b-tableName" lay-verify="required" autocomplete="off" placeholder="请输入' +
                    nodeTypeNameLabel + '名称" style="width:330px" class="layui-input">\
                                    </div>\
                                </div>\
                                <div class="layui-form-item layui-hide">\
                                    <label class="fieldlabel layui-form-label">' + nodeTypeNameLabel + '密级:</label>\
                                    <div class="layui-input-block" style="width:330px">\
                                        <select name="security" id="b-security">' + securityOptions + '</select>\
                                    </div>\
                                </div>\
                                <div class="layui-form-item">\
                                    <label class="fieldlabel layui-form-label">文件内容:</label>\
                                    <div class="layui-input-block">\
                                        <div class="layui-upload">\
                                            <button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>\
                                            <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
                                        </div>\
                                    </div>\
                                </div>\
                                <div class="layui-form-item" id="selectedFile" style="display: none;">\
                                    <label class="fieldlabel layui-form-label">已选文件:</label>\
                                    <div class="layui-input-block">\
                                        <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
                                    </div>\
                                </div>\
                                <div class="layui-form-item" style="display:none;">\
                                    <center>\
                                        <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
                                        <button id="btn_cancel" class="layui-btn">取消</button>\
                                    </center>\
                                </div>\
                            </form>'
                $("#importTableContent").append(tpl);
                form.render(null, 'importTableForm');
                uploadInst = upload.render({
                    elem: '#uploadChoice',
                    url: fileHandlerUrl + '/online/import/table',
                    auto: false,
                    exts: 'xls|xlsx',
                    field: 'uploadFile',
                    bindAction: '#uploadStart',
                    dataType: "json",
                    choose: function (obj) {
                        fileFlag = true;
                        var files = obj.pushFile();
                        obj.preview(function (index, file, result) {
                            var filename = file.name;
                            $("#selectedFile").show();
                            $("#selectedFileName").text(filename);
                            filename = filename.replace("：", ":");

                            // 提取表序号（与批量导入逻辑一致）
                            var tableNum = "";
                            if (filename.indexOf(":") > -1) {
                                tableNum = filename.split(":")[0];
                                filename = filename.split(":")[1];
                            }

                            // 提取密级信息
                            var securityInfo = HotUtil.extractSecurityFromFileName(filename);
                            var cleanFileName = securityInfo.cleanFileName;

                            if (cleanFileName.indexOf(".") > -1) {
                                cleanFileName = cleanFileName.split(".")[0];
                            }

                            // 设置提取的表序号
                            $("#b-tableNum").val(tableNum);

                            // 设置净化后的文件名
                            $("#b-tableName").val(cleanFileName);
                            $("#b-tableName").parent().parent().removeClass("layui-hide");
                            $("#b-tableNum").parent().parent().removeClass("layui-hide");
                            $("#b-security").parent().parent().removeClass("layui-hide");


                            // 根据提取的密级自动选择下拉框选项
                            $("#b-security").val(securityInfo.securityKey);
                            form.render('select', 'importTableForm'); // 重新渲染select组件
                        });
                    },
                    before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                        layer.load(); //上传loading
                    },
                    done: function (res, index, upload) {
                        layer.closeAll();
                        if (res.success) {
                            log.reqResult = 1;
                            layer.msg("导入成功");
                            reloadTree(treeNode.ID, res.data.id);
                        } else {
                            log.reqResult = 0;
                            layer.alert(res.msg, {
                                icon: 2
                            });
                        }
                        addConfirmLog(log);
                    }
                });
            }
        });
    }
};

/**
 * 获取节点类型信息
 * @param {string} type
 * @returns {{isTableNum: boolean, level: number, nodeTypeNameLabel: string}}
 */
HotUtil.getTypeInfo = function (type) {
    var nodeTypeNameLabel = '节点',
        level;
    var isTableNum = true;
    if (type == "a") {
        level = 3;
        nodeTypeNameLabel = 'A表';
    } else if (type == "b") {
        level = 4;
        nodeTypeNameLabel = 'B表';
    } else if (type == "model") {
        level = 1;
        nodeTypeNameLabel = "型号";
        isTableNum = false;
    } else if (type == "project") {
        level = 2;
        nodeTypeNameLabel = "项目";
        isTableNum = false;
    } else if (type == "folder") {
        level = 0;
        nodeTypeNameLabel = "分类";
        isTableNum = false;
    } else if (type == 'report') {
        level = 6;
    } else if (type == 'table') {
        level = 7;
    } else if (type == 'table_1') {
        level = 8;
    } else if (type == 'table_2') {
        level = 9;
    } else if (type == 'table_3') {
        level = 10;
    }
    return {
        isTableNum: isTableNum,
        level: level,
        nodeTypeNameLabel: nodeTypeNameLabel
    };
};

/**
 * 添加表格节点
 * @param {Object} treeNode
 * @param {string} type
 * @param {boolean} isCopy 是否为复制节点
 */
HotUtil.addTableNode = function (treeNode, type, isCopy) {
    var typeInfo = HotUtil.getTypeInfo(type);
    var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
        level = typeInfo.level,
        isTableNum = typeInfo.isTableNum;
    var optText = isCopy ? '复制' : '添加';
    var optTitle = optText + nodeTypeNameLabel;
    //日志记录
    var log = {};
    log.operation = optTitle;
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;

    var securityOptions = "";
    for (var i = 0; i < securitys.length; i++) {
        securityOptions += '<option value="' + securitys[i].KEY + '">' + securitys[i].NAME + '</option>';
    }

    var addTpl =
        '<form class="layui-form" action="" lay-filter="add-node-form">\
            <div class="layui-form-item ' + (isTableNum ? '' : 'layui-hide') + '">\
                <label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '序号:</label>\
                <div class="layui-input-block">\
                    <input type="text" name="tableNum" lay-verify="' + (isTableNum ? 'required' : '') +
        '" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '序号"  class="layui-input">\
                </div>\
            </div>\
            <div class="layui-form-item">\
                <label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
                <div class="layui-input-block">\
                    <input type="text" name="name" lay-verify="required" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '名称" class="layui-input">\
                </div>\
            </div>\
            <div class="layui-form-item ' + (isTableNum ? '' : 'layui-hide') + '">\
                <label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '密级:</label>\
                <div class="layui-input-block">\
                    <select name="security" id="security">' + securityOptions + '</select>\
                </div>\
            </div>\
            <div class="layui-form-item" style="display:none;">\
                <div class="layui-input-block">\
                    <div class="layui-footer">\
                        <button class="layui-btn" id="addNodeSubmit" lay-submit="" lay-filter="submit-node">确认</button>\
                        <button type="reset" id="addNodeReset" class="layui-btn layui-btn-primary">重置</button>\
                    </div>\
                </div>\
            </div>\
        </form>';
    layer.open({
        title: optTitle,
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false, //不允许拉伸
        area: ['500px', isTableNum ? '280px' : '170px'],
        content: '<div id="addTableNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确认', '重置', '取消'],
        yes: function () {
            $('#addNodeSubmit').click();
        },
        btn2: function () {
            $('#addNodeReset').click();
            return false;
        },
        btn3: function () {
            return true;
        },
        success: function (layero, userLayerIndex, that) {
            $(layero).find('.layui-layer-content').css("overflow", "visible");
            $("#addTableNodeContent").append(addTpl);
        }
    });
    form.render(null, 'add-node-form');

    form.on('submit(submit-node)', function (formData) {
        var param = {};
        var parentId = isCopy ? treeNode.PID : treeNode.ID;
        if (HotUtil.checkNodeNameIsRepeat(parentId, formData.field.name)) {
            layer.alert("该" + nodeTypeNameLabel + "名称已经存在！", {
                icon: 2
            });
            return false;
        }
        if (isTableNum) {
            if (HotUtil.checkTableNumIsRepeat(parentId, formData.field.tableNum)) {
                layer.alert("该" + nodeTypeNameLabel + "序号已经存在！", {
                    icon: 2
                });
                return false;
            }
        }

        param.name = formData.field.name;
        param.tableNum = formData.field.tableNum || '';
        param.security = formData.field.security;
        param.creator = sessionStorage.getItem('username');
        if (isCopy) {
            param.id = treeNode.ID;
            param.pid = treeNode.PID;
            param.processTreeId = treeNode.TREE_ID;
        } else {
            param.pid = treeNode.ID;
            param.treeId = treeNode.TREE_ID;
            param.type = type;
            param.level = level;
        }
        var content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" + optText + "了" +
            nodeTypeNameLabel;
        if (isTableNum) {
            content += "，具体内容为（" + nodeTypeNameLabel + "序号：" + param.tableNum +
                "，" + nodeTypeNameLabel + "名称：" + param.name +
                "，" + nodeTypeNameLabel + "密级：" + HotUtil.getSecurityName(param.security) + "）";
        } else {
            content += "，具体内容为（" + nodeTypeNameLabel + "名称：" + param.name + "）";
        }
        log.content = content;
        layer.load();
        var cb_success = function (res) {
            if (res.success) {
                layer.closeAll();
                layer.msg(res.msg);
                reloadTree(parentId, res.data.id);
                log.reqResult = 1;
            } else {
                log.reqResult = 0;
                layer.alert(res.msg, {
                    icon: 2
                });
            }
            addConfirmLog(log);
        };
        var cb_error = function (data) {
            layer.alert('添加失败，请联系管理员！', {
                icon: 2
            });
        };
        twxAjax(THING, isCopy ? 'CopyNode' : 'AddTableNode', param, true, cb_success, cb_error);
        return false;
    });
};

/**
 * 移动节点
 * @param {string} thing
 * @param {Object} treeNode
 */
HotUtil.moveNode = function (thing, treeNode) {
    var parentNode = treeNode.getParentNode();
    var moveZtreeObj;
    layer.open({
        title: "移动节点",
        type: 1,
        fixed: false,
        maxmin: false,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        resize: false,
        area: ['500px', '600px'],
        content: '<div id="moveNodeContent" style="padding-top: 15px;padding-right: 15px;"><ul id="moveTree" class="ztree"></ul></div>',
        btn: ['确定', '取消'],
        yes: function () {
            var checkedNodes = moveZtreeObj.getCheckedNodes(true);
            if (checkedNodes.length == 0) {
                layer.alert("请选择目标节点！", {
                    icon: 2
                });
            } else {
                var targetNode = checkedNodes[0];
                var targetId = targetNode['ID'];
                var targetTreeId = targetNode['TREE_ID'];
                var log = {};
                log.operation = '移动节点';
                log.tablePid = treeNode['PID'];
                log.tableId = treeNode['ID'];
                log.content = "将节点【" + treeNode['NAME'] + "（" + treeNode['ID'] +
                    "）】移动到节点【" + targetNode['NAME'] + "（" + targetId + "）】下";
                twxAjax(THING, 'MoveNode', {
                    id: treeNode['ID'],
                    targetId: targetId,
                    targetTreeId: targetTreeId
                }, true, function (res) {
                    if (res.success) {
                        log.reqResult = 1;
                        layer.closeAll();
                        ztreeObj.reAsyncChildNodes(parentNode.getParentNode(),
                            'refresh', false,
                            function () {
                                var nodeByParam = ztreeObj.getNodeByParam("ID",
                                    targetNode.getParentNode()['ID'], null);
                                if (nodeByParam !== null) {
                                    ztreeObj.reAsyncChildNodes(nodeByParam,
                                        'refresh', false,
                                        function () {
                                            loadTreeMenu();
                                        });
                                }
                            });
                    } else {
                        log.reqResult = 0;
                    }
                    addConfirmLog(log);
                });
            }
        },
        btn2: function () {
            return true;
        },
        success: function (layero, index, that) {
            var cb_success = function (res) {
                if (res.success) {
                    var datas = res.data;
                    if (datas.length > 0) {
                        datas = dealMoveTreeData(datas, parentNode);
                        treeSetting.check = {
                            enable: true,
                            chkStyle: "radio",
                            radioType: "all"
                        };
                        treeSetting.async.dataFilter = function (treeId, parentNode1,
                            responseData) {
                            if (responseData.success) {
                                var datas = responseData.data;
                                if (datas.length > 0) {
                                    datas = dealMoveTreeData(datas, parentNode);
                                }
                                return datas;
                            } else {
                                layer.alert(responseData.msg, {
                                    icon: 2
                                });
                            }
                        };
                        treeSetting.edit.enable = false;
                        treeSetting.callback.onExpand = function (event, treeId,
                            treeNode) { };

                        treeSetting.callback.onClick = function (event, treeId,
                            treeNode) { };
                        moveZtreeObj = $.fn.zTree.init($("#moveTree"), treeSetting,
                            datas);
                        var node = moveZtreeObj.getNodeByParam("LEVEL_NUM", 0, null);
                        moveZtreeObj.expandNode(node, true, false, true);
                    }
                } else {
                    layer.alert(res.msg);
                }
            };
            //使用ajax进行异步加载Tree
            twxAjax(thing, 'QueryTreeRoot', {
                username: sessionStorage.getItem('username')
            }, true, cb_success);
        }
    });
};

/**
 * 批量导入Excel节点
 * @param {Object} treeNode
 * @param {string} type
 */
HotUtil.batchImportExcel = function (treeNode, type) {
    var typeInfo = HotUtil.getTypeInfo(type);
    var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
        level = typeInfo.level;
    var domHtml = `<div style="padding:12px;">
                    <!-- 密级下拉框模板 -->
                    <script type="text/html" id="TPL-security-dropdown">
                        <button class="layui-btn layui-btn-primary security-dropdown">
                            <span>{{= d.securityName || '内部' }}</span>
                            <i class="layui-icon layui-icon-down layui-font-12"></i>
                        </button>
                    </script>
                    
                    <div class="layui-form" style="margin-bottom: 10px;">
                        <div class="layui-form-item" style="margin-bottom:0px;">
                            <div class="layui-inline" style="margin-bottom:0px;">
                                <div class="layui-upload" style="margin-bottom:2px;">
                                    <div id="chooseFile">选择文件</div>
                                    <button type="button" class="layui-btn" id="manyUploadStart" style="display: none;">开始上传</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <table id="file-table" lay-filter="file-table"></table>
                </div>`;
    layer.open({
        title: '批量导入' + nodeTypeNameLabel,
        type: 1,
        area: ['1100px', '570px'],
        content: domHtml,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        resize: false,
        btn: ['上传', '关闭'],
        yes: function () {
            $("#manyUploadStart").click();
        },
        btn2: function () {
            table.cache["file-table"] = [];
            return true;
        },
        success: function () {

            var fileTable = table.render({
                elem: '#file-table',
                data: [],
                height: 375,
                limit: 999,
                css: [ // 设置单元格样式
                    // 取消默认的溢出隐藏，并设置适当高度
                    '.layui-table-cell{height: 50px; line-height: 40px;}',
                    '.layui-table-cell .layui-colorpicker{width: 38px; height: 38px;}',
                    '.layui-table-cell select{height: 36px; padding: 0 5px;}'
                ].join(''),
                cols: [
                    [{
                        field: '',
                        type: 'numbers',
                        width: 80
                    }, {
                        field: 'tableNum',
                        title: nodeTypeNameLabel + '序号',
                        templet: '<div>{{d.tableNum}}</div>',
                        edit: 'text',
                        width: 150
                    }, {
                        field: 'tableName',
                        title: nodeTypeNameLabel + '名称',
                        templet: '<div>{{d.tableName}}</div>',
                        edit: 'text',
                        width: 250
                    }, {
                        field: 'security',
                        title: '密级',
                        templet: '#TPL-security-dropdown',
                        unresize: true,
                        align: 'center',
                        width: 150
                    }, {
                        field: 'fileSize',
                        title: '文件大小',
                        templet: '<div>{{d.fileSize}}</div>',
                        width: 100
                    }, {
                        field: 'operate',
                        title: '操作(双击)',
                        width: 100,
                        templet: `<div class="operate">
                                            <a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>
                                          </div>`
                    }]
                ],
                done: function (res, curr, count) {
                    var options = this;

                    // 获取当前行数据 - 自定义方法
                    table.getRowData = function (tableId, elem) {
                        var index = $(elem).closest('tr').data('index');
                        return table.cache[tableId][index] || {};
                    };

                    // 构建密级下拉数据
                    var securityDropdownData = [];
                    if (window.securitys) {
                        for (var i = 0; i < window.securitys.length; i++) {
                            securityDropdownData.push({
                                title: window.securitys[i].NAME,
                                id: window.securitys[i].KEY
                            });
                        }
                    }

                    // dropdown 方式的密级选择
                    dropdown.render({
                        elem: '.security-dropdown',
                        data: securityDropdownData,
                        click: function (obj) {
                            var data = table.getRowData(options.id, this.elem); // 获取当前行数据

                            // 更新按钮显示文本
                            this.elem.find('span').html(obj.title);

                            // 更新数据中对应的字段
                            data.security = obj.id;
                            data.securityName = obj.title;

                            console.log('密级已更新:', {
                                securityKey: obj.id,
                                securityName: obj.title
                            });
                        }
                    });
                }
            });

            table.on('toolDouble(file-table)', function (obj) {
                var data = obj.data; // 得到当前行数据
                var layEvent = obj.event; // 获得元素对应的 lay-event 属性值
                if (layEvent === 'del') { //删除
                    layer.confirm('确定删除吗？', function (index) {
                        obj.del();
                        uploader.removeFile(data.file);
                        layer.close(index);
                    });
                }
            });

            var doneDatas = [];
            var uploader = WebUploader.create({
                // 选完文件后，是否自动上传。
                auto: false,
                // 文件接收服务端。
                server: fileHandlerUrl + '/online/batch/import/table',
                // 选择文件的按钮。可选。
                pick: {
                    id: '#chooseFile',
                    multiple: true
                },
                accept: {
                    title: 'Excel文件',
                    extensions: 'xlsx',
                    mimeTypes: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                timeout: 10 * 60 * 1000,
                threads: 1,
                // 配置分片上传
                formData: {
                    extraData: JSON.stringify({})
                }
            });
            uploader.on('uploadBeforeSend', function (object, data, headers) {
                var uid = object.blob.uid;
                var tableData = table.cache["file-table"];
                var tableNum = "",
                    tableName = "",
                    security = "1"; // 默认密级为内部
                for (var i = 0; i < tableData.length; i++) {
                    if (tableData[i].uid == uid) {
                        tableNum = tableData[i].tableNum;
                        tableName = tableData[i].tableName;
                        security = tableData[i].security || "1";
                        break;
                    }
                }
                data.extraData = JSON.stringify({
                    pid: treeNode['ID'],
                    treeId: treeNode['TREE_ID'],
                    thing: THING,
                    type: type,
                    level: level,
                    tableNum: tableNum,
                    tableName: tableName,
                    security: security,
                    saveUser: sessionStorage.getItem("username")
                });
            });


            // 当有文件被添加进队列之前触发
            uploader.on('beforeFileQueued', function (file) {

            });
            // 当有文件被添加进队列的时候
            uploader.on('fileQueued', function (file) {
                var fileName = file.name;
                var o = {};
                fileName = fileName.replace("：", ":");
                var tableNum = "";
                if (fileName.indexOf(":") > -1) {
                    tableNum = fileName.split(":")[0];
                    fileName = fileName.split(":")[1];
                }

                // 提取密级信息
                var securityInfo = HotUtil.extractSecurityFromFileName(fileName);
                var cleanFileName = securityInfo.cleanFileName;

                if (cleanFileName.indexOf(".") > -1) {
                    cleanFileName = cleanFileName.split(".")[0];
                }

                o.tableNum = tableNum;
                o.tableName = cleanFileName;
                o.security = securityInfo.securityKey;
                o.securityName = securityInfo.securityName;
                o.fileSize = WebUploader.formatSize(file.size);
                o.uid = file.source.uid;
                o.file = file;
                table.cache["file-table"].push(o);
                table.renderData('file-table');

                // 重新渲染dropdown组件以确保新增行的dropdown能够正常工作
                setTimeout(function () {
                    // 构建密级下拉数据
                    var securityDropdownData = [];
                    if (window.securitys) {
                        for (var i = 0; i < window.securitys.length; i++) {
                            securityDropdownData.push({
                                title: window.securitys[i].NAME,
                                id: window.securitys[i].KEY
                            });
                        }
                    }

                    // 为新增的dropdown重新绑定事件
                    dropdown.render({
                        elem: '.security-dropdown',
                        data: securityDropdownData,
                        click: function (obj) {
                            var data = table.getRowData('file-table', this.elem);

                            // 更新按钮显示文本
                            this.elem.find('span').html(obj.title);

                            // 更新数据中对应的字段
                            data.security = obj.id;
                            data.securityName = obj.title;

                            console.log('密级已更新:', {
                                securityKey: obj.id,
                                securityName: obj.title
                            });
                        }
                    });
                }, 100);
            });
            uploader.on('filesQueued', function (files) {

            });
            uploader.on('uploadProgress', function (file, percentage) {

            })


            uploader.on('uploadSuccess', function (file, res) {
                console.log(res);
            });

            // 文件上传失败，显示上传出错。
            uploader.on('uploadError', function (file) {

            });

            // 完成上传完毕，成功或者失败，先删除进度条。
            uploader.on('uploadComplete', function (file) {

            });

            // 当所有文件上传结束时触发
            uploader.on('uploadFinished', function () {
                reloadTree(treeNode.ID, treeNode.ID);
                //提示完成后，点击确定再刷新界面
                layer.closeAll();
                table.cache["file-table"] = [];
                layer.msg('导入成功');
            });

            $("#manyUploadStart").on('click', function () {
                var tableData = table.cache["file-table"];
                if (tableData.length > 0) {
                    var hasEmpty = false;
                    for (var i = 0; i < tableData.length; i++) {
                        if (tableData[i].tableNum == '' || tableData[i].tableName ==
                            '') {
                            hasEmpty = true;
                            break;
                        }
                    }
                    if (hasEmpty) {
                        layer.alert('表序号和表名称都不能为空！', {
                            icon: 2
                        });
                    } else {
                        layer.load();
                        uploader.upload(); // 手动触发上传操作
                    }
                } else {
                    layer.alert('请选择需要上传的Excel文件！', {
                        icon: 2
                    });
                }
            });
        }
    });
};

/**
 * 测试密级提取功能（开发阶段使用）
 * 可以在浏览器控制台中调用 HotUtil.testSecurityExtraction() 来测试
 */
HotUtil.testSecurityExtraction = function () {
    var testCases = [
        "文件a(内部).xlsx",
        "(秘密)重要文档.docx",
        "报告[机密]2024.pdf",
        "普通文件.xlsx",
        "测试（公开）数据.xls",
        "多个(内部)括号(秘密).xlsx",
        "【机密】重要报告.docx",
        "文件(未知密级).xlsx"
    ];

    console.log("=== 密级提取功能测试 ===");
    for (var i = 0; i < testCases.length; i++) {
        var result = HotUtil.extractSecurityFromFileName(testCases[i]);
        console.log("原文件名: " + testCases[i]);
        console.log("净化文件名: " + result.cleanFileName);
        console.log("密级KEY: " + result.securityKey);
        console.log("密级名称: " + result.securityName);
        console.log("---");
    }
};

/**
 * 更新B表序号
 * @param {Object} treeNode A表节点对象
 */
HotUtil.updateBTableNumbers = function (treeNode) {
    // 记录日志
    var log = {};
    log.operation = "更新B表序号";
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;
    log.content = "更新A表节点【" + treeNode.NAME + "（" + treeNode.ID + "）】下属B表记录的序号";

    // 确认操作
    layer.confirm('确定要更新当前A表下所有B表的序号吗？<br><br>操作说明：<br>1. 将A表序号中的字母"a"替换为"b"（保持大小写）<br>2. 在转换后的序号末尾添加"-{顺序号}"<br>3. 顺序号从1开始依次递增', {
        btn: ['确定', '取消'],
        title: '更新B表序号确认'
    }, function () {
        // 显示进度提示
        var loading = layer.msg("正在更新B表序号，请稍候...", {
            icon: 16,
            shade: 0.3,
            time: 0
        });

        // 调用后端服务更新B表序号
        twxAjax("Thing.Util.HandsonTable", "UpdateBTableNumbers", {
            aTableId: treeNode.ID,
            username: sessionStorage.getItem("username"),
            thingName: THING
        }, true, function (res) {
            layer.close(loading);
            if (res.success) {
                log.reqResult = 1;
                addConfirmLog(log);
                layer.msg(res.msg || '更新成功');
                // 刷新当前节点显示
                reloadTree(treeNode.ID);
            } else {
                log.reqResult = 0;
                addConfirmLog(log);
                layer.alert(res.msg || '更新失败', {
                    icon: 2
                });
            }
        }, function () {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.alert('更新B表序号失败', {
                icon: 2
            });
        });
    });
}
