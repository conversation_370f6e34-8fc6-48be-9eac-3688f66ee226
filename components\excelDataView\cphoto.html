<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">

		<link rel="Shortcut Icon" href="../../img/favicon.ico">
		<link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">

		<link rel="stylesheet" href="../../css/icon.css">

		<script src="../../plugins/layui/layui.js"></script>

		<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
		<script src="../../plugins/easyui/jquery.min.js"></script>
		<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
		<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
		
		<script src="../js/config/twxconfig.js"></script>
		<script src="../js/util.js"></script>
		
		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">
		<script type="text/javascript" src="../../components/dataTree/bom_tree.js"></script>
		<script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
		<script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
		
		<script type="text/javascript" src="../js/intercept.js"></script>
		<script type="text/javascript" src="../js/logUtil.js"></script>

		<title>采集管理页面查看影像记录</title>
		<style>
			
			/* 弹窗不加载滚动条 */
			.layui-layer-page .layui-layer-content{
				overflow: visible !important;
			}
			.datalinklabel{
				width: 90px;
			}
			
			.tablelabeltd{
				width: 80px;
				text-align: right;
				padding: 5px;
			}
			
			.layui-btn-mycolor3 {
				background-color: #CCDB75;
			}
			
			.layui-btn-mycolor4 {
				background-color: #FBA0A0;
			}
    </style>
	</head>
	<body>
		<div class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
			<div data-options="region:'center',border:false">
				<div id="cphoto_table_tb" style="padding: 5px;">
					<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="cphoto_stateCheck">
						<i class="layui-icon">&#xe672;</i> 状态确认
					</button>
					<button type="button" class="layui-btn layui-btn-sm" id="cphoto_datapkgLink">
						<i class="layui-icon">&#xe64c;</i> 数据包关联
					</button>
					<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3" id="cphoto_procuct_link">
						<i class="layui-icon">&#xe64c;</i> 产品结构树关联
					</button>
					<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4" id="cphoto_param">
						<i class="layui-icon">&#xe642;</i> 编写属性
					</button>
					<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="cphoto_delete">
						<i class="layui-icon">&#xe63c;</i> 删除
					</button>
				</div>
				<div id="dataTable"></div>
			</div>
		</div>
	</body>
</html>

<script src="cphoto.js"></script>

<script type="text/html" id="datapkglink">
	<form class="layui-form" style="">
        <div class="layui-form-item">
			<label class="layui-form-label datalinklabel">产品型号:</label>
			<div class="layui-input-block" >
				<select name="model" lay-filter="model" lay-verify="required" lay-search id="model" >
					<option value=""></option>
				</select>
			</div>
        </div>
		<div class="layui-form-item">
			<label class="layui-form-label datalinklabel">阶段:</label>
			<div class="layui-input-block" >
				<select name="phase" lay-filter="phase" lay-verify="required" id="phase" >
					<option value=""></option>
				</select>
			</div>
		</div>
        <div class="layui-form-item">
			<label class="layui-form-label datalinklabel">数据包:</label>
			<div class="layui-input-block">
				<select name="datapkgname" lay-filter="datapkgname" lay-search lay-verify="required" id="datapkgname" >
					<option value=""></option>
				</select>
			</div>
        </div>
        <div class="layui-form-item">
			<label class="layui-form-label datalinklabel">清单类别:</label>
			<div class="layui-input-block">
				<select name="typelist" lay-filter="typelist" lay-verify="required" id="typelist" >
					<option value=""></option>
					<option value="DESIGN_DATA_LIST">设计</option>
					<option value="CRAFT_DATA_LIST">工艺</option>
					<option value="PROCESS_CONTROL_LIST">过程控制</option>
					<option value="QUALITY_CONTROL_LIST">质量综合</option>
				</select>
			</div>
        </div>
        <div class="layui-form-item">
			<label class="layui-form-label datalinklabel">文件类型:</label>
			<div class="layui-input-block">
				<select name="filetype" lay-filter="filetype" lay-verify="required" id="filetype">
					<option value=""></option>
				</select>
			</div>
        </div>
    
        <div class="layui-form-item" style="display: none;">
            <center>
                <button id="btn_add" class="layui-btn" lay-submit lay-filter="addData" >提交</button>
                <button id="btn_reset" class="layui-btn layui-btn-primary" type="reset" >重置</button>
            </center>
        </div>
    </form>
</script>
