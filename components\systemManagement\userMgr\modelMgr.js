function ModelMgr() {

	var sysMgrThingName = 'Thing.Fn.SystemManagement';

	this.initUGModelComp = function() {
		$('#modelTable').datagrid({
			data: [],
			singleSelect: false,
			striped: true,
			rownumbers: true,
			toolbar: '#modelTable_tb',
			fit: true,
			columns: [
				[{
					field: 'ck',
					checkbox: true
				}, {
					field: 'ID',
					title: '型号ID',
					hidden: true
				}, {
					field: 'NAME',
					title: '型号'
				}]
			],
			emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
			loadMsg: '正在加载数据...'
		});
	};

	//获取选择的用户
	var getUserSelectedData = function() {
		var sels = $('#userTable').datagrid('getSelections');
		if (sels.length == 0) {
			layer.msg('请选择待操作的用户...', {
				icon: 2,
				anim: 6
			});
			return;
		} else if (sels.length > 1) {
			layer.msg('只能选择一个操作的用户...', {
				icon: 2,
				anim: 6
			});
			return;
		}
		var user = {
			userId: sels[0].USER_ID,
			name: sels[0].USER_NAME,
			fullname: sels[0].USER_FULLNAME
		};
		return user;
	};

	//获取选择的型号
	var getModelSelectedData = function() {
		var sels = $('#modelTable').datagrid('getSelections');
		return sels;
	};

	//加载角色表格数据
	this.initUGModelData = function() {
		$('#modelTable').datagrid('loading');
		var user = getUserSelectedData();
		if (!user) {
			return;
		}
		var param = {};
		param.userId = user.userId;
		var cb_success = function(data) {
			$('#modelTable').datagrid('loadData', data.models);
			$('#modelTable').datagrid('loaded');
		};
		var cb_error = function() {

		};
		twxAjax(sysMgrThingName, 'QueryModelsByUserId', param, true, cb_success, cb_error);
	};

	var getAssignModelSelectedData = function() {
		var sels = $('#assignModelTable').datagrid('getSelections');
		return sels;
	};


	this.initBtnAssign = function(layui) {
		$('#assignmodel').bind('click', function() {
			var layer = layui.layer;
			var table = layui.table;
			var user = getUserSelectedData();
			if (!user) {
				return;
			}
			layer.open({
				title: '分配型号',
				type: 1,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				shadeClose: false,
				// fixed: false,
				maxmin: false,
				resize: false, //不允许拉伸
				area: ['400px', '400px'],
				content: '<div id="addContent" style="padding-top:15px;"></div>',
				btn: ['分配', '关闭'],
				yes: function() {
					var modelSels = getAssignModelSelectedData();
					if (modelSels.length == 0) {
						layer.msg('请选择型号...', {
							icon: 2,
							anim: 6
						});
						return;
					}

					var param = {};
					param.userId = user.userId;
					var modelids = '';
					var logs = ''
					for (var i = 0; i < modelSels.length; i++) {
						modelids += "," + modelSels[i].NAME;
						logs += "," + '(ID：' + modelSels[i].NAME + '、型号名称：' + modelSels[i].NAME + ')'
					}
					logs = logs.substring(1);
					param.modelids = modelids.substring(1);

					var cb_success = function(data) {
						if (data.success === false) {
							layer.msg(data.message, {
								icon: 2,
								anim: 6
							});
							logRecord('编辑', '用户管理-给用户(ID：' + user.userId + '、用户名：' + user.name + '、全名：' + user.fullname +
								')分配了' + modelSels.length + '个型号【' + logs + '】', 0);
							return;
						}

						var rows = [];
						var retModel = data.models ? data.models.split(',') : [];
						retModel.sort();
						retModel.forEach(function(m) {
							rows.push({
								NAME: m,
								ID: m
							});
						});
						$('#modelTable').datagrid('loadData', rows);
						layer.closeAll();
						logRecord('编辑', '用户管理-给用户(ID：' + user.userId + '、用户名：' + user.name + '、全名：' + user.fullname +
							')分配了' + modelSels.length + '个型号【' + logs + '】', 1);
						layer.msg('分配成功');
					};
					var cb_error = function() {};

					twxAjax(sysMgrThingName, 'AddUserModel', param, true, cb_success, cb_error);
				},
				btn2: function() {
					return true;
				},
				success: function() {
					var addTpl = $("#modelSelect")[0].innerHTML;
					$("#addContent").append(addTpl);
					//初始化assignModelTable
					initAssignModelComp();
					initAssignModelData();
					$.parser.parse();
				}
			});
		});
	};

	var initAssignModelComp = function() {
		$('#assignModelTable').datagrid({
			data: [],
			singleSelect: false,
			fitColumns: true,
			striped: true,
			rownumbers: true,
			height: 225,
			// fit: true,
			columns: [
				[{
					field: 'ck',
					checkbox: true
				}, {
					field: 'NAME',
					title: '型号名称',
					width: 250
				}, {
					field: 'ID',
					title: '型号ID',
					hidden: true
				}]
			],
			emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>无可分配的型号...</font></div>',
			loadMsg: '正在加载数据...'
		});
	};

	var getModelids = function() {
		var assignedSels = $('#modelTable').datagrid('getData').rows;
		var modelids = '';
		for (var i = 0; i < assignedSels.length; i++) {
			modelids += ',' + assignedSels[i].NAME;
		}
		modelids = modelids.substring(1);
		return modelids;
	}
	//初始化可用分配的角色
	var initAssignModelData = function() {
		$('#assignModelTable').datagrid('loading');

		twxAjax("Thing.Fn.BOM", 'getModelList', null, true, function(data) {
			if (data.array.length == 0) {
				return;
			}

			var user = getUserSelectedData();
			if (!user) {
				return;
			}
			var param = {};
			param.userId = user.userId;
			twxAjax(sysMgrThingName, 'QueryModelsByUserId', param, true,
				function(assignedList) {
					var rows = [];
					var models = assignedList.models;
					data.array.forEach(function(m) {
						var found = false;
						for (var i = 0; i < models.length; i++) {
							if (models[i].NAME == m.name) {
								found = true;
								break;
							}
						}
						if (found) {
							return;
						}
						rows.push({
							NAME: m.name,
							ID: m.name
						});
					});

					$('#assignModelTable').datagrid('loadData', rows);
					$('#assignModelTable').datagrid('loaded');
					if (rows.length == 0) {
						return;
					}
					$('#modelSearchIpt').on('input propertychange',
						function() { //监听文本框
							var newRows = [];
							var keyword = $('#modelSearchIpt').val();
							if (keyword) {
								rows.forEach(function(m) {
									if (m.NAME.indexOf(keyword) > -1) {
										newRows.push(m);
									}
								});
							} else {
								newRows = rows;
							}
							$('#assignModelTable').datagrid('loadData', newRows);
						});
				});
		});
	};

	this.initBtnRemove = function(layui) {
		$('#removemodel').bind('click', function() {
			var layer = layui.layer;
			var user = getUserSelectedData();
			if (!user) {
				return;
			}
			var sels = getModelSelectedData();
			if (sels.length == 0) {
				layer.msg('请选择需要移除的型号...', {
					icon: 2,
					anim: 6
				});
				return;
			}

			layer.confirm('确认移除该分配吗？', {
				icon: 3,
				title: '提示'
			}, function(index) {
				//调用ajax进行关系的移除
				var param = {};
				param.models = "";
				param.userId = user.userId;
				var logs = '';
				for (var i = 0; i < sels.length; i++) {
					param.models += ',' + sels[i].NAME;
					logs += "," + '(型号名：' + sels[i].NAME + ')'
				}
				logs = logs.substring(1);
				param.models = param.models.substring(1);
				var cb_success = function(data) {
					if (data.success === false) {
						layer.msg(data.message, {
							icon: 2,
							anim: 6
						});
						logRecord('编辑', '用户管理-给用户(ID：' + user.userId + '、用户名：' + user.name + '、全名：' + user.fullname +
							')取消分配了' + sels.length + '个型号【' + logs + '】', 0);
						return;
					}

					var rows = [];
					var retModel = data.models ? data.models.split(',') : [];
					retModel.forEach(function(m) {
						rows.push({
							NAME: m,
							ID: m
						});
					});
					$('#modelTable').datagrid('loadData', rows);

					layer.closeAll();
					logRecord('编辑', '用户管理-给用户(ID：' + user.userId + '、用户名：' + user.name + '、全名：' + user.fullname +
						')取消分配了' + sels.length + '个角色【' + logs + '】', 1);
					layer.msg('移除成功');
				};
				var cb_error = function() {};
				//TODO:DeleteAssignModel
				twxAjax(sysMgrThingName, 'DeleteAssignModel', param, true, cb_success, cb_error);
			});
		});
	};
}

var modelMgr = new ModelMgr();
$(document).ready(function() {
	layui.use(['layer', 'form', 'jquery', 'table'], function() {
		var layer = layui.layer,
			form = layui.form;
		modelMgr.initUGModelComp();
		modelMgr.initBtnAssign(layui);
		modelMgr.initBtnRemove(layui);

	});
});
