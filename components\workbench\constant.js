//表格的分页参数
var page = {
	layout: ['limit', 'count', 'prev', 'page', 'next', 'refresh', 'skip'],
	groups: 1,
	first: false,
	last: false
};
var thingName = 'Thing.Fn.Workbench';
var tableAlign = 'center';
//我的待办表格列
var my_table_col = [
	[{
		field: 'ROWNO',
		width: 60,
		align: tableAlign,
		title: '序号'
	}, {
		field: 'PROC_INST_ID',
		title: '流程编号',
		align: tableAlign,
		width: 88
	}, {
		field: 'PROC_INST_NAME',
		title: '流程名称',
		align: tableAlign,
		width: 180
	}, {
		field: 'PROC_INST_USERNAME',
		title: '发起人',
		align: tableAlign,
		width: 208
	}, {
		field: 'PROC_INST_START_TIME',
		title: '发起时间',
		align: tableAlign,
		width: 200
	}, {
		field: 'TASK_ID',
		width: 88,
		align: tableAlign,
		title: '任务编号'
	}, {
		field: 'TASK_TYPE',
		width: 130,
		align: tableAlign,
		title: '任务类型'
	}, {
		field: 'TASK_NAME',
		align: tableAlign,
		title: '任务名称'
	}, {
		field: 'TASK_USERNAME',
		width: 208,
		align: tableAlign,
		title: '处理人'
	}, {
		field: 'TASK_START_TIME',
		width: 200,
		align: tableAlign,
		title: '任务开始时间'
	}, {
		field: 'operation',
		align: tableAlign,
		title: '操作',
		width: 160,
		templet: function(d) {
			var html = '<div class="layui-clear-space">';
			html += '<a class="layui-btn layui-btn-xs" lay-event="viewDetail">查看详情</a>';
			if (d.TASK_STATUS == '已完成') {
				html += '<a class="layui-btn layui-btn-xs" lay-event="returnTask">退回</a>';
			} else {
				html += '<a class="layui-btn layui-btn-xs" lay-event="finishedTask">完成</a>';
			}
			html += "</div>";
			return html;
		}
	}]
];

var my_table_col1 = [
	[{
		field: 'number',
		width: 80,
		align: tableAlign,
		title: '编号'
	}, {
		field: 'model',
		width: 100,
		align: tableAlign,
		title: '型号'
	}, {
		field: 'phase',
		width: 100,
		align: tableAlign,
		title: '阶段'
	}, {
		field: 'major',
		title: '专业',
		align: tableAlign,
		width: 200
	}, {
		field: 'process',
		title: '过程',
		align: tableAlign,
		width: 200
	}, {
		field: 'q_table_name',
		title: '质量表名称',
		align: tableAlign,
		width: 280
	}, {
		field: 'operation',
		align: tableAlign,
		title: '操作',
		width: 100,
		templet: function(d) {
			return '<span style="cursor: pointer;" class="layui-badge layui-bg-green" onClick="qualityConfirm(' + d.push_id + ')">确认</span>';
			// return '<span style="cursor: pointer;" class="layui-badge layui-bg-blue">查看数据</span><span style="margin-left:20px;cursor: pointer;" class="layui-badge layui-bg-green">确认</span>';
		}
	}, {
		field: 'remark',
		align: tableAlign,
		title: '备注'
	}]
];

function qualityConfirm(pushId) {
	var objHtml = '<object id="DWebSignSeal" style="display:none;" classid="CLSID:77709A87-71F9-41AE-904F-886976F99E3E"\
            codebase="http://www.XXX.com.cn/demo/websign/WebSign.ocx#version=4,4,9,6" width="100" height="100"></object>';
	if ($("body").find("#DWebSignSeal").length == 0) {
		$('body').prepend(objHtml);
	}
	var tpl = '<form class="layui-form" lay-filter="confirmForm">\
				<div class="layui-form-item">\
					<label class="layui-form-label">签名:</label>\
					<div class="layui-input-block">\
						<img id="IMG_SRC" name="IMG_SRC" src="" style="width: 325px;height: 150px"/>\
					</div>\
				</div>\
				<div class="layui-form-item">\
					<label class="layui-form-label">确认意见:</label>\
					<div class="layui-input-block">\
						<textarea placeholder="请输入内容" class="layui-textarea" name="confirmComment"></textarea>\
					</div>\
				</div>\
				<div class="layui-form-item" style="display:none;">\
					<center>\
						<button id="confirm-form-submit" class="layui-btn" lay-submit lay-filter="confirm">确认</button>\
					</center>\
				</div>\
			</form>'
	layui.use(['layer', 'form'], function() {
		var layer = layui.layer,
			form = layui.form;
		var confirmLayerIndex = layer.open({
			title: '数据确认',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ['450px', '400px'],
			content: '<div id="confirmContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['确认', '取消'],
			yes: function() {
				$('#confirm-form-submit').click();
			},
			btn2: function() {
				return true;
			},
			success: function() {
				/* 弹窗不加载滚动条 */
				$("#confirmContent").parent().css('overflow', 'visible');
				$("#confirmContent").append(tpl);
				$("#IMG_SRC").unbind("click").bind('click', function() {
					var strSealName = document.all.DWebSignSeal.HandWrite(8, 0, "signName");
					document.all.DWebSignSeal.ShowWebSeals();
					var data = document.all.DWebSignSeal.GetSealBmpString(strSealName, "jpg");
					$('#IMG_SRC').attr('src', "data:image/jpg;base64," + data);
					// document.all.DWebSignSeal.GetSealBmpToFile(strSealName, "jpg", "C:\\signTest\\test.jpg");
					document.all.DWebSignSeal.DelSeal(strSealName);
				});
			}
		});
		form.render(null, 'confirmForm');
		//监听提交
		form.on('submit(confirm)', function(data) {
			var imgSrc = $('#IMG_SRC').attr('src');
			if (imgSrc == '') {
				layer.alert("请先签名再确认！");
				return false;
			}
			var param = data.field;

			var cb_success = function(data) {
				if (data.success) {
					layer.closeAll();
					layer.msg("确认成功");
					window.myTable.reload();
				} else {
					layer.alert('确认失败,原因：' + data.msg, {
						icon: 2
					});
				}
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {
				layer.alert('确认失败!', {
					icon: 2
				});
			};
			param.pushId = pushId;
			param.signImg = $('#IMG_SRC').attr('src');
			//同步新增
			twxAjax("Thing.Fn.ProductQuality", "ConfirmQuality", param, false, cb_success, cb_error);
			return false;
		});
	})
}
//我的数据包表格列
var my_pkg_table_col = [
	[{
		field: 'number',
		width: 80,
		align: tableAlign,
		title: '编号'
	}, {
		field: 'model',
		width: 100,
		align: tableAlign,
		title: '所属型号'
	}, {
		field: 'category',
		width: 200,
		align: tableAlign,
		title: '所属类别'
	}, {
		field: 'file_type',
		align: tableAlign,
		title: '文件类型'
	}, {
		field: 'collect_mode',
		title: '采集方式',
		align: tableAlign,
		width: 120
	}, {
		field: 'status',
		title: '所属状态',
		align: tableAlign,
		width: 120
	}, {
		field: 'date',
		title: '采集日期',
		align: tableAlign,
		width: 200
	}]
];
var pdm_table_col = [
	[{
		type: "checkbox"
	}, {
		field: 'PRODUCT_NAME',
		width: 250,
		align: tableAlign,
		title: '产品名称'
	}, {
		field: 'NAME',
		align: tableAlign,
		title: '文件名称',
		templet: function(d) {
			return '<a style="cursor: pointer;color:#0000EE;text-decoration: underline;" onClick = "viewPdm(\'' + d.INFOURL + '\')">' + d.NAME + '</a>';
		}
	}, {
		field: 'NUMBER',
		width: 150,
		align: tableAlign,
		title: '文件编号'
	}, {
		field: 'TYPE',
		width: 200,
		align: tableAlign,
		title: '文件类型'
	}, {
		field: 'VERSION',
		width: 60,
		align: tableAlign,
		title: '版本'
	}, {
		field: 'MUSERNAME',
		title: '最后修改人',
		align: tableAlign,
		width: 150
	}, {
		field: 'STATE',
		title: '状态',
		align: tableAlign,
		width: 120
	}, {
		field: 'UPDATE_TIME',
		title: '采集时间',
		align: tableAlign,
		width: 200
	}, {
		field: 'operation',
		align: tableAlign,
		title: '操作',
		templet: function(d) {
			var relationProcessClass = d.IS_PROCESS == '1' ? 'layui-hide' : '';
			var relationProductClass = d.IS_PRODUCT == '1' ? 'layui-hide' : '';
			return '<span style="cursor: pointer;" class="layui-badge layui-bg-blue ' + relationProcessClass + '" onClick = "relationOneProcess(\'' + d.ID + '\',\'' + d.PDM_TYPE + '\')">过程关联</span>\
			<span style="margin-left:10px;cursor: pointer;" class="layui-badge layui-bg-blue ' + relationProductClass + '" onClick = "relationOneProduct(\'' + d.IS_PROCESS + '\',\'' + d.ID + '\',\'' + d.PDM_TYPE + '\')">产品关联</span>';
		},
		width: 200
	}]
];

/**
 * 查看Pdm文件数据
 */
function viewPdm(url) {
	window.open(url);
}

function relationOneProcess(id, type) {
	var pdmDatas = [{
		id: id,
		type: type
	}];
	relationProcess(pdmDatas);
}
/**
 * Pdm数据关联到过程结构树
 */
function relationProcess(pdmDatas) {
	var tpl = '<form class="layui-form" style="">\
					<div class="layui-form-item">\
						<label class="layui-form-label datalinklabel">产品型号:</label>\
						<div class="layui-input-block" >\
							<select name="model" lay-filter="model" lay-verify="required" lay-search id="model" >\
								<option value=""></option>\
							</select>\
						</div>\
					</div>\
					<div class="layui-form-item">\
						<label class="layui-form-label datalinklabel">阶段:</label>\
						<div class="layui-input-block" >\
							<select name="phase" lay-filter="phase" lay-verify="required" id="phase" >\
								<option value=""></option>\
							</select>\
						</div>\
					</div>\
					<div class="layui-form-item">\
						<label class="layui-form-label datalinklabel">数据包:</label>\
						<div class="layui-input-block">\
							<select name="datapkgname" lay-filter="datapkgname" lay-search lay-verify="required" id="datapkgname" >\
								<option value=""></option>\
							</select>\
						</div>\
					</div>\
					<div class="layui-form-item">\
						<label class="layui-form-label datalinklabel">清单类别:</label>\
						<div class="layui-input-block">\
							<select name="typelist" lay-filter="typelist"  lay-search lay-verify="required" id="typelist" >\
								<option value=""></option>\
								<option value="DESIGN_DATA_LIST">设计</option>\
								<option value="CRAFT_DATA_LIST">工艺</option>\
								<option value="PROCESS_CONTROL_LIST">过程控制</option>\
								<option value="QUALITY_CONTROL_LIST">质量综合</option>\
							</select>\
						</div>\
					</div>\
					<div class="layui-form-item">\
						<label class="layui-form-label datalinklabel">文件类型:</label>\
						<div class="layui-input-block">\
							<select name="filetype" lay-filter="filetype"  lay-search lay-verify="required" id="filetype">\
								<option value=""></option>\
							</select>\
						</div>\
					</div>\
					<div class="layui-form-item" style="display: none;">\
						<center>\
							<button id="relationProcess" class="layui-btn" lay-submit lay-filter="relationProcess" >提交</button>\
							<button id="" class="layui-btn layui-btn-primary" type="reset" >重置</button>\
						</center>\
					</div>\
				</form>';
	layui.use(['layer', 'form'], function() {
		var layer = layui.layer,
			form = layui.form;
		layer.open({
			title: '过程关联',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ["650px", '380px'],
			content: '<div id="relationProcessContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['关联', '取消'],
			yes: function(index, layero) {
				$("#relationProcess").click();
			},
			btn2: function(index, layero) {
				return true;
			},
			success: function() {
				$("#relationProcessContent").append(tpl);
				$("#relationProcessContent").parent().css("overflow", "visible");
				//加载型号产品
				twxAjax("publishMissionThing", "getTreeNodeByTypeAndParentID", {
					type: "product"
				}, false, function(data) {
					for (var i = 0; i < data.rows.length; i++) {
						$("#model").append('<option value="' + data.rows[i].TREEID + '">' + data.rows[i].NODENAME + '</option>');
					}
				});
				form.render();

				//初始化文件类型
				var initFileType = function() {
					//判断条件是否齐全
					//获取数据包的ID
					//获取类别表
					var param = {};
					param.datapkgid = '';
					param.tabletype = '';

					param.datapkgid = $('#datapkgname').val();
					param.tabletype = $('#typelist').val();

					if (param.datapkgid === '' || param.tabletype === '') {
						return;
					}
					var cb_success = function(res) {
						if (res.success) {
							$('#filetype').empty();
							for (var i = 0; i < res.data.length; i++) {
								var row = res.data[i];
								$('#filetype').append('<option value="' + row.FILE_TYPE + '">' + row.FILE_TYPE + '</option>');
							}
						} else {
							$('#filetype').empty();
							layer.alert(res.msg);
						}
						form.render();
					};
					var cb_error = function() {

					};
					twxAjax('Thing.Fn.DataCollect', 'GetDataPkgPlanFileType', param, true, cb_success, cb_error);
				};

				form.on('select(model)', function(d) {
					twxAjax("Thing.Fn.DataDownload", "QueryPhase", {
						parentId: document.getElementById("model").value
					}, false, function(data) {
						$("#phase").empty();
						$("#datapkgname").empty();
						$("#phase").append('<option value="">请选择</option>');
						for (var i = 0; i < data.rows.length; i++) {
							$("#phase").append('<option value="' + data.rows[i].TREEID + '">' + data.rows[i].NODENAME + '</option>');
						}
						form.render();
					});

					//选择改变事件进行文件类型的获取
					initFileType();
				});

				form.on('select(phase)', function(d) {
					twxAjax("Thing.Fn.DataDownload", "QueryDataPkgByTreeId", {
						parentId: document.getElementById("phase").value
					}, false, function(data) {
						$("#datapkgname").empty();
						$("#datapkgname").append('<option value="">请选择</option>');
						for (var i = 0; i < data.rows.length; i++) {
							$("#datapkgname").append('<option value="' + data.rows[i].ID + '">' + data.rows[i].NAME + '《' + data.rows[i].CODE + '》' + '</option>');
						}
						form.render();
					});

					//选择改变事件进行文件类型的获取
					initFileType();
				});

				form.on('select(datapkgname)', function(d) {
					//选择改变事件进行文件类型的获取
					initFileType();
				});

				form.on('select(typelist)', function() {
					//选择改变事件进行文件类型的获取
					initFileType();
				});

				form.on('submit(relationProcess)', function(data) {
					var d = data.field;
					if (d.filetype == '' || d.filetype == undefined) {
						layer.alert('请选择文件类别！');
						return false;
					}
					if (pdmDatas.length == 1) {
						var param = {
							creator: sessionStorage.getItem("username"),
							datapkgId: d.datapkgname,
							tableName: d.typelist,
							fileType: d.filetype,
							pdmType: pdmDatas[0].type,
							pdmId: pdmDatas[0].id
						}
						twxAjax('Thing.Fn.Workbench', 'RelationProcess', param, true, function(json) {
							if (json.success) {
								layer.closeAll();
								layer.msg(json.msg);
								window.pdmTable.reload();
							} else {
								layer.alert(json.msg);
							}
						});
					} else {
						var param = {
							creator: sessionStorage.getItem("username"),
							datapkgId: d.datapkgname,
							tableName: d.typelist,
							fileType: d.filetype,
							pdmDatas: JSON.stringify(pdmDatas)
						}
						twxAjax('Thing.Fn.Workbench', 'BatchRelationProcess', param, true, function(json) {
							if (json.success) {
								layer.closeAll();
								layer.msg(json.msg);
								window.pdmTable.reload();
							} else {
								layer.alert(json.msg);
							}
						});
					}

					return false;
				});
			}
		});
	})

}

function relationOneProduct(isProcess, pdmId, pdmType) {
	var pdmDatas = [{
		id: pdmId,
		type: pdmType,
		isProcess: isProcess
	}];
	relationProduct(pdmDatas);
}
/**
 * Pdm数据关联到产品结构树
 */
function relationProduct(pdmDatas) {
	var isHasProcess = false;
	for (var i = 0; i < pdmDatas.length; i++) {
		var pdmData = pdmDatas[i];
		if (pdmData.isProcess == '0') {
			isHasProcess = true;
			break;
		}
	}
	if (isHasProcess) {
		layer.alert('所选数据中存在未关联过程！');
		return false;
	}
	layer.open({
		title: '产品关联',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		maxmin: false,
		resize: false, //不允许拉伸
		// maxmin: true,
		area: ["700px", '700px'],
		content: '<div id="relationProductContent" style="padding-top: 15px;padding-right: 15px;"><ul id="bomTree" class="ztree"></ul></div>',
		btn: ['关联', '关闭'],
		yes: function(index, layero) {
			var checkedTrees = bomTree.ztreeObj.getCheckedNodes();
			if (checkedTrees.length > 0) {
				var treeIds = '';

				for (var i = 0; i < checkedTrees.length; i++) {
					if (checkedTrees[i].ID !== undefined) {
						treeIds += ',' + checkedTrees[i].ID;
					}
				}
				if (treeIds !== '') {
					treeIds = treeIds.substring(1);
				}

				if (pdmDatas.length == 1) {
					twxAjax("Thing.Fn.Workbench", "RelationProduct", {
						creator: sessionStorage.getItem("username"),
						pdmType: pdmDatas[0].type,
						pdmId: pdmDatas[0].id,
						treeIds: treeIds
					}, true, function(res) {
						if (res.success) {
							layer.closeAll();
							layer.msg(res.msg);
							window.pdmTable.reload();
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					}, function(err) {
						layer.alert("关联失败", {
							icon: 2
						});
					});
				} else {
					twxAjax("Thing.Fn.Workbench", "BacthRelationProduct", {
						creator: sessionStorage.getItem("username"),
						pdmDatas: JSON.stringify(pdmDatas),
						treeIds: treeIds
					}, true, function(res) {
						if (res.success) {
							layer.closeAll();
							layer.msg(res.msg);
							window.pdmTable.reload();
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					}, function(err) {
						layer.alert("关联失败", {
							icon: 2
						});
					});
				}

			} else {
				layer.alert("请勾选要关联的产品结构树节点", {
					icon: 2
				});
				return false;
			}
		},
		btn2: function(index, layero) {
			return true;
		},
		success: function() {
			window.bomTree = new BomTree(true);
			bomTree.loadTree();
		}
	});
}