var curInfo = {
	tableId: 'dataTable',
	tableType: '接插件连接固封情况',
	rsId: parent.window.tjfxRsID,
	columns: [
		[{
			field: 'TJFX_ID',
			title: '统计分析ID',
			hidden: true
		}, {
			field: 'DATA_ID',
			title: 'MES DATAID',
			hidden: true
		}, {
			field: 'REF_DPID',
			title: '关联的数据包ID',
			hidden: true
		}, {
			field: 'MODELCODE',
			title: '型号代号',
			width: 100,
			align: 'center'
		}, {
			field: 'ISNAME',
			title: '单机名称',
			width: 100,
			align: 'center'
		}, {
			field: 'ISCODE',
			title: '单机代号',
			width: 100,
			align: 'center'
		}, {
			field: 'CONNECTORCODE',
			title: '接插件代号',
			width: 120,
			align: 'center'
		}, {
			field: 'OPERATIONTYPE',
			title: '接插类型',
			width: 100,
			align: 'center'
		}, {
			field: 'CREATEDATE',
			title: '操作时间',
			width: 150,
			align: 'center'
		}, {
			field: 'NOTE',
			title: '备注',
			width: 200,
			align: 'center'
		}]
	]
};
/** 重置搜索条件 */
var resetSearchCondition = function() {
	$('#isname').textbox('setValue', '');
	$('#iscode').textbox('setValue', '');
	$('#connectorcode').textbox('setValue', '');
	$('#operationtype').textbox('setValue', '');
	$('#note').textbox('setValue', '');
};
var getFieldValue = function() {
	var param = {};
	param.type = curInfo.tableType;
	param.rsId = curInfo.rsId;
	var isname = $('#isname').textbox('getValue');
	var iscode = $('#iscode').textbox('getValue');
	var connectorcode = $('#connectorcode').textbox('getValue');
	var operationtype = $('#operationtype').textbox('getValue');
	var note = $('#note').textbox('getValue');
	param.conditionData = {
		isname: isname,
		iscode: iscode,
		connectorcode: connectorcode,
		operationtype: operationtype,
		note: note
	};
	return param;
};
