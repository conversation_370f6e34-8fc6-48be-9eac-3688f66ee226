var curInfo = {
	tableId: 'dataTable',
	tableType: '查看影像记录',
	cphoto: {}

};


$(document).ready(function() {
	layui.use(['layer', 'form'], function() {
		curInfo.cphoto = parent.window.cphoto;
		//初始化easyui的表格
		initTableComp();
		initStateCheckBtn(layui);
		initDataRefBtn(layui);
		initProductRefBtn(layui);
		initParamBtn(layui);
		initDeleteBtn(layui);
		//加载数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	});
});

//初始化状态确认按钮
var initStateCheckBtn = function(layui) {
	$('#cphoto_stateCheck').bind('click', function() {
		var layer = layui.layer;
		var sels = $('#' + curInfo.tableId).datagrid('getSelections');
		if (sels.length == 0) {
			layer.alert('请选择需要确认的数据...', {
				icon: 2
			});
			return;
		}

		var ids = '';
		for (var i = 0; i < sels.length; i++) {
			var row = sels[i];
			if (row.STATE_CHECK !== '已确认') {
				ids += ',' + row.ID;
			}
		}

		if (ids === '') {
			layer.alert('选择的数据均已确认', {
				icon: 1
			});
			return;
		}

		ids = ids.substring(1);

		var tabId = curInfo.cphoto.tableName;
		var type = "";
		var typeName = '';
		if (tabId === 'design_list_table') {
			type = 'DESIGN_DATA_RESULT';
			typeName = '设计类';
		} else if (tabId === 'craft_list_table') {
			type = 'CRAFT_DATA_RESULT';
			typeName = '工艺类';
		} else if (tabId === 'processcontrol_list_table') {
			type = 'PROCESS_CONTROL_RESULT';
			typeName = '过程控制';
		} else if (tabId === 'quanlitycontrol_list_table') {
			type = 'QUALITY_CONTROL_RESULT';
			typeName = '质量综合';
		}

		var datapkg = curInfo.cphoto.datapkg;
		var cb_success = function(data) {
			if (data.success === false) {
				layer.alert(data.message, {
					icon: 2
				});
				return;
			}
			layer.msg('确认成功');
			queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			//记录日志
			logRecord('编辑', '采集管理-数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的(IDs:' + ids +
				'、类型：' + typeName + ')状态确认', 1);
		};
		//添加失败的弹窗
		var cb_error = function(xhr) {
			//记录日志
			logRecord('编辑', '采集管理-数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的(IDs:' + ids +
				'、类型：' + typeName + ')状态确认', 0)
			layer.alert('状态确认失败!', {
				icon: 2
			});
		};

		var param = {};
		param.type = type;
		param.ids = ids;
		param.STATE_CHECK = '已确认';

		twxAjax("Thing.Fn.DataCollect", "UpdateaDataState", param, true, cb_success, cb_error);

	});
};

//初始化文件类型
var initFileType = function(layui) {
	var layer = layui.layer,
		form = layui.form;
	//判断条件是否齐全
	//获取数据包的ID
	//获取类别表
	var param = {};
	param.datapkgid = '';
	param.tabletype = '';

	param.datapkgid = $('#datapkgname').val();
	param.tabletype = $('#typelist').val();

	if (param.datapkgid === '' || param.tabletype === '') {
		return;
	}
	var cb_success = function(res) {
		if (res.success) {
			$('#filetype').empty();
			for (var i = 0; i < res.data.length; i++) {
				var row = res.data[i];
				$('#filetype').append('<option value="' + row.FILE_TYPE + '">' + row.FILE_TYPE + '</option>');
			}
		} else {
			$('#filetype').empty();
			layer.alert(res.msg);
		}
		form.render();
	};
	var cb_error = function() {

	};
	twxAjax('Thing.Fn.DataCollect', 'GetDataPkgPlanFileType', param, true, cb_success, cb_error);
};


//初始化数据包关联按钮
var initDataRefBtn = function(layui) {
	$('#cphoto_datapkgLink').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		var tabId = curInfo.cphoto.tableName;

		//获取选中的数据
		var datas = $('#' + curInfo.tableId).datagrid('getSelections');
		if (datas.length == 0) {
			//提示用户，请选择待编辑的数据
			layer.alert('请选择关联数据...', {
				icon: 2
			});
			return;
		} else {
			for (var i = 0; i < datas.length; i++) {
				if (datas[i].GATHERING_METHOD != '自动采集') {
					layer.alert('请选择自动采集的数据关联...', {
						icon: 2
					});
					return;
				}
			}
		}


		form.on('select(model)', function(d) {
			twxAjax("Thing.Fn.DataDownload", "QueryPhase", {
				parentId: document.getElementById("model").value
			}, false, function(data) {
				$("#phase").empty();
				$("#phase").append('<option value="">请选择</option>');
				for (var i = 0; i < data.rows.length; i++) {
					$("#phase").append('<option value="' + data.rows[i].TREEID + '">' + data.rows[i].NODENAME + '</option>');
				}
				form.render();
			});

			//选择改变事件进行文件类型的获取
			initFileType(layui);
		});

		form.on('select(phase)', function(d) {
			twxAjax("Thing.Fn.DataDownload", "QueryDataPkgByTreeId", {
				parentId: document.getElementById("phase").value
			}, false, function(data) {
				$("#datapkgname").empty();
				$("#datapkgname").append('<option value="">请选择</option>');
				for (var i = 0; i < data.rows.length; i++) {
					$("#datapkgname").append('<option value="' + data.rows[i].ID + '">' + data.rows[i].NAME + '《' + data.rows[i].CODE + '》' + '</option>');
				}
				form.render();
			});

			//选择改变事件进行文件类型的获取
			initFileType(layui);
		});


		form.on('select(typelist)', function() {
			//选择改变事件进行文件类型的获取
			initFileType(layui);
		});

		form.on('select(datapkgname)', function(d) {

			//选择改变事件进行文件类型的获取
			initFileType(layui);
		});

		layer.open({
			title: '数据包关联',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			maxmin: false,
			resize: false, //不允许拉伸
			// maxmin: true,
			area: ["650px", '370px'],
			content: '<div id="datapkglinkContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['关联', '重置', '关闭'],
			yes: function(index, layero) {
				var param = {};
				param.tabletype = $('#typelist').val();
				var tableTypeName = $('#typelist  option:selected').text();
				param.file_type = $('#filetype').val();
				// param.dataid = datas[0].ID;
				param.datapkgid = $('#datapkgname').val();
				var pkgName = $('#datapkgname  option:selected').text();
				param.dataids = '';

				for (var i = 0; i < datas.length; i++) {
					param.dataids += ',' + datas[i].ID;
				}
				if (param.dataids !== '') {
					param.dataids = param.dataids.substring(1);
				}

				var source_table = "";
				var typeName = '';
				if (tabId === 'design_list_table') {
					source_table = 'DESIGN_DATA_RESULT';
					typeName = '设计类';
				} else if (tabId === 'craft_list_table') {
					source_table = 'CRAFT_DATA_RESULT';
					typeName = '工艺类';
				} else if (tabId === 'processcontrol_list_table') {
					source_table = 'PROCESS_CONTROL_RESULT';
					typeName = '过程控制';
				} else if (tabId === 'quanlitycontrol_list_table') {
					source_table = 'QUALITY_CONTROL_RESULT';
					typeName = '质量综合';
				}

				param.source_table = source_table;
				var datapkg = curInfo.cphoto.datapkg;;
				if (param.file_type === '' || param.datapkgid === '') {
					layer.alert('请选择需要关联的数据包的信息...', {
						icon: 2
					});
					return;
				}

				var cb_success = function(data) {
					if (data.success === false) {
						logRecord('编辑', '采集管理-数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的(类型：' +
							typeName + '、IDs：' + param.dataids + ')关联到数据包(ID：' + param.datapkgid + '、名称：' + pkgName + ')下的(类型：' +
							tableTypeName + '、文件类别：' + param.file_type + ')', 0);
						layer.alert(data.message, {
							icon: 2
						});
						return;
					}
					logRecord('编辑', '采集管理-数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的(类型：' +
						typeName + '、IDs：' + param.dataids + ')关联到数据包(ID：' + param.datapkgid + '、名称：' + pkgName + ')下的(类型：' +
						tableTypeName + '、文件类别：' + param.file_type + ')', 1);
					queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
					layer.close(index);
				};
				var cb_error = function() {};
				// twxAjax('Thing.Fn.DataCollect','ReAssignData',param,true,cb_success,cb_error);
				twxAjax('Thing.Fn.DataCollect', 'BatchReAssignData', param, true, cb_success, cb_error);
			},
			btn2: function(index, layero) {
				return false;
			},
			btn3: function(index, layero) {
				return true;
			},
			success: function() {
				var editTpl = $("#datapkglink")[0].innerHTML;
				$("#datapkglinkContent").append(editTpl);

				//加载型号产品
				twxAjax("publishMissionThing", "getTreeNodeByTypeAndParentID", {
					type: "product"
				}, false, function(data) {
					for (var i = 0; i < data.rows.length; i++) {
						$("#model").append('<option value="' + data.rows[i].TREEID + '">' + data.rows[i].NODENAME + '</option>');
					}
				});
				form.render();
			}
		});

	});
};

var initParamBtn = function(layui) {
	$('#cphoto_param').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		var tabId = curInfo.cphoto.tableName;

		//获取选中的数据
		var datas = $('#' + curInfo.tableId).datagrid('getSelections');
		if (datas.length == 0) {
			//提示用户，请选择待编辑的数据
			layer.alert('请选择待编辑的数据...', {
				icon: 2
			});
			return;
		} else {
			var tpl = '<form class="layui-form" action="" lay-filter="param-form">\
						<div class="layui-form-item layui-form-text">\
						    <div class="">\
						      <textarea name="param" placeholder="请输入内容" lay-verify="required" style="height:160px;resize:none;" class="layui-textarea"></textarea>\
						    </div>\
						  </div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="submit-param" lay-submit="" lay-filter="submit-param">确认</button>\
								</div>\
							</div>\
						</div>\
					</form>';
			layer.open({
				title: '编写属性',
				type: 1,
				fixed: false,
				maxmin: false,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				shadeClose: false,
				resize: false, //不允许拉伸
				area: ['380px', '270px'],
				content: '<div id="paramContent" style="padding-top: 15px;padding-left: 15px;padding-right: 15px;"></div>',
				btn: ['确认', '取消'],
				yes: function() {
					$('#submit-param').click();
				},
				btn2: function() {
					return true;
				},
				success: function() {
					$("#paramContent").append(tpl);
				}
			});
			form.render(null, 'param-form');
			form.on('submit(submit-param)', function(data) {
				var ids = '';
				for (var i = 0; i < datas.length; i++) {
					if (datas[i].ID != undefined) {
						ids += ',' + datas[i].ID;
					}
				}

				ids = ids.substring(1);

				var type = "";
				var typeName = '';
				if (tabId === 'design_list_table') {
					type = 'DESIGN_DATA_RESULT';
					typeName = '设计类';
				} else if (tabId === 'craft_list_table') {
					type = 'CRAFT_DATA_RESULT';
					typeName = '工艺类';
				} else if (tabId === 'processcontrol_list_table') {
					type = 'PROCESS_CONTROL_RESULT';
					typeName = '过程控制';
				} else if (tabId === 'quanlitycontrol_list_table') {
					type = 'QUALITY_CONTROL_RESULT';
					typeName = '质量综合';
				}

				var cb_success = function(res) {
					if (res.success) {
						layer.closeAll();
						//新增完成后需要刷新界面
						queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
					} else {
						layer.alert(res.msg, {
							icon: 2
						})
					}
				};
				//添加失败的弹窗
				var cb_error = function(xhr) {
					layer.alert('更新失败!', {
						icon: 2
					});
				};

				//向服务端发送删除指令
				twxAjax("Thing.Fn.DataCollect", "UpdateParam", {
					param: data.field.param,
					type: type,
					ids: ids
				}, false, cb_success, cb_error);

				return false;
			});
		}
	});
}

//初始化产品结构树关联按钮
var initProductRefBtn = function(layui) {
	$('#cphoto_procuct_link').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		var tabId = curInfo.cphoto.tableName;;

		//获取选中的数据
		var datas = $('#' + curInfo.tableId).datagrid('getSelections');
		if (datas.length == 0) {
			//提示用户，请选择待编辑的数据
			layer.alert('请选择关联数据...', {
				icon: 2
			});
			return;
		}
		layer.open({
			title: '产品结构树关联',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			maxmin: false,
			resize: false, //不允许拉伸
			// maxmin: true,
			area: ["700px", '600px'],
			content: '<div id="productLinkContent" style="padding-top: 15px;padding-right: 15px;"><ul id="bomTree" class="ztree"></ul></div>',
			btn: ['关联', '关闭'],
			yes: function(index, layero) {
				var checkedTrees = bomTree.ztreeObj.getCheckedNodes();
				if (checkedTrees.length > 0) {
					var source_table = "";
					var typeName = '';
					if (tabId === 'design_list_table') {
						source_table = 'DESIGN_DATA_RESULT';
						typeName = '设计类';
					} else if (tabId === 'craft_list_table') {
						source_table = 'CRAFT_DATA_RESULT';
						typeName = '工艺类';
					} else if (tabId === 'processcontrol_list_table') {
						source_table = 'PROCESS_CONTROL_RESULT';
						typeName = '过程控制';
					} else if (tabId === 'quanlitycontrol_list_table') {
						source_table = 'QUALITY_CONTROL_RESULT';
						typeName = '质量综合';
					}

					var relationIds = '';

					for (var i = 0; i < datas.length; i++) {
						if (datas[i].ID !== undefined) {
							relationIds += ',' + datas[i].ID;
						}
					}
					if (relationIds !== '') {
						relationIds = relationIds.substring(1);
					}

					var treeIds = '';

					for (var i = 0; i < checkedTrees.length; i++) {
						if (checkedTrees[i].ID !== undefined) {
							treeIds += ',' + checkedTrees[i].ID;
						}
					}
					if (treeIds !== '') {
						treeIds = treeIds.substring(1);
					}

					twxAjax("Thing.Fn.BOM", "ProductLinkList", {
						relationIds: relationIds,
						treeIds: treeIds,
						relationUser: sessionStorage.getItem('username'),
						relationName: source_table
					}, true, function(res) {
						if (res.success) {
							layer.closeAll();
							layer.msg(res.msg);
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					}, function(err) {
						layer.alert('关联请求失败！');
					});
				} else {
					layer.alert('请勾选要关联的产品结构树节点', {
						icon: 2
					});
					return false;
				}

			},
			btn2: function(index, layero) {
				return true;
			},
			success: function() {
				window.bomTree = new BomTree(true);
				bomTree.loadTree();
			}
		});
	});
};


//初始化删除按钮
var initDeleteBtn = function(layui) {
	$('#cphoto_delete').bind('click', function() {
		var layer = layui.layer;
		var tabId = curInfo.cphoto.tableName;;
		//判断是否选定了数据
		//获取选中的数据
		var datas = $('#' + curInfo.tableId).datagrid('getSelections');
		if (datas.length == 0) {
			//提示用户，请选择待编辑的数据
			layer.alert('请选择待删除数据...', {
				icon: 2
			});
			return;
		}

		layer.confirm('是否确认删除选中数据?', {
			icon: 3,
			title: '删除提示'
		}, function(index) {
			var ids = '';
			for (var i = 0; i < datas.length; i++) {
				ids += ',' + datas[i].ID;
			}

			ids = ids.substring(1);

			var type = "";
			var typeName = '';
			if (tabId === 'design_list_table') {
				type = 'DESIGN_DATA_RESULT';
				typeName = '设计类';
			} else if (tabId === 'craft_list_table') {
				type = 'CRAFT_DATA_RESULT';
				typeName = '工艺类';
			} else if (tabId === 'processcontrol_list_table') {
				type = 'PROCESS_CONTROL_RESULT';
				typeName = '过程控制';
			} else if (tabId === 'quanlitycontrol_list_table') {
				type = 'QUALITY_CONTROL_RESULT';
				typeName = '质量综合';
			}

			var datapkg = curInfo.cphoto.datapkg;

			var cb_success = function(data) {
				if (data.success === false) {
					layer.alert(data.message, {
						icon: 2
					});
					return;
				}
				//新增完成后需要刷新界面
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
				//记录日志
				logRecord('删除', '采集管理-删除数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的数据(类型：' +
					typeName + '、IDs：' + ids + ')', 1);
				layer.msg('删除成功');
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {
				logRecord('删除', '采集管理-删除数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的数据(类型：' +
					typeName + '、IDs：' + ids + ')', 0);
				layer.alert('删除失败!', {
					icon: 2
				});
			};

			//向服务端发送删除指令
			// twxAjax("publishMissionThing", "deleteDataListDataByTypeAndID", {type: type, ID: row.ID},false,cb_success,cb_error);
			twxAjax("Thing.Fn.PlanBuild", "DeletePlanListByID", {
				type: type,
				ids: ids
			}, false, cb_success, cb_error);

		});
	});
};

//初始化表格 - 
var initTableComp = function(layui) {
	var columns = listTableUtil.getColumns('photo');
	columns[0].unshift({
		field: 'ck',
		checkbox: true
	});
	$('#' + curInfo.tableId).datagrid({
		data: [],
		singleSelect: false,
		fitColumns: true,
		striped: true,
		// fit: true,
		height: 618,
		rownumbers: true,
		pagination: true,
		columns: columns,
		loadMsg: '正在加载数据...',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>',
		onLoadSuccess: function(data) {
			changeWidth(curInfo.tableId);
		}
	});

	//初始化分页控件
	initPagination(curInfo.tableId, []);
};

//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var getFieldValue = function() {
	var param = {};
	param = curInfo.cphoto;

	var query = {};
	query.processTreeId = param.datapkg.REFTREEID;
	query.secLevel = param.secLevel;
	if (param.tableName == 'processcontrol_list_table') {
		query.queryType = 'process';
	} else if (param.tableName == 'craft_list_table') {
		query.queryType = 'craft';
	} else if (param.tableName == 'design_list_table') {
		query.queryType = 'design';
	} else if (param.tableName == 'quanlitycontrol_list_table') {
		query.queryType = 'quality';
	}
	query.fileType = '影像记录';
	return {
		query: query
	};
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	$('#' + curInfo.tableId).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
}


//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(res) {
		pageLoadFlag = true;
		totalRecords = res.data;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function() {};

	//传递参数
	var param = getFieldValue();
	twxAjax('Thing.Fn.ListData', 'QueryListDataCount', param, true, cb_success, cb_error);
};

//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + curInfo.tableId).datagrid('loading');
	initTotalRecords();
	var cb_success = function(res) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#' + curInfo.tableId).datagrid('loadData', res.data);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#' + curInfo.tableId).datagrid('loaded');
	};
	var cb_error = function() {
		$('#' + curInfo.tableId).datagrid('loaded');
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	//传递的参数
	var param = getFieldValue();
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.ListData', 'QueryListDataPage', param, true, cb_success, cb_error);
};

//初始化分页组件
var initPagination = function(tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {

		},
		onRefresh: function(pageNumber, pageSize) {
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	});
};
