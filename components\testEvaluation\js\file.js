/**
 * 提交上传的文件
 */
function submitUploadFiles(index, treeId) {
	//获取表格的数据
	var datas = table.cache['upload-table'];
	if (datas.length == 0) {
		layer.alert("请先上传文件！", {
			icon: 2
		});
	} else {
		//校验表格数据的有效性
		var isAllSuccess = true;
		var isAllHasText = true;
		for (var i = 0; i < datas.length; i++) {
			if (datas[i].status == 2 || datas[i].status == 0) {
				isAllSuccess = false;
			}
			if (datas[i].category == '' || datas[i].name == '') {
				isAllHasText = false;
			}
		}
		if (!isAllSuccess) {
			layer.alert("表中有文件未上传成功，请重试！", {
				icon: 2
			});
		} else {
			if (!isAllHasText) {
				layer.alert("表中有类别或者名称未填写，请检查后重试！", {
					icon: 2
				});
			} else {
				var loadIndex = layer.load(0);
				twxAjax(THING, 'AddFiles', {
					files: JSON.stringify(datas),
					treeId: treeId,
					creator: sessionStorage.getItem("username")
				}, true, function(res) {
					layer.close(loadIndex);
					if (res.success) {
						layer.close(index);
						reloadTable(treeId);
						layer.msg(res.msg);
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}, function(res) {
					layer.close(loadIndex);
					layer.alert('请求失败，原因：' + JSON.stringify(res), {
						icon: 2
					});
				});
			}
		}
	}
}

/**
 * 请求文件类别
 * @param {Object} successFn
 */
function queryCategory(successFn) {
	twxAjax(THING, 'QueryCategory', {}, true,
		function(res) {
			if (res.success) {
				successFn(res.data);
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		},
		function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		}
	);
}
/**
 * 加载类别输入下拉框
 */
function renderCategoryInput() {
	queryCategory(function(data) {
		var ds = [];
		for (var i = 0; i < data.length; i++) {
			$("#category").append('<option value="' + data[i].NAME + '">' + data[i].NAME + "</option>");
			ds.push(data[i].NAME);
		}
		insertSelect.render({
			elem: "#category",
			data: ds
		});
	});
}

function uploadFileLayer(successFn, yesFn) {
	layer.open({
		title: '上传文件',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['1100px', '560px'],
		content: '<div id="uploadContent" style="padding-top:10px "></div>',
		btn: ['提交', '取消'],
		yes: function(index, layero, that) {
			yesFn(index);
		},
		btn2: function() {
			layer.closeAll();
		},
		success: function(layero, index, that) {
			$("#uploadContent").parent().css('overflow', 'visible');
			var addTpl = `<div class="layui-form" lay-filter="uploadForm">
							<div class="layui-form-item" style="margin-bottom: 5px;">
								<div class="layui-inline">
									<label class="layui-form-label">类别</label>
									<div class="layui-input-inline">
										<select name="category" lay-search id="category">
											<option value=""></option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<div class="layui-input-inline">
										<div class="layui-upload">
											<button type="button" style="float: left;" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
											<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
										</div>
									</div>
								</div>
							</div>
						</div>
						<table id="upload-table" lay-upload="file-table"></table>`;
			$("#uploadContent").append(addTpl);
			renderCategoryInput();
			form.render(null, 'uploadForm');
			successFn(index);
		}
	});
}

//上传组件中的文件列表删除已经上传成功的文件 和手动删除的文件 避免重复上传
function deleteSuccessFile(files, oldTableData) {
	var datas = table.cache['upload-table'];
	for (var i = 0; i < deleteUploadFiles.length; i++) {
		delete files[deleteUploadFiles[i]];
	}

	for (var i = 0; i < oldTableData.length; i++) {
		if (oldTableData[i].status == 1) {
			delete files[oldTableData[i].fileIndex];
		}
	}
}



function renderUpload() {
	upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + '/test/evaluation/upload',
		auto: true,
		field: 'file',
		accept: 'file',
		multiple: true,
		dataType: "json",
		choose: function(obj) {
			var files = obj.pushFile();
			var tableData = table.getData('upload-table');
			var category = $("#category").val();
			deleteSuccessFile(files, tableData);
			for (var fileIndex in files) {
				var file = files[fileIndex];
				var fileName = file.name;
				tableData.push({
					category: category,
					name: fileName.substring(0, fileName.lastIndexOf('.')),
					fileFormat: fileName.substring(fileName.lastIndexOf('.') + 1),
					status: 0,
					security: getFileSecurity(file.name),
					fileName: file.name,
					fileIndex: fileIndex
				});
			}
			table.reloadData('upload-table', {
				data: tableData,
				scrollPos: 'fixed'
			});
		},
		done: function(res, index) {
			updateRow(res, index);
			fileResInfos.push({
				index: index,
				res: res
			});
		},
		allDone: function(obj) {
			var datas = table.getData('upload-table');
			for (var x = 0; x < fileResInfos.length; x++) {
				var fileResInfo = fileResInfos[x];
				for (var i = 0; i < datas.length; i++) {
					if (datas[i].fileIndex == fileResInfo.index) {
						if (fileResInfo.res.success) {
							datas[i].status = 1;
							datas[i].filePath = fileResInfo.res.data.filePath;
						} else {
							datas[i].status = 2;
						}
						break;
					}
				}
			}
			table.reloadData('upload-table', {
				data: datas,
				scrollPos: 'fixed'
			});
		}
	});
}

//手动删除的已经上传了的文件
var deleteUploadFiles = [];
//存放单个文件上传完成之后的信息
var fileResInfos = [];

/**
 * 上传文件
 * @param {Object} treeId
 */
function uploadFile(treeId) {
	deleteUploadFiles = [];
	fileResInfos = [];
	uploadFileLayer(function(index) {
		renderUploadTable();
		renderUpload();
	}, function(index) {
		submitUploadFiles(index, treeId);
	});
}