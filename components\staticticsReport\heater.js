var curInfo = {
	tableId: 'dataTable',
	tableType: '星上加热器',
	rsId: parent.window.tjfxRsID,
	columns: [
		[{
			field: 'TJFX_ID',
			title: '统计分析ID',
			hidden: true
		}, {
			field: 'DATA_ID',
			title: 'MES DATAID',
			hidden: true
		}, {
			field: 'REF_DPID',
			title: '关联的数据包ID',
			hidden: true
		}, {
			field: 'MODELCODE',
			title: '型号代号',
			width: 100,
			align: 'center'
		}, {
			field: 'HEATERNAME',
			title: '加热器名称',
			width: 120,
			align: 'center'
		}, {
			field: 'HEATERCODE',
			title: '加热器代号',
			width: 120,
			align: 'center'
		}, {
			field: 'DESIGNVALUE',
			title: '设计阻值（Ω）',
			width: 100,
			align: 'center'
		}, {
			field: 'HEATERCONTENT',
			title: '实测情况',
			width: 100,
			align: 'center'
		}, {
			field: 'LEADWIRECONFORM',
			title: '引线标签符合性',
			width: 140,
			align: 'center'
		}, {
			field: 'WEIGHT',
			title: '装星重量',
			width: 100,
			align: 'center'
		}, {
			field: 'ENVIRONMENTTEMP',
			title: '环境温度',
			width: 100,
			align: 'center'
		}, {
			field: 'CREATEDATE',
			title: '操作时间',
			width: 150,
			align: 'center'
		}]
	]
};
/** 重置搜索条件 */
var resetSearchCondition = function() {
	$('#heatername').textbox('setValue', '');
	$('#heatercode').textbox('setValue', '');
	$('#designvalue').textbox('setValue', '');
	$('#heatercontent').textbox('setValue', '');
	$('#leadwireconform').textbox('setValue', '');
	$('#weight').textbox('setValue', '');
	$('#environmenttemp').textbox('setValue', '');
};
var getFieldValue = function() {
	var param = {};
	param.type = curInfo.tableType;
	//数据包的ID
	param.rsId = curInfo.rsId;
	var heatername = $('#heatername').textbox('getValue');
	var heatercode = $('#heatercode').textbox('getValue');
	var designvalue = $('#designvalue').textbox('getValue');
	var heatercontent = $('#heatercontent').textbox('getValue');
	var leadwireconform = $('#leadwireconform').textbox('getValue');
	var weight = $('#weight').textbox('getValue');
	var environmenttemp = $('#environmenttemp').textbox('getValue');
	param.conditionData = {
		heatername: heatername,
		heatercode: heatercode,
		designvalue: designvalue,
		heatercontent: heatercontent,
		leadwireconform: leadwireconform,
		weight: weight,
		environmenttemp: environmenttemp
	};
	return param;
};
