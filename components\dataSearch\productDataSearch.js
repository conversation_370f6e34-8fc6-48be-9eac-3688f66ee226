var selTreeId = 0; //左侧产品结构树选中的节点的treeId 默认为0
var selTabId = 'quality_data'; //右侧选中的tab的id 默认为质量数据
var queryType = 'process';
var ztreeObj;
$(function() {
	loadTree();

	$("#root_layout").layout("panel", "center").panel({
		onResize: function(width, height) {
			//2是边框宽度
			var gridWidth = width - 2;
			var gridId = "";
			if (selTabId == 'quality_data') {
				gridId = "secondTable";
			} else {
				gridId = selTabId;
			}
			if (gridId != "") {
				$("#" + gridId).datagrid('resize', {
					width: gridWidth
				});
				changeWidth(gridId);
			}
		}
	});


	$('#root_layout_tabs').tabs({
		onSelect: function(title, index) {
			var tab = $('#root_layout_tabs').tabs('getTab', index);
			selTabId = tab.panel('options').tabId;
			queryType = tab.panel('options').queryType;
			loadTableData();
		}
	});
	loadTableData();
})

/**
 * 加载产品结构树的数据
 */
function loadTableData() {
	if (selTabId == 'quality_data') {
		new QualityData(selTreeId);
	} else if (selTabId == 'report_table') {

	} else if (selTabId == 'analysis_table') {

	} else {
		new ListData(selTreeId, selTabId);
	}
}
//加载树结构
function loadTree() {
	var cb_success = function(res) {
		if (res.success) {
			var datas = res.data;
			if (datas.length > 0) {
				datas = bomTreeUtil.dealDataIcons(datas);
				var treeSetting = bomTreeUtil.treeSetting;
				treeSetting.callback = {
					beforeClick: function(treeId, treeNode, clickFlag) {
						if (treeNode.NODE_TYPE == 'folder') {
							return false;
						} else {
							return true;
						}
					},
					onClick: function(event, treeId, treeNode) {
						selTreeId = treeNode.ID;
						loadTableData();
					},
					onExpand: function(event, treeId, treeNode) {

					}
				}
				ztreeObj = $.fn.zTree.init($("#bomTree"), treeSetting, datas);
				var nodes = ztreeObj.getNodes();
				for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
					ztreeObj.expandNode(nodes[i], true, false, true);
				}
			}
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax('Thing.Fn.BOM', 'QueryProductTreeRoot', {
		username: sessionStorage.getItem('username')
	}, true, cb_success);
}

//从mes系统同步产品结构树
function asyncBomTree() {
	layui.use(['layer'], function() {
		var layer = layui.layer;
		var Iindex = layer.msg('正在同步,请不要关闭页面,请稍等......', {
			icon: 16,
			shade: 0.01,
			time: 0
		});
		var cb_success = function(data) {
			var rs = data.rows[0].result;
			layer.close(Iindex);
			if (rs.indexOf('中断') > -1) {
				layer.alert(rs, {
					icon: 2
				})
			} else {
				layer.msg(rs);
				loadTree();
			}
		};
		var cb_error = function(data) {
			layer.close(Iindex);
			layer.alert('同步出错...', {
				icon: 2
			})
		};
		twxAjax('Thing.Fn.BOM', 'autoAsyncBOM', '', true, cb_success, cb_error);
	})
}

/**
 * 数据包清单数据  包含设影像记录、计类、工艺类、过程控制和质量综合信息数据
 * tabId
 */
function ListData(treeId, tabId) {
	var othis = this;
	this.dataGirdId = tabId;
	this.pageOptions = {
		pageSize: 30,
		pageNumber: 1
	};
	this.renderTable = function() {
		var columns = listTableUtil.getColumns(queryType);
		$('#' + othis.dataGirdId).datagrid({
			data: [],
			fitColumns: true,
			height: windowH - 38,
			columns: columns,
			emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>数据加载中...</font></div>',
			pagination: true,
			loadMsg: '正在加载数据...',
			rownumbers: true,
			singleSelect: true,
			striped: true,
			onDblClickRow: function(rowIndex, rowData) {
				twxAjax('Thing.Fn.ProductDataQuery', 'QueryParentsByTreeId', {
					treeId: rowData.PRODUCT_TREE_IDS.split(",")[0]
				}, true, function(data) {
					locationTreeNodeUtil(data.rows, ztreeObj, 'ID', function(thisNode) {});
				});
			},
			onLoadSuccess: function(data) {
				changeWidth(othis.dataGirdId);
			}
		})
	};

	//初始化分页组件
	this.initPagination = function(data) {
		$('#' + othis.dataGirdId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function() {
					othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions.pageNumber);
				}
			}],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function(pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function(pageNumber, pageSize) {
				//返回false可以在取消刷新操作
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			},
			onRefresh: function(pageNumber, pageSize) {
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function(pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};
	//分页查询数据
	this.queryDataByPage = function(pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.dataGirdId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function(res) {
			if (res.success) {
				othis.dataLoadFlag = true;
				//调用成功后，渲染数据
				$('#' + othis.dataGirdId).datagrid('loadData', res.data);
				if (othis.pageLoadFlag) {
					othis.paginationShow();
				}
				$('#' + othis.dataGirdId).datagrid('loaded');
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function() {
			layui.use(['layer'], function() {
				var layer = layui.layer;
				layer.alert('加载出错...', {
					icon: 2
				});
			});
		};
		var params = {};
		params.query = othis.getParams();
		params.pageSize = othis.pageOptions.pageSize;
		params.pageNumber = othis.pageOptions.pageNumber;
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.ListData', 'QueryListDataPage', params, true, cb_success, cb_error);
	};
	this.getParams = function() {
		var params = {};
		if (queryType == 'photo') {
			params = {
				secLevel: '<' + userSecLevel,
				productTreeId: treeId,
				fileType: '影像记录',
				queryUser: sessionStorage.getItem('username')
			}
		} else {
			params = {
				secLevel: '<' + userSecLevel,
				productTreeId: treeId,
				queryType: queryType,
				queryUser: sessionStorage.getItem('username')
			}
		}
		return params;
	};
	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function() {
		$('#' + othis.dataGirdId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
		//重新初始化行号
		othis.initLineNumbers();
	};

	//初始化全部的记录条数
	this.initTotalRecords = function() {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function(res) {
			if (res.success) {
				othis.pageLoadFlag = true;
				othis.totalRecords = res.data;
				if (othis.dataLoadFlag) {
					othis.paginationShow();
				}
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function() {};
		var params = {};
		params.query = othis.getParams();
		twxAjax('Thing.Fn.ListData', 'QueryListDataCount', params, true, cb_success, cb_error);
	};

	//初始化行号
	this.initLineNumbers = function() {
		var rowNumbers = $('.datagrid-cell-rownumber');
		var start = (othis.pageOptions.pageNumber - 1) * othis.pageOptions.pageSize;
		$(rowNumbers).each(function(index) {
			var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
			$(rowNumbers[index]).html("");
			$(rowNumbers[index]).html(row);
		});
	};

	this.renderTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
}