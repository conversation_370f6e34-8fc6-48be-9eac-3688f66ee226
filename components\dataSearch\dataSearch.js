$(function() {
	loadDataSearchTypeCombobox();
})

//表格名称
var tableName = 'searchDataTable';
var selTableType = '';
var selTableName = '';
//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};
var label = {
	modelLabel: '型号',
	nameLabel: '名称'
};
//构建表格
var renderTable = function() {
	var tableType = selTableType;
	var tableColumns = [];
	if (tableType == 'electronic_components') {
		tableColumns = electronicComponentsColumns;
		label.modelLabel = '型号';
		label.nameLabel = '元器件名称';
	} else if (tableType == 'cable_insulation_test') {
		tableColumns = cableTestColumns;
		label.modelLabel = '型号';
		label.nameLabel = '电缆编号';
	} else if (tableType == 'heating_element_reinspection') {
		tableColumns = heatingReinspectionColumns;
		label.modelLabel = '型号';
		label.nameLabel = '加热片名称';
	} else if (tableType == 'heating_circuit_test') {
		tableColumns = heatingTestColumns;
		label.modelLabel = '型号';
		label.nameLabel = '加热回路名称';
	} else if (tableType == 'StandAlong') {
		tableColumns = standAlongColumns;
		label.modelLabel = '型号代号';
		label.nameLabel = '装星批次';
	} else if (tableType == 'ConnectorOnOff') {
		tableColumns = connectorOnOffColumns;
		label.modelLabel = '型号代号';
		label.nameLabel = '单机名称';
	} else if (tableType == 'ConnectorOnOffTimes') {
		tableColumns = connectorOnOffTimesColumns;
		label.modelLabel = '单机代号';
		label.nameLabel = '单机名称';
	} else if (tableType == 'Heater') {
		tableColumns = heaterColumns;
		label.modelLabel = '型号代号';
		label.nameLabel = '加热器名称';
	} else if (tableType == 'HeatResist') {
		tableColumns = heatResistColumns;
		label.modelLabel = '型号代号';
		label.nameLabel = '测温点名称';
	} else if (tableType == 'LayersOnOff') {
		tableColumns = layersOnOffColumns;
		label.modelLabel = '型号代号';
		label.nameLabel = '多层名称';
	} else if (tableType == 'StructuralAssembly') {
		tableColumns = structuralAssemblyColumns;
		label.modelLabel = '型号';
		label.nameLabel = '检测项目';
	}
	resetForm();
	changeLabel();
	$('#' + tableName).datagrid({
		data: [],
		columns: tableColumns,
		fit: true,
		toolbar: '#tb',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"></div>',
		pagination: true,
		loadMsg: '正在加载数据...',
		rownumbers: true,
		singleSelect: true,
		striped: true
	});
}

//改变搜索名称
var changeLabel = function() {
	$("#dataModel").parent().prev().text(label.modelLabel + '：');
	$("#dataName").parent().prev().text(label.nameLabel + '：');
}

//初始化分页组件
var initPagination = function(data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function(pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};



//获取查询条件参数
function getSearchParams() {
	var json = {
		dataModel: $("#dataModel").val(),
		dataName: $("#dataName").val()
	};
	return {
		json: json
	};
}
//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + tableName).datagrid('loading');
	initTotalRecords();
	var cb_success = function(data) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#' + tableName).datagrid('loadData', data.rows);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('div.datagrid-empty').html(
			'<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>数据加载中...</font></div>'
		);
		$('#' + tableName).datagrid('loaded');
	};
	var cb_error = function() {
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	var parmas = getSearchParams();
	parmas.pageSize = pageOptions.pageSize;
	parmas.pageNumber = pageOptions.pageNumber;
	parmas.tableType = selTableType;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.DataSearch', 'QueryDataSearch', parmas, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	$('#' + tableName).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
	// var log = getSearchLog();
	// logRecord('查询', '高级查询-查询条件(' + log + ')，共查询出' + totalRecords + '条数据', 1);
}

//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(data) {
		pageLoadFlag = true;
		totalRecords = data.rows[0].COUNT;
		if (dataLoadFlag) {
			paginationShow();
		}
		//initPagination('logtable',{total:data.rows[0].COUNT});

	};
	var cb_error = function() {};
	var parmas = getSearchParams();
	parmas.tableType = selTableType;
	twxAjax('Thing.Fn.DataSearch', 'QueryDataSearchCount', parmas, true, cb_success, cb_error);
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//加载表格数据
function loadTable() {
	//渲染表格
	renderTable();
	//初始化分页组件
	initPagination({
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}

//搜索表格数据
function searchTable() {
	pageOptions.pageNumber = 1;
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}


//加载数据类型列表
function loadDataSearchTypeCombobox() {
	twxAjax('Thing.Fn.SystemDic', 'getDataSearchType', '', true, function(data) {
		var deaflutValue = data.rows[0].KEY;
		$("#dataType").combobox({
			valueField: 'KEY',
			textField: 'NAME',
			panelHeight: 'auto',
			data: data.rows,
			editable: false,
			onLoadSuccess: function() {
				$("#dataType").combobox('setValue', deaflutValue);
			},
			onSelect: function(record) {
				selTableType = record.KEY;
				selTableName = record.NAME;
				loadTable();
			}
		});
	});
}
//置空查询条件
function resetForm() {
	$("#dataModel").textbox('setValue', '');
	$("#dataName").textbox('setValue', '');
}
//导出excel
function exportExcel() {
	layui.use(['layer'], function() {
		var layer = layui.layer;
		var loading;
		var url = fileHandlerUrl + "/table/export/excel";
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"tableType": selTableType,
				"tableName": selTableName,
				"dataModel": $("#dataModel").val(),
				"dataName": $("#dataName").val()
			},
			prepareCallback: function(url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function(url) {
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function(url) {
				layer.close(loading);
			},
			failCallback: function(html, url) {
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});
	});
}