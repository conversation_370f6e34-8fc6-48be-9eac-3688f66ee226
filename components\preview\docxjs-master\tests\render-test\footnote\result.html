<!--docxjs library predefined styles--><style>
.docx-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.docx-wrapper>section.docx { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }
.docx { color: black; hyphens: auto; text-underline-position: from-font; }
section.docx { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.docx>article { margin-bottom: auto; z-index: 1; }
section.docx>footer { z-index: 1; }
.docx table { border-collapse: collapse; }
.docx table td, .docx table th { vertical-align: top; }
.docx p { margin: 0pt; min-height: 1em; }
.docx span { white-space: pre-wrap; overflow-wrap: break-word; }
.docx a { color: inherit; text-decoration: inherit; }
</style><!--docxjs document styles--><style>.docx span {
  font-family: Liberation Serif;
}
.docx p {
}
p.docx_normal {
  text-align: left;
}
p.docx_normal span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
span.docx_style14 {
}
span.docx_style15 {
}
span.docx_style16 {
}
span.docx_style17 {
}
p.docx_style18 {
  margin-top: 12.00pt;
  margin-bottom: 6.00pt;
  text-align: left;
}
p.docx_style18 span {
  font-family: Liberation Sans;
  min-height: 14.00pt;
  font-size: 14.00pt;
  color: black;
}
p.docx_style19 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
  text-align: left;
}
p.docx_style19 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style20 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
  text-align: left;
}
p.docx_style20 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style21 {
  margin-top: 6.00pt;
  margin-bottom: 6.00pt;
  text-align: left;
}
p.docx_style21 span {
  font-style: italic;
  min-height: 12.00pt;
  font-size: 12.00pt;
  font-family: Liberation Serif;
  color: black;
}
p.docx_style22 {
  text-align: left;
}
p.docx_style22 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style23 {
  border-bottom: 0.25pt solid #808080;
  margin-top: 0.00pt;
  margin-bottom: 14.15pt;
  text-align: left;
}
p.docx_style23 span {
  min-height: 6.00pt;
  font-size: 6.00pt;
  font-family: Liberation Serif;
  color: black;
}
p.docx_style24 {
  text-indent: -16.95pt;
  margin-left: 16.95pt;
  text-align: left;
}
p.docx_style24 span {
  min-height: 10.00pt;
  font-size: 10.00pt;
  font-family: Liberation Serif;
  color: black;
}
</style><div class="docx-wrapper"><section class="docx" style="padding: 56.7pt; width: 595.3pt; min-height: 841.9pt;"><article><p class="docx_normal"><span class="docx_style15"><sup>1</sup></span><span lang="en-US">content</span><span></span></p></article><ol><li><p class="docx_style24"><span class="docx_style14"></span><span lang="en-US"><span> </span>footnote</span></p></li></ol></section><section class="docx" style="padding: 56.7pt; width: 595.3pt; min-height: 841.9pt;"><article><p class="docx_normal"><span lang="en-US">content2</span><span class="docx_style15" lang="en-US"><sup>1</sup></span></p></article><ol><li><p class="docx_style24"><span class="docx_style14"></span><span lang="en-US"><span> </span>footnote2</span></p></li></ol></section></div>