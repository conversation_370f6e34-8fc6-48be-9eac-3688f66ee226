//判断长时间未操作就退出登录
var lastTime = new Date().getTime();
var currentTime = new Date().getTime();
var timeOut = 100000 * 60 * 1000; //设置超时时间： 10分钟
$(function() {
	/* 监听鼠标移动事件 */
	$(document).mouseover(function() {
		lastTime = new Date().getTime(); //更新操作时间
	});


	// layui.use(['layer'], function() {
	// 	var layer = layui.layer;
	// 	//检查密码是否可用
	// 	if (!checkPwd(window.atob(sessionStorage.getItem('pwd')))) {
	// 		layer.alert('您的密码强度不足，请立即修改密码，密码必须包含大小写字母、特殊字符、数字中的其中两项，且长度不能少于8位',{ icon: -1, closeBtn: 0 }, function(index){
	// 			updatePwd(true);
	// 			layer.close(index);
	// 		});
	// 	}

	// 	//检查密码是否超过7天
	// 	var updatePwdTime = st.a('updatePwdTime');
	// 	var nowTime = new Date().getTime();
	// 	var sevenDay = 7*24*60*60*1000;
	// 	if(updatePwdTime==undefined||((nowTime-updatePwdTime)>sevenDay)){
	// 		layer.alert('您的密码已经超过7天未修改，请立即修改密码',{ icon: -1, closeBtn: 0 }, function(index){
	// 			updatePwd(true);
	// 			layer.close(index);
	// 		});
	// 	}
	// })

});

function testTime() {
	currentTime = new Date().getTime(); //更新当前时间
	if (currentTime - lastTime > timeOut) { //判断是否超时
		alert("登录超时，请重新登录");
		sessionStorage.clear();
		window.location.href = "login.html";
	}
}

/* 定时器 间隔5秒检测是否长时间未操作页面 */
window.setInterval(testTime, 5000);

//exce导入模板下载
function downloadExcelTpl() {
	layui.use(['layer', 'form'], function() {
		var layer = layui.layer,
			form = layui.form;
		var downloadExcelTplIndex = layer.open({
			title: 'Excel导入模板下载',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ['440px', '180px'],
			content: '<div id="downloadExcelTplContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['确定', '取消'],
			yes: function() {
				$('#downLoadExcelTpl').click();
			},
			btn2: function() {
				return true;
			},
			success: function() {
				var addTpl = $("#excelTplDownloadHtml")[0].innerHTML;
				$("#downloadExcelTplContent").append(addTpl);
			}
		});
		twxAjax("Thing.Fn.SystemDic", "GetDicDataByDicType", {
			typename: "Excel导入类型"
		}, false, function(data) {
			for (var i = 0; i < data.rows.length; i++) {
				$("#excelFileType").append('<option value="' + data.rows[i].KEY + '">' + data.rows[i].NAME + '</option>');
			}
		});

		form.render(null, 'excelTplDownloadForm');

		//监听提交
		form.on('submit(downLoadExcelTpl)', function(data) {
			var path = "C:\\Program Files\\Apache Software Foundation\\Tomcat 8.5\\webapps\\FileHandle\\excelTpl\\";
			path = path + data.field.excelFileType + ".xlsx";
			var name = data.field.excelFileType;
			var url = fileHandlerUrl + "/first/phase/download/file";
			var form = $("<form></form>").attr("action", url).attr("method", "post");
			form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", name));
			form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", path));
			form.appendTo('body').submit().remove();
			layer.close(downloadExcelTplIndex);
			return false;
		});
	});
}
//修改密码
function updatePwd(isForce) {
	var closeBtn = 1;
	var btn = ['确定', '取消'];
	if (isForce) {
		closeBtn = 0;
		btn = ['确定'];
	}
	layui.use(['layer', 'form'], function() {
		var layer = layui.layer,
			form = layui.form;
		layer.open({
			title: '修改密码',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeBtn: closeBtn,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ['340px', '280px'],
			content: '<div id="setPassContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: btn,
			yes: function() {
				$('#setmypass').click();
			},
			btn2: function() {
				return true;
			},
			success: function() {
				var addTpl = $("#updatePwdHtml")[0].innerHTML;
				$("#setPassContent").append(addTpl);
			}
		});

		form.verify({
			oldPassword: function(value, item) {
				if (sessionStorage.getItem('pwd') !== window.btoa(value)) {
					return '旧密码输入错误';
				}
			},
			checkPwd: function(value, item) {
				if (!checkPwd(value)) {
					return '密码必须包含大小写字母、特殊字符、数字中的两项，且长度不能少于8位';
				}

				if (sessionStorage.getItem('pwd') == window.btoa(value)) {
					return '新密码不能与旧密码相同';
				}
			},
			repass: function(value, item) {
				var password = $("#edit_new_password").val();
				if (value !== password) {
					return '两次密码输入不一致';
				}
			}
		});

		form.render(null, 'setPassForm');

		//监听提交
		form.on('submit(setmypass)', function(data) {
			var pwd = data.field.password;
			twxAjax('Thing.UserLogin', 'setUserPwd', {
				username: sessionStorage.getItem('username'),
				password: pwd
			}, true, function(data) {
				if (data.rows[0].result == 1) {
					layer.closeAll();
					layer.msg('修改成功');
					sessionStorage.setItem("pwd", window.btoa(pwd));
					formReset();
				} else {
					layer.msg('修改失败');
					formReset();
				}
			});
			return false;
		});

		function formReset() {
			form.val("setPassForm", {
				"password": "",
				"oldPassword": "",
				"repassword": ""
			})
		}

	});
}

//轮询手动同步任务状态，如果获取到才完成的任务，则通知用户
function ManualSyncStatusMonitor() {
	//1.调用queryCompletedManualSyncTask方法获取是否存在当前处理人的后台任务；
	var fg_confirming = false;
	var confirmMap = {};

	function queryStatus() {
		twxAjax('Thing.Integration.DataCollect', 'queryCompletedManualSyncTask', {
			userId: sessionStorage.getItem('username')
		}, true, cb_sucess, cb_err);
	}

	function cb_sucess(data) {
		if (data.message) {
			console.log(data.message);
		}
		if (!fg_confirming && data.completed > 0) {
			fg_confirming = true; //防止不停的弹框
			var strIdList = JSON.stringify(data.idList).replace("[", "").replace("]", "");
			layer.confirm(data.message, {
				btn: ['确定'] //按钮
			}, function(index) {
				postConfirm(strIdList);
				layer.close(index);
			});
		}
		if (data.running == 0) {
			//没有活动任务了 不再轮询
			window.clearInterval(pollingTimer);
		}
	}

	function postConfirm(idList) {
		twxAjax('Thing.Integration.DataCollect',
			'confirmManualSyncStatus', {
				idList: idList
			}, true,
			function(data) {
				fg_confirming = false;
				console.log("确认完成[成功]:" + data);
			},
			function(data) {
				fg_confirming = false;
				cb_err(data);
			});
	}

	function cb_err(data) {
		console.log("操作出错:" + data);
	}

	this.loop = function() {
		pollingTimer = window.setInterval(queryStatus, 1000);
	}
	this.checkRunningManualSyncTask = function(treeId) {
		var isRunning = false;
		twxAjax('Thing.Integration.DataCollect',
			'queryRunningManualSyncTask', {
				treeId: treeId
			}, false,
			function(data) {
				if (!data.completed) {
					isRunning = true;
					layer.confirm(data.message, {
						btn: ['确定']
					}, function(index) {
						layer.close(index);
					});
				}
			},
			function(data) {
				console.warn(data);
			});
		return isRunning;
	}

}

//每次刷新页面都会执行
var manualSyncStatusMonitor = new ManualSyncStatusMonitor();
manualSyncStatusMonitor.loop();

clearUserEdit(function() {});