// 保存全局筛选条件
var globalSearchParams = {
	category: '',
	fileName: '',
	creator: '',
	startDate: '',
	endDate: '',
	treeId: ''
};

// 原始表格高度
var originalTableHeight = 0;

function getBtnHtml(btns) {
	// 左侧搜索筛选区域
	var $searchContainer = $(
		'<div class="layui-inline search-inline-container">' +
			'<div class="layui-inline" style="margin-right: 4px;">' +
				'<div class="layui-input-inline" style="width: 120px;">' +
					'<select name="category" lay-filter="category">' +
						'<option value="">类别</option>' +
					'</select>' + 
				'</div>' +
			'</div>' +
			'<div class="layui-inline" style="margin-right: 4px;">' +
				'<input type="text" name="fileName" placeholder="文件名称" autocomplete="off" class="layui-input" style="width: 220px;">' +
			'</div>' +
			'<div class="layui-inline" style="margin-right: 4px;">' +
				'<input type="text" name="creator" placeholder="创建人" autocomplete="off" class="layui-input" style="width: 100px;">' + 
			'</div>' +
			'<div class="layui-inline" style="margin-right: 4px;">' +
				'<input type="text" name="startDate" id="search-start-date" placeholder="开始日期" autocomplete="off" class="layui-input" style="width: 110px;">' +
			'</div>' +
			'<div class="layui-inline" style="margin-right: 0;">' +
				'<span style="margin: 0 2px;">-</span>' +
			'</div>' +
			'<div class="layui-inline" style="margin-right: 4px; margin-left: 0;">' +
				'<input type="text" name="endDate" id="search-end-date" placeholder="结束日期" autocomplete="off" class="layui-input" style="width: 110px;">' +
			'</div>' +
			'<div class="layui-inline" style="margin-right: 6px;">' +
				'<button class="layui-btn" lay-submit lay-filter="searchBtn"><i class="layui-icon layui-icon-search"></i> 搜索</button>' +
			'</div>' +
			'<div class="layui-inline">' +
				'<button type="reset" class="layui-btn layui-btn-primary" id="resetSearchBtn"><i class="layui-icon layui-icon-refresh"></i> 重置</button>' +
			'</div>' +
		'</div>'
	);

	// 左右两侧按钮区域
	var $leftContainer = $('<div class="layui-btn-container layui-inline" style="margin-left: 10px;"></div>');
	var $rightContainer = $('<div class="layui-btn-container layui-inline" style="float:right;"></div>');
	
	for (var i = 0; i < btns.length; i++) {
		var btn = btns[i];
		var $btn = $('<button id="' + btn.id + '" class="layui-btn layui-btn-sm ' + btn.css + '" lay-event="' + btn.event + '">\
								<i class="layui-icon layui-icon-' + btn.icon + '"></i> ' + btn.name + '\
							</button>');
		
		if (btn.position === 'right') {
			$rightContainer.append($btn);
		} else {
			$leftContainer.append($btn);
		}
	}
	
	var $wrapper = $('<div class="layui-form layui-inline-block layui-search-area" style="width:100%;overflow:hidden;"></div>')
		.append($searchContainer)
		.append($leftContainer)
		.append($rightContainer);
	
	return $wrapper[0].outerHTML;
}

function reloadTable(treeId) {
	// 获取表单中的搜索条件
	var category = $('select[name="category"]').val();
	var fileName = $('input[name="fileName"]').val();
	var creator = $('input[name="creator"]').val();
	var startDate = $('#search-start-date').val();
	var endDate = $('#search-end-date').val();
	
	// 更新全局筛选条件
	globalSearchParams = {
		category: category || '',
		fileName: fileName || '',
		creator: creator || '',
		startDate: startDate || '',
		endDate: endDate || '',
		treeId: treeId
	};
	
	// 打印调试信息
	console.log("搜索条件:", globalSearchParams);
	
	table.reloadData('file-table', {
		where: {
			treeId: treeId,
			category: globalSearchParams.category,
			fileName: globalSearchParams.fileName,
			creator: globalSearchParams.creator,
			startDate: globalSearchParams.startDate,
			endDate: globalSearchParams.endDate
		},
		scrollPos: 'fixed',
		done: function() {
			// 表格重载完成后，重新设置表单值和渲染表单
			setTimeout(function() {
				restoreSearchFormValues();
			}, 100);
		}
	});
}

/**
 * 重新设置表单值并渲染
 */
function restoreSearchFormValues() {
	$('select[name="category"]').val(globalSearchParams.category);
	$('input[name="fileName"]').val(globalSearchParams.fileName);
	$('input[name="creator"]').val(globalSearchParams.creator);
	$('#search-start-date').val(globalSearchParams.startDate);
	$('#search-end-date').val(globalSearchParams.endDate);
	
	// 重新渲染表单
	form.render('select');
}

function confirmFiles(treeId, ids) {
	layer.confirm("确认之后文件无法删除，是否继续？", {
		icon: 3,
		title: '提示'
	}, function(index) {
		twxAjax(THING, 'Confirm', {
			reviewer: sessionStorage.getItem('username'),
			ids: ids
		}, true, function(res) {
			if (res.success) {
				reloadTable(treeId);
				layer.msg(res.msg)
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	});
}

function deleteFiles(treeId, ids) {
	layer.confirm("确认要删除吗？", {
		icon: 3,
		title: '提示'
	}, function(index) {
		twxAjax(THING, 'Delete', {
			optUser: sessionStorage.getItem('username'),
			ids: ids
		}, true, function(res) {
			if (res.success) {
				reloadTable(treeId);
				layer.msg(res.msg)
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	});
}

/**
 * 导出数据包
 * @param {Object} treeId
 */
function exportDataPackage(treeId) {
	var url = fileHandlerUrl + "/test/evaluation/export/pkg";
	var loading = layer.msg('正在导出中', {
		icon: 16,
		shade: 0.01
	});
	$.fileDownload(url, {
		httpMethod: 'POST',
		data: {
			"treeId": treeId
		},
		prepareCallback: function(url) {},
		abortCallback: function(url) {
			layer.close(loading);
			layer.msg("数据包导出异常！！");
		},
		successCallback: function(url) {
			layer.close(loading);
		},
		failCallback: function(html, url) {
			layer.close(loading);
			layer.msg("数据包导出失败！！");
		}
	});
}

/**
 * 获取选中数据的id集合
 */
function getCheckIds(successFn) {
	var checkStatus = table.checkStatus('file-table');
	var checkDatas = checkStatus.data;
	if (checkDatas.length == 0) {
		layer.alert("至少选择一条数据！", {
			icon: 2
		});
	} else {
		var idArr = [];
		for (var i = 0; i < checkDatas.length; i++) {
			idArr.push(checkDatas[i].ID_);
		}
		successFn(idArr.join(","));
	}
}

/**
 *校验选中的数据中是否存在已确认的数据
 */
function checkDatasHasReview() {
	var hasReview = false;
	var checkStatus = table.checkStatus('file-table');
	var checkDatas = checkStatus.data;
	for (var i = 0; i < checkDatas.length; i++) {
		if (checkDatas[i].STATUS_ == "1") {
			hasReview = true;
			break;
		}
	}
	return hasReview;
}

/**
 * 加载文件表格
 */
function renderFileTable(treeId, allowUpload) {
	// 重置表格高度，避免累积减小
	if (originalTableHeight === 0) {
		// 第一次加载时保存原始高度
		originalTableHeight = $("body").height() - 5;
	}
	tableHeight = originalTableHeight;

	var tableAlign = 'center';
	var cols = [
		[{
			type: "checkbox",
			fixed: 'left'
		}, {
			width: 60,
			align: tableAlign,
			title: '序号',
			type: 'numbers',
			fixed: 'left'
		}, {
			field: 'CATEGORY_',
			title: '类别',
			align: tableAlign,
			width: 110
		}, {
			field: 'NAME_',
			title: '文件名称',
			align: 'left'
		}, {
			field: 'CREATOR_',
			title: '创建人',
			align: tableAlign,
			width: 80
		}, {
			field: 'CREATE_TIME_',
			title: '创建时间',
			align: tableAlign,
			width: 160
		}, {
			field: 'FILE_FORMAT_',
			width: 88,
			align: tableAlign,
			title: '文件类型'
		}, {
			field: 'SECURITY_',
			width: 80,
			align: tableAlign,
			title: '密级',
			templet: function(d) {
				return getSecurityName(d.SECURITY_);
			}
		}, {
			field: 'REVIEWER_',
			width: 80,
			align: tableAlign,
			title: '审核人'
		}, {
			field: 'REVIEW_TIME_',
			width: 160,
			align: tableAlign,
			title: '审核时间'
		}, {
			field: 'STATUS_',
			align: tableAlign,
			width: 80,
			title: '状态',
			templet: function(d) {
				if (d.STATUS_ == 1) {
					return '已确认';
				} else {
					return '未确认';
				}
			}
		}, {
			field: 'operation',
			align: tableAlign,
			title: '操作',
			width: 220,
			fixed: "right",
			templet: function(d) {
				var deleteHtml = "";
				var confirmHtml = "";
				if (d.STATUS_ == 1) {
					deleteHtml = '<a class="layui-btn layui-btn-xs layui-bg-red layui-btn-disabled">删除</a>';
					confirmHtml = '<a class="layui-btn layui-btn-xs layui-bg-orange layui-btn-disabled">确认</a>';
				} else {
					deleteHtml = '<a class="layui-btn layui-btn-xs layui-bg-red" lay-event="delete">删除</a>';
					confirmHtml = '<a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="confirm">确认</a>';
				}
				return '<div class="layui-clear-space">\
					<a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="download">下载</a>\
					<a class="layui-btn layui-btn-xs " lay-event="preview">预览</a>\
				 ' + deleteHtml + confirmHtml + '</div>';
			}
		}]
	];
	var toolbar = false;
	var tools = [{
		name: "删除",
		css: "layui-bg-red",
		icon: "delete",
		event: "delete",
		id: "delete-btn"
	}, {
		name: "确认",
		css: "layui-bg-orange",
		icon: "auz",
		event: "confirm",
		id: "confirm-btn"
	}];
	if (allowUpload) {
		tools.unshift({
			name: "上传",
			css: "layui-bg-blue",
			icon: "add-1",
			event: "add",
			id: "upload-btn"
		});
		tools.push({
			name: "导出数据包",
			css: "",
			icon: "export",
			event: "export",
			id: "export-btn"
		});
	}
	// 添加数据看板按钮
	tools.push({
		name: "数据看板",
		css: "layui-bg-green",
		icon: "chart",
		event: "dashboard",
		id: "dashboard-btn",
		position: 'right'  // 指定按钮位置在右边
	});
	var toolbar = getBtnHtml(tools);
	
	// 保存当前treeId到全局变量，以便后续刷新使用
	globalSearchParams.treeId = treeId;
	
	// 先渲染表格
	table.render({
		elem: '#file-table',
		url: getUrl(THING, 'QueryTestFile'),
		toolbar: toolbar,
		defaultToolbar: [],
		where: {
			treeId: treeId,
			// 应用当前的筛选条件
			category: globalSearchParams.category,
			fileName: globalSearchParams.fileName,
			creator: globalSearchParams.creator,
			startDate: globalSearchParams.startDate,
			endDate: globalSearchParams.endDate
		},
		cellMinWidth: 80,
		height: tableHeight,
		page: {
			layout: ['limit', 'count', 'prev', 'page', 'next', 'refresh', 'skip'],
			limit: 20,
			limits: [10, 20, 30, 40, 50, 100],
			groups: 1,
			first: false,
			last: false
		},
		cols: cols,
		done: function() {
			// 表格渲染完成后再初始化搜索组件
			initSearchComponents(treeId);
		}
	});

	// 工具栏事件
	table.on('toolbar(file-table)', function(obj) {
		var event = obj.event;
		if (event === 'add') {
			uploadFile(treeId);
		} else if (event === 'confirm') {
			getCheckIds(function(ids) {
				confirmFiles(treeId, ids);
			});
		} else if (event === 'delete') {
			getCheckIds(function(ids) {
				deleteFiles(treeId, ids);
			});
		} else if (event === 'export') {
			exportDataPackage(treeId);
		} else if (event === 'dashboard') {
			// 在新标签页打开数据看板页面
			window.open('../testEvaluationScreen/testEvaluationScreen.html', '_blank');
		}
	});
	//行内事件
	table.on('tool(file-table)', function(obj) {
		var data = obj.data;
		if (obj.event === 'preview') {
			var filePath = data.FILE_PATH_ || '';
			if (filePath) {
				previewfile({
					FILEPATH: filePath,
					FILE_NAME: data.NAME_,
					FILE_FORMAT: data.FILE_FORMAT_,
					FILE_TYPE: "",
					GATHERING_METHOD: ""
				});
			} else {
				layer.alert('未发现文件地址！', {
					icon: 2
				});
			}
		} else if (obj.event === 'delete') {
			deleteFiles(treeId, data.ID_);
		} else if (obj.event === 'download') {
			var url = getFileDownloadUrl({
				FILEPATH: data.FILE_PATH_,
				FILE_NAME: data.FILE_NAME_,
				SECURITY_LEVEL_NAME: getSecurityName(data.SECURITY_),
				FILE_FORMAT: data.FILE_FORMAT_
			});
			window.open(url);
		} else if (obj.event === 'confirm') {
			confirmFiles(treeId, data.ID_);
		}
	});

	// 复选框事件
	table.on('checkbox(file-table)', function(obj) {
		if (checkDatasHasReview()) {
			$("#delete-btn").addClass("layui-btn-disabled");
			$("#confirm-btn").addClass("layui-btn-disabled");
		} else {
			$("#delete-btn").removeClass("layui-btn-disabled");
			$("#confirm-btn").removeClass("layui-btn-disabled");
		}
	});
}

/**
 * 初始化搜索组件
 */
function initSearchComponents(treeId) {
	// 因为表单元素更小，表格高度需要调整，但只调整一次，不累积
	tableHeight = tableHeight - 50;
	
	// 确保在DOM元素渲染完成后再初始化日期选择器和绑定事件
	setTimeout(function() {
		// 初始化日期选择器，使用yyyy-MM-dd格式
		layui.laydate.render({
			elem: '#search-start-date',
			type: 'date',
			format: 'yyyy-MM-dd'
		});
		layui.laydate.render({
			elem: '#search-end-date',
			type: 'date',
			format: 'yyyy-MM-dd'
		});
		
		// 加载类别下拉框数据
		twxAjax(THING, 'GetCategories', {}, true, 
			function(res) {
				if (res.success) {
					var categories = res.data || [];
					var $select = $('select[name="category"]');
					$select.empty().append('<option value="">类别</option>');
					
					for (var i = 0; i < categories.length; i++) {
						$select.append('<option value="' + categories[i] + '">' + categories[i] + '</option>');
					}
					
					form.render('select');
					
					// 恢复筛选条件
					restoreSearchFormValues();
				}
			}
		);
		
		// 绑定搜索按钮事件
		form.on('submit(searchBtn)', function(data) {
			reloadTable(treeId);
			return false;
		});
		
		// 绑定重置按钮事件
		$('#resetSearchBtn').off('click').on('click', function() {
			// 清空全局筛选条件
			globalSearchParams = {
				category: '',
				fileName: '',
				creator: '',
				startDate: '',
				endDate: '',
				treeId: treeId
			};
			
			// 清空表单值
			$('.search-inline-container input').val('');
			$('.search-inline-container select').val('');
			form.render('select');
			
			// 重新加载表格
			reloadTable(treeId);
			return false;
		});
	}, 100);
}