//加载树结构
function loadTree() {
	var cb_success = function(data) {
		var datas = data.rows;
		if (datas.length > 0) {
			datas = dealDataIcons(datas);
			datas = dealDataNodeName(datas);
			treeSetting.callback.onClick = function(event, treeId, treeNode) {
				var tree_name = getNodeName(treeNode.NODENAME);
				treeName = tree_name;
				// if (treeName == '一般结构件') {
				// 	$("#tableTable_tb").hide();
				// 	$('#' + tableId).datagrid("getPanel").hide();
				// 	$("#tableTableMsg").show().text('特殊节点无需配置！');
				// 	$('#' + paramId).datagrid("getPanel").hide();
				// 	$("#paramTableMsg").text('特殊节点无需配置！').show();
				// } else {
				$("#tableTable_tb").show();
				$('#' + tableId).datagrid("getPanel").show();
				$("#tableTableMsg").hide();
				$('#' + paramId).datagrid("getPanel").show();
				$("#paramTableMsg").hide();
				loadTable(treeName);
				// }
			};
			treeSetting.callback.onExpand = function(event, treeId, treeNode) {

			};
			//禁止拖拽
			treeSetting.callback.beforeDrag = function() {
				return false;
			};
			treeSetting.callback.beforeDrop = function() {
				return false;
			};
			treeSetting.async = {
				enable: false
			};
			ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
			ztreeObj.expandAll(true);
			// loadTreeMenu();
			// var nodes = ztreeObj.getNodes();
			// for (var i = 0; i < nodes.length; i++) { //设置节点展开
			// 	ztreeObj.expandAll(nodes[i], true, false, true);
			// }
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax(THING, 'QueryTemplateTree', '', true, cb_success);
}

//加载树节点右键菜单
function loadTreeMenu() {
	$("#dpTree a").each(function(i, n) {
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		if ((node.NODETYPE == 'leaf') || (node.NODETYPE == 'dir') && (!node.ISPARENT)) {
			var menu = [{
				text: "上传质量报告模板",
				icon: '../dataTree/images/upload.png',
				callback: function() {
					uploadReportTpl(getNodeName(node.NODENAME));
				}
			}, {
				text: "下载质量报告模板",
				icon: '../dataTree/images/download.png',
				callback: function() {
					downloadReportTpl(getNodeName(node.NODENAME));
				}
			}];
			$(n).contextMenu({
				width: 155,
				menu: menu,
				target: function(ele) {
					var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
					ztreeObj.selectNode(node, false, true);
				}
			});
		}

	});
}