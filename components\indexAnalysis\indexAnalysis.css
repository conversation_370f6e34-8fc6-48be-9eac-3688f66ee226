.checkbox {
	position: relative;
	border: 2px solid #5fb878;
	-moz-border-radius: 5px 5px 5px 5px;
	-webkit-border-radius: 5px 5px 5px 5px;
	border-radius: 5px 5px 5px 5px;
}

.checkbox-checked {
	border: 0;
	background: #5fb878;
}

.div-input {
	float: left;
	margin-right: 20px;
}

.icon-select {
	background-image: url(../../img/select.png) !important;
	background-size: 16px 16px;
}

.icon-analysis {
	background-image: url(../../img/analysis_b.png) !important;
	background-size: 16px 16px;
}

.icon-search {
	background-image: url(../../img/search.png) !important;
	background-size: 16px 16px;
}

.icon-pull-right {
	background-image: url(../../img/pull_right.png) !important;
	background-size: 16px 16px;
}

.icon-clear {
	background-image: url(../../img/clear1.png) !important;
	background-size: 16px 16px;
}

.layui-row {
	margin-top: 15px;
}

.textbox-label {
	width: auto;
}

.index-ck {
	float: left;
	margin-bottom: 15px;
	margin-right: 15px;
}

#select-layout .datagrid-header {
	border-color: #000000;
	border-width: 1px 0 1px 0;
}

#select-layout .datagrid-header td,
#select-layout .datagrid-body td {
	border-color: #000000;
}

#select-layout .datagrid-header td {
	font-weight: 600;
}

#select-layout .datagrid-header,
#select-layout .datagrid-td-rownumber {
	background-color: transparent !important;
}

.table-row-a {
	color: blue;
	cursor: pointer;
}
