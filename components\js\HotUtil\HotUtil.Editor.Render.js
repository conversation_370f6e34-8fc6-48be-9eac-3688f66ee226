/**
 * HotUtil.Editor.Render.js - Handsontable 编辑器工具库 (渲染模块)
 *
 * 负责 Handsontable 实例的创建、配置和渲染。
 */

/**
 * 编辑表格初始化
 * @param {string} saveData
 * @param {Object} treeNode
 * @param {string} tableHeader
 * @param {Function} afterChange
 */
HotUtil.renderHot = function (saveData, treeNode, tableHeader, afterChange) {
    var fixedRowsTop = HotUtil.getTableHeader(tableHeader).max;
    var initData = [];
    var merged = [];
    var metas = [];
    var colWidths = [];
    if (saveData) {
        saveData = JSON.parse(saveData);
        initData = saveData.tableData;
        merged = saveData.merged;
        colWidths = saveData.colWidths || void 0;
        metas = saveData.meta || [];
    } else {
        var initColNum = 12,
            initRowNum = 15;
        for (var r = 0; r < initRowNum; r++) {
            var rows = [];
            for (var c = 0; c < initColNum; c++) {
                rows.push(null);
            }
            initData.push(rows);
        }
    }

    function myCellRenderer(instance, td, row, col, prop, value, cellProperties) {
        Handsontable.renderers.TextRenderer.apply(this, arguments);
        const stringifiedValue = Handsontable.helper.stringify(value);

        var eles = cellProperties.eles;
        var isText = true;
        var signImgs = [];
        var photoNums = [];
        if (Array.isArray(eles)) {
            for (var i = 0; i < eles.length; i++) {
                isText = false;
                var ele = eles[i];
                var eleType = ele.type;
                if (eleType == "sign") {
                    var src = "/File" + ele.src;
                    var date = ele.date;
                    var $img = $('<br><img type="sign" src="' + src + '" class="sign-img" date="' + date +
                        '"><br><span>' + date + '</span>');
                    signImgs.push($img);
                } else if (eleType == "photo") {
                    photoNums.push(ele.photoShowNum);
                }
            }
        }

        // 处理签署人员信息显示
        var signUsersText = '';
        // 只有签署框类型的单元格才显示签署人员信息
        if (cellProperties.signUsers && Array.isArray(cellProperties.signUsers) &&
            cellProperties.signUsers.length > 0 &&
            cellProperties.className && cellProperties.className.indexOf('sign-box') !== -1) {
            // 过滤已签名的用户
            var unsignedUsers = [];
            for (var j = 0; j < cellProperties.signUsers.length; j++) {
                var user = cellProperties.signUsers[j];
                var isSigned = false;

                // 检查该用户是否已签名
                if (Array.isArray(eles)) {
                    for (var k = 0; k < eles.length; k++) {
                        var ele = eles[k];
                        if (ele.type === "sign" &&
                            ele.signName === user.user_fullname &&
                            ele.workno === user.user_workno) {
                            isSigned = true;
                            break;
                        }
                    }
                }

                // 如果用户未签名，添加到显示列表
                if (!isSigned) {
                    unsignedUsers.push(user.user_fullname);
                }
            }

            // 只有存在未签名用户时才显示
            if (unsignedUsers.length > 0) {
                signUsersText = unsignedUsers.join('，');
            }
        }

        if (!isText || signUsersText) {
            $(td).empty();

            // 显示原始数据
            if (stringifiedValue) {
                $(td).append(stringifiedValue);
            }

            // 显示签名图片
            for (var i = 0; i < signImgs.length; i++) {
                $(td).append(signImgs[i]);
            }

            // 显示照片数量
            if (photoNums.length > 0) {
                $(td).append(HotUtil.dealImgNumShow(photoNums));
            }

            // 显示签署人员信息
            if (signUsersText) {
                var $signUsersDiv = $('<div style="margin-top: 5px; padding: 2px 5px; background-color: #f0f8ff; border-left: 3px solid #1E9FFF; font-size: 12px; color: #666;">' + signUsersText + '</div>');
                $(td).append($signUsersDiv);
            }
        }
    }

    var container = document.getElementById('handsontable');

    // 增强错误处理：检查容器是否存在
    if (!container) {
        console.error('Handsontable容器元素未找到');
        layer.alert('表格容器初始化失败，请刷新页面重试', {
            icon: 2,
            title: '初始化错误'
        });
        return;
    }

    // 优化配置：利用16.0.1版本的性能改进
    var handsontableConfig = {
        data: initData,
        fixedRowsTop: fixedRowsTop,
        mergeCells: merged,
        rowHeaders: true,
        colHeaders: true,
        rowHeights: 40,
        dropdownMenu: false,
        customBorders: true,
        comments: true,
        colWidths: colWidths,
        fillHandle: true,
        renderer: myCellRenderer,
        language: 'zh-CN',
        licenseKey: 'non-commercial-and-evaluation',
        className: 'htMiddle htCenter',
        manualColumnResize: true,
        manualRowResize: true,
        afterChange: afterChange,
        contextMenu: {
            callback: function (key, selection, clickEvent) {
                // 增强错误处理
                try {
                    console.log(key, selection, clickEvent);
                } catch (error) {
                    console.error('上下文菜单回调错误:', error);
                }
            },
            items: HotUtil.getContextItems(treeNode)
        }
    };

    // 增强错误处理：Handsontable实例化
    try {
        window.hot = new Handsontable(container, handsontableConfig);

        // 16.0.1版本上下文菜单定位优化：使用afterContextMenuShow钩子
        window.hot.addHook('afterContextMenuShow', function () {
            HotUtil.adjustContextMenuPosition();
        });
    } catch (error) {
        console.error('Handsontable初始化失败:', error);
        layer.alert('表格初始化失败: ' + error.message, {
            icon: 2,
            title: '初始化错误'
        });
        return;
    }

    // 增强错误处理：afterAutofill hook
    window.hot.addHook('afterAutofill', function (fillData, sourceRange, targetRange, direction) {
        try {
            //只有选中一个单元格自动填充才会增加序列
            if ((sourceRange.from.row == sourceRange.to.row) && (sourceRange.from.col == sourceRange.to.col)) {
                var fromRow = targetRange.from.row;
                var fromCol = targetRange.from.col;
                var toRow = targetRange.to.row;
                var toCol = targetRange.to.col;

                if (direction == "down" || direction == "right") {
                    for (var r = fromRow; r <= toRow; r++) {
                        for (var c = fromCol; c <= toCol; c++) {
                            try {
                                if (window.hot.getCellMeta(r, c).readOnly != true) {
                                    var nextValue = "";
                                    if (direction == "down") {
                                        nextValue = window.hot.getDataAtCell(r - 1, c);
                                    } else if (direction == "right") {
                                        nextValue = window.hot.getDataAtCell(r, c - 1);
                                    }
                                    window.hot.setDataAtCell(r, c, HotUtil.incrementString(nextValue, "add"));
                                }
                            } catch (cellError) {
                                console.warn('处理单元格(' + r + ',' + c + ')时出错:', cellError);
                            }
                        }
                    }
                } else {
                    for (var r = toRow; r >= fromRow; r--) {
                        for (var c = toCol; c >= fromCol; c--) {
                            try {
                                if (window.hot.getCellMeta(r, c).readOnly != true) {
                                    var nextValue = "";
                                    if (direction == "up") {
                                        nextValue = window.hot.getDataAtCell(r + 1, c);
                                    } else if (direction == "left") {
                                        nextValue = window.hot.getDataAtCell(r, c + 1);
                                    }
                                    window.hot.setDataAtCell(r, c, HotUtil.incrementString(nextValue, 'subtract'));
                                }
                            } catch (cellError) {
                                console.warn('处理单元格(' + r + ',' + c + ')时出错:', cellError);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('afterAutofill hook执行错误:', error);
        }
    });

    // 增强错误处理：批量设置单元格元数据
    // 16.0.1版本优化：使用批量操作提高性能
    try {
        // hot.suspendRender(); // 16.0.1版本中可选择性使用
        for (var m = 0; m < metas.length; m++) {
            var meta = metas[m];
            if (meta && typeof meta.row === 'number' && typeof meta.col === 'number') {
                try {
                    //将表头添加背景颜色
                    if (meta.row < fixedRowsTop) {
                        if (meta.className) {
                            meta.className = meta.className + " td-bg";
                        } else {
                            meta.className = "td-bg";
                        }
                    }

                    // 安全地设置单元格元数据
                    if (meta.className) {
                        window.hot.setCellMeta(meta.row, meta.col, 'className', meta.className);
                    }
                    if (meta.readOnly) {
                        window.hot.setCellMeta(meta.row, meta.col, 'readOnly', meta.readOnly);
                    }
                    if (meta.eles) {
                        window.hot.setCellMeta(meta.row, meta.col, 'eles', meta.eles);
                    }
                    if (meta.comment) {
                        window.hot.setCellMeta(meta.row, meta.col, 'comment', meta.comment);
                    }
                    if (meta.signUsers) {
                        window.hot.setCellMeta(meta.row, meta.col, 'signUsers', meta.signUsers);
                    }
                } catch (metaError) {
                    console.warn('设置单元格(' + meta.row + ',' + meta.col + ')元数据时出错:', metaError);
                }
            }
        }
        // hot.resumeRender(); // 16.0.1版本中可选择性使用

        // 增强错误处理：渲染操作
        window.hot.render();
    } catch (error) {
        console.error('设置单元格元数据或渲染时出错:', error);
        layer.alert('表格渲染失败，请刷新页面重试', {
            icon: 2,
            title: '渲染错误'
        });
    }
};