var layer, form, laydate, table, element;
var thing = "Thing.Fn.AitScreen";

var username = sessionStorage.getItem("username") || 'adm';


layui.use(['layer', 'form', 'laydate', 'table', 'element'], function () {
	layer = layui.layer;
	form = layui.form;
	laydate = layui.laydate;
	table = layui.table;
	element = layui.element;
	//日期范围
	laydate.render({
		elem: '#date-range', //设置开始日期、日期日期的 input 选择器 //数组格式为 2.6.6 开始新增，之前版本直接配置 true 或任意分割字符即可
		range: ['#start-date', '#end-date']
	});

	// 现场临时处理单导出按钮点击事件
	$('#export-temp-btn').on('click', function () {
		// 获取当前筛选条件
		var treeId = $('#model-select').val() || '-1';
		var startDate = $('#start-date').val() || '';
		var endDate = $('#end-date').val() || '';

		// 打开状态选择弹窗
		layer.open({
			type: 1,
			title: '导出现场临时处理单',
			area: ['400px', '240px'],
			content: '<div class="layui-form" style="padding: 10px 0 0 0;">' +
				'<div class="layui-form-item">' +
				'<label class="layui-form-label">导出状态</label>' +
				'<div class="layui-input-block">' +
				'<input type="radio" name="export-status" value="all" title="全部" checked>' +
				'<input type="radio" name="export-status" value="finished" title="已完成">' +
				'<input type="radio" name="export-status" value="unfinished" title="未完成">' +
				'</div>' +
				'</div>' +
				'<div class="layui-form-item" style="margin-top: 60px; text-align: center;">' +
				'<button type="button" class="layui-btn layui-btn-normal" id="confirm-export-btn">确定</button>' +
				'<button type="button" class="layui-btn layui-btn-primary" id="cancel-export-btn">取消</button>' +
				'</div>' +
				'</div>',
			success: function (layero, index) {
				form.render('radio');

				// 取消按钮
				$('#cancel-export-btn').on('click', function () {
					layer.close(index);
				});

				// 确定按钮
				$('#confirm-export-btn').on('click', function () {
					var status = $('input[name=export-status]:checked').val();
					// 显示loading，并添加文字提示
					var loadingIndex = layer.msg('正在导出,请稍等......', {
						icon: 16,
						shade: 0.01,
						time: 0
					});

					// 调用导出接口
					$.fileDownload(fileHandlerUrl + '/aitScreen/exportTempExcel', {
						httpMethod: 'POST',
						data: {
							treeId: treeId,
							startDate: startDate,
							endDate: endDate,
							status: status
						},
						successCallback: function () {
							layer.close(loadingIndex);
							layer.close(index);
							layer.msg('导出成功', { icon: 1 });
						},
						failCallback: function (responseHtml, url) {
							layer.close(loadingIndex);
							layer.msg('导出失败', { icon: 2 });
							console.error('导出失败:', responseHtml);
						}
					});
				});
			}
		});
	});
});

function loadAll(treeId, startDate, endDate) {
	for (var i = 0; i < handlerList.length; i++) {
		var handlerObj = handlerList[i];
		loadHandleChart(treeId, startDate, endDate, handlerObj.fileType, handlerObj.chartId, handlerObj.service);
	}
}

loadAll(-1, "", "");