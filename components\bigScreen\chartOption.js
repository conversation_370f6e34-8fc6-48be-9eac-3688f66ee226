function getBarChartOption(name) {
	var option = {
		tooltip: {
			formatter: function(params) {
				return '<div style="margin: 0px 0 0;line-height:1;">\
								<div style="margin: 0px 0 0;line-height:1;">\
									' + params.marker + '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' + params.name.split("~~~")[0] + '</span>\
									<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">' + params.value + '</span>\
									<div style="clear:both"></div>\
								</div>\
								<div style="clear:both"></div>\
							</div>';
			}
		},
		xAxis: {
			type: 'category',
			data: [],
			axisLabel: {
				rotate: 30,
				// 设置标签的显示方式
				formatter: function(value) {
					// 将标签文本截断为4个字符
					return value.split("~~~")[0];
				}
			},
			// 设置坐标轴的样式
			axisLine: {
				lineStyle: {
					color: 'white'
				}
			}
		},
		yAxis: {
			name: name + "数量",
			nameRotate: 90,
			nameGap: 60,
			nameLocation: 'center',
			nameTextStyle: {
				fontSize: 14,
				color: "white"
			},
			// 设置坐标轴的样式
			axisLine: {
				lineStyle: {
					color: 'white'
				}
			},
			axisLabel: {
				fontSize: 14
			},
			type: 'value'
		},
		series: [{
			data: [],
			type: 'bar',
			label: {
				show: true,
				position: 'top',
				textStyle: {
					color: 'white'
				}
			},
			itemStyle: {
				color: '#63B7EC'
			},
			barWidth: 40
		}]
	};

	return option;
}