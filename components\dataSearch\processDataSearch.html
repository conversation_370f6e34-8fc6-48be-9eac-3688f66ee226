<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<link rel="Shortcut Icon" href="../../img/favicon.ico">
		<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all">
		<!-- <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css"> -->

		<!-- <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->

		<link rel="stylesheet" href="../../css/icon.css">

		<!-- <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
		<script src="../../plugins/layui-lasted/layui.js"></script>

		<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
		<link rel="stylesheet" href="../../plugins/preview/preview.css">

		<script src="../../plugins/easyui/jquery.min.js"></script>
		<script src="../../plugins/preview/preview.js"></script>
		<script src="../../plugins/preview/jquery.rotate.min.js"></script>

		<script src="../../plugins/easyui/jq-signature.js"></script>
		<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
		<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

		<!-- <script>
		sessionStorage.setItem('secLevel','1');
		sessionStorage.setItem('username','adm');
		sessionStorage.setItem('roleid','4,29');
	</script> -->

		<script src="../js/config/twxconfig.js"></script>
		<script src="../js/util.js"></script>

		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">
		<script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
		<script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
		<script type="text/javascript" src="../dataTree/tree.js"></script>
		<script type="text/javascript" src="../dataTree/bom_tree.js"></script>
		<!-- <script type="text/javascript" src="../../plugins/loading/jquery.loading.min.js"></script> -->

		<!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
		<script type="text/javascript" src="../js/logUtil.js"></script>
		<style type="text/css">
			.layui-form-label {
				font-family: '微软雅黑';
				font-size: 14px;
				width: 90px;
			}

			.layui-btn-mycolor2 {
				background-color: #99c1fc;
			}

			.layui-btn-mycolor3 {
				background-color: #FE909D;
			}

			.layui-btn-mycolor4 {
				background-color: #c078fe;
			}

			.layui-btn-mycolor5 {
				background-color: #FBA0A0;
			}

			.layui-btn-mycolor6 {
				background-color: #4a7189;
			}

			.table-row-a {
				color: blue;
				cursor: pointer;
			}

			.td-bold-left {
				font-weight: bold !important;
			}

			.original-table .datagrid-header {
				border-color: #000000;
				border-width: 1px 0 1px 0;
			}

			.original-table .datagrid-header td,
			.original-table .datagrid-body td {
				border-color: #000000;
			}

			.original-table .datagrid-header td {
				font-weight: 600;
			}

			.original-table .datagrid-header,
			.original-table .datagrid-td-rownumber {
				background-color: transparent !important;
			}

			.my-table td,
			.my-table th,
			.my-table .layui-table-col-set,
			.my-table .layui-table-fixed-r,
			.my-table .layui-table-grid-down,
			.my-table .layui-table-header,
			.my-table .layui-table-page,
			.my-table .layui-table-tips-main,
			.my-table .layui-table-tool,
			.my-table .layui-table-total,
			.my-table .layui-table-view,
			.my-table .layui-table[lay-skin=line],
			.my-table .layui-table[lay-skin=row] {
				border-color: #333;
			}

			.my-table td {
				font-family: 宋体;
				font-size: 18px;
				color: black;
			}

			.table-name {
				font-size: 20px !important;
				font-weight: bold;
			}

			.table-head {
				font-weight: bold;
			}

			.sign-img {
				border: 1px solid #aaa;
				box-shadow: 0px 0px 5px 1px #aaa;
				width: 300px;
				height: 150px;
				margin-right: 10px;
				margin-top: 10px;
			}

			/* 弹窗不加载滚动条 */
			/* .layui-layer-page .layui-layer-content{
			overflow: visible !important;
		} */
		</style>
		<title>过程结构树查询</title>
	</head>
	<body>
		<!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
		<!--[if lt IE 9]>
		  <script src="../../plugins/html5/html5.min.js"></script>
		  <script src="../../plugins/html5/respond.min.js"></script>
		<![endif]-->
		<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
			<div id="p" data-options="region:'west',split:true" title="数据包结构树" style="width:350px;padding:10px">
				<ul id="dpTree" class="ztree"></ul>
			</div>
			<div data-options="region:'center'">
				<div id="root_layout_tabs" class="easyui-tabs" data-options="fit:true,border:false" style="width:100%;height:100%;">
					<div title="质量数据" id="product_list_table" class="original-table" data-options="name:'产品质量',tableId:'product_list_table',fit:true,iconCls:'product_list'" style="display:none;padding-top: 10px;">
						<div id="msg" style="float: left; width: 100%;color: red;padding-left: 15px;padding-top: 15px;">请选择过程节点查询!</div>
						<div style="float: left; width: 100%;">
							<div id="productTypeDiv" style="display: none;margin-left:15px;margin-bottom: 10px; float: left;">
								<input id="productType">
							</div>
							<div id="tbr" style="display: none;float: left;">
								<!-- <button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor1" id="product-quality-upload">
									<i class="layui-icon">&#xe681;</i> 上传三级表
								</button> -->
								<!-- <button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-warm" id="product-quality-download">
									<i class="layui-icon">&#xe601;</i> 下载三级表模板
								</button> -->
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor1" id="upload_23_file">
									上传二、三级表<i class="layui-icon layui-icon-down"></i>
								</button>
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-warm" id="download_23_file">
									下载二、三级表模板<i class="layui-icon layui-icon-down"></i>
								</button>

								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-normal" id="product-quality-export">
									<i class="layui-icon">&#xe601;</i> 导出二级表
								</button>
								<!-- <button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-normal" id="export-all-dlw">
									<i class="layui-icon">&#xe601;</i> 导出全部
								</button> -->
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor2" id="product-quality-manualSync">
									<i class="layui-icon">&#xe669;</i> 手动同步
								</button>
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor3" id="product-quality-reassociation">
									<i class="layui-icon">&#xe669;</i> 重新关联
								</button>
								<!-- {{ wanghq: Add - 新增一键关联按钮，支持批量处理当前节点下的所有数据 }} -->
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor3" id="product-quality-batch-reassociation">
									<i class="layui-icon">&#xe669;</i> 一键关联
								</button>
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor4" id="product-quality-productLink">
									<i class="layui-icon">&#xe669;</i> 产品结构树关联
								</button>
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor5" func="func-delete-quality-data" id="product-quality-delete">
									<i class="layui-icon">&#xe640;</i> 删除
								</button>
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor6" id="product-quality-confirm">
									<i class="layui-icon">&#xe672;</i> 确认
								</button>
							</div>
						</div>
						<div id="secondTableDiv" style="float: left; width: 100%;">
							<!-- 显示二级表表格 -->
							<div id="secondTable" style="float: left;" data-options="border:false"></div>
						</div>

						<!-- 显示三级表excel -->
						<div id="threeExcel"></div>

						<div id="otherTableDiv" style="float: left; width: 100%;">
							<!-- 显示其他质量表表格 -->
							<div id="otherTable" style="float: left;" data-options="border:false"></div>
						</div>

						<!-- <div id="electronic_components_table" data-options="border:false"></div>
					<div id="cable_insulation_test_table" data-options="border:false"></div>
					<div id="heating_element_reinspection_table" data-options="border:false"></div>
					<div id="heating_circuit_test_table" data-options="border:false"></div>

					<div id="StandAlong_table" data-options="border:false"></div>
					<div id="ConnectorOnOff_table" data-options="border:false"></div>
					<div id="ConnectorOnOffTimes_table" data-options="border:false"></div>
					<div id="Heater_table" data-options="border:false"></div>
					<div id="HeatResist_table" data-options="border:false"></div>
					<div id="LayersOnOff_table" data-options="border:false"></div>
					<div id="StructuralAssembly_table" data-options="border:false"></div> -->
					</div>
					<div title="影像记录" data-options="name:'影像记录',queryType:'photo',tableId:'photo_table',fit:true,iconCls:'photo-icon'" style="display:none;">
						<div id="photo_table" data-options="border:false"></div>
					</div>
					<div title="设计类要求" data-options="name:'设计类',queryType:'design',tableId:'design_list_table',fit:true,iconCls:'design_list'" style="display:none;">
						<div id="design_list_table" data-options="border:false"></div>
					</div>
					<div title="工艺类要求" data-options="name:'工艺类',queryType:'craft',tableId:'craft_list_table',fit:true,iconCls:'gongyi_list'" style="display:none;">
						<div id="craft_list_table" data-options="border:false"></div>
					</div>
					<div title="过程控制记录" data-options="name:'过程控制',queryType:'process',tableId:'processcontrol_list_table',fit:true,iconCls:'guocheng_list'" style="display:none;">
						<div id="processcontrol_list_table" data-options="border:false"></div>
					</div>
					<div title="质量综合信息" data-options="name:'质量综合',queryType:'quality',tableId:'quanlitycontrol_list_table',fit:true,iconCls:'quanlity_list'" style="display:none;">
						<div id="quanlitycontrol_list_table" data-options="border:false"></div>
					</div>
				</div>
			</div>
		</div>
	</body>
</html>
<script>
	handleFuncBtn();
</script>

<script src="../../plugins/index/jquery.fileDownload.js"></script>
<script src="dataSearchTable.js"></script>
<script src="tableCol.js"></script>
<script src="productList.js"></script>
<script src="processDataSearch.js"></script>

<script type="text/html" id="uploadHtml">
	<form class="layui-form" lay-filter="uploadForm">
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
					<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="selectedFile" style="display: none;">
			<label class="fieldlabel layui-form-label">已选文件:</label>
			<div class="layui-input-block">
				<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="uploadHtml1">
	<form class="layui-form" lay-filter="uploadForm">
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">类型:</label>
			<div class="layui-input-block">
				<input type="radio" name="selectFile1" value="1" title="扩热板、转接支架类单件生产产品" checked="">
				<input type="radio" name="selectFile1" value="2" title="垫片、配重块类多件生产产品">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
					<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="selectedFile" style="display: none;">
			<label class="fieldlabel layui-form-label">已选文件:</label>
			<div class="layui-input-block">
				<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="selectFileHtml">
	<form class="layui-form" lay-filter="selectFileForm">
		<div class="layui-form-item" style="margin-left: 10px;">
			<input type="radio" name="selectFile" value="扩热板、转接支架类单件生产产品" title="扩热板、转接支架类单件生产产品" checked="">
			<input type="radio" name="selectFile" value="垫片、配重块类多件生产产品" title="垫片、配重块类多件生产产品">
		</div>
	</form>
</script>

<script type="text/html" id="viewThreeTableHtml">
	<button type="button" style="margin-left: 10px;margin-top: 10px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor1" id="product-quality-upload">
		<i class="layui-icon">&#xe601;</i> 保存为pdf
	</button>
	<button type="button" style="margin-left: 10px;margin-top: 10px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-normal" id="product-quality-export">
		<i class="layui-icon">&#xe601;</i> 保存为excel
	</button>
	<div id="three_excel"></div>
</script>

<script type="text/html" id="reassociationHtml">
	<form class="layui-form" style="">
		<div class="layui-form-item">
			<label class="layui-form-label">型号:</label>
			<div class="layui-input-block">
				<select name="model" lay-filter="model" lay-verify="required" lay-search id="model">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">阶段:</label>
			<div class="layui-input-block">
				<select name="phase" lay-filter="phase" lay-verify="required" id="phase">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">专业:</label>
			<div class="layui-input-block">
				<select name="dir" lay-filter="dir" lay-verify="required" id="dir">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div id="leafDiv" class="layui-form-item">
			<label class="layui-form-label">过程:</label>
			<div class="layui-input-block">
				<select name="leaf" lay-filter="leaf" id="leaf">
					<option value=""></option>
				</select>
			</div>
		</div>

		<div class="layui-form-item" style="display: none;">
			<center>
				<button id="btn_add" class="layui-btn" lay-submit lay-filter="addData">提交</button>
				<button id="btn_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
			</center>
		</div>
	</form>
</script>