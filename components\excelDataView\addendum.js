var curInfo = {
	tableId: 'dataTable',
	tableType: '查看附表',
	addendumTableType: '',
	addendumResultId: ''

};


$(document).ready(function () {
	layui.use('layer', function () {
		curInfo.addendumTableType = parent.window.addendumTableType;
		curInfo.addendumResultId = parent.window.addendumResultId;
		//初始化easyui的表格
		initTableComp();

		//加载数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	});
});

//初始化表格 - 
var initTableComp = function (layui) {
	$('#' + curInfo.tableId).datagrid({
		data: [],
		singleSelect: true,
		// fitColumns: true,
		striped: true,
		fit: true,
		rownumbers: true,
		pagination: true,
		columns: [
			[{
				field: 'ID',
				hidden: true
			}, {
				field: 'FORMNAME',
				title: '附表名称',
				align: 'center',
				width: 300
			}, {
				field: 'opt',
				title: '附表PDF',
				align: 'center',
				width: 150,
				formatter: function (value, rec) {
					var rowData = JSON.stringify(rec).replace(/"/g, "\'");
					var html =
						'<button type="button" class="layui-btn layui-btn-xs" onclick="previewAddendum(' +
						rowData +
						',1)">查看</button>';
					html +=
						'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="downloadAddendum(' +
						rowData + ',1)">下载</button>';
					return html;
				}
			}, {
				field: 'opt1',
				title: '附表Excel',
				align: 'center',
				width: 150,
				formatter: function (value, rec) {
					var rowData = JSON.stringify(rec).replace(/"/g, "\'");
					var html =
						'<button type="button" class="layui-btn layui-btn-xs" onclick="previewAddendum(' +
						rowData +
						',2)">查看</button>';
					html +=
						'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="downloadAddendum(' +
						rowData + ',2)">下载</button>';
					return html;
				}
			}]
		],
		loadMsg: '正在加载数据...',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>'
	});

	//初始化分页控件
	initPagination(curInfo.tableId, []);
};

var previewAddendum = function (rec, type) {
	var url = '';
	var path = '';
	var format = '';
	var filename = rec.FORMNAME;
	if (type == 1) {
		path = rec.PDFFILEPATH;
		format = 'pdf';
	} else if (type == 2) {
		path = rec.EXCELFILEPATH;
		format = 'xls';
	}
	if (path === undefined) {
		path = "";
	}
	path = path.replace(/\\/g, "/");


	var url = fileHandlerUrl + "/system/open/file?filePath=" + path + "&fileName=" + encodeURIComponent("预览") +
		"." + format;

	url += "&fullfilename=" + encodeURIComponent(filename) + "." + format + "&browser=" + browser();
	url = encodeURIComponent(url);

	if (format === 'PDF' || format === 'pdf') {
		// url += '&officePreviewType=pdf';
		window.open('/File' + path);
	} else {
		window.open(sessionStorage.getItem('previewUrl') + '/onlinePreview?url=' + url);
	}
};

//附件下载
function downloadAddendum(rec, type) {
	var path = '';
	var format = '';
	if (type == 1) {
		path = rec.PDFFILEPATH;
		format = 'pdf';
	} else if (type == 2) {
		path = rec.EXCELFILEPATH;
		format = 'xls';
	}
	if (path === undefined) {
		path = "";
	}
	path = path.replace(/\\/g, "/");
	var filename = rec.FORMNAME;
	downloadFile(path, filename + "." + format);
};

//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var getFieldValue = function () {
	var param = {};
	param.addendumTableType = curInfo.addendumTableType;
	param.addendumResultId = curInfo.addendumResultId;
	return param;
};

//初始化行号
var initLineNumbers = function () {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function (index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//初始化全部的记录条数
var initTotalRecords = function () {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function (data) {
		//initPagination('logtable',{total:data.rows[0].COUNT});
		$('#' + curInfo.tableId).datagrid('getPager').pagination('refresh', {
			total: data.rows[0].COUNT,
			pageSize: pageOptions.pageSize,
			pageNumber: pageOptions.pageNumber
		});
		//重新初始化行号
		initLineNumbers();
	};
	var cb_error = function () { };

	//传递参数
	var param = getFieldValue();
	twxAjax('Thing.Fn.DataSearch', 'QueryAddendumCount', param, true, cb_success, cb_error);
};

//分页查询数据
var queryDataByPage = function (pageSize, pageNumber) {
	$('#' + curInfo.tableId).datagrid('loading');
	var cb_success = function (data) {
		//调用成功后，渲染数据
		$('#' + curInfo.tableId).datagrid('loadData', data.rows);
		initTotalRecords();
		$('#' + curInfo.tableId).datagrid('loaded');
	};
	var cb_error = function () {
		$('#' + curInfo.tableId).datagrid('loaded');
		layui.use(['layer'], function () {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	//传递的参数
	var param = getFieldValue();
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.DataSearch', 'QueryAddendum', param, true, cb_success, cb_error);
};

//初始化分页组件
var initPagination = function (tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function () {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function (pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function (pageNumber, pageSize) {

		},
		onRefresh: function (pageNumber, pageSize) {
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function (pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	});
};