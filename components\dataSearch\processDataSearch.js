$(function() {
	// 初始化layui组件
	layui.use(['layer'], function() {
		window.layer = layui.layer;
	});
	
	loadTree();

	$("#root_layout").layout("panel", "center").panel({
		onResize: function(width, height) {
			//2是边框宽度
			var gridWidth = width - 2;
			var gridId = "";
			if (tableId == 'product_list_table') {
				gridId = "secondTable";
			} else {
				gridId = tableId;
			}
			if (gridId != "") {
				try {
					$("#" + gridId).datagrid('resize', {
						width: gridWidth
					});
					changeWidth(gridId);
				} catch (e) {

				}

			}
		}
	});
	//绑定事件
	$('#root_layout_tabs').tabs({
		onSelect: function(title, index) {
			var tab = $('#root_layout_tabs').tabs('getTab', index);
			tableId = tab.panel('options').tableId;
			queryType = tab.panel('options').queryType;
			reloadTable();
		}
	});
	$('#root_layout_tabs').tabs('select', 4);
})
var tableId = "product_list_table";
var queryType = 'process';
//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};
//四个类型表格是否加载
var tableLoadFlag = {
	'design_list_table': false,
	'craft_list_table': false,
	'processcontrol_list_table': false,
	'quanlitycontrol_list_table': false,
	'photo_table': false
};
var cmenus = {
	'design_list_table': undefined,
	'craft_list_table': undefined,
	'processcontrol_list_table': undefined,
	'quanlitycontrol_list_table': undefined,
	'photo_table': undefined
};
//加载表格
var renderTable = function() {
	var columns = listTableUtil.getColumns(queryType);
	if (!tableLoadFlag[tableId]) {
		$('#' + tableId).datagrid({
			data: [],
			fitColumns: true,
			toolbar: '#tb',
			// fit: true,
			columns: columns,
			emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>数据加载中...</font></div>',
			pagination: true,
			loadMsg: '正在加载数据...',
			height: windowH - 40,
			singleSelect: true,
			rownumbers: true,
			striped: true,
			onHeaderContextMenu: function(e, field) {
				e.preventDefault();
				if (!cmenus[tableId]) {
					cmenus[tableId] = gridUtil.createColumnMenu(tableId);
				}
				cmenus[tableId].menu('show', {
					left: e.pageX,
					top: e.pageY
				});
			},
			onDblClickRow: function(rowIndex, rowData) {
				var nodeCode = rowData.NODECODE;
				var cb_success = function(data) {
					var trees = data.rows;
					locationTreeNodeUtil(data.rows, ztreeObj, 'TREEID', function(thisNode) {});
				};
				twxAjax('Thing.Fn.DataSearch', 'QueryTreeIdsByNodeCode', {
					nodeCode: nodeCode
				}, true, cb_success);
			},
			onLoadSuccess: function(data) {
				changeWidth(tableId);
				// $("#secondTableDiv .datagrid-body").css("overflow-x", "auto");
			}
		});
		tableLoadFlag[tableId] = true;
	}
};

//初始化分页组件
var initPagination = function(data) {
	$('#' + tableId).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function(pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};

//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + tableId).datagrid('loading');
	initTotalRecords();
	var cb_success = function(res) {
		if (res.success) {
			dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + tableId).datagrid('loadData', res.data);
			if (pageLoadFlag) {
				paginationShow();
			}
			$('#' + tableId).datagrid('loaded');
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}

	};
	var cb_error = function() {
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	var params = {};
	params.query = getSearchParams();
	params.pageSize = pageOptions.pageSize;
	params.pageNumber = pageOptions.pageNumber;

	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.ListData', 'QueryListDataPage', params, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	$('#' + tableId).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
	var log = getSearchLog();
	logRecord('查询', '过程结构树查询-查询(' + log + ')的数据，共查询出' + totalRecords + '条数据', 1);
}

//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(res) {
		if (res.success) {
			pageLoadFlag = true;
			totalRecords = res.data;
			if (dataLoadFlag) {
				paginationShow();
			}
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	};
	var cb_error = function() {};
	var params = {};
	params.query = getSearchParams();
	twxAjax('Thing.Fn.ListData', 'QueryListDataCount', params, true, cb_success, cb_error);
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//获取查询条件参数
function getSearchParams() {
	var processTreeId = 1;
	if ($.fn.zTree.getZTreeObj("dpTree") != null) {
		var selNodes = $.fn.zTree.getZTreeObj("dpTree").getSelectedNodes();
		if (selNodes.length > 0) {
			processTreeId = selNodes[0].TREEID;
		}
	}
	var queryType = $('#root_layout_tabs').tabs('getSelected').panel('options').queryType;
	var params = {};
	if (queryType == 'photo') {
		params = {
			processTreeId: processTreeId,
			fileType: '影像记录',
			secLevel: '<' + userSecLevel,
			isMergeFolder: '0',
			queryUser: sessionStorage.getItem('username')
		};
	} else {
		params = {
			processTreeId: processTreeId,
			queryType: queryType,
			secLevel: '<' + userSecLevel,
			queryUser: sessionStorage.getItem('username')
		};
	}
	return params
}

//获取查询日志信息
function getSearchLog() {
	var log = '所有数据包';
	if ($.fn.zTree.getZTreeObj("dpTree") != null) {
		var selNodes = $.fn.zTree.getZTreeObj("dpTree").getSelectedNodes();
		if (selNodes.length > 0) {
			var node = selNodes[0];
			var type = node.NODETYPE;
			if (type == 'root') {
				log = '所有数据包';
			} else if (type == "product") {
				log = '(型号：' + node.NODENAME + ')下的数据包';
			} else if (type == "phase") {
				log = '(型号：' + node.getParentNode().NODENAME + '、阶段：' + node.NODENAME + ')下的数据包';
			} else if (type == "dir") {
				log = '(型号：' + node.getParentNode().getParentNode().NODENAME + '、阶段：' + node.getParentNode().NODENAME +
					'、专业：' +
					node.NODENAME + ')下的数据包';
			} else if (type == "leaf") {
				log = '(型号：' + node.getParentNode().getParentNode().getParentNode().NODENAME + '、阶段：' + node
					.getParentNode().getParentNode()
					.NODENAME + '、专业：' + node.getParentNode().NODENAME + '、过程：' + node.NODENAME + ')下的数据包';
			}
		}
	}
	var type = $('#root_layout_tabs').tabs('getSelected').panel('options').name;
	log = log + "的" + type;
	return log;
}

//加载table的数据
var loadTable = function(type) {
	//渲染表格
	renderTable();

	//初始化分页组件
	initPagination({
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}

//重新加载table的数据
var reloadTable = function() {
	if (tableId == 'product_list_table') {
		initProductDataPage();
	} else {

		//渲染表格
		renderTable();

		//初始化分页组件
		initPagination({
			total: 0
		});
		pageOptions.pageNumber = 1;
		//显示第一页的数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);

	}
}

//加载树结构
function loadTree() {
	var cb_success = function(res) {
		var datas = res.data;
		if (datas.length > 0) {
			datas = dealDataIcons(datas);
			datas = dealDataNodeName(datas);
			treeSetting.callback.onClick = function(event, treeId, treeNode) {
				reloadTable();
			};
			treeSetting.callback.onExpand = function(event, treeId, treeNode) {
				loadTreeMenu();
			};
			//禁止拖拽
			treeSetting.callback.beforeDrag = function() {
				return false;
			};
			treeSetting.callback.beforeDrop = function() {
				return false;
			};
			treeSetting.async.url = getTreeUrl("Thing.Fn.DataPackage", "QueryDataPackageTree1", ""),
				ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
			var nodes = ztreeObj.getNodes();
			for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
				ztreeObj.expandNode(nodes[i], true, false, true);
			}
			//横向集合节点
			var hengNode = ztreeObj.getNodeByParam("TREEID", -1, null);
			if (hengNode != null) {
				ztreeObj.expandNode(hengNode, true, false, true);
			}
			loadTreeMenu();
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax('Thing.Fn.ProcessTree', 'QueryTreeRoot', {
		username: sessionStorage.getItem('username')
	}, true, cb_success);
}

//加载树节点右键菜单
function loadTreeMenu() {
	$("#dpTree a").each(function(i, n) {
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		var menu = getNodeMenu(node);
		if (menu.length != 0) {
			$(n).contextMenu({
				width: 150,
				menu: menu,
				target: function(ele) {
					var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
					ztreeObj.selectNode(node, false, true);
				}
			});
		}
	});
}

//获取节点右键菜单数组
function getNodeMenu(treeNode) {
	var imgSuffix = '../dataTree/';
	var menu = [];
	
	// 只为过程节点（叶子节点或没有子节点的目录节点）添加批量同步菜单
	var isProcessNode = (treeNode.NODETYPE == 'leaf') || 
						((treeNode.NODETYPE == 'dir') && (!treeNode.ISPARENT));
	
	if (isProcessNode) {
		var batchSyncMenu = {
			text: "同步所有二级表",
			icon: imgSuffix + 'images/sync.png',
			callback: function() {
				batchSyncAllTables(treeNode);
			}
		};
		menu.push(batchSyncMenu);
	}
	
	return menu;
}

//批量同步所有二级表
function batchSyncAllTables(treeNode) {
	var loadingIndex = layer.msg('正在批量同步，请稍候...', {
		icon: 16,
		shade: 0.3,
		time: 0
	});
	
	var cb_success = function(res) {
		layer.close(loadingIndex);
		
		if (res.success) {
			// 显示同步结果详情
			showSyncResults(res);
			// 刷新当前页面数据
			reloadTable();
		} else {
			layer.alert(res.msg, {
				icon: 2,
				title: '批量同步失败'
			});
		}
	};
	
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.close(loadingIndex);
		layer.alert('批量同步请求失败，请检查网络连接！', {
			icon: 2,
			title: '请求错误'
		});
	};
	
	twxAjax('Thing.Fn.SecondTable', 'BatchSyncAllTables', {
		treeId: treeNode.TREEID
	}, true, cb_success, cb_error);
}

//显示同步结果详情
function showSyncResults(res) {
	var summary = res.summary;
	var results = res.data;
	
	// 构建结果表格HTML
	var tableHtml = '<div style="padding: 15px;">' +
		'<div style="margin-bottom: 15px;">' +
		'<h4 style="margin: 0 0 10px 0;">批量同步结果汇总</h4>' +
		'<p style="margin: 5px 0;">总表数：' + summary.totalTables + '</p>' +
		'<p style="margin: 5px 0; color: green;">成功：' + summary.successCount + '个</p>' +
		'<p style="margin: 5px 0; color: red;">失败：' + summary.failCount + '个</p>' +
		'<p style="margin: 5px 0;">共同步数据：' + summary.totalSyncCount + '条</p>' +
		'</div>' +
		'<div>' +
		'<h4 style="margin: 0 0 10px 0;">详细结果</h4>' +
		'<table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">' +
		'<thead>' +
		'<tr style="background-color: #f5f5f5;">' +
		'<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">表名</th>' +
		'<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">状态</th>' +
		'<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">同步数量</th>' +
		'<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">结果说明</th>' +
		'</tr>' +
		'</thead>' +
		'<tbody>';
	
	for (var i = 0; i < results.length; i++) {
		var result = results[i];
		var statusColor = result.success ? 'green' : 'red';
		var statusText = result.success ? '成功' : '失败';
		
		tableHtml += '<tr>' +
			'<td style="border: 1px solid #ddd; padding: 8px;">' + result.tableName + '</td>' +
			'<td style="border: 1px solid #ddd; padding: 8px; color: ' + statusColor + ';">' + statusText + '</td>' +
			'<td style="border: 1px solid #ddd; padding: 8px;">' + result.syncCount + '</td>' +
			'<td style="border: 1px solid #ddd; padding: 8px;">' + result.msg + '</td>' +
			'</tr>';
	}
	
	tableHtml += '</tbody></table></div></div>';
	
	layer.open({
		type: 1,
		title: '批量同步结果',
		area: ['800px', '600px'],
		content: tableHtml,
		btn: ['确定'],
		yes: function(index) {
			layer.close(index);
		}
	});
}