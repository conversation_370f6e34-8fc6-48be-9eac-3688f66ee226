<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>计算模型管理</title>
		<link href="../../../plugins/layui-lasted/css/layui.css" rel="stylesheet" />

		<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/metroStyle/metroStyle.css">
		<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/contextMenu.css">
		<link href="model.css" rel="stylesheet" />

		<script src="../../../plugins/easyui/jquery.min.js"></script>
		<script type="text/javascript" src="../../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
		<script type="text/javascript" src="../../../plugins/ztree/js/jquery.contextMenu.min.js"></script>

		<script src="../../js/config/twxconfig.js"></script>
		<script src="../../js/util.js"></script>

	</head>
	<body>
		<div class="layui-fluid layui-bg-gray">
			<div class="layui-row layui-col-space16">
				<div class="layui-col-md2">
					<div class="layui-card">
						<div class="layui-card-header">模型分类</div>
						<div class="layui-card-body">
							<ul id="tree" class="ztree"></ul>
						</div>
					</div>
				</div>
				<div class="layui-col-md10">
					<div class="layui-row layui-col-space16">
						<div class="layui-col-md5">
							<div class="layui-card">
								<div class="layui-card-header">输入参数</div>
								<div class="layui-card-body">
									<div id="param-msg" class="msg">
										请选择模型节点！
									</div>
									<div id="param-div">
										<table class="layui-hide" id="param-table" lay-filter="param-table"></table>
									</div>
								</div>
							</div>

							<div class="layui-card">
								<div class="layui-card-header" id="formula-title">计算公式</div>
								<div class="layui-card-body">
									<div id="formula-msg" class="msg">
										请选择模型节点！
									</div>
									<div id="formula-div" style="display: none;">
										<textarea id="calc-formula" class="calc-formula layui-textarea"></textarea>
										<table class="layui-table calc-table">
											<tr>
												<td align="center" class="calc-num">
													<div>(</div>
												</td>
												<td align="center" class="calc-num">
													<div>)</div>
												</td>
												<td align="center" class="calc-clear">
													<div>C</div>
												</td>
												<td align="center" class="calc-back">
													<div>←</div>
												</td>
											</tr>
											<tr>
												<td align="center" class="calc-num">
													<div>1</div>
												</td>
												<td align="center" class="calc-num">
													<div>2</div>
												</td>
												<td align="center" class="calc-num">
													<div>3</div>
												</td>
												<td align="center" class="calc-num">
													<div>＋</div>
												</td>
											</tr>
											<tr>
												<td align="center" class="calc-num">
													<div>4</div>
												</td>
												<td align="center" class="calc-num">
													<div>5</div>
												</td>
												<td align="center" class="calc-num">
													<div>6</div>
												</td>
												<td align="center" class="calc-num">
													<div>－</div>
												</td>
											</tr>
											<tr>
												<td align="center" class="calc-num">
													<div>7</div>
												</td>
												<td align="center" class="calc-num">
													<div>8</div>
												</td>
												<td align="center" class="calc-num">
													<div>9</div>
												</td>
												<td align="center" class="calc-num">
													<div>×</div>
												</td>
											</tr>
											<tr>
												<td align="center" class="calc-num">
													<div>0</div>
												</td>
												<td align="center" class="calc-num">
													<div>.</div>
												</td>
												<td align="center" class="calc-save">
													<div>保存</div>
												</td>
												<td align="center" class="calc-num">
													<div>÷</div>
												</td>
											</tr>
										</table>
									</div>
								</div>
							</div>

						</div>

						<div class="layui-col-md7">
							<div class="layui-card">
								<div class="layui-card-header">输出参数</div>
								<div class="layui-card-body">
									<div id="result-msg" class="msg">
										请选择模型节点！
									</div>
									<div id="result-div">
										<table class="layui-hide" id="result-table" lay-filter="result-table"></table>
									</div>
								</div>
							</div>
							<div class="layui-card" style="overflow: auto;">
								<div class="layui-card-header">公式验证</div>
								<div class="layui-card-body">
									<div id="verify-msg" class="msg">
										请选择模型节点！
									</div>
									<div id="verify-div">

									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>

	<script type="text/html" id="param-toolbar">
		<div class="layui-btn-container">
			<button class="layui-btn layui-btn-sm" lay-event="add-btn">新增</button>
			<button class="layui-btn layui-btn-sm layui-bg-red" lay-event="delete-btn">删除</button>
		</div>
	</script>
	<script type="text/html" id="param-rowbar">
		<div class="layui-clear-space">
			<a class="layui-btn layui-btn-xs" lay-event="quote">引用</a>
			<a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="edit">修改</a>
			<a class="layui-btn layui-btn-xs layui-bg-red" lay-event="delete">删除</a>
		</div>
	</script>

	<script type="text/html" id="result-toolbar">
		<div class="layui-btn-container">
			<button class="layui-btn layui-btn-sm" lay-event="add-btn">新增</button>
			<button class="layui-btn layui-btn-sm layui-bg-red" lay-event="delete-btn">删除</button>
		</div>
	</script>
	<script type="text/html" id="result-rowbar">
		<div class="layui-clear-space">
			<a class="layui-btn layui-btn-xs" lay-event="quote">引用</a>
			<a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="formula">编辑公式</a>
			<a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="edit">修改</a>
			<a class="layui-btn layui-btn-xs layui-bg-red" lay-event="delete">删除</a>
		</div>
	</script>
	<script src="../../../plugins/layui-lasted/layui.js"></script>
	<script src="tree.js"></script>
	<script src="calc.js"></script>
	<script src="param.js"></script>
	<script src="result.js"></script>
	<script src="verify.js"></script>
	<script src="model.js"></script>
</html>