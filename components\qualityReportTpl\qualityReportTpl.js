$(function () {
    loadTree();
});

layui.use(['layer', 'form', 'upload', 'table', 'laydate'], function () {
    table = layui.table;
    layer = layui.layer;
    upload = layui.upload;
    form = layui.form;
    laydate = layui.laydate;
    device = layui.device();
});
var postType = 2;
var contextEle; //签名td元素
var funcIdent = "tpl";


//加载树结构
function loadTree() {
    var cb_success = function (res) {
        if (res.success) {
            var datas = res.data;
            if (datas.length > 0) {
                datas = dealDataIcons(datas);
                datas = dealDataNodeName(datas);

                treeSetting.callback.onClick = function (event, treeId, treeNode) {
                    reloadTable(treeNode);
                };
                treeSetting.callback.onExpand = function (event, treeId, treeNode) {
                    loadTreeMenu();
                };
                ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
                loadTreeMenu();
                var nodes = ztreeObj.getNodes();
                for (var i = 0; i < nodes.length; i++) { //设置节点展开
                    ztreeObj.expandNode(nodes[i], true, false, true);
                }
            }
        } else {
            layer.alert(res.msg);
        }
    };
    //使用ajax进行异步加载Tree
    twxAjax(THING, 'QueryTreeRoot', '', true, cb_success);
}

//操作完节点之后重新加载节点
function reloadTree(refrushId, selId) {
    if (selId) {

    } else {
        selId = refrushId;
    }
    var refrushTreeNode = ztreeObj.getNodeByParam("ID", refrushId, null);
    if (!refrushTreeNode.ISPARENT) {
        refrushTreeNode.ISPARENT = true;
        ztreeObj.updateNode(refrushTreeNode);
    }
    ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false, function () {
        ztreeObj.expandNode(refrushTreeNode, true, false, true);
        var newSelNode = ztreeObj.getNodeByParam("ID", selId, null);
        ztreeObj.selectNode(newSelNode, false, true);
        loadTreeMenu();
        reloadTable(newSelNode);
    });
}


// function reloadTree(optNode) {
// 	var cb_success = function(res) {
// 		if (res.success) {
// 			var datas = res.data;
// 			if (datas.length > 0) {
// 				datas = dealDataIcons(datas);
// 				datas = dealDataNodeName(datas);

// 				treeSetting.callback.onExpand = function(event, treeId, treeNode) {
// 					loadTreeMenu();
// 				};

// 				treeSetting.callback.onClick = function(event, treeId, treeNode) {
// 					reloadTable(treeNode);
// 				};
// 				ztreeObj = $.fn.zTree.init($("#" + treeId), treeSetting, datas);
// 				loadTreeMenu();

// 				setTimeout(function() {
// 					var node = ztreeObj.getNodeByParam("ID", optNode["ID"], null);
// 					ztreeObj.selectNode(node, false, false);
// 					reloadTable(node);
// 				}, 100);
// 			}
// 		} else {
// 			layer.alert(res.msg);
// 		}
// 	};
// 	//使用ajax进行异步加载Tree
// 	twxAjax(THING, 'QueryTplTree', '', true, cb_success);
// }

//加载树节点右键菜单
function loadTreeMenu() {
    $("#dpTree a").each(function (i, n) {
        var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
        menu = getNodeMenu(node);
        if (menu.length != 0) {
            $(n).contextMenu({
                width: 130,
                menu: menu,
                target: function (ele) {
                    var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
                    ztreeObj.selectNode(node, false, true);
                }
            });
        }
    });
}

//校验同一节点下子节点表序号是否重复
function checkTableNumIsRepeat(parentId, tableNum, oldTableNum) {
    var flag = false;
    var cb_success = function (res) {
        if (res.success) {
            var ds = res.data;
            for (var i = 0; i < ds.length; i++) {
                var d = ds[i];
                if (tableNum == d.TABLE_NUM) {
                    flag = true;
                    break;
                }
            }
            if (tableNum == oldTableNum) {
                flag = false;
            }
        } else {
            layer.alert(res.msg);
        }
    };
    twxAjax(THING, 'QueryChildrenByPid', {
        pid: parentId
    }, false, cb_success); //同步请求校验

    return flag;
}


//校验同一节点下子节点是否重复
function checkNodeNameIsRepeat(parentId, nodeName, oldNodeName) {
    var flag = false;
    var cb_success = function (res) {
        if (res.success) {
            var ds = res.data;
            for (var i = 0; i < ds.length; i++) {
                var d = ds[i];
                if (nodeName == d.NAME) {
                    flag = true;
                    break;
                }
            }
            if (nodeName == oldNodeName) {
                flag = false;
            }
        } else {
            layer.alert(res.msg);
        }
    };
    twxAjax(THING, 'QueryChildrenByPid', {
        pid: parentId
    }, false, cb_success); //同步请求校验
    return flag;
}


//获取节点右键菜单数组
function getNodeMenu(treeNode) {
    var imgSuffix = '../dataTree/';
    var type = treeNode.TYPE;
    var menu = [];

    var addReportNode = {
        text: "添加A表",
        icon: imgSuffix + 'images/add.png',
        callback: function () {
            HotUtil.addTableNode(treeNode, 'report');
        }
    };

    var importReportNode = {
        text: "导入A表",
        icon: imgSuffix + 'images/excelupload.png',
        callback: function () {
            HotUtil.importTableNode(treeNode, 'report');
        }
    };

	var addTableNode = {
		text: "添加节点",
		icon: imgSuffix + 'images/add1.png',
		callback: function() {
			var type = "";
			if (treeNode.TYPE == 'leaf') {
				type = "report";
			} else if (treeNode.TYPE == 'report') {
				type = "table";
			} else if (treeNode.TYPE == 'table') {
				type = "table_1";
			} else if (treeNode.TYPE == 'table_1') {
				type = "table_2";
			} else if (treeNode.TYPE == 'table_2') {
				type = "table_3";
			}

			HotUtil.addTableNode(treeNode, type);
		}
	};

    var importTableNode = {
        text: "导入节点",
        icon: imgSuffix + 'images/excelupload1.png',
        callback: function() {
            var type = "";
            if (treeNode.TYPE == 'leaf') {
                type = "report";
            } else if (treeNode.TYPE == 'report') {
                type = "table";
            } else if (treeNode.TYPE == 'table') {
                type = "table_1";
            } else if (treeNode.TYPE == 'table_1') {
                type = "table_2";
            } else if (treeNode.TYPE == 'table_2') {
                type = "table_3";
            }

            HotUtil.importTableNode(treeNode, type);
        }
    };


    var editNodeMenu = {
        text: "编辑节点",
        icon: imgSuffix + 'images/edit.png',
        callback: function () {
            HotUtil.editTableNode(treeNode);
        }
    };

    var deleteNodeMenu = {
        text: "删除节点",
        icon: imgSuffix + 'images/remove.png',
        callback: function () {
            HotUtil.deleteTableNode(treeNode);
        }
    };

    if (treeNode.IS_PROCESS == "true") {
        menu.push(addReportNode);
        menu.push(importReportNode);
    }

    if (type == "report" || type == 'table' || type == 'table_1' || type == 'table_2' || type == 'table_3') {
        if (type !== 'table_3') {
            menu.push(addTableNode);
        }
        menu.push(importTableNode);
        menu.push(editNodeMenu);
        menu.push(deleteNodeMenu);

    }
    return menu;
}


function initTbr(treeNode) {
    $('#eidt-table').show();
    $("#import-excel").show();
    if (treeNode.HTML_DATA) {
        $("#table-header").show();
    } else {
        $("#table-header").hide();
    }

    //导入Excel
    $('#import-excel').unbind("click").bind('click', function () {
        var url = fileHandlerUrl + '/report/import/tpl/excel';
        HotUtil.importExcel(treeNode, url);
    });

    //设置表格表头行
    $('#table-header').unbind("click").bind('click', function () {
        HotUtil.updateHeaderRow(treeNode);
    });
    //编辑表格
    $('#eidt-table').unbind("click").bind('click', function () {
        HotUtil.openEdit(treeNode, 1, function () {
            HotUtil.editTable(treeNode);
        });
    });
}

//保存配置信息
function saveConfig(treeNode) {
    $('#saveConfig').unbind("click").bind('click', function () {
        //获取数据来源
        var dataSource = $('#dataSource').combobox('getValue');
        if (dataSource == '') {
            layer.alert("请选择数据来源！");
            return false;
        }

        //获取自动填充类型
        var dataType = $('#dataType').combobox('getValue');
        //获取报告类型
        var reportType = $('#reportType').combobox('getValue');

        //自动关联
        if (dataSource == 'auto') {
            if (dataType == '') {
                layer.alert("请选择自动填充类型！");
                return false;
            }
            if (reportType == '') {
                layer.alert("请选择报告类型！");
                return false;
            }
        }
        var cb_success = function (res) {
            if (res.success) {
                layer.msg(res.msg);
                //更新树节点的数据
                treeNode.DATA_SOURCE = dataSource;
                treeNode.DATA_TYPE = dataType;
                treeNode.REPORT_TYPE = reportType;
                ztreeObj.updateNode(treeNode);
                reloadTable(treeNode);
            } else {
                layer.alert(res.msg);
            }
        };

        var cb_error = function (err) {
            layer.alert("请求失败！");
        };

        twxAjax(THING, "SaveConfig", {
            id: treeNode.ID,
            dataSource: dataSource,
            dataType: dataType,
            reportType: reportType,
            creator: sessionStorage.getItem('username')
        }, true, cb_success, cb_error);
    })
}

//加载数据配置下拉框
function loadCombobox(treeNode) {
    $("#dataConfig").show();

    var dataSource = treeNode.DATA_SOURCE || '';
    var dataType = treeNode.DATA_TYPE || '';
    var reportType = treeNode.REPORT_TYPE || '';

    $('#dataSource').combobox({
        valueField: 'value',
        textField: 'text',
        panelHeight: 'auto',
        data: [{
            value: 'manual',
            text: '手动填写'
        }, {
            value: 'auto',
            text: '自动填充'
        }, {
            value: 'list',
            text: '文件清单'
        }, {
            value: 'test',
            text: '试验管控系统'
        }],
        onSelect: function (record) {
            if (record.value == 'auto' || record.value == 'test') {
                $(".auto").show();
                var cb_success = function (res) {
                    if (res.success) {
                        //加载数据类型下拉框
                        $('#dataType').combobox({
                            valueField: 'ID',
                            textField: 'TREE_NAME',
                            panelHeight: 'auto',
                            data: res.data
                        });
                        var isIn = false;
                        for (var i = 0; i < res.data.length; i++) {
                            if (dataType == res.data[i].ID) {
                                isIn = true;
                                break;
                            }
                        }
                        if (isIn) {
                            $('#dataType').combobox('select', dataType);
                        }
                        if (record.value == 'auto') {
                            $(".reportType").show();
                            //加载报告类型下拉框
                            $('#reportType').combobox({
                                valueField: 'value',
                                textField: 'text',
                                panelHeight: 'auto',
                                data: [{
                                    value: 'confirm',
                                    text: '策划确认表'
                                }, {
                                    value: 'summary',
                                    text: '策划汇总表'
                                }, {
                                    value: 'actual',
                                    text: '实际汇总表'
                                }, {
                                    value: 'photo',
                                    text: '影像记录表'
                                }]
                            });
                            $('#reportType').combobox('select', reportType);
                        } else {
                            $(".reportType").hide();
                        }

                    } else {
                        layer.alert(res.msg);
                    }
                };
                var cb_error = function (xhr) {
                    layer.alert('请求失败!', {
                        icon: 2
                    });
                };
                twxAjax(THING, "QueryDataType", {
                    id: treeNode.ID,
                    dataSource: record.value
                }, true, cb_success, cb_error);
            } else {
                $(".auto").hide();
            }
        }
    });

    $('#dataSource').combobox('select', dataSource);
}

//加载左侧的表格
function reloadTable(treeNode) {
    var type = treeNode.TYPE;
    if (type == 'table' || type == 'report') {
        saveConfig(treeNode);
        loadCombobox(treeNode);
        if (treeNode.DATA_SOURCE == 'auto' || treeNode.DATA_SOURCE == 'list' || treeNode.DATA_SOURCE == 'test') {
            $("#msg").text('自动填充的数据不需要编辑表格！').show();
            $("#tbr").hide();
            $("#table").hide();
        } else {
            $("#msg").hide();
            $("#tbr").show();
            $("#table").show();
            initTbr(treeNode);
            var cb_success = function (res) {
                if (res.success) {
                    $("#table").empty();
                    var html = res.data.HTML_DATA || "";
                    //更新node节点的数据
                    treeNode.HTML_DATA = html;
                    treeNode.SAVE_DATA = res.data.SAVE_DATA || "";
                    ztreeObj.updateNode(treeNode);
                    initTbr(treeNode);
                    if (html != "") {
                        var html = res.data.HTML_DATA;
                        //更新node节点的数据
                        treeNode.HTML_DATA = res.data.HTML_DATA;
                        treeNode.SAVE_DATA = res.data.SAVE_DATA;
                        ztreeObj.updateNode(treeNode);
                        initTbr(treeNode);
                        //添加表格
                        var $table = $(html.replaceAll("\n", "<br>"));
                        if (res.data.TABLE_HEADER) {
                            var tableHeader = Number(res.data.TABLE_HEADER);
                            $table.find("tr").each(function (i, n) {
                                if (i < tableHeader) {
                                    $(n).css("font-weight", "bold").css("background-color", "#e6e6e6");
                                }
                            });
                        }
                        $("#table").append($table);
                        $("#table .layui-table tbody tr:hover").css("background-color", "");

                        HotUtil.tableAddLink(treeNode, $table);
                    } else {
                        $("#table").append('<span style="color:red;"> 请先编辑表格！</span>');
                    }
                } else {
                    layer.alert(res.msg);
                }
            };
            var cb_error = function (xhr) {
                layer.alert('加载表格失败!', {
                    icon: 2
                });
            };
            twxAjax(THING, "QueryNodeById", {
                id: treeNode.ID
            }, true, cb_success, cb_error);
        }
    } else {
        $("#dataConfig").hide();
        $("#msg").text("请选择表节点！").show();
        $("#tbr").hide();
        $("#table").hide();
    }
}
