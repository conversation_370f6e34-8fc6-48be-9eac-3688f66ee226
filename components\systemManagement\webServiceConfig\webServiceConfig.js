//加载表格
var loadTableData = function(tableId, data) {
	$('#' + tableId).datagrid({
		data: data,
		singleSelect: true,
		fitColumns: true,
		striped: true,
		toolbar: [{
			iconCls: 'icon-myadd',
			text: '新增',
			handler: function() {
				addData();
			}
		}, {
			iconCls: 'icon-myedit',
			text: '编辑',
			handler: function() {
				editData();
			}
		}, {
			iconCls: 'icon-myremove',
			text: '删除',
			handler: function() {
				deleteData();
			}
		}],
		fit: true,
		rownumbers: true,
		columns: [
			[
				// {field: 'ROWNUM', title: '序号'}
				{
					field: 'ID',
					title: 'ID',
					hidden: true
				}, {
					field: 'SOURCE_SYSTEM',
					title: '接口系统',
					width: 100
				}, {
					field: 'TYPE_NAME',
					title: '类型',
					width: 100
				}, {
					field: 'DOCTYPE',
					title: '文档类型',
					width: 100
				}, {
					field: 'PURPOSE',
					title: '用途',
					width: 100
				}, {
					field: 'URL',
					title: '服务路径',
					width: 200
				}
			]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...'
	});
};


//加载table的数据
var reloadTable = function(tableName) {

	//无数据的渲染
	loadTableData("wsConfigTable", []);

	var cb_success = function(data) {
		loadTableData("wsConfigTable", data.rows);
	};
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		if (xhr.status === 500) {
			layui.use(['layer'], function() {
				var layer = layui.layer;
				layer.alert('<font color=red>后台错误，请联系管理员!</font>', {
					icon: 2
				});
			});
		}
	};
	//异步加载数据
	twxAjax("publishMissionThing", "getAllDataListDataByType", {
		type: "WSConfigTable"
	}, true, cb_success, cb_error);

}

//渲染第一个表格
//reloadTable('design_list_table');

//页面件参数传递使用
var selectedData = {
	tabName: undefined,
	data: undefined,
	node: undefined
};

//获取选中的zTree节点
function getSelectedNode() {
	var treeObj = undefined;
	layui.use(['laypage'], function() {
		treeObj = $.fn.zTree.getZTreeObj('dpTree');
	});
	var selNodes = treeObj.getSelectedNodes();
	if (selNodes.length == 0) {
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('<font color=red>请选择左侧的过程节点...</font>', {
				icon: 2
			});
		});
		return false;
	}
	var selNode = selNodes[0];
	if (selNode.NODETYPE !== 'leaf') {
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('<font color=red>当前节点未非过程节点,请选择过程节点...</font>', {
				icon: 2
			});
		});
		return false;
	}

	selectedData.node = selNode;
	return true;
}


function manualUpload() {
	layui.use('layer', function() {
		var layer = layui.layer;
		layer.open({
			type: 2,
			title: '上传',
			maxmin: true,
			area: ["500px", '500px'],
			content: '/DataPackageManagement/components/captureManagement/manualUpload.html', //这里content是一个普通的String
			end: function() {
				// reloadTable(tabId);
			}
		});
	});

}

function linkDataPackage() {
	layui.use('layer', function() {
		var layer = layui.layer;
		layer.open({
			type: 2,
			title: '数据包关联',
			// maxmin: true,
			area: ["500px", '500px'],
			content: '/DataPackageManagement/components/captureManagement/dataPackageLink.html', //这里content是一个普通的String
			end: function() {
				// reloadTable(tabId);
			}
		});
	});

}
//新增按钮操作
function addData() {
	layui.use('layer', function() {
		var layer = layui.layer;
		layer.open({
			type: 2,
			title: '新增',
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ["500px", '390px'],
			content: '/DataPackageManagement/components/systemManagement/webServiceConfig/addConfigData.html', //这里content是一个普通的String
			btn: ['新增', '重置', '关闭'],
			yes: function(index, layero) {
				var body = layer.getChildFrame('body', index);
				var iframeWin = window[layero.find('iframe')[0]['name']];
				iframeWin.$('#btn_add').click();

			},
			btn2: function(index, layero) {
				var body = layer.getChildFrame('body', index);
				var iframeWin = window[layero.find('iframe')[0]['name']];
				iframeWin.$('#btn_reset').click();
				return false;
			},
			btn3: function() {
				return true;
			},
			end: function() {
				reloadTable("wsConfigTable");
			}
		});
	});
}

//编辑按钮操作
function editData() {

	//获取选中的数据
	var datas = $('#' + "wsConfigTable").datagrid('getSelections');
	if (datas.length == 0) {
		//提示用户，请选择待编辑的数据
		layui.use('layer', function() {
			var layer = layui.layer;
			layer.alert('<font color=red>请选择待编辑数据...</font>', {
				icon: 2
			});
		});
		return;
	}

	selectedData.data = datas[0];

	layui.use('layer', function() {
		var layer = layui.layer;
		layer.open({
			type: 2,
			title: '编辑',
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ["500px", '390px'],
			content: '/DataPackageManagement/components/systemManagement/webServiceConfig/editConfigData.html', //这里content是一个普通的String
			btn: ['编辑', '重置', '关闭'],
			yes: function(index, layero) {
				var body = layer.getChildFrame('body', index);
				var iframeWin = window[layero.find('iframe')[0]['name']];
				iframeWin.$('#btn_add').click();

			},
			btn2: function(index, layero) {
				var body = layer.getChildFrame('body', index);
				var iframeWin = window[layero.find('iframe')[0]['name']];
				iframeWin.$('#btn_reset').click();
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function(layero, index) {
				var row = $('#wsConfigTable').datagrid('getSelected');
				if (row) {
					var iframeWin = window[layero.find('iframe')[0]['name']];
				}
			},
			end: function() {
				// reloadTable(tabId);
			}
		});
	});
}

//删除按钮操作
function deleteData() {

	layui.use(['layer'], function() {
		var layer = layui.layer;
		layer.confirm('是否确认删除选中数据?', {
			skin: 'layui-layer-lan',
			icon: 3,
			title: '删除提示'
		}, function(index) {
			var row = $('#' + "wsConfigTable").datagrid('getSelected');

			var cb_success = function(data) {
				//新增完成后需要刷新界面
				layer.alert("删除完成！", {
					icon: 1
				}, function() {
					reloadTable("wsConfigTable");

					//记录日志
					logRecord('删除', '接口配置-删除接口配置(ID：' + row.ID + '、来源系统：' + row.SOURCE_SYSTEM + '、类别：' +
						row.TYPE_NAME +
						'、文件类别：' + row.DOCTYPE +
						'、用途：' + row.PURPOSE + '、服务路径：' + row.URL + ')', 1);
					// var index = parent.layer.getFrameIndex(window.name);
					// parent.layer.close(index); //再执行关闭
					layui.use(['laypage'], function() {
						layer.closeAll();
					});
				});
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {
				logRecord('删除', '接口配置-删除接口配置(ID：' + row.ID + '、来源系统：' + row.SOURCE_SYSTEM + '、类别：' +
					row.TYPE_NAME +
					'、文件类别：' + row.DOCTYPE +
					'、用途：' + row.PURPOSE + '、服务路径：' + row.URL + ')', 0);
				var index = layer.alert('删除失败!', {
					icon: 2
				}, function() {
					// var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
					// var index = parent.layer.getFrameIndex(window.name);
					// parent.layer.close(index); //再执行关闭
					layui.use(['laypage'], function() {
						layer.closeAll();
					});
				});
			};

			//向服务端发送删除指令
			twxAjax("publishMissionThing", "deleteDataListDataByTypeAndID", {
				type: "wsConfigTable",
				ID: row.ID
			}, false, cb_success, cb_error);

		});
	});

	// layui.use(['layer'],function(){
	//     var layer = layui.layer;
	//     layer.confirm('是否确认删除选中数据?', {skin:'layui-layer-lan',icon: 3, title:'删除提示'}, function(index){
	//         //do something
	//         //layer.close(index);
	//         layer.close(index);
	//     },function(index){
	//         layer.close(index);
	//     });
	// });
}



$(document).ready(function() {
	layui.use(['layer', 'laypage'], function() {
		reloadTable("wsConfigTable");
	});
});
