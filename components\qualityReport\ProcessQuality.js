/**
 * 过程结构树中的质量数据
 */
var ProcessQuality = function () {
	var othis = this;
	this.qualityPlanId = 0;
	//获取左侧选中的节点  默认选中根节点
	this.getSelTreeNode = function () {
		var ztreeObj = $.fn.zTree.getZTreeObj("dpTree");
		var selNodes = ztreeObj.getSelectedNodes();
		var treeNode;
		if (selNodes.length > 0) {
			treeNode = selNodes[0];
		} else {
			treeNode = ztreeObj.getNodeByParam("TREEID", 1, null);
		}
		return treeNode;
	};
	//初始化显示
	this.init = function () {
		var selNode = othis.getSelTreeNode();
		//判断是否为过程节点
		if (selNode.IS_PROCESS == 'true') {
			othis.hideMsg();
			othis.loadTypeSelect(selNode.TREEID);
		} else {
			othis.showMsg("请选择过程节点！");
		}
	};
	this.renderQualityTabs = function (treeId, tableConfigId) {
		$('#quality_tabs').tabs({
			onSelect: function (title, index) {
				var tab = $('#quality_tabs').tabs('getTab', index);
				var tableId = tab.panel('options').tableId;
				if (tableId == 'quality_summary_tab') {
					// othis.loadSummaryData(treeId, tableConfigId);
				}
			}
		});
	};
	//显示提示消息 并且隐藏其他元素
	this.showMsg = function (msg) {
		$("#qualityMsg").text(msg).show();
		$('#qualityTypeDiv').hide();
		$('#qualityTbr').hide();
		$('#qualityTableDiv').hide();
		$('#quality_tabs').hide();
	};
	//隐藏提示消息 并且显示其他元素
	this.hideMsg = function () {
		$("#qualityMsg").hide();
		$('#qualityTypeDiv').show();
		$('#qualityTbr').show();
		$('#qualityTableDiv').show();
		$('#quality_tabs').show();
	};
	this.showTableMsg = function (msg) {
		$('#qualityTableDiv').empty();
		$('#qualityTableDiv').append('<div style="float: left; width: 100%;color: red;padding-left: 15px;padding-top: 15px;">' + msg + '</div>');
	};
	//初始化表格上方的操作按钮
	this.initTbr = function (record, treeId) {

		//上传三级表
		$('#quality-upload-three').unbind("click").bind('click', function () {
			othis.uploadFile(record, treeId, "3");
		});
		//下载三级表模板
		$('#quality-download-three').unbind("click").bind('click', function () {
			othis.downloadTplFile(record, treeId, "3");
		});
		//上传策划表
		$('#quality-upload-plan').unbind("click").bind('click', function () {
			othis.uploadFile(record, treeId, "plan");
		});
		//下载策划表模板文件
		$('#quality-download-planTpl').unbind("click").bind('click', function () {
			othis.downloadTplFile(record, treeId, "plan");
		});

		//生成质量报告
		$('#generate-report').unbind("click").bind('click', function () {
			othis.generateReport(treeId);
		});

		//数据来源于MES显示手动同步
		if (record.mtype) {
			$('#quality-manualSync').show();
			//手动同步mes
			$('#quality-manualSync').unbind("click").bind('click', function () {
				var Iindex = layer.msg('正在同步,请不要关闭页面,请稍等......', {
					icon: 16,
					shade: 0.01,
					time: 0
				});
				var cb_success = function (data) {
					layer.close(Iindex);
					if (data.success) {
						layer.msg('同步成功！');
						loadType1Table(record, treeId);
					} else {
						layer.alert(data.msg, {
							icon: 2
						});
					}
				};
				var cb_error = function () {
					layer.close(Iindex);
					layer.alert('手动同步出错...', {
						icon: 2
					});
				};
				twxAjax("Thing.Fn.SecondTable", "manualSyncMes", {
					treeId: treeId,
					type: record.mtype,
					tableId: record.id,
					endY: record.endy,
					tableType: record.ctype
				}, true, cb_success, cb_error);
			});
		} else {
			$('#quality-manualSync').hide();
		}
	};
	//生成质量报告
	this.generateReport = function (treeId) {
		var cb_success = function (data) {
			if (data.success) {
				var name = data.data.FILENAME;
				var url = fileHandlerUrl + "/first/phase/download/file";
				var form = $("<form></form>").attr("action", url).attr("method", "post");
				form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", name));
				form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", data.data.FILEPATH));
				form.appendTo('body').submit().remove();
			} else {
				layer.alert(data.msg);
			}
		};
		//添加失败的弹窗
		var cb_error = function (xhr) {
			layer.alert('下载失败!', {
				icon: 2
			});
		};
		//同步新增
		twxAjax("Thing.Fn.SecondTable", "GenerateQualityReportByTreeId", {
			treeId: treeId
		}, false, cb_success, cb_error);
	};

	//下载模板文件
	this.downloadTplFile = function (record, treeId, type) {
		var path = "";
		var name = "";
		if (type == '3') {
			path = record.tpath;
			name = "三级";
		} else if (type = 'plan') {
			path = record.planPath;
			name = "策划";
		}
		if (path != undefined && path != '' && path != null) {
			var fileName = record.name + "_" + name + "表模板.xlsx";
			var url = fileHandlerUrl + "/first/phase/download/file";
			var form = $("<form></form>").attr("action", url).attr("method", "post");
			form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", fileName));
			form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", path));
			form.appendTo('body').submit().remove();
		} else {
			layer.alert("该节点未上传" + name + "表模板！", {
				icon: 2
			})
		}
	};
	//导出质量确认表excel
	this.exportQualityExcel = function (tableConfigId, treeId) {
		var loading;
		var url = fileHandlerUrl + "/table/quality/export";
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"treeId": treeId,
				"tableConfigId": tableConfigId,
				"fileName": $('#qualityType').combobox("getValue") + "质量确认表",
				"username": sessionStorage.getItem('username')
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				layer.close(loading);
			},
			failCallback: function (html, url) {
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});
	};
	//上传三级表或策划表
	this.uploadFile = function (record, treeId, type) {
		var name = "";
		var url = "";
		var fileDone;
		if (type == '3') {
			name = "三级";
			var params = '';
			params += 'type=' + record.ctype;
			params += '&tableId=' + record.id;
			params += '&fileType=3';
			params += '&endY=' + (record.ctype == '3' ? "" : record.endy);
			params += '&treeId=' + treeId;
			params += '&creator=' + sessionStorage.getItem('username');
			url = fileHandlerUrl + '/table/import/three?' + params;
			fileDone = function (res, index, upload) {
				if (res.success) {
					layer.closeAll();
					layer.msg("成功导入" + res.result + "条数据！");
					othis.postTableData(record.id, treeId);
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			};
		} else if (type = 'plan') {
			name = "策划";
			var params = '';
			params += 'type=1'; //代表上传的是策划表类型是质量数据
			params += '&tableConfigId=' + record.id;
			params += '&treeId=' + treeId;
			params += '&creator=' + sessionStorage.getItem('username');
			url = fileHandlerUrl + '/table/import/plan?' + params;
			fileDone = function (res, index, upload) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					othis.postTableData(record.id, treeId);
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			};
		}
		var area = ['350px', '220px'];

		var fileFlag = false;

		layer.open({
			title: '导入' + record.name + name + '表',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: area,
			content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			success: function () {
				var addTpl = $("#uploadHtml")[0].innerHTML;
				$("#uploadContent").append(addTpl);
			},
			btn: ['确认', '取消'],
			yes: function () {
				if (!fileFlag) {
					layer.alert('请选择需要导入的excel文件!', {
						icon: 2
					});
					return false;
				}
				uploadInst.config.url = url;
				if (device.ie && device.ie < 10) {
					$("form[target]")[0].action = url;
				}
				$('#uploadStart').click();
			},
			btn2: function () {
				layer.closeAll();
			}
		});

		form.render(null, 'uploadForm');

		var uploadInst = upload.render({
			elem: '#uploadChoice',
			url: url,
			auto: false,
			accept: 'file',
			field: 'uploadFile',
			exts: 'xls|xlsx',
			bindAction: '#uploadStart',
			dataType: "json",
			choose: function (obj) {
				fileFlag = true;
				var o = obj.pushFile();
				var filename = '';
				for (var k in o) {
					var file = o[k];
					filename = file.name;
				}
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			},
			before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
				// layer.load(); //上传loading
			},
			done: fileDone
		});
		if (device.ie && device.ie < 10) {
			$("input[name='uploadFile']").change(function () {
				var filename = $(this).val();
				filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			});
		}
	};
	//根据数据和名称 获取对应的 名称、代号和批次号
	this.getInfo = function (cols, row) {
		var nameStr = "名称";
		var codeStr = "代号";
		var actualBatchNumberStr = "实际批次号";
		var name = "",
			code = "",
			batchNumber = "";
		for (var i = 0; i < row.length; i++) {
			if (i == cols.indexOf(nameStr)) {
				name = row[i];
			} else if (i == cols.indexOf(codeStr)) {
				code = row[i];
			} else if (i == cols.indexOf(actualBatchNumberStr)) {
				batchNumber = row[i];
			}
		}
		return {
			name: name,
			code: code,
			batchNumber: batchNumber
		}
	};
	this.getParamValue = function (isBase, paramName, row, cols) {
		var paramValue = '';
		if (isBase == 1) {
			var paramNames = paramName.split(",");
			var tempArr = [];
			for (var i = 0; i < paramNames.length; i++) {
				tempArr.push(row[cols.indexOf(paramNames[i])]);
			}
			paramValue = tempArr.join(",");
		} else {
			paramValue = row[cols.indexOf(paramName)]
		}
		return paramValue;
	};
	//单行的质量数据确认
	this.singleLineConfirm = function (btn, treeId, tableConfigId) {
		var cols = $(btn).data("cols");
		var row = $(btn).data("row");
		var confirmer = sessionStorage.getItem('username');
		var paramName = $(btn).data("paramName");
		var paramId = $(btn).data("paramId");
		var isBase = $(btn).data("isBase");
		var onlyValue = $(btn).data("onlyValue");
		var paramValue = othis.getParamValue(isBase, paramName, row, cols);
		var cb_success = function (res) {
			if (res.success) {
				layer.msg(res.msg);
				othis.postTableData(tableConfigId, treeId);
			} else {
				layer.alert(res.msg);
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("确认质量数据出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "QualityDataConfirm", {
			onlyValue: JSON.stringify(onlyValue),
			tableConfigId: tableConfigId,
			treeId: treeId,
			paramId: paramId,
			paramName: paramName,
			paramValue: paramValue,
			confirmer: confirmer,
			type: 1
		}, false, cb_success, cb_error);

	};
	//初始化质量数据汇总表
	this.renderSummaryTable = function (cols) {
		//清除多选列和状态列
		cols[0].splice(0, 2);
		$('#qualitySummaryTable').datagrid({
			data: [],
			columns: cols,
			height: windowH - 130,
			singleSelect: true,
			remoteSort: false,
			// pagination: true,
			emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
			loadMsg: '正在加载数据...',
			striped: false,
			onHeaderContextMenu: function (e, field) {
				e.preventDefault();
				if (!othis.cmenu) {
					othis.cmenu = gridUtil.createColumnMenu('qualitySummaryTable');
				}
				othis.cmenu.menu('show', {
					left: e.pageX,
					top: e.pageY
				});
			},
			onLoadSuccess: function (data) {
				var rows = data.rows;
				var $datagrid = $('#qualitySummaryTable');
				if (rows.length > 0) {
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						var mergedInfo = row.mergedInfo;
						if (mergedInfo != "" && mergedInfo != undefined) {
							var mergeds = mergedInfo.split(",");
							for (var j = 0; j < mergeds.length; j++) {
								var merged = mergeds[j];
								var columnName = merged.split(":")[0];
								var rowspan = merged.split(":")[1];
								$datagrid.datagrid('mergeCells', {
									index: i,
									field: columnName,
									rowspan: rowspan
								});
							}
						}
					}
				}
				changeWidth('qualitySummaryTable');
				$("#qualitySummaryDiv .datagrid-body").css("overflow-x", "auto");
				var tableW = $("#qualitySummaryDiv").parent().width();
				$("#qualitySummaryDiv .datagrid-wrap").css("width", tableW + "px");
				$('#qualitySummaryTable').datagrid('loaded');
			}
		});
	}
	//请求质量数据汇总表表头
	this.postSummaryHeader = function (tableConfigId) {
		var cb_success = function (data) {
			if (data.success) {
				var dealCol = dealColumns(JSON.parse(data.result));
				othis.renderSummaryTable(dealCol.col);
			} else {
				layer.alert("表头获取失败！", {
					icon: 2
				});
			}
		};
		var cb_error = function () {
			layer.alert("表头获取失败！", {
				icon: 2
			});
		};
		twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', {
			id: tableConfigId
		}, false, cb_success, cb_error);
	};
	//加载质量数据汇总表数据
	this.loadSummaryData = function (treeId, tableConfigId) {
		othis.postSummaryHeader(tableConfigId);
		$('#qualitySummaryTable').datagrid('loading');
		var cb_success = function (data) {
			//调用成功后，渲染数据
			$('#qualitySummaryTable').datagrid('loadData', data.data);
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			table_config_id: tableConfigId,
			tree_id: treeId,
			username: sessionStorage.getItem('username')
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryQualitySummary', parmas, true, cb_success, cb_error);
	};

	//加载质量数据确认表
	this.loadTableData = function (data, treeId, tableConfigId, signs) {
		$("#qualityTableDiv").empty();
		$("#qualityTableDiv").css('height', (windowH - 120) + 'px');
		$("#qualityTableDiv").show();

		var statusStr = "状态";
		var confirmerStr = "确认人";
		var $table = $('<table class="my-table layui-table"></table>');
		for (var i = 0; i < data.length; i++) {
			var table = data[i];
			var isBase = table.isBase;
			var datas = table.datas;
			var onlyValues = table.onlyValues;
			var cols = table.cols;
			var statusIndex = cols.indexOf(statusStr);
			var confirmerIndex = cols.indexOf(confirmerStr);
			var relationParamName = table.relationParamName;
			var relationParamId = table.relationParamId;
			//添加合并行
			$table.append('<tr><td class="table-name" colspan=' + cols.length + '>' + table.name + '</td></tr>');
			//添加表头
			var $headerTr = $('<tr></tr>');
			for (var j = 0; j < cols.length; j++) {
				$headerTr.append('<td class="table-head" align="' + planTableAlign + '">' + cols[j] + '</td>');
			}
			$table.append($headerTr);
			//添加表数据
			for (var j = 0; j < datas.length; j++) {
				var $tr = $('<tr></tr>');
				var d = datas[j];
				var onlyValue = onlyValues[j];
				for (var x = 0; x < d.length; x++) {
					if (x == confirmerIndex) {
						//确认人一栏  添加确认按钮
						if (d[x] == '') {
							var $td = $('<td align="' + planTableAlign + '"></td>');
							var $btn = $('<button class="layui-btn layui-btn-sm layui-btn-normal">确认</button>');
							$btn.data('row', d)
								.data('cols', cols)
								.data('paramName', relationParamName)
								.data('paramId', relationParamId)
								.data("isBase", isBase)
								.data("onlyValue", onlyValue);
							$btn.bind('click', function () {
								othis.singleLineConfirm(this, treeId, tableConfigId);
							});
							$td.append($btn);
							$tr.append($td);
						} else {
							$tr.append('<td align="' + planTableAlign + '">' + d[x] + '</td>');
						}

					} else if (x == statusIndex) {
						//状态一栏 可编辑 如果确认了不可以编辑  但是确认之后有权限的人可以继续修改
						var isEidt = false;
						//
						if (d[confirmerIndex] == '') {
							isEidt = true;
						} else {
							var allfuns = sessionStorage.getItem('funcids');
							var funcArr = allfuns.split(',');
							if (contains(funcArr, 'modify-plan-status')) {
								isEidt = true;
							} else {
								isEidt = false;
							}
						}
						if (isEidt) {
							var $td = $('<td align="' + planTableAlign + '" data-field="status">' + d[x] + '</td>');
							$td.data('row', d)
								.data('cols', cols)
								.data('paramName', relationParamName)
								.data('paramId', relationParamId)
								.data("isBase", isBase)
								.data("onlyValue", onlyValue);
							$tr.append($td);
						} else {
							$tr.append('<td align="' + planTableAlign + '">' + d[x] + '</td>');
						}

					} else {
						$tr.append('<td align="' + planTableAlign + '">' + d[x] + '</td>');
					}
				}
				$table.append($tr);
			}
		}
		$("#qualityTableDiv").append($table);
		$('#qualityTableDiv td[data-field="status"]').dblclick(function () {
			var $td = $(this);
			// 根据表格文本创建文本框 并加入表表中--文本框的样式自己调整
			var text = $td.text();
			$td.text("");
			var statusArr = ['监测', '固封', '冻结'];
			var $form = $('<div class="layui-form"></div>');
			var $select = $('<select name="status" style="width:60px;" lay-filter="status"></select>');
			for (var m = 0; m < statusArr.length; m++) {
				if (text == statusArr[m]) {
					$select.append('<option value = "' + statusArr[m] + '" selected >' + statusArr[m] + '</option>');
				} else {
					$select.append('<option value = "' + statusArr[m] + '">' + statusArr[m] + '</option>');
				}
			}
			$form.append($select);
			$td.append($form);
			form.render('select');
			$form.find('.layui-form-select').css('width', '80px');
			form.on('select(status)', function (data) {
				$form.remove();
				$td.text(data.value);
				if (text != data.value) {
					var cols = $td.data("cols");
					var row = $td.data("row");
					var paramName = $td.data("paramName");
					var paramId = $td.data("paramId");
					var isBase = $td.data("isBase");
					var onlyValue = $td.data("onlyValue");
					var paramValue = othis.getParamValue(isBase, paramName, row, cols);
					var cb_success = function (res) {
						if (res.success) {
							layer.msg(res.msg);
							othis.postTableData(tableConfigId, treeId);
						} else {
							layer.alert(res.msg);
						}
					}
					//请求失败的回调
					var cb_error = function (xhr, textStatus, errorThrown) {
						layer.alert("更新质量数据状态出错！", {
							icon: 2
						});
					};
					twxAjax("Thing.Fn.SecondTable", "UpdateQualityDataStatus", {
						onlyValue: JSON.stringify(onlyValue),
						tableConfigId: tableConfigId,
						treeId: treeId,
						paramId: paramId,
						paramName: paramName,
						paramValue: paramValue,
						status: data.value,
						creator: sessionStorage.getItem('username'),
						type: 1
					}, false, cb_success, cb_error);
				}
			});
		});

		var $signDiv = $('<div class="layui-row"></div>');
		var $innerDiv = $('<div class="layui-col-md6"></div>');
		var $outerDiv = $('<div class="layui-col-md6"></div>');
		var $innerWacomBtn = $('<button class="layui-btn layui-btn-sm layui-btn-normal">所内人员手写板签字</button>');
		var $innerMouseBtn = $('<button class="layui-btn layui-btn-sm">所内人员鼠标签字</button>');
		var $outerWacomBtn = $('<button class="layui-btn layui-btn-sm layui-btn-normal">所外人员手写板签字</button>');
		var $outerMouseBtn = $('<button class="layui-btn layui-btn-sm">所外人员鼠标签字</button>');
		$innerWacomBtn.bind('click', function () {
			othis.sign(1, "wacom");
		});
		$innerMouseBtn.bind('click', function () {
			othis.sign(1, "mouse");
		});
		$outerWacomBtn.bind('click', function () {
			othis.sign(2, "wacom");
		});
		$outerMouseBtn.bind('click', function () {
			othis.sign(2, "mouse");
		});


		$innerDiv.append($innerWacomBtn).append($innerMouseBtn);
		$outerDiv.append($outerWacomBtn).append($outerMouseBtn);
		$signDiv.append($innerDiv).append($outerDiv);

		var $signImgDiv = $('<div class="layui-row"></div>');
		var $innerImgDiv = $('<div class="layui-col-md6" style = "height:1px;" id="data-innerImgDiv"></div>');
		var $outerImgDiv = $('<div class="layui-col-md6" id="data-outerImgDiv"></div>');
		for (var i = 0; i < signs.length; i++) {
			var sign = signs[i];
			var type = sign['TYPE'];
			var img = sign['IMG'];
			var $img = $('<img>').attr('src', img).attr('class', 'sign-img');
			if (type == 1) {
				$innerImgDiv.append($img);
			} else {
				$outerImgDiv.append($img);
			}
		}
		$signImgDiv.append($innerImgDiv).append($outerImgDiv);
		$("#qualityTableDiv").append($signDiv).append($signImgDiv);
	};

	this.sign = function (type, mode) {
		var title = type == 1 ? '所内签名' : '所外签名';
		var imgDiv = type == 1 ? 'data-innerImgDiv' : 'data-outerImgDiv';
		if (device.ie && device.ie < 10) {

			layer.alert('请在Chrome浏览器中使用！', {
				icon: 2
			});

		} else {
			if (mode == 'wacom') {

				function saveSignToBase64() {
					if (!!signPlugin) {
						//Get the signPlugin's signature image data.
						signPlugin.saveSignToBase64( /*615, 272*/ 0, 0, function (state, args) {
							if (state) {
								var img_base64_data = args[0];
								//Show the signature image.
								var img_base64 = "data:image/png;base64," + img_base64_data;
								if (img_base64 == blankSign) {
									layer.alert('获取签名图形失败！', {
										icon: 2
									});
								} else {
									document.getElementById("img_sign_result").src = img_base64;

									var dataUrl = $('#img_sign_result').attr("src");

									if (dataUrl) {
										var cb_success = function (res) {
											if (res.success) {
												var $img = $('<img>').attr('src', dataUrl).attr('class', 'sign-img');
												$("#" + imgDiv).prepend($img);
												layer.closeAll();
												layer.msg('签名成功！');
											} else {
												layer.alert(res.msg);
											}
										}
										//请求失败的回调
										var cb_error = function (xhr, textStatus, errorThrown) {
											layer.alert("签名出错！", {
												icon: 2
											});
										};
										//同步新增
										twxAjax("Thing.Fn.SecondTable", "AddQualitySign", {
											qualityPlanId: othis.qualityPlanId,
											type: type,
											img: dataUrl,
											creator: sessionStorage.getItem('username')
										}, false, cb_success, cb_error);
									} else {
										layer.alert('请先签名！', {
											icon: 2
										});
									}
								}


								debugPrint("saveSignToBase64 OK");
								//Submit the signature base64 string to the server
								//...
							} else {
								debugPrint("saveSignToBase64 error,description:" + args[0]);
							}
						});
					}
				}

				/*confirm event*/
				signPlugin.onConfirm = function () {
					saveSignToBase64();
					endSign();
				};

				//在页面中构建一个隐藏的img标签用来存储签名图片 
				var $signImg = $('<img style="display:none;" src="" id="img_sign_result"/>');
				$signImg.appendTo($('body'));
				beginSign();
			} else {
				layer.open({
					title: title,
					type: 1,
					area: ['900px', '560px'],
					content: '<div id = "confirm-signContent"></div>',
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					resize: false,
					btn: ['确定', '重签', '取消'],
					yes: function () {
						var dataUrl = $('.js-signature').eq(0).jqSignature('getDataURL');
						var $img = $('<img>').attr('src', dataUrl).attr('class', 'sign-img');

						var cb_success = function (res) {
							if (res.success) {
								$("#" + imgDiv).prepend($img);
								layer.closeAll();
								layer.msg('签名成功！');
							} else {
								layer.alert(res.msg);
							}
						}
						//请求失败的回调
						var cb_error = function (xhr, textStatus, errorThrown) {
							layer.alert("签名出错！", {
								icon: 2
							});
						};
						//同步新增
						twxAjax("Thing.Fn.SecondTable", "AddQualitySign", {
							qualityPlanId: othis.qualityPlanId,
							type: type,
							img: dataUrl,
							creator: sessionStorage.getItem('username')
						}, false, cb_success, cb_error);
					},
					btn2: function () {
						$('.js-signature').eq(0).jqSignature('clearCanvas');
						return false;
					},
					btn3: function () {
						return true;
					},
					success: function () {
						var tpl = '<div class="js-signature" data-width="900" data-height="450" data-border="1px solid #333"\
										data-line-color="#000" data-line-width="3" data-auto-fit="true">\
									</div>'
						$("#confirm-signContent").append(tpl);
						$('.js-signature').jqSignature();

					}
				});
			}


		}
	};
	//从服务器请求质量数据
	this.postTableData = function (tableConfigId, treeId) {
		var loadIndex = layer.load();
		var cb_success = function (res) {
			if (res.success) {
				othis.loadTableData(res.data, treeId, tableConfigId, res.signs);
				othis.qualityPlanId = res.id;
				//下载策划表
				$('#quality-download-plan').show().unbind("click").bind('click', function () {
					downloadFile(res.filepath, res.filename);
				});
				//导出质量表
				$('#quality-export').show().unbind("click").bind('click', function () {
					othis.exportQualityExcel(tableConfigId, treeId);
				});

			} else {
				othis.qualityPlanId = 0;
				$('#quality-download-plan').hide();
				$('#quality-export').hide();
				othis.showTableMsg(res.msg);
			}
			layer.close(loadIndex);
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("加载质量数据出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "QueryProcessQualityData", {
			treeId: treeId,
			tableConfigId: tableConfigId
		}, true, cb_success, cb_error);
	};
	//加载质量数据类型下拉框
	this.loadTypeSelect = function (treeId) {
		var loadIndex = layer.load();
		var cb_success = function (res) {
			layer.close(loadIndex);
			if (res.data.length > 0) {
				$('#qualityType').combobox({
					data: res.data,
					valueField: 'name',
					textField: 'name',
					editable: false,
					width: 300,
					panelHeight: 400,
					onSelect: function (record) {
						othis.initTbr(record, treeId);
						// othis.renderQualityTabs(treeId, record.id);
						othis.postTableData(record.id, treeId);
						othis.loadSummaryData(treeId, record.id);
					}
				});
				$('#qualityType').combobox("select", res.data[0].name);
			} else {
				othis.showMsg("暂无质量数据！");
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.close(loadIndex);
			layer.alert("加载质量数据类型下拉框出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "GetProductTypeByTreeId", {
			treeId: treeId,
			username: sessionStorage.getItem('username')
		}, true, cb_success, cb_error);
	};

	this.init();
}