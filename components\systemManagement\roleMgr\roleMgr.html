<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
    <!-- <link href="../../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css"> -->
    <link rel="stylesheet" href="../../../plugins/layui/css/layui.css">
    <!-- <link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">-->
	<link rel="stylesheet" href="../../../css/icon.css">
    <!-- <script src="../../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../../plugins/InsdepUI/jquery.easyui.min.js"></script> -->
    <!-- <script src="../../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
    <script src="../../../plugins/layui/layui.js"></script>
    <link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
    <script src="../../../plugins/easyui/jquery.min.js"></script>
    <script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
    <script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
    <script src="../../js/config/twxconfig.js"></script>
    <script src="../../js/util.js"></script>
	<script src="../../js/logUtil.js"></script>
    <script src="../../js/intercept.js"></script>
    <title>角色管理</title>
    <style>
        .layui-form-label{
            width: 95px;
        }
        .layui-form-checkbox span{
            height:18px;
        }
    </style>
</head>

<body>
    <div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
        <div data-options="region:'west',split:true,collapsible:false,border:false" title="角色管理" style="width:450px;">
			<div id="roleTable_tb" style="padding: 5px;">
				 <button type="button"  class="layui-btn layui-btn-sm" id="roleadd">
				   <i class="layui-icon">&#xe608;</i> 添加
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm  layui-btn-warm" id="roleedit">
				   <i class="layui-icon">&#xe642;</i> 编辑
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm layui-btn-danger" id="roledel">
				   <i class="layui-icon">&#xe640;</i> 删除
				 </button>
			</div>
            <div id="roleTable"></div>
        </div>
        <div data-options="region:'center',border:false">
            <div id="root_layout_sub" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
                <div data-options="region:'west',split:true,collapsible:false" title="菜单分配" style="width:300px;">
                    <div id="root_layout_sub2" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
                        <div data-options="region:'north',split:false,collapsible:false,border:false" style="height:48px;padding: 8px;">
                            <button type="button" id="btnassignmenu" class="layui-btn layui-btn-sm layui-btn-normal">分配菜单</button>
                        </div>
                        <div data-options="region:'center',border:false">
                            <div id="selectedMenu">
                                <div id="noData" style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>请先分配菜单...</font></div>
                                <ul id="selectedMenuTree" data-options="border:false"></ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-options="region:'center'" title="功能分配">
                    <div style="height:50px;padding:8px;">
                        <button type="button" id="ckall" class="layui-btn layui-btn-sm">全选</button>
                        <button type="button" id="unckall" class="layui-btn layui-btn-sm">全不选</button>
                        <button type="button" id="btnfuncsave" class="layui-btn layui-btn-sm layui-btn-normal">保存</button>
                    </div>
                    <div id="view"></div>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="role.js"></script>

<script src="assignMenu.js"></script>

<script id="func" type="text/html">
    {{# if(JSON.stringify(d) == '{}'){ }}
        <div  style="width:100%;padding:10px;text-align:left;font-size:14px;"><font color=red>请先分配菜单...</font></div>
    {{# }else{ }}
    <form class="layui-form" lay-filter="func">
    {{#     var funcsid = getSelectedData()[0].FUNCIDS; }}
    {{#     for(var x in d){ }}
    {{#         if(d[x].length == 0){continue;} }}
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
            <legend>{{ x }}</legend>
        </fieldset>
        
            <div class="layui-form-item" pane="">
                <div class="layui-input-block">
                    {{# layui.each(d[x],function(index,item){ }}
                        <input type="checkbox" id="{{ item.funcbtnid }}" name="func" lay-skin="primary" title="{{ item.funcname }}" >
                    {{# }); }}
                </div>
            </div>
        
    {{#     } }}
    </form>
    {{# } }}
</script>