//加载表格
var loadTableData = function(tableId, data) {
	$("#" + tableId).datagrid({
		data: data,
		singleSelect: false,
		fitColumns: true,
		striped: true,
		rownumbers: true,
		// toolbar: [{
		// 		iconCls: 'icon-myadd',
		// 		text: '新增',
		// 		id:'myadd',
		// 		funcId:'is',
		// 		handler: function() {
		// 			addData();
		// 		}
		// 	}, {
		// 		iconCls: 'icon-myedit',
		// 		text: '编辑',
		// 		handler: function() {
		// 			editData();
		// 		}
		// 	}, {
		// 		iconCls: 'icon-myremove',
		// 		text: '删除',
		// 		handler: function() {
		// 			deleteData();
		// 		}
		// 	},
		// 	'-',
		// 	{
		// 		id: tableId + '_save',
		// 		iconCls: 'icon-cp-save',
		// 		text: '保存提交'
		// 	}
		// ],
		toolbar: "#" + tableId + "_tb",
		fit: true,
		columns: [
			[{
					field: "ck",
					checkbox: true,
				},
				{
					field: "ID",
					title: "ID",
					hidden: true,
					width: 40,
				},
				{
					field: "FILE_TYPE",
					title: "文件类别",
					width: 255,
					align: "center",
				},
				{
					field: "GATHERING_METHOD",
					title: "采集方式",
					width: 100,
					align: "center",
					formatter: function(value) {
						if (value === undefined || value == "") {
							return '<span class="layui-badge layui-bg-warm">未定义</span>';
						}
						if (value === "自动采集") {
							return '<span class="layui-badge layui-bg-blue">' + value + "</span>";
						} else {
							return '<span class="layui-badge layui-bg-green">' + value + "</span>";
						}
					},
				},
				{
					field: "SOURCE_SYSTEM",
					title: "来源系统",
					width: 100,
					align: "center",
				},
				{
					field: "MODIFIED_TIMESTAMP",
					title: "修改时间",
					width: 120,
					align: "center",
				},
				{
					field: "DELIVERY_STATE",
					title: "交付状态",
					width: 100,
					align: "center",
				},
				{
					field: "COLLECTSTATUS",
					title: "采集状态",
					width: 140,
					align: "center",
					formatter: function(value) {
						if (value === "0") {
							//未提交
							return '<span class="layui-badge layui-bg-blue">未提交</span>';
						} else if (value === "1") {
							//运行
							return '<span class="layui-badge layui-bg-green">运行</span>';
						} else if (value === "2") {
							//已暂停
							return '<span class="layui-badge layui-bg-warm">已暂停</span>';
						}
					},
				},
			],
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: "正在加载数据...",
	});
};

//加载table的数据
var reloadTable = function(tableName) {
	$("#" + tableName).datagrid("loading");

	layui.use(["layer", "laypage"], function() {
		var treeId = "";
		var selNodes = ztreeObj.getSelectedNodes();
		if (selNodes.length > 0) {
			treeId = selNodes[0].TREEID;
		}

		var type = "";
		if (tableName === "design_list_table") {
			type = "DESIGN_DATA_LIST";
		} else if (tableName === "craft_list_table") {
			type = "CRAFT_DATA_LIST";
		} else if (tableName === "processcontrol_list_table") {
			type = "PROCESS_CONTROL_LIST";
		} else if (tableName === "quanlitycontrol_list_table") {
			type = "QUALITY_CONTROL_LIST";
		}
		//无数据的渲染
		// loadTableData(tableName, []);
		var cb_success = function(data) {
			// loadTableData(tableName, data.rows);
			$("#" + tableName).datagrid("loadData", data.rows);
			$("#" + tableName).datagrid("loaded");
			// $('#root_layout_tabs').loading('stop');
		};
		//请求失败的回调
		var cb_error = function(xhr, textStatus, errorThrown) {
			if (xhr.status !== 200) {
				layui.use(["layer"], function() {
					var layer = layui.layer;
					layer.msg("后台错误，请联系管理员!", {
						icon: 2,
						anim: 6,
					});
				});
			}
		};
		//异步加载数据
		if (treeId !== "") {
			//根据关联的数据ID进行数据的查询
			//获取选中的数据包的ID
			var sels = $("#datapkgref").datagrid("getSelections");
			var dpid = ""; //数据包ID
			if (sels.length > 0) {
				dpid = sels[0].ID;
				twxAjax(
					"publishMissionThing",
					"getAllDataListDataByTypeAndNodeName", {
						type: type,
						NODECODE: dpid,
					},
					true,
					cb_success,
					cb_error
				);
			} else {
				$("#" + tableName).datagrid("loadData", []);
				$("div.datagrid-empty").html('<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=blue>请先创建数据包...</font></div>');
				$("#" + tableName).datagrid("loaded");
			}
		} else {
			// $('#root_layout_tabs').loading('stop');
			$("#" + tableName).datagrid("loaded");
		}
	});
};

//渲染第一个表格
//reloadTable('design_list_table');

//页面件参数传递使用
var selectedData = {
	tabName: undefined,
	data: undefined,
	node: undefined,
};

//获取选中的zTree节点
function getSelectedNode() {
	// var treeObj = ztreeObj;
	// var selNodes = treeObj.getSelectedNodes();
	// if (selNodes.length == 0) {
	// 	layui.use(['layer'], function() {
	// 		var layer = layui.layer;
	// 		layer.alert('请选择左侧的过程节点...', {
	// 			icon: 2
	// 		});
	// 	});
	// 	return false;
	// }
	// var selNode = selNodes[0];
	// if (selNode.NODETYPE !== 'leaf') {
	// 	layui.use(['layer'], function() {
	// 		var layer = layui.layer;
	// 		layer.alert('当前节点未非过程节点,请选择过程节点...', {
	// 			icon: 2
	// 		});
	// 	});
	// 	return false;
	// }

	//判断是否存在相应的数据包
	var sels = $("#datapkgref").datagrid("getSelections");
	if (sels.length == 0) {
		layui.use("layer", function() {
			layer.alert("请先创建数据包...", {
				icon: 2,
			});
		});
		return false;
	}

	// selectedData.node = selNode;
	return true;
}

//新增按钮操作
function addData() {
	if (!getSelectedNode()) {
		return;
	}
	var selectedTab = $("#root_layout_tabs").tabs("getSelected");
	var tabId = selectedTab.panel("options").name;
	selectedData.tabName = tabId;
	layui.use(["layer", "form", "insertSelect"], function() {
		var layer = layui.layer,
			form = layui.form,
			insertSelect = layui.insertSelect;
		layer.open({
			title: "新增",
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ["450px", "330px"],
			content: '<div id="addContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ["新增", "重置", "关闭"],
			yes: function() {
				$("#btn_add").click();
			},
			btn2: function() {
				$("#btn_reset").click();
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				var addTpl = $("#addHtml")[0].innerHTML;
				$("#addContent").append(addTpl);
			},
		});

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "自动采集清单",
			},
			false,
			function(data) {
				var ds = [];
				for (var i = 0; i < data.rows.length; i++) {
					$("#fileType").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
					ds.push(data.rows[i].NAME);
				}
				insertSelect.render({
					elem: "#fileType",
					data: ds,
				});
			}
		);

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "采集方式",
			},
			false,
			function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					$("#collectmethod").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
				}
			}
		);

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "来源系统",
			},
			false,
			function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					$("#systems").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
				}
			}
		);

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "交付状态",
			},
			false,
			function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					$("#dstates").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
				}
			}
		);

		form.render(null, "addForm");

		form.on("select(gatheringmethod)", function(d) {
			if (d.value === "手动采集") {
				// form.val('addForm',{
				// 	SOURCE_SYSTEM:''
				// });
				$("#systems").val("");
				form.render();
				$("#source_div").hide();
			} else {
				$("#source_div").show();
			}
		});

		//监听提交
		form.on("submit(addData)", function(data) {
			var param = data.field;
			if (param.GATHERING_METHOD == "手动采集") {
				param.SOURCE_SYSTEM = "/";
			}
			//获取父页面的参数
			var tableName = selectedData.tabName;
			var type = "";
			//计算后台应该使用哪张表
			if (tableName === "design_list_table") {
				param.type = "DESIGN_DATA_LIST";
				type = "设计类";
			} else if (tableName === "craft_list_table") {
				param.type = "CRAFT_DATA_LIST";
				type = "工艺类";
			} else if (tableName === "processcontrol_list_table") {
				param.type = "PROCESS_CONTROL_LIST";
				type = "过程控制";
			} else if (tableName === "quanlitycontrol_list_table") {
				param.type = "QUALITY_CONTROL_LIST";
				type = "质量综合";
			}
			var datapkg = $("#datapkgref").datagrid("getSelections")[0];
			param.NODECODE = datapkg.ID; //selectedData.node.TREEID; //数据包的ID
			param.NODENAME = datapkg.ID;
			//添加成功的弹窗
			var cb_success = function(data) {
				//新增完成后需要刷新界面
				//提示完成后，点击确定再刷新界面
				layer.closeAll();
				reloadTable(tableName);
				logRecord("新增", "策划构建-数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下新建项目清单(类型：" + type + "、文件类别：" + param.FILE_TYPE + "、采集方式：" + param.GATHERING_METHOD + "、来源系统：" + param.SOURCE_SYSTEM + "、交付状态：" + param.DELIVERY_STATE + ")", 1);
				layer.msg("新增成功");
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {
				logRecord("新建", "策划构建-数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下新建项目清单(类型：" + type + "、文件类别：" + param.FILE_TYPE + "、采集方式：" + param.GATHERING_METHOD + "、来源系统：" + param.SOURCE_SYSTEM + "、交付状态：" + param.DELIVERY_STATE + ")", 0);
				layer.alert("新增失败!", {
					icon: 2,
				});
			};
			//同步新增
			twxAjax("publishMissionThing", "AddDataToDataListTable", param, false, cb_success, cb_error);

			return false;
		});
	});
}

//编辑按钮操作
function editData() {
	var selectedTab = $("#root_layout_tabs").tabs("getSelected");
	var tabId = selectedTab.panel("options").name;

	selectedData.tabName = tabId;

	//获取选中的数据
	var datas = $("#" + tabId).datagrid("getSelections");
	if (datas.length == 0) {
		//提示用户，请选择待编辑的数据
		layui.use("layer", function() {
			var layer = layui.layer;
			layer.alert("请选择待编辑数据...", {
				icon: 2,
			});
		});
		return;
	} else if (datas.length > 1) {
		layui.use("layer", function() {
			layer.alert("无法进行批量编辑...", {
				icon: 2,
			});
		});
		return;
	}

	selectedData.data = datas[0];

	layui.use(["layer", "form", "insertSelect"], function() {
		var layer = layui.layer,
			form = layui.form,
			insertSelect = layui.insertSelect;
		layer.open({
			title: "编辑",
			type: 1,
			shadeClose: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ["450px", "330px"],
			content: '<div id="editContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ["更新", "重置", "关闭"],
			yes: function() {
				$("#btn_update").click();
			},
			btn2: function() {
				$("#btn_reset").click();
				form.val("addForm", selectedData.data);
				if (selectedData.data.GATHERING_METHOD === "手动采集") {
					$("#systems").val("");
					form.render();
					$("#source_div").hide();
				}
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				var editTpl = $("#addHtml")[0].innerHTML;
				$("#editContent").append(editTpl);
			},
		});

		form.on("select(gatheringmethod)", function(d) {
			if (d.value === "手动采集") {
				// form.val('addForm',{
				// 	SOURCE_SYSTEM:''
				// });
				$("#systems").val("");
				form.render();
				$("#source_div").hide();
			} else {
				$("#source_div").show();
			}
		});

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "自动采集清单",
			},
			false,
			function(data) {
				var ds = [];
				for (var i = 0; i < data.rows.length; i++) {
					$("#fileType").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
					ds.push(data.rows[i].NAME);
				}
				if (!contains(ds, selectedData.data["FILE_TYPE"])) {
					$("#fileType").append('<option value="' + selectedData.data["FILE_TYPE"] + '">' + selectedData.data["FILE_TYPE"] + "</option>");
					ds.push(selectedData.data["FILE_TYPE"]);
				}
				insertSelect.render({
					elem: "#fileType",
					data: ds,
				});
			}
		);

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "采集方式",
			},
			false,
			function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					$("#collectmethod").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
				}
			}
		);

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "来源系统",
			},
			false,
			function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					$("#systems").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
				}
			}
		);

		twxAjax(
			"Thing.Fn.SystemDic",
			"GetDicDataByDicType", {
				typename: "交付状态",
			},
			false,
			function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					$("#dstates").append('<option value="' + data.rows[i].NAME + '">' + data.rows[i].NAME + "</option>");
				}
			}
		);

		form.render(null, "addForm");
		form.val("addForm", selectedData.data);
		if (selectedData.data.GATHERING_METHOD === "手动采集") {
			$("#systems").val("");
			form.render();
			$("#source_div").hide();
		}
		$("#fileType").next().find("input:first").val(selectedData.data["FILE_TYPE"]);
		$("#fileType").val(selectedData.data["FILE_TYPE"]);
		//监听提交
		form.on("submit(updateData)", function(data) {
			var param = data.field;
			if (param.GATHERING_METHOD == "手动采集") {
				param.SOURCE_SYSTEM = "/";
			}
			//获取父页面的参数
			var tableName = selectedData.tabName;
			var type = "";
			//计算后台应该使用哪张表
			if (tableName === "design_list_table") {
				param.type = "DESIGN_DATA_LIST";
				type = "设计类";
			} else if (tableName === "craft_list_table") {
				param.type = "CRAFT_DATA_LIST";
				type = "工艺类";
			} else if (tableName === "processcontrol_list_table") {
				param.type = "PROCESS_CONTROL_LIST";
				type = "过程控制";
			} else if (tableName === "quanlitycontrol_list_table") {
				param.type = "QUALITY_CONTROL_LIST";
				type = "质量综合";
			}
			param.ID = selectedData.data.ID;
			var datapkg = $("#datapkgref").datagrid("getSelections")[0];
			param.NODECODE = datapkg.ID; //selectedData.node.TREEID; //数据包的ID
			param.NODENAME = datapkg.ID;
			//添加成功的弹窗
			var cb_success = function(data) {
				//新增完成后需要刷新界面
				//提示完成后，点击确定再刷新界面
				layer.closeAll();
				reloadTable(tableName);
				logRecord("编辑", "策划构建-数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(ID：" + param.ID + "、类型：" + type + "、文件类别：" + selectedData.data.FILE_TYPE + "、采集方式：" + selectedData.data.GATHERING_METHOD + "、来源系统：" + (selectedData.data.SOURCE_SYSTEM == undefined ? "" : selectedData.data.SOURCE_SYSTEM) + "、交付状态：" + selectedData.data.DELIVERY_STATE + ")更新为(类型：" + type + "、文件类别：" + param.FILE_TYPE + "、采集方式：" + param.GATHERING_METHOD + "、来源系统：" + param.SOURCE_SYSTEM + "、交付状态：" + param.DELIVERY_STATE + ")", 1);
				layer.msg("更新成功");
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {
				logRecord("更新", "策划构建-数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(ID：" + param.ID + "、类型：" + type + "、文件类别：" + selectedData.data.FILE_TYPE + "、采集方式：" + selectedData.data.GATHERING_METHOD + "、来源系统：" + (selectedData.data.SOURCE_SYSTEM == undefined ? "" : selectedData.data.SOURCE_SYSTEM) + "、交付状态：" + selectedData.data.DELIVERY_STATE + ")更新为(类型：" + type + "、文件类别：" + param.FILE_TYPE + "、采集方式：" + param.GATHERING_METHOD + "、来源系统：" + param.SOURCE_SYSTEM + "、交付状态：" + param.DELIVERY_STATE + ")", 0);
				layer.alert("编辑失败!", {
					icon: 2,
				});
			};

			//应该使用update进行参数的传递
			twxAjax("publishMissionThing", "updateDataToDataListTable", param, false, cb_success, cb_error);
			return false;
		});
	});
}

//删除按钮操作
function deleteData() {
	var selectedTab = $("#root_layout_tabs").tabs("getSelected");
	var tabId = selectedTab.panel("options").name;
	layui.use(["layer"], function() {
		//需要判断是否选中了数据
		//获取选中的数据
		var datas = $("#" + tabId).datagrid("getSelections");
		if (datas.length == 0) {
			//提示用户，请选择待编辑的数据
			layui.use("layer", function() {
				var layer = layui.layer;
				layer.alert("请选择需要删除的数据...", {
					icon: 2,
				});
			});
			return;
		}
		var layer = layui.layer;
		layer.confirm(
			"是否确认删除选中数据?", {
				// skin: 'layui-layer-lan',
				icon: 3,
				title: "删除提示",
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
			},
			function(index) {
				var row = datas[0];
				var ids = "";
				for (var i = 0; i < datas.length; i++) {
					ids += "," + datas[i].ID;
				}
				if (ids !== "") {
					ids = ids.substring(1);
				}
				var type = "";
				var typeName = "";
				if (tabId === "design_list_table") {
					type = "DESIGN_DATA_LIST";
					typeName = "设计类";
				} else if (tabId === "craft_list_table") {
					type = "CRAFT_DATA_LIST";
					typeName = "工艺类";
				} else if (tabId === "processcontrol_list_table") {
					type = "PROCESS_CONTROL_LIST";
					typeName = "过程控制";
				} else if (tabId === "quanlitycontrol_list_table") {
					type = "QUALITY_CONTROL_LIST";
					typeName = "质量综合";
				}
				var datapkg = $("#datapkgref").datagrid("getSelections")[0];
				var cb_success = function(data) {
					layer.closeAll();
					reloadTable(tabId);
					//记录日志
					logRecord("删除", "策划构建-删除数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(IDs：" + ids + "、类型：" + typeName + ")", 1);
					layer.msg("删除成功");
				};
				//添加失败的弹窗
				var cb_error = function(xhr) {
					logRecord("删除", "策划构建-删除数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(IDs：" + ids + "、类型：" + typeName + ")", 0);
					layer.alert("删除失败", {
						icon: 2,
					});
				};

				//向服务端发送删除指令
				// twxAjax("publishMissionThing", "deleteDataListDataByTypeAndID", {
				// 	type: type,
				// 	ID: row.ID
				// }, false, cb_success, cb_error);

				twxAjax(
					"Thing.Fn.PlanBuild",
					"DeletePlanListByID", {
						type: type,
						ids: ids,
					},
					false,
					cb_success,
					cb_error
				);
			}
		);
	});

	// layui.use(['layer'],function(){
	//     var layer = layui.layer;
	//     layer.confirm('是否确认删除选中数据?', {skin:'layui-layer-lan',icon: 3, title:'删除提示'}, function(index){
	//         //do something
	//         //layer.close(index);
	//         layer.close(index);
	//     },function(index){
	//         layer.close(index);
	//     });
	// });
}

var initBtnSave = function(tableName, layui) {
	$("#" + tableName + "_save").bind("click", function() {
		var layer = layui.layer;
		if (!getSelectedNode()) {
			return;
		}
		var allData = $("#" + tableName).datagrid("getData");
		if (allData.rows.length == 0) {
			layer.alert("当前类别下没有项目清单可以采集...", {
				icon: 2,
			});
			return;
		}
		var sels = $("#" + tableName).datagrid("getSelections");
		if (sels.length == 0) {
			layer.alert("请至少选择一条数据！", {
				icon: 2,
			});
			return;
		}
		//对提交的数据进行校验，校验所有自动采集的数据是否在配置的接口中
		var datas = [];
		var autoCollects = []; //存放自动采集的配置
		for (var i = 0; i < sels.length; i++) {
			var row = sels[i];
			if (row.COLLECTSTATUS !== "1") {
				datas.push(row);
			}
		}
		if (datas.length == 0) {
			layer.alert("所选数据已提交", {
				icon: 2,
			});
			return;
		}

		var tName = "";
		var typeName = "";
		if (tableName === "design_list_table") {
			tName = "design_data_list";
			typeName = "设计";
		} else if (tableName === "craft_list_table") {
			tName = "craft_data_list";
			typeName = "工艺";
		} else if (tableName === "processcontrol_list_table") {
			tName = "process_control_list";
			typeName = "过程控制";
		} else if (tableName === "quanlitycontrol_list_table") {
			tName = "quality_control_list";
			typeName = "质量综合";
		}
		var datapkg = $("#datapkgref").datagrid("getSelections")[0];

		var param = {};
		param.ids = "";
		for (var i = 0; i < datas.length; i++) {
			param.ids += "," + datas[i].ID;
		}
		if (param.ids !== "") {
			param.ids = param.ids.substring(1);
		}
		param.tableName = tName;
		//如果配置已经存在则提交到后台进行数据采集
		var cb_success = function(data) {
			if (data.success === false) {
				layer.alert(data.message, {
					icon: 2,
				});
				return;
			}
			layer.closeAll();
			layer.msg("提交成功");
			logRecord("保存提交", "策划构建-保存提交数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(IDs：" + param.ids + "、类型：" + typeName + ")", 1);
			reloadTable(tableName);
		};
		var cb_error = function() {
			logRecord("保存提交", "策划构建-保存提交数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(IDs：" + param.ids + "、类型：" + typeName + ")", 0);
			layer.msg("提交失败");
		};
		twxAjax("publishMissionThing", "UpdateListStatus", param, true, cb_success, cb_error);
	});
};

var initBtnSave1 = function(tableName, layui) {
	$("#" + tableName + "_save").bind("click", function() {
		var layer = layui.layer;
		if (!getSelectedNode()) {
			return;
		}
		var sels = $("#" + tableName).datagrid("getData");
		if (sels.rows.length == 0) {
			layer.alert("当前类别下没有项目清单可以采集...", {
				icon: 2,
			});
			return;
		}
		//对提交的数据进行校验，校验所有自动采集的数据是否在配置的接口中
		var datas = [];
		var autoCollects = []; //存放自动采集的配置
		for (var i = 0; i < sels.rows.length; i++) {
			var row = sels.rows[i];
			if (row.COLLECTSTATUS !== "1") {
				datas.push(row);
			}
			if (row.GATHERING_METHOD == "自动采集") {
				autoCollects.push(row);
			}
		}
		if (datas.length == 0) {
			layer.alert("没有可以提交的数据...", {
				icon: 2,
			});
			return;
		}

		var tName = "";
		var typeName = "";
		if (tableName === "design_list_table") {
			tName = "design_data_list";
			typeName = "设计";
		} else if (tableName === "craft_list_table") {
			tName = "craft_data_list";
			typeName = "工艺";
		} else if (tableName === "processcontrol_list_table") {
			tName = "process_control_list";
			typeName = "过程控制";
		} else if (tableName === "quanlitycontrol_list_table") {
			tName = "quality_control_list";
			typeName = "质量综合";
		}
		var datapkg = $("#datapkgref").datagrid("getSelections")[0];
		//调用Ajax进行判断配置的接口是否存在
		twxAjax(
			"publishMissionThing",
			"CheckAutoCollectConfig", {
				paramjson: autoCollects,
				typeName: typeName,
			},
			false,
			function(data) {
				if (data.success === false) {
					layer.alert(data.message, {
						icon: 2,
					});
					return;
				}

				var param = {};
				param.ids = "";
				for (var i = 0; i < datas.length; i++) {
					param.ids += "," + datas[i].ID;
				}
				if (param.ids !== "") {
					param.ids = param.ids.substring(1);
				}
				param.tableName = tName;
				//如果配置已经存在则提交到后台进行数据采集
				var cb_success = function(data) {
					if (data.success === false) {
						layer.alert(data.message, {
							icon: 2,
						});
						return;
					}
					layer.closeAll();
					layer.msg("提交成功");
					logRecord("保存提交", "策划构建-保存提交数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(IDs：" + param.ids + "、类型：" + typeName + ")", 1);
					reloadTable(tableName);
				};
				var cb_error = function() {
					logRecord("保存提交", "策划构建-保存提交数据包(ID：" + datapkg.ID + "、名称：" + datapkg.NAME + "、编号：" + datapkg.CODE + ")下的清单(IDs：" + param.ids + "、类型：" + typeName + ")", 0);
					layer.msg("提交失败");
				};
				twxAjax("publishMissionThing", "UpdateListStatus", param, true, cb_success, cb_error);
			}
		);
	});
};

var initTableComp = function(layui) {
	var gridids = ["design_list_table", "craft_list_table", "processcontrol_list_table", "quanlitycontrol_list_table"];
	for (var i = 0; i < gridids.length; i++) {
		var tableName = gridids[i];
		loadTableData(tableName);

		initBtnSave(tableName, layui);
	}
};

//加载树类型下拉框
function loadTreeTypeCombobox() {
	$("#tree_type").combobox({
		valueField: "type",
		textField: "name",
		panelHeight: "auto",
		data: [{
				type: "1",
				name: "过程结构树",
			},
			{
				type: "2",
				name: "产品结构树",
			}
		],
		editable: false,
		onSelect: function(record) {
			if (record.type == "1") {
				$("#bomTree").hide();
				$("#dpTree").show();
			} else if (record.type == "2") {
				$("#dpTree").hide();
				$("#bomTree").show();
			}
		},
		onLoadSuccess: function() {
			$("#tree_type").combobox("select", "1");
		},
	});

}

$(document).ready(function() {
	loadTreeTypeCombobox();
	layui
		.config({
			base: "../../plugins/layui/define/",
		})
		.extend({
			insertSelect: "InsertSelect",
			multiSelect: 'multiSelect'
		})
		.use(["layer", "form"], function() {
			var form = layui.form;
			initTableComp(layui);

			form.verify({
				sourcesystem: function(value) {
					var cm = $("#collectmethod").val();
					if (cm === "自动采集") {
						if (value === "") {
							return "自动采集时必须选择来源系统";
						}
					}
				},
			});

			//绑定事件
			$("#root_layout_tabs").tabs({
				onSelect: function(title, index) {
					var tab = $("#root_layout_tabs").tabs("getTab", index);
					var tabName = tab.panel("options").name;
					reloadTable(tabName);
				},
			});
		});
});
