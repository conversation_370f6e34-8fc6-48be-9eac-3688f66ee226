var contextMenuWidth = 115;

$(function() {
	initData();
	loadTree();
})

//加载树结构
function loadTree() {
	var cb_success = function(data) {
		var datas = data.rows;
		if (datas.length > 0) {
			datas = dealDataIcons(datas);
			datas = dealDataNodeName(datas);
			treeSetting.callback.onClick = function(event, treeId, treeNode) {

				if (reloadRefDPTable && typeof reloadRefDPTable == 'function') {
					reloadRefDPTable(treeNode.TREEID);
				}

				// twxAjax('Thing.Fn.DataPackage', 'QueryDataPkg', {
				// 	refTreeId: treeNode.TREEID
				// }, true, function(data) {
				// 	reloadRefDPTable(treeNode.TREEID);
				// });
				// // alert(treeNode.TREEID);
				// var selectedTab = $('#root_layout_tabs').tabs('getSelected');
				// var tabId = selectedTab.panel('options').name;
				// reloadTable(tabId);
			};
			treeSetting.callback.onExpand = function(event, treeId, treeNode) {
				loadTreeMenu();
			};
			ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
			var nodes = ztreeObj.getNodes();
			for (var i = 0; i < nodes.length; i++) { //设置节点展开
				ztreeObj.expandNode(nodes[i], true, false, true);
			}
			loadTreeMenu();
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax('Thing.Fn.DataPackage', 'QueryDataPackageJSON', '', true, cb_success);
}

function initData() {
	twxAjax('Thing.Fn.DataPackage', 'InitData', '', true);
}
//刷新树结构
function refreshTree(data) {
	loadTree();
}

//加载树节点右键菜单
function loadTreeMenu() {
	$("#dpTree a").each(function(i, n) {
		var menu = [];
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		menu = getNodeMenu(node);
		$(n).contextMenu({
			width: contextMenuWidth,
			menu: menu,
			target: function(ele) {
				var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
				ztreeObj.selectNode(node, false, true);
			}
		});
	});
}
//获取节点右键菜单数组
function getNodeMenu(treeNode) {
	var imgSuffix = '../dataTree/';
	var level = treeNode.level;
	var menu = [];
	var addNodeMenu = {
		text: "添加节点",
		icon: imgSuffix + 'images/add.png',
		callback: function() {
			layui.use(['layer'], function() {
				var layer = layui.layer;
				layer.prompt({
					title: '请输入新加的节点名称'
				}, function(nodeName, index1, elem) {
					if (nodeName != null && nodeName != "") {
						var parentId = treeNode.TREEID;
						var nodeLevel = treeNode.NODELEVEL + 1;
						twxAjax('Thing.Fn.DataPackage', 'AddNode', {
							parentId: parentId,
							nodeName: nodeName,
							nodeLevel: nodeLevel
						}, true, function(data) {
							layer.closeAll();
							layer.msg('添加成功！')
							loadTree();
						});

					} else {
						layer.msg('请输入节点名称！')
					}
				});
			});
		}
	};
	var creatDataPackageMenu = {
		text: "创建数据包",
		icon: imgSuffix + 'images/add.png',
		callback: function() {
			twxAjax('Thing.Fn.DataPackage', 'QueryDataPkg', {
				refTreeId: treeNode.TREEID
			}, true, function(data) {
				layui.use(['layer', 'form'], function() {
					var layer = layui.layer,
						form = layui.form;
					if (data.rows.length == 0) {
						twxAjax('Thing.Fn.DataPackage', 'getDataPkgNumber', {
							treeId: treeNode.TREEID
						}, true, function(data) {
							var name = getNodeName(treeNode.NODENAME) + "数据包";
							var code = data.rows[0].result;
							var addTpl =
								'<form class="layui-form" action="" lay-filter="component-create-datapkg">\
									<input type="hidden" name="refTreeId" value="">\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">数据包名称:</label>\
										<div class="layui-input-block">\
											<input type="text" name="name" value="' +
								name +
								'" lay-verify="required" required autocomplete="off" placeholder="请输入数据包名称" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">数据包编号:</label>\
										<div class="layui-input-block">\
											<input type="text" readOnly = "readOnly" value="' +
								code +
								'"  name="code" autocomplete="off" placeholder="请输入数据包编号" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">备注:</label>\
										<div class="layui-input-block">\
											<textarea placeholder="请输入备注" name="remark" style="resize:none" class="layui-textarea"></textarea>\
										</div>\
									</div>\
									<div class="layui-form-item" style="display:none;">\
										<div class="layui-input-block">\
											<div class="layui-footer">\
												<button class="layui-btn" id="createDatapkgSunmit" lay-submit="" lay-filter="component-form-create-datapkg">确认</button>\
												<button type="reset" id="createDatapkgReset" class="layui-btn layui-btn-primary">重置</button>\
											</div>\
										</div>\
									</div>\
								</form>';
							layer.open({
								title: '创建数据包',
								type: 1,
								fixed: false,
								maxmin: false,
								anim: false,
								openDuration: 200,
								isOutAnim: false,
								closeDuration: 200,
								shadeClose: false,
								resize: false, //不允许拉伸
								area: ['400px', '340px'],
								content: '<div id="creatDataPackage" style="padding-top: 15px;padding-right: 15px;"></div>',
								btn: ['新增', '重置', '关闭'],
								yes: function() {
									$('#createDatapkgSunmit').click();
								},
								btn2: function() {
									$('#createDatapkgReset').click();
									return false;
								},
								btn3: function() {
									return true;
								},
								success: function() {
									$("#creatDataPackage").append(addTpl);
								}
							});
							form.render(null, 'component-create-datapkg');
							form.on('submit(component-form-create-datapkg)', function(data) {
								data.field.refTreeId = treeNode.TREEID;
								data.field.creator = sessionStorage.getItem('fullname');
								twxAjax('Thing.Fn.DataPackage', 'CreateDataPkg', data.field, true, function(data) {
									layer.closeAll();
									layer.msg('添加成功!');

									//添加成功后重新加载相关的数据包表格
									if (reloadRefDPTable && typeof reloadRefDPTable == 'function') {
										reloadRefDPTable(treeNode.TREEID);
									}
								}, function(data) {
									layer.msg('添加失败，请联系管理员！');
								});
								return false;
							});
						});
					} else {
						layer.msg('该节点下已经添加过数据包！');
					}
				});
			});
		}
	};
	var editNodeMenu = {
		text: "编辑节点",
		icon: imgSuffix + 'images/edit.png',
		callback: function() {
			var nodeName = getNodeName(treeNode.NODENAME);
			var nodeNum = getNodeNum(treeNode.NODENAME);
			var code = treeNode.CODE == undefined ? '' : treeNode.CODE;

			layui.use(['layer', 'form'], function() {
				var layer = layui.layer,
					form = layui.form;
				var addTpl =
					'<form class="layui-form" action="" lay-filter="editNodeForm">\
								<input type="hidden" name="refTreeId" value="">\
								<div class="layui-form-item">\
									<label class="fieldlabel layui-form-label">节点名称:</label>\
									<div class="layui-input-block">\
										<input type="text" name="newName" value="' +
					nodeName +
					'" lay-verify="required" required autocomplete="off" placeholder="请输入节点名称" class="layui-input">\
									</div>\
								</div>\
								<div class="layui-form-item">\
									<label class="fieldlabel layui-form-label">code:</label>\
									<div class="layui-input-block">\
										<input type="text" value="' +
					code +
					'"  name="code" autocomplete="off" placeholder="请输入code" class="layui-input">\
									</div>\
								</div>\
								<div class="layui-form-item" style="display:none;">\
									<div class="layui-input-block">\
										<div class="layui-footer">\
											<button class="layui-btn" id="editNodeSubmit" lay-submit="" lay-filter="editNodeSubmit">确认</button>\
											<button type="reset" id="editNodeReset" class="layui-btn layui-btn-primary">重置</button>\
										</div>\
									</div>\
								</div>\
							</form>';
				layer.open({
					title: '编辑节点',
					type: 1,
					fixed: false,
					maxmin: false,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					resize: false, //不允许拉伸
					area: ['400px', '240px'],
					content: '<div id="editNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ['确认', '重置', '关闭'],
					yes: function() {
						$('#editNodeSubmit').click();
					},
					btn2: function() {
						$('#editNodeReset').click();
						return false;
					},
					btn3: function() {
						return true;
					},
					success: function() {
						$("#editNodeContent").append(addTpl);
					}
				});
				form.render(null, 'editNodeForm');
				form.on('submit(editNodeSubmit)', function(data) {
					data.field.treeId = treeNode.TREEID;
					var newName = nodeNum + data.field.newName;
					var newCode = data.field.code;
					twxAjax('Thing.Fn.DataPackage', 'updateNodeName', data.field, true, function(data) {
						layer.closeAll();
						layer.msg('编辑成功!');
						treeNode.NODENAME = newName;
						treeNode.CODE = newCode;
						ztreeObj.updateNode(treeNode);
					});
					return false;
				});
			});
		}
	};
	var deleteNodeMenu = {
		text: "删除节点",
		icon: imgSuffix + 'images/remove.png',
		callback: function() {
			layui.use(['layer'], function() {
				var layer = layui.layer;
				layer.confirm("确认删除 节点 -- " + treeNode.NODENAME + " 吗？", {
					icon: 3,
					title: '提示'
				}, function(index) {
					twxAjax('Thing.Fn.DataPackage', 'QueryDataPkg', {
						refTreeId: treeNode.TREEID
					}, true, function(data) {
						if (data.rows.length == 0) {
							ztreeObj.removeNode(treeNode, true);
						} else {
							layer.msg('对不起，有数据包文件，不能删除！');
						}
					});
				});
			});
		}
	};
	var copyNodeMenu = {
		text: "复制节点",
		icon: imgSuffix + 'images/copy.png',
		callback: function() {
			layui.use(['layer'], function() {
				var layer = layui.layer;
				layer.confirm("确认复制 节点 -- " + treeNode.NODENAME + " 吗？", {
					icon: 3,
					title: '提示'
				}, function(index) {
					layer.prompt({
						title: '请输入复制的型号名称'
					}, function(nodeName, index1, elem) {
						if (nodeName != null && nodeName != "") {
							twxAjax('Thing.Fn.DataPackage', 'copyNode', {
								treeId: treeNode.TREEID,
								nodeName: nodeName
							}, true, function(data) {
								if (data.rows[0].result == 99999) {
									layer.msg('该型号已存在，请重新输入！');
								} else {
									loadTree();
									layer.msg('复制成功!');
									layer.close(index1);
								}
							});
						} else {
							layer.msg('请输入型号名称！')
						}
					});
				});
			});
		}
	};
	if (treeNode.NODETYPE == 'leaf' || ((treeNode.NODETYPE == 'dir') && (!treeNode.isParent))) {
		menu.push(creatDataPackageMenu);
	} else {
		menu.push(addNodeMenu);
	}
	if (level != 0) {//根节点不能修改删除复制
		menu.push(editNodeMenu);
		if (treeNode.NODETYPE == 'leaf') {
			menu.push(deleteNodeMenu);
		} else if (treeNode.NODETYPE == 'product') {
			menu.push(copyNodeMenu);
		}
	}
	return menu;
}
