/**
 * <AUTHOR>
 * @datetime 2025年4月2日10:53:23
 * @function	treeContextMenu
 * @description	树右键菜单
 */


//获取节点右键菜单数组
function getNodeMenu(treeNode) {
    var imgSuffix = '../dataTree/';
    var type = treeNode.TYPE;
    var menu = [];

    var initReportNode = {
        text: "初始化报告",
        icon: imgSuffix + 'images/init.png',
        callback: function () {
            var cb_success = function (res) {
                if (res.success) {
                    layer.msg(res.msg);
                    ztreeObj.reAsyncChildNodes(treeNode.getParentNode(), 'refresh', false, function () {
                        loadTreeMenu();
                    });
                } else {
                    layer.alert(res.msg);
                }
            };
            var cb_error = function () {
                layer.closeAll();
            };
            twxAjax(THING, 'InitReport', {
                treeId: treeNode.TREE_ID,
                name: treeNode.NAME,
                creator: sessionStorage.getItem('username')
            }, true, cb_success, cb_error);
        }
    };

    //刷新子节点
    var refreshChildNode = {
        text: "刷新子节点",
        icon: imgSuffix + 'images/refresh.png',
        callback: function () {
            reloadTree(treeNode.ID);
        }
    };

    //同步B表数据
    var syncBTableNode = {
        text: "同步B表数据",
        icon: imgSuffix + 'images/sync.png',
        callback: function () {
            syncBTable(treeNode);
        }
    };


    //结构件批量标记
    var batchMarkNode = {
        text: "结构件批量标记",
        icon: imgSuffix + 'images/batch-mark.png',
        callback: function () {
            batchMarkStructureComponents(treeNode);
        }
    };

    //结构件标记
    var markNode = {
        text: "结构件标记",
        icon: imgSuffix + 'images/mark.png',
        callback: function () {
            markStructureComponent(treeNode);
        }
    };

    //批量开合格证
    var batchOpenCertificateNode = {
        text: "批量开合格证",
        icon: imgSuffix + 'images/certificate.png',
        callback: function () {
            createCertificateDialog({
                title: '批量开合格证',
                logOperation: '批量开合格证',
                requestUrl: '/report/batch/create/certificate',
                treeNode: treeNode
            });
        }
    };

    //开合格证
    var openCertificateNode = {
        text: "开合格证",
        icon: imgSuffix + 'images/certificate.png',
        callback: function () {
            createCertificateDialog({
                title: '开合格证',
                logOperation: '开合格证',
                requestUrl: '/report/create/certificate',
                treeNode: treeNode
            });
        }
    };

    //确认表锁定统计
    var tableLockStatisticsNode = {
        text: "确认表锁定统计",
        icon: imgSuffix + 'images/statistics.png',
        callback: function () {
            showLockStatistics(treeNode);
        }
    };

    //确认表签署统计
    var tableSignStatisticsNode = {
        text: "确认表签署统计",
        icon: imgSuffix + 'images/statistics.png',
        callback: function () {
            showSignStatistics(treeNode);
        }
    };

    var addReportNode = {
        text: "添加节点",
        icon: imgSuffix + 'images/add1.png',
        callback: function () {
            HotUtil.addTableNode(treeNode, 'report');
        }
    };

    var importReportNode = {
        text: "导入A表",
        icon: imgSuffix + 'images/excelupload1.png',
        callback: function () {

        }
    };

    var syncTestNode = {
        text: "同步电测试系统",
        icon: imgSuffix + 'images/update.png',
        callback: function () {
            syncElectricTest(treeNode);
        }
    };

    var syncQualityTestNode = {
        text: "同步质测数据",
        icon: imgSuffix + 'images/update.png',
        callback: function () {
            syncQualityTest();
        }
    };


    var addTableNode = {
        text: "添加节点",
        icon: imgSuffix + 'images/add1.png',
        callback: function () {
            var type = "";
            if (treeNode.TYPE == 'leaf') {
                type = "report";
            } else if (treeNode.TYPE == 'report') {
                type = "table";
            } else if (treeNode.TYPE == 'table') {
                type = "table_1";
            } else if (treeNode.TYPE == 'table_1') {
                type = "table_2";
            } else if (treeNode.TYPE == 'table_2') {
                type = "table_3";
            }

            HotUtil.addTableNode(treeNode, type);
        }
    };

    var importTableNode = {
        text: "导入节点",
        icon: imgSuffix + 'images/excelupload1.png',
        callback: function () {
            var type = "";
            if (treeNode.TYPE == 'leaf') {
                type = "report";
            } else if (treeNode.TYPE == 'report') {
                type = "table";
            } else if (treeNode.TYPE == 'table') {
                type = "table_1";
            } else if (treeNode.TYPE == 'table_1') {
                type = "table_2";
            } else if (treeNode.TYPE == 'table_2') {
                type = "table_3";
            }

            HotUtil.importTableNode(treeNode, type);
        }
    };

    var batchImportTableNode = {
        text: "批量导入节点",
        icon: imgSuffix + 'images/excelupload1.png',
        callback: function () {
            var type = "";
            if (treeNode.TYPE == 'leaf') {
                type = "report";
            } else if (treeNode.TYPE == 'report') {
                type = "table";
            } else if (treeNode.TYPE == 'table') {
                type = "table_1";
            } else if (treeNode.TYPE == 'table_1') {
                type = "table_2";
            } else if (treeNode.TYPE == 'table_2') {
                type = "table_3";
            }
            HotUtil.batchImportExcel(treeNode, type);
        }
    };

    var editNodeMenu = {
        text: "编辑节点",
        icon: imgSuffix + 'images/edit1.png',
        callback: function () {
            HotUtil.editTableNode(treeNode, funcIdent);
        }
    };
    var copyNodeMenu = {
        text: "复制节点",
        icon: imgSuffix + 'images/copy1.png',
        callback: function () {
            HotUtil.addTableNode(treeNode, treeNode.TYPE, true);
        }
    };

    var moveNodeMenu = {
        text: "移动节点",
        icon: imgSuffix + 'images/transfer.png',
        callback: function () {
            HotUtil.moveNode('Thing.Fn.ProcessTree', treeNode);
        }
    };

    var deleteNodeMenu = {
        text: "删除节点",
        icon: imgSuffix + 'images/remove1.png',
        callback: function () {
            HotUtil.deleteTableNode(treeNode);
        }
    };

    var pdfNodeMenu = {
        text: "导出PDF",
        icon: imgSuffix + 'images/pdf1.png',
        callback: function () {
            var log = {};
            log.operation = "结构树导出PDF";
            log.tablePid = treeNode.PID;
            log.tableId = treeNode.ID;
            log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出PDF";

            // 使用PdfExportDialog模块显示导出设置弹窗
            PdfExportDialog.showDialog(function (options) {
                log.reqResult = 1;
                addConfirmLog(log);
                twxAjax(THING, 'ReqGenerateFile', {
                    tableId: treeNode.ID,
                    tablePId: treeNode.PID,
                    exportType: 1,
                    creator: sessionStorage.getItem("username"),
                    // 传递PDF导出选项
                    pageSize: options.pageSize,
                    pageOrientation: options.pageOrientation
                }, true, function (res) { }, function (xhr, textStatus, errorThrown) { });
                layer.alert("文件正在生成中，请稍后在下载列表中查看下载！");
            });
        }
    };

    var pdfNodeMenu1 = {
        text: "导出PDF",
        icon: imgSuffix + 'images/pdf1.png',
        callback: function () {
            var log = {};
            log.operation = "结构树导出PDF";
            log.tablePid = treeNode.PID;
            log.tableId = treeNode.ID;
            log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出PDF";

            // 使用PdfExportDialog模块显示导出设置弹窗
            PdfExportDialog.showDialog(function (options) {
                var loading;
                var url = fileHandlerUrl + "/report/export/more/pdf";
                $.fileDownload(url, {
                    httpMethod: 'POST',
                    data: {
                        "id": treeNode.ID,
                        "pid": treeNode.PID,
                        "name": treeNode.NAME,
                        // 传递PDF导出选项
                        "pageSize": options.pageSize,
                        "pageOrientation": options.pageOrientation
                    },
                    prepareCallback: function (url) {
                        loading = layer.msg("正在导出...", {
                            icon: 16,
                            shade: 0.3,
                            time: 0
                        });
                    },
                    abortCallback: function (url) {
                        layer.close(loading);
                        log.reqResult = 0;
                        layer.msg("导出异常！！");
                        addConfirmLog(log);
                    },
                    successCallback: function (url) {
                        log.reqResult = 1;
                        layer.close(loading);
                        addConfirmLog(log);
                    },
                    failCallback: function (html, url) {
                        log.reqResult = 0;
                        addConfirmLog(log);
                        layer.close(loading);
                        layer.alert(html, {
                            icon: 2
                        })
                    }
                });
            });
        }
    };
    var pdfZipNodeMenu = {
        text: "导出PDF压缩包",
        icon: imgSuffix + 'images/morepdf.png',
        callback: function () {
            var log = {};
            log.operation = "导出PDF压缩包";
            log.tablePid = treeNode.PID;
            log.tableId = treeNode.ID;
            log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出PDF压缩包";

            // 使用PdfExportDialog模块显示导出设置弹窗
            PdfExportDialog.showDialog(function (options) {
                var loading;
                var url = fileHandlerUrl + "/report/export/pdf/zip";
                $.fileDownload(url, {
                    httpMethod: 'POST',
                    data: {
                        "id": treeNode.ID,
                        "name": treeNode.NAME,
                        // 传递PDF导出选项
                        "pageSize": options.pageSize,
                        "pageOrientation": options.pageOrientation
                    },
                    prepareCallback: function (url) {
                        loading = layer.msg("正在导出...", {
                            icon: 16,
                            shade: 0.3,
                            time: 0
                        });
                    },
                    abortCallback: function (url) {
                        log.reqResult = 0;
                        addConfirmLog(log);
                        layer.close(loading);
                        layer.msg("导出异常！！");
                    },
                    successCallback: function (url) {
                        log.reqResult = 1;
                        addConfirmLog(log);
                        layer.close(loading);
                    },
                    failCallback: function (responseHtml, url, error) {
                        log.reqResult = 0;
                        addConfirmLog(log);
                        layer.close(loading);
                        layer.alert('导出失败！', {
                            icon: 2
                        })
                    }
                });
            });
        }
    };

    var excelNodeMenu = {
        text: "导出Excel",
        icon: imgSuffix + 'images/excel1.png',
        callback: function () {
            var log = {};
            log.operation = "结构树导出Excel";
            log.tablePid = treeNode.PID;
            log.tableId = treeNode.ID;
            log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出Excel";

            var loading;
            var url = fileHandlerUrl + "/report/export/more/excel";
            $.fileDownload(url, {
                httpMethod: 'POST',
                data: {
                    "id": treeNode.ID,
                    "pid": treeNode.PID
                },
                prepareCallback: function (url) {
                    loading = layer.msg("正在导出...", {
                        icon: 16,
                        shade: 0.3,
                        time: 0
                    });
                },
                abortCallback: function (url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出异常！！");
                },
                successCallback: function (url) {
                    log.reqResult = 1;
                    addConfirmLog(log);
                    layer.close(loading);
                },
                failCallback: function (html, url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出失败！！");
                }
            });
        }
    };

    var exportZipMenu = {
        text: "导出数据包",
        icon: imgSuffix + 'images/export-data.png',
        callback: function () {
            var log = {};
            log.operation = "导出数据包";
            log.tablePid = treeNode.PID;
            log.tableId = treeNode.ID;
            log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出数据包";
            var loading;
            var url = fileHandlerUrl + "/report/export/zip";
            $.fileDownload(url, {
                httpMethod: 'POST',
                data: {
                    "id": treeNode.ID,
                    "name": treeNode.NAME,
                    "thing": THING
                },
                prepareCallback: function (url) {
                    loading = layer.msg("正在导出...", {
                        icon: 16,
                        shade: 0.3,
                        time: 0
                    });
                },
                abortCallback: function (url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出异常！！");
                },
                successCallback: function (url) {
                    log.reqResult = 1;
                    addConfirmLog(log);
                    layer.close(loading);
                },
                failCallback: function (html, url) {
                    log.reqResult = 0;
                    addConfirmLog(log);
                    layer.close(loading);
                    layer.msg("导出失败！！");
                }
            });
        }
    };

    var importZipMenu = {
        text: "导入数据包",
        icon: imgSuffix + 'images/import-data.png',
        callback: function () {
            HotUtil.importBigZip(treeNode, fileHandlerUrl + "/report/import/big/zip");
        }
    };

    var viewTableId = {
        text: "查看tableId",
        icon: imgSuffix + 'images/id.png',
        callback: function () {
            layer.alert(treeNode.ID);
        }
    };

    // AIT映射同步相关菜单项
    var syncAitMappingDataMenu = {
        text: "同步映射数据",
        icon: imgSuffix + 'images/sync.png',
        callback: function () {
            syncAitMappingData(treeNode);
        }
    };

    var exportTableListMenu = {
        text: "导出确认表列表",
        icon: imgSuffix + 'images/excel1.png',
        callback: function () {
            exportConfirmTableList(treeNode);
        }
    };

    var updateBTableNumbersNode = {
        text: "更新B表序号",
        icon: imgSuffix + 'images/refresh.png',
        callback: function () {
            HotUtil.updateBTableNumbers(treeNode);
        }
    };

    var allfuns = sessionStorage.getItem('funcids');
    var funcArr = allfuns.split(',');
    menu.push(refreshChildNode);
    if (type == 'root') {
        menu.push(syncQualityTestNode);
    }

    if (type != 'root' && type != 'folder' && type != 'report' && type.indexOf('table') == -1) {
        menu.push(tableLockStatisticsNode);
        menu.push(tableSignStatisticsNode);
    }

    if (type == 'phase') {
        menu.push(syncTestNode);
        menu.push(syncAitMappingDataMenu);
    }

    if (type == 'leaf' || type == 'phase' || type == 'dir') {
        menu.push(exportTableListMenu);
    }

    if (treeNode.IS_PROCESS == "true") {
        if (treeNode.HAS_REPORT == "true") {
            menu.push(addTableNode);
            menu.push(importTableNode);
            menu.push(batchImportTableNode);
            menu.push(pdfNodeMenu);
            menu.push(pdfZipNodeMenu);
            menu.push(excelNodeMenu);
            if (contains(funcArr, 'func-' + funcIdent + '-export-zip-node')) {
                menu.push(exportZipMenu);
            }
        } else {
            menu.push(initReportNode);
        }
        if (contains(funcArr, 'func-' + funcIdent + '-import-zip-node')) {
            menu.push(importZipMenu);
        }
        var pName = treeNode.getParentNode().NAME;
        if (pName.indexOf("整星AIT") > -1 || pName.indexOf("整星ait") > -1) {
            menu.push(syncTestNode);
        }
    }

    if (type == "report" || type == 'table' || type == 'table_1' || type == 'table_2' || type == 'table_3') {
        if (type == "report") {
            menu.push(updateBTableNumbersNode);
            if (treeNode.getParentNode().NAME == '一般结构件') {
                if (treeNode.TABLE_STATUS == "edit") {
                    menu.push(syncBTableNode);
                }
                menu.push(batchMarkNode);
                menu.push(batchOpenCertificateNode);
            }
        }
        if (type == "table" && treeNode.getParentNode().getParentNode().NAME == '一般结构件') {
            menu.push(markNode);
            if (treeNode.IS_GENERAL_COMPONENT == 1) {
                menu.push(openCertificateNode);
            }
        }
        if (type !== 'table_3') {
            menu.push(addTableNode);
        }
        menu.push(importTableNode);
        menu.push(batchImportTableNode);
        menu.push(editNodeMenu);
        menu.push(copyNodeMenu);
        menu.push(moveNodeMenu);
        menu.push(deleteNodeMenu);
        if (type !== 'table_3') {
            menu.push(pdfNodeMenu);
            menu.push(pdfZipNodeMenu);
            menu.push(excelNodeMenu);
        }

        if (contains(funcArr, 'func-' + funcIdent + '-export-zip-node')) {
            menu.push(exportZipMenu);
        }
        if (contains(funcArr, 'func-' + funcIdent + '-import-zip-node')) {
            menu.push(importZipMenu);
        }
        menu.push(viewTableId);
    }
    return menu;
}