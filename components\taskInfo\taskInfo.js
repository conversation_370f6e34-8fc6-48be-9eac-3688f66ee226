var resData = {
	"msg": "查询任务详细信息成功",
	"data": {
		"CALLBACK_FLAG": " ",
		"SYNC_TIME": "2023-11-02 15:27:05",
		"TASK_NAME": "数据采集任务执行",
		"TASK_STATUS": "执行中",
		"TASK_USERNAME": "杨柳-yang<PERSON><EMAIL>",
		"PROC_INST_USERNAME": "杨柳-yang<PERSON><EMAIL>",
		"PROC_INST_START_TIME": "2023-09-05 09:15:45.0",
		"PROC_INST_ID": "370001",
		"DELEGATE_FLAG": " ",
		"collectionDataInfo": {
			"MODEL_FIELD": "运载火箭",
			"DATA_PACKET_NAME": "单机数据包01",
			"DATA_PACKET_NODE_ID": "2c9170818a62de62018a62e7a462000c",
			"MODEL_NAME": "UAT测试",
			"FEN_XI_TONG": " ",
			"DATA_PACKET_NODE_FULL_PATH": "UAT测试/M/M-01/CPMC-XT-01/FXT-MC-01/CPMC-DJ-01",
			"dataPacketListInfo": [{
					"DISTRIBUTE_TASK_TIME": "2023-09-05 09:15:45",
					"LIST_PROJECT_CATEGORY": "设计开发",
					"LIST_TEMPLATE_TYPE": "tableTemplate",
					"LIST_STATUS": "已下发",
					"LIST_COLLECT_USER": "杨柳<EMAIL>",
					"LIST_COLLECT_NODE_NAME": "设计评审,产品投产前,测试试验前检查,产品最终检验,产品质量评审,产品交付前,产品交付后以及发射场",
					"LIST_NAME": "Ⅰ、Ⅱ类更改单",
					"LIST_COLLECT_NODE": "designReview,beforeTheProduction,testPreTest,finalProductInspection,productQualityReview,beforeProductDelivery,afterProductDeliveryLaunchSite",
					"LIST_SUBMIT_OR_DELIVER": "提交",
					"LIST_COLLECT_UNIT": "149厂",
					"REJECT_REASON": " ",
					"REQUIRED_DELIVER_TIME": " ",
					"LIST_ID": "2c9170818a62de62018a62e86cf10011",
					"ELE_ENTITY_DOC_INFO": " ",
					"TABLE_TITLE_INFO": "[{\"internalName\":\"F797137591671612058724\",\"displayName\":\"TEST1\",\"displayOrder\":\"1\"},{\"internalName\":\"F797137601671612058724\",\"displayName\":\"TEST2\",\"displayOrder\":\"2\"},{\"internalName\":\"F797137611671612058724\",\"displayName\":\"TEST3\",\"displayOrder\":\"3\"}]",
					"TASK_ID": "370031",
					"LIST_TEMPLATE_ID": "2c99e0818808701101880a07ca3a000e",
					"LIST_VERSION": " "
				},
				{
					"DISTRIBUTE_TASK_TIME": "2023-09-05 09:15:45",
					"LIST_PROJECT_CATEGORY": "设计开发",
					"LIST_TEMPLATE_TYPE": "eleEntityDoc",
					"LIST_STATUS": "已下发",
					"LIST_COLLECT_USER": "杨柳<EMAIL>",
					"LIST_COLLECT_NODE_NAME": "设计评审,产品投产前,测试试验前检查,产品最终检验,产品质量评审,产品交付前,产品交付后以及发射场",
					"LIST_NAME": "产品配套表-04",
					"LIST_COLLECT_NODE": "designReview,beforeTheProduction,testPreTest,finalProductInspection,productQualityReview,beforeProductDelivery,afterProductDeliveryLaunchSite",
					"LIST_SUBMIT_OR_DELIVER": "提交",
					"LIST_COLLECT_UNIT": "149厂",
					"REJECT_REASON": " ",
					"REQUIRED_DELIVER_TIME": " ",
					"LIST_ID": "2c9170818a62de62018a62e8cf350012",
					"ELE_ENTITY_DOC_INFO": "[{\"internalName\":\"DOCUMENT_CODE\",\"displayName\":\"文档编号\",\"displayOrder\":\"1\"},{\"internalName\":\"DOCUMENT_NAME\",\"displayName\":\"文档名称\",\"displayOrder\":\"2\"},{\"internalName\":\"FILENAME\",\"displayName\":\"文件名称\",\"displayOrder\":\"3\"},{\"internalName\":\"IMAGE\",\"displayName\":\"图片预览\",\"displayOrder\":\"4\"}]",
					"TABLE_TITLE_INFO": " ",
					"TASK_ID": "370031",
					"LIST_TEMPLATE_ID": "2c939081872b5c4b01872bd3a3240063",
					"LIST_VERSION": " "
				}
			],
			"NODE_CREATE_USER": "杨柳<EMAIL>",
			"DATA_PACKET_SECURITY_LEVEL": "公开",
			"NODE_OWNER": "杨柳<EMAIL>",
			"DAN_JI": " ",
			"MODEL_ID": "4028280184c2f2df0184e7e256f20058",
			"GRASPING_UNIT": "805",
			"MODEL_TYPE": "型号产品",
			"DATA_PACKET_NUMBER": "SYS000000381",
			"PRODUCT_STATUS": " ",
			"DATA_PACKET_PLAN_USER": "杨柳<EMAIL>",
			"DATA_PACKET_ID": "2c9170818a62de62018a62e7e73b000e",
			"DATA_PACKET_VERSION": "1",
			"XI_TONG": " ",
			"BELONG_SERIES": "YZ_系列",
			"TASK_ID": "370031",
			"MODEL_CODE": "UAT_Test"
		},
		"PROC_INST_NAME": "数据包采集流程",
		"TASK_TYPE": "采集任务",
		"TASK_START_TIME": "2023-09-05 09:15:45.51",
		"CALLBACK_REASON": " ",
		"TASK_ID": "370031"
	},
	"success": true
};

var tree, table, upload, form, taskId;
var THING = 'Thing.Fn.Interface';
layui.use(function() {
	tree = layui.tree;
	table = layui.table;
	upload = layui.upload;
	form = layui.form;
	// renderTree(resData.data);
	taskId = getQueryString("taskId");
	twxAjax(THING, "QueryTaskInfo", {
		taskId: taskId
	}, true, function(res) {
		if (res.success) {
			renderTree(res.data);
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});
});