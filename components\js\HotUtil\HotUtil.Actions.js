/**
 * HotUtil.Actions.js - Handsontable工具类 (表格/行操作模块)
 *
 * 包含对表格或行进行高级状态更改的功能。
 */

/**
 * 锁定行（入口）
 * @param {jQuery} tr
 * @param {Object} treeNode
 */
HotUtil.lockRow = function (tr, treeNode) {
    var contextMenuTd = void 0;
    tr.find("td:first").contextMenu({
        width: 110,
        menu: [{
            text: "锁定该行",
            icon: '../dataTree/images/lock.png',
            callback: function () {
                HotUtil.openEdit(treeNode, 0, function () {
                    HotUtil.updateLockRow(contextMenuTd, treeNode, 'lock');
                });
            }
        }],
        target: function (ele) {
            contextMenuTd = ele;
        }
    });
};

/**
 * 上传锁定单元格的信息
 * @param {Array} tds
 * @param {Object} treeNode
 * @param {string} type 'lock' or 'unlock'
 * @param {Function} successFn
 */
HotUtil.uploadLockTds = function (tds, treeNode, type, successFn) {
    var log = {};
    if (type == "lock") {
        log.operation = "锁定";
    } else {
        log.operation = "解锁";
    }
    log.operation = log.operation + "行";
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;
    var row = tds[0].row + 1;
    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中" + log.operation + "了第" + row + "行";
    var cb_success = function (res) {
        if (res.success) {
            log.reqResult = 1;
            successFn(res.msg);
        } else {
            log.reqResult = 0;
            layer.alert(res.msg, {
                icon: 2
            });
        }
        addConfirmLog(log);
    }
    var cb_error = function (err) {
        layer.alert('请求失败', {
            icon: 2
        });
    }
    twxAjax(THING, "UpdateLockRow", {
        id: treeNode.ID,
        lockRow: JSON.stringify(tds),
        userName: sessionStorage.getItem('username'),
        type: type
    }, true, cb_success, cb_error);
};

/**
 * 更新锁定行的信息
 * @param {jQuery} contextMenuTd
 * @param {Object} treeNode
 * @param {string} type 'lock' or 'unlock'
 */
HotUtil.updateLockRow = function (contextMenuTd, treeNode, type) {
    // 在锁定行操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, type == "lock" ? "锁定行" : "解锁行", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '设置失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        var msg = "";
        if (type == 'lock') {
            msg = "锁定之后不可编辑，是否继续？";
        } else {
            msg = "解除锁定之后会清除签名图片，并且不可恢复，是否继续？";
        }
        layer.confirm(msg, {
            icon: 3,
            title: '提示'
        }, function (index) {
            var tds = [];
            contextMenuTd.parent().find("td").each(function (i, n) {
                tds.push({
                    row: parseInt($(n).attr("row")),
                    col: parseInt($(n).attr("col"))
                });
            });

            HotUtil.uploadLockTds(tds, treeNode, type, function (msg) {
                layer.msg(msg);
                reloadTable(treeNode);
            });
        });
    });
};

/**
 * 清除整个表格的锁定和签名
 * @param {Object} treeNode
 */
HotUtil.clearLock = function (treeNode) {
    // 在清除签名操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "清除签名", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '清除签名失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        if (treeNode.TABLE_STATUS != 'sign') {
            layer.alert("表格还未确认，无需清除签名！");
            return false;
        }
        layer.confirm("清除签名之后不可恢复，您确定吗？", {
            icon: 3,
            title: '提示'
        }, function (index) {
            var cb_success = function (res) {
                var log = {};
                log.operation = "清除签名";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;
                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上清除签名";

                if (res.success) {
                    log.reqResult = 1;
                    //更新node节点的数据
                    treeNode.TABLE_STATUS = 'edit';
                    ztreeObj.updateNode(treeNode);
                    reloadTable(treeNode);
                    layer.closeAll();
                    layer.msg(res.msg);
                } else {
                    log.reqResult = 0;
                    layer.alert(res.msg, {
                        icon: 2
                    });
                }
                addConfirmLog(log);
            };
            var cb_error = function (xhr) {
                layer.alert('清除签名失败!', {
                    icon: 2
                });
            };
            twxAjax(THING, "UpdateTableStatus", {
                id: treeNode.ID,
                status: 'edit'
            }, false, cb_success, cb_error);
        });
    });
};

/**
 * 锁定整个表格
 * @param {Object} treeNode
 */
HotUtil.lockTable = function (treeNode) {
    // 在锁定表格操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "锁定表格", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '锁定失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        if (treeNode.HTML_DATA) {
            if (treeNode.TABLE_STATUS == 'sign') {
                layer.alert("表格已经锁定！");
                return false;
            }
            layer.confirm("请确认表格数据无误，锁定之后不可再次编辑表格！", {
                icon: 3,
                title: '提示'
            }, function (index) {
                var cb_success = function (res) {
                    var log = {};
                    log.operation = "锁定整表";
                    log.tablePid = treeNode.PID;
                    log.tableId = treeNode.ID;
                    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上锁定整表";

                    if (res.success) {
                        log.reqResult = 1;
                        //更新node节点的数据
                        treeNode.TABLE_STATUS = 'sign';
                        ztreeObj.updateNode(treeNode);
                        reloadTable(treeNode);
                        layer.closeAll();
                        layer.msg(res.msg);
                    } else {
                        log.reqResult = 0;
                        layer.alert(res.msg, {
                            icon: 2
                        });
                    }
                    addConfirmLog(log);
                };
                var cb_error = function (xhr) {
                    layer.alert('锁定失败!', {
                        icon: 2
                    });
                };
                twxAjax(THING, "UpdateTableStatus", {
                    id: treeNode.ID,
                    status: 'sign',
                    saveUser: sessionStorage.getItem("username")
                }, false, cb_success, cb_error);
            });
        } else {
            layer.alert("请先编辑表格");
        }
    });
};

/**
 * 更新表头行
 * @param {Object} treeNode
 */
HotUtil.updateHeaderRow = function (treeNode) {
    // 在设置表头行操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "设置表头行", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '设置失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        // 乐观锁检查通过，继续执行设置表头行逻辑
        HotUtil._executeUpdateHeaderRow(treeNode);
    });
};