/**
 * <AUTHOR>
 * @datetime 2025年4月2日10:56:04
 * @function	tableButtons
 * @description	表格按钮
 */

function initTbr(treeNode) {
    if (treeNode.ATTACHMENT) {
        $("#view-attachment").show();
    } else {
        $("#view-attachment").hide();
    }

    var showCalculateButton = isCalculateButtonVisible(treeNode);

    if (showCalculateButton) {
        $('#calculate-result').show();
    } else {
        $('#calculate-result').hide();
    }

    if (treeNode.TABLE_STATUS == 'edit' || treeNode.TABLE_STATUS == 'list') {
        $('#eidt-table').show();
        $('#import-excel').show();
        $('#clear-sign').hide();
        $("#dataConfig").show();
        if (treeNode.HTML_DATA) {
            $('#export-img').show();
            $('#export-pdf').show();
            $('#export-excel').show();
            $("#table-header").show();
            $('#confirm-table').show();
            $('#push-sign').show();
        } else {
            $('#export-img').hide();
            $('#confirm-table').hide();
            $("#table-header").hide();
            $('#export-pdf').hide();
            $('#export-excel').hide();
            $('#push-sign').hide();
        }
        if (treeNode.TABLE_STATUS == 'edit') {
            if (treeNode.TYPE.indexOf('table') > -1) {
                $('#import-pdf').show();
                if (HotUtil.isPdf(treeNode)) {
                    $('#import-excel').hide();
                    $('#eidt-table').hide();
                    $('#export-img').hide();
                    $('#confirm-table').hide();
                    $("#table-header").hide();
                    $('#export-pdf').show();
                    $('#push-sign').hide();
                    $('#export-excel').hide();
                }
            } else {
                $('#import-pdf').hide();
            }
        }
    } else if (treeNode.TABLE_STATUS == 'sign') {
        $('#export-img').show();
        $("#table-header").show();
        $("#dataConfig").hide();
        $('#eidt-table').hide();
        $('#import-excel').hide();
        $('#confirm-table').hide();
        $('#clear-sign').show();
        $('#export-pdf').show();
        $('#export-excel').show();
        $('#import-pdf').hide();
        $('#calculate-result').hide();
    }

    // 计算结果按钮点击事件
    $('#calculate-result').unbind("click").bind('click', function () {
        openCalculationDialog(treeNode);
    });

    //推送签署
	$('#push-sign').unbind("click").bind('click', function () {
		SignPushDialog.show(treeNode.ID, funcIdent);
	});

    //查看
    $('#view-attachment').unbind("click").bind('click', function () {
        openAttachmentDialog(treeNode);
    });

    //导入Excel
    $('#import-excel').unbind("click").bind('click', function () {
        var url = fileHandlerUrl + '/report/import/excel';
        HotUtil.openEdit(treeNode, 0, function () {
            HotUtil.importExcel(treeNode, url);
        });
    });

    $('#export-img').unbind("click").bind('click', function () {
        var url = fileHandlerUrl + "/online/export/img";
        HotUtil.exportImg(treeNode, url);
    });

    //导出excel
    $('#export-excel').unbind("click").bind('click', function () {
        var url = fileHandlerUrl + "/report/export/excel";
        HotUtil.exportExcel(treeNode, url);
    });

    //导出PDF
    $('#export-pdf').unbind("click").bind('click', function () {
        if (HotUtil.isPdf(treeNode)) {
            var fileName = treeNode.TABLE_NUM + '：' + treeNode.NAME;
            var url = fileHandlerUrl + "/file/download?filePath=" + treeNode.FILE_PATH + "&fileName=" +
                encodeURIComponent(fileName) +
                "." + treeNode.FILE_FORMAT;
            window.open(url);
        } else {
            // 使用PdfExportDialog模块显示导出设置弹窗
            PdfExportDialog.showDialog(function(options) {
                var url = fileHandlerUrl + "/report/export/pdf";

                // 导出PDF
                var loading;

                // 使用fileDownload处理文件下载
                $.fileDownload(url, {
                    httpMethod: 'POST',
                    data: {
                        "id": treeNode.ID,
                        // 传递PDF导出选项
                        "pageSize": options.pageSize,
                        "pageOrientation": options.pageOrientation
                    },
                    prepareCallback: function(url) {
                        loading = layer.msg("正在导出PDF，请稍候...", {
                            icon: 16,
                            shade: 0.3,
                            time: 0
                        });
                    },
                    successCallback: function(url) {
                        layer.close(loading);
                    },
                    failCallback: function(html, url) {
                        layer.close(loading);
                        layer.alert("导出失败，表格中可能存在多余的合并单元格，请处理之后重试。", {
                            icon: 2
                        });
                    }
                });
            });
        }
    });

    //导入PDF
    $('#import-pdf').unbind("click").bind('click', function () {
        var url = fileHandlerUrl + "/report/import/pdf";
        HotUtil.openEdit(treeNode, 0, function () {
            HotUtil.importPdf(treeNode, url);
        });
    });

    //确认表格之后可以签署
    $('#confirm-table').unbind("click").bind('click', function () {
        HotUtil.openEdit(treeNode, 0, function () {
            HotUtil.lockTable(treeNode);
        });
    });

    //清除表格签名
    $('#clear-sign').unbind("click").bind('click', function () {
        HotUtil.clearLock(treeNode);
    });

    //设置表格表头行
    $('#table-header').unbind("click").bind('click', function () {
        HotUtil.updateHeaderRow(treeNode);
    });
    //编辑表格
    $('#eidt-table').unbind("click").bind('click', function () {
        HotUtil.openEdit(treeNode, 1, function () {
            HotUtil.editTable(treeNode);
        });
    });
}