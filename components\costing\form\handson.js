/**
 * components/costing/form/handson.js - 成本表单管理
 *
 * 版本升级记录：
 * - 2025-07-11: 升级适配Handsontable 16.0.1版本
 *   * 增强错误处理机制，提高稳定性
 *   * 优化Handsontable配置，利用16.0.1性能改进
 *   * 改进上下文菜单定位
 *   * 增加参数验证和异常捕获
 *   * 保持ES5语法兼容性
 *
 * <AUTHOR>
 * @version 16.0.1-compatible
 */

/**
 * 加载表单
 * @param {Object} myForm
 */
function renderForm(myForm) {
	var fixedRowsTop = myForm.headerRow || 0;
	var initData = myForm.tableData;
	var merged = myForm.merged;
	var metas = myForm.meta;
	var colWidths = myForm.colWidths || [];
	var rowHeights = myForm.rowHeights || [];

	function myCellRenderer(instance, td, row, col, prop, value, cellProperties) {
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		const stringifiedValue = Handsontable.helper.stringify(value);
	}

	// 16.0.1版本兼容性：增强容器检查
	var container = document.getElementById('handsontable');
	if (!container) {
		console.error('Handsontable容器未找到');
		return;
	}

	// 16.0.1版本优化：增强错误处理的Handsontable实例化
	try {
		window.hot = new Handsontable(container, {
		data: initData,
		fixedRowsTop: fixedRowsTop,
		mergeCells: merged,
		rowHeaders: true,
		colHeaders: true,
		rowHeights: rowHeights,
		dropdownMenu: false,
		customBorders: true,
		comments: true,
		colWidths: colWidths,
		fillHandle: true,
		renderer: myCellRenderer,
		language: 'zh-CN',
		licenseKey: 'non-commercial-and-evaluation',
		className: 'htMiddle htCenter',
		manualColumnResize: true,
		manualRowResize: true,
		// 16.0.1版本性能优化配置
		preventOverflow: 'horizontal',
		preventWheel: true,
		contextMenu: {
			callback(key, selection, clickEvent) {
				// Common callback for all options
				console.log(key, selection, clickEvent);
			},
			items: {
				row_above: {
					name: '上方插入行'
				},
				row_below: {
					name: '下方插入行'
				},
				sp1: '---------',
				col_left: {
					name: '左方插入列'
				},
				col_right: {
					name: '右方插入列'
				},
				sp2: '---------',
				remove_row: {
					name: '移除该行'
				},
				remove_col: {
					name: '移除该列'
				},
				remove_col: {
					name: '移除该列'
				},
				set_header: {
					name: function() {
						var sels = this.getSelectedRange();
						if (sels) {
							var sel = sels[0];
							if (this.getSettings().fixedRowsTop == sel.to.row + 1) {
								return "取消设置表头";
							}
						}
						return "设置表头";
					},
					hidden: function() {
						var sels = this.getSelectedRange();
						//必须选中第一行 并且只有一个选中区域才允许
						if (sels.length == 1) {
							var sel = sels[0];
							if (sel.from.row == 0 && sel.from.col == -1) {
								return false;
							} else {
								return true;
							}
						} else {
							return true;
						}
					},
					callback(key, selection, clickEvent) {
						var headerRow = 0;
						if (clickEvent.target.innerText == '设置表头') {
							headerRow = selection[0].end.row + 1;
							HotUtil.dealClass("td-bg", "add");
						} else {
							HotUtil.dealClass("td-bg", "cancel");
						}
						this.updateSettings({
							fixedRowsTop: headerRow
						});
					}
				},
				sp3: '---------',
				mergeCells: {
					name: function name() {
						var sel = this.getSelectedLast();
						if (sel) {
							var info = this.getPlugin('MergeCells').mergedCellsCollection.get(sel[0], sel[1]);

							if (info.row === sel[0] && info.col === sel[1] && info.row + info.rowspan - 1 === sel[2] && info.col + info.colspan - 1 === sel[3]) {
								return "取消合并";
							}
						}
						return "合并";
					},
					disabled() {
						return HotUtil.atLeastOneReadOnly(this);
					}
				},
				alignment: {
					name: '对齐'
				},
				make_read_only: {
					name: '只读'
				},
				fontSize: {
					name() {
						return '字体大小';
					},
					submenu: {
						items: HotUtil.fontSizeItems()
					}
				},
				bold: { // Own custom option
					name() { // `name` can be a string or a function
						return '加粗'; // Name can contain HTML
					},
					callback(key, selection, clickEvent) { // Callback for specific option
						HotUtil.dealClass("c-bold", "add");
					}
				},
				cancelBold: { // Own custom option
					name() { // `name` can be a string or a function
						return '取消加粗'; // Name can contain HTML
					},
					callback(key, selection, clickEvent) { // Callback for specific option
						HotUtil.dealClass("c-bold", "cancel");
					}
				},
				set_type: {
					name() {
						return '单元格类型'
					},
					submenu: {
						items: [{
							name: '文本',
							key: 'set_type:text',
							callback: function(key, selection, clickEvent) {
								HotUtil.eachSelectedRange(window.hot, function(r, c) {
									window.hot.setCellMeta(r, c, 'type', 'text');
									window.hot.setCellMeta(r, c, 'myType', 'text');
								});
								window.hot.render();
							}
						}, {
							name: '日期',
							key: 'set_type:date',
							callback: function(key, selection, clickEvent) {
								HotUtil.eachSelectedRange(window.hot, function(r, c) {
									window.hot.setCellMeta(r, c, 'type', 'date');
									window.hot.setCellMeta(r, c, 'myType', 'date');
									window.hot.setCellMeta(r, c, 'dateFormat', 'YYYY-MM-DD');
								});
								window.hot.render();
							}
						}, {
							name: '下拉选择',
							key: 'set_type:dropdown',
							callback: function(key, selection, clickEvent) {
								var srs = this.getSelectedRange();
								var sources = window.hot.getCellMeta(srs[0].from.row, srs[0].from.col).source || [];
								var tableDatas = [];
								for (var i = 0; i < sources.length; i++) {
									tableDatas.push({
										source: sources[i]
									});
								}
								layer.open({
									title: "设置选项",
									type: 1,
									area: ['450px', "510px"],
									content: '<div id="sourceContent" style=""><table class="layui-hide" id="source-table" lay-filter="source-table"></table></div>',
									anim: false,
									openDuration: 200,
									isOutAnim: false,
									closeDuration: 200,
									resize: false,
									btn: ['确定', '取消'],
									yes: function(index, layero, that) {
										var datas = table.getData('source-table');
										var arr = [];
										for (var i = 0; i < datas.length; i++) {
											if (datas[i].source) {
												arr.push(datas[i].source);
											}
										}
										HotUtil.eachArrays(srs, window.hot, function(r, c) {
											window.hot.setCellMeta(r, c, 'type', 'dropdown');
											window.hot.setCellMeta(r, c, 'myType', 'dropdown');
											window.hot.setCellMeta(r, c, 'source', arr);
										});
										window.hot.render();
										layer.close(index);
									},
									btn2: function() {
										return true;
									},
									success: function() {
										table.render({
											elem: '#source-table',
											id: 'source-table',
											data: tableDatas,
											toolbar: `<div class="layui-btn-container">
														<button class="layui-btn layui-btn-sm" lay-event="add-btn">新增</button>
														<button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete-btn">删除</button>
													</div>`,
											defaultToolbar: [],
											height: 400, // 最大高度减去其他容器已占有的高度差
											cellMinWidth: 80,
											cols: [
												[{
														type: 'checkbox',
														fixed: 'left'
													},
													{
														title: '序号',
														type: "numbers",
														width: 60
													},
													{
														field: 'source',
														title: '下拉选项',
														align: 'center',
														edit: 'text'
													}
												]
											]
										});

										table.on('toolbar(source-table)', function(obj) {
											var options = obj.config;
											var id = options.id;
											var checkStatus = table.checkStatus();
											if (obj.event == 'add-btn') {
												var datas = table.cache[options.id];
												datas.push({
													'source': ''
												});
												table.renderData(id);
											} else if (obj.event == 'delete-btn') {
												if (table.checkStatus(id).data.length == 0) {
													layer.alert('至少选择一条数据删除！', {
														icon: 2
													});
												} else {
													var datas = table.cache[id];
													var newData = [];
													for (var i = 0; i < datas.length; i++) {
														if (!datas[i]['LAY_CHECKED']) {
															newData.push(datas[i]);
														}
													}
													table.cache[id] = newData;
													table.renderData(id);
												}
											}
										});
									}
								});
							}
						}, {
							name: '数字',
							key: 'set_type:numeric',
							callback: function(key, selection, clickEvent) {
								HotUtil.eachSelectedRange(window.hot, function(r, c) {
									window.hot.setCellMeta(r, c, 'type', 'numeric');
									window.hot.setCellMeta(r, c, 'myType', 'numeric');
								});
								window.hot.render();
							}
						}, {
							name: '文件上传',
							key: 'set_type:file',
							callback: function(key, selection, clickEvent) {
								HotUtil.eachSelectedRange(window.hot, function(r, c) {
									window.hot.setCellMeta(r, c, 'myType', 'file');
								});
								window.hot.render();
							}
						}, {
							name: '公式引用',
							key: 'set_type:formula',
							callback: function(key, selection, clickEvent) {
								HotUtil.eachSelectedRange(window.hot, function(r, c) {
									window.hot.setCellMeta(r, c, 'myType', 'formula');
								});
								window.hot.render();
							}
						}]
					},
					hidden() {
						return HotUtil.atLeastOneReadOnly(this);
					}
				}
			}
		}
	});

	// 16.0.1版本上下文菜单定位优化
	if (window.hot && window.hot.addHook) {
		window.hot.addHook('afterContextMenuShow', function() {
			// 调用HandsonTableUtil的菜单定位优化函数
			if (typeof HotUtil !== 'undefined' && HotUtil.adjustContextMenuPosition) {
				HotUtil.adjustContextMenuPosition();
			}
		});
	}

	} catch (error) {
		console.error('Handsontable初始化失败:', error);
		layer.alert('表格初始化失败: ' + error.message, {
			icon: 2,
			title: '初始化错误'
		});
		return;
	}

	// 16.0.1版本优化：增强错误处理的setCellMeta操作
	try {
		for (var m = 0; m < metas.length; m++) {
		var meta = metas[m];
		if (meta) {
			var isReadOnly = false;
			if (meta.row < fixedRowsTop) {
				isReadOnly = true;
				meta.readOnly = isReadOnly;
			}
		}
	}


	for (var m = 0; m < metas.length; m++) {
		var meta = metas[m];
		if (meta && typeof meta.row === 'number' && typeof meta.col === 'number') {
			try {
				meta.className = 'htMiddle htCenter';
				//将表头添加背景颜色
				if (meta.row < fixedRowsTop) {
					meta.className = meta.className + " td-bg";
				}

				// 安全地设置单元格元数据
				if (meta.className) {
					window.hot.setCellMeta(meta.row, meta.col, 'className', meta.className);
				}
				if (meta.readOnly) {
					window.hot.setCellMeta(meta.row, meta.col, 'readOnly', meta.readOnly);
				}
				if (meta.eles) {
					window.hot.setCellMeta(meta.row, meta.col, 'eles', meta.eles);
				}
				if (meta.comment) {
					window.hot.setCellMeta(meta.row, meta.col, 'comment', meta.comment);
				}
				if (meta.type) {
					window.hot.setCellMeta(meta.row, meta.col, 'type', meta.type);
				}
				if (meta.myType) {
					window.hot.setCellMeta(meta.row, meta.col, 'myType', meta.myType);
				}
				if (meta.dateFormat) {
					window.hot.setCellMeta(meta.row, meta.col, 'dateFormat', meta.dateFormat);
				}
				if (meta.source) {
					window.hot.setCellMeta(meta.row, meta.col, 'source', meta.source);
				}
			} catch (metaError) {
				console.warn('设置单元格(' + meta.row + ',' + meta.col + ')元数据时出错:', metaError);
			}
		}
	}

	// 16.0.1版本优化：增强错误处理的渲染操作
	try {
		window.hot.render();
	} catch (renderError) {
		console.error('表格渲染失败:', renderError);
		layer.alert('表格渲染失败，请刷新页面重试', {
			icon: 2,
			title: '渲染错误'
		});
	}

	} catch (error) {
		console.error('设置单元格元数据时发生异常:', error);
		layer.alert('表格配置失败: ' + error.message, {
			icon: 2,
			title: '配置错误'
		});
	}
}