/**
 * 统计表格模块
 * 负责处理系统统计数据的加载和展示
 */

// 更新系统统计数据
function updateSystemStatistics(data) {
    var table = $('.statistics-table');
    var thead = table.find('thead');
    var tbody = table.find('tbody');

    // 清空现有内容
    thead.empty();
    tbody.empty();

    // 创建表头行 - 系统名称
    var headerRow = $('<tr class="system-names">');
    data.forEach(function (item) {
        // 将标题文本包装在header-content容器内
        var th = $('<th>');
        var headerContent = $('<div class="header-content">').text(item.NAME);
        th.append(headerContent);
        headerRow.append(th);
    });
    thead.append(headerRow);

    // 创建统计数据行
    var statsRow = $('<tr class="completion-stats">');
    data.forEach(function (item) {
        var td = $('<td>');
        // 分别添加不同样式的元素，并为数字添加点击事件
        td.append($('<span class="stats-label">').text('共计'));
        td.append($('<span class="stats-number total-number clickable">')
            .text(item.TOTAL)
            .on('click', function () {
                // 获取所有产品，使用subsysNAME字段
                twxAjax('Thing.Fn.TestEvaluation', 'GetProductListV2', {
                    modelId: $("#model-select").val(),
                    subsystemName: item.SUBSYS_NAME,
                    status: ''  // 不筛选状态，获取所有产品
                }, true, function (res) {
                    if (res.success) {
                        window.generateProductTable(res.data, item.NAME + ' - 全部产品列表');
                    } else {
                        layer.msg(res.msg, { icon: 2 });
                    }
                });
            }));
        td.append($('<span class="stats-label">').text('项，已完成'));
        td.append($('<span class="stats-number completed-number clickable">')
            .text(item.COMPLETED)
            .on('click', function () {
                // 获取已完成的产品，使用subsysNAME字段
                twxAjax('Thing.Fn.TestEvaluation', 'GetProductListV2', {
                    modelId: $("#model-select").val(),
                    subsystemName: item.SUBSYS_NAME,
                    status: '已完成'
                }, true, function (res) {
                    if (res.success) {
                        window.generateProductTable(res.data, item.NAME + ' - 已完成产品列表');
                    } else {
                        layer.msg(res.msg, { icon: 2 });
                    }
                });
            }));
        td.append($('<span class="stats-label">').text('项'));
        statsRow.append(td);
    });
    thead.append(statsRow);

    // 创建进度条行
    var progressRow = $('<tr class="progress-bars">');
    data.forEach(function (item) {
        // 修改进度条逻辑：当总数为0时，如果是四级或三级产品，显示100%，否则显示0%
        var percentage = 0;
        if (item.TOTAL > 0) {
            percentage = Math.round((item.COMPLETED / item.TOTAL) * 100);
        } else if (item.NAME === '四级产品状态鉴定' || item.NAME === '三级产品状态鉴定') {
            percentage = 100; // 当四级或三级产品的共计为0时，显示100%
        }

        var td = $('<td>');
        var container = $('<div class="progress-container">');
        var bar = $('<div class="progress-bar">').css('width', percentage + '%');
        var text = $('<span class="progress-text">').text(percentage + '%');

        // 根据完成度设置进度条颜色
        if (percentage === 100) {
            bar.css('background', 'linear-gradient(90deg, #00C853, #69F0AE)');  // 更清新的绿色
        } else if (percentage >= 50) {
            bar.css('background', 'linear-gradient(90deg, #2196F3, #64B5F6)');  // 更柔和的蓝色
        } else {
            bar.css('background', 'linear-gradient(90deg, #FF5252, #FF8A80)');  // 更柔和的红色
        }

        container.append(bar).append(text);
        td.append(container);
        progressRow.append(td);
    });
    tbody.append(progressRow);
}

// 导出函数
window.updateSystemStatistics = updateSystemStatistics;