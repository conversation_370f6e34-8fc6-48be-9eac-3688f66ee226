/**
 * 初始化参数表
 */
function renderParamTable() {
	var tableHeight = $(".layui-col-md10 .layui-card").height() - 63;
	table.render({
		elem: '#param-table',
		id: 'param-table',
		url: getUrl(THING, 'QueryCalcParam'),
		where: {
			pId: currentTreeId
		},
		toolbar: '#param-toolbar',
		defaultToolbar: ['filter', {
			title: '刷新',
			layEvent: 'param_reload',
			icon: 'layui-icon-refresh'
		}],
		height: tableHeight,
		cellMinWidth: 80,
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'NAME',
					width: 120,
					title: '名称'
				},
				{
					field: 'CODE',
					title: '代号',
					width: 120
				},
				{
					field: 'REMARK',
					title: '备注',
					width: 120
				},
				{
					fixed: 'right',
					title: '操作',
					width: 180,
					minWidth: 180,
					toolbar: '#param-rowbar',
					align: 'center'
				}
			]
		],
		done: function(res, curr, count, origin) {
			//加载完成参数列表之后 再重新加载结果列表
			renderResultTable();
		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});

	// 工具栏事件
	table.on('toolbar(param-table)', function(obj) {
		switch (obj.event) {
			case 'add-btn':
				addParam();
				break;
			case 'delete-btn':
				deleteMoreParam();
				break;
			case 'param_reload':
				renderTable();
				break;
		};
	});

	table.on('tool(param-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'quote') {
			formulaAdd('`' + data['CODE'] + '`');
		} else if (obj.event === 'edit') {
			editParam(data);
		} else if (obj.event === 'delete') {
			deleteParam(data.ID);
		}
	});
}

/**
 * 获取当前参数表格的所有数据
 */
function getCurrentParamData() {
	return table.getData('param-table');
}

/**
 * 隐藏参数模块 并给出信息提示
 * @param {Object} msg
 */
function hideParam(msg) {
	$('#param-div').hide();
	$("#param-msg").text(msg).show();
}

/**
 * 显示参数模块 并隐藏信息提示
 */
function showParam() {
	$('#param-div').show();
	$("#param-msg").hide();
}


function deleteParam(ids) {
	var msg = "确认删除参数吗？";
	layer.confirm(msg, {
		icon: 3,
		title: '提示'
	}, function(index) {
		twxAjax(THING, 'DeleteCalcParam', {
			ids: ids
		}, true, function(res) {
			layer.close(index)
			if (res.success) {
				renderTable();
				layer.msg(res.msg)
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	});
}

/**
 * 删除多条参数
 */
function deleteMoreParam() {
	var checks = table.checkStatus('param-table').data;
	if (checks.length == 0) {
		layer.alert("至少选择一条数据！", {
			icon: 2
		});
	} else {
		var tempArr = [];
		for (var i = 0; i < checks.length; i++) {
			var id = checks[i]["ID"];
			tempArr.push(id);
		}
		var ids = tempArr.join(",");
		deleteParam(ids);
	}
}

/**
 * 修改参数 并修改引用值
 * @param {Object} data
 */
function editParam(data) {
	var remark = data['REMARK'] || '';
	var editTpl =
		'<form class="layui-form" action="" lay-filter="edit-param-form">\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数名称:</label>\
				<div class="layui-input-block">\
					<input type="text" name="name" lay-verify="required" value="' + data['NAME'] + '" autocomplete="off" placeholder="请输入参数名称" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数代号:</label>\
				<div class="layui-input-block">\
					<input type="text" name="code" lay-verify="required|calcParamCode" value="' + data['CODE'] + '" autocomplete="off" placeholder="请输入参数代号" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">备注:</label>\
				<div class="layui-input-block">\
					<textarea name="remark" autocomplete="off" placeholder="请输入备注" class="layui-textarea">' + remark + '</textarea>\
				</div>\
			</div>\
			<div class="layui-form-item" style="display:none;">\
				<div class="layui-input-block">\
					<div class="layui-footer">\
						<button class="layui-btn" id="editParamSubmit" lay-submit="" lay-filter="submit-edit-param">确认</button>\
						<button type="reset" id="editParamReset" class="layui-btn layui-btn-primary">重置</button>\
					</div>\
				</div>\
			</div>\
		</form>';
	layer.open({
		title: '修改参数',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: ['500px', '320px'],
		content: '<div id="editTableParamContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '重置', '取消'],
		yes: function() {
			$('#editParamSubmit').click();
		},
		btn2: function() {
			$('#editParamReset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function(layero, userLayerIndex, that) {
			$(layero).find('.layui-layer-content').css("overflow", "visible");
			$("#editTableParamContent").append(editTpl);
		}
	});

	form.on('submit(submit-edit-param)', function(formData) {
		var param = {};
		param.id = data.ID;
		param.name = formData.field.name;
		param.code = formData.field.code;
		param.remark = formData.field.remark;
		param.updateUser = sessionStorage.getItem('username');
		var loadIndex = layer.load();
		var cb_success = function(res) {
			layer.close(loadIndex);
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				renderTable();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function() {
			layer.alert('添加失败，请联系管理员！', {
				icon: 2
			});
		};
		twxAjax(THING, 'UpdateCalcParam', param, true, cb_success, cb_error);
		return false;
	});
}
/**
 * 添加参数
 */
function addParam() {
	var addTpl =
		'<form class="layui-form" action="" lay-filter="add-param-form">\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数名称:</label>\
				<div class="layui-input-block">\
					<input type="text" name="name" lay-verify="required" autocomplete="off" placeholder="请输入参数名称" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数代号:</label>\
				<div class="layui-input-block">\
					<input type="text" name="code" lay-verify="required|calcParamCode" autocomplete="off" placeholder="请输入参数代号" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">备注:</label>\
				<div class="layui-input-block">\
					<textarea name="remark" autocomplete="off" placeholder="请输入备注" class="layui-textarea"></textarea>\
				</div>\
			</div>\
			<div class="layui-form-item" style="display:none;">\
				<div class="layui-input-block">\
					<div class="layui-footer">\
						<button class="layui-btn" id="addParamSubmit" lay-submit="" lay-filter="submit-add-param">确认</button>\
						<button type="reset" id="addParamReset" class="layui-btn layui-btn-primary">重置</button>\
					</div>\
				</div>\
			</div>\
		</form>';
	layer.open({
		title: '添加参数',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: ['500px', '320px'],
		content: '<div id="addTableParamContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '重置', '取消'],
		yes: function() {
			$('#addParamSubmit').click();
		},
		btn2: function() {
			$('#addParamReset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function(layero, userLayerIndex, that) {
			$(layero).find('.layui-layer-content').css("overflow", "visible");
			$("#addTableParamContent").append(addTpl);
		}
	});

	form.on('submit(submit-add-param)', function(formData) {
		var param = {};
		param.pId = currentTreeId;
		param.name = formData.field.name;
		param.code = formData.field.code;
		param.remark = formData.field.remark;
		param.creator = sessionStorage.getItem('username');
		var loadIndex = layer.load();
		var cb_success = function(res) {
			layer.close(loadIndex);
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				renderTable();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function(data) {
			layer.alert('添加失败，请联系管理员！', {
				icon: 2
			});
		};
		twxAjax(THING, 'AddCalcParam', param, true, cb_success, cb_error);
		return false;
	});
}