/**
 * 文件统计图表模块
 * 负责处理文件统计图表的展示
 */

// 绘制文件统计图
function drawFileChart(data) {
    if (!window.fileChart) return;
    
    // 为每个数据项添加渐变色
    var chartData = data.map(function (item, index) {
        // 根据不同文件类型设置不同的渐变色
        var colors;
        var name = item.NAME || "未知类型";
        var value = item.VALUE || 0;
        
        // 根据文件类型名称分配不同的颜色 - 使用更加鲜明且差异化的颜色
        if (name.includes("图样技术")) {
            // 鲜红色系
            colors = [{ offset: 0, color: '#FF4D4F' }, { offset: 1, color: '#FF7A45' }];
        } else if (name.includes("研制") && name.includes("总结")) {
            // 翠绿色系
            colors = [{ offset: 0, color: '#13C2C2' }, { offset: 1, color: '#36CFC9' }];
        } else if (name.includes("研制") && name.includes("依据")) {
            // 蓝色系
            colors = [{ offset: 0, color: '#1890FF' }, { offset: 1, color: '#69C0FF' }];
        } else if (name.includes("试验")) {
            // 紫色系
            colors = [{ offset: 0, color: '#9254DE' }, { offset: 1, color: '#B37FEB' }];
        } else {
            // 金黄色系
            colors = [{ offset: 0, color: '#FAAD14' }, { offset: 1, color: '#FFD666' }];
        }
        
        return {
            name: name,
            value: value,
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, colors),
                borderRadius: 10,
                borderColor: '#162b4e',
                borderWidth: 2
            }
        };
    });

    // 获取当前选中的型号
    var modelId = $("#model-select").val();
    var modelName = $("#model-select option:selected").text();
    var titleText = "文件统计";
    
    // 如果选择了特定型号（不是"所有型号"），则在标题中添加型号名称
    if (modelId !== "-1") {
        titleText = modelName + " - " + titleText;
    }

    var option = {
        title: {
            text: titleText,
            left: "center",
            textStyle: {
                color: "#ffffff"
            }
        },
        tooltip: {
            trigger: "item",
            formatter: function(params) {
                return '<div style="padding: 4px 0; font-weight: 600; color: #fff;">' +
                    params.seriesName + '</div>' +
                    '<div style="display: flex; justify-content: space-between; margin: 5px 0;">' +
                    '<span style="font-size: 13px; margin-right: 15px; color: #eee;">' + params.name + ':</span>' +
                    '<span style="font-size: 14px; font-weight: 600;">' + params.value + ' 个</span>' +
                    '</div>';
            },
            backgroundColor: "rgba(22, 43, 78, 0.9)",
            borderColor: "#1D3461",
            borderWidth: 1,
            borderRadius: 4,
            padding: [10, 15],
            textStyle: {
                color: "#ffffff"
            },
            extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);'
        },
        series: [
            {
                name: "文件类型",
                type: "pie",
                radius: ["35%", "70%"],
                center: ['50%', '50%'],  
                avoidLabelOverlap: false,
                label: {
                    show: true,
                    position: 'outside',
                    formatter: "{b}: {c} ({d}%)",
                    color: "#ffffff"
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: "16",
                        fontWeight: "bold"
                    }
                },
                labelLine: {
                    show: true,
                    length: 15,
                    length2: 10,
                    lineStyle: {
                        color: "#ffffff",
                        opacity: 0.5
                    }
                },
                data: chartData
            }
        ]
    };
    fileChart.setOption(option);

    fileChart.off("click");
    fileChart.on("click", function (params) {
        window.generateFileTable(params.data.name, params.data.name + " 文件列表");
    });
}

// 导出函数
window.drawFileChart = drawFileChart;