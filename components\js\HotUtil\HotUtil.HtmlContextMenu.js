/**
 * HotUtil.HtmlContextMenu.js - Handsontable工具类 (HTML表格右键菜单模块)
 *
 * 负责管理静态HTML表格视图的右键菜单。
 */

/**
 * 表格行的右键菜单
 * @param {jQuery} $newTr
 * @param {Object} treeNode
 * @param {jQuery} tableSel
 * @param {string} pageType
 */
HotUtil.trContextMenu = function ($newTr, treeNode, tableSel, pageType) {
    var trIsHasLockTd = false;
    //处理锁定单元格的显示
    $newTr.find("td").each(function (j, m) {
        if ($(m).attr("lock") == "true") {
            trIsHasLockTd = true;
            $(m).addClass("htDimmed");
            if (j == 0) {
                HotUtil.tdContextSignMenu(m, treeNode, tableSel, true, pageType);
            } else {
                HotUtil.tdContextSignMenu(m, treeNode, tableSel, false, pageType);
            }
        } else {
            HotUtil.tdContextEditMenu(m, treeNode, tableSel, false, pageType);
        }
    })
    if (!trIsHasLockTd) {
        //没有锁定的话
        $newTr.find("td").each(function (j, m) {
            if (j == 0) {
                HotUtil.tdContextEditMenu(m, treeNode, tableSel, true, pageType);
            } else {
                HotUtil.tdContextEditMenu(m, treeNode, tableSel, false, pageType);
            }
        })
    }
};

/**
 * 锁定之前的右键菜单
 * @param {HTMLElement} n td元素
 * @param {Object} treeNode
 * @param {jQuery} tableSel
 * @param {boolean} islockRow
 * @param {string} pageType
 */
HotUtil.tdContextEditMenu = function (n, treeNode, tableSel, islockRow, pageType) {
    var contextEle = void 0;
    var menus = [];
    if (islockRow) {
        menus.push({
            text: "锁定该行",
            icon: '../dataTree/images/lock.png',
            callback: function () {
                HotUtil.openEdit(treeNode, 0, function () {
                    HotUtil.updateLockRow(contextEle, treeNode, 'lock');
                });
            }
        });
    }
    menus.push({
        text: "上传图片",
        icon: '../dataTree/images/upload-image.png',
        callback: function () {
            HotUtil.openEdit(treeNode, 0, function () {
                HotUtil.insertImage(treeNode, contextEle, tableSel);
            });
        }
    });
    if (HotUtil.isAllowTakePhoto()) {
        menus.push({
            text: "拍照",
            icon: '../dataTree/images/拍照.png',
            callback: function () {
                HotUtil.takePhoto(treeNode, contextEle, tableSel);
            }
        });
    }
    if ($(n).data("hasImg")) {
        menus.push({
            text: "查看图片",
            icon: '../dataTree/images/view.png',
            callback: function () {
                HotUtil.viewImage(treeNode, contextEle, tableSel, true);
            }
        });
    }
    $(n).contextMenu({
        width: 110,
        menu: menus,
        target: function (ele) {
            contextEle = ele;
        }
    });
};

/**
 * 锁定之后的右键菜单
 * @param {HTMLElement} n td元素
 * @param {Object} treeNode
 * @param {jQuery} tableSel
 * @param {boolean} isUnlockRow
 * @param {string} pageType
 */
HotUtil.tdContextSignMenu = function (n, treeNode, tableSel, isUnlockRow, pageType) {
    var contextEle = void 0;
    var menus = [];
    if (isUnlockRow && contains(sessionStorage.getItem('funcids').split(','), 'func-' + pageType +
        '-unlock-row')) {
        menus.push({
            text: "解锁此行",
            icon: '../dataTree/images/unlock.png',
            callback: function () {
                HotUtil.updateLockRow(contextEle, treeNode, 'unlock');
            }
        });
    }
    menus = menus.concat([{
        text: "鼠标签名",
        icon: '../dataTree/images/sign.png',
        callback: function () {
            HotUtil.sign(treeNode, contextEle, tableSel);
        }
    }, {
        text: "手写板签名",
        icon: '../dataTree/images/sign.png',
        callback: function () {
            HotUtil.wacomSign(treeNode, contextEle, tableSel);
        }
    }, {
        text: "上传签章",
        icon: '../dataTree/images/sign1.png',
        callback: function () {
            HotUtil.uploadSign(treeNode, contextEle, tableSel);
        }
    }]);
    if ($(n).data("hasImg")) {
        menus.push({
            text: "查看图片",
            icon: '../dataTree/images/view.png',
            callback: function () {
                HotUtil.viewImage(treeNode, contextEle, tableSel, false);
            }
        });
    }
    $(n).contextMenu({
        width: 110,
        menu: menus,
        target: function (ele) {
            contextEle = ele;
        }
    });
};