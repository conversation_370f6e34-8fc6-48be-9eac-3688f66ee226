/**
 * 隐藏验证模块 并给出信息提示
 * @param {Object} msg
 */
function hideVerify(msg) {
	$('#verify-div').hide();
	$("#verify-msg").text(msg).show();
}

/**
 * 显示验证模块 并隐藏信息提示
 */
function showVerify() {
	$('#verify-div').show();
	$("#verify-msg").hide();
}

/**
 * 依据输入输出参数生成表单元素的html字符串
 * @param {Object} items
 * @param {Object} isReadOnly
 */
function createFormItemHtmlStr(items, isReadOnly) {
	var itemLength = items.length;
	var col = 3;
	var row = Math.ceil(itemLength / col);
	var res = "";
	for (var r = 0; r < row; r++) {
		var $row = $('<div class="layui-form-item"></div>');
		for (var c = 0; c < col; c++) {
			var itemIndex = r * col + c;
			if (itemIndex < itemLength) {
				var readOnly = isReadOnly ? 'readonly="readonly"' : 'lay-verify="required"';
				var itemName = items[itemIndex]['NAME'];
				var itemCode = items[itemIndex]['CODE'];
				var itemValue = items[itemIndex]['VALUE'] || '';
				var inline = '<div class="layui-inline">\
								<label class="layui-form-label">' + itemName + ':</label>\
								<div class="layui-input-inline" style="width:80px">\
									<input type="number" value="' + itemValue + '" name="' + itemCode + '" id="calc_' + itemCode + '" ' + readOnly + ' autocomplete="off" class="layui-input">\
								</div>\
							</div>';
				$row.append(inline);
			}
		}
		res += $row[0].outerHTML;
	}
	return res;
}

/**
 * 初始化验证表单
 */
function initVerifyForm() {
	$("#verify-div").empty();
	var params = getCurrentParamData();
	var results = getCurrentResultData();
	var $form = $('<form class="layui-form" lay-filter="verify-form"></form>');
	var paramsHtml = createFormItemHtmlStr(params, false);
	$form.append(paramsHtml);

	var btn = `<div class="layui-form-item">
			<div class="layui-input-block">
				<div class="layui-footer">
					<button class="layui-btn" id="verify-submit" lay-submit="" lay-filter="verify-submit">验证</button>
					<button type="reset" id="verify-reset" class="layui-btn layui-btn-primary">重置</button>
				</div>
			</div>
		</div>`;
	$form.append(btn)

	var resultsHtml = createFormItemHtmlStr(results, true);
	$form.append(resultsHtml);

	$("#verify-div").append($form);

	form.on('submit(verify-submit)', function(formData) {
		var fields = formData.field;
		try {
			calcResult(fields, results);
			return false;
		} catch (e) {
			return false;
		} finally {
			return false;
		}
	});
}

/**
 * 替换运算符号
 */
function replaceCalcSymbol(formula) {
	return formula.replaceAll('＋', '+').replaceAll('－', '-').replaceAll('×', '*').replaceAll('÷', '/')
}

/**
 * 计算输出结果
 */
function calcResult(fields, results) {
	for (var i = 0; i < results.length; i++) {
		var result = results[i];
		var rsCode = result['CODE'];
		if (result['FORMULA']) {
			var formula = transformFormula(result['FORMULA'], results);
			formula = replaceCalcSymbol(formula);
			for (var field in fields) {
				var value = fields[field] || 0;
				formula = formula.replaceAll('`' + field + '`', value);
			}
			var calcResult = eval(formula);
			$("#calc_" + rsCode).val(calcResult);
		}

	}

}

/**
 * 判断当前公式是否存在输出参数的code
 */
function formulaHasResultCode(formula, results) {
	var isHas = false;
	for (var i = 0; i < results.length; i++) {
		var result = results[i];
		var rsCode = '`' + result['CODE'] + '`';
		if (formula && formula.indexOf(rsCode) > -1) {
			isHas = true;
			break;
		}
	}
	return isHas;
}


/**
 * 转换公式中的结果变量
 */
function transformFormula(formula, results) {
	for (var i = 0; i < results.length; i++) {
		var result = results[i];
		var rsCode = '`' + result['CODE'] + '`';
		var rsFormula = result['FORMULA'];
		if (formula && formula.indexOf(rsCode) > -1) {
			formula = formula.replaceAll(rsCode, '(' + rsFormula + ')');
			if (formulaHasResultCode(formula, results)) {
				formula = transformFormula(formula, results);
			}
		}

	}
	return formula;

}