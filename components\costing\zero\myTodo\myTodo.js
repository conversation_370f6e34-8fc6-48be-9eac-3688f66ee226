/**
 * 加载我的待办表格搜索表单
 */
function renderMyTodoSearchForm() {
	form.render(null, 'todo-table-form');
	laydate.render({
		elem: '#todo-range-date',
		range: ['#todo-start-date', '#todo-end-date'],
		rangeLinked: true // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
	});

	// 搜索提交
	form.on('submit(todo-table-search)', function(data) {
		var field = data.field; // 获得表单字段
		field.username = currentUser;
		// 执行搜索重载
		table.reload('todo-table', {
			page: {
				curr: 1 // 重新从第 1 页开始
			},
			where: field // 搜索的字段
		});
		return false;
	});
}

/**
 * 加载我的待办列表
 */
function renderMyTodo() {
	// 创建渲染实例
	table.render({
		elem: '#todo-table',
		id: 'todo-table',
		url: getUrl(THING, 'QueryTodoTask'),
		where: {
			username: currentUser
		},
		toolbar: '#todo-toolbar',
		defaultToolbar: ['filter', {
			title: '刷新',
			layEvent: 'todo_reload',
			icon: 'layui-icon-refresh'
		}],
		height: 'full-90', // 最大高度减去其他容器已占有的高度差
		cellMinWidth: 80,
		page: true,
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'ID_',
					width: 100,
					title: '任务编号'
				},
				{
					field: 'NAME_',
					title: '任务名称',
					width: 150
				},
				{
					field: 'DEF_NAME_',
					width: 150,
					title: '所属流程'
				},
				{
					field: 'DEF_VERSION_',
					width: 90,
					title: '流程版本',
					templet: formatVersion,
					align: 'center'
				},
				{
					field: 'START_USER_ID_N_',
					width: 115,
					title: '流程发起人'
				},
				{
					field: 'CREATE_TIME_',
					title: '接收时间',
					width: 160,
					templet: formatTableDate,
					align: 'center'
				},
				{
					fixed: 'right',
					title: '操作',
					width: 180,
					minWidth: 180,
					toolbar: '#todo-rowbar'
				}
			]
		],
		done: function() {

		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});
}

function initMyTodoTableTool() {
	// 触发单元格工具事件
	table.on('tool(todo-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'handle') {
			//办理
			layer.tab({
				area: ['1040px', '750px'],
				tab: [{
					title: '表单信息',
					content: '<div class="form-content" id="handle1-form-content"></div>',
				}, {
					title: '流转记录',
					content: '<div class="record-content" id="handle1-record-content"></div>'
				}, {
					title: '流程图',
					content: '<div class="chat-content" id="handle1-chart-content"></div>'
				}],
				success: function(layero) {
					handleTodo(data);
					viewTransferRecords(data, "handle1-record-content");
				},
				change: function(index, layero) {
					if (index == 2) {
						viewFlowChart(data, "handle1-chart-content");
					}
				}
			});
		} else if (obj.event === 'transfer') {
			var taskId = data['ID_'];
			getCurrentFlowNode(taskId, function(assignee) {
				if (assignee == undefined) {
					//下一节点是结束任务

				} else {
					alterSelectUser(assignee, function(selectUser) {
						transferTask(taskId, selectUser);
					});
				}
			});
		}
	});

	// 工具栏事件
	table.on('toolbar(todo-table)', function(obj) {
		var id = obj.config.id;
		var checkStatus = table.checkStatus(id);
		var othis = lay(this);
		switch (obj.event) {
			case 'todo_reload':
				table.reloadData('todo-table');
				break;
		};
	});
}


/**
 * 转办任务
 * @param {Object} taskId 
 * @param {Object} selectUser
 */
function transferTask(taskId, selectUser) {
	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: 'transferTask',
			taskId: taskId,
			newUser: selectUser,
			optUser: currentUser
		},
	}, true, function(res) {
		if (res.success) {
			layer.msg(res.msg)
			table.reloadData('todo-table');
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('转办任务出错！', {
			icon: 2
		});
	});
}


/**
 * 获取表单提交数据
 * @param {Object} formEl
 * @param {Object} isPass
 * @param {Object} selectUser
 */
function getPostFormDatas(formEl, isPass, selectUser) {
	var postEles = $(formEl).find('[isPost="true"]');
	var postFormDatas = [];
	postEles.each(function(i, n) {
		var isReason = $(n).attr("isReason");
		var f = {
			name: $(n).attr("name"),
			value: $(n).val(),
			isReason: isReason,
			passField: {
				name: $(n).attr("passField"),
				value: isPass
			},
			userField: {
				name: $(n).attr("userField"),
				value: currentUser
			},
			nextUserField: {
				name: $(n).attr("nextUserField"),
				value: selectUser
			},
			dateField: $(n).attr("dateField")
		};
		postFormDatas.push(f);
	});
	return postFormDatas;
}
/**
 * 获取审批意见
 */
function getReason(postFormDatas) {
	var reason = "";
	for (var i = 0; i < postFormDatas.length; i++) {
		if (postFormDatas[i].isReason) {
			reason = postFormDatas[i].value;
			break;
		}
	}
	return reason;
}

/**
 * 提交任务
 * @param {Object} formEl
 * @param {Object} formData
 * @param {Object} taskId
 * @param {Object} assignee
 * @param {Object} selectUser
 */
function submitTask(formEl, formData, taskId, assignee, selectUser) {
	var loadIndex = layer.msg('正在提交中......', {
		icon: 16,
		shade: 0.01,
		time: 0
	});

	var isPass = "1";
	var postFormDatas = getPostFormDatas(formEl, isPass, selectUser);
	var zId = formData['Z_ID'];
	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: 'completeTask',
			assigneeVar: assignee,
			assignee: selectUser,
			zId: zId,
			taskId: taskId,
			postFormDatas: JSON.stringify(postFormDatas)
		},
	}, true, function(res) {
		if (res.success) {
			layer.closeAll();
			layer.alert(res.msg)
			table.reloadData('todo-table');
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}

/**
 * 任务退回到上一个节点
 */
function returnTask(formEl, formData, taskId) {
	layer.confirm('确定退回吗？', {
		icon: 3
	}, function() {
		var loadIndex = layer.msg('正在提交中......', {
			icon: 16,
			shade: 0.01,
			time: 0
		});

		var isPass = "2";
		var postFormDatas = getPostFormDatas(formEl, isPass);
		var reason = getReason(postFormDatas);
		var zId = formData['Z_ID'];
		if (reason) {
			postReturnTask(reason, zId, taskId, postFormDatas);
		} else {
			layer.prompt({
				title: '请输入退回原因',
				formType: 2
			}, function(value, index, elem) {
				if (value === '') {
					return elem.focus();
				} else {
					postReturnTask(value, zId, taskId, postFormDatas);
				}
				layer.close(index);
			});
		}
	});
}

/**
 * 请求退回操作
 * @param {Object} reason
 * @param {Object} zId
 * @param {Object} taskId
 * @param {Object} postFormDatas
 */
function postReturnTask(reason, zId, taskId, postFormDatas) {
	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: 'returnTask',
			reason: reason,
			zId: zId,
			taskId: taskId,
			postFormDatas: JSON.stringify(postFormDatas)
		},
	}, true, function(res) {
		if (res.success) {
			layer.closeAll();
			layer.alert(res.msg)
			table.reloadData('todo-table');
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}

/**
 * 拒绝任务 直接结束流程
 * @param {Object} formEl
 * @param {Object} formData
 * @param {Object} taskId
 */
function rejectTask(formEl, formData, taskId) {
	var loadIndex = layer.msg('正在提交中......', {
		icon: 16,
		shade: 0.01,
		time: 0
	});

	var isPass = "2";
	var postFormDatas = getPostFormDatas(formEl, isPass);
	var reason = getReason(postFormDatas);
	var zId = formData['Z_ID'];
	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: 'rejectTask',
			reason: reason,
			zId: zId,
			taskId: taskId,
			postFormDatas: JSON.stringify(postFormDatas)
		},
	}, true, function(res) {
		if (res.success) {
			layer.closeAll();
			layer.alert(res.msg)
			table.reloadData('todo-table');
		} else {
			layer.alert(res.msg);
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}

/**
 * 处理我的待办任务
 */
function handleTodo(formData) {
	var taskName = formData['NAME_'];
	var taskId = formData['ID_'];

	var html = $("#zero-form-html")[0].innerHTML;
	$("#handle1-form-content").append(html);
	dealForm(taskName);
	$(".handle1").removeClass('layui-hide');
	form.render(null, 'zero-form');
	$("p[name]").each(function(i, n) {
		$(n).text(formData[$(n).attr("name")]);
	});
	form.val('zero-form', formData);

	//通过
	form.on('submit(handle1-form-pass)', function(data) {


		getNextFlowNode(taskId, function(assignee) {
			if (assignee == undefined) {
				//下一节点是结束任务
				submitTask(data.form, formData, taskId);
			} else {
				alterSelectUser(assignee, function(selectUser) {
					submitTask(data.form, formData, taskId, assignee, selectUser);
				});
			}
		});
		return false;
	});

	//不通过 退回
	form.on('submit(handle1-form-nopass)', function(data) {
		returnTask(data.form, formData, taskId);
		return false;
	});

	//拒绝任务 直接结束任务
	form.on('submit(handle1-form-reject)', function(data) {
		rejectTask(data.form, formData, taskId);
		return false;
	});


	$("#handle1-form-cancel").click(function() {
		layer.closeAll();
		return false;
	});

}