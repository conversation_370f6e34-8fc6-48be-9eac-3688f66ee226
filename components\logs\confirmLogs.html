<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../img/favicon.ico">
	<link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
	<!-- <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css"> -->

	<!-- <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->

	<link rel="stylesheet" href="../../css/icon.css">

	<!-- <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
	<script src="../../plugins/layui/layui.js"></script>

	<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../plugins/easyui/jquery.min.js"></script>
	<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
	<script src="../../plugins/index/jquery.fileDownload.js"></script>
	<!-- <script src="../../plugins/loading/jquery.loading.min.js"></script> -->
	<script src="../js/config/twxconfig.js"></script>
	<script src="../js/util.js"></script>

	<script type="text/javascript" src="../js/intercept.js"></script>
	<script type="text/javascript" src="../js/logUtil.js"></script>

	<title>确认表日志</title>
	<style>
		.cust-input {
			font-size: 14px;
		}

		.fieldlabel {
			font-family: '微软雅黑';
			font-size: 14px;
			padding-left: 15px;
		}
	</style>
</head>
<body>
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div data-options="region:'north',split:false" style="width:100%;height:100px;">
			<table style="margin:10px 10px" >
				<tr height="38px">
					<td class="fieldlabel" align="right" style="width: 80px;">用户：</td>
					<td>
						<input class="easyui-textbox" id="username" style="width:120px;">
					</td>
					<td class="fieldlabel" align="right" style="width: 80px;">操作：</td>
					<td>
						<select class="easyui-combobox" id="operation" data-options="valueField:'OPERATION',textField:'OPERATION',editable:false,panelHeight:'auto'" panelMaxHeight="400px" style="width:150px;">
						</select>
					</td>
					<td class="fieldlabel" style="width:90px" align="right">开始时间：</td>
					<td>
						<input class="easyui-datetimebox" id="stime" data-options="editable:false" style="width:180px;">
					</td>
					<td class="fieldlabel" style="width:90px" align="right">结束时间：</td>
					<td>
						<input class="easyui-datetimebox" id="etime" data-options="editable:false" style="width:180px;">
					</td>
					<td class="fieldlabel" align="right" style="width: 80px;">结果：</td>
					<td>
						<select class="easyui-combobox" id="reqResult" style="width:95px" data-options="valueField:'key',textField:'value',editable:false,panelHeight:'auto',data:[{key:'1',value:'成功'},{key:'0',value:'失败'}]">
						</select>
					</td>
				</tr>
				<tr height="38px">
					<td class="fieldlabel" align="right" style="width: 80px;">模块：</td>
					<td>
						<select class="easyui-combobox" id="moduleType" data-options="valueField:'MODULE_TYPE',textField:'MODULE_TYPE',editable:false,panelHeight:'auto'" panelMaxHeight="200px" style="width:120px;">
						</select>
					</td>
					<td class="fieldlabel" align="right" style="width: 80px;">型号：</td>
					<td>
						<input class="easyui-textbox" id="modelName" style="width:150px;">
					</td>
					<td class="fieldlabel" align="right" style="width: 80px;">内容：</td>
					<td colspan="3">
						<input class="easyui-textbox" id="content" style="width:100%;">
					</td>
					<td style="min-width: 67px;padding-left: 10px;">
						<!-- <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-mysearch', plain:true" 
                            onclick="searchLogs()">搜索</a> -->
						<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="searchLogs()">
							<i class="layui-icon">&#xe615;</i> 搜索
						</button>
					</td>
					<td style="min-width: 67px;padding-left: 10px;">
						<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="clearFields()">
							<i class="layui-icon">&#xe639;</i> 清除
						</button>

						<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="exportExcel()">
							<i class="layui-icon">&#xe67d;</i> 导出Excel
						</button>
						<!--  <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-clear', plain:true" 
                            onclick="clearFields()">清除</a> -->
					</td>
				</tr>
			</table>
		</div>
		<div data-options="region:'center'">
			<div id="logtable" data-options="border:false"></div>
		</div>
	</div>
</body>

<script src="confirmLogs.js"></script>