/**
 * 签署推送弹窗组件
 * 作者：wanghq
 * 创建时间：2025-07-15 18:30:00
 * 说明：用于显示未签署用户列表并支持批量推送到BPM系统
 */

var SignPushDialog = {
    // 当前表格信息
    currentTableInfo: null,
    // 未签署用户数据
    unsignedUsers: [],
    // 表格实例
    tableInstance: null,

    /**
     * 显示推送签署弹窗
     * @param {number} tableId 表格ID
     * @param {string} pageType 页面类型
     */
    show: function (tableId, pageType) {
        var self = this;

        // 获取未签署用户数据
        self.loadUnsignedUsers(tableId, pageType, function (success, data) {
            if (success) {
                self.currentTableInfo = data.tableInfo;
                self.unsignedUsers = data.unsignedUsers;

                if (self.unsignedUsers.length === 0) {
                    layer.msg('当前表格没有未签署用户', { icon: 1 });
                    return;
                }

                self.openDialog();
            } else {
                layer.msg('获取未签署用户失败：' + data, { icon: 2 });
            }
        });
    },

    /**
     * 获取未签署用户数据
     * @param {number} tableId 表格ID
     * @param {string} pageType 页面类型
     * @param {function} callback 回调函数
     */
    loadUnsignedUsers: function (tableId, pageType, callback) {
        var param = {
            tableId: tableId,
            pageType: pageType
        };

        var cb_success = function (res) {
            if (res.success) {
                callback(true, res.data);
            } else {
                callback(false, res.msg);
            }
        };

        var cb_error = function () {
            callback(false, '网络请求失败');
        };

        twxAjax('Thing.Util.SignPushTask', 'GetUnsignedUsers', param, true, cb_success, cb_error);
    },

    /**
     * 打开弹窗
     */
    openDialog: function () {
        var self = this;

        layer.open({
            title: '推送签署任务',
            type: 1,
            area: ['1000px', '650px'],
            maxmin: true,
            content: self.getDialogContent(),
            success: function (layero, index) {
                self.initTable();
                self.bindEvents();
            },
            cancel: function (index) {
                // 清理资源
                self.cleanup();
            }
        });
    },

    /**
     * 获取弹窗内容HTML
     */
    getDialogContent: function () {
        var tableInfo = this.currentTableInfo;
        var html = '<div style="padding: 15px;">';

        // {{ wanghq: Modify - 添加样式定义，包含已签署单元格样式.  }}
        // 添加样式定义
        html += '<style>';
        html += '.unsigned-position { display: inline-flex; align-items: center; justify-content: center; margin: 2px; padding: 1px 2px; background-color: #fff1f0; border: 1px solid #ffccc7; border-radius: 2px; color: #ff4d4f; font-size: 12px; line-height: 1.2; white-space: nowrap; flex-grow: 0; flex-shrink: 0; flex-basis: calc(100%/3 - 4px); box-sizing: border-box; }';
        html += '.signed-position { display: inline-flex; align-items: center; justify-content: center; margin: 2px; padding: 1px 2px; background-color: #f6ffed; border: 1px solid #b7eb8f; border-radius: 2px; color: #52c41a; font-size: 12px; line-height: 1.2; white-space: nowrap; flex-grow: 0; flex-shrink: 0; flex-basis: calc(100%/3 - 4px); box-sizing: border-box; }';
        html += '.unsigned-positions-wrapper, .signed-positions-wrapper { display: flex; flex-wrap: wrap; overflow-x: hidden; padding: 2px; align-content: flex-start; max-height: none; overflow-y: visible; }';
        html += '.layui-table-cell { height: auto !important; white-space: normal !important; }';
        html += '.push-sign-table .layui-table-cell { min-height: 40px; vertical-align: top; padding: 8px 15px; }';
        html += '</style>';

        // 表格信息
        html += '<div class="layui-row">';
        html += '<p><strong>表格名称：</strong>' + (tableInfo.tableNum || '') + '：' + (tableInfo.tableName || '') + '</p>';
        html += '<p><strong>节点路径：</strong>' + (tableInfo.parentTitle || '') + '</p>';
        html += '</div>';

        // 操作按钮区域
        html += '<div style="margin: 15px 0;">';
        html += '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="batch-push-btn">';
        html += '<i class="layui-icon">&#xe609;</i> 批量推送';
        html += '</button>';
        html += '<button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="refresh-btn">';
        html += '<i class="layui-icon">&#xe669;</i> 刷新';
        html += '</button>';
        html += '<span style="margin-left: 20px; color: #999;">共 ' + this.unsignedUsers.length + ' 个未签署用户</span>';
        html += '</div>';

        // 表格容器
        html += '<div id="push-sign-table" class="push-sign-table"></div>';

        html += '</div>';
        return html;
    },

    /**
     * 初始化表格
     */
    initTable: function () {
        var self = this;

        // {{ wanghq: Modify - 处理表格数据，增加已签署单元格信息.  }}
        // 处理表格数据
        var tableData = [];
        for (var i = 0; i < self.unsignedUsers.length; i++) {
            var user = self.unsignedUsers[i];
            var unsignedCellsDisplay = self.formatUnsignedCells(user.unsignedCells);
            var signedCellsDisplay = self.formatSignedCells(user.unsignedCells);

            tableData.push({
                userId: user.userId,
                userDisplay: user.userFullname + '(' + user.userWorkno + ')',
                userFullname: user.userFullname,
                userWorkno: user.userWorkno,
                tableName: self.currentTableInfo.tableNum + '：' + self.currentTableInfo.tableName,
                unsignedCells: user.unsignedCells,
                unsignedCellsDisplay: unsignedCellsDisplay,
                signedCellsDisplay: signedCellsDisplay,
                localStatus: user.localStatus,
                localStatusValue: user.localStatusValue,
                bpmStatus: user.bpmStatus,
                bpmStatusValue: user.bpmStatusValue
            });
        }

        // 初始化layui表格
        layui.use('table', function () {
            var table = layui.table;

            self.tableInstance = table.render({
                elem: '#push-sign-table',
                data: tableData,
                height: 464,
                cols: [[
                    { type: 'checkbox', width: 60 },
                    { title: '序号', width: 60, align: 'center', type: 'numbers' },
                    { field: 'userDisplay', title: '姓名', width: 180 },
                    { field: 'tableName', title: '表格名称' },
                    {
                        field: 'unsignedCellsDisplay',
                        title: '未签署单元格',
                        width: 115,
                        templet: function (d) {
                            return d.unsignedCellsDisplay;
                        }
                    },
                    {
                        field: 'signedCellsDisplay',
                        title: '已签署单元格',
                        width: 115,
                        templet: function (d) {
                            return d.signedCellsDisplay;
                        }
                    },
                    {
                        field: 'localStatus', title: '本地状态', width: 85, align: 'center', templet: function (d) {
                            var statusClass = '';
                            switch (d.localStatusValue) {
                                case 'PENDING':
                                    statusClass = 'layui-bg-gray';
                                    break;
                                case 'COMPLETED':
                                    statusClass = 'layui-bg-green';
                                    break;
                                case 'CANCELLED':
                                    statusClass = 'layui-bg-red';
                                    break;
                            }
                            return '<span class="layui-badge ' + statusClass + '">' + d.localStatus + '</span>';
                        }
                    },
                    {
                        field: 'bpmStatus', title: 'BPM状态', width: 85, align: 'center', templet: function (d) {
                            var statusClass = '';
                            switch (d.bpmStatusValue) {
                                case 'PENDING':
                                    statusClass = 'layui-bg-gray';
                                    break;
                                case 'PUSHED':
                                    statusClass = 'layui-bg-blue';
                                    break;
                                case 'COMPLETED':
                                    statusClass = 'layui-bg-green';
                                    break;
                                case 'CANCELLED':
                                    statusClass = 'layui-bg-red';
                                    break;
                            }
                            return '<span class="layui-badge ' + statusClass + '">' + d.bpmStatus + '</span>';
                        }
                    },
                    {
                        field: 'operation', title: '操作', width: 70, align: 'center', templet: function (d) {
                            // {{ wanghq: Modify - 根据BPM状态调整操作列显示逻辑 }}
                            switch (d.bpmStatusValue) {
                                case 'PENDING':  // 待推送
                                    return '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="SignPushDialog.pushSingle(' + d.userId + ')">推送</button>';
                                case 'PUSHED':   // 已推送
                                    return '<span class="layui-text-muted">已推送</span>';
                                case 'FAILED':   // 未完成
                                    return '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="SignPushDialog.retryComplete(' + d.userId + ')">重试</button>';
                                case 'COMPLETED': // 已完成
                                    return '<span class="layui-text-muted">已完成</span>';
                                default:
                                    return '<span class="layui-text-muted">-</span>';
                            }
                        }
                    }
                ]],
                page: false
            });
        });
    },

    /**
     * 绑定事件
     */
    bindEvents: function () {
        var self = this;

        // 批量推送按钮
        $('#batch-push-btn').on('click', function () {
            self.batchPush();
        });

        // 刷新按钮
        $('#refresh-btn').on('click', function () {
            self.refreshData();
        });
    },

    /**
     * 批量推送
     */
    batchPush: function () {
        var self = this;

        layui.use('table', function () {
            var table = layui.table;
            var checkStatus = table.checkStatus('push-sign-table');
            var data = checkStatus.data;

            if (data.length === 0) {
                layer.msg('请选择要推送的用户', { icon: 2 });
                return;
            }

            // {{ wanghq: Modify - 根据BPM状态过滤可推送的用户 }}
            // 过滤出可以推送的用户（待推送或未完成）
            var pendingUsers = [];
            for (var i = 0; i < data.length; i++) {
                if (data[i].bpmStatusValue === 'PENDING' || data[i].bpmStatusValue === 'FAILED') {
                    pendingUsers.push(data[i].userId);
                }
            }

            if (pendingUsers.length === 0) {
                layer.msg('选中的用户都不能推送', { icon: 2 });
                return;
            }

            self.pushToUsers(pendingUsers);
        });
    },

    /**
     * 单个推送
     * @param {number} userId 用户ID
     */
    pushSingle: function (userId) {
        this.pushToUsers([userId]);
    },

    /**
     * 重试完成任务
     * @param {number} userId 用户ID
     */
    retryComplete: function (userId) {
        var self = this;

        // 从表格数据中找到对应的用户信息
        var userInfo = null;
        for (var i = 0; i < self.unsignedUsers.length; i++) {
            if (self.unsignedUsers[i].userId === userId) {
                userInfo = self.unsignedUsers[i];
                break;
            }
        }

        if (!userInfo) {
            layer.msg('未找到用户信息', { icon: 2 });
            return;
        }

        layer.confirm('确定要重试完成该用户的签署任务吗？', {
            icon: 3,
            title: '重试确认'
        }, function(index) {
            layer.close(index);
            self.callCompleteSignTask(userInfo);
        });
    },

    /**
     * 推送到指定用户
     * @param {array} userIds 用户ID数组
     */
    pushToUsers: function (userIds) {
        // layer.alert("正在开发");
        // return;
        var self = this;

        var param = {
            tableId: self.currentTableInfo.tableId,
            pageType: self.currentTableInfo.pageType,
            userIds: userIds.join(','),
            creator: sessionStorage.getItem('username') || 'admin',
            pushWorkno: sessionStorage.getItem('workno') || '',
            pushName: sessionStorage.getItem('fullname') || ''
        };

        var loadIndex = layer.load(2, { shade: 0.3 });

        var cb_success = function (res) {
            layer.close(loadIndex);

            if (res.success) {
                var summary = res.data.summary;
                var message = '推送完成！成功：' + summary.success + '个，失败：' + summary.failure + '个';

                if (summary.failure > 0) {
                    // 显示详细结果
                    self.showPushResults(res.data.pushResults);
                } else {
                    layer.msg(message, { icon: 1 });
                }

                // 刷新数据
                self.refreshData();
            } else {
                layer.msg('推送失败：' + res.msg, { icon: 2 });
            }
        };

        var cb_error = function () {
            layer.close(loadIndex);
            layer.msg('网络请求失败', { icon: 2 });
        };

        twxAjax('Thing.Util.SignPushTask', 'PushSignTask', param, true, cb_success, cb_error);
    },

    /**
     * 显示推送结果
     * @param {array} results 推送结果
     */
    showPushResults: function (results) {
        var html = '<div style="padding: 15px; max-height: 400px; overflow-y: auto;">';
        html += '<table class="layui-table">';
        html += '<thead><tr><th>用户</th><th>状态</th><th>消息</th></tr></thead>';
        html += '<tbody>';

        for (var i = 0; i < results.length; i++) {
            var result = results[i];
            var statusIcon = result.success ? '<i class="layui-icon layui-icon-ok" style="color: green;"></i>' : '<i class="layui-icon layui-icon-close" style="color: red;"></i>';
            html += '<tr>';
            html += '<td>' + (result.userFullname || result.userId) + '</td>';
            html += '<td>' + statusIcon + '</td>';
            html += '<td>' + result.message + '</td>';
            html += '</tr>';
        }

        html += '</tbody></table></div>';

        layer.open({
            title: '推送结果',
            type: 1,
            area: ['600px', '500px'],
            content: html
        });
    },

    /**
     * 刷新数据
     */
    refreshData: function () {
        var self = this;
        self.loadUnsignedUsers(self.currentTableInfo.tableId, self.currentTableInfo.pageType, function (success, data) {
            if (success) {
                self.unsignedUsers = data.unsignedUsers;
                self.initTable();
            }
        });
    },

    /**
     * 格式化未签署单元格显示
     * @param {array} cells 单元格数组
     */
    formatUnsignedCells: function (cells) {
        if (!cells || cells.length === 0) {
            return '';
        }

        var html = '<div class="unsigned-positions-wrapper">';
        for (var i = 0; i < cells.length; i++) {
            // {{ wanghq: Modify - 只显示未签署的单元格.  }}
            if (!cells[i].signed) {
                html += '<div class="unsigned-position">' + cells[i].position + '</div>';
            }
        }
        html += '</div>';

        return html;
    },

    /**
     * 格式化已签署单元格显示
     * @param {array} cells 单元格数组
     */
    formatSignedCells: function (cells) {
        if (!cells || cells.length === 0) {
            return '';
        }

        var html = '<div class="signed-positions-wrapper">';
        for (var i = 0; i < cells.length; i++) {
            // 只显示已签署的单元格
            if (cells[i].signed) {
                html += '<div class="signed-position">' + cells[i].position + '</div>';
            }
        }
        html += '</div>';

        return html;
    },

    /**
     * 获取页面类型显示名称
     * @param {string} pageType 页面类型
     */
    getPageTypeDisplay: function (pageType) {
        switch (pageType) {
            case 'report':
                return 'AIT质量确认';
            case 'launch':
                return '发射场确认';
            case 'confirm':
                return '产品质量确认';
            default:
                return pageType;
        }
    },

    /**
     * 调用CompleteSignTask服务完成签署任务
     * @param {object} userInfo 用户信息
     */
    callCompleteSignTask: function (userInfo) {
        var self = this;

        var param = {
            tableId: self.currentTableInfo.tableId,
            pageType: self.currentTableInfo.pageType,
            userWorkno: userInfo.userWorkno,
            triggerType: 'manual'
        };

        var loadIndex = layer.load(2, { shade: 0.3 });

        var cb_success = function (res) {
            layer.close(loadIndex);

            if (res.success) {
                layer.msg('重试BPM完成任务成功！', { icon: 1 });
                // 刷新数据
                self.refreshData();
            } else {
                layer.msg('重试失败：' + res.msg, { icon: 2 });
            }
        };

        var cb_error = function () {
            layer.close(loadIndex);
            layer.msg('网络请求失败', { icon: 2 });
        };

        twxAjax('Thing.Util.SignPushTask', 'CompleteSignTask', param, true, cb_success, cb_error);
    },

    /**
     * 清理资源
     */
    cleanup: function () {
        this.currentTableInfo = null;
        this.unsignedUsers = [];
        this.tableInstance = null;
    }
};
