/**
 * <AUTHOR>
 * @datetime 2025年6月25日15:13:06
 * @function	aitMappingSync
 * @description	AIT确认表映射同步功能
 */

// AIT映射同步相关全局变量 - ES5语法兼容
var AIT_MAPPING_THING = 'Thing.Fn.QualityReport';
var aitMappingFuncIdent = "aitMapping";

// 导出确认表列表
function exportConfirmTableList(treeNode) {
    var url = fileHandlerUrl + "/report/export/confirm/table/list?nodeName=" + treeNode.NAME + "&nodeId=" + treeNode.ID.split("T_")[1] + "&nodeType=" + treeNode.TYPE;
    $.fileDownload(url, {
        httpMethod: 'POST',
        prepareCallback: function (url) {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function (url) {
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function (url) {
            layer.close(loading);
        },
        failCallback: function (html, url) {
            layer.close(loading);
            layer.msg("导出失败！！");
        }
    });
}

// 显示AIT映射模板Layer弹窗 - ES5语法兼容
function showAitMappingLayer() {
    var fileFlag = false;
    layer.open({
        title: '确认表映射模板',
        type: 1,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        maxmin: false,
        resize: false,
        area: ['450px', '300px'],
        content: '<div id="uploadAitContent" style="padding-top: 15px;padding-right: 15px;"></div>',
        btn: ['确认', '取消'],
        yes: function () {
            if (!fileFlag) {
                layer.alert('请选择需要导入的excel文件!', {
                    icon: 2
                });
                return false;
            }
            uploadInst.config.url = fileHandlerUrl + '/report/import/ait/mapping?username=' +
                sessionStorage.getItem("username");
            $('#uploadStart').click();
        },
        btn2: function () {
            layer.closeAll();
        },
        success: function (layero, index, that) {
            var addTpl = '';
            addTpl = $("#uploadAitMappingHtml")[0].innerHTML;
            $("#uploadAitContent").append(addTpl);
        }
    });

    form.render(null, 'uploadAitMappingForm');

    // 下载模板按钮事件
    $("#downloadAitTpl").unbind('click').bind('click', function () {
        var loading;
        var url = fileHandlerUrl + "/report/export/ait/mapping";
        $.fileDownload(url, {
            httpMethod: 'POST',
            prepareCallback: function (url) {
                loading = layer.msg("正在下载...", {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });
            },
            abortCallback: function (url) {
                layer.close(loading);
                layer.msg("下载异常！！");
            },
            successCallback: function (url) {
                layer.close(loading);
            },
            failCallback: function (html, url) {
                layer.close(loading);
                layer.msg("下载失败！！");
            }
        });
    });

    // 文件上传配置
    var loadIndex;
    var uploadInst = upload.render({
        elem: '#uploadChoice',
        url: '',
        auto: false,
        accept: 'file',
        field: 'file',
        exts: 'xlsx',
        bindAction: '#uploadStart',
        dataType: "json",
        choose: function (obj) {
            fileFlag = true;
            var files = obj.pushFile();
            var thisFile = obj.getChooseFiles();
            var thisFileIndex;
            var filename = '';
            for (var k in thisFile) {
                thisFileIndex = k;
                filename = thisFile[k].name;
            }

            for (var k in files) {
                if (thisFileIndex != k) {
                    delete files[k];
                }
            }
            $("#selectedFile").show();
            $("#selectedFileName").text(filename);
        },
        before: function (obj) {
            loadIndex = layer.load();
        },
        done: function (res) {
            layer.close(loadIndex);
            if (res.success) {
                layer.closeAll();
                layer.msg('导入成功');
            } else {
                layer.alert(res.msg || '导入失败', {
                    icon: 2
                });
            }
        },
        error: function () {
            layer.close(loadIndex);
            layer.alert('导入失败');
        }
    });
}

// 同步AIT映射数据函数 - ES5语法兼容
function syncAitMappingData(node) {
    // 检查节点类型，只有阶段节点才能执行同步
    if (node.TYPE !== 'phase') {
        layer.alert('只能在阶段节点上执行同步操作！', {
            icon: 2
        });
        return;
    }

    layer.confirm('确定要同步数据吗？', {
        btn: ['确定', '取消']
    }, function () {
        var loading = layer.load(1, {
            shade: [0.1, '#fff']
        });

        // 调用后端同步接口
        twxAjax(AIT_MAPPING_THING, "SyncAitMappingData", {
            nodeId: node.ID.split("T_")[1],
            username: sessionStorage.getItem("username")
        }, true, function (res) {
            layer.close(loading);
            if (res.success) {
                layer.msg(res.msg);
                reloadTree(node.PID, node.ID);
                // 同步成功后显示结果反馈弹框
                showAitSyncResultLayer(res.data);
            } else {
                layer.alert(res.msg || '同步失败');
            }
        }, function () {
            layer.close(loading);
            layer.alert('同步失败');
        });
    });
}

// 终止AIT同步函数和恢复AIT同步函数已移除 - 功能简化

// 显示AIT同步结果反馈弹框 - ES5语法兼容 - 重构版本
function showAitSyncResultLayer(syncResultData) {
    if (!syncResultData || !syncResultData.syncResults) {
        layer.alert('同步结果数据为空', {
            icon: 2
        });
        return;
    }

    var resultList = syncResultData.syncResults || [];
    var syncCount = syncResultData.syncCount || 0;
    var totalCount = syncResultData.totalCount || 0;
    var failedCount = totalCount - syncCount;

    // 构建统计信息HTML
    var statisticsHtml = '<div style="padding: 15px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">' +
        '<div style="display: flex; gap: 20px;">' +
        '<span style="color: #28a745;"><strong>成功：' + syncCount + '条</strong></span>' +
        '<span style="color: #dc3545;"><strong>失败：' + failedCount + '条</strong></span>' +
        '<span style="color: #6c757d;"><strong>总计：' + totalCount + '条</strong></span>' +
        '</div>' +
        '</div>';

    // 使用layer.open创建弹框
    layer.open({
        type: 1,
        title: '同步结果详情',
        content: '<div id="aitSyncResultContent" style="padding: 0;">' +
            statisticsHtml +
            '<div><table id="aitSyncResultTable"></table></div>' +
            '</div>',
        area: ['1400px', '700px'],
        success: function () {
            // 渲染同步结果表格
            renderAitSyncResultTable(resultList);
        }
    });
}

// 渲染AIT同步结果表格 - ES5语法兼容
function renderAitSyncResultTable(resultList) {
    // 定义表格列配置
    var tableCols = [[
        { field: 'sourceProfessionalNode', title: '来源专业节点', width: 120, align: 'center' },
        { field: 'sourceProcessNode', title: '来源过程节点', width: 120, align: 'center' },
        { field: 'sourceConfirmTable', title: '来源确认表', width: 150, align: 'center' },
        { field: 'targetProfessionalNode', title: '目标专业节点', width: 120, align: 'center' },
        { field: 'targetProcessNode', title: '目标过程节点', width: 120, align: 'center' },
        { field: 'targetConfirmTable', title: '目标确认表', width: 150, align: 'center' },
        {
            field: 'syncStatus',
            title: '同步状态',
            width: 100,
            align: 'center',
            templet: function (d) {
                var statusClass = '';
                var statusIcon = '';
                var statusText = '';

                if (d.syncStatus === 'SUCCESS') {
                    statusClass = 'color: #28a745; font-weight: bold;';
                    statusIcon = '✓ ';
                    statusText = '成功';
                } else if (d.syncStatus === 'FAILED') {
                    statusClass = 'color: #dc3545; font-weight: bold;';
                    statusIcon = '✗ ';
                    statusText = '失败';
                } else {
                    statusClass = 'color: #6c757d; font-weight: bold;';
                    statusIcon = '? ';
                    statusText = '未知';
                }

                return '<span style="' + statusClass + '">' + statusIcon + statusText + '</span>';
            }
        },
        {
            field: 'errorMessage',
            title: '失败原因',
            align: 'left',
            templet: function (d) {
                if (d.errorMessage && d.errorMessage !== '') {
                    return '<span style="color: #dc3545;">' + d.errorMessage + '</span>';
                } else {
                    return '<span style="color: #6c757d;">-</span>';
                }
            }
        }
    ]];

    // 渲染表格
    table.render({
        elem: '#aitSyncResultTable',
        data: resultList,
        cols: tableCols,
        page: false,
        height: 596,
        even: true
    });
}
