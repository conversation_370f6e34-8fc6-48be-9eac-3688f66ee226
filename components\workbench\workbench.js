//加载四个类别的采集数量
function loadCount() {
	twxAjax(thingName, 'QueryTypeCount', '', true, function(data) {
		for (var i = 0; i < data.array.length; i++) {
			$("#" + data.array[i].TYPE).html(data.array[i].COUNT);
		}
	}, function(e) {
		layer.alert("加载数量失败！");
	});
}

layui.use(['layer', 'element', 'table'], function() {
	var pie = new PieChart();
	var line = new LineChart();
	var bar1 = new BarChart1();
	var bar2 = new BarChart2();
	var bar3 = new BarChart3();
	var element = layui.element;
	element.render();
	loadCount();
	var table = layui.table;
	var tableH = 480;
	//加载我的待办表格
	window.myTable = table.render({
		elem: '#my-table',
		url: getUrl("Thing.Fn.Interface", 'QueryToDoTaskList', "&username=" + sessionStorage.getItem(
			'username')),
		cellMinWidth: 80,
		height: tableH,
		page: page,
		cols: my_table_col
	});

	//加载已完成任务表格
	window.myTable = table.render({
		elem: '#finished-table',
		url: getUrl("Thing.Fn.Interface", 'QueryFinishedTaskList', "&username=" + sessionStorage.getItem(
			'username')),
		cellMinWidth: 80,
		height: tableH,
		page: page,
		cols: my_table_col
	});

	function reloadTable() {
		table.reload('finished-table', {
			page: {
				curr: 1
			}
		});
		table.reload('my-table', {
			page: {
				curr: 1
			}
		});
	}

	function rowClick(obj, type) {
		var data = obj.data;
		if (obj.event === 'viewDetail') {
			var taskType = data.TASK_TYPE;
			if ('策划任务' == taskType) {
				var taskId = data.TASK_ID;
				var header = data.PROC_INST_NAME + "-" + data.TASK_NAME + ":" + data.PROC_INST_ID;
				layer.open({
					type: 2,
					title: [header, 'font-size: 20px;'],
					shadeClose: true,
					maxmin: true,
					area: ['1500px', '800px'],
					content: '../../components/taskInfo/planTask/planTask.html?type=' + type + '&taskId=' + taskId,
					success: function(layero, index) {
						layer.full(index);
					}
				});
			} else {
				var taskId = data.TASK_ID;
				var header = data.PROC_INST_NAME + "-" + data.TASK_NAME + ":" + data.PROC_INST_ID;
				layer.open({
					type: 2,
					title: [header, 'font-size: 20px;'],
					shadeClose: true,
					maxmin: true,
					area: ['1500px', '800px'],
					content: '../../components/taskInfo/taskInfo.html?taskId=' + taskId,
					success: function(layero, index) {
						$($(layero).find('.layui-layer-btn').children('a')[0]).removeClass(
							'layui-layer-btn0').addClass('layui-layer-btn1');
						$($(layero).find('.layui-layer-btn').children('a')[1]).removeClass(
							'layui-layer-btn1').addClass('layui-layer-btn0');
						layer.full(index);
					},
					btn: ['驳回', '完成任务'],
					btn1: function(index, layero, that) {
						layer.prompt({
							title: '请输入驳回原因',
							formType: 2
						}, function(value, index1, elem) {
							if (value === '') {
								return elem.focus();
							} else {
								var loadIndex = layer.msg('提交中', {
									icon: 16,
									shade: 0.01,
									time: 0
								});
								twxAjax("Thing.Fn.Interface", 'RejectTask', {
									taskId: taskId,
									optUser: sessionStorage.getItem(
										"username"),
									rejectReason: value
								}, true, function(res) {
									layer.close(loadIndex);
									if (res.success) {
										layer.close(index1);
										layer.msg(res.msg);
										layer.close(index);
									} else {
										layer.alert(res.msg, {
											icon: 2
										});
									}
								}, function(e) {
									layer.alert("提交失败！");
								});
							}
						});
						return false;
					},
					btn2: function(index, layero, that) {
						var loadIndex = layer.msg('提交中', {
							icon: 16,
							shade: 0.01,
							time: 0
						});
						twxAjax("Thing.Fn.Interface", 'SubmitTask', {
							taskId: taskId,
							optUser: sessionStorage.getItem("username")
						}, true, function(res) {
							layer.close(loadIndex);
							if (res.success) {
								layer.msg(res.msg);
								layer.close(index);
							} else {
								layer.alert(res.msg, {
									icon: 2
								});
							}
						}, function(e) {
							layer.alert("提交失败！");
						});
						return false;
					}
				});
			}
		} else if (obj.event === 'finishedTask') {
			twxAjax("Thing.Fn.Interface", 'FinishedTask', {
				taskId: data.TASK_ID
			}, true, function(res) {
				if (res.success) {
					layer.msg(res.msg);
					reloadTable();
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			});
		} else if (obj.event === 'returnTask') {
			twxAjax("Thing.Fn.Interface", 'ReturnTask', {
				taskId: data.TASK_ID
			}, true, function(res) {
				if (res.success) {
					layer.msg(res.msg);
					reloadTable();
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			});
		}
	}

	table.on('tool(my-table)', function(obj) {
		rowClick(obj, 'toDo');
	});

	table.on('tool(finished-table)', function(obj) {
		rowClick(obj, 'finished');
	});

	//加载我的数据包表格
	table.render({
		elem: '#my-pkg-table',
		url: getUrl(thingName, 'QueryMyPkgTable'),
		cellMinWidth: 80,
		height: tableH,
		page: page,
		cols: my_pkg_table_col
	});

	//加载pdm采集信息表格
	window.pdmTable = table.render({
		elem: '#pdm-table',
		id: "pdm-table",
		url: getUrl(thingName, 'QueryPdmTable', "&username=" + sessionStorage.getItem('username')),
		cellMinWidth: 80,
		height: tableH,
		page: page,
		cols: pdm_table_col
	});

	$("#reload_pdm").click(function() {
		table.reload('pdm-table', {
			page: {
				curr: 1 //重新从第 1 页开始
			},
			where: {
				productName: $("#search_product").val(),
				name: $("#search_name").val(),
				type: $("#search_type").val()
			}
		});
	});

	$("#batch_process").click(function() {
		var checkStatus = table.checkStatus('pdm-table');
		if (checkStatus.data.length == 0) {
			layer.alert('请先选择数据！', {
				icon: 2
			});
		} else {
			var pdmDatas = [];
			for (var i = 0; i < checkStatus.data.length; i++) {
				var pdmData = checkStatus.data[i];
				if (pdmData.IS_PROCESS != '1') {
					pdmDatas.push({
						id: pdmData['ID'],
						type: pdmData['PDM_TYPE']
					});
				}
			}
			relationProcess(pdmDatas);
		}
	});

	$("#batch_product").click(function() {
		var checkStatus = table.checkStatus('pdm-table');
		if (checkStatus.data.length == 0) {
			layer.alert('请先选择数据！', {
				icon: 2
			});
		} else {
			var pdmDatas = [];
			for (var i = 0; i < checkStatus.data.length; i++) {
				var pdmData = checkStatus.data[i];
				if (pdmData.IS_PRODUCT != '1') {
					pdmDatas.push({
						id: pdmData['ID'],
						type: pdmData['PDM_TYPE'],
						isProcess: pdmData.IS_PROCESS
					});
				}
			}
			relationProduct(pdmDatas);
		}
	});
});