function uploadClick(tableName, layui) {
	var layer = layui.layer,
		form = layui.form,
		upload = layui.upload,
		table = layui.table,
		layuiTableColumnSelect = layui.layuiTableColumnSelect;
	var domHtml = document.getElementById('many-upload').innerHTML;
	layer.open({
		title: '手动上传',
		type: 1,
		area: ['1100px', '560px'],
		content: domHtml,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		resize: false,
		btn: ['上传', '关闭'],
		yes: function () {
			$("#uploadStart").click();
		},
		btn2: function () {
			table.cache["file-table"] = [];
			return true;
		},
		success: function () {
			var tName = '';
			if (tableName === 'design_list_table') {
				tName = 'DESIGN_DATA_list';
			} else if (tableName === 'craft_list_table') {
				tName = 'CRAFT_DATA_list';
			} else if (tableName === 'processcontrol_list_table') {
				tName = 'PROCESS_CONTROL_list';
			} else if (tableName === 'quanlitycontrol_list_table') {
				tName = 'QUALITY_CONTROL_list';
			}
			var sels = $('#datapkgref').datagrid('getSelections');
			var fileTypes = [];
			$("#fileType").empty();
			$("#fileType").append('<option value=""></option>')
			twxAjax("Thing.Fn.DataCollect", "GetUploadFileType", {
				tablename: tName,
				datapkgid: sels[0].ID
			}, false, function (data) {
				for (var i = 0; i < data.rows.length; i++) {
					fileTypes.push({
						name: data.rows[i].FILE_TYPE,
						value: data.rows[i].FILE_TYPE
					});
					$("#fileType").append('<option value="' + data.rows[i].FILE_TYPE + '">' + data
						.rows[i].FILE_TYPE + '</option>')
				}
			});
			form.render("select");

			form.on('select(fileType)', function (data) {

			});

			var fileTable = table.render({
				elem: '#file-table',
				data: [],
				height: 375,
				limit: 999,
				cols: [
					[{
						field: '',
						title: '序号',
						type: 'numbers',
						width: 80
					}, {
						field: 'fileName',
						title: '文档名称',
						templet: '<div>{{d.fileName}}</div>',
						edit: 'text'
					}, {
						field: 'fileSize',
						title: '文件大小',
						templet: '<div>{{d.fileSize}}</div>',
						width: 100
					}, {
						field: 'fileType',
						title: '文件类别',
						event: 'selectFileType',
						templet: '<div>{{d.fileType}}</div>',
						width: 120
					}, {
						field: 'fileTypeValue',
						hide: true
					}, {
						field: 'securityLevel',
						title: '密级',
						// event: 'selectSecurityLevel',
						templet: '<div>{{d.securityLevel}}</div>',
						width: 80
					}, {
						field: 'securityLevelValue',
						hide: true
					}, {
						field: 'fileNumber',
						title: '文档编号',
						hide: true
					},
					{
						field: 'fileFormat',
						title: '文件形式',
						templet: '<div>{{d.fileFormat}}</div>',
						width: 100
					}, {
						field: 'operate',
						title: '操作(双击)',
						width: 100,
						templet: '<div class="opetate">\
												<a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>\
											</div>'
					}
					]
				],
				done: function (res, curr, count) {

				}
			});
			table.on('toolDouble(file-table)', function (obj) {
				var data = obj.data; // 得到当前行数据
				var layEvent = obj.event; // 获得元素对应的 lay-event 属性值
				if (layEvent === 'del') { //删除
					layer.confirm('确定删除吗？', function (index) {
						obj.del();
						uploader.removeFile(data.file);
						layer.close(index);
					});
				}
			});
			layuiTableColumnSelect.addSelect({
				data: fileTypes,
				layFilter: 'file-table',
				event: 'selectFileType'
			});

			var securityLevels = [];
			twxAjax("Thing.Fn.SystemDic", "GetSecDataBySecLevel", {
				secLevel: userSecLevel
			}, false, function (data) {
				for (var i = 0; i < data.rows.length; i++) {
					securityLevels.push({
						name: data.rows[i].NAME,
						value: data.rows[i].KEY
					});
				}
				layuiTableColumnSelect.addSelect({
					data: securityLevels,
					layFilter: 'file-table',
					event: 'selectSecurityLevel'
				});
			});

			var doneDatas = [];
			var uploadErrors = [];
			var totalFiles = 0;
			var completedFiles = 0;
			var reqIdent = new Date().getTime();
			var uploader = WebUploader.create({
				// 选完文件后，是否自动上传。
				auto: false,
				// 文件接收服务端。
				server: fileHandlerUrl + "/file/upload/big/file",
				// 选择文件的按钮。可选。
				pick: {
					id: '#chooseFile',
					multiple: true
				},
				timeout: 10 * 60 * 1000,
				threads: 5,
				// 配置分片上传
				chunked: true,
				chunkSize: 20 * 1024 * 1024,
				formData: {
					reqIdent: reqIdent,
					extraData: JSON.stringify({})
				}
			});
			uploader.on('uploadBeforeSend', function (object, data, headers) {

			});



			// 当有文件被添加进队列之前触发
			uploader.on('beforeFileQueued', function (file) {
				var fileName = file.name;
				var flag = false;
				for (var x = 0; x < securityLevels.length; x++) {
					var security = securityLevels[x];
					var name = "（" + security.name + "）";
					var name1 = "(" + security.name + ")";
					if (fileName.lastIndexOf(name) > -1 || fileName.lastIndexOf(name1) > -1) {
						flag = true;
						file.security = security
					}
				}
				if (!flag) {
					layer.msg('已经过滤掉不符合自身密级的文件！');
				}
				return flag;
			});
			// 当有文件被添加进队列的时候
			uploader.on('fileQueued', function (file) {
				totalFiles++; // 增加总文件计数
				var tableData = table.cache["file-table"];
				var fileName = file.name;
				var o = {};
				o.fileType = $("#fileType").val();
				o.fileTypeValue = $("#fileType").val();
				o.securityLevel = file.security.name;
				o.securityLevelValue = file.security.value;
				o.fileNumber = '';
				o.fileName = fileName.substr(0, fileName.lastIndexOf('.'));
				o.fileFormat = file.ext;
				o.fileSize = WebUploader.formatSize(file.size);
				o.index = file.source.uid;
				o.file = file;
				table.cache["file-table"].push(o);
				table.renderData('file-table');
			});
			uploader.on('filesQueued', function (files) {

			});
			uploader.on('uploadProgress', function (file, percentage) {
				// 上传进度处理（如需要可在此添加进度显示逻辑）
			})


			uploader.on('uploadSuccess', function (file, res) {
				completedFiles++; // 增加完成文件计数

				// 检查后端返回的success字段
				if (!res || res.success !== true) {
					// 后端返回失败，记录到错误列表
					var errorReason = '服务器返回失败';
					if (res && res.msg) {
						errorReason = res.msg;
					} else if (!res) {
						errorReason = '服务器无响应';
					}

					uploadErrors.push({
						file: file,
						fileName: file.name || '未知文件',
						reason: errorReason
					});
					return;
				}

				var rowData = {};
				var datas = table.cache["file-table"];
				for (var i = 0; i < datas.length; i++) {
					if (datas[i].index == file.source.uid) {
						rowData = datas[i];
						break;
					}
				}
				var param = {};
				param.FILE_TYPE = rowData.fileType;
				param.FILE_NUMBER = rowData.fileNumber;
				param.FILE_NAME = rowData.fileName;
				param.FILE_FORMAT = rowData.fileFormat;
				param.SECURITY_LEVEL = rowData.securityLevelValue;
				param.securityLevel = rowData.securityLevel;
				param.fileIndex = file.id;
				var type = "";
				var typeName = '';
				if (tableName === 'design_list_table') {
					type = 'DESIGN_DATA_RESULT';
					typeName = '设计类';
				} else if (tableName === 'craft_list_table') {
					type = 'CRAFT_DATA_RESULT';
					typeName = '工艺类';
				} else if (tableName === 'processcontrol_list_table') {
					type = 'PROCESS_CONTROL_RESULT';
					typeName = '过程控制';
				} else if (tableName === 'quanlitycontrol_list_table') {
					type = 'QUALITY_CONTROL_RESULT';
					typeName = '质量综合';
				}
				param.type = type;
				var datapkg = $('#datapkgref').datagrid('getSelections')[0];
				param.NODECODE = datapkg.ID;
				param.NODENAME = datapkg.ID;
				param.typeName = typeName;
				param.datapkg = datapkg;
				param.CREATOR = sessionStorage.getItem('username');
				param.STATE_CHECK = "未确认";
				param.DELIVERY_STATE = "提交";
				param.GATHERING_METHOD = "手动采集";
				param.SOURCE_SYSTEM = "/";
				param.FILEPATH = res.filePath;
				doneDatas.push(param);
			});

			// 文件上传失败，显示上传出错。
			uploader.on('uploadError', function (file, reason) {
				completedFiles++; // 增加完成文件计数
				uploadErrors.push({
					file: file,
					fileName: file.name,
					reason: reason || '上传失败'
				});
			});

			// 完成上传完毕，成功或者失败，先删除进度条。
			uploader.on('uploadComplete', function (file) {

			});

			// 当所有文件上传结束时触发
			uploader.on('uploadFinished', function () {
				// 检查是否所有文件都处理完毕
				if (completedFiles !== totalFiles) {
					layer.msg('文件上传未完成，请稍后重试', {icon: 2});
					return;
				}

				// 检查是否有错误
				if (uploadErrors.length > 0) {
					var successCount = doneDatas.length;
					var failCount = uploadErrors.length;
					var errorMsg = '上传完成，成功' + successCount + '个，失败' + failCount + '个\n\n失败文件：\n';

					for (var i = 0; i < uploadErrors.length; i++) {
						errorMsg += '• ' + uploadErrors[i].fileName;
						if (uploadErrors[i].reason) {
							errorMsg += '（' + uploadErrors[i].reason + '）';
						}
						errorMsg += '\n';
					}

					layer.alert(errorMsg, {icon: 2, title: '上传结果'});
					return;
				}

				// 检查是否有成功的文件
				if (doneDatas.length === 0) {
					layer.msg('没有文件上传成功', {icon: 2});
					return;
				}

				// 只有在没有错误且有成功文件时才继续处理
				doneDatas.sort(function (x, y) {
					return Number(x.fileIndex.split('_')[2]) - Number(y.fileIndex.split(
						'_')[2]);
				});
				for (var i = 0; i < doneDatas.length; i++) {
					var param = doneDatas[i];
					var cb_success = function (data) {
						logRecord('上传', '采集管理-数据包(ID：' + param.datapkg.ID + '、名称：' + param
							.datapkg.NAME + '、编号：' + param.datapkg.CODE +
							')下手动上传(类型：' +
							param.typeName + '、文档名称：' + param.FILE_NAME + '、文件类别：' +
							param
								.FILE_TYPE + '、密级：' + param.securityLevel +
							'、文档编号：' + param.FILE_NUMBER + '、文件形式：' + param
								.FILE_FORMAT +
							')', 1);
					};

					var cb_error = function (xhr) {
						logRecord('上传', '采集管理-数据包(ID：' + param.datapkg.ID + '、名称：' + param
							.datapkg.NAME + '、编号：' + param.datapkg.CODE +
							')下手动上传(类型：' +
							param.typeName + '、文档名称：' + param.FILE_NAME + '、文件类别：' +
							param
								.FILE_TYPE + '、密级：' + param.securityLevel +
							'、文档编号：' + param.FILE_NUMBER + '、文件形式：' + param
								.FILE_FORMAT +
							')', 0);
					};

					//同步新增
					twxAjax("publishMissionThing", "AddDataToCaptureManagementTable", param,
						false,
						cb_success, cb_error);
				}

				//提示完成后，点击确定再刷新界面
				layer.closeAll();
				table.cache["file-table"] = [];
				doneDatas = [];
				reloadTable(tableName);
				layer.msg('上传成功');
			});

			$("#uploadStart").on('click', function () {
				// 重置状态变量
				uploadErrors = [];
				completedFiles = 0;
				layer.load();
				uploader.upload(); // 手动触发上传操作
			});
		}
	})

}