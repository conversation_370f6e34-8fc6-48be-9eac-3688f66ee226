/**
 * HotUtil.Download.js - Handsontable工具类 (下载管理模块)
 *
 * 负责文件下载列表相关功能。
 */

/**
 * 弹出文件下载列表
 * @param {Object} o 配置对象
 */
HotUtil.ejectDownloadTable = function (o) {
    layer.open({
        title: "文件下载列表",
        type: 1,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        maxmin: true,
        resize: false, //不允许拉伸
        area: ['' + o.tableWidth + 'px', '700px'],
        scrollbar: false,
        content: o.seachFormHtml + '<div id="download-table"></div>',
        success: function () {
            renderSearchForm();
            HotUtil.renderDownloadTable(o.cols, o.tableHeight);
        }
    });
};

/**
 * 渲染下载表格
 * @param {Array} cols
 * @param {number} tableHeight
 */
HotUtil.renderDownloadTable = function (cols, tableHeight) {
    // 创建渲染实例
    table.render({
        elem: '#download-table',
        id: 'download-table',
        url: getUrl(THING, 'QueryDownloadTable'),
        where: {
            creator: sessionStorage.getItem("username")
        },
        height: tableHeight, // 最大高度减去其他容器已占有的高度差
        cellMinWidth: 80,
        page: {
            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        },
        cols: [
            [{
                title: '序号',
                type: "numbers",
                width: 60
            },
            {
                field: 'DWONLOAD_ID',
                hide: true
            },
            {
                field: 'FOLDER',
                width: 102,
                title: '分类'
            },
            {
                field: 'MODEL',
                title: '型号',
                width: 85
            }
            ].concat(cols).concat([{
                field: 'A_TABLE',
                width: 300,
                title: 'A表',
                templet: function (d) {
                    return d.A_TABLE == '：' ? "" : d.A_TABLE;
                },
            },
            {
                field: 'EXPORT_TYPE',
                title: '文件类型',
                width: 85,
                templet: function (d) {
                    var html = "";
                    if (d.EXPORT_TYPE == 1) {
                        html = '<span class="layui-badge layui-bg-blue">Pdf</span>';
                    } else if (d.EXPORT_TYPE == 2) {
                        html =
                            '<span class="layui-badge layui-bg-green">Pdf压缩包</span>';
                    } else if (d.EXPORT_TYPE == 3) {
                        html =
                            '<span class="layui-badge layui-bg-red">Excel压缩包</span>';
                    } else if (d.EXPORT_TYPE == 4) {
                        html = '<span class="layui-badge layui-bg-red">数据包</span>';
                    }
                    return html;
                },
                align: 'center'
            },
            {
                field: 'START_TIME',
                width: 152,
                title: '提交时间',
                align: 'center'
            },
            {
                field: 'FILE_SIZE',
                width: 100,
                title: '文件大小',
                align: 'center',
                templet: function (d) {
                    var html = "";
                    if (d.FILE_SIZE) {
                        if (d.FILE_SIZE != 'undefined' && d.FILE_SIZE != '') {
                            html = d.FILE_SIZE;
                        }
                    }
                    return html;
                }
            },
            {
                field: 'IS_COMPLETE',
                title: '是否完成',
                width: 90,
                minWidth: 90,
                templet: function (d) {
                    var html = "";
                    if (d.IS_COMPLETE == 0) {
                        html = '<span class="layui-badge layui-bg-blue">进行中</span>';
                    } else if (d.IS_COMPLETE == 1) {
                        html =
                            '<span class="layui-badge layui-bg-green">已完成</span>';
                    } else if (d.IS_COMPLETE == 2) {
                        html =
                            '<span class="layui-badge layui-bg-red show-msg" title="点击查看原因" msg="' +
                            d.MSG + '">生成失败</span>';
                    }
                    return html;
                },
                align: 'center'
            },
            {
                field: 'END_TIME',
                title: '完成时间',
                width: 152,
                align: 'center'
            },
            {
                field: 'IS_DOWNLOAD',
                title: '是否下载',
                width: 85,
                templet: function (d) {
                    var html = "";
                    if (d.IS_DOWNLOAD == 0) {
                        html = '<span class="layui-badge layui-bg-blue">未下载</span>';
                    } else if (d.IS_DOWNLOAD == 1) {
                        html =
                            '<span class="layui-badge layui-bg-green">已下载</span>';
                    }
                    return html;
                },
                align: 'center'
            },
            {
                fixed: 'right',
                title: '操作',
                width: 68,
                minWidth: 68,
                toolbar: `<div class="layui-clear-space">
                                <a class="layui-btn layui-btn-xs" lay-event="download">下载</a>
                            </div>`,
                align: 'left'
            }
            ])
        ],
        done: function () {
            $(".show-msg").off('click').on('click', function () {
                layer.alert($(this).attr("msg"));
            });
        },
        error: function (res, msg) {
            console.log(res, msg)
        }
    });

    // 工具栏事件
    table.on('tool(download-table)', function (obj) {
        var data = obj.data; // 获得当前行数据
        if (obj.event === 'download') {
            if (data.FILE_PATH) {
                var filePath = "//" + data.FILE_PATH;
                filePath = filePath.replace(/\\/g, "/");
                var fileName = data.FILE_NAME;
                var export_type = data.EXPORT_TYPE;
                if (export_type == 4) {
                    fileName = new Date().getTime() + ".dat";
                }

                twxAjax('Thing.Util.HandsonTable', 'RecordDownloadFile', {
                    downloadId: data.ID
                }, true, function (res) {
                    downloadFile(filePath, fileName);
                    table.reload('download-table');
                }, function () { });
            } else {
                layer.alert("文件还未生成，请稍后再试！", {
                    icon: 2
                });
            }
        } else if (obj.event === 'delete') {
            if (data.IS_COMPLETE == 0) {

            }
        }
    });
};