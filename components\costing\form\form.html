<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>表单配置</title>
		<link href="../../../plugins/layui-lasted/css/layui.css" rel="stylesheet" />
		<link href="../../../plugins/handson/handsontable.full.min.css" rel="stylesheet" media="screen">
		<link href="../../../css/HotStyle.css" rel="stylesheet" />
		<link href="../css/common.css" rel="stylesheet" />
		<link rel="stylesheet" href="form.css" />

		<script src="../../../plugins/common/jquery-3.7.0.min.js"></script>
		<script src="../../../plugins/handson/handsontable.full.min.js"></script>
		<script src="../../../plugins/handson/languages/zh-CN.min.js"></script>
		<script src="../../../plugins/layui-lasted/layui.js"></script>
		<script src="../../../plugins/index/jquery.fileDownload.js"></script>


		<script src="../../js/config/twxconfig.js"></script>
		<script src="../../js/util.js"></script>
		<script src="../../js/HandsonTableUtil.js"></script>
	</head>
	<body>
		<form class="layui-form search-form" lay-filter="form-table-form">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">表单名称</label>
					<div class="layui-input-inline">
						<input type="text" name="formName" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<button class="layui-btn layui-btn-sm" lay-submit lay-filter="form-table-search">搜索</button>
					<button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
				</div>
			</div>
		</form>
		<table class="layui-hide" id="form-table" lay-filter="form-table"></table>
	</body>

	<script type="text/html" id="form-toolbar">
		<div class="layui-btn-container">
			<button class="layui-btn layui-btn-sm" lay-event="add-btn">新增</button>
		</div>
	</script>
	<script type="text/html" id="form-rowbar">
		<div class="layui-clear-space">
			<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
			<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="download">下载</a>
			<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
		</div>
	</script>

	<script src="../js/common.js"></script>
	<script src="handson.js"></script>
	<script src="form.js"></script>
</html>