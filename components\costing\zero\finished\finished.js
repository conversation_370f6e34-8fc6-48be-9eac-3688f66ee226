/**
 * 加载我的待办表格搜索表单
 */
function renderMyFinishedSearchForm() {
	form.render(null, 'finished-table-form');
	laydate.render({
		elem: '#finished-range-date',
		range: ['#finished-start-date', '#finished-end-date'],
		rangeLinked: true // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
	});

	// 搜索提交
	form.on('submit(finished-table-search)', function(data) {
		var field = data.field; // 获得表单字段
		field.username = currentUser;
		// 执行搜索重载
		table.reload('finished-table', {
			page: {
				curr: 1 // 重新从第 1 页开始
			},
			where: field // 搜索的字段
		});
		return false;
	});
}

/**
 * 加载我的已办列表
 */
function renderMyFinished() {
	table.render({
		elem: '#finished-table',
		id: 'finished-table',
		url: getUrl(THING, 'QueryFinishedTask'),
		where: {
			username: currentUser
		},
		toolbar: '#finished-toolbar',
		defaultToolbar: ['filter', {
			title: '刷新',
			layEvent: 'finished_reload',
			icon: 'layui-icon-refresh'
		}],
		height: 'full-90', // 最大高度减去其他容器已占有的高度差
		cellMinWidth: 80,
		page: true,
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'ID_',
					width: 100,
					title: '任务编号'
				},
				{
					field: 'NAME_',
					title: '任务名称',
					width: 150
				},
				{
					field: 'DEF_NAME_',
					width: 150,
					title: '所属流程'
				},
				{
					field: 'DEF_VERSION_',
					width: 90,
					title: '流程版本',
					templet: formatVersion,
					align: 'center'
				},
				{
					field: 'START_USER_ID_N_',
					width: 115,
					title: '流程发起人'
				},
				{
					field: 'START_TIME_',
					title: '接收时间',
					width: 160,
					templet: formatTableDate,
					align: 'center'
				},
				{
					field: 'END_TIME_',
					title: '结束时间',
					width: 160,
					templet: formatTableDate,
					align: 'center'
				},
				{
					field: 'DURATION_',
					title: '耗时',
					width: 160,
					templet: formatTableDuration
				},
				{
					fixed: 'right',
					title: '操作',
					width: 180,
					minWidth: 180,
					toolbar: '#finished-rowbar'
				}
			]
		],
		done: function() {

		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});
}

function initMyFinishedTableTool() {
	// 触发单元格工具事件
	table.on('tool(finished-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'record') {
			//办理
			layer.tab({
				area: ['1040px', '750px'],
				tab: [{
					title: '表单信息',
					content: '<div class="form-content" id="finished-form-content"></div>',
				}, {
					title: '流转记录',
					content: '<div class="record-content" id="finished-record-content"></div>'
				}, {
					title: '流程图',
					content: '<div class="chat-content" id="finished-chart-content"></div>'
				}],
				success: function(layero) {
					viewForm(data, "finished-form-content");
					viewTransferRecords(data, "finished-record-content");
				},
				change: function(index, layero) { //监听tab切换
					if (index == 2) {
						viewFlowChart(data, "finished-chart-content");
					}
				}
			});
		} else if (obj.event === 'revoke') {
			layer.confirm('确定撤回吗？', {
				icon: 3
			}, function() {
				var loadIndex = layer.msg('正在提交中......', {
					icon: 16,
					shade: 0.01,
					time: 0
				});

				twxAjax(THING, 'postFlw', {
					servlet: '/zero',
					params: {
						act: 'revokeTask',
						taskId: data['ID_'],
						procInsId: data['PROC_INST_ID_']
					},
				}, true, function(res) {
					if (res.success) {
						table.reloadData('finished-table');
						layer.msg(res.msg)
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}, function(xhr, textStatus, errorThrown) {
					layer.alert('请求出错！', {
						icon: 2
					});
				});
			})

		}
	});

	// 工具栏事件
	table.on('toolbar(finished-table)', function(obj) {
		var id = obj.config.id;
		var checkStatus = table.checkStatus(id);
		var othis = lay(this);
		switch (obj.event) {
			case 'finished_reload':
				table.reloadData('finished-table');
				break;
		};
	});
}