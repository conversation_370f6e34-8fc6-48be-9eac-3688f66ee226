var roleThingName = 'Thing.Fn.SystemManagement';

//初始化成员表格
var initUserComp = function() {
	$('#userTable').datagrid({
		data: [],
		singleSelect: true,
		// fitColumns:true,
		striped: true,
		rownumbers: true,
		toolbar: [{
			iconCls: 'icon-myadd',
			text: '添加',
			handler: function() {
				var sels = $('#roleTable').datagrid('getSelections');
				if (sels.length == 0) {
					layui.use('layer', function() {
						var layer = layui.layer;
						layer.msg('请选择角色...', {
							icon: 2,
							anim: 6
						});
					});
					return;
				}
				$('#addUser').dialog('open');
			}
		}, {
			iconCls: 'icon-myremove',
			text: '删除',
			handler: function() {

			}
		}],
		fit: true,
		columns: [
			[{
				field: 'USER_ID',
				title: '用户ID',
				width: 100,
				hidden: true
			}, {
				field: 'USER_NAME',
				title: '用户名称',
				width: 150
			}, {
				field: 'USER_FULLNAME',
				title: '全名',
				width: 150
			}, {
				field: 'USER_SEX',
				title: '性别',
				width: 80
			}, {
				field: 'USER_PHONE',
				title: '电话',
				width: 130
			}, {
				field: 'USER_EMAIL',
				title: '邮箱',
				width: 150
			}]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...'
	});
};

//初始化添加成员的窗口及人员表格
var initAddUserTableComp = function() {

};

//初始化角色表格
var initRoleComp = function(layui) {
	$('#roleTable').datagrid({
		data: [],
		singleSelect: true,
		fitColumns: true,
		striped: true,
		rownumbers: true,
		toolbar: '#roleTable_tb',
		fit: true,
		columns: [
			[{
				field: 'ROLE_NAME',
				title: '角色名称',
				width: 250
			}, {
				field: 'ROLE_DESC',
				title: '角色描述',
				width: 500
			}, {
				field: 'ROLE_ID',
				title: '角色ID',
				hidden: true
			}]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...',
		onDblClickRow: function(rowIndex, rowData) {
			$('#roleedit').click();
		},
		onSelect: function(rowIndex, rowData) {
			initAssignMenu();
			if (!rowData.MENUIDS || rowData.MENUIDS === '') {
				$('#noData').show();
				$('#selectedMenuTree').hide();
			} else {
				$('#noData').hide();
				if (rowData.ROLE_NAME == '安全管理员' || rowData.ROLE_NAME == '系统管理员' || rowData.ROLE_NAME == '审计管理员') {
					$("#root_layout_sub2").layout('panel', 'north').hide();
					$('#selectedMenuTree').hide();
				} else {
					$("#root_layout_sub2").layout('panel', 'north').show();
					$('#selectedMenuTree').show();
				}
			}
			initFuncs(layui);
		}
	});
};

//初始化表格数据
var initRoleData = function(index) {
	$('#roleTable').datagrid('loading');
	var cb_success = function(data) {
		$('#roleTable').datagrid('loadData', data.rows);
		$('#roleTable').datagrid('loaded');

		if (index || index === 0) {
			$('#roleTable').datagrid('selectRow', index);
		}
	};
	var cb_error = function() {};
	twxAjax(roleThingName, 'getAllRole', {}, true, cb_success, cb_error);
};
//初始化新增、编辑、删除按钮
var getSelectedData = function() {
	var sels = $('#roleTable').datagrid('getSelections');
	return sels;
};

var contentHtml = '';
var initBtnAdd = function(layui) {
	$('#roleadd').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		layer.open({
			title: '新增角色',
			type: 1,
			area: ['400px', '300px'],
			content: contentHtml,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			btn: ['新增', '重置', '关闭'],
			yes: function() {
				$('#btn_submit').click();
			},
			btn2: function() {
				$('#btn_reset').click();
				return false;
			},
			btn3: function() {
				return true;
			}
		});
	});
};

var initBtnEdit = function(layui) {
	$('#roleedit').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		var sels = getSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择数据...', {
				icon: 2,
				anim: 6
			});
			return;
		}
		layer.open({
			title: '更新角色',
			type: 1,
			area: ['400px', '300px'],
			content: contentHtml,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			btn: ['保存', '重置', '关闭'],
			yes: function() {
				$('#btn_update').click();
			},
			btn2: function() {
				$('#btn_reset').click();
				var param = {};
				param.role_name = sels[0].ROLE_NAME;
				param.role_desc = sels[0].ROLE_DESC;
				param.role_id = sels[0].ROLE_ID;

				form.val('roleinfo', param);
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function(layero, index) {
				var param = {};
				param.role_name = sels[0].ROLE_NAME;
				param.role_desc = sels[0].ROLE_DESC;
				param.role_id = sels[0].ROLE_ID;

				form.val('roleinfo', param);
				$("#roleName").attr('oldname', param.role_name);
				$("#roleDesc").attr('olddesc', param.role_desc);
			}
		});
	});
};

var initBtnDel = function(layui) {
	$('#roledel').bind('click', function() {
		var layer = layui.layer;
		var sels = getSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择需要删除的数据...', {
				icon: 2,
				anim: 6
			});
			return;
		}
		layer.confirm('确认删除数据吗?', {
			icon: 3,
			title: '提示'
		}, function(index) {
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					logRecord('删除', '角色管理-删除角色(ID：' + sels[0].ROLE_ID + '、名称：' + sels[0].ROLE_NAME + ')', 0);
					return;
				}
				//刷新用户表格
				initRoleData();
				layer.closeAll();
				logRecord('删除', '角色管理-删除角色(ID：' + sels[0].ROLE_ID + '、名称：' + sels[0].ROLE_NAME + ')', 1);
				layer.msg('删除成功');
			};
			var cb_error = function() {};
			var param = {};
			param.ids = sels[0].ROLE_ID;
			// layer.close(index);
			twxAjax(roleThingName, 'DeleteRole', param, true, cb_success, cb_error);
		});
	});
};

var formVerify = function(layui) {
	layui.form.verify({
		roleNameRepeat: function(value, item) {
			var oldName = $(item).attr('oldname');
			var flag = true;
			var data = $('#roleTable').datagrid('getData').rows;
			for (var i = 0; i < data.length; i++) {
				var d = data[i];
				if (value == d.ROLE_NAME) {
					flag = false;
				}
			}
			if (value == oldName) {
				flag = true;
			}
			if (!flag) {
				return '该角色名称已存在！';
			}
		}
	});
}


$(document).ready(function() {
	layui.use(['layer', 'form', 'jquery', 'laytpl'], function() {
		var layer = layui.layer,
			form = layui.form;

		var htmlObj = $.ajax({
			url: 'tpl/roleInfo.html',
			async: false
		});
		contentHtml = htmlObj.responseText;

		initRoleComp(layui);
		initRoleData();

		form.on('submit(formVerify)', function(data) {
			var param = data.field;
			var cb_success = function(d) {
				if (d.success === false) {
					// layer.msg(d.message,{icon:2,anim:6});
					logRecord('新增', '角色管理-新增角色(名称：' + param.role_name + '、描述：' + param.role_desc + ')', 0);
					return;
				}

				initRoleData();
				layer.closeAll();
				layer.msg('保存成功');
				logRecord('新增', '角色管理-新增角色(名称：' + param.role_name + '、描述：' + param.role_desc + ')', 1);
			};
			var cb_error = function() {};
			twxAjax(roleThingName, 'addRole', param, true, cb_success, cb_error);

			return false;
		});

		form.on('submit(updateVerify)', function(data) {
			var param = data.field;
			var oldName = $("#roleName").attr("oldname");
			var oldDesc = $("#roleDesc").attr("olddesc");
			var cb_success = function(data) {
				if (data.success === false) {
					logRecord('编辑', '角色管理-角色(ID：' + param.role_id + '、名称：' + param.role_name + '、描述：' + param.role_desc +
						')更新为(名称：' + oldName + '、描述：' + oldDesc + ')', 0);
					return;
				}

				initRoleData();
				layer.closeAll();
				layer.msg('更新成功');
				logRecord('编辑', '角色管理-角色(ID：' + param.role_id + '、名称：' + param.role_name + '、描述：' + param.role_desc +
					')更新为(名称：' + oldName + '、描述：' + oldDesc + ')', 1);

			};
			var cb_error = function() {};
			twxAjax(roleThingName, 'UpdateRole', param, true, cb_success, cb_error);

			return false;
		});

		//初始化按钮
		initBtnAdd(layui);
		initBtnEdit(layui);
		initBtnDel(layui);

		formVerify(layui);
	});
});
