<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link rel="Shortcut Icon" href="../../img/favicon.ico" />
    <link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all" />
    <link rel="stylesheet" type="text/css" href="../../plugins/webuploader/webuploader.css" />

    <link rel="stylesheet" href="../../css/icon.css" />

    <script src="../../plugins/layui-lasted/layui.js"></script>

    <link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css" />
    <link rel="stylesheet" href="../../plugins/preview/preview.css" />
    <link href="../../plugins/handson/handsontable.full.min.css" rel="stylesheet" media="screen" />
    <script src="../../plugins/easyui/jquery.min.js"></script>
    <script src="../../plugins/preview/preview.js"></script>
    <script src="../../plugins/preview/jquery.rotate.min.js"></script>

    <script src="../../plugins/easyui/jq-signature.js"></script>
    <script src="../../plugins/easyui/jquery.easyui.min.js"></script>
    <script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="../../plugins/webuploader/webuploader.min.js"></script>

    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>

    <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css" />
    <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css" />
    <script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>

    <script src="../../plugins/handson/handsontable.full.js"></script>
    <script src="../../plugins/handson/languages/all.js"></script>
    <script src="../js/js-NSV.js"></script>
    <script src="../js/sign.js"></script>
    <!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
    <script type="text/javascript" src="../js/logUtil.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/HotStyle.css" />
    <style type="text/css">
        .layui-row .sign-img {
            border: 1px solid #aaa;
            box-shadow: 0px 0px 5px 1px #aaa;
            width: 300px;
            height: 150px;
            max-width: 300px !important;
            max-height: 150px !important;
            margin-right: 10px;
            margin-top: 10px;
        }
    </style>
    <title>过程结构树查询</title>
</head>

<body onload="initPlugin()">
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
            <script src="../../plugins/html5/html5.min.js"></script>
            <script src="../../plugins/html5/respond.min.js"></script>
        <![endif]-->
    <div id="root_layout" class="easyui-layout" style="width: 100%; height: 100%" data-options="fit:true">
        <div id="p"
            data-options="region:'west',split:true,tools:[{iconCls:'download',handler:function(){ejectDownloadTable();}},{iconCls:'upload',handler:function(){showAitMappingLayer();}}]"
            title="结构树" style="width: 400px; padding: 10px">
            <ul id="dpTree" class="ztree"></ul>
        </div>
        <div data-options="region:'center'">
            <div id="tabs" class="easyui-tabs" data-options="fit:true,border:false" style="width: 100%; height: 100%">
                <div title="策划质量数据" id="quality_tab" class="original-table"
                    data-options="name:'策划质量数据',tableId:'quality_tab',fit:true,iconCls:'product_list'"
                    style="padding-top: 10px">
                    <div id="qualityMsg"
                        style="float: left; width: 100%; color: red; padding-left: 15px; padding-top: 15px"></div>
                    <div class="layui-row" style="float: left">
                        <div id="qualityTypeDiv"
                            style="display: none; margin-left: 15px; margin-bottom: 10px; float: left">
                            <input id="qualityType" />
                        </div>
                        <div id="qualityTbr" style="display: none; float: left">
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor1" id="quality-upload-three"><i
                                    class="layui-icon">&#xe681;</i> 上传三级表</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-normal" id="quality-export"><i
                                    class="layui-icon">&#xe601;</i> 导出质量表</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-warm" id="quality-download-three"><i
                                    class="layui-icon">&#xe601;</i> 下载三级表模板</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor3" id="quality-download-planTpl"><i
                                    class="layui-icon">&#xe601;</i> 下载策划表模板</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor5" id="quality-download-plan"><i
                                    class="layui-icon">&#xe601;</i> 下载策划表</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor4" id="quality-upload-plan"><i
                                    class="layui-icon">&#xe601;</i> 上传策划表</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor2" id="quality-manualSync"><i
                                    class="layui-icon">&#xe669;</i> 手动同步</button>
                            <!-- <button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor4"  id="quality-productLink">
                        <i class="layui-icon">&#xe669;</i> 产品结构树关联
                    </button> -->
                        </div>
                    </div>
                    <div id="quality_tabs" class="easyui-tabs" data-options="fit:true,border:false"
                        style="width: 100%; height: 100%">
                        <div title="质量数据确认表" id="" data-options="name:'质量数据确认表',tableId:'quality_data_tab',fit:true"
                            style="">
                            <div id="qualityTableDiv"
                                style="float: left; width: 100%; overflow: auto; padding: 0px 15px">
                                <!-- <div id="qualityTable" style="float: left;" data-options="border:false"></div> -->
                            </div>
                        </div>
                        <div title="质量数据汇总表" id="" data-options="name:'质量数据汇总表',tableId:'quality_summary_tab',fit:true"
                            style="">
                            <div id="qualitySummaryDiv"
                                style="float: left; width: 100%; overflow: auto; padding-top: 10px">
                                <div id="qualitySummaryTable" style="float: left" data-options="border:false"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div title="质量影像记录" id="quality_photo_tab" class="original-table"
                    data-options="name:'质量影像记录',tableId:'quality_photo_tab',fit:true,iconCls:'photo-icon'"
                    style="padding-top: 10px">
                    <div id="qualityPhotoMsg"
                        style="float: left; width: 100%; color: red; padding-left: 15px; padding-top: 15px"></div>
                    <div style="float: left">
                        <div id="qualityPhotoTypeDiv"
                            style="display: none; margin-left: 15px; margin-bottom: 10px; float: left">
                            <input id="qualityPhotoType" />
                        </div>
                        <div id="qualityPhotoTbr" style="display: none; float: left">
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-normal" id="quality-photo-export"><i
                                    class="layui-icon">&#xe601;</i> 导出质量影像记录表</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor3" id="quality-photo-download-planTpl"><i
                                    class="layui-icon">&#xe601;</i> 下载策划表模板</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor4" id="quality-photo-upload-plan"><i
                                    class="layui-icon">&#xe601;</i> 上传策划表</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn- mycolor5" id="quality-photo-download-plan"><i
                                    class="layui-icon">&#xe601;</i> 下载策划表</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor6"
                                id="quality-photo-download-all-photo"><i class="layui-icon">&#xe601;</i> 下载所有照片</button>
                            <button type="button" style="margin-left: 10px; margin-top: 0px; margin-bottom: 10px"
                                class="layui-btn layui-btn-sm layui-btn-mycolor7" id="quality-photo-download-list"><i
                                    class="layui-icon">&#xe601;</i> 文件下载列表</button>
                            <!-- <button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-mycolor2"  id="quality-manualSync">
                        <i class="layui-icon">&#xe669;</i> 手动同步
                    </button> -->
                        </div>
                    </div>
                    <div id="photo_tabs" class="easyui-tabs" data-options="fit:true,border:false"
                        style="width: 100%; height: 100%">
                        <div title="影像记录确认表" id="" data-options="name:'影像记录确认表',tableId:'photo_data_tab',fit:true"
                            style="">
                            <div id="qualityPhotoDiv"
                                style="float: left; width: 100%; overflow: auto; padding: 0px 15px"></div>
                        </div>
                        <div title="影像记录汇总表" id="" data-options="name:'影像记录汇总表',tableId:'photo_summary_tab',fit:true"
                            style="">
                            <div id="photoSummaryDiv"
                                style="float: left; width: 100%; overflow: auto; padding-top: 10px">
                                <div id="photoSummaryTable" style="float: left" data-options="border:false"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div title="确认表格" id="quality_report_tab" class="original-table"
                    data-options="name:'确认表格',tableId:'quality_report_tab',fit:true,iconCls:'report'">
                    <div id="dataConfig" style="display: none">
                        <table style="margin: 2px 10px; font-family: 微软雅黑; font-size: 14px">
                            <tr height="38px">
                                <td class="fieldlabel" align="right">数据来源：</td>
                                <td>
                                    <input class="easyui-combobox" id="dataSource" data-options="editable:false"
                                        style="width: 120px" />
                                </td>
                                <td class="fieldlabel auto" align="right" style="padding-left: 20px">自动填充类型：</td>
                                <td class="auto">
                                    <input class="easyui-combobox" id="dataType" data-options="editable:false"
                                        style="width: 280px" />
                                </td>

                                <td class="fieldlabel auto reportType" align="right" style="padding-left: 20px">报告类型：
                                </td>
                                <td class="auto reportType">
                                    <input class="easyui-combobox" id="reportType" data-options="editable:false"
                                        style="width: 120px" />
                                </td>
                                <td>
                                    <button type="button" id="saveConfig"
                                        class="layui-btn layui-btn-sm layui-btn-normal"
                                        style="margin-left: 10px">保存设置</button>
                                    <button type="button" id="exportExcel"
                                        class="layui-btn layui-btn-sm layui-btn-danger"
                                        style="margin-left: 10px; display: none">导出Excel</button>
                                    <button type="button" id="dlw-export-all"
                                        class="layui-btn layui-btn-sm layui-btn-danger"
                                        style="margin-left: 10px; display: none">导出全部</button>
                                    <button type="button"
                                        style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px; display: none"
                                        class="layui-btn layui-btn-sm layui-btn-danger" id="viewHtml"><i
                                            class="layui-icon">&#x1007;</i> 查看转换结果</button>
                                </td>
                                <td style="width: 300px">
                                    <div id="table-security1" class="table-security"></div>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div id="msg" style="float: left; width: 100%; color: red; padding-left: 15px; padding-top: 15px">
                        请选择表节点！</div>
                    <div id="tbr" class="layui-row" style="display: none; float: left">
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-normal" id="eidt-table"><i
                                class="layui-icon">&#xe642;</i> 编辑表格</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm" id="table-header"><i class="layui-icon">&#xe62d;</i>
                            设置表头行</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-mycolor4" id="export-pdf"><i
                                class="layui-icon">&#xe66d;</i> 导出PDF</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-mycolor8" id="import-pdf"><i
                                class="layui-icon">&#xe62f;</i> 导入PDF</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-mycolor3" id="export-excel"><i
                                class="layui-icon">&#xe67d;</i> 导出Excel</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-mycolor5" id="import-excel"><i
                                class="layui-icon">&#xe9aa;</i> 导入Excel</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-mycolor6" id="export-img"><i
                                class="layui-icon">&#xe66d;</i> 下载所有照片</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-warm" func="func-report-lock-table"
                            id="confirm-table"><i class="layui-icon">&#xe679;</i> 锁定</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-danger" func="report-clear-sign" id="clear-sign"><i
                                class="layui-icon">&#x1007;</i> 清除签名</button>
                        <button type="button"
                            style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px; background-color: #eb9765; display: none"
                            class="layui-btn layui-btn-sm" id="view-attachment"><i class="layui-icon">&#xe655;</i>
                            查看附件</button>
                        <button type="button"
                            style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px; display: none"
                            class="layui-btn layui-btn-sm layui-btn-mycolor1" id="calculate-result"><i
                                class="layui-icon">&#xe756;</i> 计算结果</button>
                        <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px"
                            class="layui-btn layui-btn-sm layui-btn-mycolor7" id="push-sign"><i
                                class="layui-icon">&#xe609;</i> 推送签署</button>
                        <div id="table-security2" class="table-security"></div>
                    </div>
                    <div id="table" style="display: none; float: left; width: 100%; overflow: auto; padding: 0px 15px">
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<script>
    handleFuncBtn();
</script>
<script src="../../plugins/index/jquery.fileDownload.js"></script>
<script src="ProcessQuality.js"></script>
<script src="QualityPhoto.js"></script>

<script src="../js/HotUtil/HotUtil.Core.js"></script>
<script src="../js/HotUtil/HotUtil.HtmlTable.js"></script>
<script src="../js/HotUtil/HotUtil.HtmlContextMenu.js"></script>
<script src="../js/HotUtil/HotUtil.Actions.js"></script>
<script src="../js/HotUtil/HotUtil.Content.js"></script>
<!-- ... 加载所有其他 HotUtil 模块 ... -->

<!-- 替换 HotUtil.Editor.js 的新模块，请注意加载顺序 -->
<!-- 1. 加载底层工具和样式 -->
<script src="../js/HotUtil/HotUtil.Editor.Utils.js"></script>
<script src="../js/HotUtil/HotUtil.Editor.Style.js"></script>
<!-- 2. 加载依赖工具的模块 -->
<script src="../js/HotUtil/HotUtil.Editor.Save.js"></script>
<script src="../js/HotUtil/HotUtil.Editor.ContextMenu.js"></script>
<!-- 3. 加载核心渲染模块 -->
<script src="../js/HotUtil/HotUtil.Editor.Render.js"></script>
<!-- 4. 最后加载顶层控制模块 -->
<script src="../js/HotUtil/HotUtil.Editor.Core.js"></script>

<script src="../js/HotUtil/HotUtil.Signatory.js"></script>
<script src="../js/HotUtil/HotUtil.TreeNode.js"></script>
<script src="../js/HotUtil/HotUtil.IO.js"></script>
<script src="../js/HotUtil/HotUtil.Download.js"></script>

<script src="../js/pdfExportDialog.js"></script>
<script src="../js/signPushDialog.js"></script>
<script src="tableContentLoading.js"></script>
<script src="tableButtons.js"></script>
<script src="aitMappingSync.js"></script>
<script src="treeContextMenu.js"></script>
<script src="featureTestCalculation.js"></script>
<script src="structureComponent.js"></script>
<script src="electricTest.js"></script>
<script src="downloadList.js"></script>
<script src="../js/aitTree.js"></script>
<script src="qualityReport.js"></script>
<script src="tableStatistics.js"></script>
<script src="tree.js"></script>

<script type="text/html" id="uploadHtml">
    <form class="layui-form" lay-filter="uploadForm">
        <div class="layui-form-item">
            <label class="fieldlabel layui-form-label">文件内容:</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
                    <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="selectedFile" style="display: none;">
            <label class="fieldlabel layui-form-label">已选文件:</label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
            </div>
        </div>
        <div class="layui-form-item" style="display:none;">
            <center>
                <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
                <button id="btn_cancel" class="layui-btn">取消</button>
            </center>
        </div>
    </form>
</script>

<script type="text/html" id="calculationHtml">
    <form class="layui-form" lay-filter="calculationForm" style="padding-top: 10px;">
        <div class="layui-row">
            <div class="layui-col-md5">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 110px;">坐标系选择</label>
                    <div class="layui-input-block" style="margin-left: 110px;">
                        <select name="coordinate" lay-filter="coordinate">
                            <option value="zUp">+Z向上</option>
                            <option value="xUp">+X向上</option>
                            <option value="zDown">+Z向下</option>
                            <option value="xDown">+X向下</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md5">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 110px;">惯量参考系</label>
                    <div class="layui-input-block" style="margin-left: 110px;">
                        <select name="reference" lay-filter="reference">
                            <option value="massCenter">质心系</option>
                            <option value="bottomPlane">底面系</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 10px;">
                        <button type="button" id="calculate" class="layui-btn layui-btn-normal">计算</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">计算结果</div>
                    <div class="layui-card-body">
                        <table class="layui-table" id="calculation-result-table">
                            <colgroup>
                                <col width="110">
                                <col width="110">
                                <col width="110">
                                <col width="110">
                                <col width="110">
                                <col width="110">
                                <col width="110">
                                <col width="110">
                                <col width="110">
                                <col width="110">
                            </colgroup>
                            <thead>
                                <tr>
                                    <th rowspan="2" style="text-align: center;">重量 kg</th>
                                    <th colspan="3" style="text-align: center;">质心位置 mm</th>
                                    <th colspan="6" style="text-align: center;">转动惯量 kg.mm²</th>
                                </tr>
                                <tr>
                                    <th style="text-align: center;">Xc</th>
                                    <th style="text-align: center;">Yc</th>
                                    <th style="text-align: center;">Zc</th>
                                    <th style="text-align: center;">Ixx</th>
                                    <th style="text-align: center;">Iyy</th>
                                    <th style="text-align: center;">Izz</th>
                                    <th style="text-align: center;">Ixy</th>
                                    <th style="text-align: center;">Ixz</th>
                                    <th style="text-align: center;">Iyz</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                    <td style="text-align: center;" contenteditable="true"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row" style="margin-top: 15px; text-align: center;">
            <div class="layui-col-md12">
                <button type="button" id="apply" class="layui-btn" style="background-color: #5FB878;">应用</button>
                <button type="button" id="cancel" class="layui-btn layui-btn-primary">取消</button>
            </div>
        </div>
    </form>
</script>

<!-- AIT映射模板上传HTML模板 -->
<script type="text/html" id="uploadAitMappingHtml">
    <form class="layui-form" action="" lay-filter="uploadAitMappingForm">
        <div class="layui-form-item">
            <label class="param-lable layui-form-label" style="width: 120px;">模板下载:</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-normal layui-btn-normal" id="downloadAitTpl">点击下载</button>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="param-lable layui-form-label" style="width: 120px;">选择文件:</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
                <button type="button" class="layui-btn layui-btn-normal layui-btn-disabled" id="uploadStart" style="display: none;">开始上传</button>
            </div>
        </div>
        <div class="layui-form-item" id="selectedFile" style="display: none;">
            <label class="param-lable layui-form-label" style="width: 120px;">已选文件:</label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux" style="width: max-content" id="selectedFileName"></div>
            </div>
        </div>
    </form>
</script>