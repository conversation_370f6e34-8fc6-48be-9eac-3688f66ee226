﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta charset="utf-8" />
		<title>紫图拍摄仪控件测试页面</title>
		<script src="UDS.js"></script>
		<script type="text/javascript">
			var base64FaceStr = "";
			var base64FaceStr2 = "";
			var testStr = "";

			function Initial() {
				ConnectServer(); //连接后台服务

			}

			function GetDeviceInfo() {
				GetVideoDevices(); //获取视频设备列表

			}

			function GetAudioDeviceInfo() {
				GetAudioDevices();
			}

			function GetCurResIndex() {
				GetCurrentResolutionIndex();
			}

			function ChangeMainDevice() {
				var camIndex = document.getElementById("MainCamDevice").selectedIndex;
				ChangeVideoDevice(camIndex);
			}

			function ChangeMainReso() {
				var resIndex = document.getElementById("MainResolution").selectedIndex;
				ChangeVideoResolution(resIndex);
			}

			function ChangeCut() {
				var type = document.getElementById("CutType").selectedIndex;
				ChangeCutType(type);
			}

			function Start() {
				var camIndex = document.getElementById("MainCamDevice").selectedIndex;
				StartVideo(camIndex);
			}

			function Start2() {
				StartRunMain();
			}

			function Start3() {
				StartRunSub();
			}

			function Start4() {
				StartRunByID("0C45", "0591");
			}

			function Start5() {
				StartRunDual(0, 0, 200, 150); //参数为副摄像头画面在主画面中的坐标x,y,及尺寸的宽、高；
			}

			function Rotate1() {
				RotateLeft();
			}

			function Rotate2() {
				RotateRight();
			}

			function Stop() {
				StopVideo();
			}

			function GetSN() {
				GetVideoDeviceSN();
			}

			function DevInfo() {
				GetVideoDevInfo();
			}

			function CheckLic() {
				CheckLicense();
			}

			function GetFolder() {
				GetSpecialFolder(2); //1-我的文档；2-桌面
			}

			function Capture1() {
				SetSavePath("c:\\456"); //设置图像保存路径，当采用文件方式拍照时必须设置，base64方式非必须设置
				SetJPGQuality(100); //jpg格式时有效
				SetColorType("彩色"); //指定图像色彩模式，“彩色”、“灰度”、“黑白”三个选项
				// SetWaterMark("这是测试水印", "宋体", 5, 150, 255, 128, 0, 0, 0.5, 1);
				SetBoardColor(0); //1灰色底板机型，0或其他值为黑色底板机型
				SetCamDPI(200); //设置高拍仪DPI，不设置则默认96DPI
				CaptureFile("0006.bmp"); //根据所给文件名后缀自动生成对应格式，jpg支持设置压缩率,必须先设置保存路径
			}

			function Capture2() {
				SetJPGQuality(70); //jpg格式时有效
				SetColorType("彩色"); //指定图像色彩模式，“彩色”、“灰度”、“黑白”三个选项
				//SetWaterMark("这是测试水印", "宋体", 5, 150, 255, 128, 0, 0, 0.5, 1);
				SetBoardColor(0); //1灰色底板机型，0或其他值为黑色底板机型
				SetCamDPI(200); //设置高拍仪DPI，不设置则默认96DPI
				CaptureBase64(0); //0-jpg,1-bmp,2-png,3-tif格式
			}

			function Capture3() {
				SetJPGQuality(70); //jpg格式时有效
				SetColorType("彩色"); //指定图像色彩模式，“彩色”、“灰度”、“黑白”三个选项
				//SetWaterMark("这是测试水印", "宋体", 5, 150, 255, 128, 0, 0, 0.5, 1);
				SetBoardColor(0); //1灰色底板机型，0或其他值为黑色底板机型
				CaptureDualBase64(0, 1); //参数为：1-0-jpg,1-bmp,2-png,3-tif格式；2-0水平合并主副头图像，1垂直合并主副头图像
			}

			function zoom1() {
				ZoomIn();
			}

			function zoom2() {
				ZoomOut();
			}

			function CaptureQR() {
				CaptureTaxQR();
			}

			function Base64() {
				ImageToBase64("d:\\finger1.bmp");
			}

			function Base642() {
				FileToBase64("d:\\test.pdf");
			}

			function PDF() {
				ImagesToPDF("d:\\001.jpg|d:\\002.jpg|d:\\003.jpg", "d:\\789\\test.pdf");
			}

			function PDF2() {
				ImagesToPDFBase64(teststr); //多张图片的base64编码以|分割
			}

			function startlive() {
				StartLiveDetect(5);
			}

			function startlive2() {
				StartLiveDetectSS(5);
			}

			function startlive3() {
				StartLiveDetectSingle(5);
			}

			function ajustdpi() {
				alert("请确保已打开主头、开启自动裁剪，并放置身份证于底座正中央，裁剪框已框选住身份证！！！")
				AjustCamDPI();
			}

			function Face() {
				VerifyFace("d:\\1.jpg", "d:\\2.jpg");
			}

			function Face2() {
				VerifyFaceByBase64(base64FaceStr, base64FaceStr2); //两个人脸图像的Base64字符串
			}

			function Face3() {
				VerifyFacePro("d:\\1.jpg", "d:\\2.jpg");
			}

			function Face4() {
				VerifyFaceByBase64Pro(base64FaceStr, base64FaceStr2); //两个人脸图像的Base64字符串
			}

			function OCR() {
				OCRImage("d:\\test.jpg");
			}

			function OCRPro() {
				OCRImagePro("d:\\test.jpg", "d:\\test.txt", "d:\\test.json", "d:\\test.rtf", "d:\\test.pdf");
			}

			function Print() {
				PrintImage("d:\\test.jpg");
			}

			function Combine() {
				CombineImages("D:\\11.jpg", "D:\\22.jpg", "D:\\out.jpg", 0);
			}

			function Combine2() {
				CombineImagesPro("D:\\11.jpg###D:\\22.jpg###D:\\33.jpg", "D:\\out.jpg", 0, 1);
			}

			function Combine3() {
				CombineImagesProByBase64(testStr, 0); //多张图片的base64编码以|分割
			}

			function ScanCode() {
				ScanBarCode(5);
			}

			function ScanCode2() {
				ScanBarCodePro(5);
			}

			function BarCode() {
				GetBarCode("D:\\test.jpg", "abcd111");
			}

			function BarCode2() {
				GetBarCodeBase64(testStr, "abcd222");
			}

			function Upload() {
				httpUpload("http://192.168.0.108:8080/test2.aspx", "d:\\1.jpg", 0);
			}

			function Upload2() {
				ftpUpload("ftp://127.0.0.1:21", "test", "admin", "123456", "D:\\001.jpg", 0);
			}

			function GetFolderFileList() {
				GetFolderFileListJson("d:\\456");
			}

			function DeleteImages() {
				DeleteImagesInFolder("d:\\456");
			}

			function DeleteFile() {
				DeleteImage("d:\\test.jpg");
			}

			function StartRec() {
				var videoIndex = document.getElementById("MainCamDevice").selectedIndex;
				var audioIndex = document.getElementById("AudioDevice").selectedIndex;
				var resolution = document.getElementById("MainResolution").options[document.getElementById("MainResolution")
					.selectedIndex].innerText;
				StartRecord(videoIndex, audioIndex, "D:\\test.avi", resolution, 30);
			}

			function StopRec() {
				StopRecord();
			}

			function ReadCard() {
				ReadIDCard();
			}

			function GetID() {
				GetSAMID();
			}

			function ReadSSCard() {
				Read2thSSCard();
			}

			function ReadSSCard3() {
				Read3thSSCardBasPSAM(1); //读三代社保卡基本信息psam方式，参数为：卡类型
			}

			function ReadSSCard4() {
				Read3thSSCardBasHSM1(1); //读三代社保卡基本信息加密机方式步骤一，参数为：卡类型
			}

			function ReadSSCard5() {
				Read3thSSCardBasHSM2("加密机计算返回的参数"); //读三代社保卡基本信息加密机方式步骤二，参数为：加密机计算出来的参数
			}

			function ReadBank() {
				ReadICCard();
			}

			function ReadMag() {
				ReadMagCard();
			}

			function CapFinger() {
				CapFingerFile("d:\\finger.bmp");
			}

			function CapFinger2() {
				CapFingerBASE64();
			}

			function MatchFinger() {

				MatchFingerByFile("d:\\1.bmp", "d:\\2.bmp");
			}

			function MatchFinger2() {

				MatchFingerByBASE64(base64FaceStr, base64FaceStr); //传入两个指纹图像的base64
			}

			function MatchFinger3() {

				MatchCardFingerByFile("d:\\1.bmp");
			}

			function MatchFinger4() {

				MatchCardFingerByBase64(base64FaceStr); //先读卡，如身份证包含指纹，则可以传入采集到的指纹图像base64和身份证指纹进行比对。
			}

			function Sign() {
				StartSign("请进行签名");
			}

			function ConfirmDoc() {
				OpenPDF("D:\\test.pdf", 1);
			}

			function ConfirmDoc2() {
				OpenPDF("D:\\test.pdf", 0);
			}

			function CloseP() {
				ClosePDF();
			}

			function Web() {
				OpenWebPage(
					"file:///D:/%E7%B4%AB%E5%85%89%E5%9B%BE%E6%96%87/%E5%94%AE%E5%89%8D%E6%94%AF%E6%8C%81%E8%B5%84%E6%96%99/%E7%B4%AB%E5%9B%BE%E9%80%9F%E6%8B%8D%E4%BB%AA%E5%BC%80%E5%8F%91%E5%8C%85/%E7%B4%AB%E5%9B%BEWS%E5%BC%80%E5%8F%91%E5%8C%85/%E9%AB%98%E6%8B%8D%E4%BB%AA/%E5%B5%8C%E5%85%A5%E5%BC%8F/demo%E6%96%B0/jsSendMessageDemo.html"
					);
				//OpenWebPage("https://twain.org/wp-content/uploads/2021/11/TWAIN-2.5-Features.pdf");
				//OpenWebPage("file:///D:/%E7%B4%AB%E5%85%89%E5%9B%BE%E6%96%87/%E5%94%AE%E5%89%8D%E6%94%AF%E6%8C%81%E8%B5%84%E6%96%99/%E7%B4%AB%E5%9B%BE%E9%80%9F%E6%8B%8D%E4%BB%AA%E5%BC%80%E5%8F%91%E5%8C%85/%E7%B4%AB%E5%9B%BEWS%E5%BC%80%E5%8F%91%E5%8C%85/%E9%AB%98%E6%8B%8D%E4%BB%AA/%E5%B5%8C%E5%85%A5%E5%BC%8F/demo%E6%96%B0/UDSVideoDemo.html");
			}

			function CloseWeb() {
				CloseWebPage();
			}

			function Evolute() {
				StartEvalute();
			}

			function Staff() {
				StartStaff("d:\\headPic.jpg", "赵四", "100045", 4);
			}

			function Staff2() {
				CloseStaff();
			}

			function pwd() {
				//StartPWDInput("arcuds","arcuds","请输入密码",0);//密钥或密钥向量为空则直接返回明文
				StartPWDInput("", "", "请输入手机号码", 1); //密钥或密钥向量为空则直接返回明文
				//StartPWDInput("","","请输入验证码",1);//密钥或密钥向量为空则直接返回明文

			}

			function pwd2() {
				ClosePWDInput();
			}

			function pwd3() {
				DecryptPWD("geZGmWiA8eJ2Yvt3Giv95g==", "arcuds", "arcuds"); //示例密文，原始文本为"abcd1234"
			}
			//接收并处理后台发送的信息,不要修改函数名称及参数定义,根据函数名和返回值进行流程控制即可，以下为几个示例
			function produceMessage(functionName, Result) {
				console.log(functionName, Result);
				if (functionName != "ShowVideo") {
					var obj = document.getElementById("show");
					obj.value = "指令名称:" + functionName + ";\r执行结果:" + Result;
				}

				if (functionName == "Connect") {
					if (Result == "0") {
						alert("websocket服务器连接失败，请确保已运行紫图外设服务!");
					} else {
						StartRunMain(); //连接成功后直接打开主头
					}

				} else if (functionName == "ShowVideo") {
					var myimg = document.getElementById("pic");
					myimg.src = "data:image/jpeg;base64," + Result;
					return false;
				} else if (functionName == "GetVideoDevices") {
					if (Result != "none") {
						document.getElementById("MainCamDevice").innerHTML = "";
						var deviceArray = new Array();
						deviceArray = Result.split("|");
						for (var i = 0; i < deviceArray.length; i++) {
							var objOption = document.createElement("option");
							objOption.text = deviceArray[i];
							objOption.value = i;
							document.getElementById("MainCamDevice").options.add(objOption);
						}

					} else {
						var objOption = document.createElement("option");
						objOption.text = "未找到合适的设备";
						objOption.value = 0;
						document.getElementById("MainCamDevice").options.add(objOption);
					}
				} else if (functionName == "GetAudioDevices") {
					if (Result != "none") {
						document.getElementById("AudioDevice").innerHTML = "";
						var resolutionArray = new Array();
						resolutionArray = Result.split("|");
						for (var i = 0; i < resolutionArray.length; i++) {
							var objOption = document.createElement("option");
							objOption.text = resolutionArray[i];
							objOption.value = i;
							document.getElementById("AudioDevice").options.add(objOption);
						}

					} else {
						var objOption = document.createElement("option");
						objOption.text = "无麦克风设备";
						objOption.value = 0;
						document.getElementById("AudioDevice").options.add(objOption);
					}
				} else if (functionName == "StartVideo") {
					if (Result == 1) {}
				} else if (functionName == "GetResolution") {
					if (Result != "none") {
						document.getElementById("MainResolution").innerHTML = "";
						var resolutionArray = new Array();
						resolutionArray = Result.split("|");
						for (var i = 0; i < resolutionArray.length; i++) {
							var objOption = document.createElement("option");
							objOption.text = resolutionArray[i];
							objOption.value = i;
							document.getElementById("MainResolution").options.add(objOption);
						}

					} else {
						var objOption = document.createElement("option");
						objOption.text = "无分辨率";
						objOption.value = 0;
						document.getElementById("MainResolution").options.add(objOption);
					}
				} else if (functionName == "CaptureFile") {
					//alert(Result);
					var myimg = document.getElementById("showPic");
					myimg.src = Result;
					return false;
				} else if (functionName == "CaptureBase64" || functionName == "CaptureDualBase64") {
					//alert(Result);
					var myimg = document.getElementById("showPic");
					myimg.src = "data:image/jpeg;base64," + Result;
					return false;
				} else if (functionName == "CaptureTaxQR") {
					alert(Result);
				} else if (functionName == "VerifyFace") {
					alert(Result);
				} else if (functionName == "CapFingerBASE64") {
					base64FaceStr = Result;
					var myimg = document.getElementById("showPic");
					myimg.src = "data:image/jpeg;base64," + Result;
					return false;
				}
				//签字结果
				else if (functionName == "StartSign") {
					if (Result != "0") {
						var myimg = document.getElementById("showPic");
						myimg.src = "data:image/png;base64," + Result;
						return false;
					}
				} else if (functionName == "ReadIDCard") {
					if (Result != "0") {
						var jsonOBJ = JSON.parse(Result);
						alert(jsonOBJ.Name);
						alert(jsonOBJ.ID);
						alert(jsonOBJ.Sex);
					}
				}
			}
		</script>
	</head>
	<body onload="Initial()">
		<p>高拍仪功能：</p>
		视频设备列表:
		<select name="MainCamDevice" id="MainCamDevice" onchange="ChangeMainDevice()">
		</select>
		分辨率:
		<select name="MainResolution" id="MainResolution" onchange="ChangeMainReso()">
		</select>
		裁剪方式:
		<select name="CutType" id="CutType" onchange="ChangeCut()">
			<option value="0">自动裁剪</option>
			<option value="1">不裁剪</option>
		</select>
		<br /><br />
		<button id="ConnectServer" onclick="ConnectServer()">初始化服务 </button>
		<button id="GetVideoDevice" onclick="GetDeviceInfo()">获取视频设备列表 </button>
		<button id="StartScan" onclick="Start()">开启视频 </button>
		<button id="GetCurRes" onclick="GetCurResIndex()">获取当前分辨率 </button>
		<button id="CaptureFile" onclick="Capture1()">拍照(文件) </button>
		<button id="CaptureBase64" onclick="Capture2()">拍照(base64) </button>
		<button id="zoom1" onclick="zoom1()">放大 </button>
		<button id="zoom2" onclick="zoom2()">缩小 </button>
		<button id="CaptureQR" onclick="CaptureQR()">发票二维码 </button>
		<button id="StopScan" onclick="Stop()">关闭视频 </button>
		<button id="Start2" onclick="Start2()">开启主头视频 </button>
		<button id="Start3" onclick="Start3()">开启副头视频 </button>
		<button id="Start4" onclick="Start4()">开启指定设备(硬件ID) </button>
		<button id="Start5" onclick="Start5()">同时开启主副头 </button>
		<button id="CaptureDualBase64" onclick="Capture3()">双头合并拍照(base64) </button>
		<button id="Rotate1" onclick="Rotate1()">左旋转 </button>
		<button id="Rotate2" onclick="Rotate2()">右旋转 </button>
		<button id="GetSN" onclick="GetSN()">获取序列号 </button>
		<button id="ImageToBase64" onclick="Base64()">图像转Base64编码 </button>
		<button id="FileToBase64" onclick="Base642()">文件转Base64编码 </button>
		<button id="DeleteFile" onclick="DeleteFile()">删除指定文件 </button>
		<button id="GetFolderFileList" onclick="GetFolderFileList()">获取文件夹下的文件列表 </button>
		<button id="DeleteAllImage" onclick="DeleteImages()">删除文件夹下的所有文件 </button>
		<button id="Upload" onclick="Upload()">http上传文件 </button>
		<button id="Upload2" onclick="Upload2()">ftp上传文件 </button>
		<button id="PDF" onclick="PDF()">图像文件转PDF </button>
		<button id="PDF2" onclick="PDF2()">图像文件转PDF（base64） </button>
		<button id="live1" onclick="startlive()">活体检测(双目) </button>
		<button id="live2" onclick="startlive2()">活体检测SS(双目) </button>
		<button id="live3" onclick="startlive3()">活体检测（单目） </button>
		<button id="Face" onclick="Face()">人脸比对（文件） </button>
		<button id="Face2" onclick="Face2()">人脸比对（base64） </button>
		<button id="Face3" onclick="Face3()">人脸比对Pro（文件） </button>
		<button id="Face4" onclick="Face4()">人脸比对Pro（base64） </button>
		<button id="ocr" onclick="OCR()">OCR识别图像 </button>
		<button id="ocrPro" onclick="OCRPro()">OCR识别图像Pro </button>
		<button id="print" onclick="Print()">打印图像 </button>
		<button id="combine" onclick="Combine()">合并图像 </button>
		<button id="combine2" onclick="Combine2()">合并图像2 </button>
		<button id="combine3" onclick="Combine3()">合并图像2(base64) </button>
		<button id="ScanCode" onclick="ScanCode()">扫码1 </button>
		<button id="ScanCode2" onclick="ScanCode2()">扫码2 </button>
		<button id="barCode" onclick="BarCode()">识别条码、二维码 </button>
		<button id="barCode" onclick="BarCode2()">识别条码、二维码(base64) </button>
		<button id="DevInfo" onclick="DevInfo()">获取设备信息 </button>
		<button id="CheckLic" onclick="CheckLic()">检查限定设备 </button>
		<button id="GetFolder" onclick="GetFolder()">获取特定文件夹</button>
		<button id="ajust" onclick="ajustdpi()">DPI校正 </button>
		<br /><br />
		<button id="GetAudioDevice" onclick="GetAudioDeviceInfo()">获取音频设备列表 </button>
		<select name="AudioDevice" id="AudioDevice">
		</select>
		<button id="Start5" onclick="StartRec()">开始录像 </button>
		<button id="Stop2" onclick="StopRec()">停止录像 </button>
		<hr />
		<p>读卡器指纹功能：</p>
		<button id="ReadIDCard" onclick="ReadCard()">读身份证</button>
		<button id="GetID" onclick="GetID()">获取读卡模块ID</button>
		<button id="ReadSS" onclick="ReadSSCard()">读二代社保卡基本信息</button>
		<button id="ReadSSCard3" onclick="ReadSSCard3()">读三代社保卡基本信息(PSAM)</button>
		<button id="ReadSSCard4" onclick="ReadSSCard4()">读三代社保卡基本信息步骤1(加密机)</button>
		<button id="ReadSSCard5" onclick="ReadSSCard5()">读三代社保卡基本信息步骤2(加密机)</button>
		<button id="ReadBankCard" onclick="ReadBank()">读IC银行卡</button>
		<button id="ReadMag" onclick="ReadMag()">读磁条银行卡</button>
		<button id="CapFinger" onclick="CapFinger()">采集指纹（文件）</button>
		<button id="CapFinger2" onclick="CapFinger2()">采集指纹（BASE64）</button>
		<button id="MatchFinger" onclick="MatchFinger()">指纹比对（文件）</button>
		<button id="MatchFinger2" onclick="MatchFinger2()">指纹比对（BASE64）</button>
		<button id="MatchFinger3" onclick="MatchFinger3()">与身份证中指纹比对（文件方式）</button>
		<button id="MatchFinger4" onclick="MatchFinger4()">与身份证中指纹比对（BASE64方式）</button>
		<hr />
		<p>手写屏功能：</p>
		<button id="Sign" onclick="Sign()">签字 </button>
		<button id="ConfirmDoc" onclick="ConfirmDoc()">推送pdf(带签字) </button>
		<button id="ConfirmDoc2" onclick="ConfirmDoc2()">推送pdf（不带签字） </button>
		<button id="CloseP" onclick="CloseP()">关闭PDF </button>
		<button id="showWeb" onclick="Web()">网页推送 </button>
		<button id="CloseWeb" onclick="CloseWeb()">关闭网页 </button>
		<button id="Evolute" onclick="Evolute()">评价器 </button>
		<button id="Staff" onclick="Staff()">工牌展示 </button>
		<button id="pwd" onclick="pwd()">密码输入 </button>
		<button id="pwd2" onclick="pwd2()">关闭密码输入 </button>
		<button id="pwd3" onclick="pwd3()">密码解密 </button>

		<p>预览窗口、执行结果：</p>
		<hr />
		<img id="pic" style="background-color:black;width:640px;height:480px" />
		<textarea id="show" rows="31" cols="60"></textarea>
		<img id="showPic" style="background-color:white;width:640px;height:480px" />
	</body>
</html>