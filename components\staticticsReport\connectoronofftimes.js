var curInfo = {
	tableId: 'dataTable',
	tableType: '接插件插拔次数统计',
	rsId: parent.window.tjfxRsID,
	columns: [
		[{
			field: 'TJFX_ID',
			title: '统计分析ID',
			hidden: true
		}, {
			field: 'DATA_ID',
			title: 'MES DATAID',
			hidden: true
		}, {
			field: 'REF_DPID',
			title: '关联的数据包ID',
			hidden: true
		}, {
			field: 'ISNAME',
			title: '单机名称',
			width: 100,
			align: 'center'
		}, {
			field: 'ISCODE',
			title: '单机代号',
			width: 100,
			align: 'center'
		}, {
			field: 'CONNECTORCODE',
			title: '接插件代号',
			width: 120,
			align: 'center'
		}, {
			field: 'CONNECTNUM',
			title: '连接次数',
			width: 80,
			align: 'center'
		}, {
			field: 'BREAKNUM',
			title: '断开次数',
			width: 80,
			align: 'center'
		}]
	]
};
/** 重置搜索条件 */
var resetSearchCondition = function() {
	$('#isname').textbox('setValue', '');
	$('#iscode').textbox('setValue', '');
	$('#connectorcode').textbox('setValue', '');
	$('#connectnum').textbox('setValue', '');
	$('#breaknum').textbox('setValue', '');
};

var getFieldValue = function() {
	var param = {};
	param.type = curInfo.tableType;
	param.rsId = curInfo.rsId;
	var isname = $('#isname').textbox('getValue');
	var iscode = $('#iscode').textbox('getValue');
	var connectorcode = $('#connectorcode').textbox('getValue');
	var connectnum = $('#connectnum').textbox('getValue');
	var breaknum = $('#breaknum').textbox('getValue');
	param.conditionData = {
		isname: isname,
		iscode: iscode,
		connectorcode: connectorcode,
		connectnum: connectnum,
		breaknum: breaknum
	};
	return param;
};
