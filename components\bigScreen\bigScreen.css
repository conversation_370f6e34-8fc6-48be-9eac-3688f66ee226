* {
	-moz-box-sizing: unset;
	-webkit-box-sizing: unset;
	-o-box-sizing: unset;
	-ms-box-sizing: unset;
	box-sizing: unset;
}


body::-webkit-scrollbar {
	display: none;
}



body {
	margin: 0;
	width: 100%;
	height: 100vh;
	background-color: #02004C;
	background-size: 100% 100%;
	overflow-x: hidden;
}


.header {
	background-image: url(img/bg_titile.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	height: 75px;
	text-align: center;
	line-height: 70px;
	color: #00D1FF;
	font-size: 36px;
	letter-spacing: 7px;
	font-weight: 500;
}

.content {
	width: 100%;
	height: 700px;
}

.content-left {
	width: 30%;
	height: 100%;
	float: left;
}

.content-left-top {
	width: calc(100% - 20px);
	height: calc(55% - 20px);
	padding: 0 0 20px 20px;
}

.content-left-top-card {
	background-image: url(img/bg_1.png);
	background-size: 100% 100%;
	background-repeat: no-repeat;
	height: 100%;
	width: 100%;
}

.content-left-bottom {
	width: calc(100% - 20px);
	height: calc(45% - 20px);
	padding: 0 0 20px 20px;
}

.content-left-bottom-card {
	background-image: url(img/bg_2.png);
	background-size: 100% 100%;
	background-repeat: no-repeat;
	height: 100%;
	width: 100%;
}

.content-center {
	padding-top: 15px;
	padding-left: 15px;
	padding-right: 15px;
	width: calc(40% - 30px);
	height: calc(100% - 15px);
	float: left;
}

.content-right {
	width: 30%;
	height: 100%;
	float: left;
}

.content-right-top {
	width: calc(100% - 20px);
	height: calc(55% - 20px);
	padding: 0 20px 20px 0;
}

.content-right-top-card {
	background-image: url(img/bg_1.png);
	background-size: 100% 100%;
	background-repeat: no-repeat;
	height: 100%;
	width: 100%;
}

.content-right-bottom {
	width: calc(100% - 20px);
	height: calc(45% - 20px);
	padding: 0 20px 20px 0;
}

.content-right-bottom-card {
	background-image: url(img/bg_2.png);
	background-size: 100% 100%;
	background-repeat: no-repeat;
	height: calc(100% - 26px);
	width: calc(100% - 26px);
	padding: 13px;
}

.content-right-bottom-card img {
	height: 100%;
	width: 100%;
}

.card {
	padding: 13px;
	height: calc(100% - 26px);
	width: calc(100% - 26px);
}

.card-header {
	position: relative;
	height: 29px;
	letter-spacing: 1px;
	line-height: 28px;
	padding-left: 18px;
	color: #00D1FF;
	font-size: 14px;
	background-image: url(img/bg_title_1.png);
	background-repeat: no-repeat;
}

.card-header-name {
	float: left;
}

.card-header-btn {
	float: right;
	cursor: pointer;
	margin-right: 5px;
}

.card-header-time {
	float: right;
	margin-right: 10px;
}

.card-body {
	height: calc(100% - 29px);
}

.num-col {
	float: left;
	width: calc(50% - 1px);
	height: 100%;
}

.num-split {
	float: left;
	width: 2px;
	height: 100%;
}

.num-split div {
	width: 100%;
	margin-top: 28px;
	height: 70px;
	background-image: url(img/icon_split.png);
	background-repeat: no-repeat;
}

.num-row {
	background-image: url(img/icon_s.png), url(img/icon_s.png);
	background-position: bottom, top;
	background-repeat: no-repeat, no-repeat;
	height: calc(33.333% - 32px);
	margin: 25px 0px 0px 0px;
	width: 100%;
	cursor: pointer;
}

.num-icon {
	float: left;
	height: 100%;
	width: 40%;
}

.num-text {
	color: #00D1FF;
	width: 50%;
	float: left;
	height: 100%;
}

.num-icon img {
	float: right;
	width: 45px;
	height: 45px;
	margin-top: 12px;
}

.num-text div:first-child {
	margin-left: 10px;
	line-height: 34px;
	height: 34px;
	width: 100%;
	float: left;
	font-size: 14px;
	font-weight: bold;
}

.num-text div:last-child {
	width: 100%;
	float: left;
	margin-left: 10px;
	font-weight: bold;
	font-size: 20px;
}

.photo-card {
	padding: 10px;
	color: white;
	height: calc(100% - 49px);
	width: calc(100% - 20px);
	line-height: 27px;
}

.table-header {
	font-weight: bold;
}

.table-row {
	height: 27px;
	margin-top: 24px;
}

.sort {
	width: 33px;
	height: 27px;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	text-align: center;
}

.sort1 {
	background-image: url(img/sort_1.png);
}

.sort2 {
	background-image: url(img/sort_2.png);
}


.photo-process .layui-progress {
	margin-top: 10px;
	width: 90%;
	border: 1px solid #00CDFF;
	background-color: #00000000;
	height: 8px;
}

.photo-process .layui-progress .layui-progress-bar {
	height: 8px;
	background-color: #00CDFF;
}

.photo-model {
	font-size: 16px;
}

.photo-num {
	color: #00CDFF;
	font-weight: bold;
	font-size: 17px;
}

.model-select {
	width: 100%;
	float: left;
	height: 40px;
}

.model-select .layui-input {
	background-color: #042473;
	border-color: #006DAB;
	color: white;
}

.model-select .layui-input::-webkit-input-placeholder {
	color: white;
}

.model-select .layui-input:hover {
	border-color: #0699ed !important;
}

.model-select .layui-input:focus {
	border-color: #0699ed !important;
}

.model-select .layui-form-select dl {
	border: 1px solid #006DAB;
	background-color: #042473;
	color: white;
}

.layui-form-select .layui-edge {
	border-top-color: #00D2FF;
}

.model-select .layui-form-select dl dd.layui-select-tips {
	color: #fff;
}

.model-select .layui-form-select dl dd.layui-this {
	background-color: #00d2ffb8;
}


.model-select .layui-form-select dl dd:hover {
	background-color: #2983cf;
	-webkit-transition: .5s all;
	transition: .5s all;
}

.quality-data {
	height: calc(100% - 40px);
	width: 100%;
	padding-top: 20px;
	float: left;
}

.quality-data div {
	height: calc(100% - 40px);
	width: 100%;
	float: left;
	background-image: url(img/bg_center.jpg);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-position: center;
}

.footer {
	padding-left: 20px;
	padding-right: 20px;
	width: calc(100% - 40px);
	float: left;
	height: 258px;
}

.footer-content {
	width: calc(100% - 26px);
	height: calc(100% - 26px);
	float: left;
	background-image: url(img/bg_5.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	padding: 13px;
	overflow-x: auto;
}

.footer-content table {
	/* width: 100%; */
	height: 100%;
	color: #00CDFF;
}



.footer-content table td {
	text-align: center;
	vertical-align: middle;
}

#diagramContainer>table>tbody>tr:nth-child(1)>td {
	padding-top: 20px;
}

#diagramContainer>table>tbody>tr:nth-child(2)>td {
	vertical-align: baseline;
	padding-top: 50px;
}

.footer-content table td div {
	/* width: fit-content; */
}

/**
 * 滚动条样式的调整
 */

.footer-content::-webkit-scrollbar {
	height: 9px;
	background-color: #969696;
}

.footer-content::-webkit-scrollbar-track {
	background: #e2e2e2;
	border-radius: 8px;
}

.footer-content::-webkit-scrollbar-thumb {
	background: #00cdff;
	border-radius: 8px;
}

.footer-content::-webkit-scrollbar-thumb:hover {
	background: #0095ff;
}

.footer-content::-webkit-scrollbar-corner {
	background: #969696;
}

.start-point {
	cursor: pointer;
	padding-top: 7px;
	text-align: center;
	display: inline-block;
	width: 52px;
	height: 23px;
	background-image: url(img/num_bg.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.start-point span {
	margin-right: 10px;
}

.end-point {
	display: flex;
	align-items: center;
	flex-direction: column;
}

.process-img {
	width: 50px;
	height: 50px;
}

.process-name {
	margin-top: 10px;
	width: 120px;
}

/**
 * 滚动条样式的调整
 */
.layui-anim-upbit::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.layui-anim-upbit::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 6px;
	background-color: skyblue;
}

.layui-anim-upbit::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #ededed;
	border-radius: 6px;
}


.original-table .datagrid-header {
	border-color: #000000;
	border-width: 1px 0 1px 0;
}

.original-table .datagrid-header td,
.original-table .datagrid-body td {
	border-color: #000000;
}

.original-table .datagrid-header td {
	font-weight: 600;
}

.original-table .datagrid-header,
.original-table .datagrid-td-rownumber {
	background-color: transparent !important;
}

.table-row-a {
	color: blue;
	cursor: pointer;
}




.model-table {
	color: white;
	font-weight: 600;
	background-color: #00003B;
}

.model-table tbody tr:hover {
	background-color: #00003B;
}

.model-table td {
	border-width: 0px 0px 1px 0px;
	border-color: #262558;
}

.select-i {
	margin-top: -9px !important;
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}

.model-list .layui-layer-content {
	padding: 0 !important;
}

.model-list .layui-layer-content {
	max-height: 300px;
	overflow: auto;
}

.model-list .layui-layer-content::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}

.model-list .layui-layer-content::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 6px;
	background-color: #00cdff;
}

.model-list .layui-layer-content::-webkit-scrollbar-thumb:hover {
	/*滚动条里面小方块*/
	border-radius: 6px;
	background-color: #0095ff;
}

.model-list .layui-layer-content::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #e2e2e2;
	border-radius: 6px;
}

.my-layer {
	background-color: #070F77 !important;
}

.my-layer .layui-layer-title {
	border-bottom: 0px !important;
	padding-left: 20px !important;
	line-height: 35px !important;
}

.my-layer .layui-layer-title span.layui-this {
	color: #000A3B !important;
	border-left: 0px !important;
	border-right: 0px !important;
	background-color: #00FFF4 !important;
}

.layui-layer-tab .layui-layer-title span {
	color: #00FFF4 !important;
	background-color: #202784 !important;
	font-size: 15px;
	font-weight: 600;
	letter-spacing: 1px;
	height: 35px !important;
	margin-top: 15px !important;
	border-radius: 4px !important;
}

#tableContent .layui-table {
	width: 100%;
	margin: 10px 0;
	background-color: #070F77;
	color: #fff;
}

#tableContent .layui-table th,
#tableContent .layui-table td {
	font-size: 16px;
}

#tableContent .layui-table-view {
	border: none;
}

#tableContent .layui-table-header {
	border-radius: 4px 4px 0px 0px;
	background-color: #070F77;
	border-bottom-width: 0;
}

#tableContent .layui-table thead tr {
	background: linear-gradient(360deg, rgba(0, 255, 244, 0.08) 0%, rgba(0, 255, 244, 0.42) 100%);
	border: 1px solid rgba(0, 255, 244, 0.5);
}

#tableContent .layui-table tr {
	background-color: #070F77;
}

#tableContent .layui-table tbody tr:hover,
#tableContent .layui-table[lay-even] tr:nth-child(even) {
	background-color: #1A2181;
}

#tableContent .layui-table-cell {
	height: 36px;
	line-height: 36px;
}

#tableContent .layui-table-page {
	border-color: #0473ab;
	padding: 20px 7px 0px 7px;
	height: 54px;
}

#tableContent .layui-laypage .layui-laypage-skip,
#tableContent .layui-laypage span {
	color: #fff;
}

#tableContent .layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: #0473ab;
}

#tableContent .layui-laypage a {
	color: #fff;
}