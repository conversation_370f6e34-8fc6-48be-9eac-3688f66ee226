//二级表对象
var SecondTable = function (record, processTreeId, productTreeId, gridHeight) {
	var othis = this;
	this.tableId = "secondTable";
	this.pageOptions = {
		pageSize: 30,
		pageNumber: 1
	};
	this.renderTable = function () {
		var cb_success = function (data) {
			if (data.success) {
				var dealCol = dealColumns(JSON.parse(data.result));
				$('#secondTable').datagrid({
					data: [],
					columns: dealCol.col,
					height: gridHeight,
					singleSelect: false,
					remoteSort: false,
					pagination: true,
					emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
					loadMsg: '正在加载数据...',
					striped: false,
					onHeaderContextMenu: function (e, field) {
						e.preventDefault();
						if (!othis.cmenu) {
							othis.cmenu = gridUtil.createColumnMenu(othis.tableId);
						}
						othis.cmenu.menu('show', {
							left: e.pageX,
							top: e.pageY
						});
					},
					onLoadSuccess: function (data) {
						var rows = data.rows;
						var $datagrid = $('#secondTable');
						if (rows.length > 0) {
							for (var i = 0; i < rows.length; i++) {
								var row = rows[i];
								var mergedInfo = row.mergedInfo;
								if (mergedInfo != "" && mergedInfo != undefined) {
									var mergeds = mergedInfo.split(",");
									for (var j = 0; j < mergeds.length; j++) {
										var merged = mergeds[j];
										var columnName = merged.split(":")[0];
										var rowspan = merged.split(":")[1];
										$datagrid.datagrid('mergeCells', {
											index: i,
											field: columnName,
											rowspan: rowspan
										});
									}
								}
							}
						}
						changeWidth('secondTable');
						$("#secondTableDiv .datagrid-body").css("overflow-x", "auto");
						$('#' + othis.tableId).datagrid('loaded');
					}
				});
			} else {
				layer.alert("表头获取失败！", {
					icon: 2
				});
			}
		}

		var cb_error = function () {
			layer.alert("表头获取失败！", {
				icon: 2
			});
		};

		var parmas = {
			id: record.id
		};
		twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', parmas, false, cb_success, cb_error);
	};
	//初始化分页组件
	this.initPagination = function (data) {
		$('#' + othis.tableId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function () {
					othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions.pageNumber);
				}
			}],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function (pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function (pageNumber, pageSize) {
				//返回false可以在取消刷新操作
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			},
			onRefresh: function (pageNumber, pageSize) {
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function (pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};
	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function () {
		$('#' + othis.tableId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
	};
	//初始化全部的记录条数
	this.initTotalRecords = function () {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function (data) {
			othis.pageLoadFlag = true;
			othis.totalRecords = data.rows[0].result;
			if (othis.dataLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () { };

		var parmas = {
			table_config_id: record.id,
			processTreeId: processTreeId,
			productTreeId: productTreeId,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataCount', parmas, true, cb_success, cb_error);
	};

	//分页查询数据
	this.queryDataByPage = function (pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.tableId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function (data) {
			othis.dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + othis.tableId).datagrid('loadData', data.array);
			if (othis.pageLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			table_config_id: record.id,
			processTreeId: processTreeId,
			productTreeId: productTreeId,
			pageSize: pageSize,
			pageNumber: pageNumber,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataPage', parmas, true, cb_success, cb_error);
	};

	this.renderTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
}


//质量数据表格
var DataTable = function () {
	var othis = this;
	var layer, form, upload, device;
	layui.use(['layer', 'form', 'upload'], function () {
		layer = layui.layer;
		upload = layui.upload;
		form = layui.form;
		device = layui.device();
	});
	this.getGridHeight = function () {
		var windowH = window.innerHeight || document.documentElement.clientHeight;
		var diagramContainerH = $("#diagramContainer").css("height").split("px")[0];
		var gridHeight = windowH - 128 - Number(diagramContainerH);
		return gridHeight;
	};
	//加载质量数据类型下拉框
	this.loadTypeSelect = function (processTreeId) {
		var cb_success = function (data) {
			if (data.array.length > 0) {
				$('#productType').combobox({
					data: data.array,
					valueField: 'name',
					textField: 'name',
					editable: false,
					width: 300,
					panelHeight: 'auto',
					onSelect: function (record) {
						othis.loadProductTable(record, processTreeId);
					}
				});
				$('#productType').combobox("select", data.array[0].name);
			} else {
				othis.showMsg('数据加载中！');
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("加载质量数据类型下拉框出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.QualitySearch", "QuerySecondTableType", {
			processTreeId: processTreeId,
			productTreeId: bomTree.selBomTreeId
		}, true, cb_success, cb_error);
	};
	//加载其他类型的质量表的表数据
	this.loadType2Table = function (record, processTreeId) {
		var tableColumns = [];
		var tableType = record.ctype;
		var tableName = '';
		if (tableType == 'electronic_components') {
			tableColumns = electronicComponentsColumns;
			tableName = "电子元器件汇总表";
		} else if (tableType == 'cable_insulation_test') {
			tableColumns = cableTestColumns;
			tableName = "电缆导通绝缘测试汇总表";
		} else if (tableType == 'heating_element_reinspection') {
			tableColumns = heatingReinspectionColumns;
			tableName = "加热片入所复验";
		} else if (tableType == 'heating_circuit_test') {
			tableColumns = heatingTestColumns;
			tableName = "加热回路测试";
		} else if (tableType == 'StandAlong') {
			tableColumns = standAlongColumns;
			tableName = "单机装星情况";
		} else if (tableType == 'ConnectorOnOff') {
			tableColumns = connectorOnOffColumns;
			tableName = "接插件连接固封情况";
		} else if (tableType == 'ConnectorOnOffTimes') {
			tableColumns = connectorOnOffTimesColumns;
			tableName = "接插件插拔次数统计";
		} else if (tableType == 'Heater') {
			tableColumns = heaterColumns;
			tableName = "星上加热器";
		} else if (tableType == 'HeatResist') {
			tableColumns = heatResistColumns;
			tableName = "热敏电阻实施";
		} else if (tableType == 'LayersOnOff') {
			tableColumns = layersOnOffColumns;
			tableName = "正样多层装配情况";
		} else if (tableType == 'StructuralAssembly') {
			tableColumns = structuralAssemblyColumns;
			tableName = "结构装配检测表";
		}
		var cb_success = function (data) {
			$('#otherTable').datagrid({
				data: data.rows,
				// title: tableName,
				columns: tableColumns,
				height: othis.getGridHeight(),
				emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"></div>',
				loadMsg: '正在加载数据...',
				rownumbers: true,
				singleSelect: true,
				striped: false,
				onLoadSuccess: function () {
					changeWidth('otherTable');
				}
			});
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			tableType: tableType,
			treeid: processTreeId
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.DataSearch', 'QueryDataSearchNoPage1', parmas, true, cb_success, cb_error);
	};
	//加载表格数据
	this.loadProductTable = function (record, processTreeId) {
		othis.resetTablePosition();
		var type = record.type; //表格类型 1：二、三级表 2：excel导入类型和质量统计数据
		if (type == '1') {
			othis.loadType1Table(record, processTreeId);
			othis.initTbrBtn(record, processTreeId);
		} else if (type == '2') {
			othis.showOtherTable();
			othis.loadType2Table(record, processTreeId);
		}
	};
	//重置表格容器的位置
	this.resetTablePosition = function () {
		$("#secondTableDiv").css('top', $("#diagramContainer").css("height"));
		$("#threeExcel").css('top', $("#diagramContainer").css("height"));
		$("#otherTableDiv").css('top', $("#diagramContainer").css("height"));
	};
	//获取下载链接
	this.getFileDownloadUrl = function (path, filename) {
		if (path === undefined) {
			path = "";
		}
		path = path.replace(/\\/g, "/");
		var url = fileHandlerUrl + "/file/download?filePath=" + encodeURIComponent(path) + "&fileName=" + encodeURIComponent(
			filename);
		return url;
	};
	//导出二级表excel
	this.exportSecondExcel = function (record, processTreeId) {
		var loading;
		var url = fileHandlerUrl + "/first/phase/export/second/excel";
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"treeId": processTreeId,
				"tableId": record.id
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				layer.close(loading);
			},
			failCallback: function (html, url) {
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});
	};
	//获取选中节点 选中类型的第一行数据的上传的三级表的excel名称
	this.getExcelName = function (record, processTreeId) {
		var result = "";
		var cb_success = function (data) {
			if (data.array.length == 0) {

			} else {
				result = data.array[0].filename;
			}
		};
		var cb_error = function () {

		};

		var parmas = {
			table_config_id: record.id,
			processTreeId: processTreeId,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		twxAjax('Thing.Fn.SecondTable', 'QueryTableData', parmas, false, cb_success, cb_error);
		return result;
	}
	//初始化按钮
	this.initTbrBtn = function (record, processTreeId) {
		//导出二级表按钮点击事件
		$('#product-quality-export').unbind("click").bind('click', function () {
			if (record.ctype == '3') {
				othis.exportThreeFile(record, processTreeId);
			} else {
				othis.exportSecondExcel(record, processTreeId);
			}
		});
	}

	//显示页面的提示信息，并且隐藏页面其他元素
	this.showMsg = function (msg) {
		$("#msg").text(msg).show();
		$('#productTypeDiv').hide();
		$('#secondTableDiv').hide();
		$('#threeExcel').hide();
		$('#otherTableDiv').hide();
		$('#tbr').hide();
	};

	//显示二级表
	this.showSecondTable = function () {
		$("#msg").hide();
		$('#productTypeDiv').show();
		$('#secondTableDiv').show();
		// $('#secondTable').datagrid('resize');
		$('#threeExcel').hide();
		$('#otherTableDiv').hide();
		$('#tbr').show();
	};

	//显示三级表excel
	this.showThreeExcel = function () {
		$("#msg").hide();
		$('#productTypeDiv').show();
		$('#secondTableDiv').hide();
		$('#threeExcel').show();
		$('#otherTableDiv').hide();
		$('#tbr').show();
	};

	//显示其他质量数据表
	this.showOtherTable = function () {
		$("#msg").hide();
		$('#productTypeDiv').show();
		$('#secondTableDiv').hide();
		$('#threeExcel').hide();
		$('#tbr').hide();
		$('#otherTableDiv').show();
	};

	//显示excel文件
	this.showExcelHtml = function (record, processTreeId) {
		var cb_success = function (data) {
			if (data.array.length == 0) {
				$("#threeExcel").empty();
				$("#threeExcel").append('<span style="color:red">数据加载中！</span>');
			} else {
				var filepath = data.array[0].filepath;
				$.ajax({
					type: "POST",
					url: fileHandlerUrl + "/table/excel/to/html?filepath=" + filepath,
					async: true,
					contentType: "application/x-www-form-urlencoded; charset=utf-8",
					success: function (data) {
						$("#threeExcel").empty();
						$("#threeExcel").append(data);
					}
				});
			}
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};

		var parmas = {
			table_config_id: record.id,
			processTreeId: processTreeId,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		twxAjax('Thing.Fn.SecondTable', 'QueryTableData', parmas, true, cb_success, cb_error);
	}
	//加载二、三级表类型的表数据
	this.loadType1Table = function (record, processTreeId) {
		if (record.ctype != '3') {
			othis.showSecondTable();
			new SecondTable(record, processTreeId, bomTree.selBomTreeId, othis.getGridHeight());
		} else {
			othis.showThreeExcel();
			othis.showExcelHtml(record, processTreeId);
		}
	};

	//初始化一般结构件的操作按钮
	this.initSpecial1TbrBtn = function (processTreeId) {
		$('#product-quality-upload').unbind('click').bind('click', function () {
			othis.uploadSpecial1ThreeFile(processTreeId);
		});

		//下载三级表模板按钮点击事件
		$('#product-quality-download').unbind("click").bind('click', function () {
			othis.downloadSpecial1ThreeFile();
		});

		//导出二级表按钮点击事件
		$('#product-quality-export').unbind("click").bind('click', function () {
			othis.exportSpecial1SecondExcel(processTreeId);
		});

	}
	//导出一般结构件二级表
	this.exportSpecial1SecondExcel = function (processTreeId) {
		var type = 6;
		var loading;
		var url = fileHandlerUrl + "/first/phase/export/excel2";
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"treeId": processTreeId,
				"type": type,
				"fi": mergeObj.firstIndex,
				"si": mergeObj.secIndex
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				layer.close(loading);
			},
			failCallback: function (html, url) {
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});

	}
	//显示一般结构件
	this.showSpecial1Tabel = function () {
		$("#msg").hide();
		$("#productTypeDiv").hide();
		$("#tbr").show();
		$("#threeExcel").hide();
		$("#otherTableDiv").hide();
		$("#secondTableDiv").show();
	}

	//一般结构件 合并单元格信息
	var mergeObj = {
		firstIndex: -1,
		secIndex: -1
	}

	//特殊处理的节点： 一般结构件
	this.loadSpecial1Tabel = function (processTreeId) {

		var cb_success = function (data) {
			var tableColumns = [];
			if (data.rows.length > 0) {
				var firstRow = data.rows[0];
				tableColumns = col6(firstRow['VAL1'], firstRow['VAL2']);
			} else {
				tableColumns = col6('', '');
			}
			$('#secondTable').datagrid({
				data: data.rows,
				// fit: true,
				columns: tableColumns,
				height: othis.getGridHeight(),
				emptyMsg: '<div style="color:red;font-size:14px;text-align:left;">数据加载中！</div>',
				loadMsg: '正在加载数据...',
				singleSelect: true,
				striped: false,
				onLoadSuccess: function (data) {
					var d = data.rows;
					var $datagrid = $('#secondTable');
					mergeObj.firstIndex = -1;
					mergeObj.secIndex = -1;
					if (d.length != 0) {
						for (var i = 0; i < d.length; i++) {
							var row = d[i];
							var ctype = row.VAL14;
							if (ctype == '1') {
								mergeObj.firstIndex = i;
								break;
							}
						}
						if (mergeObj.firstIndex != -1) {
							$datagrid.datagrid('insertRow', {
								index: mergeObj.firstIndex,
								row: {
									ROWNUM: '<span class="td-bold-left">一、扩热板、支架</span>'
								}
							});
							$datagrid.datagrid('mergeCells', {
								index: mergeObj.firstIndex,
								field: 'ROWNUM',
								colspan: 11
							});
						}
						for (var i = 0; i < d.length; i++) {
							var row = d[i];
							var ctype = row.VAL14;
							if (ctype == '2') {
								mergeObj.secIndex = i;
								break;
							}
						}
						if (mergeObj.secIndex != -1) {
							$datagrid.datagrid('insertRow', {
								index: mergeObj.secIndex,
								row: {
									ROWNUM: '<span class="td-bold-left">二、定位块、铲刮片等</span>'
								}
							});
							$datagrid.datagrid('mergeCells', {
								index: mergeObj.secIndex,
								field: 'ROWNUM',
								colspan: 11
							});
						}
					}
					$("#secondTableDiv .td-bold-left").each(function (i, n) {
						$(n).parent().css("text-align", "left");
					});

					changeWidth("secondTable");
				}
			});
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			type: 6,
			treeId: processTreeId
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.ProductQuality', 'QueryData', parmas, true, cb_success, cb_error);
	};

	this.loadTable = function (treeNode) {
		var processTreeId = treeNode.TREEID;
		var isSpecial1 = false;
		var nodename = treeNode.NODENAME;
		if ((treeNode.NODETYPE == 'leaf') || (treeNode.NODETYPE == 'dir') && (!treeNode.ISPARENT)) {
			if (nodename == '一般结构件') {
				othis.showSpecial1Tabel();
				othis.initSpecial1TbrBtn(processTreeId);
				othis.loadSpecial1Tabel(processTreeId);
				isSpecial1 = true;
			}
		}
		if (!isSpecial1) {
			othis.loadTypeSelect(processTreeId); //加载质量数据类型下拉框
		}
	}
};