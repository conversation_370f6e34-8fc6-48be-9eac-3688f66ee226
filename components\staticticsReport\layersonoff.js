var curInfo = {
	tableId: 'dataTable',
	tableType: '正样多层装配情况',
	rsId: parent.window.tjfxRsID,
	columns: [
		[{
			field: 'TJFX_ID',
			title: '统计分析ID',
			hidden: true
		}, {
			field: 'DATA_ID',
			title: 'MES DATAID',
			hidden: true
		}, {
			field: 'REF_DPID',
			title: '关联的数据包ID',
			hidden: true
		}, {
			field: 'MODELCODE',
			title: '型号代号',
			width: 100,
			align: 'center'
		}, {
			field: 'LAYERSNAME',
			title: '多层名称',
			width: 100,
			align: 'center'
		}, {
			field: 'LAYERSCODE',
			title: '多层代号',
			width: 100,
			align: 'center'
		}, {
			field: 'LS_NUMBER',
			title: '数量',
			width: 80,
			align: 'center'
		}, {
			field: 'AREA',
			title: '安装位置',
			width: 100,
			align: 'center'
		}, {
			field: 'GROUNDPOINT',
			title: '接地点序号',
			width: 120,
			align: 'center'
		}, {
			field: 'RESISTANCEREQUIRE',
			title: '接地阻值要求',
			width: 130,
			align: 'center'
		}, {
			field: 'CONTENT',
			title: '实测数据',
			width: 100,
			align: 'center'
		}, {
			field: 'CREATEDATE',
			title: '操作时间',
			width: 150,
			align: 'center'
		}]
	]
};

/** 重置搜索条件 */
var resetSearchCondition = function() {
	$('#layersname').textbox('setValue', '');
	$('#layerscode').textbox('setValue', '');
	$('#ls_number').textbox('setValue', '');
	$('#area').textbox('setValue', '');
	$('#groundpoint').textbox('setValue', '');
	$('#resistancerequire').textbox('setValue', '');
	$('#content').textbox('setValue', '');
};

var getFieldValue = function() {
	var param = {};
	param.type = curInfo.tableType;
	//数据包的ID
	param.rsId = curInfo.rsId;

	var layersname = $('#layersname').textbox('getValue');
	var layerscode = $('#layerscode').textbox('getValue');
	var ls_number = $('#ls_number').textbox('getValue');
	var area = $('#area').textbox('getValue');
	var groundpoint = $('#groundpoint').textbox('getValue');
	var resistancerequire = $('#resistancerequire').textbox('getValue');
	var content = $('#content').textbox('getValue');

	param.conditionData = {
		layersname: layersname,
		layerscode: layerscode,
		ls_number: ls_number,
		area: area,
		groundpoint: groundpoint,
		resistancerequire: resistancerequire,
		content: content
	};

	return param;
};
