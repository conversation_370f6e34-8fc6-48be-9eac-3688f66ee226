<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all" />
    <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css" />

    <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css" />
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css" />

    <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script>
    <script src="../../plugins/layui/layui.js"></script>
    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>

    <script type="text/javascript" src="../js/logUtil.js"></script>
</head>

<body style="padding: 15px">
    <form class="layui-form layui-form-pane" action="">
        <div class="layui-form-item">
            <label class="layui-form-label">文档类型</label>
            <div class="layui-input-block">
                <select name="FILE_TYPE" lay-verify="required" id="file_types">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">文档编号</label>
            <div class="layui-input-block">
                <input type="text" name="FILE_NUMBER" lay-verify="title" autocomplete="off" placeholder="请输入标题" class="layui-input" id="FILE_NUMBER" />
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">文档名称</label>
            <div class="layui-input-block">
                <input type="text" name="FILE_NAME" lay-verify="title" autocomplete="off" placeholder="请输入标题" class="layui-input" id="FILE_NAME" />
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">密级</label>
            <div class="layui-input-block">
                <select name="SECURITY_LEVEL" lay-verify="required" id="systems">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">上传文件</label>
            <div class="layui-input-block">
                <input type="file" name="uploadFile" id="uploadFile" />
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">问题描述</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入内容" class="layui-textarea" id="comment"></textarea>
            </div>
        </div>

        <div class="layui-form-item" style="display: none">
            <center>
                <button id="btn_add" class="layui-btn" lay-submit lay-filter="addData">提交</button>
                <button id="btn_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
                <!--<button class="layui-btn" lay-submit lay-filter="addData">提交</button>-->
                <!--<button class="layui-btn layui-btn-primary" type="reset">重置</button>-->
            </center>
        </div>
    </form>
</body>

<script>
    layui
        .config({
            base: "/DataPackageManagement/build/js/" //假设这是你存放拓展模块的根目录
        })
        .use(["form", "laydate", "table", "utils", "layer", "upload", "laypage"], function () {
            var form = layui.form;
            var laydate = layui.laydate;
            var table = layui.table;
            var utils = layui.utils;
            var layer = layui.layer;
            var upload = layui.upload;
            laydate.render({
                elem: "#updateTime", //或 elem: document.getElementById('test')、elem: lay('#test') 等
                position: "abolute",
                showBottom: false,
                trigger: "click"
            });

            //监听提交
            form.on("submit(addData)", function (data) {
                //layer.msg(JSON.stringify(data.field));
                // layer.msg(JSON.stringify(data.field));
                var param = data.field;

                var tableName = parent.selectedData.tabName;
                var type = "";
                if (tableName === "design_list_table") {
                    type = "DESIGN_DATA_RESULT";
                } else if (tableName === "craft_list_table") {
                    type = "CRAFT_DATA_RESULT";
                } else if (tableName === "processcontrol_list_table") {
                    type = "PROCESS_CONTROL_RESULT";
                } else if (tableName === "quanlitycontrol_list_table") {
                    type = "QUALITY_CONTROL_RESULT";
                }
                param.type = type;
                // param.NODECODE = parent.selectedData.node.TREEID;
                // param.NODENAME = parent.selectedData.node.TREEID;

                param.NODECODE = parent.$("#datapkgref").datagrid("getSelections")[0].ID;
                param.NODENAME = parent.$("#datapkgref").datagrid("getSelections")[0].ID;

                var fileObj = document.getElementById("uploadFile").files;
                var url = fileHandlerUrl + "/file/upload";
                var formFile = new FormData();
                for (var i = 0; i < fileObj.length; i++) {
                    formFile.append("file" + i, fileObj[i]);
                }
                $.ajax({
                    url: url,
                    data: formFile,
                    type: "post",
                    async: false,
                    cache: false, //上传文件无需缓存
                    processData: false, //用于对data参数进行序列化处理 这里必须false
                    contentType: false, //必须
                    success: function (result) {
                        var json = eval("(" + result + ")");
                        param.STATE_CHECK = "未确认";
                        param.DELIVERY_STATE = "提交";
                        param.GATHERING_METHOD = "手工采集";
                        param.SOURCE_SYSTEM = "";
                        param.FILEPATH = json.data.filePath;
                        var cb_success = function (data) {
                            layer.alert(
                                "上传成功",
                                {
                                    icon: 1
                                },
                                function (index) {
                                    //提示完成后，点击确定再刷新界面
                                    parent.reloadTable(tableName);

                                    logRecord("上传", "上传数据包");

                                    var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                                    parent.layer.close(index); //再执行关闭
                                }
                            );
                        };

                        var cb_error = function (xhr) {
                            layer.alert("上传失败!", {
                                icon: 2
                            });
                        };
                        //同步新增
                        twxAjax("publishMissionThing", "AddDataToCaptureManagementTable", param, false, cb_success, cb_error);
                    },
                    error: function (r) {}
                });

                return false;
            });
        });
    var $ = layui.$,
        active = {};

    $(function () {
        // $("#file_types").empty();
        var types = twxAjax(
            "publishMissionThing",
            "getDataFromDataDictionary",
            {
                type: "文件类别"
            },
            false,
            function (data) {
                for (var i = 0; i < data.rows.length; i++) {
                    $("#file_types").append('<option value="' + data.rows[i].ITEM_KEY + '">' + data.rows[i].ITEM_VALUE + "</option>");
                }
            }
        );

        var systems = twxAjax(
            "publishMissionThing",
            "getDataFromDataDictionary",
            {
                type: "密级"
            },
            false,
            function (data) {
                for (var i = 0; i < data.rows.length; i++) {
                    $("#systems").append('<option value="' + data.rows[i].ITEM_KEY + '">' + data.rows[i].ITEM_VALUE + "</option>");
                }
            }
        );

        var dstates = twxAjax(
            "publishMissionThing",
            "getDataFromDataDictionary",
            {
                type: "交付状态"
            },
            false,
            function (data) {
                for (var i = 0; i < data.rows.length; i++) {
                    $("#dstates").append('<option value="' + data.rows[i].ITEM_KEY + '">' + data.rows[i].ITEM_VALUE + "</option>");
                }
            }
        );
    });

    function dataURLtoBlob(dataurl) {
        var arr = dataurl.split(","),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], {
            type: mime
        });
    }
</script>
