function deleteRow(fileIndex) {
	var datas = table.cache['upload-table'];
	var newDatas = [];
	for (var i = 0; i < datas.length; i++) {
		if (fileIndex == datas[i].fileIndex) {
			deleteUploadFiles.push(fileIndex);
		} else {
			newDatas.push(datas[i]);
		}
	}
	table.cache['upload-table'] = newDatas;
	table.renderData('upload-table');
}

/**
 * 通过文件名称获取密级
 * @param {Object} fileName
 */
function getFileSecurity(fileName) {
	var securitys = ['公开', '内部', '秘密', '机密'];
	var fileSecurity = "内部";
	fileName = fileName.replace("（", "(").replace("）", ")");
	//最后一个括号里的内容
	var lastBracket = "";
	// 使用正则表达式匹配最后一个括号内的内容
	var regex = /（([^）]+)）.*$/;
	var match = fileName.match(regex);
	if (match) {
		lastBracket = match[1];
	} else {
		//如果文件名称中不存在括号 则使用整个文件名来匹配
		lastBracket = fileName;
	}
	for (var i = 0; i < securitys.length; i++) {
		if (lastBracket.indexOf(securitys[i]) > -1) {
			fileSecurity = securitys[i];
			break;
		}
	}
	return fileSecurity;
}

function updateRow(res, fileIndex) {
	// var datas = table.cache['upload-table'];
	var datas = table.getData('upload-table');
	for (var i = 0; i < datas.length; i++) {
		if (datas[i].fileIndex == fileIndex) {
			if (res.success) {
				datas[i].status = 1;
				datas[i].filePath = res.data.filePath;
			} else {
				datas[i].status = 2;
			}
			break;
		}
	}
	table.reloadData('upload-table', {
		data: datas,
		scrollPos: 'fixed'
	});
}


function renderUploadTable() {
	var cols = [
		[{
			width: 60,
			align: "left",
			title: '序号',
			type: 'numbers'
		}, {
			field: 'category',
			title: '类别',
			align: "center",
			edit: 'text',
			width: 200
		}, {
			field: 'name',
			title: '文件名称',
			edit: 'text',
			align: "center",
		}, {
			field: 'fileFormat',
			width: 88,
			align: "center",
			title: '文件类型'
		}, {
			field: 'filePath',
			hide: true,
		}, {
			field: 'security',
			width: 97,
			align: "center",
			title: '密级',
			templet: function(d) {
				return ' <button class="layui-btn layui-btn-primary security-dropdown" style="padding: 0 8px;height: 27px;line-height: 27px;">\
							<span>' + d.security + '</span>\
							<i class="layui-icon layui-icon-down layui-font-12"></i>\
						</button>';
			}
		}, {
			field: 'status',
			align: "center",
			width: 91,
			title: '状态',
			templet: function(d) {
				var html = "";
				if (d.status == 0) {
					html = '<span class="layui-btn layui-btn-xs layui-bg-blue" style="margin-top:2px;">等在上传</span>';
				} else if (d.status == 1) {
					html = '<span class="layui-btn layui-btn-xs" style="background-color:#16b777;margin-top:2px;">上传成功</span>';
				} else if (d.status == 2) {
					html = '<span class="layui-btn layui-btn-xs layui-btn-danger" style="margin-top:2px;">上传失败</span>';
				}
				return html;
			}
		}, {
			field: 'operation',
			align: "center",
			title: '操作',
			width: 180,
			fixed: "right",
			toolbar: '<div class="layui-clear-space">\
					<a class="layui-btn layui-btn-xs " lay-event="preview">预览</a>\
					<a class="layui-btn layui-btn-xs layui-bg-red" lay-event="delete">删除</a>\
				 </div>'
		}]
	];


	table.render({
		elem: '#upload-table',
		cols: cols,
		data: [],
		height: 395,
		page: false,
		done: function(res, curr, count, origin) {
			var options = this;
			// 获取当前行数据
			table.getRowData = function(elem) {
				var index = $(elem).closest("tr").data("index");
				return table.cache[options.id][index] || {};
			};

			// dropdown 方式的下拉选择
			dropdown.render({
				elem: ".security-dropdown",
				data: [{
						title: "公开",
						id: 0
					},
					{
						title: "内部",
						id: 1
					},
					{
						title: "秘密",
						id: 2
					},
					{
						title: "机密",
						id: 3
					}
				],
				click: function(obj) {
					var data = table.getRowData(this.elem);
					this.elem.find("span").html(obj.title);
					data.security = obj.title;
					var tableData = table.cache[options.id];
					table.reloadData(options.id, {
						data: tableData,
						scrollPos: 'fixed'
					});
					// table.renderData('upload-table');
				}
			});
		}
	});

	table.on('tool(upload-table)', function(obj) {
		var data = obj.data;
		if (obj.event === 'preview') {
			var filePath = data.filePath || '';
			if (filePath) {
				previewfile({
					FILEPATH: filePath,
					FILE_NAME: data.name,
					FILE_FORMAT: data.fileFormat,
					FILE_TYPE: "",
					GATHERING_METHOD: ""
				});
			} else {
				layer.alert('未发现文件地址！', {
					icon: 2
				});
			}
		} else if (obj.event === 'delete') {
			deleteRow(data.fileIndex);
		}
	});
}