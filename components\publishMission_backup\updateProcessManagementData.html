<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

    <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">


    <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script>
    <script src="../../plugins/layui/layui.js"></script>
    <script src="../js/util.js"></script>
</head>


<body style="padding: 15px">
<form class="layui-form layui-form-pane" action="">

    <div class="layui-form-item">
        <label class="layui-form-label">文件类别</label>
        <div class="layui-input-block" >
            <select name="file_types" lay-verify="required" id="file_types">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">采集方式</label>
        <div class="layui-input-block">
            <select name="taskStatus" lay-verify="required" >
                <option value=""></option>
                <option value="自动采集">自动采集</option>
                <option value="手工采集">手工采集</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">来源系统</label>
        <div class="layui-input-block">
            <select name="taskStatus" lay-verify="required" id="systems">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">交付状态</label>
        <div class="layui-input-block">
            <select name="taskStatus" lay-verify="required" id="dstates">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">修改时间</label>
        <div class="layui-input-block"> <!-- 注意：这一层元素并不是必须的 -->
            <input type="text" class="layui-input" id="updateTime">
        </div>
        <!--<div class="layui-form-mid layui-word-aux">辅助文字</div>-->
    </div>


    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="formTask">提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>



</body>

<script>
    layui.config({
        base: '/DataPackageManagement/build/js/' //假设这是你存放拓展模块的根目录
    }).use(['form','laydate','table','utils','layer'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var table = layui.table;
        var utils = layui.utils;
        var layer = layui.layer;
        laydate.render({
            elem: '#updateTime' //或 elem: document.getElementById('test')、elem: lay('#test') 等
        });

        //监听提交
        form.on('submit(formTask)', function (data) {
            layer.msg(JSON.stringify(data.field));
            var param = data.field;
            param.type = "PROCESS_CONTROL_DATA_LIST";
            twxAjax("publishMissionThing","AddDataToDataListTable",data.field);
            layer.alert("新增完成！");
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
            parent.layer.close(index); //再执行关闭
            return false;
        });

    });
    var $ = layui.$, active = {


    };

    $(function () {
        // $("#file_types").empty();
        var types = twxAjax("publishMissionThing","getDataFromDataDictionary",{type:"文件类别"}).data;
        for (var i = 0;i<types.length;i++){
            $("#file_types").append('<option value="'+types[i].ITEM_KEY+'">'+types[i].ITEM_VALUE+'</option>');
        }

        var systems = twxAjax("publishMissionThing","getDataFromDataDictionary",{type:"来源系统"}).data;
        for (var i = 0;i<systems.length;i++){
            $("#systems").append('<option value="'+systems[i].ITEM_KEY+'">'+systems[i].ITEM_VALUE+'</option>');
        }

        var dstates = twxAjax("publishMissionThing","getDataFromDataDictionary",{type:"交付状态"}).data;
        for (var i = 0;i<dstates.length;i++){
            $("#dstates").append('<option value="'+dstates[i].ITEM_KEY+'">'+dstates[i].ITEM_VALUE+'</option>');
        }
    });

</script>