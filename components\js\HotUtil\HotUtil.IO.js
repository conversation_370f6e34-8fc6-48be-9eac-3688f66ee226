/**
 * HotUtil.IO.js - Handsontable工具类 (导入/导出模块)
 *
 * 负责数据的导入和导出功能。
 */

/**
 * 导入PDF
 * @param {Object} treeNode
 * @param {string} url
 */
HotUtil.importPdf = function (treeNode, url) {
    layer.confirm("导入Pdf会覆盖现有表格，是否继续？", {
        icon: 3,
        title: '提示'
    }, function (index) {
        layer.close(index);
        var fileFlag = false;
        layer.open({
            title: "导入Pdf",
            type: 1,
            anim: false,
            openDuration: 200,
            isOutAnim: false,
            closeDuration: 200,
            shadeClose: false,
            // fixed: false,
            maxmin: false,
            resize: false, //不允许拉伸
            area: ['350px', '220px'],
            content: '<div id="importPdfContent" style="padding-top: 15px;padding-right: 15px;"></div>',
            btn: ['确认', '取消'],
            yes: function () {
                if (!fileFlag) {
                    layer.alert('请选择需要导入的pdf文件!', {
                        icon: 2
                    });
                    return false;
                }
                $('#uploadStart').click();
            },
            btn2: function () {
                layer.closeAll();
            },
            success: function () {
                var addTpl = $("#uploadHtml")[0].innerHTML;
                $("#importPdfContent").append(addTpl);
                form.render(null, 'uploadForm');
                var log = {};
                log.operation = "导入Pdf";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;

                var uploadInst = upload.render({
                    elem: '#uploadChoice',
                    url: url + '?thing=' + THING + '&id=' + treeNode.ID +
                        '&saveUser=' + sessionStorage.getItem("username"),
                    auto: false,
                    accept: 'file',
                    field: 'uploadFile',
                    exts: 'pdf|PDF',
                    bindAction: '#uploadStart',
                    dataType: "json",
                    choose: function (obj) {
                        fileFlag = true;
                        var o = obj.pushFile();
                        var filename = '';
                        for (var k in o) {
                            var file = o[k];
                            filename = file.name;
                        }
                        $("#selectedFile").show();
                        $("#selectedFileName").text(filename);
                        log.content = "在节点【" + treeNode.NAME + "（" +
                            treeNode.ID + "）】上导入了Pdf文件(" + filename + ")";
                    },
                    before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                        layer.load(); //上传loading
                    },
                    done: function (res, index, upload) {
                        layer.closeAll();
                        if (res.success) {
                            layer.msg("导入成功");
                            reloadTable(treeNode);
                            log.reqResult = 1;
                        } else {
                            log.reqResult = 0;
                            layer.alert(res.msg, {
                                icon: 2
                            });
                        }
                        addConfirmLog(log);
                    }
                });
            }
        });
    });
};

/**
 * 导入大文件压缩包
 * @param {Object} treeNode
 * @param {string} url
 */
HotUtil.importBigZip = function (treeNode, url) {
    layer.confirm("导入数据包会覆盖点该节点的原有数据，是否继续？", {
        icon: 3,
        title: '提示'
    }, function (index) {
        layer.close(index);
        var log = {};
        log.operation = "导入数据包";
        log.tablePid = treeNode['PID'];
        log.tableId = treeNode['ID'];
        log.reqResult = 0;
        oneBigFileUpload({
            url: url,
            layerTitle: "导入数据包",
            emptyMsg: "请选择需要导入的数据文件!",
            extraData: {
                username: sessionStorage.getItem("username"),
                tableId: treeNode['ID'],
                tablePId: treeNode['PID'],
                srcType: treeNode['TYPE'],
                treeId: treeNode['TREEID'],
                thing: THING
            },
            fileQueued: function (fileName) {
                log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] +
                    "）】上导入数据包(" +
                    fileName + ")";
            },
            uploadSuccess: function (res) {
                log.reqResult = 1;
                layer.msg("导入成功！");
                reloadTree(treeNode['PID'], res['rootId']);
            },
            uploadComplete: function () {
                addConfirmLog(log);
            }
        })

    });
};

/**
 * 导入Excel
 * @param {Object} treeNode
 * @param {string} url
 */
HotUtil.importExcel = function (treeNode, url) {
    // 在导入Excel操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "导入Excel", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '导入失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        // 乐观锁检查通过，继续执行导入Excel逻辑
        HotUtil._executeImportExcel(treeNode, url);
    });
};

/**
 * 执行导入Excel的具体逻辑（从原 importExcel 函数中提取）
 * @param {Object} treeNode
 * @param {string} url
 */
HotUtil._executeImportExcel = function (treeNode, url) {
    function importExcelLayer(index) {
        if (index) {
            layer.close(index);
        }
        var fileFlag = false;
        layer.open({
            title: "导入Excel",
            type: 1,
            anim: false,
            openDuration: 200,
            isOutAnim: false,
            closeDuration: 200,
            shadeClose: false,
            // fixed: false,
            maxmin: false,
            resize: false, //不允许拉伸
            area: ['350px', '220px'],
            content: '<div id="importExcelContent" style="padding-top: 15px;padding-right: 15px;"></div>',
            btn: ['确认', '取消'],
            yes: function () {
                if (!fileFlag) {
                    layer.alert('请选择需要导入的excel文件!', {
                        icon: 2
                    });
                    return false;
                }
                $('#uploadStart').click();
            },
            btn2: function () {
                layer.closeAll();
            },
            success: function () {
                var addTpl = $("#uploadHtml")[0].innerHTML;
                $("#importExcelContent").append(addTpl);
            }
        });
        form.render(null, 'uploadForm');

        var log = {};
        log.operation = "导入Excel";
        log.tablePid = treeNode.PID;
        log.tableId = treeNode.ID;

        var uploadInst = upload.render({
            elem: '#uploadChoice',
            url: url + '?thing=' + THING + '&id=' + treeNode.ID + '&saveUser=' + sessionStorage
                .getItem("username"),
            auto: false,
            accept: 'excel', // 指定只接受excel类型文件
            acceptMime: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // 限制文件浏览器只显示excel文件
            field: 'uploadFile',
            exts: 'xls|xlsx',
            bindAction: '#uploadStart',
            dataType: "json",
            choose: function (obj) {
                fileFlag = true;
                var o = obj.pushFile();
                var filename = '';
                for (var k in o) {
                    var file = o[k];
                    filename = file.name;
                }
                $("#selectedFile").show();
                $("#selectedFileName").text(filename);
                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导入了Excel文件(" +
                    filename + ")";
            },
            before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                layer.load(1, { content: '正在上传文件...' }); //上传loading
            },
            done: function (res, index, upload) {
                layer.closeAll();
                if (res.success) {
                    layer.msg("导入成功");
                    reloadTable(treeNode);
                    log.reqResult = 1;
                } else {
                    log.reqResult = 0;
                    layer.alert(res.msg, {
                        icon: 2
                    });
                }
                addConfirmLog(log);
            }
        });
        if (device.ie && device.ie < 10) {
            $("input[name='uploadFile']").change(function () {
                var filename = $(this).val();
                filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
                $("#selectedFile").show();
                $("#selectedFileName").text(filename);
            });
        }

    }

    if (treeNode.HTML_DATA) {
        if (treeNode.HTML_DATA.indexOf('lock="true"') > -1) {
            layer.alert('表格已有部分内容锁定，不可导入！', {
                icon: 2
            });
        } else {
            layer.confirm("导入Excel会覆盖现有表格，并且已经上传的图片会被删除，是否继续？", {
                icon: 3,
                title: '提示'
            }, function (index) {
                importExcelLayer(index);
            });
        }
    } else {
        importExcelLayer();
    }
};

/**
 * 导出PDF
 * @param {Object} treeNode
 * @param {string} url
 */
HotUtil.exportPdf = function (treeNode, url) {
    //日志记录
    var log = {};
    log.operation = "导出PDF";
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;
    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出了PDF文件";
    var loading;
    $.fileDownload(url, {
        httpMethod: 'POST',
        data: {
            "id": treeNode.ID,
            "thing": THING
        },
        prepareCallback: function (url) {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function (url) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function (url) {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
        },
        failCallback: function (html, url) {
            var msg = '导出失败！表格中可能存在多余的合并单元格，请处理之后重试。';
            log.reqResult = 0;
            log.content = log.content + ",报错：" + msg;
            addConfirmLog(log);
            layer.close(loading);
            layer.alert(msg, {
                icon: 2
            });
        }
    });
};

/**
 * 导出Excel
 * @param {Object} treeNode
 * @param {string} url
 */
HotUtil.exportExcel = function (treeNode, url) {
    //日志记录
    var log = {};
    log.operation = "导出Excel";
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;
    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出了Excel文件";

    var loading;
    $.fileDownload(url, {
        httpMethod: 'POST',
        data: {
            "id": treeNode.ID,
            "thing": THING
        },
        prepareCallback: function (url) {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function (url) {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function (url) {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
        },
        failCallback: function (html, url) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出失败！！");
        }
    });
};

/**
 * 导出所有图片
 * @param {Object} treeNode
 * @param {string} url
 */
HotUtil.exportImg = function (treeNode, url) {
    //日志记录
    var log = {};
    log.operation = "下载所有照片";
    log.tablePid = treeNode.PID;
    log.tableId = treeNode.ID;
    log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上下载所有照片";

    var loading;
    $.fileDownload(url, {
        httpMethod: 'POST',
        data: {
            "id": treeNode.ID,
            "thing": THING
        },
        prepareCallback: function (url) {
            loading = layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        abortCallback: function (url) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出异常！！");
        },
        successCallback: function (url) {
            log.reqResult = 1;
            addConfirmLog(log);
            layer.close(loading);
        },
        failCallback: function (html, url) {
            log.reqResult = 0;
            addConfirmLog(log);
            layer.close(loading);
            layer.msg("导出失败！！");
        }
    });
};