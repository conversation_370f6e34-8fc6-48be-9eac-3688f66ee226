var userThingName = 'Thing.Fn.SystemManagement';
var tableName = "userTable";
var userInfoHtml = '';
//分页信息
var pageOptions = {
	pageSize: 20,
	pageNumber: 1
};

var role = sessionStorage.getItem('rolename');
if (role.indexOf('系统管理员') > -1) {
	$("#roleTable_tb").remove();
	$("#modelTable_tb").remove();
	$("#model").remove();
} else if (role.indexOf('安全管理员') > -1) {
	$("#addUser_linkBtn").remove();
	$("#deleteUser_linkBtn").remove();
	$("#syncBpmUser_linkBtn").remove();
	$("#downloadUser_linkBtn").remove();
	$("#downloadTpl_linkBtn").remove();
	$("#import_linkBtn").remove();
	$("#editUser_linkBtn").html('<i class="layui-icon">&#xe642;</i> 修改用户密级');
}
$(document).ready(function () {
	// var htmlobj = $.ajax({url:"tpl/userInfo.html",async:false});
	userInfoHtml = document.getElementById('userInfoForm').innerHTML;
});
layui.config({
	base: '../../../plugins/layui_exts/'
}).extend({
	xmSelect: 'xm-select'
});

//搜索用户
var initSearchBtn = function () {
	$('#searchUser_linkBtn').bind('click', function () {
		// 搜索时重置页码为1，确保能正确显示搜索结果
		pageOptions.pageNumber = 1;
		initUserData();
	});
};

//添加用户
var initAddBtn = function (layui) {
	var layer = layui.layer,
		$ = layui.$,
		form = layui.form;
	$('#addUser_linkBtn').bind('click', function () {
		layer.open({
			title: '新增用户',
			type: 1,
			btn: ['新增', '重置', '关闭'],
			content: '<div id="addContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			area: ['685px', '400px'],
			anim: false,
			isOutAnim: false,
			resize: false,
			yes: function () {
				$('#btn_submit').click();
			},
			btn2: function () {
				$('#btn_reset').click();
				return false;
			},
			btn3: function () {
				return true;
			},
			success: function (layero, index) {

				$('#addContent').append(userInfoHtml);

				renderSelect("securitylevel", '密级');
				renderSelect("unit", '单位');
				renderSelect("department", '部门');
				renderSelect("job", '岗位');

				form.render();
			}
		});
	});
};

function renderSelect(dom, type) {
	var cb_success = function (data) {
		if (type == "密级") {
			if (role.indexOf('系统管理员') > -1) {
				$('#securitylevel').attr("disabled", "disabled");
			} else if (role.indexOf('安全管理员') > -1) {

			}
		}
		for (var i = 0; i < data.rows.length; i++) {
			var row = data.rows[i];
			$('#' + dom).append("<option value='" + row.KEY + "'>" + row
				.NAME + "</option>");
		}
	};
	twxAjax('Thing.Fn.SystemDic', 'GetDicDataByDicType', {
		typename: type
	}, false, cb_success);
}

//编辑用户
var initEditBtn = function (layui) {
	var layer = layui.layer,
		form = layui.form;
	$('#editUser_linkBtn').bind('click', function () {

		var sels = $('#userTable').datagrid('getSelections');
		if (sels.length == 0) {
			layer.msg('请选择待编辑的数据...', {
				icon: 2,
				anim: 6
			});
			return;
		}

		layer.open({
			title: '编辑用户',
			type: 1,
			btn: ['保存', '重置', '关闭'],
			content: '<div id="addContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			area: ['685px', '400px'],
			anim: false,
			isOutAnim: false,
			resize: false,
			yes: function () {
				$('#btn_update').click();
			},
			btn2: function () {
				$('#btn_reset').click();
				form.val('userinfo', {
					userid: sels[0].USER_ID,
					username: sels[0].USER_NAME,
					fullname: sels[0].USER_FULLNAME,
					password: sels[0].USER_PASSWORD,
					confirmpwd: sels[0].USER_PASSWORD,
					sex: sels[0].USER_SEX,
					phone: sels[0].USER_PHONE,
					ip: sels[0].USER_IP,
					unit: sels[0].USER_UNIT,
					department: sels[0].USER_DEPARTMENT,
					job: sels[0].USER_JOB,
					email: sels[0].USER_EMAIL,
					usersecuritylevel: sels[0].USER_SECURITYLEVEL,
					code: sels[0].USER_CODE,
					workno: sels[0].USER_WORKNO
					// modelList: sels[0].USER_MODEL?sels[0].USER_MODEL.split(','):[]
					// modelList: sels[0].USER_MODEL
				});
				// renderModelSlectList(sels[0].USER_MODEL ? sels[0].USER_MODEL.split(',') : []);

				return false;
			},
			btn3: function () {
				return true;
			},
			success: function (layero, index) {

				$('#addContent').append(userInfoHtml);

				if (role.indexOf('系统管理员') > -1) {
					$('#securitylevel').attr("disabled", "disabled");
				} else if (role.indexOf('安全管理员') > -1) {
					$('input[name=fullname]').attr('disabled', 'disabled');
					$('input[name=password]').attr('disabled', 'disabled');
					$('input[name=confirmpwd]').attr('disabled', 'disabled');
					$('input[name=sex]').attr('disabled', 'disabled');
					$('input[name=ip]').attr('disabled', 'disabled');
					$('#unit').attr("disabled", "disabled");
					$('#department').attr("disabled", "disabled");
					$('#job').attr("disabled", "disabled");
					$('input[name=code]').attr('disabled', 'disabled');
					$('input[name=phone]').attr('disabled', 'disabled');
					$('input[name=email]').attr('disabled', 'disabled');
					$('input[name=workno]').attr('disabled', 'disabled');
				}
				$('input[name=username]').attr('disabled', 'disabled');

				renderSelect("securitylevel", '密级');
				renderSelect("unit", '单位');
				renderSelect("department", '部门');
				renderSelect("job", '岗位');

				// renderModelSlectList(sels[0].USER_MODEL ? sels[0].USER_MODEL.split(',') : []);

				form.render();

				form.val('userinfo', {
					userid: sels[0].USER_ID,
					username: sels[0].USER_NAME,
					fullname: sels[0].USER_FULLNAME,
					password: sels[0].USER_PASSWORD,
					confirmpwd: sels[0].USER_PASSWORD,
					sex: sels[0].USER_SEX,
					phone: sels[0].USER_PHONE,
					ip: sels[0].USER_IP,
					unit: sels[0].USER_UNIT,
					department: sels[0].USER_DEPARTMENT,
					job: sels[0].USER_JOB,
					email: sels[0].USER_EMAIL,
					usersecuritylevel: sels[0].USER_SECURITYLEVEL,
					code: sels[0].USER_CODE,
					workno: sels[0].USER_WORKNO
					// modelList: sels[0].USER_MODEL
				});
				$("#userForm [name='username']").attr('old', sels[0].USER_NAME);
				$("#userForm [name='fullname']").attr('old', sels[0].USER_FULLNAME);
				$("#userForm [name='password']").attr('old', sels[0].USER_PASSWORD);
				$("#userForm [name='confirmpwd']").attr('old', sels[0].USER_PASSWORD);
				$("#userForm [name='sex']").attr('old', sels[0].USER_SEX);
				$("#userForm [name='phone']").attr('old', sels[0].USER_PHONE);
				$("#userForm [name='ip']").attr('old', sels[0].USER_IP);
				$("#userForm [name='unit']").attr('old', sels[0].USER_UNIT);
				$("#userForm [name='department']").attr('old', sels[0].USER_DEPARTMENT);
				$("#userForm [name='job']").attr('old', sels[0].USER_JOB);
				$("#userForm [name='code']").attr('old', sels[0].USER_CODE);
				$("#userForm [name='workno']").attr('old', sels[0].USER_WORKNO);
				$("#userForm [name='email']").attr('old', sels[0].USER_EMAIL);
				$("#userForm [name='usersecuritylevel']").attr('old', sels[0]
					.USER_SECURITYLEVEL);
				// $("#userForm [name='modelList']").attr('old', sels[0].USER_MODEL);
			}
		});
	});
};

//初始化删除按钮
var initDeleteBtn = function (layui) {
	var layer = layui.layer;
	$('#deleteUser_linkBtn').bind('click', function () {
		var sels = $('#userTable').datagrid('getSelections');
		if (sels.length == 0) {
			layer.msg('请选择待删除的用户...', {
				icon: 2,
				anim: 6
			});
			return;
		}

		layer.confirm('确认删除选中用户吗?', {
			icon: 3,
			title: '提示'
		}, function (index) {
			var cb_success = function (data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					logRecord('删除', '用户管理-删除用户(ID：' + sels[0].USER_ID + '、用户名：' + sels[0]
						.USER_NAME + '、全名：' + sels[0].USER_FULLNAME +
						')', 0);
					return;
				}
				//刷新用户表个
				initUserData();
				layer.closeAll();
				logRecord('删除', '用户管理-删除用户(ID：' + sels[0].USER_ID + '、用户名：' + sels[0]
					.USER_NAME + '、全名：' + sels[0].USER_FULLNAME +
					')', 1);
				layer.msg('删除成功');
			};
			var cb_error = function () { };
			var param = {};
			param.userid = sels[0].USER_ID;
			// layer.close(index);
			twxAjax(userThingName, 'DeleteUser', param, true, cb_success, cb_error);
		});
	});
};

//初始化用户表格
var initUserComp = function () {
	$('#' + tableName).datagrid({
		data: [],
		singleSelect: true,
		striped: true,
		rownumbers: true,
		toolbar: '#userTable_tb',
		pagination: true,
		fit: true,
		columns: [
			[{
				field: 'ck',
				checkbox: true
			}, {
				field: 'USER_ID',
				title: '用户ID',
				width: 100,
				hidden: true
			}, {
				field: 'USER_NAME',
				title: '用户名称',
				width: 150
			}, {
				field: 'USER_FULLNAME',
				title: '全名',
				width: 150
			}, {
				field: 'USER_WORKNO',
				title: '工号',
				width: 100
			}, {
				field: 'USER_SECURITYLEVELNAME',
				title: '密级',
				width: 40
			}, {
				field: 'USER_SEX',
				title: '性别',
				width: 40
			}, {
				field: 'USER_UNIT',
				title: '单位',
				width: 60
			}, {
				field: 'USER_DEPARTMENT',
				title: '部门',
				width: 60
			}, {
				field: 'USER_JOB',
				title: '岗位',
				width: 80
			},
			// {
			// 	field: 'USER_PHONE',
			// 	title: '电话',
			// 	width: 130
			// }, 
			{
				field: 'USER_CODE',
				title: '关联用户名',
				width: 150
			},
			{
				field: 'USER_IP',
				title: 'IP地址',
				width: 100
			},
			{
				field: 'USER_SIGN',
				title: '签章',
				width: 100,
				formatter: function (value, rec) {
					if (value) {
						var path = '/File' + value;
						return '<img src="' + path + '" width=65 height=28 />';
					} else {
						return "";
					}
				}
			},
			{
				field: 'option',
				title: '操作',
				width: 100,
				formatter: function (value, rec) {
					var userId = rec.USER_ID;
					return '<button type="button" class="layui-btn layui-btn-xs" onclick="uploadSign(' + userId + ')">上传签章</button>';
				}
			}
			]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...',
		onClickRow: function (rowIndex, rowData) {
			initUGRoleData();
			modelMgr.initUGModelData();
		}
	});
};

function uploadSign(userId) {
	var fileFlag = false;
	layer.open({
		title: "上传签章",
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['350px', '320px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要上传的签章!', {
					icon: 2
				});
				return false;
			}
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = '';
			addTpl = $("#uploadHtml")[0].innerHTML;
			$("#uploadContent").append(addTpl);
		}
	});

	form.render(null, 'uploadForm');

	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + '/file/upload',
		auto: false,
		accept: 'images',
		field: 'file',
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;

			var files = obj.pushFile();
			obj.preview(function (index, file, result) {
				$("#selectedFile").show();
				$("#selectedFileName").text(file.name);
				$("#previewFile").show();
				$("#previewImg").attr("src", result);
			});

		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			// layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			twxAjax("Thing.Fn.SystemManagement", 'UploadUserSign', {
				userId: userId,
				path: res.data.filePath,
				format: res.data.fileFormat
			}, true, function (res) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					initUserData();
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}, function (xhr, textStatus, errorThrown) {
				layer.alert('请求出错！', {
					icon: 2
				});
			});
		}
	});
	if (device.ie && device.ie < 10) {
		$("input[name='uploadFile']").change(function () {
			var filename = $(this).val();
			filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		});
	}
}
var layer, form, upload, device;
//初始化工具栏的按钮
var initToolbarBtns = function () {
	layui.use(['layer', 'form', 'jquery', 'upload'], function () {
		layer = layui.layer,
			form = layui.form,
			upload = layui.upload,
			device = layui.device,
			$ = layui.$;

		form.verify({
			userNameRepeat: function (value, item) {
				if (value.length == 0) {
					return '用户名不能为空';
				}
				var oldName = $(item).attr('old');
				var flag = true;
				var data = $('#userTable').datagrid('getData').rows;
				for (var i = 0; i < data.length; i++) {
					var d = data[i];
					if (value == d.USER_NAME) {
						flag = false;
					}
				}
				if (value == oldName) {
					flag = true;
				}
				if (!flag) {
					return '该用户名已存在！';
				}
			},
			userFullNameRepeat: function (value, item) {
				if (value.length == 0) {
					return '全名不能为空';
				}
				var oldName = $(item).attr('old');
				var flag = true;
				var data = $('#userTable').datagrid('getData').rows;
				for (var i = 0; i < data.length; i++) {
					var d = data[i];
					if (value == d.USER_FULLNAME) {
						flag = false;
					}
				}
				if (value == oldName) {
					flag = true;
				}
				if (!flag) {
					return '该用户全名已存在！';
				}
			},
			confirmpwd: function (value) {
				var pwdVal = $('input[name=password]').val();
				if (value !== pwdVal) {
					return '两次输入的密码不一致!';
				}
			},
			phone: function (value) {
				if (value !== '') {
					if (!(/^1[3456789]\d{9}$/.test(value))) {
						return '电话号码错误';
					}
				}
			},
			email: function (value) {
				if (value !== '') {
					var reg = new RegExp(
						"^[a-z0-9A-Z]+[- | a-z0-9A-Z . _]+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-z]{2,}$"
					)
					if (!reg.test(value)) {
						return '邮箱格式错误';
					}
				}
			}
		});

		form.on('submit(formVerify)', function (data) {
			var dataField = data.field;
			var param = dataField;
			var securitylevel = $('#securitylevel  option:selected').text();
			var logContent = '用户管理-新增用户(用户名：' + param.username + '、全名：' + param
				.fullname + '、性别：' + param.sex + '、密级：' +
				securitylevel + '、电话：' + param.phone + '、IP地址：' + param.ip + '、关联用户名：' + param.code +
				'、工号：' + param.workno +
				'、邮箱：' +
				param.email + '、单位：' + param.unit + '、部门：' + param.department + '、岗位：' + param.job +
				')'
			var cb_success = function (data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					logRecord('新增', logContent, 0);
					return;
				}
				//刷新用户表个
				initUserData();
				layer.closeAll();
				logRecord('新增', logContent, 1);
				layer.msg('保存成功');
			};
			var cb_error = function () { };
			twxAjax(userThingName, 'addUser', param, true, cb_success, cb_error);
			return false;
		});

		form.on('submit(updateVerify)', function (data) {
			var dataField = data.field;
			var param = dataField;
			var securitylevel = $('#securitylevel  option:selected').text();
			var old = {};
			old.username = $("#userForm [name='username']").attr('old');
			old.fullname = $("#userForm [name='fullname']").attr('old');
			old.password = $("#userForm [name='password']").attr('old');
			old.sex = $("#userForm [name='sex']").attr('old');
			old.phone = $("#userForm [name='phone']").attr('old');
			old.email = $("#userForm [name='email']").attr('old');
			old.ip = $("#userForm [name='ip']").attr('old');
			old.unit = $("#userForm [name='unit']").attr('old');
			old.department = $("#userForm [name='department']").attr('old');
			old.job = $("#userForm [name='job']").attr('old');
			old.code = $("#userForm [name='code']").attr('old');
			old.workno = $("#userForm [name='workno']").attr('old');
			old.securitylevel = $('#securitylevel  option[value=' + $(
				"#userForm [name='usersecuritylevel']").attr('old') +
				']').text();
			var logContent = '用户管理-用户(ID：' + param.userid + '、用户名：' + old.username + '、全名：' +
				old.fullname + '、性别：' + old.sex + '、密级：' + old.securitylevel +
				'、电话：' + old.phone + '、地址：' + old.ip + '、单位：' + old.unit + '、部门：' + old.department + '、关联用户名：' + old.code + '、工号：' + old.workno + '、岗位：' + old.job + '、邮箱：' +
				old.email +
				')更新为(用户名：' + param.username + '、全名：' + param.fullname + '、性别：' +
				param.sex + '、密级：' + securitylevel + '、电话：' + param.phone + '、IP地址：' +
				param.ip + '、单位：' + param.unit + '、部门：' + param.department + '、岗位：' + param.job + '、关联用户名：' + param.code + '、工号：' + param.workno + '、邮箱：' +
				param.email +
				')';
			var cb_success = function (data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					logRecord('编辑', logContent, 0);
					return;
				}
				//刷新用户表个
				initUserData();
				layer.closeAll();
				logRecord('编辑', logContent, 1);
				layer.msg('更新成功');
			};
			var cb_error = function () { };
			twxAjax(userThingName, 'UpdateUser', param, true, cb_success, cb_error);
			return false;
		});
		initSearchBtn();
		initAddBtn(layui);
		initEditBtn(layui);
		initDeleteBtn(layui);
		initBtn(layui);
		initSyncBpmUserBtn(layui);
	});
};
var initBtn = function (layui) {

	$('#downloadUser_linkBtn').bind('click', function () {
		var url = fileHandlerUrl + "/system/export/user";
		$.fileDownload(url, {
			httpMethod: 'POST',
			prepareCallback: function (url) { },
			abortCallback: function (url) {
				layer.alert("导出失败！", {
					icon: 2
				});
			},
			successCallback: function (url) { },
			failCallback: function (html, url) {
				layer.alert("导出失败！", {
					icon: 2
				});
			}
		});
	});

	$('#downloadTpl_linkBtn').bind('click', function () {
		var url = fileHandlerUrl + "/system/export/user/tpl";
		$.fileDownload(url, {
			httpMethod: 'POST',
			prepareCallback: function (url) { },
			abortCallback: function (url) {
				layer.alert("下载失败！", {
					icon: 2
				});
			},
			successCallback: function (url) { },
			failCallback: function (html, url) {
				layer.alert("下载失败！", {
					icon: 2
				});
			}
		});
	});

	$('#import_linkBtn').bind('click', function () {
		var fileFlag = false;
		var form = layui.form,
			upload = layui.upload,
			device = layui.device;
		layer.open({
			title: "批量导入用户",
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ['350px', '220px'],
			content: '<div id="importContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['确认', '取消'],
			yes: function () {
				if (!fileFlag) {
					layer.alert('请选择需要导入的xlsx文件!', {
						icon: 2
					});
					return false;
				}
				$('#importStart').click();
			},
			btn2: function () {
				layer.closeAll();
			},
			success: function () {
				var addTpl = $("#importHtml")[0].innerHTML;
				$("#importContent").append(addTpl);
			}
		});
		form.render(null, 'importForm');

		var uploadInst = upload.render({
			elem: '#importChoice',
			url: fileHandlerUrl + '/system/import/user',
			auto: false,
			accept: 'file',
			field: 'uploadFile',
			exts: 'xlsx',
			bindAction: '#importStart',
			dataType: "json",
			choose: function (obj) {
				fileFlag = true;
				var o = obj.pushFile();
				var filename = '';
				for (var k in o) {
					var file = o[k];
					filename = file.name;
				}
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			},
			before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
				layer.load(); //上传loading
			},
			done: function (res, index, upload) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					initUserData();
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}
		});
		if (device.ie && device.ie < 10) {
			$("input[name='uploadFile']").change(function () {
				var filename = $(this).val();
				filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			});
		}
	});
}

//初始化分页组件
var initPagination = function (data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function () {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function (pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function (pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function (pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function (pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};

//分页查询数据
var queryDataByPage = function (pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + tableName).datagrid('loading');
	initTotalRecords();
	var cb_success = function (res) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#' + tableName).datagrid('loadData', res.data);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#' + tableName).datagrid('loaded');
	};
	var cb_error = function () {
		layui.use(['layer'], function () {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	var fullname = $("#searchUser_fullname").val();
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax(userThingName, 'QueryUserPage', {
		fullname: fullname,
		pageSize: pageSize,
		pageNumber: pageNumber
	}, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function () {
	//initPagination('logtable',{total:data.rows[0].COUNT});
	$('#' + tableName).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
}

//初始化全部的记录条数
var initTotalRecords = function () {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function (res) {
		pageLoadFlag = true;
		totalRecords = res.data;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function () { };
	var fullname = $("#searchUser_fullname").val();
	twxAjax(userThingName, 'QueryUserCount', {
		fullname: fullname,
	}, true, cb_success, cb_error);
};


//初始化行号
var initLineNumbers = function () {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function (index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};


//初始化表格数据
var initUserData = function () {
	//初始化分页组件
	initPagination({
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
};

//初始化BPM用户工号同步按钮
var initSyncBpmUserBtn = function (layui) {
	var layer = layui.layer;

	$('#syncBpmUser_linkBtn').bind('click', function () {
		// 显示确认对话框

		layer.confirm('此操作将同步BPM系统的用户工号到本地系统，会修改数据库中的用户工号数据。<br><br>确定要继续吗？', { icon: 3 }, function (index) {
			layer.close(index);
			// 执行同步操作
			executeBpmUserSync(layui);
		}, function () {
			layer.close(index);
		});
	});
};

//执行BPM用户工号同步
var executeBpmUserSync = function (layui) {
	var layer = layui.layer;

	// 显示加载提示

	var loadingIndex = layer.msg('正在同步BPM用户工号，请稍候...', {
		icon: 16,
		shade: 0.01
	});;

	// 调用ThingWorx同步服务
	var cb_success = function (data) {
		layer.close(loadingIndex);

		if (data && typeof data === 'object') {
			if (data.success) {
				// 同步成功，显示结果
				showSyncResult(layui, data);
				// 刷新用户表格数据
				initUserData();
			} else {
				// 同步失败
				layer.alert('同步失败：' + (data.msg || '未知错误'), {
					icon: 2,
					title: '同步失败'
				});
			}
		} else {
			layer.alert('服务返回数据格式异常，请检查ThingWorx服务是否正常', {
				icon: 2,
				title: '数据异常'
			});
		}
	};

	var cb_error = function (_, textStatus) {
		layer.close(loadingIndex);
		var errorMsg = textStatus || '未知网络错误';
		layer.alert('网络请求失败，请检查ThingWorx服务器连接状态<br><br>错误信息：' + errorMsg, {
			icon: 2,
			title: '网络错误',
			area: ['400px', '200px']
		});
	};

	// 调用ThingWorx服务
	twxAjax('Thing.Fn.BPM', 'SyncBpmUserWorkNo', {}, true, cb_success, cb_error);
};

//显示同步结果
var showSyncResult = function (layui, syncResult) {
	var layer = layui.layer;

	// 构建tab内容
	var tabContent = buildTabContent(syncResult.data);

	// 使用layer.tab创建tab界面
	layer.tab({
		area: ['900px', '650px'],
		tab: [
			{
				title: '✓ 成功同步 (' + (syncResult.data.successCount || 0) + ')',
				content: tabContent.success
			},
			{
				title: '✗ 同步失败 (' + (syncResult.data.failureCount || 0) + ')',
				content: tabContent.failure
			},
			{
				title: '⚠ 跳过处理 (' + (syncResult.data.skipCount || 0) + ')',
				content: tabContent.skip
			}
		],
		success: function () {
			// tab创建成功后的回调，可以在这里添加刷新按钮等功能
			layer.msg('同步结果展示完成，可切换tab查看详细信息');
		}
	});
};

//构建tab内容
var buildTabContent = function (data) {
	var style = '<style>' +
		'.sync-stats-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }' +
		'.sync-stats-table th, .sync-stats-table td { border: 1px solid #e6e6e6; padding: 12px; text-align: center; }' +
		'.sync-stats-table th { background-color: #f2f2f2; font-weight: bold; }' +
		'.sync-success { color: #5FB878; font-weight: bold; }' +
		'.sync-failure { color: #FF5722; font-weight: bold; }' +
		'.sync-skip { color: #FFB800; font-weight: bold; }' +
		'.sync-user-list { max-height: 400px; overflow-y: auto; padding: 15px; background-color: #fafafa; border-radius: 4px; }' +
		'.sync-user-item { padding: 8px 0; border-bottom: 1px solid #eeeeee; line-height: 1.5; }' +
		'.sync-user-item:last-child { border-bottom: none; }' +
		'.sync-empty { text-align: center; color: #999; padding: 40px 0; font-size: 14px; }' +
		'</style>';

	// 统计信息表格HTML
	var statsTable = '<div style="padding: 15px;">' + style +
		'<h3 style="margin-top: 0; margin-bottom: 20px;">BPM用户工号同步结果</h3>' +
		'<table class="sync-stats-table">' +
		'<thead><tr>' +
		'<th>总处理用户数</th>' +
		'<th class="sync-success">成功同步</th>' +
		'<th class="sync-failure">同步失败</th>' +
		'<th class="sync-skip">跳过处理</th>' +
		'</tr></thead>' +
		'<tbody><tr>' +
		'<td>' + (data.totalRecords || 0) + '</td>' +
		'<td class="sync-success">' + (data.successCount || 0) + '</td>' +
		'<td class="sync-failure">' + (data.failureCount || 0) + '</td>' +
		'<td class="sync-skip">' + (data.skipCount || 0) + '</td>' +
		'</tr></tbody></table>';

	return {
		success: statsTable + buildUserListHtml(data.successList, 'success') + '</div>',
		failure: statsTable + buildUserListHtml(data.failureList, 'failure') + '</div>',
		skip: statsTable + buildUserListHtml(data.skipList, 'skip') + '</div>'
	};
};

//构建用户列表HTML
var buildUserListHtml = function (userList, type) {
	if (!userList || userList.length === 0) {
		return '<div class="sync-user-list"><div class="sync-empty">暂无数据</div></div>';
	}

	// HTML转义函数，防止XSS攻击
	var escapeHtml = function (text) {
		if (!text) return '';
		return text.toString()
			.replace(/&/g, '&amp;')
			.replace(/</g, '&lt;')
			.replace(/>/g, '&gt;')
			.replace(/"/g, '&quot;')
			.replace(/'/g, '&#39;');
	};

	var html = '<div class="sync-user-list">';
	for (var i = 0; i < userList.length; i++) {
		var user = userList[i];
		if (!user) continue; // 跳过空对象

		html += '<div class="sync-user-item">';

		if (type === 'success') {
			html += '<span class="sync-success">✓</span> ';
			html += '<strong>' + escapeHtml(user.username) + '</strong>';
			html += ' → 工号: <strong>' + escapeHtml(user.workno) + '</strong>';
			if (user.previousWorkno && user.previousWorkno !== '无') {
				html += ' <span style="color: #999;">(原工号: ' + escapeHtml(user.previousWorkno) + ')</span>';
			}
		} else if (type === 'failure') {
			html += '<span class="sync-failure">✗</span> ';
			html += '<strong>' + escapeHtml(user.username) + '</strong>';
			html += ' - <span style="color: #666;">失败原因: ' + escapeHtml(user.reason) + '</span>';
		} else if (type === 'skip') {
			html += '<span class="sync-skip">⚠</span> ';
			html += '<strong>' + escapeHtml(user.username) + '</strong>';
			html += ' - <span style="color: #666;">跳过原因: ' + escapeHtml(user.reason) + '</span>';
		}

		html += '</div>';
	}
	html += '</div>';

	return html;
};



