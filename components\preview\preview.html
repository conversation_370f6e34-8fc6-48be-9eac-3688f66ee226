<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>预览文件</title>
		<link rel="Shortcut Icon" href="../../img/preview.png">
		<script src="../../plugins/easyui/jquery.min.js"></script>
	</head>
	<body style="margin: 0px;">
		<iframe id="iframe"></iframe>
	</body>

	<script>
		function getQueryString(name) {
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
			var r = window.location.search.substr(1).match(reg);
			if (r != null) return decodeURI(r[2]);
			return null;
		}

		var urlKey = getQueryString("key");
		//登录拦截
		var _dcTime = (new Date().getTime());
		if (sessionStorage.username) {
			//如果存在session的话 继续预览
			if (sessionStorage.getItem(urlKey)) {
				var src = sessionStorage.getItem(urlKey);
				var ifHeight = window.innerHeight - 4;
				$("#iframe").css({
					"height": ifHeight + "px",
					"border": "none",
					"width": "100%"
				});
				$("#iframe").attr("src", src);
			} else {
				$("#iframe").hide();
				alert("无效地址！");
			}
		} else {
			if (location.href.indexOf('login.html') == -1) {
				location.href = '/DataPackageManagement/login.html?_dc=' + _dcTime;
				//如果需要跳转到登录页面则把sessionStorage中的变量清空
				sessionStorage.clear();
			}
		}
	</script>
</html>
