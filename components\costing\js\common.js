/**
 * 表格中格式化时间
 */
function formatTableDate(d) {
	return util.toDateString(d[this.field]);
}
/**
 * 表格中格式化耗时时间
 */
function formatTableDuration(d) {
	return formatDuration(d[this.field]);
}

/**
 * 格式化显示流程版本号
 * @param {Object} d
 */
function formatVersion(d) {
	return '<span class="layui-badge layui-bg-blue">v' + d[this.field] + '</span>'
}

/**
 * 流程完成时间处理
 * @param {Object} ms
 */
function formatDuration(ms) {
	var second = 1000;
	var minute = 60 * second;
	var hour = 60 * minute;
	var day = 24 * hour;

	var days = Math.floor(ms / day);
	var hours = Math.floor((ms % day) / hour);
	var minutes = Math.floor((ms % hour) / minute);
	var seconds = Math.floor((ms % minute) / second);
	var milliseconds = ms % second;

	var result = '';

	if (days > 0) {
		result += days + '天';
	}

	if (hours > 0) {
		result += hours + '小时';
	}

	if (minutes > 0) {
		result += minutes + '分钟';
	}

	if (seconds > 0 && days < 1 && minutes < 1) {
		result += seconds + '秒';
	}

	if (milliseconds > 0 && days < 1 && minutes < 1 && seconds < 1) {
		result += milliseconds + '毫秒';
	}

	return result;
}

/**
 * 处理表单元素的显示、只读、禁用、校验
 * @param {Object} type
 */
function dealForm(type) {
	var fc = formConfig[type];
	for (var id in fc) {
		var cf = fc[id];
		var type = cf.type || '';
		var $ele = $("#" + id);
		if (cf.show) {
			if (cf.verify) {
				$ele.attr("lay-verify", cf.verify);
			} else {
				$ele.removeAttr("lay-verify");
			}

			if (cf.disabled) {
				if (type == 'file') {
					$ele.parent().find('.upload-choose').addClass('layui-btn-disabled').hide();
					renderPreviewBtn($ele.parent());
				} else {
					$ele.attr("disabled", "disabled");
				}
			} else {
				if (type == 'file') {
					$ele.parent().find('.upload-choose').removeClass('layui-btn-disabled').show();
					renderUpload($ele.parent());
				} else {
					$ele.removeAttr("disabled");
				}

			}
			if (cf.readonly) {
				$ele.attr("readonly", "readonly");
			} else {
				$ele.removeAttr("readonly");
			}

			if (cf.hasSuffix) {
				$ele.addClass("has-suffix");
			} else {
				$ele.removeClass("has-suffix");
				$ele.next(".layui-input-suffix").remove();
			}

			if (cf.isPost) {
				if (type == 'file') {
					$ele.parent().find('.upload-name').attr("isPost", "true");
					$ele.parent().find('.upload-path').attr("isPost", "true");
					$ele.parent().find('.upload-size').attr("isPost", "true");
					$ele.parent().find('.upload-format').attr("isPost", "true");
				} else {
					$ele.attr("isPost", "true");
				}
			} else {
				$ele.attr("isPost", "false");
			}
			if (cf.passField) {
				$ele.attr("passField", cf.passField);
			}
			if (cf.userField) {
				$ele.attr("userField", cf.userField);
			}
			if (cf.dateField) {
				$ele.attr("dateField", cf.dateField);
			}
			if (cf.nextUserField) {
				$ele.attr("nextUserField", cf.nextUserField);
			}
			//是否做为审批意见
			if (cf.isReason) {
				$ele.attr("isReason", cf.isReason);
			}
		} else {
			$ele.parent().parent().remove();
		}
	}
}

/**
 * 获取时间线的html
 */
function getTimeLineHtml(datas) {
	var $root = $('<div class="layui-timeline"></div>');
	for (var i = 0; i < datas.length; i++) {
		var d = datas[i];
		var $item = $('<div class="layui-timeline-item"></div>');
		var completeClass = d.END_TIME_ ? "layui-icon-success" : "layui-icon-time";
		var returnReason = "";
		if (d.COM_TYPE == '2') {
			//退回操作
			completeClass = 'layui-icon-backspace';
			returnReason = d.COM_MESSAGE;
		}
		var $icon = $('<i class="layui-icon ' + completeClass + ' layui-timeline-axis"></i>');
		var $content = $('<div class="layui-timeline-content layui-text"></div>');
		var $title = $('<h3 class="layui-timeline-title">' + d.ACT_NAME_ + '</h3>');
		var $assignee = $('<p>办理人：' + d.ASSIGNEE_N_ + '</p>');
		var $returnReason = returnReason ? $('<p>退回理由：' + returnReason + '</p>') : "";
		var endTime = d.END_TIME_ ? '<span>-</span>结束时间：' + util.toDateString(d.END_TIME_) + '' : "";
		var $time = $('<p>接收时间：' + util.toDateString(d.START_TIME_) + endTime + '</p>');

		var $duration = d.DURATION_ ? $('<p>耗时：' + formatDuration(d.DURATION_) + '</p>') : "";
		$content.append($title).append($assignee).append($returnReason).append($time).append($duration);
		$item.append($icon).append($content)
		$root.append($item);
	}
	return $root[0].outerHTML;
}

/**
 * 获取flowable后台的请求url
 */
function getFlowPostUrl() {
	var url = "";
	twxAjax("Thing.Fn.Costing", 'GetStringPropertyValue', {
		propertyName: 'flowableUrl'
	}, false, function(res) {
		url = res.rows[0].result;
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('获取flowable后台的请求url出错！', {
			icon: 2
		});
	});
	return url;
}

var flowPostUrl = getFlowPostUrl();