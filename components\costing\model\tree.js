var THING = "Thing.Fn.Costing";
var treeId = 'tree';
var ztreeObj;
var curDragNodes, autoExpandNode;
//tree setting
var treeSetting = {
	view: {
		dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
		showLine: true, //是否显示节点之间的连线
		fontCss: {
			'color': 'black'
		}, //字体样式函数
		selectedMulti: false, //设置是否允许同时选中多个节点,
		txtSelectedEnable: true,
		showTitle: true
	},
	async: {
		enable: true,
		url: getTreeUrl(THING, "QueryCalcTreeChild", ""),
		type: "post",
		autoParam: ["ID"],
		contentType: "application/json;charset=utf-8",
		dataType: 'json',
		dataFilter: function(treeId, parentNode, responseData) {
			if (responseData.success) {
				var datas = responseData.data;
				if (datas.length > 0) {
					datas = dealDataIcons(datas);
				}
				return datas;
			} else {
				layer.alert(responseData.msg, {
					icon: 2
				});
			}
		}
	},
	check: {
		enable: false
	},
	edit: {
		enable: true,
		editNameSelectAll: false,
		showRemoveBtn: false,
		showRenameBtn: false,
		drag: {
			autoExpandTrigger: true,
			prev: dropPrev,
			inner: dropInner,
			next: dropNext
		}
	},
	data: {
		simpleData: { //简单数据模式
			enable: true,
			idKey: "ID",
			pIdKey: "PID",
			rootPId: -1
		},
		key: {
			name: 'NAME',
			title: 'NAME',
			isParent: "ISPARENT"
		}
	},
	callback: {
		beforeDrag: beforeDrag,
		beforeDrop: beforeDrop,
		beforeDragOpen: beforeDragOpen,
		onDrag: onDrag,
		onDrop: onDrop
	}
};

function dropPrev(treeId, nodes, targetNode) {
	var pNode = targetNode.getParentNode();
	if (pNode && pNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			var curPNode = curDragNodes[i].getParentNode();
			if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function dropInner(treeId, nodes, targetNode) {
	if (targetNode && targetNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			if (!targetNode && curDragNodes[i].dropRoot === false) {
				return false;
			} else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
				.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function dropNext(treeId, nodes, targetNode) {
	var pNode = targetNode.getParentNode();
	if (pNode && pNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			var curPNode = curDragNodes[i].getParentNode();
			if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function beforeDrag(treeId, treeNodes) {
	for (var i = 0, l = treeNodes.length; i < l; i++) {
		if (treeNodes[i].drag === false) {
			curDragNodes = null;
			return false;
		} else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
			curDragNodes = null;
			return false;
		}
	}
	curDragNodes = treeNodes;
	return true;
}

function beforeDragOpen(treeId, treeNode) {
	autoExpandNode = treeNode;
	return true;
}

function beforeDrop(treeId, treeNodes, targetNode, moveType, isCopy) {
	return true;
}

function onDrag(event, treeId, treeNodes) {}


function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
	if (targetNode != null) {
		var sourceNodeSort = treeNodes[0].SORT;
		var sourceNodePId = treeNodes[0].PID;
		var sourceNodeId = treeNodes[0].ID;
		var sourceNodeName = treeNodes[0].NAME;

		var targetNodeSort = targetNode.SORT;
		var targetNodeId = targetNode.ID;
		var targetNodeName = targetNode.NAME;
		var type = "上面";
		if (sourceNodeSort < targetNodeSort) {
			type = '下面';
		}
		var parentNode = treeNodes[0].getParentNode();
		var allNode = parentNode.children;
		var arr = [];
		for (var i = 1; i <= allNode.length; i++) {
			arr.push(allNode[i - 1].ID + ":" + i);
		}
		var str = arr.join(",");
		twxAjax(THING, 'UpdateCalcNodeSort', {
			str: str
		}, true, function(res) {
			if (res.success) {
				reloadTreeNode(sourceNodePId, sourceNodeId);
			} else {
				layer.alert(res.msg);
			}
		}, function(data) {
			layer.alert("拖动失败！");
		});
	}
}

//加载树节点右键菜单
function loadTreeMenu() {
	$("#tree a").each(function(i, n) {
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		menu = getNodeMenu(node);
		if (menu.length != 0) {
			$(n).contextMenu({
				width: 140,
				menu: menu,
				target: function(ele) {
					var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
					ztreeObj.selectNode(node, false, true);
				}
			});
		}
	});
}

/**
 * 结构树增加子节点
 * @param {Object} pId
 */
function addNode(pId, name) {
	var addTpl =
		'<form class="layui-form" action="" lay-filter="add-node-form">\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">' + name + '名称:</label>\
				<div class="layui-input-block">\
					<input type="text" name="name" lay-verify="required" autocomplete="off" placeholder="请输入' + name + '名称" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item" style="display:none;">\
				<div class="layui-input-block">\
					<div class="layui-footer">\
						<button class="layui-btn" id="addNodeSubmit" lay-submit="" lay-filter="submit-add-node">确认</button>\
						<button type="reset" id="addNodeReset" class="layui-btn layui-btn-primary">重置</button>\
					</div>\
				</div>\
			</div>\
		</form>';
	layer.open({
		title: '添加' + name,
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: ['500px', '170px'],
		content: '<div id="addTableNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '重置', '取消'],
		yes: function() {
			$('#addNodeSubmit').click();
		},
		btn2: function() {
			$('#addNodeReset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function(layero, userLayerIndex, that) {
			$(layero).find('.layui-layer-content').css("overflow", "visible");
			$("#addTableNodeContent").append(addTpl);
		}
	});

	form.on('submit(submit-add-node)', function(formData) {
		var param = {};
		param.pId = pId;
		param.name = formData.field.name;
		param.creator = sessionStorage.getItem('username');
		var loadIndex = layer.load();
		var cb_success = function(res) {
			layer.close(loadIndex);
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				reloadTreeNode(pId, res.data);
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function(data) {
			layer.alert('添加失败，请联系管理员！', {
				icon: 2
			});
		};
		twxAjax(THING, 'AddCalcNode', param, true, cb_success, cb_error);
		return false;
	});
}

/**
 * 更新节点
 * @param {Object} treeNode
 */
function updateNode(treeNode) {
	var updateTpl =
		'<form class="layui-form" action="" lay-filter="edit-node-form">\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">名称:</label>\
				<div class="layui-input-block">\
					<input type="text" name="name" lay-verify="required" value="' + treeNode['NAME'] + '" autocomplete="off" placeholder="请输入名称" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item" style="display:none;">\
				<div class="layui-input-block">\
					<div class="layui-footer">\
						<button class="layui-btn" id="editNodeSubmit" lay-submit="" lay-filter="submit-edit-node">确认</button>\
						<button type="reset" id="editNodeReset" class="layui-btn layui-btn-primary">重置</button>\
					</div>\
				</div>\
			</div>\
		</form>';
	layer.open({
		title: '修改节点',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: ['500px', '170px'],
		content: '<div id="editTableNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '重置', '取消'],
		yes: function() {
			$('#editNodeSubmit').click();
		},
		btn2: function() {
			$('#editNodeReset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function(layero, userLayerIndex, that) {
			$(layero).find('.layui-layer-content').css("overflow", "visible");
			$("#editTableNodeContent").append(updateTpl);
		}
	});
	form.render(null, 'edit-node-form');

	form.on('submit(submit-edit-node)', function(data) {
		var param = {};
		param.name = data.field.name;
		param.updateUser = sessionStorage.getItem("username");
		param.id = treeNode.ID;
		var cb_success = function(res) {
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				reloadTreeNode(treeNode.PID, treeNode.ID);
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function() {
			layer.closeAll();
			layer.msg('修改失败!');
		};
		twxAjax(THING, 'UpdateCalcNode', param, true, cb_success, cb_error);
		return false;
	});

}

/**
 * 删除节点
 * @param {Object} id
 */
function deleteNode(treeNode) {

	var msg = "确认删除 节点 -- " + treeNode.NAME + " 吗？";
	if (treeNode.ISPARENT) {
		msg = "该节点下有子节点,确认删除吗?"
	}
	layer.confirm(msg, {
		icon: 3,
		title: '提示'
	}, function(index) {
		var cb_success = function(res) {
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				reloadTreeNode(treeNode.PID);
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function() {
			layer.msg('删除失败！');
		}
		twxAjax(THING, 'DeleteCalcNode', {
			id: treeNode.ID
		}, true, cb_success, cb_error);
	});

}

/**
 * 操作完节点之后重新加载节点
 * @param {Object} refrushId 需要刷新的节点 id
 * @param {Object} selId 刷新之后需要选中的节点 id
 */
function reloadTreeNode(refrushId, selId) {
	if (selId) {

	} else {
		selId = refrushId;
	}
	var refrushTreeNode = ztreeObj.getNodeByParam("ID", refrushId, null);

	if (!refrushTreeNode.ISPARENT) {
		refrushTreeNode.ISPARENT = true;
		ztreeObj.updateNode(refrushTreeNode);
	}
	ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false, function() {
		ztreeObj.expandNode(refrushTreeNode, true, false, true);
		var newSelNode = ztreeObj.getNodeByParam("ID", selId, null);
		ztreeObj.selectNode(newSelNode, false, true);
		loadTreeMenu();
	});
}


//获取节点右键菜单数组
function getNodeMenu(treeNode) {
	var type = treeNode.TYPE;
	var nodeId = treeNode.ID;
	var menu = [];

	var addFolderMenu = {
		text: "添加分类",
		icon: 'img/add.png',
		callback: function() {
			addNode(nodeId, "分类");
		}
	};

	var addModelMenu = {
		text: "添加模型",
		icon: 'img/add.png',
		callback: function() {
			addNode(nodeId, "模型");
		}
	};

	var editMenu = {
		text: "编辑节点",
		icon: 'img/edit.png',
		callback: function() {
			updateNode(treeNode);
		}
	};

	var deleteMenu = {
		text: "删除节点",
		icon: 'img/remove.png',
		callback: function() {
			deleteNode(treeNode);
		}
	};

	if (type == 'root') {
		menu.push(addFolderMenu);
	} else if (type == 'folder') {
		menu.push(addModelMenu);
		menu.push(editMenu);
		menu.push(deleteMenu);
	} else if (type == 'model') {
		menu.push(editMenu);
		menu.push(deleteMenu);
	}

	return menu;
}

/**
 * 对数据进行处理
 * @param {} datas 
 */
function dealDataIcons(datas) {
	for (var i = 0; i < datas.length; i++) {
		var dtype = datas[i].TYPE;
		if (dtype === 'root') {
			datas[i].icon = "../model/img/model.png";
			datas[i].drag = false;
			datas[i].childOuter = false;
			datas[i].nocheck = true;
		} else if (dtype === 'folder') {
			datas[i].NAME = dealPrefix(datas[i].SORT) + "-" + datas[i].NAME;
			datas[i].icon = "../model/img/folder.png";
			datas[i].drag = true;
			datas[i].dropInner = true;
			datas[i].childOuter = false;
			datas[i].nocheck = true;
		} else if (dtype === 'model') {
			datas[i].icon = "../model/img/calc.png";
			datas[i].dropInner = false;
			datas[i].nocheck = false;
		}
	}
	return datas;
}

/**
 * 树的点击事件
 * 
 * @param {Object} treeNode
 */
function treeClick(treeNode) {
	currentTreeId = treeNode.ID;
	var type = treeNode.TYPE;
	if (type == "model") {
		showParam();
		showResult();
		hideFormula("请点击输出参数的编辑公式按钮进行公式编辑！");
		renderTable();
	} else {
		var msg = '请选择模型节点！';
		hideParam(msg);
		hideResult(msg);
		hideFormula(msg);
		hideVerify(msg);
	}
}

/**
 * 加载结构树
 */
function loadTree(isEdit = true, isCheck = false) {
	var cb_success = function(res) {
		if (res.success) {
			var datas = res.data;
			if (datas.length > 0) {
				datas = dealDataIcons(datas);
				treeSetting.callback.onExpand = function(event, treeId, treeNode) {
					loadTreeMenu();
				};
				treeSetting.callback.onClick = function(event, treeId, treeNode) {
					treeClick(treeNode);
				};
				if (!isEdit) {
					treeSetting.edit.enable = false;
					treeSetting.callback = {};
				}
				if (isCheck) {
					treeSetting.check = {
						enable: true,
						chkStyle: "radio",
						radioType: "all"
					};
				}

				ztreeObj = $.fn.zTree.init($("#" + treeId), treeSetting, datas);
				var node = ztreeObj.getNodeByParam("LEVE", 0, null);
				ztreeObj.expandNode(node, true, false, true);
				if (isEdit) {
					loadTreeMenu();
				}
			}
		} else {
			layer.alert(res.msg);
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax(THING, 'QueryCalcTreeRoot', '', true, cb_success);
}