/**
 * HotUtil.Content.js - Handsontable工具类 (内容操作模块)
 *
 * 负责向单元格添加动态内容，如签名和照片。
 */

/**
 * 检查此客户端是否允许拍照
 * @returns {boolean}
 */
HotUtil.isAllowTakePhoto = function () {
    var flag = false;
    try {
        if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator.webkitGetUserMedia ||
            navigator
                .mozGetUserMedia) {
            flag = true;
        }
    } catch (e) {

    }
    return flag;
};

/**
 * Base64上传
 * @param {string} dataURL
 * @param {string} uploadURL
 * @param {Function} successFn
 */
HotUtil.base64Upload = function (dataURL, uploadURL, successFn) {
    var loadIndex = layer.load(); //上传loading
    // 解码Base64字符串
    var base64Data = dataURL.split(',')[1];
    var binaryData = atob(base64Data);

    // 将二进制数据创建为Blob对象
    var blobData = new Blob([new Uint8Array(binaryData.length).map(function (_, i) {
        return binaryData.charCodeAt(i);
    })], {
        type: 'image/bmp'
    });

    // 创建一个FormData对象并将Blob对象附加到其中
    var formData = new FormData();
    formData.append('file', blobData, 'image.bmp'); // 'file'是服务器接受文件的字段名， 'image.bmp'是上传的文件名

    // 发送AJAX请求
    var xhr = new XMLHttpRequest();
    xhr.open('POST', uploadURL, true); // 上传到服务器的URL
    xhr.onreadystatechange = function () {

        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                layer.close(loadIndex);
                successFn(JSON.parse(xhr.responseText).data.filePath);
            } else {
                // 处理上传失败的回调
                layer.alert("图片上传失败", {
                    icon: 2
                });
            }
        }
    };

    xhr.send(formData);
};

/**
 * 拍照
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 */
HotUtil.takePhoto = function (treeNode, ele, tableSel) {
    ele = HotUtil.findDataTableEle(ele);
    var mediaStreamTrack = null;
    var isTake = false;
    var photoWidth = 960;
    var photoHeight = 600;
    layer.open({
        title: false,
        closeBtn: 0,
        type: 1,
        area: [photoWidth + 'px', (photoHeight + 68) + 'px'],
        content: '<div id="take-photo-content"></div>',
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        resize: false,
        btn: ['拍照', '重拍', '上传', '取消'],
        yes: function () {
            var canvas = document.getElementById('canvas');
            $("#video").hide();
            $("#canvas").show();
            var context = canvas.getContext('2d');
            context.drawImage(video, 0, 0, photoWidth, photoHeight);
            isTake = true;
        },
        btn2: function () {
            $("#video").show();
            $("#canvas").hide();
            isTake = false;
            return false;
        },
        btn3: function () {
            var dataUrl = document.getElementById('canvas').toDataURL();
            if (isTake) {
                HotUtil.base64Upload(dataUrl, fileHandlerUrl + '/file/upload', function (filePath) {
                    HotUtil.updateTableSign(filePath, ele, tableSel, treeNode, "photo")
                    mediaStreamTrack.stop();
                    $("#video").remove();
                });
            } else {
                layer.alert('请先拍照！', {
                    icon: 2
                });
            }
            return false;
        },
        btn4: function () {
            try {
                mediaStreamTrack.stop();
            } catch (e) { }

            $("#video").remove();
            return true;
        },
        success: function () {
            var tpl = '<video id="video" width="' + photoWidth + '" height="' + photoHeight + '"  autoplay="autoplay">\
                        </video>\
                        <canvas id="canvas" width="' + photoWidth + '" height="' + photoHeight + '" style="display:none" ></canvas>';
            $("#take-photo-content").append(tpl);
            var video = document.getElementById('video');
            if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator
                .webkitGetUserMedia || navigator
                    .mozGetUserMedia) {

                //访问用户媒体设备的兼容方法
                function getUserMedia(constraints, success, error) {
                    try {
                        if (navigator.mediaDevices.getUserMedia) {
                            //最新的标准API
                            navigator.mediaDevices.getUserMedia(constraints).then(success)
                                .catch(error);
                        } else if (navigator.webkitGetUserMedia) {
                            //webkit核心浏览器
                            navigator.webkitGetUserMedia(constraints, success, error)
                        } else if (navigator.mozGetUserMedia) {
                            //firfox浏览器
                            navigator.mozGetUserMedia(constraints, success, error);
                        } else if (navigator.getUserMedia) {
                            //旧版API
                            navigator.getUserMedia(constraints, success, error);
                        }
                    } catch (e) {
                        //TODO handle the exception
                    }
                }

                function renderMedia(type, errorFn) {
                    //默认后置摄像头
                    var facingMode = {
                        exact: "environment"
                    };
                    //前置摄像头
                    if (type == "user") {
                        facingMode = "user";
                    }
                    getUserMedia({
                        video: {
                            width: photoWidth,
                            height: photoHeight,
                            facingMode: facingMode
                        }
                    }, function (stream) {
                        //兼容webkit核心浏览器
                        var CompatibleURL = window.URL || window.webkitURL;
                        //将视频流设置为video元素的源
                        console.log(stream);
                        mediaStreamTrack = stream.getTracks()[0];
                        //video.src = CompatibleURL.createObjectURL(stream);
                        video.srcObject = stream;
                        video.play();
                    }, function (error) {
                        errorFn(error);
                    });
                }

                //调用用户媒体设备, 访问摄像头
                renderMedia("environment", function (error) {
                    renderMedia("user", function (error) {
                        $("#video").remove();
                        $("#take-photo-content").append(
                            '<div style="color:red;padding:20px;font-size:20px">访问用户媒体设备失败,请检查是否连接摄像头设备！</div>'
                        );
                        console.log(
                            `访问用户媒体设备失败${error.name}, ${error.message}`);
                    });
                });
            } else {
                alert('不支持访问用户媒体');
            }
        }
    });
};

/**
 * 弹出上传签章的窗口
 * @param {Function} uploadSuccessFn
 */
HotUtil.uploadSignLayer = function (uploadSuccessFn) {
    if (device.ie && device.ie < 10) {
        layer.alert("请使用chrome浏览器！", {
            icon: 2
        });
    } else {
        var isAdm = false;
        var layerHeight = "460px";
        if (sessionStorage.getItem('username') == 'adm' || sessionStorage.getItem('username') == 'admin' || sessionStorage.getItem('username') == 'huaxiaodong') {
            isAdm = true;
            layerHeight = "510px";
        }

        var layerIndex = layer.open({
            title: "上传签章",
            type: 1,
            area: ['400px', layerHeight],
            content: '<div id="uploadSignContent" style="padding: 15px 0px 0px 0px;"></div>',
            anim: false,
            openDuration: 200,
            isOutAnim: false,
            closeDuration: 200,
            resize: false,
            btn: ['确定', '取消'],
            yes: function () {
                var signSrc = $("#previewImg").attr("src");
                if (signSrc) {
                    $("#uploadStart").click();
                } else {
                    layer.alert("请选择签章图片！", {
                        icon: 2
                    });
                }
            },
            btn2: function () {
                return true;
            },
            success: function () {
                var signDateDiv = "";
                if (isAdm) {
                    signDateDiv = '<div class="layui-form-item">\
                                <label class="fieldlabel layui-form-label">签署日期:</label>\
                                <div class="layui-input-block">\
                                    <input type="text" class="layui-input" id="sign-date" style="width:274px;">\
                                </div>\
                            </div>';
                }
                var tpl = '<form class="layui-form" lay-filter="uploadSignForm">' +
                    signDateDiv + '<div class="layui-form-item">\
                                <label class="fieldlabel layui-form-label">文件内容:</label>\
                                <div class="layui-input-block">\
                                    <div class="layui-upload">\
                                         <div id="uploadChoice">选择文件</div>											\
                                         <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
                                    </div>\
                                </div>\
                            </div>\
                            <div class="layui-form-item selectedFile" style="display: none;">\
                                <label class="fieldlabel layui-form-label">已选文件:</label>\
                                <div class="layui-input-block">\
                                    <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
                                </div>\
                            </div>\
                            <div class="layui-form-item selectedFile" style="display: none;">\
                                <label class="fieldlabel layui-form-label">签名用户:</label>\
                                <div class="layui-input-block">\
                                    <input type="text" class="layui-input" id="sign-name" style="width:274px;">\
                                </div>\
                            </div>\
                            <div class="layui-form-item" id="previewFile" style="display: none;">\
                                <label class="fieldlabel layui-form-label">签章预览:</label>\
                                <div class="layui-input-block">\
                                    <img style="max-width: 285px;max-height: 150px;" id="previewImg" />\
                                </div>\
                            </div>\
                            <div class="layui-form-item" style="display:none;">\
                                <center>\
                                    <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
                                    <button id="btn_cancel" class="layui-btn">取消</button>\
                                </center>\
                            </div>\
                        </form>';
                $("#uploadSignContent").append(tpl);
                form.render(null, 'uploadSignForm');
                laydate.render({
                    elem: '#sign-date' //指定元素
                });

                var reqIdent = new Date().getTime();
                var uploader = WebUploader.create({
                    // 选完文件后，是否自动上传。
                    auto: false,
                    // 文件接收服务端。
                    server: fileHandlerUrl + '/table/web/upload/photo',
                    // 选择文件的按钮。可选。
                    pick: {
                        id: '#uploadChoice',
                        multiple: false // 设置multiple为false
                    },
                    timeout: 60 * 60 * 1000,
                    // 配置分片上传
                    chunked: true,
                    chunkSize: 10 * 1024 * 1024,
                    fileNumLimit: 1,
                    accept: {
                        title: 'Images',
                        extensions: 'gif,jpg,jpeg,bmp,png',
                        mimeTypes: 'image/*'
                    },
                    thumb: {
                        width: 285,
                        height: 150,
                        // 图片质量，只有type为`image/jpeg`的时候才有效。
                        quality: 100,
                        // 是否允许放大，如果想要生成小图的时候不失真，此选项应该设置为false.
                        allowMagnify: false,
                        // 是否允许裁剪。
                        crop: false
                    },
                    formData: {
                        reqIdent: reqIdent,
                        extraData: JSON.stringify({
                            username: sessionStorage.getItem("username")
                        })
                    }
                });
                uploader.on('uploadBeforeSend', function (object, data, headers) {

                });

                // 当有文件被添加进队列之前触发
                uploader.on('beforeFileQueued', function (file) {
                    // 检查队列中是否已经有文件
                    if (uploader.getFiles().length > 0) {
                        // 如果有文件，先移除旧的文件
                        uploader.removeFile(uploader.getFiles()[0], true);
                    }
                });

                // 当有文件被添加进队列的时候
                uploader.on('fileQueued', function (file) {
                    $(".selectedFile").show();
                    $("#selectedFileName").text(file.name);
                    $("#previewFile").show();
                    $("#sign-name").val(sessionStorage.getItem('fullname'));
                    uploader.makeThumb(file, function (error, ret) {
                        if (error) {

                        } else {
                            $("#previewImg").attr("src", ret);
                        }
                    });
                });

                uploader.on('uploadSuccess', function (file, res) {
                    if (res.success) {
                        var src = res.data.path;
                        var date = $("#sign-date").val() || layui.util.toDateString(
                            new Date(), 'yyyy-MM-dd');
                        var signName = $("#sign-name").val();
                        uploadSuccessFn(src, date, signName);
                        layer.close(layerIndex);
                    } else {
                        layer.alert(res.msg, {
                            icon: 2
                        });
                    }
                });

                // 文件上传失败，显示上传出错。
                uploader.on('uploadError', function (file) {

                });

                // 完成上传完毕，成功或者失败，先删除进度条。
                uploader.on('uploadComplete', function (file) {

                });

                // 当所有文件上传结束时触发
                uploader.on('uploadFinished', function () {

                });

                $("#uploadStart").on('click', function () {
                    uploader.upload();
                });
            }
        });
    }
};

/**
 * 上传签章
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 */
HotUtil.uploadSign = function (treeNode, ele, tableSel) {
    // 在上传签章操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "上传签章", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '签名失败'
            });
            return; // 直接返回，不打开上传界面
        }

        // 乐观锁检查通过，继续执行上传签章逻辑
        ele = HotUtil.findDataTableEle(ele);
        HotUtil.uploadSignLayer(function (src, date, signName) {
            HotUtil.updateTableSign(src, ele, tableSel, treeNode, "upload", date, signName);
        });
    });
};

/**
 * Wacom手写板签名
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 */
HotUtil.wacomSign = function (treeNode, ele, tableSel) {
    // 在手写板签名操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "手写板签名", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '签名失败'
            });
            return; // 直接返回，不开始签名流程
        }

        // 乐观锁检查通过，继续执行手写板签名逻辑
        HotUtil._executeWacomSign(treeNode, ele, tableSel);
    });
};

/**
 * 执行手写板签名的具体逻辑（从原 wacomSign 函数中提取）
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 */
HotUtil._executeWacomSign = function (treeNode, ele, tableSel) {
    ele = HotUtil.findDataTableEle(ele);
    if (device.ie && device.ie < 10) {
        layer.alert('请在Chrome浏览器中使用！', {
            icon: 2
        });
    } else {
        function saveSignToBase64() {
            if (!!signPlugin) {
                //Get the signPlugin's signature image data.
                signPlugin.saveSignToBase64( /*615, 272*/ 0, 0, function (state, args) {
                    if (state) {
                        var img_base64_data = args[0];
                        //Show the signature image.
                        var img_base64 = "data:image/png;base64," + img_base64_data;
                        if (img_base64 == blankSign) {
                            layer.alert('获取签名图形失败！', {
                                icon: 2
                            });
                        } else {
                            document.getElementById("img_sign_result").src = img_base64;

                            var dataUrl = $('#img_sign_result').attr("src");
                            if (dataUrl) {
                                HotUtil.updateTableSign(dataUrl, ele, tableSel, treeNode, "wacom");
                            } else {
                                layer.alert('请先签名！', {
                                    icon: 2
                                });
                            }

                        }
                        debugPrint("saveSignToBase64 OK");
                        //Submit the signature base64 string to the server
                        //...
                    } else {
                        debugPrint("saveSignToBase64 error,description:" + args[0]);
                    }
                });
            }
        }

        /*confirm event*/
        signPlugin.onConfirm = function () {
            saveSignToBase64();
            endSign();
        };

        //在页面中构建一个隐藏的img标签用来存储签名图片
        var $signImg = $('<img style="display:none;" src="" id="img_sign_result"/>');
        $signImg.appendTo($('body'));
        beginSign();
    }
};

/**
 * 鼠标签名
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 */
HotUtil.sign = function (treeNode, ele, tableSel) {
    // 在鼠标签名操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "鼠标签名", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '签名失败'
            });
            return; // 直接返回，不打开签名界面
        }

        // 乐观锁检查通过，继续执行鼠标签名逻辑
        HotUtil._executeSign(treeNode, ele, tableSel);
    });
};

/**
 * 执行鼠标签名的具体逻辑（从原 sign 函数中提取）
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 */
HotUtil._executeSign = function (treeNode, ele, tableSel) {
    ele = HotUtil.findDataTableEle(ele);
    if (device.ie && device.ie < 10) {
        layer.alert('请在Chrome浏览器中使用！', {
            icon: 2
        });
    } else {
        layer.open({
            title: "签名",
            type: 1,
            area: ['1000px', '570px'],
            content: '<div id="report-signContent"></div>',
            anim: false,
            openDuration: 200,
            isOutAnim: false,
            closeDuration: 200,
            resize: false,
            btn: ['确定', '重签', '取消'],
            yes: function () {
                var dataUrl = $('.js-signature').eq(0).jqSignature('getDataURL');
                if (dataUrl) {
                    HotUtil.updateTableSign(dataUrl, ele, tableSel, treeNode, "mouse");
                } else {
                    layer.alert('请先签名！', {
                        icon: 2
                    });
                }
            },
            btn2: function () {
                $('.js-signature').eq(0).jqSignature('clearCanvas');
                return false;
            },
            btn3: function () {
                return true;
            },
            success: function () {
                var tpl = '<div class="js-signature" data-width="1000" data-height="460" data-border="1px solid #333"\
                                data-line-color="#000" data-line-width="5" data-auto-fit="true">\
                                </div>';
                $("#report-signContent").append(tpl);
                $('.js-signature').jqSignature();
            }
        });
    }
};

/**
 * 更新表格签名
 * @param {string} signSrc
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 * @param {Object} treeNode
 * @param {string} type
 * @param {string} [signDate]
 * @param {string} [signName]
 * @param {string} [workno]
 */
HotUtil.updateTableSign = function (signSrc, ele, tableSel, treeNode, type, signDate, signName, workno) {
    // 处理默认参数（ES5兼容方式）
    if (typeof signName === 'undefined') {
        signName = sessionStorage.getItem('fullname');
    }
    if (typeof workno === 'undefined') {
        workno = sessionStorage.getItem('workno');
    }

    // 在签名操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "签名", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '签名失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        // var nowDate = layui.util.toDateString(new Date(), 'yyyy-MM-dd');
        // var $img = $('<img>').attr("type", "sign").attr('src', signSrc).attr('class', 'sign-img').attr('date', nowDate);
        // var date = '<br><span>' + nowDate + '</span>'
        // if ($(ele).text().trim() != '' || $(ele).find("img").length > 0) {
        // 	$(ele).append("<br>");
        // }
        // $(ele).append($img).append(date);
        var log = {};
        if (type == "photo") {
            log.operation = "拍照";
        } else if (type == "mouse") {
            log.operation = "鼠标签名";
        } else if (type == "wacom") {
            log.operation = "手写板签名";
        } else if (type == "upload") {
            log.operation = "上传签章";
        }
        log.tablePid = treeNode.PID;
        log.tableId = treeNode.ID;
        log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中第" +
            (parseInt($(ele).attr("row")) + 1) + "行第" + (parseInt($(ele).attr("col")) + 1) + "列" + log
                .operation;

        if (signDate) {

        } else {
            signDate = layui.util.toDateString(new Date(), 'yyyy-MM-dd');
        }
        // var htmlData = tableSel.find(".data-table")[0].outerHTML;
        var loadIndex = layer.load(); //上传loading
        var scrollObj = {
            isScroll: true,
            row: $(ele).attr("row"),
            col: $(ele).attr("col"),
        };
        var cb_success = function (res) {
            if (res.success) {
                log.content = log.content + "，照片路径为：" + res.data;
                log.reqResult = 1;
                layer.close(loadIndex);
                reloadTable(treeNode, scrollObj);
                layer.closeAll();
                if (res.msg) {
                    layer.msg(res.msg);
                }
            } else {
                log.reqResult = 0;
                layer.close(loadIndex);
                layer.alert(res.msg);
            }
            addConfirmLog(log);
        }
        //请求失败的回调
        var cb_error = function (xhr, textStatus, errorThrown) {
            layer.alert("签名出错！", {
                icon: 2
            });
        };

        //同步新增
        twxAjax(THING, "AddSign", {
            id: treeNode.ID,
            img: signSrc,
            row: $(ele).attr("row"),
            col: $(ele).attr("col"),
            type: type,
            date: signDate,
            signName: signName,
            creator: sessionStorage.getItem('username'),
            workno: workno
        }, true, cb_success, cb_error);
    });
};

/**
 * 找到数据存放表格对应的ele
 * @param {HTMLElement} ele
 * @returns {HTMLElement}
 */
HotUtil.findDataTableEle = function (ele) {
    var tdCol = $(ele).attr("col");
    var tdRow = $(ele).attr("row");
    var dataTableEle = $(".data-table").find('td[col=' + tdCol + '][row=' + tdRow + ']');
    return dataTableEle[0];
};

/**
 * 弹出上传图片层
 * @param {Object} treeNode
 * @param {Function} uploadAllDoneSuccess
 */
HotUtil.insertImageLayer = function (treeNode, uploadAllDoneSuccess) {
    // 在上传图片操作开始时进行异步乐观锁检查
    HotUtil.checkOptimisticLockBeforeAction(treeNode, "上传图片", function (lockCheckResult) {
        if (!lockCheckResult.success) {
            // 乐观锁检查失败，直接显示错误信息并返回
            layer.alert(lockCheckResult.errorMsg, {
                icon: 2,
                title: '上传失败'
            });
            return; // 直接返回，不执行后续逻辑
        }

        // 乐观锁检查通过，继续执行上传图片逻辑
        HotUtil._executeInsertImageLayer(treeNode, uploadAllDoneSuccess);
    });
};

/**
 * 执行上传图片的具体逻辑（从原 insertImageLayer 函数中提取）
 * @param {Object} treeNode
 * @param {Function} uploadAllDoneSuccess
 */
HotUtil._executeInsertImageLayer = function (treeNode, uploadAllDoneSuccess) {
    var tpl = '<div style="padding:12px;">\
                        <div class="layui-upload" style="margin-bottom:2px;">\
                            <button type="button" class="layui-btn layui-btn-normal" id="chooseFile">选择图片</button>\
                            <button type="button" class="layui-btn" id="startUpload" style="display:none;">开始上传</button>\
                        </div>\
                        <div id="photo-table" ></div>\
                    </div>';
    if (device.ie && device.ie < 10) {
        layer.alert("请使用chrome浏览器！", {
            icon: 2
        });
    } else {

        var cb_success = function (indexRes) {
            var photoIndex = indexRes.rows[0].result;

            var loadIndex;
            var layerIndex = layer.open({
                title: '上传图片',
                type: 1,
                area: ['1100px', '660px'],
                content: tpl,
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                resize: false,
                btn: ['上传', '取消'],
                yes: function () {
                    var photoDatas = $('#photo-table').datagrid("getData").rows;
                    if (photoDatas.length == 0) {
                        layer.msg('请选择图片!');
                        return false;
                    }
                    $('#startUpload').click();
                },
                btn2: function () {
                    return true;
                },
                success: function () {
                    window.delRow = function (fileIndex) {
                        var rowInex = $('#photo-table').datagrid("getRowIndex",
                            fileIndex);
                        $('#photo-table').datagrid("deleteRow", rowInex);
                        window.deletedFile.push(fileIndex);
                    }
                    //点击了删除按钮的文件列表
                    window.deletedFile = [];
                    $('#photo-table').datagrid({
                        data: [],
                        fitColumns: true,
                        idField: 'index',
                        // fit: true,
                        columns: [
                            [{
                                field: 'photoName',
                                title: '图片名称',
                                width: 350
                            }, {
                                field: 'photoSize',
                                title: '图片大小',
                                width: 100,
                                align: 'right',
                            }, {
                                field: 'photoShowNum',
                                title: '图片编号',
                                width: 350
                            }, {
                                field: 'photoNumber',
                                hidden: true
                            }, {
                                field: 'index',
                                hidden: true
                            }, {
                                field: 'photoFormat',
                                title: '图片格式',
                                width: 100
                            }, {
                                field: 'result',
                                title: '预览',
                                align: 'center',
                                width: 100,
                                formatter: function (value, row, index) {
                                    if (value) {
                                        return '<img style="max-width: 90px;height: 90px;" src="' +
                                            value + '" />';
                                    }
                                    return "";
                                }
                            }, {
                                field: 'operation',
                                title: '操作',
                                align: 'center',
                                formatter: function (value, row, index) {
                                    var fileIndex = row.index;
                                    return '<a fileIndex="' +
                                        fileIndex +
                                        '" onclick="delRow(\'' +
                                        fileIndex +
                                        '\')" class="layui-btn layui-btn-danger layui-btn-xs">删除</a>';
                                },
                                width: 100
                            }]
                        ],
                        emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>请上传图片</font></div>',
                        loadMsg: '正在加载数据...',
                        height: 498,
                        singleSelect: true,
                        rownumbers: true,
                        striped: true
                    });

                    //上传组件
                    var photoUploadIns = upload.render({
                        elem: '#chooseFile',
                        url: fileHandlerUrl + '/table/upload/photo',
                        auto: false,
                        multiple: true,
                        accept: 'images',
                        acceptMime: 'image/*', // 限制文件浏览器只显示图片文件
                        field: 'photo',
                        bindAction: '#startUpload',
                        dataType: "json",
                        before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                            loadIndex = layer.load(); //上传loading
                            //删除文件
                            var files = obj.pushFile();
                            for (var i = 0; i < window.deletedFile
                                .length; i++) {
                                delete files[window.deletedFile[i]];
                            }
                        },
                        choose: function (obj) {
                            var files = obj.pushFile();

                            function isInTable(key, tableData) {
                                for (var i = 0; i < tableData.length; i++) {
                                    if (key == tableData[i].index) {
                                        return true;
                                    }
                                }
                                return false;
                            }

                            var datas = $('#photo-table').datagrid("getData")
                                .rows;
                            for (var key in files) {
                                if (!isInTable(key, datas) && window.deletedFile
                                    .indexOf(key) == -1) {
                                    var file = files[key];
                                    var fileName = file.name;
                                    var photoObj = {};
                                    photoObj.photoName = fileName.substr(0,
                                        fileName.lastIndexOf('.'));
                                    photoObj.photoFormat = fileName.substr(
                                        fileName.lastIndexOf('.') + 1);
                                    photoObj.photoSize = (file.size / 1024)
                                        .toFixed(1) + 'kb';
                                    photoObj.index = key;
                                    photoObj.file = file;
                                    datas.push(photoObj);
                                }
                            }
                            datas.sort(function (x, y) {
                                return x['photoName'].localeCompare(y[
                                    'photoName'], "zh");
                            });
                            for (var i = 0, initIndex = photoIndex; i < datas
                                .length; i++, initIndex++) {
                                var photoObj = datas[i];
                                var tempStr = treeNode.NAME;
                                if (treeNode.TABLE_NUM) {
                                    tempStr = treeNode.TABLE_NUM;
                                }
                                photoObj.photoShowNum = tempStr + "-图" +
                                    initIndex;
                                photoObj.photoNumber = initIndex;
                            }
                            $('#photo-table').datagrid("loadData", datas);

                            //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                            obj.preview(function (index, file, result) {
                                var rowInex = $('#photo-table')
                                    .datagrid("getRowIndex", index);
                                var rowData = $('#photo-table')
                                    .datagrid("getData").rows[rowInex];
                                rowData.result = result;
                                $('#photo-table').datagrid(
                                    "updateRow", {
                                    index: rowInex,
                                    row: rowData
                                });
                                $('#photo-table').datagrid("refreshRow",
                                    rowInex);
                            });
                        },
                        done: function (res, fileIndex) {
                            if (res.success) {
                                var rowIndex = $('#photo-table').datagrid(
                                    "getRowIndex", fileIndex);
                                var rowData = $('#photo-table').datagrid(
                                    "getData").rows[rowIndex];
                                rowData.photoPath = res.data[0].photoPath;
                                rowData.success = true;
                                $('#photo-table').datagrid("updateRow", {
                                    index: rowIndex,
                                    row: rowData
                                });
                                $('#photo-table').datagrid("refreshRow",
                                    rowIndex);
                            } else {
                                layer.alert(res.msg, {
                                    icon: 2
                                });
                            }
                        },
                        allDone: function (obj) {
                            layer.close(loadIndex);
                            if (obj.aborted > 0) {
                                layer.msg('上传失败！');
                            } else {
                                var datas = $('#photo-table').datagrid(
                                    "getData").rows;
                                uploadAllDoneSuccess(datas, layerIndex,
                                    loadIndex);
                            }
                        }
                    });
                }
            });
        }
        //请求失败的回调
        var cb_error = function (xhr, textStatus, errorThrown) {
            layer.alert("请求照片序号出错！", {
                icon: 2
            });
        };
        twxAjax(THING, "GetNewPhotoNum", {
            id: treeNode.ID
        }, true, cb_success, cb_error);
    }
};

/**
 * 插入图片
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 */
HotUtil.insertImage = function (treeNode, ele, tableSel) {
    ele = HotUtil.findDataTableEle(ele);
    var eleCol = parseInt($(ele).attr("col"));
    var eleRow = parseInt($(ele).attr("row"));
    HotUtil.insertImageLayer(treeNode, function (datas, layerIndex) {
        var photos = [];
        var photoText = "[";
        var photoCount = 0;
        for (var i = 0; i < datas.length; i++) {
            var rowData = datas[i];
            if (rowData.success) {
                var params = {};
                params.id = treeNode.ID;
                params.photoPath = rowData.photoPath;
                params.photoFormat = rowData.photoFormat;
                params.photoName = rowData.photoName;
                params.photoNumber = rowData.photoNumber;
                params.photoShowNum = rowData.photoShowNum;
                params.photoSize = rowData.photoSize;
                params.index = rowData.index;
                params.creator = sessionStorage.getItem('username');
                params.col = eleCol;
                params.row = eleRow;
                photos.push(params);
                photoText += "{照片名称：" + (params.photoName + "." + params.photoFormat) + "，路径：" +
                    params.photoPath + "，大小：" + params.photoSize + "，编号：" + params.photoShowNum +
                    "},";
                photoCount++;
            }
            photoText = photoText.substring(0, photoText.length - 1);
            photoText += "]";
        }
        var log = {};
        log.operation = "上传图片";
        log.tablePid = treeNode.PID;
        log.tableId = treeNode.ID;
        var logContent = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中的第" + (eleRow + 1) +
            "行第" + (eleCol + 1) +
            "列中上传了" + photoCount + "张图片，分别是" + photoText;
        log.content = logContent;
        var cb_success = function (res) {
            if (res.success) {
                log.reqResult = 1;
                reloadTable(treeNode);
                layer.close(layerIndex);
                layer.msg(res.msg);
            } else {
                log.reqResult = 0;
                layer.alert(res.msg, {
                    icon: 2
                });
            }
            addConfirmLog(log);
        }

        //请求失败的回调
        var cb_error = function (xhr, textStatus, errorThrown) {
            layer.alert("上传图片出错！", {
                icon: 2
            });
        };
        //同步新增
        twxAjax(THING, "AddPhotos", {
            datas: photos
        }, false, cb_success, cb_error);
    });
};

/**
 * 获取图片预览元素
 * @param {Array} imgs
 * @param {boolean} isAllowDel
 * @returns {jQuery}
 */
HotUtil.getPreviewEle = function (imgs, isAllowDel) {
    var $ul = $('<ul class="picView-magnify-list" style="padding: 30px"></ul>');
    for (var i = 0; i < imgs.length; i++) {
        var photoPath = imgs[i].photoPath;
        if (photoPath.indexOf("File") == -1) {
            photoPath = "/File" + photoPath;
        }
        var photoName = imgs[i].photoName + "." + imgs[i].photoformat;
        var photoShowNum = imgs[i].photoShowNum;
        var photoId = imgs[i].photoId || imgs[i].id;
        var deleteDiv = '<div class="img-icon">\
                                                <i class="layui-icon" photo-id="' + photoId + '" photo-name="' + photoName + '" photo-num="' +
            photoShowNum + '" photo-path="' + photoPath + '">&#xe640;</i>\
                                            </div>';
        var li = '<li>\
                                                <a href="javascript:void(0)" data-magnify="gallery" data-group="g1" data-src="' + photoPath +
            '" data-caption="' + photoShowNum + '">\
                                                    <img src="' + photoPath + '">\
                                                </a>\
                                                <div class="img-title">' + photoShowNum + '</div>\
                                            </li>';
        var $li = $(li);
        $ul.append($li);

        if (isAllowDel) {
            $li.prepend(deleteDiv);
        }
    }
    return $ul;
};

/**
 * 查看图片层
 * @param {Array} imgs
 * @param {boolean} isAllowDel
 * @param {Function} cancelFn
 * @param {Function} delClickFn
 */
HotUtil.viewImageLayer = function (imgs, isAllowDel, cancelFn, delClickFn) {
    layer.open({
        title: '查看图片',
        type: 1,
        area: ['1130px', '560px'],
        zIndex: layer.zIndex + 1,
        content: '<div id="previewContent"></div>',
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        resize: false,
        cancel: function (index, layero) {
            cancelFn();
        },
        success: function () {
            var $ul = HotUtil.getPreviewEle(imgs, isAllowDel);
            $('#previewContent').append($ul);
            if (isAllowDel) {
                //删除事件
                $ul.find('i').click(function () {
                    var $i = $(this);
                    delClickFn($i, $ul);
                });
            }
            $('[data-magnify]').magnify({
                resizable: false,
                initMaximized: true,
                zIndex: layer.zIndex + 1,
                headerToolbar: [
                    'close'
                ],
                i18n: {
                    minimize: '最小化',
                    maximize: '最大化',
                    close: '关闭',
                    zoomIn: '缩小',
                    zoomOut: '放大',
                    prev: '上一张',
                    next: '下一张',
                    fullscreen: '全屏',
                    actualSize: '实际大小',
                    rotateLeft: '向左旋转',
                    rotateRight: '向右旋转',
                }
            });

        }
    });
};

/**
 * 查看图片
 * @param {Object} treeNode
 * @param {HTMLElement} ele
 * @param {jQuery} tableSel
 * @param {boolean} isAllowDel
 */
HotUtil.viewImage = function (treeNode, ele, tableSel, isAllowDel) {
    ele = HotUtil.findDataTableEle(ele);
    if (!$(ele).data("hasImg")) {
        layer.msg("暂无图片");
        return false;
    }
    var imgs = $(ele).data("imgs");
    HotUtil.viewImageLayer(imgs, isAllowDel, function () {
        if ($(".picView-magnify-list").data("delete")) {
            //如果删除过照片则需要在关闭预览窗口的时候刷新整个表格
            var scrollObj = {
                isScroll: true,
                row: $(ele).attr("row"),
                col: $(ele).attr("col"),
            };
            reloadTable(treeNode, scrollObj);
        }
    }, function ($i, $ul) {
        // 在删除图片操作前进行异步乐观锁检查
        HotUtil.checkOptimisticLockBeforeAction(treeNode, "删除图片", function (lockCheckResult) {
            if (!lockCheckResult.success) {
                // 乐观锁检查失败，直接显示错误信息并返回
                layer.alert(lockCheckResult.errorMsg, {
                    icon: 2,
                    title: '删除失败'
                });
                return; // 直接返回，不执行后续逻辑
            }

            var src = $i.attr("photo-path");
            var photoShowNum = $i.attr("photo-num");
            var photoName = $i.attr("photo-name");
            var log = {};
            log.operation = "删除照片";
            log.tablePid = treeNode.PID;
            log.tableId = treeNode.ID;
            log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID +
                "）】的表中的第" +
                (parseInt($(ele).attr("row")) + 1) + "行第" + (parseInt($(ele)
                    .attr("col")) + 1) +
                "列删除了照片（名称：" + photoName + "，编号：" + photoShowNum + "，路径：" +
                src + "）";

            twxAjax(THING, "DeletePhoto", {
                id: treeNode.ID,
                src: src,
                col: $(ele).attr("col"),
                row: $(ele).attr("row")
            }, true, function (res) {
                if (res.success) {
                    log.reqResult = 1;
                    $i.parent().parent().remove();
                    $ul.data("delete", true);
                    layer.msg(res.msg);
                } else {
                    log.reqResult = 0;
                    layer.alert(res.msg, {
                        icon: 2
                    });
                }
                addConfirmLog(log);
            }, function () {
                layer.alert("删除失败");
            });
        });
    });
};