/**
 * 不合格品审理单相关图表处理
 * <AUTHOR>
 * @date 2025年5月23日
 */

/**
 * 不合格品审理单柱状图提示格式化函数
 * @param {Object} params
 */
var nonconformityTooltipFormatter = function (params) {
    var value = params.value[params.dimensionNames.indexOf(params.seriesName)];
    if (!value || value === '0') {
        return '';
    }
    // 兼容渐变色和纯色
    var dotColor = (typeof params.color === 'object' && params.color.colorStops)
        ? params.color.colorStops[0].color
        : params.color;

    // 创建一个唯一ID，用于饼图容器
    var pieId = 'pie-chart-' + new Date().getTime();

    // 构建包含饼图的tooltip HTML
    var tooltipHtml = '<div style="padding:10px;background:rgba(255,255,255,0.95);border-radius:5px;box-shadow:0 0 10px rgba(0,0,0,0.1);">\
                        <div style="font-size:15px;color:#333;font-weight:bold;margin-bottom:8px;border-bottom:1px solid #eee;padding-bottom:6px;">' +
        '<span style="display:inline-block;width:12px;height:12px;border-radius:50%;background:' + dotColor + ';margin-right:6px;vertical-align:middle;"></span>' +
        params.seriesName + '</div>\
                        <div style="text-align:center;margin-bottom:10px;">\
                            <span style="font-size:16px;color:#333;font-weight:bold;">' + params.name.split("~~~")[0] + '</span>\
                            <span style="font-size:16px;color:#333;font-weight:bold;margin-left:5px;">' + value + '</span>\
                        </div>\
                        <div id="' + pieId + '" style="width:240px;height:180px;margin:0 auto;"></div>\
                    </div>';

    // 使用setTimeout创建饼图（因为需要等待DOM元素创建完成）
    setTimeout(function () {
        var pieContainer = document.getElementById(pieId);
        if (!pieContainer) return;

        // 获取状态数据
        var finishedCount = 0;
        var unfinishedCount = 0;
        var total = parseInt(value);
        var legendTitle = '';
        var modelId = params.name.split("~~~")[1];
        var seriesName = params.seriesName;

        // 根据不同系列选择不同的数据和标题
        if (window.nonconformityStatusData) {
            if (seriesName === '总数量') {
                legendTitle = '完成情况';
                finishedCount = window.nonconformityStatusData.finished[modelId] || 0;
                unfinishedCount = window.nonconformityStatusData.unfinished[modelId] || 0;
                // 使用真实数据，不再生成随机数据
                // 如果没有数据，则使用0
            } else {
                legendTitle = seriesName + '情况';
                if (window.nonconformityStatusData.severityStatus &&
                    window.nonconformityStatusData.severityStatus[seriesName]) {
                    finishedCount = window.nonconformityStatusData.severityStatus[seriesName].finished[modelId] || 0;
                    unfinishedCount = window.nonconformityStatusData.severityStatus[seriesName].unfinished[modelId] || 0;
                    // 使用真实数据，不再生成随机数据
                    // 如果没有数据，则使用0
                } else {
                    // 使用真实数据，不再生成随机数据
                    finishedCount = 0;
                    unfinishedCount = total;
                }
            }
        } else {
            // 使用真实数据，不再生成随机数据
            finishedCount = 0;
            unfinishedCount = total;
            legendTitle = seriesName === '总数量' ? '完成情况' : seriesName + '情况';
        }

        var finishedPercent = total > 0 ? Math.round((finishedCount / total) * 100) : 0;
        var unfinishedPercent = 100 - finishedPercent;

        var finishedLabel = '已完成';
        var unfinishedLabel = '未完成';

        var pieChart = echarts.init(pieContainer);
        pieChart.setOption({
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                formatter: '{b}: {c} ({d}%)',
                backgroundColor: 'rgba(255,255,255,0.8)',
                borderColor: '#ccc',
                borderWidth: 1,
                textStyle: {
                    color: '#333'
                }
            },
            legend: {
                orient: 'vertical',
                right: '0%',
                top: 'middle',
                itemWidth: 10,
                itemHeight: 10,
                itemGap: 10,
                textStyle: {
                    color: '#666',
                    fontSize: 12
                },
                formatter: function (name) {
                    if (name === finishedLabel) {
                        return name + ' ' + finishedCount + '(' + finishedPercent + '%)';
                    } else {
                        return name + ' ' + unfinishedCount + '(' + unfinishedPercent + '%)';
                    }
                },
                data: [finishedLabel, unfinishedLabel]
            },
            series: [{
                name: legendTitle,
                type: 'pie',
                radius: ['40%', '65%'],
                center: ['28%', '50%'],
                avoidLabelOverlap: true,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: finishedCount, name: finishedLabel, itemStyle: { color: '#67C23A' } },
                    { value: unfinishedCount, name: unfinishedLabel, itemStyle: { color: '#F56C6C' } }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }],
            animation: true
        });
    }, 10);

    return tooltipHtml;
}

/**
 * 处理不合格品审理单柱状图点击事件
 * @param {Object} params 图表点击参数
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 * @param {String} seriesName 系列名称
 */
function nonconformityChartClick(params, startDate, endDate, seriesName) {
    // 获取型号ID
    var modelId, modelName;

    // 处理不同格式的name
    if (typeof params.name === 'string' && params.name.indexOf("~~~") !== -1) {
        // 旧格式：name包含~~~分隔符
        modelName = params.name.split("~~~")[0];
        modelId = params.name.split("~~~")[1];
    } else {
        // 新格式：直接使用name
        modelName = params.name;
        // 尝试从数据中获取modelId
        if (window.nonconformityStatusData && window.nonconformityStatusData.modelMap) {
            modelId = window.nonconformityStatusData.modelMap[modelName];
        }

        if (!modelId) {
            modelId = "unknown";
            return; // 如果无法确定modelId，则不继续处理
        }
    }

    // 定义初始严重程度筛选值
    var severityLevel = "all";

    // 根据系列名称确定严重程度筛选条件
    if (seriesName !== '总数量') {
        severityLevel = seriesName;
    }

    // 构建文件类型字符串
    var fileType = modelName + ' - ' + seriesName;

    // 调用详情tab显示函数
    showNonconformityDetailTab(modelId, startDate, endDate, fileType, severityLevel);
}

/**
 * 显示不合格品审理单详细信息tab页
 * @param {String} modelId 模型ID
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 * @param {String} fileType 文件类型
 * @param {String} severityLevel 严重程度筛选值
 */
function showNonconformityDetailTab(modelId, startDate, endDate, fileType, severityLevel) {
    var modelName = fileType.split(' - ')[0] || '不合格品审理单';
    var seriesName = fileType.split(' - ')[1] || '详情';

    // 存储当前fileType，用于后续筛选
    var curFileType = fileType;
    var currentStatus = 'all';
    var currentSeverityLevel = severityLevel || 'all'; // 严重程度由点击的柱状图确定，不再变更

    // 构建筛选按钮HTML - 只保留状态筛选和导出按钮
    var tableSelectHtml = '<div class="status-select-container layui-form" style="position:absolute;right:50px;top:10px;z-index:10;width:285px;">' +
        '<div class="layui-form-item" style="margin-bottom:0;">' +
        '<div class="layui-inline" style="margin-right:0;margin-bottom:0;">' +
        '<span style="color:#fff;font-weight:500;float:left;line-height:30px;margin-right:10px;">状态筛选：</span>' +
        '<div class="model-select" style="width:100px;float:left;">' +
        '<select id="table-status-select" name="table-status-select" lay-filter="status-select">' +
        '<option value="all" selected>全部</option>' +
        '<option value="finished">已完成</option>' +
        '<option value="unfinished">未完成</option>' +
        '</select>' +
        '</div>' +
        '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="export-table-btn" style="margin-left:10px;margin-top:0px;float:left;">' +
        '<i class="layui-icon layui-icon-export"></i> 导出数据' +
        '</button>' +
        '</div>' +
        '</div>' +
        '</div>';

    layer.tab({
        type: 1,
        tab: [{
            title: '详细表格',
            content: '<div id="tableContent" style="height: 620px;padding:15px 50px 50px 50px;position:relative;">' + tableSelectHtml + '<div style="margin-top:45px;"></div><table id="list-table"></table></div>'
        }],
        anim: false,
        openDuration: 200,
        skin: 'layui-layer-tab my-layer',
        isOutAnim: false,
        closeDuration: 200,
        closeBtn: 2,
        shadeClose: false,
        maxmin: false,
        resize: false,
        area: ['1500px', '770px'],
        success: function () {
            // 初始化表单组件
            form.render();

            // 定义表格列配置
            var cols = [[
                { title: '序号', type: 'numbers', width: 70 },
                { field: 'BILLNO', title: '单据编号' },
                { field: 'XH', title: '型号', width: 120 },
                { field: 'YZJD', title: '研制阶段', width: 120 },
                {
                    field: 'BHGPYZCD', title: '严重程度', width: 100, templet: function (d) {
                        var color = '';
                        if (d.BHGPYZCD === '一级') {
                            color = 'layui-bg-red';
                        } else if (d.BHGPYZCD === '二级') {
                            color = 'layui-bg-orange';
                        } else if (d.BHGPYZCD === '三级') {
                            color = 'layui-bg-green';
                        }
                        return '<span class="layui-badge ' + color + '">' + d.BHGPYZCD + '</span>';
                    }
                },
                {
                    field: 'STATUS_TEXT', title: '状态', width: 100, templet: function (d) {
                        return d.STATUS_TEXT === '已完成' ?
                            '<span class="layui-badge layui-bg-green">已完成</span>' :
                            '<span class="layui-badge layui-bg-orange">未完成</span>';
                    }
                },
                { field: 'CREATE_DATE', title: '创建日期', width: 120 },
                {
                    title: '操作', width: 120, templet: function () {
                        return '<a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>';
                    }
                }
            ]];

            // 初始化表格
            table.render({
                id: "list-table",
                elem: '#list-table',
                skin: "nob",
                url: getUrl(thing, 'QueryNonconformityList', ""),
                where: {
                    treeId: modelId,
                    username: username,
                    startDate: startDate,
                    endDate: endDate,
                    status: currentStatus,
                    severityLevel: currentSeverityLevel
                },
                page: {
                    layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                    groups: 1,
                    first: false,
                    last: false
                },
                cols: cols,
                even: true
            });

            // 监听表格工具条事件
            table.on('tool(list-table)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    showNonconformityDetail(data);
                }
            });

            // 添加状态筛选下拉框选择事件
            form.on('select(status-select)', function (data) {
                currentStatus = data.value;
                // 重新加载表格数据
                table.reload('list-table', {
                    page: {
                        layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                        groups: 1,
                        first: false,
                        last: false,
                        curr: 1
                    },
                    where: {
                        treeId: modelId,
                        username: username,
                        startDate: startDate,
                        endDate: endDate,
                        status: currentStatus,
                        severityLevel: currentSeverityLevel
                    }
                });
            });

            // 添加导出按钮点击事件
            $('#export-table-btn').on('click', function () {
                // 显示loading
                var loading = layer.msg('正在导出数据，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 调用导出接口
                $.fileDownload(fileHandlerUrl + '/aitScreen/exportNonconformityExcel', {
                    httpMethod: 'POST',
                    data: {
                        treeId: modelId,
                        username: username,
                        startDate: startDate,
                        endDate: endDate,
                        status: currentStatus,
                        severityLevel: currentSeverityLevel
                    },
                    successCallback: function () {
                        layer.close(loading);
                        layer.msg('导出成功', { icon: 1 });
                    },
                    failCallback: function (responseHtml) {
                        layer.close(loading);
                        layer.msg('导出失败', { icon: 2 });
                    }
                });
            });
        }
    });
}

/**
 * 显示不合格品审理单详情
 * @param {Object} data 不合格品审理单数据
 */
function showNonconformityDetail(data) {
    // 辅助函数：安全获取字段值
    function getSafeValue(value) {
        return value || '';
    }

    // 辅助函数：格式化日期
    function formatDate(dateStr) {
        if (!dateStr) return '';
        try {
            if (dateStr instanceof Date) {
                return dateFormat(dateStr, "yyyy-MM-dd");
            } else if (typeof dateStr === 'string') {
                return dateStr.indexOf('-') > -1 ? dateStr : '';
            }
            return dateStr;
        } catch (e) {
            return dateStr || '';
        }
    }

    // 定义CSS样式 - 模拟bhgpsl.html中的样式
    var formStyles = `
        <style>
            .aws-form-ux-container {
                font-family: "微软雅黑", Arial, sans-serif;
                background: #fff;
                padding: 10px;
            }
            .aws-form-ux-maintable {
                width: 100%;
                table-layout: fixed;
                border-collapse: collapse;
                margin: 0 auto;
            }
            .aws-form-ux-titlebg {
                background: #f5f5f5;
            }
            .aws-form-ux-header {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border: 1px solid #000;
                position: relative;
                border-bottom: none;
            }
            .awsui-ux.table-striped {
                width: 100%;
                table-layout: fixed;
                border-collapse: collapse;
                padding: 0px;
            }
            .awsui-ux-title {
                background: #f9f9f9;
                font-weight: bold;
                text-align: center;
                padding: 8px;
                border: 1px solid #000;
                font-family: "微软雅黑";
                white-space: nowrap;
                vertical-align: middle;
            }
            .aws-form-ux-content {
                padding: 8px;
                border: 1px solid #000;
                vertical-align: middle;
                min-height: 20px;
            }
            .aws-form-ux-content.colspan_3 {
                padding: 12px 8px;
                min-height: 40px;
            }
            .sqdh {
                font-size: 14px;
                font-weight: normal;
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
            }
            /* 内嵌表格样式 - 无边框 */
            .inner-table {
                width: 100%;
                border-collapse: collapse;
            }
            .inner-table td {
                border: none !important;
                padding: 4px;
                vertical-align: middle;
            }
            .inner-table .inner-title {
                text-align: right;
                font-size: 14px;
                font-weight: normal;
                padding-right: 8px;
            }
            /* 强制列宽设置 */
            .awsui-ux.table-striped col:nth-child(1) {
                width: 10% !important;
            }
            .awsui-ux.table-striped col:nth-child(2) {
                width: 23% !important;
            }
            .awsui-ux.table-striped col:nth-child(3) {
                width: 10% !important;
            }
            .awsui-ux.table-striped col:nth-child(4) {
                width: 23% !important;
            }
            .awsui-ux.table-striped col:nth-child(5) {
                width: 10% !important;
            }
            .awsui-ux.table-striped col:nth-child(6) {
                width: 24% !important;
            }
            /* 强制单元格宽度 */
            .awsui-ux.table-striped td:nth-child(1) {
                width: 10% !important;
                max-width: 10% !important;
            }
            .awsui-ux.table-striped td:nth-child(2) {
                width: 23% !important;
                max-width: 23% !important;
            }
            .awsui-ux.table-striped td:nth-child(3) {
                width: 10% !important;
                max-width: 10% !important;
            }
            .awsui-ux.table-striped td:nth-child(4) {
                width: 23% !important;
                max-width: 23% !important;
            }
            .awsui-ux.table-striped td:nth-child(5) {
                width: 10% !important;
                max-width: 10% !important;
            }
            .awsui-ux.table-striped td:nth-child(6) {
                width: 24% !important;
                max-width: 24% !important;
            }
        </style>
    `;

    // 完全按照bhgpsl.html结构构建表单内容
    var detailContent = formStyles +
        '<div class="aws-form-ux-container">' +
        '<table class="aws-form-ux-maintable">' +
        '<tbody>' +
        // 标题行
        '<tr class="aws-form-ux-titlebg">' +
        '<td class="aws-form-ux-header">' +
        '不合格品审理单<br />' +
        '<div class="sqdh">单据编号：' + getSafeValue(data.BILLNO) + '</div>' +
        '</td>' +
        '</tr>' +
        // 表单内容区
        '<tr class="aws-form-ux-formcontent">' +
        '<td>' +
        '<table class="awsui-ux table-striped" style="padding: 0px; table-layout: fixed; margin-left: -1px;" width="100%" cellspacing="1" cellpadding="0">' +
        '<colgroup>' +
        '<col class="awsui-ux-title" style="width: 10%;" />' +
        '<col style="width: 23%;" />' +
        '<col class="awsui-ux-title" style="width: 10%;" />' +
        '<col style="width: 23%;" />' +
        '<col class="awsui-ux-title" style="width: 10%;" />' +
        '<col style="width: 24%;" />' +
        '</colgroup>' +
        '<tbody>' +
        // 第一行：型号、研制阶段、产品名称
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>型号</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.XH) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>研制阶段</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.YZJD) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: break-spaces;"><b>产品（或零部组件）名称</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.CP) + '</td>' +
        '</tr>' +
        // 第二行：产品编号、批次号、产品图号
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>产品编号</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.CPBH) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>批次号</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.PCH) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>产品图号（代号）</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.CPTH) + '</td>' +
        '</tr>' +
        // 第三行：工序号、发现地点、不合格品数量
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>工序号</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.GXH) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>发现地点</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.FXDD) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>不合格品数量</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.BHGSL) + '</td>' +
        '</tr>' +
        // 第四行：责任部门、操作者、送检数
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>责任部门</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.ZRBM) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>操作者</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.CZZ) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>送检数</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.SJS) + '</td>' +
        '</tr>' +
        // 第五行：发现日期、审理日期、审理地点
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>发现日期</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.FXRQ) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>审理日期</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.SLRQ) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-family: 微软雅黑;"><span style="white-space: nowrap;"><b>审理地点</b></span></span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.SLDD) + '</td>' +
        '</tr>' +
        // 不合格品情况描述区域
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">不合格品<br />情况描述</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td colspan="6" class="aws-form-ux-content">' + getSafeValue(data.BHGPQKMS) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""></td>' +
        '<td class=""></td>' +
        '<td class="inner-title"><span style="font-size: 14px;">质量检验人员：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.BZRY) + '</td>' +
        '<td class="inner-title"><span style="font-size: 14px;">日期：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.ZDRQ) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 不合格品严重程度区域
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">不合格品<br />严重程度</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td colspan="6" class="aws-form-ux-content">' + getSafeValue(data.BHGPYZCD) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""></td>' +
        '<td class=""></td>' +
        '<td class="inner-title"><span style="font-size: 14px;">质量检验人员：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.QZ1) + '</td>' +
        '<td class="inner-title"><span style="font-size: 14px;">日期：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.RQ1) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 原因分类
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">原因分类</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content colspan_3">' + getSafeValue(data.YYFL) + '</td>' +
        '</tr>' +
        // 原因分析
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">原因分析</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content colspan_3">' + getSafeValue(data.YYFX) + '</td>' +
        '</tr>' +
        // 不合格品审理意见
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">不合格品<br />审理意见</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content colspan_3">' + getSafeValue(data.BHGPSLYJ) + '</td>' +
        '</tr>' +
        // 纠正和纠正措施
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">纠正</span></strong></span></td>' +
        '<td class="aws-form-ux-content colspan_3">' + getSafeValue(data.JZ) + '</td>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">纠正措施</span></strong></span></td>' +
        '<td class="aws-form-ux-content colspan_3" colspan="3">' + getSafeValue(data.JZCS) + '</td>' +
        '</tr>' +
        // 不合格品审理组长
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">不合格品<br />审理组长：</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content">' + getSafeValue(data.BHGPSLZZ) + '</td>' +
        '</tr>' +
        // 不合格品审理人员签字
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">不合格品<br />审理人员签字</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td colspan="6" class="aws-form-ux-content">' + getSafeValue(data.YJ2) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""></td>' +
        '<td class=""></td>' +
        '<td class="inner-title"><span style="font-size: 14px;">签字：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.QZ2) + '</td>' +
        '<td class="inner-title"><span style="font-size: 14px;">日期：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.RQ2) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 审理组组长确认 - 第一行
        '<tr>' +
        '<td class="awsui-ux-title" rowspan="3"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">审理组组长确认</span></strong></span></td>' +
        '<td class="aws-form-ux-content" colspan="4">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td width="20%" class="awsui-ux-title"><span style="font-family: 微软雅黑;"><b>提交型号总体审查：</b></span></td>' +
        '<td class="">' + getSafeValue(data.TJXHZTSC) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '<td class="aws-form-ux-content" rowspan="3">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td class="">' + getSafeValue(data.QZ3) + '</td>' +
        '<td style="width: 5% !important; text-align: center;" class="">/</td>' +
        '<td class="">' + getSafeValue(data.RQ3) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 审理组组长确认 - 第二行
        '<tr>' +
        '<td class="aws-form-ux-content" colspan="4">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td width="20%" class="awsui-ux-title"><span style="font-family: 微软雅黑;"><b>提交院不合格品审理委员会审查：</b></span></td>' +
        '<td class="">' + getSafeValue(data.TJYBHGPWYHSC) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 审理组组长确认 - 第三行
        '<tr>' +
        '<td class="aws-form-ux-content" colspan="4">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td width="20%" class="awsui-ux-title"><span style="font-family: 微软雅黑;"><b>提交军代表审查：</b></span></td>' +
        '<td class="">' + getSafeValue(data.TJJDBSC) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 型号总体意见
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">型号总体意见</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td colspan="6" class="aws-form-ux-content">' + getSafeValue(data.YJ3) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""></td>' +
        '<td class=""></td>' +
        '<td class="inner-title"><span style="font-size: 14px;">签字：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.QZ4) + '</td>' +
        '<td class="inner-title"><span style="font-size: 14px;">日期：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.RQ4) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 院不合格品审理委员会意见
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">院不合格品<br />审理委员会意见</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td colspan="6" class="aws-form-ux-content">' + getSafeValue(data.YJ4) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""></td>' +
        '<td class=""></td>' +
        '<td class="inner-title"><span style="font-size: 14px;">签字：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.QZ5) + '</td>' +
        '<td class="inner-title"><span style="font-size: 14px;">日期：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.RQ5) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 军代表意见
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">军代表意见</span></strong></span></td>' +
        '<td colspan="5" class="aws-form-ux-content">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td colspan="6" class="aws-form-ux-content">' + getSafeValue(data.YJ5) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""></td>' +
        '<td class=""></td>' +
        '<td class="inner-title"><span style="font-size: 14px;">签字：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.QZ6) + '</td>' +
        '<td class="inner-title"><span style="font-size: 14px;">日期：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.RQ6) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 落实情况
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">落实情况</span></strong></span></td>' +
        '<td class="aws-form-ux-content" colspan="5">' +
        '<table class="inner-table">' +
        '<tbody>' +
        '<tr>' +
        '<td style="width: 20% !important; max-width: 20% !important;"><span style="color: #660000; font-family: 微软雅黑;"><span style="font-size: 15px;"><b>不合格品落实情况：</b></span></span></td>' +
        '</tr>' +
        '<tr>' +
        '<td class="aws-form-ux-content" colspan="6">' + getSafeValue(data.BHGPCZLSQK) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""><span style="font-size: 15px; font-family: 微软雅黑;"><br /><strong><span style="color: #660000;">纠正措施落实情况：</span></strong></span></td>' +
        '</tr>' +
        '<tr>' +
        '<td class="aws-form-ux-content" colspan="6">' + getSafeValue(data.JZCSLSQK) + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td class=""></td>' +
        '<td class=""></td>' +
        '<td class="inner-title" style="width: 15% !important;max-width: 15% !important;"><span style="font-size: 14px;">型号和专业质量师：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.QZ7) + '</td>' +
        '<td class="inner-title"><span style="font-size: 14px;">日期：</span></td>' +
        '<td class="aws-form-ux-content">' + getSafeValue(data.RQ7) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        // 上传审查意见
        '<tr>' +
        '<td class="awsui-ux-title"><span style="font-size: 15px;"><strong><span style="font-family: 微软雅黑;">上传审查意见</span></strong></span></td>' +
        '<td class="aws-form-ux-content" colspan="5">' + getSafeValue(data.SCSCYJ) + '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</td>' +
        '</tr>' +
        '</tbody>' +
        '</table>' +
        '</div>';

    layer.open({
        type: 1,
        title: '不合格品审理单详情',
        area: ['1400px', '900px'],
        content: detailContent,
        maxmin: true,
        scrollbar: true
    });
}

/**
 * 加载不合格品审理单柱状图
 * @param {String} modelId 模型ID
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 */
function loadNonconformityChart(modelId, startDate, endDate) {
    var chartDom = document.getElementById('bhgp-chart');
    if (!chartDom) {
        return;
    }

    // 确保图表容器有足够的高度
    if (chartDom.offsetHeight < 50) {
        chartDom.style.height = '300px';
    }

    var myChart = echarts.init(chartDom);
    myChart.showLoading("default", loadingOption);

    twxAjax(thing, 'QueryNonconformityCount', {
        username: username,
        treeId: modelId,
        startDate: startDate,
        endDate: endDate
    }, true, function (res) {
        if (res.success) {
            myChart.hideLoading();
            var option = res.data.option;

            // 保存状态数据到全局变量，供提示框使用
            window.nonconformityStatusData = res.data.statusData;

            // 获取总数数据，用于图例显示
            var totalData = res.data.total || {};

            // 设置图例formatter，显示总数
            option.legend.formatter = function (name) {
                var count = totalData[name] || 0;
                return name + ' (' + count + ')';
            };

            // 设置图例项间距，缩小换行后的间距
            option.legend.itemGap = 6;

            // 设置提示框格式化函数
            option.tooltip.formatter = nonconformityTooltipFormatter;

            // 设置X轴标签格式化函数
            option.xAxis.axisLabel.formatter = xAxisLabelFormatter;

            // 渲染图表
            try {
                myChart.setOption(option, true); // 添加第二个参数true，强制清除之前的图表
            } catch (error) {
            }

            // 绑定点击事件
            myChart.off('click');
            myChart.on('click', function (params) {
                nonconformityChartClick(params, startDate, endDate, params.seriesName);
            });
        } else {
            myChart.hideLoading();
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (error) {
        myChart.hideLoading();
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}
