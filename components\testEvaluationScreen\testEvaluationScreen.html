<!DOCTYPE html>
<html style="background: #0a1931;">

<head>
    <meta charset="utf-8" />
    <title>试验鉴定数据看板</title>
    <link rel="icon" href="../../img/chart.png" type="image/png" />
    <link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" type="text/css" />
    <link rel="stylesheet" href="testEvaluationScreen.css" type="text/css" />
    <style>
        body {
            background: #0a1931 !important;
        }
    </style>

    <script src="../../plugins/easyui/jquery.min.js"></script>
    <script src="../../plugins/jsPlumb/jsplumb.bundle.js"></script>
    <script src="../../plugins/echarts/echarts.min.js"></script>
    <script src="../../plugins/layui-lasted/layui.js"></script>
    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>
</head>

<body>
    <div class="container">
        <div class="title-container">
            <h1 class="page-title">试验鉴定看板</h1>
            <div class="model-selector">
                <form class="layui-form">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">型号选择</label>
                            <div class="layui-input-inline">
                                <select name="model" lay-filter="model-select" lay-search id="model-select">
                                    <option value="-1">所有型号</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="content">
            <div class="top-section">
                <!-- 统计表格 -->
                <table class="statistics-table">
                    <thead></thead>
                    <tbody></tbody>
                </table>
                <div class="table-divider"></div>
                <!-- 状态表格 -->
                <table class="status-table">
                    <thead></thead>
                    <tbody></tbody>
                </table>
                <!-- 列装定型表格 -->
                <div class="table-divider"></div>
                <table class="deployment-type-table">
                    <thead></thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="bottom-section">
                <div class="bottom-left-model">
                    <div id="modelProgressChart" class="chart-container"></div>
                </div>
                <div class="bottom-left">
                    <div id="productChart" class="chart-container"></div>
                </div>
                <div class="bottom-right">
                    <div id="fileChart" class="chart-container"></div>
                </div>
            </div>
        </div>
    </div>
    <script src="statisticsTable.js"></script>
    <script src="statusTable.js"></script>
    <script src="deploymentTypeTable.js"></script>
    <script src="productChart.js"></script>
    <script src="fileChart.js"></script>
    <script src="modelProgressChart.js"></script>
    <script src="testEvaluationScreen.js"></script>
</body>

</html>
