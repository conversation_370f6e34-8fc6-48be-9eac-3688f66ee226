var curInfo = {
	tableId: 'dataTable',
	tableType: '查看影像记录',
	gphoto:{}

};


$(document).ready(function() {
	layui.use(['layer','form'], function() {
		curInfo.gphoto = parent.window.gphoto;
		//初始化easyui的表格
		initTableComp();
		//加载数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	});
});

//初始化表格 - 
var initTableComp = function(layui) {
	$('#' + curInfo.tableId).datagrid({
		data: [],
		singleSelect: true,
		fitColumns: true,
		striped: true,
		fit: true,
		rownumbers: true,
		pagination: true,
		columns: [
				[
					{
						field: 'FILE_TYPE',
						title: '文件类别',
						width: 150,
						align: 'center'
					},
					{
						field: 'FILE_NUMBER',
						title: '文件编号',
						hidden: true
					},
					{
						field: 'FILE_NAME',
						title: '文件名称',
						width: 300,
						align: 'center'
					},
					{
						field: 'GATHERING_METHOD',
						title: '采集方式',
						width: 100,
						align: 'center'
					},
					{
						field: 'SOURCE_SYSTEM',
						title: '来源系统',
						width: 100,
						align: 'center'
					},
					{
						field: 'SECURITY_LEVEL',
						title: '密级',
						width: 100,
						align: 'center'
					},
					{
						field: 'STATE_CHECK',
						title: '状态',
						width: 100,
						align: 'center'
					},
					{
						field: 'DELIVERY_STATE',
						title: '交付状态',
						width: 100,
						align: 'center'
					},
					{
						field: 'USER_FULLNAME',
						title: '创建人',
						width: 100,
						align: 'center'
					},
					{
						field: 'CREATE_TIMESTAMP',
						title: '创建日期',
						width: 150,
						align: 'center'
					},
					{
						field: 'FILE_FORMAT',
						title: '文件形式',
						width: 100,
						align: 'center',
						formatter: formatterEmptyValue
					}, {
						field: 'op',
						title: '操作',
						align: 'center',
						width: 150,
						formatter: opFomatter
					}
				]
			],
		loadMsg: '正在加载数据...',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>'
	});

	//初始化分页控件
	initPagination(curInfo.tableId, []);
};

//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var getFieldValue = function() {
	var param = {};
	param = curInfo.gphoto;
	return param;
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	$('#' + curInfo.tableId).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
}


//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(data) {
		pageLoadFlag = true;
		totalRecords = data.rows[0].COUNT;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function() {};

	//传递参数
	var param = getFieldValue();
	twxAjax('Thing.Fn.DataSearch', 'QueryProcessPhotoDataCount', param, true, cb_success, cb_error);
};

//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + curInfo.tableId).datagrid('loading');
	initTotalRecords();
	var cb_success = function(data) {
		//调用成功后，渲染数据
		$('#' + curInfo.tableId).datagrid('loadData', data.rows);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#' + curInfo.tableId).datagrid('loaded');
	};
	var cb_error = function() {
		$('#' + curInfo.tableId).datagrid('loaded');
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	//传递的参数
	var param = getFieldValue();
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.DataSearch', 'QueryProcessPhotoData', param, true, cb_success, cb_error);
};

//初始化分页组件
var initPagination = function(tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {

		},
		onRefresh: function(pageNumber, pageSize) {
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	});
};
