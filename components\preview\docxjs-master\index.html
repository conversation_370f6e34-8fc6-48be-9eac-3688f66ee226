<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<link href="../../../plugins/layui-lasted/css/layui.css" rel="stylesheet">
		<link href="../../../plugins/docx/bootstrap.min.css" rel="stylesheet">
		<script src="../../../plugins/layui-lasted/layui.js"></script>
		<script>
			layui.use(function() {
				var layer = layui.layer;
				var loading = layer.msg('正在加载中', {
					icon: 16,
					shade: 0.01
				});
			});
		</script>
		<script src="../../../plugins/docx/jszip.min.js"></script>
		<script src="dist/docx-preview.js"></script>
	</head>

	<body class="vh-100 d-flex flex-column">
		<div class="flex-grow-1 d-flex flex-row" style="height: 0;">
			<div id="document-container" class="overflow-auto flex-grow-1 h-100"></div>
		</div>

		<script>
			var currentDocument = null;
			var docxOptions = Object.assign(docx.defaultOptions, {
				debug: false,
				experimental: true,
				ignoreWidth: true,
				inWrapper: false
			});
			var container = document.querySelector("#document-container");

			function renderDocx(file) {
				currentDocument = file;

				if (!currentDocument) {
					return;
				}

				docx.renderAsync(currentDocument, container, null, docxOptions)
					.then(function(x) {
						layui.layer.closeAll();
					});

			}

			function load(url) {
				fetch(url)
					.then(function(resp) {
						return resp.blob();
					})
					.then(function(blob) {
						renderDocx(blob);
					});
			}
			container.addEventListener("dragover", function(ev) {
				ev.preventDefault();
			});
			container.addEventListener("drop", function(ev) {
				ev.preventDefault();
				renderDocx(ev.dataTransfer.files[0]);
			});
			var url = sessionStorage.getItem('docxurl');
			load(url);
		</script>
	</body>

</html>