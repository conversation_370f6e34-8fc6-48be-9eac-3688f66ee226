var formConfig = {
	initProcess: {
		z_num: {
			show: true,
			readonly: true,
			disabled: false,
			verify: "required"
		},
		z_question_num: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required"
		},
		z_phase: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required"
		},
		z_product_name: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required"
		},
		z_product_num: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required"
		},
		z_place: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required"
		},
		z_date: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required|date"
		},
		z_department: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required"
		},
		z_question_desc: {
			show: true,
			readonly: false,
			disabled: false,
			verify: "required",
			hasSuffix: false
		},
		z_request: {
			show: false
		},
		z_leader_opinion: {
			show: false
		},
		z_business_opinion: {
			show: false
		},
		z_witness_file_name: {
			type: 'file',
			show: false,
		},
		z_loop: {
			show: false
		}
	},
	viewProcess: {
		z_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_phase: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_name: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_place: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_date: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_department: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_desc: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_request: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_leader_opinion: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_business_opinion: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_witness_file_name: {
			type: 'file',
			show: true,
			readonly: true,
			disabled: true
		},
		z_loop: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		}
	},
	"填写归零要求": {
		z_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_phase: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_name: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_place: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_date: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_department: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_desc: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_request: {
			show: true,
			readonly: false,
			disabled: false,
			hasSuffix: false,
			isPost: true,
			passField: "z_request_if",
			userField: "z_request_u",
			nextUserField: "z_leader_opinion_u",
			isReason: true,
			dateField: "z_request_d",
			verify: "required"
		},
		z_leader_opinion: {
			show: false
		},
		z_business_opinion: {
			show: false
		},
		z_witness_file_name: {
			show: false
		},
		z_loop: {
			show: false
		}
	},
	"批准归零要求": {
		z_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_phase: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_name: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_place: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_date: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_department: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_desc: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_request: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_leader_opinion: {
			show: true,
			readonly: false,
			disabled: false,
			hasSuffix: false,
			passField: "z_leader_opinion_if",
			userField: "z_leader_opinion_u",
			nextUserField: "z_business_opinion_u",
			dateField: "z_leader_opinion_d",
			isReason: true,
			isPost: true,
			verify: "required"
		},
		z_business_opinion: {
			show: false
		},
		z_witness_file_name: {
			show: false
		},
		z_loop: {
			show: false
		}
	},
	"业务部门接收并指派任务": {
		z_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_phase: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_name: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_place: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_date: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_department: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_desc: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_request: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_leader_opinion: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_business_opinion: {
			show: true,
			readonly: false,
			disabled: false,
			hasSuffix: false,
			passField: "z_business_opinion_if",
			userField: "z_business_opinion_u",
			nextUserField: "z_witness_file_u",
			dateField: "z_business_opinion_d",
			isReason: true,
			isPost: true,
			verify: "required"
		},
		z_witness_file_name: {
			show: false
		},
		z_loop: {
			show: false
		}
	},
	"归零工作负责人提交见证材料": {
		z_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_phase: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_name: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_place: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_date: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_department: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_desc: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_request: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_leader_opinion: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_business_opinion: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_witness_file_name: {
			type: 'file',
			show: true,
			readonly: true,
			disabled: false,
			hasSuffix: false,
			passField: "z_witness_file_if",
			userField: "z_witness_file_u",
			nextUserField: "z_loop_u",
			dateField: "z_witness_file_d",
			isPost: true,
			verify: "required"
		},
		z_loop: {
			show: false
		}
	},
	"监督闭环情况检查": {
		z_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_phase: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_name: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_product_num: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_place: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_date: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_department: {
			show: true,
			readonly: true,
			disabled: true
		},
		z_question_desc: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_request: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_leader_opinion: {
			show: true,
			readonly: true,
			disabled: true,
			hasSuffix: true
		},
		z_witness_file_name: {
			type: 'file',
			show: true,
			disabled: true
		},
		z_loop: {
			show: true,
			readonly: false,
			disabled: false,
			hasSuffix: false,
			passField: "z_loop_if",
			userField: "z_loop_u",
			dateField: "z_loop_d",
			isReason: true,
			isPost: true,
			verify: "required"
		}
	}
};