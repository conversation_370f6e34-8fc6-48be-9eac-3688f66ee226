<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

    <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">


    <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script>
    <script src="../../plugins/layui/layui.js"></script>
    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>
    <title>设计类数据清单</title>
</head>
<body>

<div class="easyui-layout" style="width:100%;height:100%;">
    <div id="p" data-options="region:'west',split:true" title="数据包结构树" style="width:30%;padding:10px">
        <p>width: 30%</p>
    </div>
    <div data-options="region:'center'" title="数据清单列表">
        <table class="easyui-datagrid"  id="taskTable" style="height: 500px">

        </table>
    </div>
</div>

<div id="tb" style="padding:2px 5px;">
    <a class="easyui-linkbutton" iconCls="icon-myadd" plain="true" func-id="craftDataList-1" style=""  onclick="addTask()">模板导入</a>
    <a class="easyui-linkbutton" iconCls="icon-myadd" plain="true" func-id="craftDataList-2" style=""  onclick="addDisignData()">新增</a>
    <a class="easyui-linkbutton" iconCls="icon-myedit" plain="true" func-id="craftDataList-3" style=""  onclick="setDisignData()">编辑</a>
    <a class="easyui-linkbutton" iconCls="icon-myremove" plain="true" func-id="craftDataList-4" style=""  onclick="delTask()">删除</a>
    <a class="easyui-linkbutton" iconCls="icon-save" plain="true" func-id="craftDataList-5" style=""  onclick="delTask()">保存</a>

</div>


<script>

    var table = undefined;
    var rowdata="";
    layui.config({
        base: '/DataPackageManagement/build/js/' //假设这是你存放拓展模块的根目录
    }).use(['form','laydate','table','utils','layer'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var table = layui.table;
        var utils = layui.utils;
        var layer = layui.layer;
    });

</script>
<script>
    $(function () {
        reloadTable();

    });

    function  reloadTable() {
        var renderTableData = function(data){
            $('#taskTable').datagrid({
                data:data,
                singleSelect:true,
                fitColumns:true,
                striped:true,
                toolbar:'#tb',
                fit: true,
                columns:[[
                    {field: 'ROWNUM', title: '序号'}
                    ,{field: 'FILE_TYPE', title: '文件类别'}
                    ,{field: 'GATHERING_METHOD', title: '采集方式'}
                    ,{field: 'SOURCE_SYSTEM', title: '来源系统'}
                    ,{field: 'MODIFIED_TIMESTAMP', title: '修改时间'}
                    ,{field: 'DELIVERY_STATE', title: '交付状态'}
                ]]
            });
        };
        renderTableData([]);

        var cb_success = function(data){
            renderTableData(data.rows);
        };

        twxAjax("publishMissionThing","getAllDataListDataByType",{type:"DESIGN_DATA_LIST"},true,cb_success);
    }

    function addDisignData() {
        layui.layer.open({
            type: 2,
            title:'新增设计数据',
            area:["50%",'98%'],
            content: '/DataPackageManagement/components/publishMission/addDesignData.html' ,//这里content是一个普通的String
            end:function () {
                //layer.msg("保存成功！");
                reloadTable();
            }
        });
    }


    function setDisignData() {
        layui.layer.open({
            type: 2,
            title:'编辑设计数据',
            area:["50%",'98%'],
            content: '/DataPackageManagement/components/publishMission/updateDesignData.html' ,//这里content是一个普通的String
            end:function () {
                //layer.msg("保存成功！");
                reloadTable();
            }
        });
    }

</script>
</body>
