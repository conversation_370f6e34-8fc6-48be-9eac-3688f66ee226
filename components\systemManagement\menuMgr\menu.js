var menuThingName = 'Thing.Fn.SystemManagement';

//初始化树表格
var initComp = function() {
	$('#menuTree').treegrid({
		data: [],
		idField: 'MENU_ID',
		treeField: 'MENU_NAME',
		fit: true,
		lines: true,
		rownumbers: true,
		toolbar: '#menuTree_tb',
		columns: [
			[{
					title: '名称',
					field: 'MENU_NAME',
					width: 180
				},
				{
					title: '图标名称',
					field: 'MENU_ICON',
					width: 180,
					formatter: function(value, row, index) {
						if (value !== '') {
							return '<i class="fa ' + value + '" aria-hidden="true"></i>&nbsp;&nbsp;' + value;
						}
					}
				},
				{
					title: '是否启用',
					field: 'MENU_ENABLE',
					width: 80,
					align: 'center',
					formatter: function(value, row, index) {
						if (value === 1) {
							return '√';
						}
					}
				},
				{
					title: '是否默认',
					field: 'MENU_DEFAULT',
					width: 80,
					align: 'center',
					formatter: function(value, row, index) {
						if (value === 1) {
							return '√';
						}
					}
				},
				{
					title: '类型',
					field: 'MENU_TYPE',
					align: 'center',
					width: 80,
					formatter: function(value, row, index) {
						var display = '';
						if (value === '') {
							return '';
						}
						if (value === 'Menu') {
							display = '菜单';
							return '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal">' + display + '</button>';
						} else if (value === 'Security') {
							display = '权限';
							return '<button type="button" class="layui-btn layui-btn-xs">' + display + '</button>';
						} else if (value === 'Virtual') {
							display = '虚拟组';
							return '<button type="button" class="layui-btn layui-btn-xs layui-btn-warm">' + display + '</button>';
						}
						return display;
					}
				},
				{
					title: '路径',
					field: 'MENU_PATH',
					width: 300
				},
				{
					title: '说明',
					field: 'MENU_DESC',
					width: 500
				},
				{
					title: '排序',
					field: 'MENU_SORT',
					width: 80
				},
				{
					title: 'id',
					field: 'MENU_ID',
					hidden: true
				},
				{
					title: 'parentid',
					field: 'MENU_PARENT',
					hidden: true
				}
			]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...',
		onDblClickRow: function(rowIndex, rowData) {
			$('#menuedit').click();
		},
		onClickRow: function(rowIndex, rowData) {
			initFuncTableData();
		},
		onLoadSuccess: function(row, data) {
			$(this).treegrid('enableDnd', row ? row.id : null);
		},
		onBeforeDrag: function(row) {
			if (row.MENU_NAME == '系统菜单') {
				return false;
			}
		},
		onStartDrag: function(row) {},
		onDragEnter: function(targetRow, sourceRow) {
			if (targetRow.MENU_PARENT !== sourceRow.MENU_PARENT) {
				return false;
			}
		},
		onDragOver: function(targetRow, sourceRow) {
			if (targetRow.MENU_PARENT !== sourceRow.MENU_PARENT) {
				return false;
			}
		},
		onDragLeave: function(targetRow, sourceRow) {},
		onBeforeDrop: function(targetRow, sourceRow, point) {
			if (point == 'append') {
				return false;
			}
		},
		onDrop: function(targetRow, sourceRow, point) {
			var nodes = $('#menuTree').treegrid('getParent', sourceRow.id).children;
			var tempArr = [];
			for (var i = 0; i < nodes.length; i++) {
				tempArr.push(nodes[i].MENU_ID + ":" + (i + 1));
			}
			var strs = tempArr.join(",");
			twxAjax(menuThingName, 'SortMenu', {
				strs: strs
			}, true, function(res) {
				if (res.success) {
					initMenuData();
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}, function(xhr, textStatus, errorThrown) {
				layer.alert('请求出错！', {
					icon: 2
				});
			});
		}
	});
};

var initMenuData = function() {
	$('#menuTree').treegrid('loading');
	var cb_success = function(data) {
		$('#menuTree').treegrid('loadData', [data]);
		$('#menuTree').treegrid('loaded');
	};
	var cb_error = function() {};
	var param = {};
	twxAjax(menuThingName, 'getAllMenus', param, true, cb_success, cb_error);
};

var getSelectedData = function() {
	var sels = $('#menuTree').treegrid('getSelections');
	return sels;
};

var contentHtml = '';

var initBtnAdd = function(layui) {
	$('#menuadd').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		var sels = getSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择节点', {
				icon: 2,
				anim: 6
			});
			return;
		}

		layer.open({
			title: '添加菜单',
			type: 1,
			area: ['400px', '630px'],
			btn: ['添加', '重置', '关闭'],
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			content: contentHtml,
			yes: function() {
				$('#btn_submit').click();
			},
			btn2: function() {
				$('#btn_reset').click();
				return false;
			},
			btn3: function() {

			},
			success: function(layero, index) {
				form.render();
				initIconInputChange();
			}
		});
	});
};

var initBtnEdit = function(layui) {
	$('#menuedit').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		var sels = getSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择节点', {
				icon: 2,
				anim: 6
			});
			return;
		}

		layer.open({
			title: '编辑菜单',
			type: 1,
			area: ['400px', '630px'],
			btn: ['保存', '重置', '关闭'],
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			content: contentHtml,
			yes: function() {
				$('#btn_update').click();
			},
			btn2: function() {
				$('#btn_reset').click();
				var param = {};
				param.menuid = sels[0].MENU_ID;
				param.menuname = sels[0].MENU_NAME;
				param.menuicon = sels[0].MENU_ICON;
				param.menupath = sels[0].MENU_PATH;
				param.menuenable = sels[0].MENU_ENABLE;
				param.menudefault = sels[0].MENU_DEFAULT;
				param.menutype = sels[0].MENU_TYPE;
				param.menudesc = sels[0].MENU_DESC;
				form.val('menuinfo', param);
				return false;
			},
			btn3: function() {

			},
			success: function(layero, index) {
				var param = {};
				param.menuid = sels[0].MENU_ID;
				param.menuname = sels[0].MENU_NAME;
				param.menuicon = sels[0].MENU_ICON;
				param.menupath = sels[0].MENU_PATH;
				param.menuenable = sels[0].MENU_ENABLE;
				param.menudefault = sels[0].MENU_DEFAULT;
				param.menutype = sels[0].MENU_TYPE;
				param.menudesc = sels[0].MENU_DESC;
				form.val('menuinfo', param);

				form.render();

				$('#previewicon').html('<i class="fa ' + param.menuicon + '" aria-hidden="true"></i>');

				initIconInputChange();
			}
		});
	});
};

var initBtnDelete = function(layui) {
	$('#menudel').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		var sels = getSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择节点', {
				icon: 2,
				anim: 6
			});
			return;
		}

		layer.confirm('确认删除选中数据吗?', {
			icon: 3,
			title: '提示'
		}, function(index) {
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					return;
				}
				//刷新用户表个
				initMenuData();
				layer.closeAll();

				layer.msg('删除成功');
			};
			var cb_error = function() {};
			var param = {};
			param.menuid = sels[0].MENU_ID;
			// layer.close(index);
			twxAjax(menuThingName, 'DeleteMenu', param, true, cb_success, cb_error);
		});
	});
};

//初始化绑定图标的输入change事件
var initIconInputChange = function() {
	$('input[name=menuicon]').change(function() {
		$('#previewicon').html('<i class="fa ' + $(this).val() + '" aria-hidden="true"></i>');
	});
}

//初始化tree控件
$(document).ready(function() {
	var htmlObj = $.ajax({
		url: 'tpl/menuInfo.html',
		async: false
	});
	contentHtml = htmlObj.responseText;

	layui.use(['layer', 'form', 'jquery'], function() {
		var form = layui.form,
			layer = layui.layer;
		form.verify({
			menupath: function(value) {
				// var val = $('select[name=menutype]').val();
				// if(val === 'Menu'){
				//     if(value === ''){
				//         return '当类型为菜单时,路径不能为空'
				//     }
				// }
			}
		});
		//新增的提交判断
		form.on('submit(formVerify)', function(data) {
			var param = data.field;

			param.menuparent = getSelectedData()[0].MENU_ID;
			if (param['menuenable'] === undefined) {
				param.menuenable = 0;
			} else {
				param.menuenable = 1;
			}
			if (param['menudefault'] === undefined) {
				param.menudefault = 0;
			} else {
				param.menudefault = 1;
			}
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					return;
				}

				initMenuData();
				layer.closeAll();

				layer.msg('保存成功');
			};
			var cb_error = function() {};
			twxAjax(menuThingName, 'AddMenu', param, true, cb_success, cb_error);
			return false;
		});
		//修改的提交
		form.on('submit(updateVerify)', function(data) {
			var param = data.field;
			param.menuparent = getSelectedData()[0].MENU_ID;
			if (param['menuenable'] === undefined) {
				param.menuenable = 0;
			} else {
				param.menuenable = 1;
			}
			if (param['menudefault'] === undefined) {
				param.menudefault = 0;
			} else {
				param.menudefault = 1;
			}
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					return;
				}

				initMenuData();
				layer.closeAll();

				layer.msg('更新成功');
			};
			var cb_error = function() {};
			twxAjax(menuThingName, 'UpdateMenu', param, true, cb_success, cb_error);
			return false;
		});

		initComp(layui);
		initMenuData(layui);

		initBtnAdd(layui);
		initBtnEdit(layui);
		initBtnDelete(layui);
	});
});