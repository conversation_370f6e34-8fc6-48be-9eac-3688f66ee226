/**
 * 上传组件的加载
 */
function renderUpload($top) {
	var $uploadChoose = $top.find('.upload-choose');

	if ($uploadChoose.hasClass('layui-btn-disabled')) {
		return false;
	}

	var $upload_name = $top.find('.upload-name');

	var $msg = $top.find('.upload-msg');
	var $msg_i = $msg.find("i");
	var $msg_span = $msg.find("span");

	var $preview = $top.find('.upload-preview');

	var flowPostUrl = getFlowPostUrl();
	var uploadUrl = flowPostUrl + "/upload?act=upload";



	upload.render({
		elem: $uploadChoose[0],
		url: uploadUrl, // 此处配置你自己的上传接口即可
		auto: true,
		exts: 'pdf|PDF|docx|doc|xlsx|xls',
		multiple: false,
		dataType: "json",
		choose: function(obj) {
			var files = obj.pushFile();
			obj.preview(function(index, file, result) {
				$upload_name.val(file.name);
			});
		},
		before: function(obj) {
			$msg_i.attr("class", "layui-icon layui-icon-loading");
			$msg_span.text("正在上传");
			$msg.removeClass("layui-hide");
			$preview.addClass("layui-hide");
		},
		done: function(res) {
			if (res.success) {
				$msg_i.attr("class", "layui-icon layui-icon-success");
				$msg_span.text("上传成功");
				$msg.removeClass("layui-hide");
				var fileData = res.data;
				$top.find('.upload-name').val(fileData.fileName);
				$top.find('.upload-path').val(fileData.filePath);
				$top.find('.upload-size').val(fileData.fileSize);
				$top.find('.upload-format').val(fileData.fileFormat);
				$top.find('.upload-user').val(sessionStorage.getItem('username'));

				renderPreviewBtn($top);
			} else {
				$msg_i.attr("class", "layui-icon layui-icon-error");
				$msg_span.text("上传失败");
				$msg.removeClass("layui-hide");
				$preview.addClass("layui-hide");
			}
		}
	});
}

/**
 * 加载预览按钮
 * @param {Object} $top
 */
function renderPreviewBtn($top) {
	var $preview = $top.find('.upload-preview');
	$preview.removeClass("layui-hide");

	$preview.unbind('click').bind('click', function() {
		var fileName = $top.find('.upload-name').val();
		var filePath = $top.find('.upload-path').val();
		var fileFormat = $top.find('.upload-format').val();

		previewUploadFile(filePath, fileFormat, fileName);
		return false;
	});
}

/**
 * 预览上传的文件
 */
function previewUploadFile(filePath, fileFormat, fileName) {
	if (filePath) {
		filePath = filePath.replace(/\\/g, "/");
		var url = fileHandlerUrl + "/system/open/file?filePath=" + filePath + "&fileName=" + encodeURIComponent("预览") + "." + fileFormat;
		url += "&fullfilename=" + encodeURIComponent(fileName) + "&browser=" + browser();
		url = encodeURIComponent(url);

		var previewURL = "";
		if (fileFormat === 'PDF' || fileFormat === 'pdf') {
			previewURL = '/File/' + filePath;
		} else {
			previewURL = sessionStorage.getItem('previewUrl') + '/onlinePreview?url=' + url;
		}
		var urlKey = parseInt(new Date().getTime() * Math.random());
		sessionStorage.setItem(urlKey, previewURL);
		window.open("/DataPackageManagement/components/preview/preview.html?key=" + urlKey);
	} else {
		layer.alert('未找到文件地址！', {
			icon: 2
		});
	}
}