/**
 * 清单柱状图  已弃用
 * <AUTHOR>
 * @date 2025-04-23 
 */

var searchList = [{
    fileType: "工艺更改单",
    numId: "gygg-num",
    chartId: "gygg-chart",
    maxValueSpan: 10
}, {
    fileType: "设计更改单",
    numId: "sjgg-num",
    chartId: "sjgg-chart",
    maxValueSpan: 10
}, {
    fileType: "工艺偏离单",
    numId: "gypl-num",
    chartId: "gypl-chart",
    maxValueSpan: 10
}, {
    fileType: "设计偏离单",
    numId: "sjpl-num",
    chartId: "sjpl-chart",
    maxValueSpan: 10
}];

var listTooltipFormatter = function (params) {
    return '<div style="margin: 0px 0 0;line-height:1;">\
							<div style="margin: 0px 0 0;line-height:1;">\
								' + params.marker + '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' + params.name.split("~~~")[0] + '</span>\
								<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">' + params.value + '</span>\
								<div style="clear:both"></div>\
							</div>\
							<div style="clear:both"></div>\
						</div>';
}

/**
 * 加载三单的柱状图
 *
 */
function loadListChart(modelId, startDate, endDate, fileType, numId, chartId, maxValueSpan) {
    var chartDom = document.getElementById(chartId);
    var myChart = echarts.init(chartDom);
    myChart.showLoading("default", loadingOption);
    twxAjax(thing, 'QueryModelListCount', {
        username: username,
        treeId: modelId,
        startDate: startDate,
        endDate: endDate,
        fileType: fileType
    }, true, function (res) {
        if (res.success) {
            myChart.hideLoading();
            var xDatas = res.data.names;
            var datas = res.data.counts;
            var total = res.data.total;
            var option = res.data.option;
            $("#" + numId).text(total);
            option.tooltip.formatter = listTooltipFormatter;
            option.xAxis.axisLabel.formatter = xAxisLabelFormatter;
            myChart.setOption(option);

            myChart.off('click');
            myChart.on('click', function (params) {
                chartCilck(params, startDate, endDate, fileType);
            });
        }
    }, function (e) {

    });
}

function chartCilck(params, startDate, endDate, fileType) {
    var modelId = params.name.split("~~~")[1];

    // 存储当前fileType，用于后续筛选
    var curFileType = fileType;

    // 增加状态筛选下拉框，改为与型号选择下拉框一致的样式，并放在右侧
    var filterBtns = '';

    // 注意：现场问题处理单和现场临时处理单的处理逻辑已移至handle-chart.js中的专用函数

    layer.tab({
        type: 1,
        tab: [{
            title: '流程图',
            content: '<div id="flowContent" style="height: 620px;padding:15px 50px 50px 50px;position:relative;">' + (filterBtns ? filterBtns.flow : '') + '<div id="diagramContainer" style="position: relative;width: 100%;height: 1px;"></div>'
        }, {
            title: '详细表格',
            content: '<div id="tableContent" style="height: 620px;padding:15px 50px 50px 50px;position:relative;">' + (filterBtns ? filterBtns.table : '') + '<div style="margin-top:45px;"></div><table id="list-table"></table></div>'
        }],
        anim: false,
        openDuration: 200,
        skin: 'layui-layer-tab my-layer',
        isOutAnim: false,
        closeDuration: 200,
        closeBtn: 2,
        shadeClose: false,
        maxmin: false,
        resize: false, //不允许拉伸
        area: ['1500px', '770px'],
        success: function () {
            /* 弹窗不加载滚动条 */
            loadProcess(modelId, startDate, endDate, curFileType);
            loadTable(modelId, startDate, endDate, curFileType);

            // 初始化表单组件
            form.render();

            // 添加导出按钮点击事件
            $('#export-table-btn').on('click', function () {
                // 获取当前表格的筛选条件
                // 优先使用全局变量中的当前选中节点processId
                var currentModelId = window.currentProcessId || modelId;
                var startDate = $('#start-date').val() || '';
                var endDate = $('#end-date').val() || '';
                var fileType = curFileType || '';
                var statusType = $('#table-status-select').val() || 'all';

                // 显示加载提示
                var loading;

                // 调用后端导出接口
                $.fileDownload(fileHandlerUrl + '/aitScreen/exportExcel', {
                    httpMethod: 'POST',
                    data: {
                        treeId: currentModelId,
                        startDate: startDate,
                        endDate: endDate,
                        fileType: fileType
                    },
                    prepareCallback: function (url) {
                        // 导出准备中
                        loading = layer.msg('正在导出数据，请稍候...', {
                            icon: 16,
                            shade: 0.3,
                            time: 0
                        });
                    },
                    abortCallback: function (url) {
                        layer.close(loading);
                        layer.msg("导出异常！！");
                    },
                    successCallback: function (url) {
                        // 导出成功
                        layer.close(loading);
                        layer.msg('导出成功', { icon: 1 });
                    },
                    failCallback: function (html, url) {
                        // 导出失败
                        layer.close(loading);
                        layer.msg('导出失败', { icon: 2 });
                    }
                });
            });

            // 添加状态筛选下拉框选择事件
            form.on('select(status-select)', function (data) {
                var statusType = data.value;
                var selectId = $(data.elem).attr('id');

                // 同步另一个下拉框的选中状态
                if (selectId === 'flow-status-select') {
                    $('#table-status-select').val(statusType);
                } else if (selectId === 'table-status-select') {
                    $('#flow-status-select').val(statusType);
                }
                form.render('select'); // 重新渲染下拉框

                // 根据当前激活的标签页决定是否需要重新加载流程图
                var activeTabIndex = $('.layui-layer-tabmain').children('.layui-layer-tabli.layui-this').index();

                // 只有在流程图页签中选择状态时，才会重置流程节点的选中样式
                if (selectId === 'flow-status-select') {
                    window.currentProcessId = null;
                    // 清除流程图中的选中状态
                    $("#diagramContainer .v-mult").removeClass('v-mult-active');
                }

                // 检查是否有选中的流程节点
                var useProcessId = window.currentProcessId || null;
                var targetTreeId = useProcessId || modelId;

                // 无论在哪个标签页，都需要更新表格数据
                // 使用当前选中节点的processId或者默认的modelId
                table.reload('list-table', {
                    page: {
                        layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                        groups: 1,
                        first: false,
                        last: false,
                        curr: 1
                    },
                    where: {
                        treeId: targetTreeId,
                        startDate: startDate,
                        endDate: endDate,
                        fileType: curFileType
                    }
                });

                // 如果当前标签页是流程图，或者是从流程图标签页触发的事件，则重新加载流程图
                if (activeTabIndex === 0 || selectId === 'flow-status-select') {
                    // 保存当前选中节点的processId，避免加载过程中丢失
                    var savedProcessId = window.currentProcessId;
                    console.log("保存选中节点ID: ", savedProcessId);

                    loadProcess(modelId, startDate, endDate, curFileType);

                    // 确保在流程图加载完成后恢复节点选中状态
                    if (savedProcessId) {
                        // 使用更长的延时确保流程图完全加载
                        setTimeout(function () {
                            console.log("尝试恢复节点选中状态: ", savedProcessId);
                            $("#diagramContainer .v-mult").each(function () {
                                var nodeData = $(this).data("treeNode");
                                if (nodeData && nodeData.processId === savedProcessId) {
                                    $(this).addClass('v-mult-active');
                                    console.log("节点已选中: " + savedProcessId);
                                }
                            });
                        }, 800);
                    }
                }
            });
        }
    });
}

function loadProcess(modelId, startDate, endDate, fileType) {
    var loadIndex = layer.load(1);
    // 保存当前选中的processId
    var savedProcessId = window.currentProcessId;
    console.log("loadProcess中保存的选中节点ID: ", savedProcessId);

    twxAjax(thing, 'QueryProccessCount', {
        modelId: modelId,
        startDate: startDate,
        endDate: endDate,
        fileType: fileType
    }, true, function (res) {
        layer.close(loadIndex);
        if (res.success) {
            // 清空容器，避免重复添加流程图
            $("#diagramContainer").empty();

            var items = res.data;
            var colNum = 6;
            var rowNum = Math.ceil(items.length / colNum);
            $("#diagramContainer").css("height", (140 * rowNum) + "px");
            for (var i = 0, t = 1; i < rowNum; i++) {
                $tr = $('<div class="layui-row"></div>');
                for (var j = 0; j < colNum; j++) {
                    var treeNode = items[(i * colNum) + j];
                    var text = (!!treeNode) ? treeNode['processName'] : "";
                    if (text !== '') {
                        var processCount = treeNode['processCount'];
                        if (processCount > 0) {
                            processCount = '<span style="color:#15ff15;">' + processCount + '</span>';
                        }
                        text = dealPrefix(t) + "-" + text;
                        text = text + "（" + processCount + "）";
                        text = $('<div class="v-mult" id="jsPlumb' + t + '"><span class="empty"></span><span class="text">' + text + '</span></div>');

                        // 判断是否需要选中当前节点
                        if (savedProcessId && treeNode.processId === savedProcessId) {
                            text.addClass('v-mult-active');
                            console.log("流程图创建时已设置节点选中: " + savedProcessId);
                        }

                        text.data("treeNode", treeNode);
                    }
                    var $colDiv = $('<div class="layui-col-md2"></div>');
                    $colDiv.append(text);
                    if (i % 2 == 1) {
                        $tr.prepend($colDiv);
                    } else {
                        $tr.append($colDiv);
                    }
                    t++;
                }
                $("#diagramContainer").append($tr);
            }
            $("#diagramContainer .v-mult").unbind('click').bind('click', function () {
                // 1. 标记当前选中节点
                $("#diagramContainer .v-mult").removeClass('v-mult-active');
                $(this).addClass('v-mult-active');
                var treeNode = $(this).data("treeNode");

                // 2. 存储当前选中节点的processId到全局变量
                window.currentProcessId = treeNode.processId;

                // 3. 自动切换到详细表格页签
                $(".my-layer > div.layui-layer-title > span:nth-child(2)").mousedown();

                // 4. 重新加载表格数据，使用节点的processId
                table.reload('list-table', {
                    page: {
                        layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                        groups: 1,
                        first: false,
                        last: false,
                        curr: 1
                    },
                    where: {
                        treeId: treeNode.processId,
                        startDate: startDate,
                        endDate: endDate,
                        fileType: fileType
                    }
                });
            });

            jsPlumbBrowserUI.ready(function () {

                var jsPlumb = jsPlumbBrowserUI.newInstance({
                    container: document.getElementById("diagramContainer")
                });

                for (var i = 1; i < items.length; i++) {

                    var anchor = ['Right', 'Left'];
                    if (Math.ceil(i / colNum) % 2 == 0) {
                        anchor = ['Left', 'Right'];
                    }
                    if (i % colNum == 0) {
                        anchor = ['Bottom', 'Top'];
                    }
                    jsPlumb.connect({
                        source: document.getElementById('jsPlumb' + i),
                        target: document.getElementById('jsPlumb' + (i + 1)),
                        anchors: anchor,
                        endpoints: ["Blank", "Blank"],
                        endpointStyle: {
                            fill: '#169BD5'
                        },
                        overlays: [{
                            type: "Arrow",
                            options: {
                                width: 12,
                                length: 12,
                                location: 1
                            }
                        }],
                        paintStyle: {
                            strokeWidth: 2,
                            stroke: '#169BD5'
                        }
                    });
                }
            });

        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (e) {

    });
}

function loadTable(modelId, startDate, endDate, fileType) {
    var cols = [{
        title: '序号',
        type: 'numbers',
        width: 70
    }, {
        field: 'modelName',
        title: '型号',
        width: 150
    }, {
        field: 'processName',
        title: '阶段',
        width: 250
    }, {
        field: 'FILE_NUMBER',
        title: '文件编号',
        width: 200
    }, {
        field: 'FILE_NAME',
        title: '文件名称'
    }];

    // 为现场问题处理单增加状态列
    if (fileType.indexOf('现场问题处理单') > -1) {
        // 根据不同类型添加不同的状态列
        if (fileType.indexOf('全部') > -1) {
            // 闭环状态列
            cols.push({
                field: 'STATE',
                title: '状态',
                width: 180,
                templet: function (d) {
                    // 根据STATE字段值判断状态
                    switch (d.STATE) {
                        case '0': return '<span class="layui-badge layui-bg-blue">已提出</span>';
                        case '1': return '<span class="layui-badge layui-bg-blue">已填写处理方案</span>';
                        case '2': return '<span class="layui-badge layui-bg-blue">已填写实施工艺</span>';
                        case '3': return '<span class="layui-badge layui-bg-blue">工艺负责人已签署</span>';
                        case '4': return '<span class="layui-badge layui-bg-blue">产品负责人已签署</span>';
                        case '5': return '<span class="layui-badge layui-bg-blue">已生效</span>';
                        case '6': return '<span class="layui-badge layui-bg-green">已完成</span>';
                        case '7': return '<span class="layui-badge layui-bg-green">已闭环</span>';
                        case '8': return '<span class="layui-badge layui-bg-red">已作废</span>';
                        case '9': return '<span class="layui-badge layui-bg-red">已填写处理结果</span>';
                        default: return '<span class="layui-badge layui-bg-gray">未知状态</span>';
                    }
                }
            });
        } else if (fileType.indexOf('更改设计文件') > -1) {
            // 新增字段设计更改单号
            cols.push({
                field: 'DESIGN_CHANGE_NO',
                title: '设计更改单号',
                width: 180
            });
            // 设计文件完成状态列
            cols.push({
                field: 'DESIGNFILESTATUS',
                title: '完成状态',
                width: 100,
                templet: function (d) {
                    var isFinished = d.DESIGN_CHANGE_NO; // d.DESIGN_CHANGE_NO有值的话是已完成
                    return isFinished ?
                        '<span class="layui-badge layui-bg-green">已完成</span>' :
                        '<span class="layui-badge layui-bg-red">未完成</span>';
                }
            });
        } else if (fileType.indexOf('更改工艺文件') > -1) {
            // 新增字段工艺更改单号
            cols.push({
                field: 'PROCESS_CHANGE_NO',
                title: '工艺更改单号',
                width: 180
            });
            // 工艺文件完成状态列
            cols.push({
                field: 'TECHFILESTATUS',
                title: '完成状态',
                width: 100,
                templet: function (d) {
                    var isFinished = d.PROCESS_CHANGE_NO;
                    return isFinished ?
                        '<span class="layui-badge layui-bg-green">已完成</span>' :
                        '<span class="layui-badge layui-bg-red">未完成</span>';
                }
            });
        }
    }

    // 添加操作列
    cols.push({
        field: 'FILEPATH',
        title: '操作',
        width: 160,
        templet: function (d) {
            var html = '<div class="layui-clear-space">';
            html += '<a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="previewList">查看</a>';
            if (d.FILE_TYPE == '现场问题处理单') {
                html += '<a class="layui-btn layui-btn-sm layui-bg-red" lay-event="previewLinkList">查看更改单</a>';
            }
            html += "</div>";
            return html;
        }
    });

    if (fileType.indexOf('现场临时处理单') > -1) {
        cols = [{
            title: '序号',
            type: 'numbers',
            width: 70
        }, {
            field: 'MODEL_NAME',
            title: '型号',
            width: 150
        }, {
            field: 'LEFT_NAME',
            title: '阶段',
            width: 250
        }, {
            field: 'NAME',
            title: '文件名称'
        }, {
            field: 'OPT_CATEGORY',
            title: '类型',
            width: 300
        }, {
            field: 'OPT_DATE',
            title: '日期',
            width: 115
        }, {
            field: 'IS_FINISHED',
            title: '状态',
            width: 100,
            templet: function (d) {
                return d.IS_FINISHED == 1 ?
                    '<span class="layui-badge layui-bg-green">已完成</span>' :
                    '<span class="layui-badge layui-bg-red">未完成</span>';
            }
        }, {
            field: 'FILEPATH',
            title: '操作',
            width: 100,
            templet: function (d) {
                var html = '<div class="layui-clear-space">';
                html += '<a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="previewTempList">查看</a>';
                html += "</div>";
                return html;
            }
        }];
    }
    table.render({
        id: "list-table",
        elem: '#list-table',
        skin: "nob",
        width: 1400,
        url: getUrl(thing, 'QueryList', ""),
        where: {
            treeId: modelId,
            startDate: startDate,
            endDate: endDate,
            fileType: fileType
        },
        page: {
            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
            groups: 1,
            first: false,
            last: false
        },
        cols: [cols],
        even: true
    });

    table.on('tool(list-table)', function (obj) {
        var d = obj.data;
        if (obj.event === 'previewList') {
            previewList(d.ID, d.TABLENAME, d.FILEPATH, d.FILE_NAME, d.FILE_FORMAT, d.FILE_TYPE, d.SOURCE_SYSTEM, d.GATHERING_METHOD);
        } else if (obj.event === 'previewLinkList') {
            previewLinkList(d.ID, d.TABLENAME);
        } else if (obj.event === 'previewTempList') {
            previewTempList(d.TABLE_HEADER, d.HTML_DATA, d.SAVE_DATA);
        }
    });
}