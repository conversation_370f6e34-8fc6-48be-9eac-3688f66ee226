//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var loadFlag = 'init';

//初始化表格
function initComp() {
	$('#logtable').datagrid({
		data: [],
		singleSelect: true,
		fitColumns: true,
		striped: true,
		fit: true,
		onClickRow: function () {
			$('#logtable').datagrid('clearSelections');
		},
		rownumbers: true,
		columns: [
			[{
				field: 'USER_FULLNAME',
				title: '姓名',
				width: 80
			}, {
				field: 'USERNAME',
				title: '用户名',
				width: 80
			}, {
				field: 'OPERATION',
				title: '操作',
				width: 60
			}, {
				field: 'CONTENT',
				title: '操作内容',
				width: 500,
				formatter: function (value, row, index) {
					return '<span class="contentSpan">' + value + '</span>';
				}
			}, {
				field: 'LOGTIME',
				title: '操作时间',
				width: 150,
				formatter: function (value, row, index) {
					var d = new Date();
					d.setTime(value);

					return getFormatDate(d, 'yyyy-MM-dd hh:mm:ss');
				}
			}, {
				field: 'MYIP',
				title: '操作IP',
				width: 150,
				formatter: function (value, row, inde) {
					if (value == 'undefined' || value == 'null') {
						return '';
					} else {
						return value;
					}
				}
			}, {
				field: 'REQRESULT',
				title: '结果',
				width: 60,
				align: 'center',
				formatter: function (value, row, inde) {
					if (value === 1) {
						return '<span class="layui-badge layui-bg-green">成功</span>';
						// return '成功';
					} else {
						return '<span class="layui-badge layui-bg-red">失败</span>';
						// return '失败';
					}
				}
			}]
		],
		loadMsg: '正在加载数据...',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有日志数据...</font></div>',
		pagination: true,
		onLoadSuccess: function () {
			// $(this).datagrid("fixRownumber");
		}
	});
	//初始化分页组件
	initPagination('logtable', {
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);

}

//获取筛选条件的值
var getFieldValue = function () {
	var params = {};
	params.username = loadFlag === 'init' ? '' : $('#username').textbox('getValue');
	params.operation = loadFlag === 'init' ? '' : $('#log_ops').combobox('getValue');
	params.stime = loadFlag === 'init' ? '' : $('#stime').textbox('getValue');
	params.etime = loadFlag === 'init' ? '' : $('#etime').textbox('getValue');
	params.content = loadFlag === 'init' ? '' : $('#content').textbox('getValue');
	params.result = loadFlag === 'init' ? '' : $('#result').combobox('getValue');
	if (userRolename.indexOf('审计管理员') > -1) {
		params.currentUser = 'audadmin';
	} else if (userRolename.indexOf('安全管理员') > -1) {
		params.currentUser = 'secadmin';
	}
	return params;
};

//获取查询日志信息
var getSearchLog = function () {
	var log = '';
	var username = $('#username').textbox('getValue'); //用户名
	var log_ops = $('#log_ops').combobox('getValue'); //操作
	var stime = $('#stime').textbox('getValue') //开始时间
	var etime = $('#etime').textbox('getValue'); //结束时间
	var content = $('#content').textbox('getValue'); //操作内容
	var result = $('#result').combobox('getValue'); //操作结果
	if (username != '') {
		log += '、' + '用户名：' + username;
	}
	if (log_ops != '') {
		log += '、' + '操作：' + log_ops;
	}
	if (stime != '') {
		log += '、' + '开始时间：' + stime;
	}
	if (etime != '') {
		log += '、' + '结束时间：' + etime;
	}
	if (content != '') {
		log += '、' + '操作内容：' + content;
	}
	if (result != '') {
		log += '、' + '操作结果：' + result;
	}
	log = log.substring(1);
	if (log == '') {
		log = '所有';
	}
	return log;
}

//初始化行号
var initLineNumbers = function () {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function (index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};


//分页查询数据
var queryDataByPage = function (pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#logtable').datagrid('loading');
	initTotalRecords();
	var cb_success = function (data) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#logtable').datagrid('loadData', data.rows);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#logtable').datagrid('loaded');
		layui.use(['layer'], function () {
			var layer = layui.layer;
			$(".datagrid-view2>.datagrid-body .contentSpan").each(function (i, n) {
				var text = $(n).text();
				$(n).mouseenter(function () {
					var width = $(n).parent().width();
					layer.tips(text, n, {
						tips: [1, '#2983CF'], //还可配置颜色
						time: 0,
						area: width + 'px'
					});
				});
				$(n).mouseleave(function () {
					layer.close(layer.index);
				});
			})
		});
	};
	var cb_error = function () {
		$('#logtable').datagrid('loaded');
		layui.use(['layer'], function () {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
			// $('#root_layout').loading('stop');
		});
	};
	//传递的参数
	var param = getFieldValue();
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.UserLogUtil', 'QueryPageData', param, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function () {
	//initPagination('logtable',{total:data.rows[0].COUNT});
	$('#logtable').datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
	var log = getSearchLog();
	logRecord('查询', '日志审计-查询条件(' + log + ')，共查询出' + totalRecords + '条数据', 1);
}


//初始化全部的记录条数
var initTotalRecords = function () {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function (data) {
		pageLoadFlag = true;
		totalRecords = data.rows[0].COUNT;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function () { };

	//传递参数
	var param = getFieldValue();
	twxAjax('Thing.UserLogUtil', 'queryTotalNumbers', param, true, cb_success, cb_error);
};

//初始化分页组件
var initPagination = function (tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function () {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function (pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function (pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function (pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function (pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};


//初始化操作下拉框
var initOperations = function () {
	var cb_success = function (data) {
		data.rows.unshift({
			"OPERATION": "全部"
		});
		$('#log_ops').combobox('loadData', data.rows);
	};
	var cb_error = function () { };
	twxAjax('Thing.UserLogUtil', 'QueryAllOperations', {}, true, cb_success, cb_error);
};

var initData = function () {
	//init operations
	initOperations();
};

//初始化组件
initComp();
//初始化控件的数据
initData();

//搜索按钮开始搜索
var searchLogs = function () {
	loadFlag = 'search';
	pageOptions.pageNumber = 1;

	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
};

//清除字段
var clearFields = function () {
	$('#username').textbox('setValue', '');
	$('#log_ops').combobox('setValue', '');
	$('#stime').textbox('setValue', '');
	$('#etime').textbox('setValue', '');
	$('#content').textbox('setValue', '');
	$('#result').combobox('setValue', '');
};

//以下解决数据过多时，行号显示不完全的问题
$.extend($.fn.datagrid.methods, {
	fixRownumber: function (jq) {
		return jq.each(function () {
			var panel = $(this).datagrid("getPanel");
			//获取最后一行的number容器,并拷贝一份
			var clone = $(".datagrid-cell-rownumber", panel).last().clone();
			//由于在某些浏览器里面,是不支持获取隐藏元素的宽度,所以取巧一下
			clone.css({
				"position": "absolute",
				left: -1000
			}).appendTo("body");
			var width = clone.width("auto").width();
			//默认宽度是25,所以只有大于25的时候才进行fix
			if (width > 25) {
				//多加5个像素,保持一点边距
				$(".datagrid-header-rownumber,.datagrid-cell-rownumber", panel).width(width + 5);
				//修改了宽度之后,需要对容器进行重新计算,所以调用resize
				$(this).datagrid("resize");
				//一些清理工作
				clone.remove();
				clone = null;
			} else {
				//还原成默认状态
				$(".datagrid-header-rownumber,.datagrid-cell-rownumber", panel).removeAttr("style");
			}
		});
	}
});

var exportExcel = function () {
	layui.use(['layer'], function () {
		var layer = layui.layer;
		var loading;
		var url = fileHandlerUrl + "/system/export/log";
		var param = getFieldValue();
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: param,
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				logRecord('导出日志', '导出日志', 0);
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				logRecord('导出日志', '导出日志', 1);
				layer.close(loading);
			},
			failCallback: function (html, url) {
				logRecord('导出日志', '导出日志', 0);
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});
	});
}
var checkLogCount = function () {
	var cb_success = function (data) {
		var count = data.rows[0].COUNT;
		count = Number(count);
		if (count > 100000) {
			layui.use(['layer'], function () {
				var layer = layui.layer;
				layer.alert('日志记录超过10万条，请及时清理部分日志记录！', {
					offset: 'rb'
				})
			});
		}
	};
	var cb_error = function () { };
	twxAjax('Thing.UserLogUtil', 'queryLogCount', {}, true, cb_success, cb_error);
}
checkLogCount();