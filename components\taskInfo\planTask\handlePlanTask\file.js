/**
 * 上传一个文件
 */
function addFile(id, title, listName) {
	var fileFlag = false,
		fileUploadSuccess = false;
	var filePath = "",
		fileName = "",
		myFile = {};
	layer.open({
		title: '上传' + title + '文件',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['500px', '450px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function (index, layero, that) {
			var inputAllValid = false;
			var isValid1 = form.validate('#fileNum');
			if (isValid1) {
				var isValid2 = form.validate('#fileName');
				if (isValid2) {
					var isValid3 = form.validate('#security');
					if (isValid3) {
						if (!fileFlag) {
							layer.alert('请选择需要上传文件!', {
								icon: 2
							});
							return false;
						} else {
							if (!fileUploadSuccess) {
								layer.alert('文件未上传成功!', {
									icon: 2
								});
								return false;
							} else {
								var security = $("#security").val();

								var datas = table.cache[id];
								var row = {
									"fileNum": $("#fileNum").val(),
									"fileName": $("#fileName").val(),
									"filePath": filePath,
									"secretLabel": security,
									"myFile": JSON.stringify(myFile)
								};
								datas.push(row);
								table.renderData(id);
								layer.close(index);
							}
						}
					}
				}
			}


		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			$("#uploadContent").parent().css('overflow', 'visible');
			var addTpl = `<form class="layui-form" lay-filter="uploadForm">
						<div class="layui-form-item">
						    <label class="layui-form-label" style="width:85px">项目清单名称</label>
						    <div class="layui-input-block" style="margin-left:115px">
								<input type="text" id="list_name" disabled="disabled" class="layui-input">
						    </div>
						</div>
						<div class="layui-form-item">
						    <label class="layui-form-label" style="width:85px">文档编码</label>
						    <div class="layui-input-block" style="margin-left:115px">
								<input type="text" id="fileNum" lay-verify="required"  placeholder="请输入文档编码" autocomplete="off"  class="layui-input">
						    </div>
						</div>
						<div class="layui-form-item">
						    <label class="layui-form-label" style="width:85px">文件名称</label>
						    <div class="layui-input-block" style="margin-left:115px">
								<input type="text" id="fileName" lay-verify="required"  placeholder="请输入文件名称" autocomplete="off"  class="layui-input">
						    </div>
						</div>
						<div class="layui-form-item">
						    <label class="layui-form-label" style="width:85px">密级</label>
						    <div class="layui-input-block" style="margin-left:115px">
								<select id="security" lay-filter="security" lay-verify="required">
									<option value="">请选择密级</option>
									<option value="公开">公开</option>
									<option value="内部">内部</option>
									<option value="秘密">秘密</option>
									<option value="机密">机密</option>
								  </select>						    
							  </div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:85px">上传主文件</label>
							<div class="layui-input-block" style="margin-left:115px">
								<div class="layui-upload">
									<button type="button" style="float: left;" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
									<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
									<div style="height: 38px;float: left;padding-left: 30px;line-height: 38px;display: none;" id="upload-div">
									  <i class="layui-icon layui-icon-success" id="upload-icon" style="font-size: 20px; color: #16b777;"></i> 
									  <span id="upload-msg" class="layui-font-gray">上传成功</span>
									</div>
								</div>
							</div>
						</div>
						<div class="layui-form-item" id="selectedFile" style="display: none;">
							<label class="layui-form-label" style="width:85px">已选文件</label>
							<div class="layui-input-block" style="margin-left:115px">
								<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
							</div>
						</div>
					</form>`;
			$("#uploadContent").append(addTpl);
			$("#list_name").val(listName);
			form.render(null, 'uploadForm');

			var uploadInst = upload.render({
				elem: '#uploadChoice',
				url: fileHandlerUrl + '/superior/upload/doc',
				auto: true,
				field: 'file',
				accept: 'file',
				dataType: 'json',
				choose: function (obj) {
					fileFlag = true;
					var files = obj.pushFile();
					var filename = '';
					var file;
					for (var k in files) {
						file = files[k];
						filename = file.name;
					}
					$("#selectedFile").show();
					$("#selectedFileName").text(filename);
				},
				done: function (res, index, upload) {
					$("#upload-div").show();
					if (res.success) {
						fileUploadSuccess = true;
						fileName = res.data.myFile.fileName;
						filePath = res.data.filePath;
						myFile = res.data.myFile;
						$("#upload-icon").removeClass("layui-icon-error").addClass("layui-icon-success").css("color", "#16b777");
						$("#upload-msg").html("上传成功");
					} else {
						fileUploadSuccess = false;
						fileName = "";
						filePath = "";
						myFile = {};
						$("#upload-icon").removeClass("layui-icon-success").addClass("layui-icon-error").css("color", "#ff5722");
						$("#upload-msg").html("上传失败，请重试");
					}
				}
			});
		}
	});
}

/**
 * 弹出行内文件上传列表
 */
function showRowFileList(rowObj, collectNode, title, listId, listType) {
	var title = title + '第' + (rowObj.index + 1) + '行';
	var id = 'rowFileListTable';
	layer.open({
		title: title + '文件上传列表',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		maxmin: false,
		resize: false,
		area: ['800px', '500px'],
		content: '<div id="rowFileListContent" style="">\
					<table class="layui-hide" lay-filter="' + id + '" id="' + id + '"></table>\
				</div>',
		btn: ['保存', '取消'],
		yes: function (index, layero, that) {
			var fileList = table.getData(id);
			rowObj.update({
				"fileList": fileList
			}, true);
			saveTable(rowObj.config.id, collectNode, listId, listType, function () {
				layer.close(index);
			});
		},
		btn2: function (index, layero, that) {
			layer.close(index);
		},
		success: function () {
			renderRowFileListTable(id, rowObj, title);
		}
	});
}

/**
 * 加载行内文件上传表格
 */
function renderRowFileListTable(id, rowObj, title) {
	var tableData = rowObj.data.fileList || [];
	var cols = [{
		type: "checkbox",
		fixed: 'left'
	}, {
		title: "#",
		type: "numbers",
		fixed: 'left'
	}, {
		field: 'fileName',
		title: '文件名称'
	}, {
		field: 'secretLabel',
		title: '密级'
	}, {
		field: 'op',
		title: '操作',
		toolbar: ' <div class="layui-clear-space">\
						<a class="layui-btn layui-btn-xs" lay-event="viewFile">预览</a>\
					  </div>'
	}];
	var btns = [{
		name: '上传',
		event: "addFile",
		icon: 'add-1'
	}, {
		name: '删除',
		event: "deleteRow",
		icon: 'delete'
	}];
	if (optType != 'toDo') {
		btns = [];
	}
	table.render({
		elem: '#' + id,
		cols: [cols],
		toolbar: getBtnHtml(btns),
		defaultToolbar: [],
		data: tableData,
		height: 391,
		page: false
	});

	// 工具栏事件
	table.on('toolbar(' + id + ')', function (obj) {
		var checkStatus = table.checkStatus(id);
		var othis = this;
		switch (obj.event) {
			case 'deleteRow':
				deleteRow(id);
				break;
			case 'addFile':
				addRowFile(id, title);
				break;
		};
	});

	table.on('tool(' + id + ')', function (obj) {
		var data = obj.data;
		if (obj.event === 'viewFile') {
			var filePath = data.filePath || '';
			if (filePath) {
				previewfile({
					FILEPATH: filePath,
					FILE_NAME: data.fileName,
					FILE_FORMAT: data.fileFormat,
					FILE_TYPE: "",
					GATHERING_METHOD: ""
				});
			} else {
				layer.alert('未发现文件地址！', {
					icon: 2
				});
			}
		}
	});
}

function addRowFile(id, title) {
	var fileFlag = false,
		fileUploadSuccess = false;
	var filePath = "",
		fileName = "",
		myFile = {};
	layer.open({
		title: '上传' + title + '文件',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['500px', '350px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function (index, layero, that) {
			var inputAllValid = false;
			var isValid1 = form.validate('#fileName');
			if (isValid1) {
				var isValid3 = form.validate('#security');
				if (isValid3) {
					if (!fileFlag) {
						layer.alert('请选择需要上传文件!', {
							icon: 2
						});
						return false;
					} else {
						if (!fileUploadSuccess) {
							layer.alert('文件未上传成功!', {
								icon: 2
							});
							return false;
						} else {
							var fileName = $("#fileName").val();
							var security = $("#security").val();

							var datas = table.cache[id];
							var row = {
								"fileName": fileName,
								"filePath": filePath,
								"fileFormat": myFile.fileFormat,
								"secretLabel": security,
								"myFile": JSON.stringify(myFile)
							};
							datas.push(row);
							table.renderData(id);
							layer.close(index);
						}
					}
				}
			}
		},
		btn2: function (index, layero, that) {
			layer.close(index);
		},
		success: function () {
			$("#uploadContent").parent().css('overflow', 'visible');
			var addTpl = `<form class="layui-form" lay-filter="uploadForm">
						<div class="layui-form-item">
						    <label class="layui-form-label" style="width:85px">文件名称</label>
						    <div class="layui-input-block" style="margin-left:115px">
								<input type="text" id="fileName" lay-verify="required"  placeholder="请输入文件名称" autocomplete="off"  class="layui-input">
						    </div>
						</div>
						<div class="layui-form-item">
						    <label class="layui-form-label" style="width:85px">密级</label>
						    <div class="layui-input-block" style="margin-left:115px">
								<select id="security" lay-filter="security" lay-verify="required">
									<option value="">请选择密级</option>
									<option value="公开">公开</option>
									<option value="内部">内部</option>
									<option value="秘密">秘密</option>
									<option value="机密">机密</option>
								  </select>						    
							  </div>
						</div>
						<div class="layui-form-item">
							<label class="layui-form-label" style="width:85px">上传主文件</label>
							<div class="layui-input-block" style="margin-left:115px">
								<div class="layui-upload">
									<button type="button" style="float: left;" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
									<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
									<div style="height: 38px;float: left;padding-left: 30px;line-height: 38px;display: none;" id="upload-div">
									  <i class="layui-icon layui-icon-success" id="upload-icon" style="font-size: 20px; color: #16b777;"></i> 
									  <span id="upload-msg" class="layui-font-gray">上传成功</span>
									</div>
								</div>
							</div>
						</div>
						<div class="layui-form-item" id="selectedFile" style="display: none;">
							<label class="layui-form-label" style="width:85px">已选文件</label>
							<div class="layui-input-block" style="margin-left:115px">
								<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
							</div>
						</div>
					</form>`;
			$("#uploadContent").append(addTpl);
			form.render(null, 'uploadForm');

			var uploadInst = upload.render({
				elem: '#uploadChoice',
				url: fileHandlerUrl + '/superior/upload/doc',
				auto: true,
				field: 'file',
				accept: 'file',
				dataType: "json",
				choose: function (obj) {
					fileFlag = true;
					var files = obj.pushFile();
					var filename = '';
					var file;
					for (var k in files) {
						file = files[k];
						filename = file.name;
					}
					$("#selectedFile").show();
					$("#selectedFileName").text(filename);
					$("#fileName").val(filename);
				},
				done: function (res, index, upload) {
					$("#upload-div").show();
					if (res.success) {
						fileUploadSuccess = true;
						fileName = res.data.myFile.fileName;
						filePath = res.data.filePath;
						myFile = res.data.myFile;
						$("#upload-icon").removeClass("layui-icon-error").addClass("layui-icon-success").css("color", "#16b777");
						$("#upload-msg").html("上传成功");
					} else {
						fileUploadSuccess = false;
						fileName = "";
						filePath = "";
						myFile = {};
						$("#upload-icon").removeClass("layui-icon-success").addClass("layui-icon-error").css("color", "#ff5722");
						$("#upload-msg").html("上传失败，请重试");
					}
				}
			});
		}
	});
}