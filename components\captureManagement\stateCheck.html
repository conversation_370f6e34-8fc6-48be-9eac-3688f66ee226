<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="Shortcut Icon" href="../../img/favicon.ico">
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="../../css/icon.css">
    <script src="../../plugins/layui/layui.js"></script>
    <link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
    <script src="../../plugins/index/jquery.min.js"></script>
    <script src="../../plugins/easyui/jquery.easyui.min.js"></script>
    <script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>



    <script type="text/javascript" src="../js/intercept.js"></script>
    <script type="text/javascript" src="../js/logUtil.js"></script>

    <object id="DWebSignSeal" classid="CLSID:77709A87-71F9-41AE-904F-886976F99E3E"
            codebase="http://www.XXX.com.cn/demo/websign/WebSign.ocx#version=4,4,9,6" width="100" height="100"></object>
</head>


<body style="padding: 15px">
<form class="layui-form layui-form-pane" action="">


    <div class="layui-form-item">
        <label class="layui-form-label">签名</label>
        <div class="layui-input-block">
            <img id="IMG_SRC" name="IMG_SRC" src="" onclick="popHandWritePage()" style="width: 300px;height: 150px"/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">确认状态</label>
        <div class="layui-input-block">
            <select name="STATES" lay-verify="required" id="STATES">
                <option value="已确认">已确认</option>
                <option value="未确认">未确认</option>
            </select>
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label">确认意见</label>
        <div class="layui-input-block">
            <textarea placeholder="请输入内容" class="layui-textarea" name="COMMENT" id="COMMENT"></textarea>
        </div>
    </div>

    <div class="layui-form-item" style="">
        <center>
            <button id="btn_add" class="layui-btn" lay-submit lay-filter="addStateCheckRecord">提交</button>
            <button id="btn_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
            <!--<button class="layui-btn" lay-submit lay-filter="addData">提交</button>-->
            <!--<button class="layui-btn layui-btn-primary" type="reset">重置</button>-->
        </center>
    </div>
</form>


</body>

<script>
    layui.config({
        base: '/DataPackageManagement/build/js/' //假设这是你存放拓展模块的根目录
    }).use(['form', 'table', 'utils', 'layer', 'upload', 'laypage'], function () {
        var form = layui.form;
        var table = layui.table;
        var utils = layui.utils;
        var layer = layui.layer;
        var upload = layui.upload;


        //监听提交
        form.on('submit(addStateCheckRecord)', function (data) {
            //layer.msg(JSON.stringify(data.field));
            //layer.msg(JSON.stringify(data.field));
            var param = data.field;
            //console.log(JSON.stringify(data.field));
            var selectedTab = parent.$('#root_layout_tabs').tabs('getSelected');
            var tabId = selectedTab.panel('options').name;
            var sels = parent.$('#' + tabId).datagrid('getSelections');

            var tableName = tabId;
            var type = "";
            if (tableName === 'design_list_table') {
                type = 'DESIGN_DATA_RESULT';
            } else if (tableName === 'craft_list_table') {
                type = 'CRAFT_DATA_RESULT';
            } else if (tableName === 'processcontrol_list_table') {
                type = 'PROCESS_CONTROL_RESULT';
            } else if (tableName === 'quanlitycontrol_list_table') {
                type = 'QUALITY_CONTROL_RESULT';
            }
            console.log(type);
            var cb_success = function(data){
                //新增完成后需要刷新界面
                layer.alert("编辑成功",{icon:1},function(index){
                    //提示完成后，点击确定再刷新界面
                    parent.reloadTable(tableName);

                    logRecord('更新','更新项目清单');

                    parent.layer.closeAll(); //再执行关闭
                });
            };
            //添加失败的弹窗
            var cb_error = function(xhr){
                layer.alert('编辑失败!',{icon:2});
            };


            var ids = '';
            for (var i = 0; i < sels.length; i++) {
                var row = sels[i];
                if (row.STATE_CHECK !== '已确认') {
                    if(row.ID!==undefined){
                        ids += ',' + row.ID;
                    }
                }
            }
            console.log(ids);
            param.DATA_ID = ids;
            param.IMG_SRC = $('#IMG_SRC').attr('src');
            param.type = type;
            param.DATA_TYPE = "数据包清单";
            console.log(param);
            //同步新增
            twxAjax("Thing.Fn.StateCheck", "insertStateCheckRecord", param, false, cb_success, cb_error);
            return false;
        });

    });


    $(function () {
        // $("#file_types").empty();
        //layui.form.render();


    });

    function dataURLtoBlob(dataurl) {
        var arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], {
            type: mime
        })
    }

    function popHandWritePage() {
        var strSealName = document.all.DWebSignSeal.HandWrite(8, 0, "signName");
        document.all.DWebSignSeal.ShowWebSeals();
        var data = document.all.DWebSignSeal.GetSealBmpString(strSealName, "jpg");
        //document.getElementById("IMG_SRC").src = "data:image/jpg;base64," + data;
        $('#IMG_SRC').attr('src',"data:image/jpg;base64," + data);
        //alert(data);
        document.all.DWebSignSeal.GetSealBmpToFile(strSealName, "jpg", "C:\\signTest\\test.jpg");
        document.all.DWebSignSeal.DelSeal(strSealName);
    }


</script>