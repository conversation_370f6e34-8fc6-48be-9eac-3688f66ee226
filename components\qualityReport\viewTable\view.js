var $ = layui.jquery;
var layer = layui.layer;
var dropdown = layui.dropdown;

function tableData2Html(data) {
    var tableData = data.tableData;
    var mergeds = data.merged;
    var metas = data.meta;

    for (var i = 0; i < tableData.length; i++) {
        var row = tableData[i];
        for (var j = 0; j < row.length; j++) {
            tableData[i][j] = {
                className: "",
                value: tableData[i][j],
                rowspan: 1,
                colspan: 1,
                row: i,
                col: j
            };
        }
    }
    if (metas) {
        for (var m = 0; m < metas.length; m++) {
            var meta = metas[m];
            if (meta) {
                var row = meta.row;
                var col = meta.col;
                if (tableData[row]) {
                    if (tableData[row][col]) {
                        var className = meta.className || "htMiddle htCenter";
                        if (className != "") {
                            tableData[row][col]["className"] = className;
                        }
                        var readOnly = meta.readOnly || false;
                        tableData[row][col]["readOnly"] = readOnly;

                        var eles = meta.eles || [];
                        tableData[row][col]["eles"] = eles;

                        if (meta.comment) {
                            tableData[row][col]["comment"] = meta.comment;
                        } else {
                            tableData[row][col]["comment"] = "";
                        }
                    }
                }
            }
        }
    }
    if (mergeds) {
        for (var m = 0; m < mergeds.length; m++) {
            var merged = mergeds[m];
            var row = merged.row;
            var col = merged.col;
            var rowspan = merged.rowspan;
            var colspan = merged.colspan;
            for (var r = row; r < rowspan + row; r++) {
                for (var c = col; c < colspan + col; c++) {
                    if (!(r == row && c == col)) {
                        tableData[r][c].value = "del";
                    }
                }
            }
            tableData[row][col]["rowspan"] = rowspan;
            tableData[row][col]["colspan"] = colspan;
        }
    }
    var html = '<table class = "layui-table">';
    for (var i = 0; i < tableData.length; i++) {
        var row = tableData[i];
        html += "<tr>";
        for (var j = 0; j < row.length; j++) {
            var cell = row[j];
            var className = cell.className;
            var readOnly = cell.readOnly;
            if (cell.value != "del") {
                var value = cell.value == null ? "" : cell.value;
                var eles = cell['eles'] || [];
                for (var e = 0; e < eles.length; e++) {
                    var ele = eles[e];
                    value += "<br>";
                    if (ele.type == "sign") {
                        var eleClass = ele.class || 'sign-img';
                        if (eleClass.indexOf('test-sign') > -1) {
                            eleClass = 'sign-img test-sign';
                        }
                        value += '<img type="' + ele.type + '" src="/File' + ele.src + '" class="' + eleClass + '" date="' + ele.date + '">';
                    } else if (ele.type == "photo") {
                        value += '<img type="photo" id="' + ele.id + '" photoname="' + ele.photoName + '" photoshownum="' + ele.photoShowNum + '" photoformat="' + ele.photoFormat + '" src="' + ele.src + '" class="sign-img photo" date="' + ele.date + '">';
                    }
                    value += '<br><span>' + ele.date + '</span>';
                }
                var comment = "";
                if (cell.comment) {
                    logger.error("cell.comment:" + cell.comment);
                    className = className + " htCommentCell";
                    comment = cell.comment.value || "";
                }
                html += '<td rowspan="' + cell.rowspan + '" class = "' + className + '" comment = "' + comment + '" lock="' + readOnly + '" row="' + cell.row + '" col="' + cell.col + '"   colspan="' + cell.colspan + '">' + value + "</td>";
            }
        }
        html += "</tr>";
    }
    html += "</table>";
    return html;
}

function dealImgNumShow(photoShowNums) {
    //表a1-12
    var tableNum = photoShowNums[0].substr(0, photoShowNums[0].lastIndexOf('-'));
    if (tableNum) {
        tableNum = tableNum + "-";
    }

    function getNum(photoShowNum) {
        //表a1-12-图8
        var tempArr = photoShowNum.split("-");
        var num = parseInt(tempArr[tempArr.length - 1].substr(1));
        return num;
    }

    var arr = [];

    for (var i = 0; i < photoShowNums.length; i++) {
        var photoShowNum = photoShowNums[i];
        var num = getNum(photoShowNum);
        arr.push(num);
    }


    // 定义一个新数组用于存储结果
    var newArr = [];

    // 使用for循环遍历原始数组
    for (var i = 0; i < arr.length; i++) {
        // 定义一个临时数组用于存储连续数字
        var tempArr = [arr[i]];

        // 使用while循环查找连续数字
        while (arr[i + 1] - arr[i] === 1) {
            tempArr.push(arr[i + 1]);
            i++;
        }
        // 将结果推入新数组
        newArr.push(tempArr);
    }

    var textArr = [""];

    for (var i = 0; i < newArr.length; i++) {
        var arr = newArr[i];
        if (arr.length == 1) {
            textArr.push('<a href="javascript:;" class="img-link">' + tableNum + "图" + arr[0] + '</a>');
        } else {
            textArr.push('<a href="javascript:;" class="img-link">' + tableNum + "图" + arr[0] + "~" + "图" + arr[arr.length - 1] + '</a>');
        }
    }
    var span = '<span style="color:red;">' + textArr.join("<br>") + '</span>'
    return span;
}

function delTableImg($table) {
    $table.find("td").each(function (i, n) {
        var $td = $(n);
        if ($td.find("img[type='photo']").length > 0) {
            var imgs = [];
            var photoShowNums = [];
            $td.find("img[type='photo']").each(function (j, m) {
                var $img = $(m);
                var photoPath = $img.attr("src");
                var photoName = $img.attr("photoName");
                var photoShowNum = $img.attr("photoShowNum");
                var photoId = $img.attr("id");
                var photoformat = $img.attr("photoformat");
                var imgData = {
                    photoPath: photoPath,
                    photoName: photoName,
                    photoShowNum: photoShowNum,
                    photoId: photoId,
                    photoformat: photoformat
                };
                imgs.push(imgData);
                photoShowNums.push(photoShowNum);

                //删除元素
                var $br1 = $img.prev();
                var $br2 = $img.next();
                var $span = $br2.next();
                $br1.remove();
                $br2.remove();
                $span.remove();
                $img.remove();
            });
            $td.data("imgs", imgs);
            $td.data("hasImg", true);
            var numText = dealImgNumShow(photoShowNums);
            $td.append(numText);
            
            // 为图序号添加点击事件 - 直接预览图片而不是显示缩略图
            $td.find('.img-link').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showFullSizeImage(imgs, 0);
            });
            
            // 为有图片的单元格添加右键菜单
            $td.on('contextmenu', function(e) {
                e.preventDefault();
                var cellImgs = $(this).data("imgs");
                
                // 根据官方文档修改右键菜单实现
                dropdown.render({
                    elem: this,
                    trigger: 'contextmenu',
                    data: [{
                        title: '查看图片',
                        id: 'view-images',
                        templet: function(){
                            return '<div><i class="layui-icon layui-icon-eye"></i> 查看图片</div>';
                        }
                    }],
                    click: function(data, othis) {
                        if (data.id === 'view-images') {
                            showImageThumbnails(cellImgs);
                        }
                    }
                });
                
                return false;
            });
        } else {
            $td.data("hasImg", false);
        }
    });
}

// 显示图片缩略图
function showImageThumbnails(images) {
    if (!images || images.length === 0) return;
    
    var content = '<div class="image-thumbnails">';
    for (var i = 0; i < images.length; i++) {
        var img = images[i];
        content += '<div class="image-item" data-index="' + i + '">';
        content += '<img src="' + img.photoPath + '" alt="' + img.photoName + '" class="thumbnail">';
        content += '<div class="image-info">' + img.photoShowNum + '</div>';
        content += '</div>';
    }
    content += '</div>';
    
    layer.open({
        type: 1,
        title: '图片预览',
        content: content,
        area: ['830px', '600px'], // 增大预览框尺寸
        success: function(layero, index) {
            $(layero).find('.image-item').on('click', function() {
                var imgIndex = $(this).data('index');
                showFullSizeImage(images, imgIndex);
            });
        }
    });
}

// 显示大图并支持左右切换
function showFullSizeImage(images, currentIndex) {
    if (!images || images.length === 0) return;
    
    // 准备所有图片数据
    var photos = {
        "title": "图片查看",
        "start": currentIndex,
        "data": []
    };
    
    for (var i = 0; i < images.length; i++) {
        photos.data.push({
            "src": images[i].photoPath,
            "alt": images[i].photoName,
            "pid": i, // 图片索引
            "thumb": images[i].photoPath // 缩略图
        });
    }
    
    // 使用layer.photos显示图片并支持左右切换
    layer.photos({
        photos: photos,
        anim: 5, // 图片切换动画
        tab: function(pic, layero) {
            // 当前查看的图片索引
            console.log('当前查看第 ' + (pic.pid + 1) + ' 张，共 ' + images.length + ' 张');
        }
    });
}

function loadTable(str) {
    var data = JSON.parse(str);
    var html = tableData2Html(data);
    var $table = $(html.replaceAll("\n", "<br>"));
    delTableImg($table);
    $table.addClass('data-table').show();

    //处理表头
    var header = data.header || 0;
    if (header > 0) {
        //如果存在表头的话 固定表头显示
        var $thead = $('<thead class="sticky-thead"></thead>');
        //处理表头的显示
        $table.find("tr").each(function (i, n) {
            if (i <= (header - 1)) {
                $thead.append($(n).clone(true));
                $(n).remove();
            }
        });
        $table.prepend($thead);
    }

    var colWidths = data.colWidths || [];

    if (colWidths.length > 0) {
        var $colgroup = $('<colgroup></colgroup>');
        for (var i = 0; i < colWidths.length; i++) {
            $colgroup.append('<col width="' + colWidths[i] + '">');
        }
        $table.prepend($colgroup);
    }

    $table.find("td").each(function (i, n) {
        var comment = $(n).attr("comment");
        if (comment) {
            $(n).mouseover(function () {
                layer.tips(comment, this);
            });

            $(n).mouseout(function () {
                layer.closeAll();
            });
        }
    });



    $("#table").append($table);
}

// 获取URL中的sessionKey参数
function getSessionKey() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get("sessionKey");
}
const sessionKey = getSessionKey();
loadTable(sessionStorage.getItem(sessionKey));
