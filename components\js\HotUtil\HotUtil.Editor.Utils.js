/**
 * HotUtil.Editor.Utils.js - Handsontable 编辑器工具库 (辅助函数模块)
 *
 * 包含供编辑器内部使用的通用辅助函数，如选择区检查、遍历等。
 * 此文件应在其他 Editor 模块之前加载。
 */

/**
 * 检查选中的单元格中是至少有一个单元格是锁定状态
 * @param {Object} thisHot
 * @returns {boolean}
 */
HotUtil.atLeastOneReadOnly = function (thisHot) {
    var atLeastOneReadOnly = false;
    HotUtil.eachSelectedRange(thisHot, function (r, c) {
        if (thisHot.getCellMeta(r, c).readOnly) {
            atLeastOneReadOnly = true;
            return true;
        } else {
            return false;
        }
    });
    return atLeastOneReadOnly;
};

/**
 * 检查选中的单元格中全是锁定状态
 * @param {Object} thisHot
 * @returns {boolean}
 */
HotUtil.allReadOnly = function (thisHot) {
    var allReadOnly = true;
    HotUtil.eachSelectedRange(thisHot, function (r, c) {
        if (!thisHot.getCellMeta(r, c).readOnly) {
            allReadOnly = false;
            return true;
        } else {
            return false;
        }
    });
    return allReadOnly;
};

/**
 * 遍历选中的每一个单元格
 * @param {Object} thisHot
 * @param {Function} eachFn
 */
HotUtil.eachSelectedRange = function (thisHot, eachFn) {
    var srs = HotUtil.getSelectedRange();
    HotUtil.eachArrays(srs, thisHot, eachFn);
};

HotUtil.getSelectedRange = function () {
    var selectedRange = window.hot.getSelectedRange();
    if (selectedRange) {
        for (var i = 0; i < selectedRange.length; i++) {
            var selectedRangeElement = selectedRange[i];
            var from = selectedRangeElement.from;
            if (from.row == -1) {
                from.row = 0;
            }
            if (from.col == -1) {
                from.col = 0;
            }

            var to = selectedRangeElement.to;
            if (to.row == -1) {
                to.row = 0;
            }
            if (to.col == -1) {
                to.col = 0;
            }
        }
        return selectedRange;
    } else {
        return [];
    }
};

HotUtil.eachArrays = function (arrays, thisHot, eachFn) {
    for (var i = 0; i < arrays.length; i++) {
        var sr = arrays[i];
        for (var r = sr.from.row; r <= sr.to.row; r++) {
            for (var c = sr.from.col; c <= sr.to.col; c++) {
                if (r > -1 && c > -1) {
                    if (thisHot.getCellMeta(r, c).hidden) {
                        continue;
                    }
                    var isBreak = eachFn(r, c);
                    if (isBreak) {
                        break;
                    }
                }
            }
        }
    }
};

HotUtil.getSelecteds = function () {
    if (!window.hot) return [];
    var selected = window.hot.getSelected() || [];
    var arr = [];
    // hot.suspendRender();
    for (var index = 0; index < selected.length; index += 1) {
        var [row1, column1, row2, column2] = selected[index];
        var startRow = Math.max(Math.min(row1, row2), 0);
        var endRow = Math.max(row1, row2);
        var startCol = Math.max(Math.min(column1, column2), 0);
        var endCol = Math.max(column1, column2);

        for (var rowIndex = startRow; rowIndex <= endRow; rowIndex += 1) {
            for (var columnIndex = startCol; columnIndex <=
                endCol; columnIndex += 1) {
                var obj = {
                    row: rowIndex,
                    col: columnIndex
                };
                arr.push(obj);
            }
        }
    }
    return arr;
};

/**
 * 禁用列相关的右键操作
 * @param {Object} thisHot
 * @returns {boolean}
 */
HotUtil.disableColContextMenu = function (thisHot) {
    //选中的单元格是否存在锁定的
    var hasLock = HotUtil.atLeastOneReadOnly(thisHot);
    if (!hasLock) {
        //检测选中单元格同列是否存在锁定的
        var selectedRange = HotUtil.getSelectedRange()[0];
        for (var row = 0; row < hot.countRows(); row++) {
            for (var col = selectedRange.from.col; col <= selectedRange.to.col; col++) {
                if (col > -1 && row > -1) {
                    if (thisHot.getCellMeta(row, col).readOnly) {
                        hasLock = true;
                        break;
                    }
                }
            }
        }
    }
    return hasLock;
};

/**
 * 自动填充自动增加序列
 * @param {string} str
 * @param {string} [type]
 * @returns {string}
 */
HotUtil.incrementString = function (str, type) {
    if (str == "" || str == null || str == " ") {
        return "";
    }
    var regex = /(\d+)(?!.*\d)/;
    var match = regex.exec(str);
    if (match) {
        var num = parseInt(match[1]);
        var incremented = (num + 1).toString();
        if (type == 'subtract') {
            incremented = (num - 1).toString();
        }
        return str.slice(0, match.index) + incremented.padStart(match[1].length, '0') + str.slice(match
            .index + match[1].length);
    } else {
        return str;
    }
};

HotUtil.getColWidths = function () {
    if (!window.hot) return [];
    var countCols = window.hot.countCols();
    var colWidths = [];
    for (var i = 0; i < countCols; i++) {
        colWidths.push(window.hot.getColWidth(i));
    }
    return colWidths;
};

HotUtil.getRowHieghts = function () {
    if (!window.hot) return [];
    var countRows = window.hot.countRows();
    var rowHieghts = [];
    for (var i = 0; i < countRows; i++) {
        rowHieghts.push(window.hot.getRowHeight(i));
    }
    return rowHieghts;
};

HotUtil.myGetCellsMeta = function () {
    if (!window.hot) return [];
    var meta = [];
    var colCount = window.hot.countCols();
    var rowCount = window.hot.countRows();
    for (var i = 0; i < rowCount; i++) {
        for (var j = 0; j < colCount; j++) {
            var cellMeta = window.hot.getCellMeta(i, j);
            if (cellMeta.className) {
                cellMeta.className = cellMeta.className.replaceAll("td-bg", "").trim();
            }
            meta.push(cellMeta);
        }
    }
    return meta;
};