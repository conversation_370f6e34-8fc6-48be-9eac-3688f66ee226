
/**
 * <AUTHOR>
 * @datetime 2025年4月2日10:17:34
 * @function	featureTestCalculation
 * @description	卫星质量特性测试计算
 */


/**
 * 同步质测数据
 * @param {Object} treeNode - 当前节点
 */
function syncQualityTest() {
    layer.confirm('确定要同步质测数据吗？', {
        icon: 3,
        title: '提示'
    }, function(index) {
        layer.close(index);
        var loadIndex = layer.msg('正在请求中', {
            icon: 16,
            shade: 0.01
        });
        twxAjax('Thing.Fn.QualityTestDataQuery', 'SyncData', {
        }, true, function (res) {
            layer.close(loadIndex);
            if (res.success) {
                layer.msg(res.message)
            } else {
                layer.alert(res.message, {
                    icon: 2
                });
            }
        }, function (xhr, textStatus, errorThrown) {
            layer.close(loadIndex);
            layer.alert('请求出错！', {
                icon: 2
            });
        });
    });
}

/**
 * 计算结果按钮显示条件判断
 * @param {Object} treeNode - 当前节点
 * @returns {boolean} - 是否显示计算结果按钮
 */
function isCalculateButtonVisible(treeNode) {
    if (treeNode.TYPE === 'table' && treeNode.NAME === '测试结果确认表') {
        var parent = treeNode.getParentNode();
        if (parent && parent.TYPE === 'report' && parent.NAME === '卫星质量特性准出控制记录表') {
            var grandparent = parent.getParentNode();
            if (grandparent && grandparent.TYPE === 'leaf' && grandparent.NAME === '特性测试') {
                return true;
            }
        }
    }
    return false;
}


/**
 * 打开计算配置对话框
 * @param {Object} treeNode - 当前节点
 */
function openCalculationDialog(treeNode) {
    var layerIndex = layer.open({
        title: '计算结果',
        type: 1,
        area: ['850px', '450px'],
        content: $('#calculationHtml').html(),
        success: function () {
            // 渲染表单
            layui.form.render();

            // 计算按钮点击事件
            $('#calculate').click(function () {
                // 获取参数
                var coordinate = $('select[name="coordinate"]').val();
                var reference = $('select[name="reference"]').val();
                var treeId = treeNode.TREE_ID;

                // 显示加载
                var loadIndex = layer.load(1, {
                    shade: [0.1, '#fff']
                });

                // 调用计算服务
                twxAjax("Thing.Fn.QualityTestDataQuery", 'CalculateQualityTestResult', {
                    treeId: treeId,
                    coordinate: coordinate,
                    reference: reference
                }, true,
                    function (res) {
                        layer.close(loadIndex);
                        if (res.success) {
                            // 填充计算结果
                            fillCalculationResult(res.data);
                        } else {
                            layer.alert(res.msg, {
                                icon: 2
                            });
                        }
                    },
                    function (xhr, textStatus, errorThrown) {
                        layer.close(loadIndex);
                        layer.alert('计算请求出错！', {
                            icon: 2
                        });
                    });
            });

            // 应用按钮点击事件
            $('#apply').click(function () {
                // 从表格中获取计算结果数据
                var resultData = getCalculationResultData();

                // 显示加载
                var loadIndex = layer.load(1, {
                    shade: [0.1, '#fff']
                });

                // 调用应用服务
                twxAjax("Thing.Fn.QualityTestDataQuery", 'ApplyQualityTestResult', {
                    tableId: treeNode.ID,
                    saveUser: sessionStorage.getItem("username"),
                    resultData: JSON.stringify(resultData)
                }, true,
                    function (res) {
                        layer.close(loadIndex);
                        if (res.success) {
                            layer.close(layerIndex);
                            // 刷新表格
                            reloadTable(treeNode);
                            layer.msg('应用成功', { icon: 1 });
                        } else {
                            layer.alert(res.msg, {
                                icon: 2
                            });
                        }
                    },
                    function (xhr, textStatus, errorThrown) {
                        layer.close(loadIndex);
                        layer.alert('应用计算结果出错！', {
                            icon: 2
                        });
                    });
            });

            // 取消按钮点击事件
            $('#cancel').click(function () {
                layer.close(layerIndex);
            });
        }
    });
}

/**
 * 填充计算结果到表格
 * @param {Object} data - 计算结果数据
 */
function fillCalculationResult(data) {
    var table = $('#calculation-result-table');
    var row = table.find('tbody tr').eq(0);

    // 填充所有数据到同一行
    // 重量
    row.find('td').eq(0).text(data.weight || '');

    // 质心位置
    row.find('td').eq(1).text(data.centerMass.x || '');
    row.find('td').eq(2).text(data.centerMass.y || '');
    row.find('td').eq(3).text(data.centerMass.z || '');

    // 转动惯量
    row.find('td').eq(4).text(data.inertia.Ixx || '');
    row.find('td').eq(5).text(data.inertia.Iyy || '');
    row.find('td').eq(6).text(data.inertia.Izz || '');
    row.find('td').eq(7).text(data.inertia.Ixy || '');
    row.find('td').eq(8).text(data.inertia.Ixz || '');
    row.find('td').eq(9).text(data.inertia.Iyz || '');
}

/**
 * 从表格获取计算结果数据
 * @returns {Object} - 计算结果数据
 */
function getCalculationResultData() {
    var table = $('#calculation-result-table');
    var row = table.find('tbody tr').eq(0);

    var resultData = {
        weight: row.find('td').eq(0).text(),
        centerMass: {
            x: row.find('td').eq(1).text(),
            y: row.find('td').eq(2).text(),
            z: row.find('td').eq(3).text()
        },
        inertia: {
            Ixx: row.find('td').eq(4).text(),
            Iyy: row.find('td').eq(5).text(),
            Izz: row.find('td').eq(6).text(),
            Ixy: row.find('td').eq(7).text(),
            Ixz: row.find('td').eq(8).text(),
            Iyz: row.find('td').eq(9).text()
        }
    };

    return resultData;
}