function renderTable(id, title, tableData, tableCols, btns, listInfo) {
	var inst = table.render({
		elem: '#' + id,
		cols: [tableCols],
		toolbar: getBtnHtml(btns),
		defaultToolbar: [],
		data: tableData,
		page: false
	});

	table.on('tool(' + id + ')', function(obj) {
		var data = obj.data;
		if (obj.event === 'viewFile') {
			var myFile = JSON.parse(data.myFile || '{}');
			var filePath = myFile.filePath || '';
			if (filePath) {
				previewfile({
					FILEPATH: myFile.filePath,
					FILE_NAME: myFile.fileName,
					FILE_FORMAT: myFile.fileFormat,
					FILE_TYPE: "",
					GATHERING_METHOD: ""
				});
			} else {
				layer.alert('未发现文件地址！', {
					icon: 2
				});
			}
		} else if (obj.event === 'uploadRowFile') {
			//上传行内文件
			showRowFileList(obj, title, listInfo.LIST_ID);
		}
	});

	// 工具栏事件
	table.on('toolbar(' + id + ')', function(obj) {
		var checkStatus = table.checkStatus(id);
		var othis = this;
		switch (obj.event) {
			case 'addRow':
				addRow(id, tableCols);
				break;
			case 'deleteRow':
				deleteRow(id);
				break;
			case 'exportTable':
				exportTable(id, title, tableCols);
				break;
			case 'importTable':
				importTable(id, title, tableCols);
				break;
			case 'saveTable':
				saveTable(id, listInfo.LIST_ID, function() {});
				break;
			case 'addFile':
				addFile(id, title, listInfo.LIST_NAME);
				break;
		};
	});
}

/**
 * 保存数据
 * @param {Object} id
 * @param {Object} listId
 */
function saveTable(id, listId, successFn) {
	var tableDatas = table.getData(id);
	twxAjax(THING, "SaveData", {
		taskId: taskId,
		nodeCode: id,
		listId: listId,
		data: JSON.stringify(tableDatas),
		optUser: sessionStorage.getItem('username')
	}, true, function(res) {
		if (res.success) {
			successFn();
			layer.msg(res.msg);
			$("#submitTime").val(res.data.submitTime);
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});
}


/**
 * 增加一行数据

 * @param {Object} id
 * @param {Object} tableCols
 */
function addRow(id, tableCols) {
	var datas = table.cache[id];
	var row = {};
	for (var i = 0; i < tableCols.length; i++) {
		if (tableCols[i].type == 'normal') {
			row[tableCols[i].field] = "";
		}
	}
	datas.push(row);
	table.renderData(id);
}

/**
 * 删除一行数据

 * @param {Object} id
 */
function deleteRow(id) {
	if (table.checkStatus(id).data.length == 0) {
		layer.alert('至少选择一条数据删除！', {
			icon: 2
		});
	} else {
		var datas = table.cache[id];
		var newData = [];
		for (var i = 0; i < datas.length; i++) {
			if (!datas[i]['LAY_CHECKED']) {
				newData.push(datas[i]);
			}
		}
		table.cache[id] = newData;
		table.renderData(id);
	}
}

/**
 * 导入excle
 * @param {Object} id
 * @param {Object} tableCols
 */
function importTable(id, title, tableCols) {
	var fileFlag = false;
	var selectFile;
	layer.open({
		title: '导入' + title,
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['400px', '250px'],
		content: '<div id="importContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function() {
			if (!fileFlag) {
				layer.alert('请选择需要导入的excel文件!', {
					icon: 2
				});
				return false;
			} else {
				var reader = new FileReader();
				reader.readAsBinaryString(selectFile);

				reader.onload = function(e) {
					// Parse Excel data
					var data = e.target.result;
					var workbook = XLSX.read(data, {
						type: 'binary'
					});
					var worksheet = workbook.Sheets[workbook.SheetNames[0]];
					var jsonDatas = XLSX.utils.sheet_to_json(worksheet);

					var newDatas = [];
					for (var i = 0; i < jsonDatas.length; i++) {
						var jsonData = jsonDatas[i];
						var newData = {};
						for (var key in jsonData) {
							for (var j = 0; j < tableCols.length; j++) {
								if (tableCols[j].type == 'normal') {
									if (key == tableCols[j].title) {
										newData[tableCols[j].field] = String(jsonData[key]);
										break;
									}
								}
							}
						}
						newDatas.push(newData);
					}
					table.cache[id] = newDatas;
					table.renderData(id);
					layer.closeAll();
				}
			}
		},
		btn2: function() {
			layer.closeAll();
		},
		success: function() {
			var addTpl = `<form class="layui-form" lay-filter="importForm">
						<div class="layui-form-item">
							<label class="layui-form-label">文件内容:</label>
							<div class="layui-input-block">
								<div class="layui-upload">
									<button type="button" class="layui-btn layui-btn-normal" id="importChoice">选择文件</button>
								</div>
							</div>
						</div>
						<div class="layui-form-item" id="selectedFile" style="display: none;">
							<label class="layui-form-label">已选文件:</label>
							<div class="layui-input-block">
								<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
							</div>
						</div>
					</form>`;
			$("#importContent").append(addTpl);
			form.render(null, 'importForm');

			var uploadInst = upload.render({
				elem: '#importChoice',
				auto: false,
				accept: 'file',
				exts: 'xlsx',
				acceptMime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				choose: function(obj) {
					fileFlag = true;
					var files = obj.pushFile();
					var filename = '';
					for (var k in files) {
						selectFile = files[k];
						filename = selectFile.name;
					}
					$("#selectedFile").show();
					$("#selectedFileName").text(filename);
				},
				done: function(res, index, upload) {}
			});
		}
	});
}

/**
 * 导出表格

 * @param {Object} id
 * @param {Object} title
 * @param {Object} tableCols
 */
function exportTable(id, title, tableCols) {
	var tableDatas = table.getData(id);
	if (tableDatas.length == 0) {
		var row = {};
		for (var i = 0; i < tableCols.length; i++) {
			if (tableCols[i].type == 'normal') {
				row[tableCols[i].field] = "";
			}
		}
		tableDatas.push(row);
	}
	var newDatas = [];
	for (var i = 0; i < tableDatas.length; i++) {
		var tableData = tableDatas[i];
		var newData = {};
		for (var key in tableData) {
			for (var j = 0; j < tableCols.length; j++) {
				if (tableCols[j].type == 'normal') {
					if (key == tableCols[j].field) {
						newData[tableCols[j].title] = tableData[key];
						break;
					}
				}
			}
		}
		newDatas.push(newData);
	}

	var sheet = XLSX.utils.json_to_sheet(newDatas);
	openDownloadDialog(sheet2blob(sheet), title + '.xlsx');
}