/**
 * 加载我的流程表格搜索表单
 */
function renderMyProcessSearchForm() {
	form.render(null, 'init-table-form');
	laydate.render({
		elem: '#init-range-date',
		range: ['#init-start-date', '#init-end-date'],
		rangeLinked: true // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
	});

	// 搜索提交
	form.on('submit(init-table-search)', function(data) {
		var field = data.field; // 获得表单字段
		field.username = currentUser;
		// 执行搜索重载
		table.reload('init-table', {
			page: {
				curr: 1 // 重新从第 1 页开始
			},
			where: field // 搜索的字段
		});
		return false;
	});
}

/**
 * 加载我的流程列表
 */
function renderMyProcess() {
	// 创建渲染实例
	table.render({
		elem: '#init-table',
		id: 'init-table',
		url: getUrl(THING, 'QueryMyInitProcess'),
		where: {
			username: currentUser
		},
		toolbar: '#init-toolbar',
		defaultToolbar: ['filter', {
			title: '刷新',
			layEvent: 'init_reload',
			icon: 'layui-icon-refresh'
		}],
		height: 'full-91', // 最大高度减去其他容器已占有的高度差
		cellMinWidth: 80,
		page: true,
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'PROC_INST_ID_',
					width: 100,
					title: '流程编号',
					align: 'center'
				},
				{
					field: 'PROC_DEF_NAME_',
					title: '流程名称',
					width: 150
				},
				{
					field: 'PROC_DEF_VERSION_',
					width: 100,
					title: '流程版本',
					templet: formatVersion,
					align: 'center'
				},
				{
					field: 'START_TIME_',
					width: 160,
					title: '提交时间',
					templet: formatTableDate,
					align: 'center'
				},
				{
					field: 'END_TIME_',
					title: '流程状态',
					width: 130,
					templet: function(d) {
						var html = "";
						if (d.END_TIME_) {
							html = '<span class="layui-badge layui-bg-blue">已完成</span>';
						} else {
							html = '<span class="layui-badge layui-bg-green">进行中</span>';
						}
						return html;
					},
					align: 'center'
				},
				{
					field: 'DURATION_',
					width: 185,
					title: '耗时',
					templet: function(d) {
						var duration = d.DURATION_ ? d.DURATION_ : new Date().getTime() - d.START_TIME_;
						return formatDuration(duration);
					}
				},
				{
					field: 'TASK_NAME_',
					title: '当前节点',
					width: 130
				},
				{
					field: 'TASK_ASSIGNEE_NAME_',
					title: '办理人',
					width: 100,
					align: 'center'
				},
				{
					fixed: 'right',
					title: '操作',
					width: 180,
					minWidth: 180,
					toolbar: '#init-rowbar',
					align: 'center'
				}
			]
		],
		done: function() {

		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});
}


/**
 * 加载表格工具事件
 */
function initMyProcessTableTool() {
	// 触发单元格工具事件
	table.on('tool(init-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'view') {
			layer.tab({
				area: ['1040px', '750px'],
				tab: [{
					title: '表单信息',
					content: '<div class="form-content" id="view-form-content"></div>',
				}, {
					title: '流转记录',
					content: '<div class="record-content" id="view-record-content"></div>'
				}, {
					title: '流程图',
					content: '<div class="chat-content" id="flow-chart-content"></div>'
				}],
				success: function() {
					viewForm(data, "view-form-content");
					viewTransferRecords(data, "view-record-content");
				},
				change: function(index, layero) { //监听tab切换
					if (index == 2) {
						viewFlowChart(data, "flow-chart-content");
					}
				}

			});
		} else if (obj.event === 'cancel') {
			layer.confirm('取消申请会使流程直接结束，请确认是否取消？', {
				icon: 3
			}, function(lIndex) {
				twxAjax(THING, 'postFlw', {
					servlet: '/zero',
					params: {
						act: 'stopProcess',
						procInstId: data['PROC_INST_ID_']
					},
				}, true, function(res) {
					layer.close(lIndex);
					if (res.success) {
						layer.msg(res.msg)
						reloadInitTable();
					} else {
						layer.alert(res.msg);
					}
				}, function(xhr, textStatus, errorThrown) {
					layer.alert('请求出错！', {
						icon: 2
					});
				});
			}, function(lIndex) {
				layer.close(lIndex);
			});
		} else if (obj.event === 'delete') {
			layer.confirm('确定要删除该流程吗？', {
				icon: 3
			}, function(lIndex) {
				twxAjax(THING, 'postFlw', {
					servlet: '/zero',
					params: {
						act: 'deleteProcess',
						procInstId: data['PROC_INST_ID_']
					},
				}, true, function(res) {
					layer.close(lIndex);
					if (res.success) {
						layer.msg(res.msg)
						reloadInitTable();
					} else {
						layer.alert(res.msg);
					}
				}, function(xhr, textStatus, errorThrown) {
					layer.alert('请求出错！', {
						icon: 2
					});
				});
			}, function(lIndex) {
				layer.close(lIndex);
			});
		}
	});

	// 工具栏事件
	table.on('toolbar(init-table)', function(obj) {
		var id = obj.config.id;
		var checkStatus = table.checkStatus(id);
		var othis = lay(this);
		switch (obj.event) {
			case 'init-btn':
				initProcess();
				break;
			case 'getData':
				var getData = table.getData(id);
				console.log(getData);
				layer.alert(layui.util.escape(JSON.stringify(getData)));
				break;
			case 'init_reload':
				reloadInitTable();
				break;
		};
	});
}

/**
 * 发起流程
 */
function initProcess() {


	layer.open({
		title: "发起新流程",
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: true,
		resize: false, //不允许拉伸
		area: ['1040px', '700px'],
		scrollbar: false,
		btn: ['提交', '取消'],
		btnAlign: 'c',
		btn1: function(index, layero, that) {
			$("#zero-form-submit").click();
		},
		btn2: function(index, layero, that) {
			return true;
		},
		content: '<div class="form-content" id="form-content"></div>',
		success: function() {
			var html = $("#zero-form-html")[0].innerHTML;
			$("#form-content").append(html);
			$("#z_num").val(getZNum());
			dealForm("initProcess");
			form.render(null, 'zero-form');
			//日期
			laydate.render({
				elem: '#z_date'
			});

			form.on('submit(zero-form-submit)', function(data) {
				getNextFlowNodeByStart(function(assignee) {
					alterSelectUser(assignee, function(selectUser) {
						var loadIndex = layer.msg('正在提交中......', {
							icon: 16,
							shade: 0.01,
							time: 0
						});
						var field = data.field; // 获取表单字段值
						var qualityDivision = selectUser;
						field.Z_QUESTION_DESC_U = sessionStorage.getItem("username");
						field.Z_REQUEST_U = qualityDivision;
						twxAjax(THING, "postFlw", {
							servlet: "/zero",
							params: {
								act: "initiate",
								params: JSON.stringify(field)
							}
						}, true, function(res) {
							layer.close(loadIndex);
							if (res.success) {
								layer.closeAll();
								layer.alert(res.msg)
								reloadInitTable();
							} else {
								layer.alert(res.msg);
							}
						}, function(xhr, textStatus, errorThrown) {
							layer.alert("请求出错！", {
								icon: 2
							});
						});

					});
				});
				return false; // 阻止默认 form 跳转
			});
		}
	});
}

/**
 * 重新加载发起流程的列表
 */
function reloadInitTable() {
	table.reloadData('init-table');
}


/**
 * 自动生成编号
 */
function getZNum() {
	return util.toDateString(new Date().getTime(), 'yyyyMMddHHmmss')
}