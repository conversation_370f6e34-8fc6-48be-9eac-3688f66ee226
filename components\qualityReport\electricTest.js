/**
 * <AUTHOR>
 * @datetime 2025年4月2日10:44:39
 * @function	electricTest
 * @description	电测试集成相关
 */

/**
 * 同步电测试系统
 * @param {Object} treeNode - 当前节点
 */
function syncElectricTest(treeNode) {
    layer.confirm('确定要同步电测试系统数据吗？', {
        icon: 3,
        title: '提示'
    }, function(index) {
        layer.close(index);
        var loadIndex = layer.msg('正在请求中', {
            icon: 16,
            shade: 0.01
        });
        twxAjax('Thing.Fn.ElectricTest', 'ManualSync', {
            treeId: treeNode.TREEID
        }, true, function (res) {
            layer.close(loadIndex);
            if (res.success) {
                reloadTree(treeNode.PID);
                layer.msg(res.msg)
            } else {
                layer.alert(res.msg, {
                    icon: 2
                });
            }
        }, function (xhr, textStatus, errorThrown) {
            layer.close(loadIndex);
            layer.alert('请求出错！', {
                icon: 2
            });
        });
    });
}


/**
 * 打开附件弹窗
 * @param {Object} treeNode - 当前节点
 */
function openAttachmentDialog(treeNode) {
    var attachments = JSON.parse(treeNode.ATTACHMENT);
    var layerIndex = layer.open({
        title: '附件',
        type: 1,
        area: ['1100px', '660px'],
        content: '<div style="padding:12px;"><div id="attachmentTable"></div></div>',
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        resize: false,
        success: function () {
            window.previewAttachemnt = function (filePath, fileName, fileFormat) {
                previewfile({
                    FILEPATH: filePath,
                    FILE_NAME: fileName,
                    FILE_FORMAT: fileFormat,
                    FILE_TYPE: "",
                    GATHERING_METHOD: ""
                });
            }

            table.render({
                elem: '#attachmentTable',
                data: attachments, // 数据
                cols: [
                    [{
                        field: 'fileName',
                        title: '附件名称',
                    },
                    {
                        field: 'fileSize',
                        title: '附件大小',
                        width: 100
                    },
                    {
                        field: 'fileFormat',
                        title: '附件格式',
                        width: 100
                    },
                    {
                        field: 'createTime',
                        title: '创建时间',
                        width: 160
                    },
                    {
                        field: '操作',
                        title: '操作',
                        width: 120,
                        templet: function (d) {
                            var fileName = d.fileName + "." + d.fileFormat;
                            return (
                                '<a class="layui-btn layui-btn-xs" onclick="previewAttachemnt(\'' +
                                d.filePath + '\',\'' + d.fileName +
                                '\',\'' + d.fileFormat +
                                '\')">预览</a> <a class="layui-btn layui-btn-xs" href="/File/' +
                                d.filePath + '" download="' + fileName +
                                '">下载</a>'
                            );
                        },
                    },
                    ],
                ],
            });
            $('#attachment-table').datagrid({
                data: attachments,
                fitColumns: true,
                idField: 'index',
                // fit: true,
                columns: [
                    [{
                        field: 'fileName',
                        title: '附件名称',
                        width: 350
                    }, {
                        field: 'fileSize',
                        title: '附件大小',
                        width: 100,
                        align: 'right',
                    }, {
                        field: 'fileFormat',
                        title: '附件格式',
                        width: 100
                    }, {
                        field: 'operation',
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            return '<a onclick="" class="layui-btn layui-btn-xs">预览</a>' +
                                '<a onclick="" class="layui-btn layui-btn-xs">下载</a>';
                        },
                        width: 120
                    }]
                ],
                loadMsg: '正在加载数据...',
                height: 498,
                singleSelect: true,
                rownumbers: true,
                striped: true
            });
        }
    })
}
