var curInfo = {
	tableId: 'dataTable',
	tableType: '热敏电阻实施',
	rsId: parent.window.tjfxRsID,
	columns: [
		[{
			field: 'TJFX_ID',
			title: '统计分析ID',
			hidden: true
		}, {
			field: 'DATA_ID',
			title: 'MES DATAID',
			hidden: true
		}, {
			field: 'REF_DPID',
			title: '关联的数据包ID',
			hidden: true
		}, {
			field: 'MODELCODE',
			title: '型号代号',
			width: 100,
			align: 'center'
		}, {
			field: 'TESTPOINTNAME',
			title: '测温点名称',
			width: 120,
			align: 'center'
		}, {
			field: 'RESISTTYPE',
			title: '热敏电阻型号',
			width: 140,
			align: 'center'
		}, {
			field: 'DISTANDCODE',
			title: '遥测代号',
			width: 100,
			align: 'center'
		}, {
			field: 'HEATERCONTENT',
			title: '实测情况',
			width: 100,
			align: 'center'
		}, {
			field: 'ENVIRONMENTTEMP',
			title: '环境温度',
			width: 100,
			align: 'center'
		}, {
			field: 'CREATEDATE',
			title: '操作时间',
			width: 150,
			align: 'center'
		}]
	]
};
/** 重置搜索条件 */
var resetSearchCondition = function() {
	$('#testpointname').textbox('setValue', '');
	$('#resisttype').textbox('setValue', '');
	$('#distandcode').textbox('setValue', '');
	$('#heatercontent').textbox('setValue', '');
	$('#environmenttemp').textbox('setValue', '');
};

var getFieldValue = function() {
	var param = {};
	param.type = curInfo.tableType;
	//数据包的ID
	param.rsId = curInfo.rsId;
	var testpointname = $('#testpointname').textbox('getValue');
	var resisttype = $('#resisttype').textbox('getValue');
	var distandcode = $('#distandcode').textbox('getValue');
	var heatercontent = $('#heatercontent').textbox('getValue');
	var environmenttemp = $('#environmenttemp').textbox('getValue');
	param.conditionData = {
		testpointname: testpointname,
		resisttype: resisttype,
		distandcode: distandcode,
		heatercontent: heatercontent,
		environmenttemp: environmenttemp
	};
	return param;
};
