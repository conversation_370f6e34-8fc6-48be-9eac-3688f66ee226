function renderParam() {
	$('#' + paramId).datagrid({
		data: [],
		toolbar: "#paramTable_tb",
		fit: true,
		columns: [
			[{
					field: 'ck',
					checkbox: true
				}, {
					field: 'PARAM_NAME',
					title: '属性名称',
					width: 280,
					align: 'center'
				},
				{
					field: 'MES_NAME',
					title: 'MES对应属性名称',
					width: 180,
					align: 'center'
				},
				{
					field: 'THREE_AREA',
					title: '三级表<br>excel数据<br>单元格位置',
					width: 80,
					align: 'center'
				},
				{
					field: 'SECOND_AREA',
					title: '二级表<br>excel数据<br>单元格位置',
					width: 80,
					align: 'center'
				},
				{
					field: 'WIDTH',
					title: '二级表表<br>格列列宽',
					width: 66,
					align: 'center'
				},
				{
					field: 'FORMAT',
					title: '二级表格式化类型',
					width: 120,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '默认显示';
						} else if (value == '1') {
							return '序号';
						} else if (value == '2') {
							return '预览三级表';
						} else if (value == '3') {
							return '影像记录超链接';
						} else if (value == '4') {
							return '跟踪卡超链接';
						} else if (value == '5') {
							return '检验人员';
						} else if (value == '6') {
							return '检验日期';
						} else if (value == '7') {
							return '图片';
						} else if (value == '8') {
							return '热管编号图片';
						}
					}
				},
				{
					field: 'VALUE_TYPE',
					title: '值类型',
					width: 52,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '无';
						} else if (value == '1') {
							return '设计值';
						} else if (value == '2') {
							return '实测值';
						}
					}
				},
				{
					field: 'RELATION_PARAM',
					title: '关联属性',
					width: 280,
					align: 'center',
					formatter: function(value, row, index) {
						var params = $('#' + paramId).datagrid('getData').rows;
						var text = "";
						for (var i = 0; i < params.length; i++) {
							if (value == params[i]['ID']) {
								text = params[i]['PARAM_NAME'];
								break;
							}
						}
						return text;
					}
				},
				{
					field: 'IS_SORT',
					title: '是否根据<br>此列排序',
					width: 66,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '否';
						} else if (value == '1') {
							return '<span style = "color:#1e9fff;">是</span>';
						}
					}
				},
				{
					field: 'IS_REMOVE_REPEAT',
					title: '是否<br>根据此列<br>去重更新',
					width: 66,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '否';
						} else if (value == '1') {
							return '<span style = "color:#1e9fff;">是</span>';
						}
					}
				},
				{
					field: 'INTERFACE_NAME',
					title: '接口属性',
					width: 66,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == 'OBJECT_NAME') {
							return '名称';
						} else if (value == 'OBJECT_CODE') {
							return '编号';
						} else if (value == 'OBJECT_BATCH') {
							return '批次号';
						} else {
							return '/';
						}
					}
				},
				{
					field: 'IS_BASE',
					title: '是否为<br>基本信息',
					width: 66,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '否';
						} else if (value == '1') {
							return '<span style = "color:#1e9fff;">是</span>';
						}
					}
				},
				{
					field: 'IS_INDEX',
					title: '是否为<br>关键指标',
					width: 66,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '否';
						} else if (value == '1') {
							return '<span style = "color:#1e9fff;">是</span>';
						}
					}
				},
				{
					field: 'IS_QUERY',
					title: '是否作为<br>查询条件',
					width: 66,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '否';
						} else if (value == '1') {
							return '<span style = "color:#1e9fff;">是</span>';
						}
					}
				},
				{
					field: 'IS_SHOW_IN_360',
					title: '是否在<br>全景图<br>中显示',
					width: 50,
					align: 'center',
					formatter: function(value, row, index) {
						if (value == '0') {
							return '否';
						} else if (value == '1') {
							return '<span style = "color:#1e9fff;">是</span>';
						}
					}
				},
				{
					field: 'TABLE_ID',
					hidden: true
				}
			]
		],
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>',
		loadMsg: '正在加载数据...',
		rownumbers: true,
		striped: true,
		onDblClickRow: function(index, row) {
			editParamRow(row);
		}
	});
}


//增加属性信息
function addParam() {
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		layer.alert('请选择一个模板！', {
			icon: 2
		});
		return false;
	}
	var tableData = datas[0];
	var type = tableData.TYPE;
	var tableName = tableData.TABLE_NAME;
	var paramAddIndex = layer.open({
		title: '新增属性',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['500px', paramLayerH],
		content: '<div id="addParamContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['新增', '重置', '关闭'],
		yes: function() {
			$('#btn_param_add').click();
		},
		btn2: function() {
			$('#btn_param_reset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function() {
			var addTpl = $("#paramHtml")[0].innerHTML;
			$("#addParamContent").append(addTpl);
			relationParamRender();
		}
	});
	form.render(null, 'paramForm');
	controlParamInputShow(type, paramAddIndex);
	//监听提交
	form.on('submit(paramFormAdd)', function(data) {
		var param = data.field;
		param.TABLE_ID = tableData.ID;
		param.tableName = tableName;
		param.creator = 'admin';
		//添加成功的弹窗
		var cb_success = function(data) {
			//新增完成后需要刷新界面
			//提示完成后，点击确定再刷新界面
			layer.closeAll();
			loadParamTable(tableData.TREE_NAME);
			layer.msg('新增成功');
		};
		//添加失败的弹窗
		var cb_error = function(xhr) {
			layer.alert('新增失败!', {
				icon: 2
			});
		};
		//同步新增
		twxAjax(THING, "AddParam", param, false, cb_success, cb_error);
		return false;
	});
}

//编辑属性信息
function editParam() {
	//获取选中的数据
	var datas = $('#' + paramId).datagrid('getSelections');
	if (datas.length == 0) {
		//提示用户，请选择待编辑的数据
		layer.alert('请选择待编辑数据...', {
			icon: 2
		});
		return;
	} else if (datas.length > 1) {
		layer.alert('无法进行批量编辑...', {
			icon: 2
		});
		return;
	}
	var selectedData = datas[0];
	editParamRow(selectedData);
}

//删除属性信息
function deleteParam() {

	var tableData = $('#' + tableId).datagrid('getSelections')[0];
	//获取选中的数据
	var datas = $('#' + paramId).datagrid('getSelections');
	if (datas.length == 0) {
		//提示用户，请选择待编辑的数据
		layer.alert('请选择需要删除的数据...', {
			icon: 2
		});
		return;
	}
	layer.confirm('是否确认删除选中数据?', {
		icon: 3,
		title: '删除提示',
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200
	}, function(index) {
		layer.confirm('删除属性会删除这一列的所有数据，谨慎操作，是否继续？', {
			icon: 3,
			title: '删除提示',
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		}, function(index) {
			var row = datas[0];
			var ids = '';
			for (var i = 0; i < datas.length; i++) {
				ids += ',' + datas[i].ID;
			}
			if (ids !== '') {
				ids = ids.substring(1);
			}
			var cb_success = function(data) {
				layer.closeAll();
				loadParamTable(tableData.TREE_NAME);
				layer.msg('删除成功');
			};
			var cb_error = function(xhr) {
				layer.alert('删除失败', {
					icon: 2
				});
			};
			twxAjax(THING, "DeleteParam", {
				ids: ids
			}, false, cb_success, cb_error);
		});
	});
}

//编辑一行属性
function editParamRow(selectedData) {
	var tableData = $('#' + tableId).datagrid('getSelections')[0];
	var type = tableData.TYPE;
	var paramEditIndex = layer.open({
		title: '编辑属性',
		type: 1,
		shadeClose: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['500px', paramLayerH],
		content: '<div id="editParamContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['更新', '重置', '关闭'],
		yes: function() {
			$('#btn_param_update').click();
		},
		btn2: function() {
			$('#btn_param_reset').click();
			form.val("paramForm", selectedData);
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function() {
			var editTpl = $("#paramHtml")[0].innerHTML;
			$("#editParamContent").append(editTpl);
			relationParamRender(selectedData['ID']);
		}
	});

	form.render(null, 'paramForm');
	controlParamInputShow(type, paramEditIndex);
	form.val("paramForm", selectedData);
	controlRelationParamDiv(selectedData['VALUE_TYPE'], paramEditIndex);
	controlRepeatParamDiv(selectedData['IS_REMOVE_REPEAT'] == 1, paramEditIndex);
	//监听提交
	form.on('submit(paramFormUpdate)', function(data) {
		var param = data.field;
		param.modifier = 'admin';
		var cb_success = function(data) {
			layer.closeAll();
			loadParamTable(tableData.TREE_NAME);
			layer.msg('修改成功');
		};
		//添加失败的弹窗
		var cb_error = function(xhr) {
			layer.alert('修改失败!', {
				icon: 2
			});
		};
		//同步新增
		twxAjax(THING, "UpdateParam", param, false, cb_success, cb_error);
		return false;
	});
}

//加载属性表数据
function loadParamTable(name) {
	$('#' + paramId).datagrid('loading');
	var cb_success = function(data) {
		$('#' + paramId).datagrid('loadData', data.rows);
		$('#' + paramId).datagrid('loaded');
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		if (xhr.status !== 200) {
			layer.msg('后台错误，请联系管理员!', {
				icon: 2,
				anim: 6
			});
		}
	};
	twxAjax(THING, "QueryParams", {
		tree_name: name
	}, true, cb_success, cb_error);
}