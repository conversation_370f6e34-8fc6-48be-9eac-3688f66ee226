<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <link rel="Shortcut Icon" href="../../img/favicon.ico" />
        <link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all" />

        <link rel="stylesheet" href="../../css/icon.css" />

        <link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css" />
        <link rel="stylesheet" href="../../plugins/preview/preview.css" />
        <link rel="stylesheet" type="text/css" href="../../plugins/webuploader/webuploader.css" />

        <link href="../../plugins/handson/handsontable.full.min.css" rel="stylesheet" media="screen" />

        <script src="../../plugins/layui-lasted/layui.js"></script>
        <script src="../../plugins/easyui/jquery.min.js"></script>
        <script src="../../plugins/preview/preview.js"></script>
        <script src="../../plugins/preview/jquery.rotate.min.js"></script>

        <script src="../../plugins/easyui/jq-signature.js"></script>
        <script src="../../plugins/easyui/jquery.easyui.min.js"></script>
        <script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
        <script type="text/javascript" src="../../plugins/webuploader/webuploader.min.js"></script>

        <script src="../js/config/twxconfig.js"></script>
        <script src="../js/util.js"></script>

        <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css" />
        <link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css" />
        <script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
        <script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>

        <script src="../../plugins/handson/handsontable.full.js"></script>
        <script src="../../plugins/handson/languages/all.js"></script>
        <script src="../js/js-NSV.js"></script>
        <script src="../js/sign.js"></script>
        <script type="text/javascript" src="../js/intercept.js"></script>
        <script type="text/javascript" src="../js/logUtil.js"></script>
        <link rel="stylesheet" type="text/css" href="../../css/HotStyle.css" />
        <title>发射场确认</title>
    </head>
    <body onload="initPlugin()">
        <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
        <!--[if lt IE 9]>
            <script src="../../plugins/html5/html5.min.js"></script>
            <script src="../../plugins/html5/respond.min.js"></script>
        <![endif]-->
        <div id="root_layout" class="easyui-layout" style="width: 100%; height: 100%" data-options="fit:true">
            <div id="p" data-options="region:'west',split:true,tools:'#tt'" title="结构树" style="width: 530px; padding: 10px">
                <div class="layui-row layui-hide">
                    <button type="button" style="margin-left: 10px; margin-bottom: 0px" class="layui-btn layui-btn-xs layui-btn-primary" id="expandAll"><i class="layui-icon">&#xe624;</i> 展开全部</button>
                    <button type="button" style="margin-left: 10px; margin-bottom: 0px" class="layui-btn layui-btn-xs layui-btn-primary" id="collapseAll"><i class="layui-icon">&#xe67e;</i> 收缩全部</button>
                </div>
                <ul id="dpTree" class="ztree"></ul>
            </div>
            <div id="tt">
                <a href="#" class="download" title="文件下载列表" onclick="javascript:ejectDownloadTable()"></a>
                <a href="#" class="upload" title="分级承诺映射模板" onclick="javascript:showTemplateLayer()"></a>
            </div>
            <div data-options="region:'center'" title="表格">
                <div id="msg" style="float: left; width: 100%; color: red; padding-left: 15px; padding-top: 15px">请选择A表或者B表！</div>
                <div id="tbr" class="layui-row">
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-normal" id="eidt-table"><i class="layui-icon">&#xe642;</i> 编辑表格</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm" id="table-header"><i class="layui-icon">&#xe62d;</i> 设置表头行</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-mycolor4" id="export-pdf"><i class="layui-icon">&#xe66d;</i> 导出PDF</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-mycolor8" id="import-pdf"><i class="layui-icon">&#xe62f;</i> 导入PDF</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-mycolor3" id="export-excel"><i class="layui-icon">&#xe66d;</i> 导出Excel</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-mycolor6" id="export-img"><i class="layui-icon">&#xe66d;</i> 下载所有照片</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-mycolor5" id="import-excel"><i class="layui-icon">&#xe9aa;</i> 导入Excel</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-warm" func="func-launch-lock-table" id="confirm-table"><i class="layui-icon">&#xe679;</i> 锁定</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm layui-btn-danger" func="func-launch-clear-sign" id="clear-sign"><i class="layui-icon">&#x1007;</i> 清除签名</button>
                    <button type="button" style="margin-left: 10px; margin-top: 10px; margin-bottom: 10px" class="layui-btn layui-btn-sm " id="push-sign"><i class="layui-icon">&#xe609;</i> 推送签署</button>
                    <div id="table-security" class="table-security"></div>
                </div>
                <div id="table" style="display: none; float: left; width: 100%; overflow: auto; padding: 0px 15px"></div>
            </div>
        </div>
    </body>
</html>
<script>
    handleFuncBtn();
</script>
<script src="../../plugins/index/jquery.fileDownload.js"></script>
<script src="launchConfirm.js"></script>
<script src="../js/aitTree.js"></script>
<script src="../js/onlineTree.js"></script>
<script src="../js/UDS.js"></script>

<script src="../js/HotUtil/HotUtil.Core.js"></script>
<script src="../js/HotUtil/HotUtil.HtmlTable.js"></script>
<script src="../js/HotUtil/HotUtil.HtmlContextMenu.js"></script>
<script src="../js/HotUtil/HotUtil.Actions.js"></script>
<script src="../js/HotUtil/HotUtil.Content.js"></script>
<!-- ... 加载所有其他 HotUtil 模块 ... -->

<!-- 替换 HotUtil.Editor.js 的新模块，请注意加载顺序 -->
<!-- 1. 加载底层工具和样式 -->
<script src="../js/HotUtil/HotUtil.Editor.Utils.js"></script>
<script src="../js/HotUtil/HotUtil.Editor.Style.js"></script>
<!-- 2. 加载依赖工具的模块 -->
<script src="../js/HotUtil/HotUtil.Editor.Save.js"></script>
<script src="../js/HotUtil/HotUtil.Editor.ContextMenu.js"></script>
<!-- 3. 加载核心渲染模块 -->
<script src="../js/HotUtil/HotUtil.Editor.Render.js"></script>
<!-- 4. 最后加载顶层控制模块 -->
<script src="../js/HotUtil/HotUtil.Editor.Core.js"></script>

<script src="../js/HotUtil/HotUtil.Signatory.js"></script>
<script src="../js/HotUtil/HotUtil.TreeNode.js"></script>
<script src="../js/HotUtil/HotUtil.IO.js"></script>
<script src="../js/HotUtil/HotUtil.Download.js"></script>

<script src="../js/pdfExportDialog.js"></script>
<script src="../js/onlineConfirm.js"></script>
<script src="../js/signPushDialog.js"></script>

<script type="text/html" id="uploadHtml">
    <form class="layui-form" lay-filter="uploadForm">
        <div class="layui-form-item">
            <label class="fieldlabel layui-form-label">文件内容:</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
                    <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="selectedFile" style="display: none;">
            <label class="fieldlabel layui-form-label">已选文件:</label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
            </div>
        </div>
        <div class="layui-form-item" style="display:none;">
            <center>
                <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
                <button id="btn_cancel" class="layui-btn">取消</button>
            </center>
        </div>
    </form>
</script>

<script type="text/html" id="uploadMappingHtml">
    <form class="layui-form" lay-filter="uploadForm">
        <div class="layui-form-item">
            <label class="param-lable layui-form-label" style="width: 120px;">模板下载:</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-normal" id="downloadTpl">点击下载</button>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="param-lable layui-form-label" style="width: 120px;">文件内容:</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
                    <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="selectedFile" style="display: none;">
            <label class="param-lable layui-form-label" style="width: 120px;">已选文件:</label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux" style="width: max-content" id="selectedFileName"></div>
            </div>
        </div>
        <div class="layui-form-item" style="display:none;">
            <center>
                <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
                <button id="btn_cancel" class="layui-btn">取消</button>
            </center>
        </div>
    </form>
</script>
