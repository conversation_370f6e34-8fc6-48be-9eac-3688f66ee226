//测试产品结构树的对象
var ProductTree = function() {
	var othis = this;
	var curDragNodes;
	this.ztreeObj = {};
	this.treeId = 'tree';
	/**
	 * 处理产品结构树的显示图标以及是否允许拖拽
	 * @param {Object} datas
	 */
	this.dealDatas = function(datas) {
		var imagePrefix = 'img';
		for (var i = 0; i < datas.length; i++) {
			var dtype = datas[i].TYPE_;
			if (dtype === 'root') {
				datas[i].drag = false;
				datas[i].icon = imagePrefix + "/root.png";
			} else if (dtype === 'folder') {
				datas[i].drag = false;
				datas[i].dropInner = false;
				datas[i].childOuter = false;
				datas[i].icon = imagePrefix + "/folder.png";
			} else if (dtype === 'model') {
				datas[i].drag = true;
				datas[i].childOuter = false;
				datas[i].dropInner = true;
				datas[i].icon = imagePrefix + "/model.png";
			} else if (dtype === 'subsystem') {
				datas[i].childOuter = false;
				datas[i].dropInner = true;
				datas[i].icon = imagePrefix + "/subsystem.png";
			} else if (dtype === 'product') {
				datas[i].dropInner = false;
				datas[i].icon = imagePrefix + "/product.png";
			}

		}
		return datas;
	};
	this.treeSetting = {
		view: {
			dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
			showLine: true, //是否显示节点之间的连线
			fontCss: {
				'color': 'black'
			}, //字体样式函数
			selectedMulti: false, //设置是否允许同时选中多个节点,
			txtSelectedEnable: true,
			showTitle: true
		},
		async: {
			enable: true,
			url: getTreeUrl(THING, "QueryTreeById", ""),
			type: "post",
			autoParam: ["ID_"],
			contentType: "application/json;charset=utf-8",
			dataType: 'json',
			dataFilter: function(treeId, parentNode, responseData) {
				if (responseData.success) {
					var datas = responseData.data;
					if (datas.length > 0) {
						datas = othis.dealDatas(datas);
					}
					return datas;
				} else {
					layer.alert(responseData.msg, {
						icon: 2
					});
				}
			}
		},
		check: {
			chkboxType: {
				"Y": "",
				"N": ""
			},
			chkStyle: "checkbox", //复选框类型
			enable: false //每个节点上是否显示 CheckBox
		},
		edit: {
			enable: true,
			editNameSelectAll: false,
			showRemoveBtn: false,
			showRenameBtn: false,
			drag: {
				autoExpandTrigger: true,
				prev: function(treeId, nodes, targetNode) {
					var pNode = targetNode.getParentNode();
					if (pNode && pNode.dropInner === false) {
						return false;
					} else if (nodes[0].LEVEL_ !== targetNode.LEVEL_) {
						return false;
					} else {
						for (var i = 0, l = curDragNodes.length; i < l; i++) {
							var curPNode = curDragNodes[i].getParentNode();
							if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
								return false;
							}
						}
					}
					return true;
				},
				next: function(treeId, nodes, targetNode) {
					var pNode = targetNode.getParentNode();
					if (pNode && pNode.dropInner === false) {
						return false;
					} else if (nodes[0].LEVEL_ !== targetNode.LEVEL_) {
						return false;
					} else {
						for (var i = 0, l = curDragNodes.length; i < l; i++) {
							var curPNode = curDragNodes[i].getParentNode();
							if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
								return false;
							}
						}
					}
					return true;
				},
				inner: function(treeId, nodes, targetNode) {
					if (targetNode && targetNode.dropInner === false) {
						return false;
					} else if (nodes[0].LEVEL_ <= targetNode.LEVEL_) {
						return false;
					} else {
						for (var i = 0, l = curDragNodes.length; i < l; i++) {
							if (!targetNode && curDragNodes[i].dropRoot === false) {
								return false;
							} else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
								.childOuter === false) {
								return false;
							}
						}
					}
					return true;
				}
			}
		},
		data: {
			simpleData: { //简单数据模式
				enable: true,
				idKey: "ID_",
				pIdKey: "PID_",
				rootPId: -1
			},
			key: {
				name: 'NAME_',
				title: 'NAME_',
				isParent: "ISPARENT"
			}
		},
		callback: {
			beforeDrag: function(treeId, treeNodes) {
				for (var i = 0, l = treeNodes.length; i < l; i++) {
					if (treeNodes[i].drag === false) {
						curDragNodes = null;
						return false;
					} else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
						curDragNodes = null;
						return false;
					}
				}
				curDragNodes = treeNodes;
				return true;
			},
			beforeDrop: function(treeId, treeNodes, targetNode, moveType, isCopy) {
				return true;
			},
			beforeDragOpen: function(treeId, treeNode) {
				autoExpandNode = treeNode;
				return true;
			},
			onDrag: function(event, treeId, treeNodes) {},
			onDrop: function(event, treeId, treeNodes, targetNode, moveType, isCopy) {
				if (targetNode != null) {
					var sourceNodeSort = treeNodes[0].SORT_;
					var sourceNodeId = treeNodes[0].ID_;
					var sourceNodeName = treeNodes[0].NAME_;
					var sourceNodeType = treeNodes[0].TYPE_;

					var targetNodeSort = targetNode.SORT_;
					var targetNodeId = targetNode.ID_;
					var targetNodeName = targetNode.NAME_;

					var parentNode = treeNodes[0].getParentNode();
					var allNode = parentNode.children;
					var arr = [];
					for (var i = 1; i <= allNode.length; i++) {
						arr.push(allNode[i - 1].ID_ + ":" + i);
					}
					var str = arr.join(",");
					twxAjax(THING, 'UpdateTreeNodeSort', {
						str: str
					}, true, function(res) {
						othis.reloadTree(parentNode.ID_, sourceNodeId);
					}, function(res) {
						othis.reloadTree(parentNode.ID_, sourceNodeId);
					});
				}
			},
			onClick: function(event, treeId, treeNode) {
				if (treeNode.TYPE_ == 'product') {
					renderFileTable(treeNode.ID_, true);
				} else {
					renderFileTable(treeNode.ID_, false);
				}

			},
			onExpand: function(event, treeId, treeNode) {
				othis.loadTreeMenu();
			}
		}
	};
	//加载树结构
	this.loadTree = function() {
		var cb_success = function(res) {
			if (res.success) {
				var datas = res.data;
				if (datas.length > 0) {
					datas = othis.dealDatas(datas);
					othis.ztreeObj = $.fn.zTree.init($("#" + othis.treeId), othis.treeSetting, datas);
					var nodes = othis.ztreeObj.getNodes();
					for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
						othis.ztreeObj.expandNode(nodes[i], true, false, true);
					}
					othis.loadTreeMenu();
				}
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		//使用ajax进行异步加载Tree
		twxAjax(THING, 'QueryTreeRoot', '', true, cb_success);
	};
	//加载树节点右键菜单
	this.loadTreeMenu = function() {
		$("#" + othis.treeId + " a").each(function(i, n) {
			var menu = [];
			var node = othis.ztreeObj.getNodeByTId($(n).parent().attr("id"));

			menu = othis.getNodeMenu(node);
			if (menu.length != 0) {
				$(n).contextMenu({
					width: 115,
					menu: menu,
					target: function(ele) {
						var node = othis.ztreeObj.getNodeByTId($(ele).parent().attr("id"));
						othis.ztreeObj.selectNode(node, false, true);
					}
				});
			}
		});
	};
	//操作完节点之后重新加载节点
	this.reloadTree = function(refrushId, selId) {
		if (selId) {

		} else {
			selId = refrushId;
		}
		var refrushTreeNode = othis.ztreeObj.getNodeByParam("ID_", refrushId, null);
		if (!refrushTreeNode.ISPARENT) {
			refrushTreeNode.ISPARENT = true;
			othis.ztreeObj.updateNode(refrushTreeNode);
		}
		othis.ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false,
			function() {
				othis.ztreeObj.expandNode(refrushTreeNode, true, false, true);
				var newSelNode = othis.ztreeObj.getNodeByParam("ID_", selId, null);
				othis.ztreeObj.selectNode(newSelNode, false, true);
				othis.loadTreeMenu();
			});
	}
	//获取节点右键菜单数组
	this.getNodeMenu = function(treeNode) {
		var imgSuffix = '../dataTree/';
		var menu = [];
		var addName = "",
			editName = "";
		if (treeNode.TYPE_ == 'root') {
			addName = "型号";
		} else if (treeNode.TYPE_ == 'model') {
			addName = "分系统";
			editName = "型号";
		} else if (treeNode.TYPE_ == 'subsystem') {
			addName = "产品";
			editName = "分系统";
		} else if (treeNode.TYPE_ == 'product') {
			editName = "产品";
		}
		var addNodeMenu = {
			text: "添加" + addName,
			icon: imgSuffix + 'images/add.png',
			callback: function() {
				var parentId = treeNode.ID_;
				layer.open({
					title: "添加" + addName,
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					// fixed: false,
					maxmin: false,
					resize: false, //不允许拉伸
					area: ["450px", "185px"],
					content: '<div id="addNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["保存", "重置", "取消"],
					yes: function() {
						$("#addNodeSubmit").click();
					},
					btn2: function() {
						$("#addNodeReset").click();
						return false;
					},
					btn3: function() {
						return true;
					},
					success: function() {
						var addTpl = '<form class="layui-form" lay-filter="addNodeForm">\
										<div class="layui-form-item">\
											<label class="layui-form-label" style="">' + addName + '名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="NAME_" id="NAME_" lay-verify="required" required autocomplete="off" placeholder="请输入' + addName + '名称" class="layui-input">\
											</div>\
										</div>\
										<div class="layui-form-item" style="display:none;">\
											<button id="addNodeSubmit" lay-submit lay-filter="addNodeSubmit">保存</button>\
											<button id="addNodeReset" type="reset">重置</button>\
										</div>\
									</form>';
						$("#addNodeContent").append(addTpl);
						form.render(null, "addNodeForm");

						//监听提交
						form.on("submit(addNodeSubmit)", function(data) {
							var cb_success = function(res) {
								if (res.success) {
									layer.closeAll();
									layer.msg(res.msg);
									othis.reloadTree(treeNode.ID_);
								} else {
									layer.alert(res.msg, {
										icon: 2,
									});
								}
							};
							var cb_error = function(xhr) {
								layer.alert("新增失败!", {
									icon: 2,
								});
							};
							twxAjax(THING, "AddTreeNode", {
								pId: parentId,
								name: data.field.NAME_,
								creator: sessionStorage.getItem('username')
							}, true, cb_success, cb_error);
							return false;
						});
					}
				});
			}
		};
		var editNodeMenu = {
			text: "修改" + editName,
			icon: imgSuffix + 'images/edit.png',
			callback: function() {
				var id = treeNode.ID_;
				var name = treeNode.NAME_;

				layer.open({
					title: "修改" + editName,
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					// fixed: false,
					maxmin: false,
					resize: false, //不允许拉伸
					area: ["450px", "185px"],
					content: '<div id="editNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["保存", "重置", "取消"],
					yes: function() {
						$("#editNodeSubmit").click();
					},
					btn2: function() {
						$("#editNodeReset").click();
						return false;
					},
					btn3: function() {
						return true;
					},
					success: function() {
						var editTpl = '<form class="layui-form" lay-filter="editNodeForm">\
										<div class="layui-form-item">\
											<label class="layui-form-label" style="">' + editName + '名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="NAME_" id="NAME_" value="' + name + '" lay-verify="required" required autocomplete="off" placeholder="请输入' + editName + '名称" class="layui-input">\
											</div>\
										</div>\
										<div class="layui-form-item" style="display:none;">\
											<button id="editNodeSubmit" lay-submit lay-filter="editNodeSubmit">保存</button>\
											<button id="editNodeReset" type="reset">重置</button>\
										</div>\
									</form>';
						$("#editNodeContent").append(editTpl);
						form.render(null, "editNodeForm");

						//监听提交
						form.on("submit(editNodeSubmit)", function(data) {
							var cb_success = function(res) {
								if (res.success) {
									layer.closeAll();
									layer.msg(res.msg);
									othis.reloadTree(treeNode.PID_, treeNode.ID_);
								} else {
									layer.alert(res.msg, {
										icon: 2,
									});
								}
							};
							var cb_error = function(xhr) {
								layer.alert("更新请求失败!", {
									icon: 2,
								});
							};
							twxAjax(THING, "UpdateTreeNode", {
								id: id,
								name: data.field.NAME_
							}, true, cb_success, cb_error);
							return false;
						});
					}
				});
			}
		};
		var deleteNodeMenu = {
			text: "删除" + editName,
			icon: imgSuffix + 'images/remove.png',
			callback: function() {
				var msg = "确认删除 " + editName + " -- " + treeNode.NAME_ + " 吗？";
				if (treeNode.ISPARENT) {
					msg = "该节点下有子节点，会一并删除，确认删除吗?"
				}
				layer.confirm(msg, {
					icon: 3,
					title: '提示'
				}, function(index) {
					var cb_success = function(data) {
						if (data.success) {
							layer.msg(data.msg);
							othis.reloadTree(treeNode.PID_);
						} else {
							layer.alert(data.msg, {
								icon: 2
							});
						}
					};
					var cb_error = function() {
						layer.msg('删除失败！');
					}
					twxAjax(THING, 'DeleteTreeNode', {
						id: treeNode.ID_
					}, true, cb_success, cb_error);

				});

			}
		};
		var copyNodeMenu = {
			text: "复制" + editName,
			icon: imgSuffix + 'images/copy.png',
			callback: function() {
				layer.open({
					title: "复制" + editName,
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					// fixed: false,
					maxmin: false,
					resize: false,
					area: ["450px", "185px"],
					content: '<div id="copyNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["复制", "重置", "取消"],
					yes: function() {
						$("#copyNodeSubmit").click();
					},
					btn2: function() {
						$("#copyNodeReset").click();
						return false;
					},
					btn3: function() {
						return true;
					},
					success: function() {
						var addTpl = '<form class="layui-form" lay-filter="copyNodeForm">\
										<div class="layui-form-item">\
											<label class="layui-form-label" style="">' + editName + '名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="NAME_" id="NAME_" lay-verify="required" required autocomplete="off" placeholder="请输入' + editName + '名称" class="layui-input">\
											</div>\
										</div>\
										<div class="layui-form-item" style="display:none;">\
											<button id="copyNodeSubmit" lay-submit lay-filter="copyNodeSubmit">保存</button>\
											<button id="copyNodeReset" type="reset">重置</button>\
										</div>\
									</form>';
						$("#copyNodeContent").append(addTpl);
						form.render(null, "copyNodeForm");

						//监听提交
						form.on("submit(copyNodeSubmit)", function(data) {
							var cb_success = function(res) {
								if (res.success) {
									layer.closeAll();
									layer.msg(res.msg);
									othis.reloadTree(treeNode.PID_);
								} else {
									layer.alert(res.msg, {
										icon: 2,
									});
								}
							};
							var cb_error = function(xhr) {
								layer.alert("复制失败!", {
									icon: 2,
								});
							};
							twxAjax(THING, "CopyTreeNode", {
								id: treeNode.ID_,
								name: data.field.NAME_,
								creator: sessionStorage.getItem('username')
							}, true, cb_success, cb_error);
							return false;
						});
					}
				});
			}
		};

		var exportNodeMenu = {
			text: "导出数据包",
			icon: imgSuffix + 'images/file-export.png',
			callback: function() {
				exportDataPackage(treeNode.ID_);
			}
		};

		var nodeType = treeNode.TYPE_;
		if (nodeType == 'root') {
			menu.push(addNodeMenu);
		} else if (nodeType == 'model') {
			menu.push(addNodeMenu);
			menu.push(editNodeMenu);
			menu.push(deleteNodeMenu);
			menu.push(copyNodeMenu);
			menu.push(exportNodeMenu);
		} else if (nodeType == 'subsystem') {
			menu.push(addNodeMenu);
			menu.push(editNodeMenu);
			menu.push(deleteNodeMenu);
			menu.push(copyNodeMenu);
			menu.push(exportNodeMenu);
		} else if (nodeType == 'product') {
			menu.push(editNodeMenu);
			menu.push(deleteNodeMenu);
			menu.push(copyNodeMenu);
			menu.push(exportNodeMenu);
		}

		return menu;
	}
	othis.loadTree();
};