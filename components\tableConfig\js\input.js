//控制输入框的显示
var controlTableInputShow = function(type, index) {
	if (type == '3' || type == '6') {
		$('#div_second_data_rownum').parent().hide();
		$('#second_data_rownum').attr("lay-verify", "");
		$('#second_data_rownum').val('');

		$('#div_three_data_rownum').parent().hide();
		$('#three_data_rownum').attr("lay-verify", "");
		$('#three_data_rownum').val('');

		$('#div_plan_start').parent().hide();
		$('#plan_start_colindex').attr("lay-verify", "");
		$('#plan_start_colindex').val('');

		$('#div_plan_end').parent().hide();
		$('#plan_end_colindex').attr("lay-verify", "");
		$('#plan_end_colindex').val('');

		$('#div_photo_start').parent().hide();
		$('#photo_start_colindex').attr("lay-verify", "");
		$('#photo_start_colindex').val('');

		$('#div_photo_end').parent().hide();
		$('#photo_end_colindex').attr("lay-verify", "");
		$('#photo_end_colindex').val('');

		layer.style(index, {
			height: tableLayerH2
		});
	} else {
		$('#div_second_data_rownum').parent().show();
		$('#second_data_rownum').attr("lay-verify", "number");

		$('#div_plan_start').parent().show();
		$('#plan_start_colindex').attr("lay-verify", "number");

		$('#div_plan_end').parent().show();
		$('#plan_end_colindex').attr("lay-verify", "number");

		$('#div_photo_start').parent().show();
		$('#photo_start_colindex').attr("lay-verify", "number");

		$('#div_photo_end').parent().show();
		$('#photo_end_colindex').attr("lay-verify", "number");

		if (type == '5') {
			$('#div_three_data_rownum').show();
			$('#three_data_rownum').attr("lay-verify", "number");
			layer.style(index, {
				height: tableLayerH1
			});
		} else {
			$('#div_three_data_rownum').hide();
			$('#three_data_rownum').attr("lay-verify", "");
			$('#three_data_rownum').val('');
			layer.style(index, {
				height: tableLayerH
			});
		}

	}
}

//控制关联属性下拉框的显示  vauleType：值类型  设计值或者实测值
var controlRelationParamDiv = function(vauleType, index) {
	if (vauleType != 0) {
		if (vauleType == 1) {
			$("#relationParamDiv").find('label').text("关联实测属性:");
			$("#indexDiv").addClass("layui-hide");
			$("input[name='IS_INDEX']")[0].checked = false;
			layer.style(index, {
				height: paramLayerH1
			});
		} else {
			//显示关键指标
			$("#indexDiv").removeClass("layui-hide");
			layer.style(index, {
				height: paramLayerH3
			});
			$("#relationParamDiv").find('label').text("关联设计属性:");
		}
		$("#relationParamDiv").removeClass("layui-hide");
		$("#relationParamDiv").find('select').attr("lay-verify", "required");
	} else {
		layer.style(index, {
			height: paramLayerH
		});
		$("#indexDiv").addClass("layui-hide");
		$("input[name='IS_INDEX']")[0].checked = false;
		$("#relationParamDiv").addClass("layui-hide");
		$("#relationParamDiv").find('select').attr("lay-verify", "");
	}
	form.render('checkbox');
}

//控制是否去重 类型的显示
var controlRepeatParamDiv = function(vauleType, index) {
	if (vauleType) {
		$("#INTERFACE_NAME_DIV").removeClass("layui-hide");
		layer.style(index, {
			width: "630px"
		});
	} else {
		$("#INTERFACE_NAME_DIV").addClass("layui-hide");
		$("#INTERFACE_NAME").val('');
		form.render();
		layer.style(index, {
			width: "500px"
		});
	}
}

//加载关联属性下拉框
function relationParamRender(selectId) {
	var paramData = $('#' + paramId).datagrid('getData').rows;
	var $select = $("#relationParam");
	$select.empty();
	$select.append('<option value="0"></option>');
	for (var i = 0; i < paramData.length; i++) {
		var param = paramData[i];
		if (selectId != param['ID']) {
			$select.append('<option value="' + param['ID'] + '">' + param['PARAM_NAME'] + '</option>');
		}
	}
}


//控制属性列表输入框的显示
var controlParamInputShow = function(type, index) {
	// if (type == '1') {
	// 	$('#div_three_area').hide();
	// 	layer.style(index, {
	// 		height: '380px'
	// 	});
	// } else 
	if (type == '2') {
		$('#div_three_area').show();
		layer.style(index, {
			height: paramLayerH
		});
	} else if (type == '4') {
		$('#div_three_area').hide();
		layer.style(index, {
			height: paramLayerH2
		});
	}
	form.on('radio(value-type)', function(data) {
		controlRelationParamDiv(data.value, index);
	});

	form.on('switch(IS_REMOVE_REPEAT)', function(data) {
		controlRepeatParamDiv(data.elem.checked, index);
	});
}