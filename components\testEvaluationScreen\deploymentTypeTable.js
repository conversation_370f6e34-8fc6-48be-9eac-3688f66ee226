/**
 * 列装定型表格模块
 * 负责处理列装定型统计数据的展示
 */

// 绘制列装定型表格
function drawDeploymentTypeTable(data) {
    var tbody = $('.deployment-type-table tbody');
    var thead = $('.deployment-type-table thead');

    // 显示表格和分隔线，确保它们可见
    $('.deployment-type-table').show();
    $('.table-divider').show();

    // 清空现有内容
    tbody.empty();
    thead.empty();

    // 使用后端返回的完整表格数据
    var headers = data.headers;
    var rows = data.rows;

    // 添加表头
    var headerRow = $('<tr>');
    headers.forEach(function (header) {
        var th = $('<th>');
        if (header !== '列装定型') {
            var headerContent;
            if (header.length > 5) {
                // 将长表头分成两行显示，使用特殊的类名
                headerContent = $('<div>', {
                    class: 'header-content deployment-header'
                });

                // 创建内部容器来保持垂直居中
                var innerContainer = $('<div>', {
                    class: 'deployment-header-container'
                });

                // 添加第一行和第二行
                innerContainer.append(
                    $('<div>', { text: header.substring(0, 5), class: 'deployment-header-line' }),
                    $('<div>', { text: header.substring(5), class: 'deployment-header-line' })
                );

                headerContent.append(innerContainer);
            } else {
                headerContent = $('<div>', {
                    class: 'header-content deployment-header',
                    text: header
                });
            }
            th.append(headerContent);
        } else {
            th.text(header);
        }
        headerRow.append(th);
    });
    thead.append(headerRow);

    // 添加数据行
    rows.forEach(function (rowData, i) {
        var tr = $('<tr>');
        tr.append($('<td>').text(rowData.name));
        
        // 检查之前是否有非零值
        var hasNonZeroBefore = function(values, endIndex) {
            for (var k = 0; k < endIndex; k++) {
                if (values[k] > 0) {
                    return true;
                }
            }
            return false;
        };
        
        // 检查后续是否有非零值
        var hasNonZeroAfter = function(values, startIndex) {
            for (var k = startIndex + 1; k < values.length; k++) {
                if (values[k] > 0) {
                    return true;
                }
            }
            return false;
        };
        
        // 添加每一列的数据
        rowData.values.forEach(function (value, j) {
            var td = $('<td>');
            if (value > 0) {
                td.addClass('clickable')
                    .html('<span class="number">' + value + '</span>')
                    .on('click', function () {
                        var modelId = $("#model-select").val();
                        // 获取产品列表 - 使用GetProductListV2服务
                        twxAjax('Thing.Fn.TestEvaluation', 'GetProductListV2', {
                            modelId: modelId,
                            subsystemName: '列装定型',
                            status: headers[j + 1]
                        }, true, function (res) {
                            if (res.success) {
                                window.generateProductTable(res.data, '列装定型 - ' + headers[j + 1] + ' 产品列表');
                            } else {
                                layer.msg(res.msg, { icon: 2 });
                            }
                        });
                    });
            } else {
                // 修改这里：如果值为0，检查是否满足显示对号的条件
                if (!hasNonZeroBefore(rowData.values, j) && hasNonZeroAfter(rowData.values, j)) {
                    // 如果前面没有非0值，且后续有非0值，显示绿色对号
                    td.html('<span style="color: #5FB878; font-weight: bold;font-size:18px;">✔</span>');
                } else {
                    // 否则显示0
                    td.text(value);
                }
            }
            tr.append(td);
        });
        tbody.append(tr);
    });
}

// 导出函数
window.drawDeploymentTypeTable = drawDeploymentTypeTable;