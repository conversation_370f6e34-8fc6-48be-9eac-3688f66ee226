/**
 * 初始化模板模板配置表格
 */
//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};
var initTemplateDataTable = function() {
	$('#templateTable').datagrid({
		data: [],
		singleSelect: true,
		// fitColumns:true,
		striped: true,
		rownumbers: true,
		toolbar: '#templateTable_tb',
		fit: true,
		columns: [
			[{
					field: 'ID',
					title: 'ID',
					hidden: true
				},
				{
					field: 'REFNAME',
					title: '节点名称',
					width: 200
				},
				{
					field: 'NAME',
					title: '文件类别',
					width: 240
				},
				{
					field: 'TYPE',
					title: '分类',
					width: 100
				}
			]
		],
		pagination: true,
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...'
	});

	//初始化分页组件
	initPagination('templateTable', {
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
};

//初始化分页组件
var initPagination = function(tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function(pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};


//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#templateTable').datagrid('loading');
	initTotalRecords();
	var cb_success = function(data) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#templateTable').datagrid('loadData', data.rows);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#templateTable').datagrid('loaded');
	};
	var cb_error = function() {
		$('#templateTable').datagrid('loaded');
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
			// $('#root_layout').loading('stop');
		});
	};
	//传递的参数
	var param = {};
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.TemplateUtil', 'GetTemplateDataPage', param, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	//initPagination('logtable',{total:data.rows[0].COUNT});
	$('#templateTable').datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
}

//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(data) {
		pageLoadFlag = true;
		totalRecords = data.rows[0].COUNT;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function() {};

	//传递参数
	var param = {};
	twxAjax('Thing.TemplateUtil', 'queryTotalNumbers', param, true, cb_success, cb_error);
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//加载并初始化数据
var initTemplateData = function() {
	var cb_success = function(data) {};
	var cb_error = function() {};
	twxAjax('', '', {}, true, cb_success, cb_error);
};

//初始化添加按钮
var initBtnAdd = function(layui) {
	$('#tbar_add').bind('click', function() {
		var layer = layui.layer;

		var domHtml = document.getElementById('templateForm').innerHTML;

		layer.open({
			title: '新增',
			type: 1,
			area: ['350px', '290px'],
			content: domHtml,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			btn: ['新增', '重置', '关闭'],
			yes: function() {
				$('#btn_add').click();
			},
			btn2: function() {
				$('#btn_reset').click();
				return false;
			},
			btn3: function() {
				return true;
			}
		});
	});
};

//获取选中的模板数据
var getTemplateSelectedData = function() {
	var sels = $('#templateTable').datagrid('getSelections');
	return sels;
};

//初始化编辑按钮
var initBtnEdit = function(layui) {
	$('#tbar_edit').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		var sels = getTemplateSelectedData();
		if (sels.length == 0) {
			layer.alert('请选择需要编辑的数据...', {
				icon: 2
			});
			return;
		}

		var domHtml = document.getElementById('templateForm').innerHTML;

		layer.open({
			title: '编辑',
			type: 1,
			area: ['350px', '290px'],
			content: domHtml,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			btn: ['更新', '重置', '关闭'],
			yes: function() {
				$('#btn_update').click();
			},
			btn2: function() {
				$('#btn_reset').click();
				form.val('addForm', {
					id: sels[0].ID,
					refname: sels[0].REFNAME,
					name: sels[0].NAME,
					type: sels[0].TYPE
				});
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				form.val('addForm', {
					id: sels[0].ID,
					refname: sels[0].REFNAME,
					name: sels[0].NAME,
					type: sels[0].TYPE
				});
			}
		})
	});
};

var templateThingName = 'Thing.TemplateUtil';

//初始化删除按钮
var initBtnDel = function(layui) {
	$('#tbar_del').bind('click', function() {
		var layer = layui.layer;
		var sels = getTemplateSelectedData();
		if (sels.length == 0) {
			layer.alert('请选择需要删除的数据...', {
				icon: 2
			});
			return;
		}

		layer.confirm('确认删除选中数据吗?', {
			icon: 3,
			title: '提示'
		}, function(index) {
			var cb_success = function(data) {
				if (data.success === false) {
					layer.alert(data.message, {
						icon: 2
					});
					logRecord('删除', '删除模板数据', 0);
					return;
				}
				//重新刷新表格
				layer.closeAll();
				layer.msg('删除成功');

				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			};
			var cb_error = function() {};
			var param = {};
			param.id = sels[0].ID;
			// layer.close(index);
			twxAjax(templateThingName, 'DeleteTemplateData', param, true, cb_success, cb_error);
		});
	});
};

//初始化数据表格
$(document).ready(function() {
	layui.use(['layer', 'form'], function() {
		var layer = layui.layer,
			form = layui.form;
		initTemplateDataTable();

		form.on('submit(addData)', function(data) {
			var param = data.field;
			param.nodename = param.refname;

			var cb_success = function(data) {
				if (data.success === false) {
					layer.alert(data.message, {
						icon: 2
					});
					logRecord('新建', '新建模板数据', 0);
					return;
				}
				layer.closeAll();
				logRecord('新建', '新建模板数据', 1);
				layer.msg('新建成功');

				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			};
			var cb_error = function() {};
			twxAjax(templateThingName, 'AddTemplateData', param, true, cb_success, cb_error);
			return false;
		});

		form.on('submit(updateData)', function(data) {
			var param = data.field;
			param.nodename = param.refname;

			var cb_success = function(data) {
				if (data.success === false) {
					layer.alert(data.message, {
						icon: 2
					});
					logRecord('更新', '更新模板数据', 0);
					return;
				}
				layer.closeAll();
				logRecord('更新', '更新模板数据', 1);
				layer.msg('更新成功');
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			};
			var cb_error = function() {};
			twxAjax(templateThingName, 'UpdateTemplateData', param, true, cb_success, cb_error);
			return false;
		});

		initBtnAdd(layui);
		initBtnEdit(layui);
		initBtnDel(layui);
	});
});
