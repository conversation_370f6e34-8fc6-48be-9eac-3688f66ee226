$(function() {
	loadTree();
	//绑定事件
	$('#root_layout_tabs').tabs({
		onSelect: function(title, index) {
			var tab = $('#root_layout_tabs').tabs('getTab', index);
			tableId = tab.panel('options').tableId;
			reloadTable();
		}
	});
	$('#root_layout_tabs').tabs('select', 3);
	initTbrBtn();
})
var tableId = "product_list_table";

//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};
//四个类型表格是否加载
var tableLoadFlag = {
	'design_list_table': false,
	'craft_list_table': false,
	'processcontrol_list_table': false,
	'quanlitycontrol_list_table': false
};
//加载表格
var renderTable = function() {
	if (!tableLoadFlag[tableId]) {
		$('#' + tableId).datagrid({
			data: [],
			fitColumns: true,
			toolbar: '#tb',
			fit: true,
			columns: [
				[
					// {
					// 	field: 'TYPE',
					// 	title: '类别',
					// 	width: 100,
					// 	align: 'center'
					// }, 
					{
						field: 'FILE_TYPE',
						title: '文件类别',
						width: 150,
						align: 'center'
					},
					{
						field: 'FILE_NUMBER',
						title: '文件编号',
						hidden: true
					},
					{
						field: 'FILE_NAME',
						title: '文件名称',
						width: 300,
						align: 'center'
					},
					{
						field: 'GATHERING_METHOD',
						title: '采集方式',
						width: 100,
						align: 'center'
					},
					{
						field: 'SOURCE_SYSTEM',
						title: '来源系统',
						width: 100,
						align: 'center'
					},
					{
						field: 'SECURITY_LEVEL',
						title: '密级',
						width: 100,
						align: 'center'
					},
					{
						field: 'STATE_CHECK',
						title: '状态',
						width: 100,
						align: 'center'
					},
					{
						field: 'DELIVERY_STATE',
						title: '交付状态',
						width: 100,
						align: 'center'
					},
					{
						field: 'USER_FULLNAME',
						title: '创建人',
						width: 100,
						align: 'center'
					},
					{
						field: 'CREATE_TIMESTAMP',
						title: '创建日期',
						width: 150,
						align: 'center'
					},
					{
						field: 'FILE_FORMAT',
						title: '文件形式',
						width: 100,
						align: 'center',
						formatter: formatterEmptyValue
					}, {
						field: 'op',
						title: '操作',
						align: 'center',
						width: 150,
						formatter: opFomatter
					}
				]
			],
			emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>数据加载中...</font></div>',
			pagination: true,
			loadMsg: '正在加载数据...',
			singleSelect: true,
			rownumbers: true,
			striped: true
		});
		tableLoadFlag[tableId] = true;
	}
};

//初始化分页组件
var initPagination = function(data) {
	$('#' + tableId).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function(pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};

//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + tableId).datagrid('loading');
	initTotalRecords();
	var cb_success = function(data) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#' + tableId).datagrid('loadData', data.rows);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#' + tableId).datagrid('loaded');
	};
	var cb_error = function() {
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	var parmas = getSearchParams();
	parmas.pageSize = pageOptions.pageSize;
	parmas.pageNumber = pageOptions.pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.DataSearch', 'QueryProcessData', parmas, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	$('#' + tableId).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
	var log = getSearchLog();
	logRecord('查询', '过程结构树查询-查询(' + log + ')的数据，共查询出' + totalRecords + '条数据', 1);
}

//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(data) {
		pageLoadFlag = true;
		totalRecords = data.rows[0].COUNT;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function() {};
	var parmas = getSearchParams();
	twxAjax('Thing.Fn.DataSearch', 'QueryProcessDataCount', parmas, true, cb_success, cb_error);
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//获取查询条件参数
function getSearchParams() {
	var treeId = 1;
	if ($.fn.zTree.getZTreeObj("dpTree") != null) {
		var selNodes = $.fn.zTree.getZTreeObj("dpTree").getSelectedNodes();
		if (selNodes.length > 0) {
			treeId = selNodes[0].TREEID;
		}
	}
	var type = $('#root_layout_tabs').tabs('getSelected').panel('options').name;
	return {
		treeId: treeId,
		type: type,
		secLevel: '<' + userSecLevel
	}
}

//获取查询日志信息
function getSearchLog() {
	var log = '所有数据包';
	if ($.fn.zTree.getZTreeObj("dpTree") != null) {
		var selNodes = $.fn.zTree.getZTreeObj("dpTree").getSelectedNodes();
		if (selNodes.length > 0) {
			var node = selNodes[0];
			var type = node.NODETYPE;
			if (type == 'root') {
				log = '所有数据包';
			} else if (type == "product") {
				log = '(型号：' + node.NODENAME + ')下的数据包';
			} else if (type == "phase") {
				log = '(型号：' + node.getParentNode().NODENAME + '、阶段：' + node.NODENAME + ')下的数据包';
			} else if (type == "dir") {
				log = '(型号：' + node.getParentNode().getParentNode().NODENAME + '、阶段：' + node.getParentNode().NODENAME + '、专业：' +
					node.NODENAME + ')下的数据包';
			} else if (type == "leaf") {
				log = '(型号：' + node.getParentNode().getParentNode().getParentNode().NODENAME + '、阶段：' + node.getParentNode().getParentNode()
					.NODENAME + '、专业：' + node.getParentNode().NODENAME + '、过程：' + node.NODENAME + ')下的数据包';
			}
		}
	}
	var type = $('#root_layout_tabs').tabs('getSelected').panel('options').name;
	log = log + "的" + type;
	return log;
}

//加载table的数据
var loadTable = function(type) {
	//渲染表格
	renderTable();

	//初始化分页组件
	initPagination({
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}

//重新加载table的数据
var reloadTable = function() {
	if (tableId != 'product_list_table') {
		//渲染表格
		renderTable();

		//初始化分页组件
		initPagination({
			total: 0
		});
		pageOptions.pageNumber = 1;
		//显示第一页的数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	} else {
		loadProductListTable();
	}
}

//加载树结构
function loadTree() {
	var cb_success = function(res) {
		var datas = res.data;
		if (datas.length > 0) {
			datas = dealDataIcons(datas);
			datas = dealDataNodeName(datas);
			treeSetting.callback.onClick = function(event, treeId, treeNode) {
				reloadTable();
			};
			//禁止拖拽
			treeSetting.callback.beforeDrag = function() {
				return false;
			};
			treeSetting.callback.beforeDrop = function() {
				return false;
			};
			treeSetting.async.url = getTreeUrl("Thing.Fn.DataPackage", "QueryDataPackageTree1", ""),
				ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
			var nodes = ztreeObj.getNodes();
			for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
				ztreeObj.expandNode(nodes[i], true, false, true);
			}
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax('Thing.Fn.ProcessTree', 'QueryTreeRoot', {
		username: sessionStorage.getItem('username')
	}, true, cb_success);
}