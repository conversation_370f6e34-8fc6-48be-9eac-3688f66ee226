/* Handsontable 16.0.1 上下文菜单定位优化 */
.htContextMenu {
    /* 确保菜单在视口内显示，不干扰默认定位 */
    max-height: calc(100vh - 20px) !important;
    overflow-y: auto !important;
    z-index: 9999 !important;
    /* 保持菜单的可见性和可访问性 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 菜单项的样式优化 */
.htContextMenu .ht_clone_top,
.htContextMenu .ht_clone_left,
.htContextMenu .ht_clone_corner {
    max-width: 300px !important;
}

.layui-form-label {
    font-family: "微软雅黑";
    font-size: 14px;
    width: 90px;
}

.layui-btn-mycolor2 {
    background-color: #99c1fc;
}

.layui-btn-mycolor3 {
    background-color: #fe909d;
}

.layui-btn-mycolor4 {
    background-color: #c078fe;
}

.layui-btn-mycolor5 {
    background-color: #fba0a0;
}

.layui-btn-mycolor6 {
    background-color: #4a7189;
}

.layui-btn-mycolor7 {
    background-color: #d0582f;
}

.layui-btn-mycolor8 {
    background-color: #0eb0c9;
}

.table-row-a {
    color: blue;
    cursor: pointer;
}

.td-bold-left {
    font-weight: bold !important;
}

.original-table .datagrid-header {
    border-color: #000000;
    border-width: 1px 0 1px 0;
}

.original-table .datagrid-header td,
.original-table .datagrid-body td {
    border-color: #000000;
}

.original-table .datagrid-header td {
    font-weight: 600;
}

.original-table .datagrid-header,
.original-table .datagrid-td-rownumber {
    background-color: transparent !important;
}

.original-table .datagrid-wrap.panel-body.panel-body-noheader {
    border-color: black !important;
    border-top-width: 0 !important;
    border-bottom: 0 !important;
}

.original-table tr td:last-child {
    border-right-width: 0 !important;
}

.layui-table tr td:last-child {
    border-right-width: 1px !important;
}

.my-table td,
.my-table th,
.my-table .layui-table-col-set,
.my-table .layui-table-fixed-r,
.my-table .layui-table-grid-down,
.my-table .layui-table-header,
.my-table .layui-table-page,
.my-table .layui-table-tips-main,
.my-table .layui-table-tool,
.my-table .layui-table-total,
.my-table .layui-table-view,
.my-table .layui-table[lay-skin="line"],
.my-table .layui-table[lay-skin="row"] {
    border-color: #333;
}

.my-table td {
    font-family: 宋体;
    font-size: 18px;
    color: black;
}

.table-name {
    font-size: 20px !important;
    font-weight: bold;
}

.table-head {
    font-weight: bold;
}

.sign-img {
    max-width: 90px !important;
    max-height: 100px;
    margin-right: 10px;
    margin-top: 10px;
}

.test-sign {
    max-width: 200px !important;
}

.data-table.layui-table td,
.data-table.layui-table th,
.data-table .layui-table-col-set,
.data-table .layui-table-fixed-r,
.data-table .layui-table-grid-down,
.data-table .layui-table-header,
.data-table .layui-table-page,
.data-table .layui-table-tips-main,
.data-table .layui-table-tool,
.data-table .layui-table-total,
.data-table .layui-table-view,
.data-table.layui-table[lay-skin="line"],
.data-table.layui-table[lay-skin="row"] {
    border-color: #333;
}

.data-table.layui-table td,
.data-table.layui-table th {
    color: #333;
    font-size: 13px;
}

td.custom-cell {
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
    background-color: #e2e2e2;
}

.c-bold {
    font-weight: bold;
}

#table .layui-table tbody tr:hover {
    background-color: white;
}

.htLeft {
    text-align: left;
}

.htCenter {
    text-align: center;
}

.htRight {
    text-align: right;
}

.htJustify {
    text-align: justify;
}

.htTop {
    vertical-align: top;
}

.htMiddle {
    vertical-align: middle;
}

.htBottom {
    vertical-align: bottom;
}

.font-size-12 {
    font-size: 12px !important;
    line-height: 19px !important;
}

.font-size-13 {
    font-size: 13px !important;
    line-height: 20px !important;
}

.font-size-14 {
    font-size: 14px !important;
    line-height: 21px !important;
}

.font-size-15 {
    font-size: 15px !important;
    line-height: 22px !important;
}

.font-size-16 {
    font-size: 16px !important;
    line-height: 23px !important;
}

.font-size-17 {
    font-size: 17px !important;
    line-height: 24px !important;
}

.font-size-18 {
    font-size: 18px !important;
    line-height: 25px !important;
}

.font-size-19 {
    font-size: 19px !important;
    line-height: 26px !important;
}

.font-size-20 {
    font-size: 20px !important;
    line-height: 27px !important;
}

.font-size-21 {
    font-size: 21px !important;
    line-height: 28px !important;
}

.font-size-22 {
    font-size: 22px !important;
    line-height: 29px !important;
}

.font-size-23 {
    font-size: 23px !important;
    line-height: 30px !important;
}

.font-size-24 {
    font-size: 24px !important;
    line-height: 31px !important;
}

.font-size-25 {
    font-size: 25px !important;
    line-height: 32px !important;
}

.font-size-26 {
    font-size: 26px !important;
    line-height: 33px !important;
}

.font-size-27 {
    font-size: 27px !important;
    line-height: 34px !important;
}

.font-size-28 {
    font-size: 28px !important;
    line-height: 35px !important;
}

.font-size-29 {
    font-size: 29px !important;
    line-height: 36px !important;
}

.font-size-30 {
    font-size: 30px !important;
    line-height: 37px !important;
}

.font-size-31 {
    font-size: 31px !important;
    line-height: 38px !important;
}

.font-size-32 {
    font-size: 32px !important;
    line-height: 39px !important;
}

.font-size-33 {
    font-size: 33px !important;
    line-height: 40px !important;
}

.font-size-34 {
    font-size: 34px !important;
    line-height: 41px !important;
}

.font-size-35 {
    font-size: 35px !important;
    line-height: 42px !important;
}

.font-size-36 {
    font-size: 36px !important;
    line-height: 43px !important;
}

.font-size-37 {
    font-size: 37px !important;
    line-height: 44px !important;
}

.font-size-38 {
    font-size: 38px !important;
    line-height: 45px !important;
}

.font-size-39 {
    font-size: 39px !important;
    line-height: 46px !important;
}

.font-size-40 {
    font-size: 40px !important;
    line-height: 47px !important;
}

.font-color-purple {
    color: purple !important;
}

.font-color-green {
    color: green !important;
}

.font-color-orange {
    color: orange !important;
}

.font-color-deeppink {
    color: deeppink !important;
}

.table-head tr {
    border: 1px solid #e6e6e6;
    color: #666666;
    font-size: 16px;
}

.table-head {
    margin-right: 17px;
    overflow: hidden;
}

.table-body .layui-table,
.table-head .layui-table {
    margin: 0px;
}

.table-body {
    overflow-y: scroll;
}

.table-cell1 {
    margin: 0;
    padding: 0 4px;
    white-space: nowrap;
    word-wrap: normal;
    overflow: hidden;
    height: 18px;
    line-height: 18px;
    font-size: 14px;
}

.htDimmed {
    color: #777 !important;
    background-color: rgb(255, 245, 217) !important;
}

.layui-layer-tips .layui-layer-content {
    position: relative;
    line-height: 22px;
    min-width: 12px;
    padding: 8px 15px;
    font-size: 12px;
    _float: left;
    border-radius: 2px;
    box-shadow: 1px 1px 3pxrgba (0, 0, 0, 0.2);
    background-color: #d3d3d3 !important;
    color: #333 !important;
}

.layui-layer-tips i.layui-layer-TipsL,
.layui-layer-tips i.layui-layer-TipsR {
    border-bottom-color: #d3d3d3 !important;
}

.htCommentCell:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    border-left: 30px solid transparent !important;
    border-top: 30px solid #f71111 !important;
}

.sticky-thead {
    position: sticky;
    top: -1px;
    z-index: 100;
}

.sticky-thead tr {
    font-weight: bold;
    background-color: #e6e6e6 !important;
}



.td-bg {
    background-color: #e6e6e6 !important;
    font-weight: bold;
}

#tbr {
    display: none;
    float: left;
    width: 100%;
}

.table-security {
    width: 100px;
    display: inline-table;
    float: right;
    height: 50px;
    font-size: 24px;
    line-height: 50px;
    font-family: fangsong;
    font-weight: 600;
}

.download {
    margin-right: 5px !important;
    opacity: 1 !important;
    background: url(../img/download.png);
}

.upload {
    margin-right: 5px !important;
    opacity: 1 !important;
    background: url(../img/excel.png);
}

.search-form .layui-input {
    height: 30px;
}

.search-form .layui-form-label {
    padding: 5px 15px;
}

.search-form .layui-form-mid {
    padding: 5px 0px !important;
}

.search-form .layui-form-item {
    margin: 10px 0px;
}

.search-form .layui-form-item .layui-inline {
    margin-bottom: 0px;
}

.show-msg {
    cursor: pointer;
}

.layui-table td,
.layui-table th {
    font-size: 13px;
}

.sign-box {
    /* background-color: #f8fbff !important; */
    border: 2px dashed #1E9FFF !important;
    position: relative !important;
}

.sign-box::before {
    content: "✍" !important;
    position: absolute !important;
    top: 2px !important;
    right: 2px !important;
    font-size: 12px !important;
    color: #1E9FFF !important;
    opacity: 0.7 !important;
    pointer-events: none !important;
}

.layui-layer-page .layui-layer-content {
    overflow: visible !important;
}