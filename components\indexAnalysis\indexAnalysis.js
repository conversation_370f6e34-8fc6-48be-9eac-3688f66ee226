var ztreeObj;
var THING = 'Thing.Fn.IndexAnalysis';
var layer;

layui.use('layer', function() {
	layer = layui.layer;
});
$(function() {
	loadTree();
	loadQualityType();
	loadEnvelope();
	renderBtn();
});

function loadQualityType() {
	var cb_success = function(res) {
		if (res.success) {
			var datas = res.data;
			$('#qualityType').combobox({
				data: datas,
				valueField: 'ID',
				textField: 'TREE_NAME',
				editable: false,
				width: 300,
				panelHeight: 400,
				onSelect: function(record) {
					loadIndexParam(record['ID']);
				}
			});
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	};
	twxAjax(THING, 'QueryQualityType', '', true, cb_success);
}

/**
 * 清空已经选择数据列表
 */
function clearDataList() {
	$("#dataList").data("selectDatas", []);
	$("#dataList").val("");
}

/**
 * 加载指标参数复选框
 */
function loadIndexParam(tableId) {
	clearDataList();
	$("#indexParamDiv").empty();
	var cb_success = function(res) {
		if (res.success) {
			var datas = res.data;
			for (var i = 0; i < datas.length; i++) {
				var paramId = "index_" + datas[i]['ID'];
				var paramName = datas[i]['PARAM_NAME'];
				$("#indexParamDiv").append('<div class="index-ck"><input id="' + paramId + '"/><div>');
				$('#' + paramId).data("id", datas[i]['ID']);
				$('#' + paramId).checkbox({
					label: paramName,
					checked: false,
					labelPosition: 'after'
				});
			}
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	};
	twxAjax(THING, 'QueryIndexParam', {
		tableId: tableId
	}, true, cb_success);
}

function loadEnvelope() {
	$('#isEnvelope').checkbox({
		label: '包络线分析',
		checked: false,
		labelPosition: 'after'
	});
}

//加载型号选择树
function loadTree() {
	var treeSetting = {
		view: {
			dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
			showLine: true, //是否显示节点之间的连线
			fontCss: {
				'color': 'black'
			}, //字体样式函数
			selectedMulti: false, //设置是否允许同时选中多个节点,
			txtSelectedEnable: true,
			showTitle: true
		},
		async: {
			enable: false
		},
		check: {
			chkboxType: {
				"Y": "",
				"N": ""
			},
			chkStyle: "checkbox",
			enable: true
		},
		data: {
			simpleData: { //简单数据模式
				enable: true,
				idKey: "TREEID",
				pIdKey: "PARENTID",
				rootPId: 1
			},
			key: {
				name: 'NODENAME',
				title: '',
				isParent: "ISPARENT"
			}
		}
	};

	function dealDataCheck(datas) {
		for (var i = 0; i < datas.length; i++) {
			var dtype = datas[i].NODETYPE;
			if (dtype === 'product') {
				datas[i].nocheck = false;
			} else {
				datas[i].nocheck = true;
			}
		}
		return datas;
	}

	var cb_success = function(res) {
		if (res.success) {
			var datas = res.data;
			if (datas.length > 0) {
				datas = dealDataIcons(datas);
				datas = dealDataNodeName(datas);
				datas = dealDataCheck(datas);
				ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
				var nodes = ztreeObj.getNodes();
				for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
					ztreeObj.expandNode(nodes[i], true, false, true);
				}
				//横向集合节点
				var hengNode = ztreeObj.getNodeByParam("TREEID", -1, null);
				if (hengNode != null) {
					ztreeObj.expandNode(hengNode, true, false, true);
				}
			}
		} else {
			layer.alert('请求结构树失败，原因：' + res.msg, {
				icon: 2
			});
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax(THING, 'QueryModelTree', {
		username: sessionStorage.getItem('username')
	}, true, cb_success);
}

/**
 * 前置条件判断
 */
function preconditions(successFn) {
	//获取选择的质量数据类型
	var tableId = $('#qualityType').combobox('getValue');
	if (tableId != '') {
		//获取型号
		var models = ztreeObj.getCheckedNodes();
		if (models.length > 0) {
			successFn(models, tableId);
		} else {
			layer.alert('请至少选择一个型号！', {
				icon: 2
			});
		}
	} else {
		layer.alert('请先选择质量数据类型！', {
			icon: 2
		});
	}
}

/**
 * 初始化按钮的点击事件 
 */
function renderBtn() {
	$("#selectData").click(function() {
		preconditions(function(models, tableId) {
			selectData(models, tableId);
		});
	});

	$("#start").click(function() {
		startAnalysis();
	});
}

/**
 * 选择数据的弹框
 */
function selectData(models, tableId) {
	layer.open({
		title: false,
		closeBtn: 0,
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		maxmin: false,
		resize: false,
		area: ["1366px", '768px'],
		content: '<div id="selectDataContent"></div>',
		btn: ['确定', '取消'],
		yes: function(index, layero) {
			fillSelectData();
		},
		btn2: function(index, layero) {
			return true;
		},
		success: function() {
			var tpl = '<div id="select-layout" class="easyui-layout" style="width:100%;height:713px;" data-options="fit:true">\
							<div data-options="region:\'west\',border:true,collapsible:false" title="所有数据" style="width:683px;height:713px">\
								<div id="allTable" data-options="border:false"></div>\
							</div>\
							<div data-options="region:\'center\'" title="已选数据列表" style="width:683px;height:713px">\
								<div id="selectTable" data-options="border:false"></div>\
							</div>\
						</div>';
			$("#selectDataContent").append(tpl);
			$("#select-layout").layout();
			renderTable(models, tableId);
		}
	});
}

/**
 * 加载质量数据表格
 */
function renderTable(models, tableId) {
	//获取质量数据的表头格式

	var cb_success = function(data) {
		if (data.success) {
			var dealCol = dealColumns(JSON.parse(data.result));

			new loadAllTable(dealCol.col, tableId, models);
			new loadSelectTable(dealCol.col);

		} else {
			layer.alert("表头获取失败！", {
				icon: 2
			});
		}
	}

	var cb_error = function() {
		layer.alert("表头获取失败！", {
			icon: 2
		});
	};
	twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', {
		id: tableId
	}, false, cb_success, cb_error);
}

/**
 * 加载已选数据列表
 * @param {Object} col
 */
function loadSelectTable(col) {
	var othis = this;
	this.tableId = 'selectTable';
	this.pageOptions = {
		pageSize: 15,
		pageNumber: 1
	};

	this.renderTb = function() {
		$("#clear-select").click(function() {
			//获取选中的数据
			var checkDatas = $('#' + othis.tableId).datagrid('getChecked');
			if (checkDatas.length > 0) {
				var newDatas = [];
				var allDatas = $("#selectTable").datagrid('getData').originalRows || [];
				for (var i = 0; i < allDatas.length; i++) {
					var isIn = false;
					for (var j = 0; j < checkDatas.length; j++) {
						if (allDatas[i]['ID'] == checkDatas[j]['ID']) {
							isIn = true;
							break;
						}
					}
					if (!isIn) {
						newDatas.push(allDatas[i]);
					}
				}
				$("#selectTable").datagrid('loadData', newDatas);
				// changeWidth("selectTable");
			} else {
				layer.alert('请至少勾选一条数据！', {
					icon: 2
				});
			}
		});
	};

	this.appendTb = function() {
		var tb = '<div id="selectTableTb" style="padding:5px;height:auto">\
				<div>\
					<a href="#" class="easyui-linkbutton" iconCls="icon-clear" id="clear-select">删除</a>\
				</div>\
			</div>';
		$('body').append(tb);
		$('#clear-select').linkbutton();
	};
	this.initTable = function() {
		$('#' + othis.tableId).datagrid({
			data: $("#dataList").data("selectDatas"),
			columns: col,
			toolbar: "#selectTableTb",
			height: 680,
			singleSelect: false,
			remoteSort: false,
			pagination: true,
			loadFilter: function(data) {
				if (typeof data.length == 'number' && typeof data.splice == 'function') { // is array
					data = {
						total: data.length,
						rows: data
					}
				}
				var dg = $(this);
				var opts = dg.datagrid('options');
				var pager = dg.datagrid('getPager');
				pager.pagination({
					showPageList: true,
					showRefresh: true,
					onSelectPage: function(pageNum, pageSize) {
						opts.pageNumber = pageNum;
						opts.pageSize = pageSize;
						pager.pagination('refresh', {
							pageNumber: pageNum,
							pageSize: pageSize
						});
						dg.datagrid('loadData', data);
					}
				});
				if (!data.originalRows) {
					data.originalRows = (data.rows);
				}
				var start = (opts.pageNumber - 1) * parseInt(opts.pageSize);
				var end = start + parseInt(opts.pageSize);
				data.rows = (data.originalRows.slice(start, end));
				return data;
			},
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber,
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">请选择数据！</div>',
			loadMsg: '正在加载数据...',
			striped: false,
			onLoadSuccess: function(data) {
				changeWidth(othis.tableId);
				$("#select-layout .datagrid-body").css("overflow-x", "auto");
				$('#' + othis.tableId).datagrid('loaded');
			}
		});
	};
	this.appendTb();
	this.initTable();
	this.renderTb();
}

/**
 * 加载查询的所有质量数据列表
 */
function loadAllTable(col, tableConfigId, models) {
	var othis = this;
	this.tableId = 'allTable';
	this.pageOptions = {
		pageSize: 15,
		pageNumber: 1
	};
	this.renderTb = function() {
		$("#add-all").click(function() {
			//获取选中的数据
			var allDatas = $('#' + othis.tableId).datagrid('getChecked');
			if (allDatas.length > 0) {
				var selectDatas = $("#selectTable").datagrid('getData').originalRows || [];
				for (var i = 0; i < allDatas.length; i++) {
					var has = false;
					for (var j = 0; j < selectDatas.length; j++) {
						if (allDatas[i]['ID'] == selectDatas[j]['ID']) {
							has = true;
							break;
						}
					}
					if (!has) {
						selectDatas.push(allDatas[i]);
					}
				}
				$("#selectTable").datagrid('loadData', selectDatas);
				// changeWidth("selectTable");
			} else {
				layer.alert('请至少勾选一条数据！', {
					icon: 2
				});
			}
		});
	};

	this.appendTb = function() {
		var tb = '<div id="allTableTb" style="padding:5px;height:auto">\
			<div>\
				<input id="search-textbox" class="easyui-textbox" style="width:150px;display:none;">\
				<a href="#" class="easyui-linkbutton" iconCls="icon-search" id="search-all" style="display:none;">搜索</a>\
				<a href="#" class="easyui-linkbutton" iconCls="icon-pull-right" id="add-all">添加</a>\
			</div>\
		</div>';
		$('body').append(tb);
		// $('#search-all').linkbutton();
		$('#add-all').linkbutton();
		// $("#search-textbox").textbox();
	};

	this.initTable = function() {
		$('#' + othis.tableId).datagrid({
			data: [],
			columns: col,
			toolbar: "#allTableTb",
			height: 680,
			singleSelect: false,
			remoteSort: false,
			pagination: true,
			emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
			loadMsg: '正在加载数据...',
			striped: false,
			onLoadSuccess: function(data) {
				changeWidth(othis.tableId);
				$("#select-layout .datagrid-body").css("overflow-x", "auto");
				$('#' + othis.tableId).datagrid('loaded');
			}
		});
	}
	//初始化分页组件
	this.initPagination = function(data) {
		$('#' + othis.tableId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			// buttons: [{
			// 	iconCls: 'pagination-load',
			// 	handler: function() {
			// 		othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions.pageNumber);
			// 	}
			// }],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: true,
			onSelectPage: function(pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function(pageNumber, pageSize) {},
			onRefresh: function(pageNumber, pageSize) {
				// othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function(pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};

	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function() {
		$('#' + othis.tableId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
	};

	//初始化全部的记录条数
	this.initTotalRecords = function() {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function(data) {
			othis.pageLoadFlag = true;
			othis.totalRecords = data.rows[0].result;
			if (othis.dataLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function() {};

		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataCount', othis.getParams(), true, cb_success, cb_error);
	};
	this.getParams = function() {
		var tempArr = [];
		for (var i = 0; i < models.length; i++) {
			tempArr.push(models[i]['TREEID']);
		}
		return {
			processTreeId: tempArr.join(','),
			table_config_id: tableConfigId,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		}
	};
	//分页查询数据
	this.queryDataByPage = function(pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.tableId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function(data) {
			othis.dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + othis.tableId).datagrid('loadData', data.array);
			if (othis.pageLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function() {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var params = othis.getParams();
		params.pageSize = pageSize;
		params.pageNumber = pageNumber;
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataPage', params, true, cb_success, cb_error);
	};
	this.appendTb();
	this.initTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
	this.renderTb();
}

/**
 * 将选择的数据填充到选择框中
 */
function fillSelectData() {
	var selectDatas = $("#selectTable").datagrid('getData').originalRows || [];
	if (selectDatas.length > 0) {
		$("#dataList").val("已选择" + selectDatas.length + "条数据");
		$("#dataList").data("selectDatas", selectDatas);
	} else {
		clearDataList();
	}
	layer.closeAll();
}

/**
 * 获取选择的分析指标
 */
function getCheckedIndex() {
	var checkedIndexs = [];
	$("input[id^='index']").each(function(i, n) {
		if ($(n).checkbox("options").checked) {
			checkedIndexs.push($(n).data("id"));
		}
	});
	return checkedIndexs;
}

/**
 * 将对象数组中的某一个字段筛选出来，用，拼接
 */
function arrToStr(arr, field) {
	var temp = [];
	for (var i = 0; i < arr.length; i++) {
		temp.push(arr[i][field]);
	}
	return temp.join(",");
}

/**
 * 开始分析
 */
function startAnalysis() {
	//如果为空的话 则分析所有的数据
	preconditions(function(models, tableId) {
		var checkedIndexs = getCheckedIndex();
		if (checkedIndexs.length > 0) {

			var isEnvelope = $("#isEnvelope").checkbox("options").checked;

			function excute() {
				//判断 选择框中是否有数据
				var selectDatas = $("#dataList").data("selectDatas");

				var loadIndex = layer.load();
				var cb_success = function(res) {
					if (res.success) {
						layer.close(loadIndex);
						loadChart(res.data);
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				};
				var cb_error = function() {
					layer.alert('查询出错...', {
						icon: 2
					});
				};

				var modelTreeId
				var params = {
					models: arrToStr(models, "TREEID"),
					tableId: tableId,
					isEnvelope: isEnvelope,
					checkedIndexs: checkedIndexs.join(","),
					selectDatas: arrToStr(selectDatas, "ID"),
					username: sessionStorage.getItem('username')
				};
				twxAjax(THING, 'QueryAnalysis', params, true, cb_success, cb_error);
			}

			if (isEnvelope) {
				if (checkedIndexs.length > 1) {
					layer.alert('勾选包络线分析后，只能勾选一个指标！', {
						icon: 2
					});
				} else {
					excute();
				}
			} else {
				excute();
			}
		} else {
			layer.alert('请至少选择一个指标！', {
				icon: 2
			});
		}
	});
}

/**
 * 加载折线图
 * @param {Object} opt
 */
function loadChart(opt) {
	var myChart = echarts.init(document.getElementById('chart'));
	myChart.setOption(opt, true);
}