var massTableCols = [
    [
        { field: "TEST_ID", title: "测试编号", sort: true, description: "", minWidth: 120, overflow: 'tips' },
        { field: "PRODUCT_MODEL", title: "产品型号", description: "", minWidth: 120, overflow: 'tips' },
        { field: "BATCH", title: "批次", description: "", minWidth: 100, overflow: 'tips' },
        { field: "PRODUCT_NAME", title: "产品名称", description: "", minWidth: 150, overflow: 'tips' },
        { field: "OPERATOR1", title: "操作人员1", description: "", minWidth: 120, overflow: 'tips' },
        { field: "OPERATOR2", title: "操作人员2", description: "", minWidth: 120, overflow: 'tips' },
        { field: "OPERATOR3", title: "操作人员3", description: "", minWidth: 120, overflow: 'tips' },
        { field: "OPERATOR4", title: "操作人员4", description: "", minWidth: 120, overflow: 'tips' },
        { field: "TEST_DATE", title: "测试日期", sort: true, description: "", minWidth: 150, overflow: 'tips' },
        { field: "H_PLAT_P1_WEIGHT", title: "HPlatP1Weight", description: "水平测试时，平台P1", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.H_PLAT_P1_WEIGHT + '">' + d.H_PLAT_P1_WEIGHT + '</div>'; } },
        { field: "H_PLAT_P2_WEIGHT", title: "HPlatP2Weight", description: "水平测试时，平台P2", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.H_PLAT_P2_WEIGHT + '">' + d.H_PLAT_P2_WEIGHT + '</div>'; } },
        { field: "H_PLAT_P3_WEIGHT", title: "HPlatP3Weight", description: "水平测试时，平台P3", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.H_PLAT_P3_WEIGHT + '">' + d.H_PLAT_P3_WEIGHT + '</div>'; } },
        { field: "H_PLAT_TOTAL_WEIGHT", title: "HPlatPTotalWeight", description: "水平测试时，平台总重量", minWidth: 180 },
        { field: "H_CLAMP_P1", title: "H_Clamp_P1", description: "水平测试时，夹具P1" },
        { field: "H_CLAMP_P2", title: "H_Clamp_P2", description: "水平测试时，夹具P2" },
        { field: "H_CLAMP_P3", title: "H_Clamp_P3", description: "水平测试时，夹具P3" },
        { field: "H_CLAMP_G", title: "H_Clamp_G", description: "水平测试时，夹具总重" },
        { field: "H_CLAMP_YC", title: "H_Clamp_Yc", description: "水平测试时，夹具Y向质心" },
        { field: "H_CLAMP_ZC", title: "H_Clamp_Zc", description: "水平测试时，夹具Z向质心" },
        { field: "H_CLAMP_R", title: "H_Clamp_R", description: "水平测试时，夹具质心与坐标原点距离" },
        { field: "H_CLAMP_ALFA", title: "H_Clamp_Alfa", description: "水平测试时，夹具质心与坐标原点连线与+Y轴间角度" },
        { field: "H_CLAMP_MY", title: "H_Clamp_My", description: "水平测试时，夹具Y向力矩" },
        { field: "H_CLAMP_MZ", title: "H_Clamp_Mz", description: "水平测试时，夹具Z向力矩" },
        { field: "H_CLAMP_BALANCE_ANGLE", title: "H_Clamp_BalanceAngle", description: "水平测试时，夹具配平角度" },
        { field: "H_CLAMP_BALANCE_MOMENT", title: "H_Clamp_BalanceMoment", description: "水平测试时，夹具配平力矩" },
        { field: "H_CLAMP_SATELLITE_P1", title: "H_ClampSatellite_P1", description: "水平测试时，夹具+卫星P1" },
        { field: "H_CLAMP_SATELLITE_P2", title: "H_ClampSatellite_P2", description: "水平测试时，夹具+卫星P2" },
        { field: "H_CLAMP_SATELLITE_P3", title: "H_ClampSatellite_P3", description: "水平测试时，夹具+卫星P3" },
        { field: "H_CLAMP_SATELLITE_G", title: "H_ClampSatellite_G", description: "水平测试时，夹具+卫星总重" },
        { field: "H_CLAMP_SATELLITE_YC", title: "H_ClampSatellite_Yc", description: "水平测试时，夹具+卫星Y向质心" },
        { field: "H_CLAMP_SATELLITE_ZC", title: "H_ClampSatellite_Zc", description: "水平测试时，夹具+卫星Z向质心" },
        { field: "H_CLAMP_SATELLITE_R", title: "H_ClampSatellite_R", description: "水平测试时，夹具+卫星质心与坐标原点距离" },
        { field: "H_CLAMP_SATELLITE_ALFA", title: "H_ClampSatellite_Alfa", description: "水平测试时，夹具+卫星质心与坐标原点连线与+Y轴间角度" },
        { field: "H_CLAMP_SATELLITE_MY", title: "H_ClampSatellite_My", description: "水平测试时，夹具+卫星Y向力矩" },
        { field: "H_CLAMP_SATELLITE_MZ", title: "H_ClampSatellite_Mz", description: "水平测试时，夹具+卫星Z向力矩" },
        { field: "H_CLAMP_SAT_BAL_ANGLE", title: "H_ClampSatellite_BalanceAngle", description: "水平测试时，夹具+卫星配平角度" },
        { field: "H_CLAMP_SAT_BAL_MOMENT", title: "H_ClampSatellite_BalanceMoment", description: "水平测试时，夹具+卫星配平力矩" },
        { field: "H_SATELLITE_P1", title: "H_Satellite_P1", description: "水平测试时，卫星P1" },
        { field: "H_SATELLITE_P2", title: "H_Satellite_P2", description: "水平测试时，卫星P2" },
        { field: "H_SATELLITE_P3", title: "H_Satellite_P3", description: "水平测试时，卫星P3" },
        { field: "H_SATELLITE_G", title: "H_Satellite_G", description: "水平测试时，卫星总重" },
        { field: "H_SATELLITE_YC", title: "H_Satellite_Yc", description: "水平测试时，卫星Y向质心" },
        { field: "H_SATELLITE_ZC", title: "H_Satellite_Zc", description: "水平测试时，卫星Z向质心" },
        { field: "H_SATELLITE_R", title: "H_Satellite_R", description: "水平测试时，卫星质心与坐标原点距离" },
        { field: "H_SATELLITE_ALFA", title: "H_Satellite_Alfa", description: "水平测试时，卫星质心与坐标原点连线与+X轴间角度" },
        { field: "H_SATELLITE_MY", title: "H_Satellite_My", description: "水平测试时，卫星Y向力矩" },
        { field: "H_SATELLITE_MZ", title: "H_Satellite_Mz", description: "水平测试时，卫星Z向力矩" },
        { field: "H_SATELLITE_BALANCE_ANGLE", title: "H_Satellite_BalanceAngle", description: "水平测试时，卫星配平角度" },
        { field: "H_SATELLITE_BALANCE_MOMENT", title: "H_Satellite_BalanceMoment", description: "水平测试时，卫星配平力矩" },
        { field: "V_PLAT_P1_WEIGHT", title: "VPlatP1Weight", description: "倾斜测试时，平台P1" },
        { field: "V_PLAT_P2_WEIGHT", title: "VPlatP2Weight", description: "倾斜测试时，平台P2" },
        { field: "V_PLAT_P3_WEIGHT", title: "VPlatP3Weight", description: "倾斜测试时，平台P3" },
        { field: "V_PLAT_TOTAL_WEIGHT", title: "VPlatPTotalWeight", description: "倾斜测试时，平台总重量" },
        { field: "V_CLAMP_P1", title: "V_Clamp_P1", description: "倾斜测试时，夹具P1" },
        { field: "V_CLAMP_P2", title: "V_Clamp_P2", description: "倾斜测试时，夹具P2" },
        { field: "V_CLAMP_P3", title: "V_Clamp_P3", description: "倾斜测试时，夹具P3" },
        { field: "V_CLAMP_G", title: "V_Clamp_G", description: "倾斜测试时，夹具总重" },
        { field: "V_CLAMP_XC", title: "V_Clamp_Xc", description: "倾斜测试时，夹具Y向质心" },
        { field: "V_CLAMP_ZC", title: "V_Clamp_Zc", description: "倾斜测试时，夹具Z向质心" },
        { field: "V_CLAMP_R", title: "V_Clamp_R", description: "倾斜测试时，夹具质心与坐标原点距离" },
        { field: "V_CLAMP_ALFA", title: "V_Clamp_Alfa", description: "倾斜测试时，夹具质心与坐标原点连线与+Y轴间角度" },
        { field: "V_CLAMP_MX", title: "V_Clamp_Mx", description: "倾斜测试时，夹具Y向力矩" },
        { field: "V_CLAMP_MZ", title: "V_Clamp_Mz", description: "倾斜测试时，夹具Z向力矩" },
        { field: "V_CLAMP_BALANCE_ANGLE", title: "V_Clamp_BalanceAngle", description: "倾斜测试时，夹具配平角度" },
        { field: "V_CLAMP_BALANCE_MOMENT", title: "V_Clamp_BalanceMoment", description: "倾斜测试时，夹具配平力矩" },
        { field: "V_SATELLITE_P1", title: "V_Satellite_P1", description: "倾斜测试时，卫星P1" },
        { field: "V_SATELLITE_P2", title: "V_Satellite_P2", description: "倾斜测试时，卫星P2" },
        { field: "V_SATELLITE_P3", title: "V_Satellite_P3", description: "倾斜测试时，卫星P3" },
        { field: "V_SATELLITE_G", title: "V_Satellite_G", description: "倾斜测试时，卫星总重" },
        { field: "V_SATELLITE_XC", title: "V_Satellite_Xc", description: "倾斜测试时，卫星Y向质心" },
        { field: "V_SATELLITE_ZC", title: "V_Satellite_Zc", description: "倾斜测试时，卫星Z向质心" },
        { field: "V_SATELLITE_R", title: "V_Satellite_R", description: "倾斜测试时，卫星质心与坐标原点距离" },
        { field: "V_SATELLITE_ALFA", title: "V_Satellite_Alfa", description: "倾斜测试时，卫星质心与坐标原点连线与+X轴间角度" },
        { field: "V_SATELLITE_MX", title: "V_Satellite_Mx", description: "倾斜测试时，卫星Y向力矩" },
        { field: "V_SATELLITE_MZ", title: "V_Satellite_Mz", description: "倾斜测试时，卫星Z向力矩" },
        { field: "V_SATELLITE_BALANCE_ANGLE", title: "V_Satellite_BalanceAngle", description: "倾斜测试时，卫星配平角度" },
        { field: "V_SATELLITE_BALANCE_MOMENT", title: "V_Satellite_BalanceMoment", description: "倾斜测试时，卫星配平力矩" },
        { field: "V_CLAMP_SATELLITE_P1", title: "V_ClampSatellite_P1", description: "倾斜测试时，夹具+卫星P1" },
        { field: "V_CLAMP_SATELLITE_P2", title: "V_ClampSatellite_P2", description: "倾斜测试时，夹具+卫星P2" },
        { field: "V_CLAMP_SATELLITE_P3", title: "V_ClampSatellite_P3", description: "倾斜测试时，夹具+卫星P3" },
        { field: "V_CLAMP_SATELLITE_G", title: "V_ClampSatellite_G", description: "倾斜测试时，夹具+卫星总重" },
        { field: "V_CLAMP_SATELLITE_XC", title: "V_ClampSatellite_Xc", description: "倾斜测试时，夹具+卫星Y向质心" },
        { field: "V_CLAMP_SATELLITE_ZC", title: "V_ClampSatellite_Zc", description: "倾斜测试时，夹具+卫星Z向质心" },
        { field: "V_CLAMP_SATELLITE_R", title: "V_ClampSatellite_R", description: "倾斜测试时，夹具+卫星质心与坐标原点距离" },
        { field: "V_CLAMP_SATELLITE_ALFA", title: "V_ClampSatellite_Alfa", description: "倾斜测试时，夹具+卫星质心与坐标原点连线与+Y轴间角度" },
        { field: "V_CLAMP_SATELLITE_MX", title: "V_ClampSatellite_Mx", description: "倾斜测试时，夹具+卫星Y向力矩" },
        { field: "V_CLAMP_SATELLITE_MZ", title: "V_ClampSatellite_Mz", description: "倾斜测试时，夹具+卫星Z向力矩" },
        { field: "V_CLAMP_SAT_BAL_ANGLE", title: "V_ClampSatellite_BalanceAngle", description: "倾斜测试时，夹具+卫星配平角度" },
        { field: "V_CLAMP_SAT_BAL_MOMENT", title: "V_ClampSatellite_BalanceMoment", description: "倾斜测试时，夹具+卫星配平力矩" }
    ]
];
var momentTableCols = [
    [
        { field: "TEST_ID", title: "测试编号", sort: true, description: "", minWidth: 120, overflow: 'tips' },
        { field: "PRODUCT_MODEL", title: "产品型号", description: "", minWidth: 120, overflow: 'tips' },
        { field: "PRODUCT_BATCH", title: "产品批次", description: "", minWidth: 120, overflow: 'tips' },
        { field: "PRODUCT_NAME", title: "产品名称", description: "", minWidth: 150, overflow: 'tips' },
        { field: "OPERATOR1", title: "操作员1", description: "", minWidth: 120, overflow: 'tips' },
        { field: "OPERATOR2", title: "操作员2", description: "", minWidth: 120, overflow: 'tips' },
        { field: "OPERATOR3", title: "操作员3", description: "", minWidth: 120, overflow: 'tips' },
        { field: "TEST_DATE", title: "测试日期", sort: true, description: "", minWidth: 150, overflow: 'tips' },
        { field: "TEST_PERIOD_COUNT", title: "TestPeriodCount", description: "空载平均周期" },
        { field: "TEST_TIMES", title: "TestTimes", description: "" },
        { field: "PLAT_TAV", title: "PlatTAV", description: "夹具平均周期" },
        { field: "CLAMP_TAV_X", title: "ClampTAV-X", description: "夹具和卫星平均周期" },
        { field: "J0_KVALUE_X", title: "J0-KValue-X", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_X", title: "ClmpSatI-X", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_X", title: "ClampAndSatTAV-X", description: "夹具和卫星计算时扭摆系数" },
        { field: "JD_KVALUE_X", title: "JD-KValue-X", description: "夹具和卫星转动惯量" },
        { field: "CLAMP_AND_SAT_I_X", title: "ClampAndSatSatI-X", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_X", title: "SatI-X", description: "卫星转动惯量" },
        { field: "CLAMP_TAV_Y", title: "ClampTAV-Y", description: "夹具平均周期" },
        { field: "J0_KVALUE_Y", title: "J0-KValue-Y", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_Y", title: "ClmpSatI-Y", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_Y", title: "ClampAndSatTAV-Y", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_Y", title: "JD-KValue-Y", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_Y", title: "ClampAndSatSatI-Y", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_Y", title: "SatI-Y", description: "卫星转动惯量" },
        { field: "CLAMP_TAV_Z", title: "ClampTAV-Z", description: "夹具平均周期" },
        { field: "J0_KVALUE_Z", title: "J0-KValue-Z", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_Z", title: "ClmpSatI-Z", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_Z", title: "ClampAndSatTAV-Z", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_Z", title: "JD-KValue-Z", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_Z", title: "ClampAndSatSatI-Z", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_Z", title: "SatI-Z", description: "卫星转动惯量" },
        { field: "WORK_NUMBER", title: "Work_Number", description: "" },
        { field: "CLAMP_TAV_STATU1", title: "ClampTAV-Statu1", description: "夹具平均周期" },
        { field: "J0_KVALUE_STATU1", title: "J0-KValue-Statu1", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_STATU1", title: "ClmpSatI-Statu1", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_STATU1", title: "ClampAndSatTAV-Statu1", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_STATU1", title: "JD-KValue-Statu1", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_STATU1", title: "ClampAndSatSatI-Statu1", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_STATU1", title: "SatI-Statu1", description: "卫星转动惯量" },
        { field: "CLAMP_TAV_STATU2", title: "ClampTAV-Statu2", description: "夹具平均周期" },
        { field: "J0_KVALUE_STATU2", title: "J0-KValue-Statu2", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_STATU2", title: "ClmpSatI-Statu2", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_STATU2", title: "ClampAndSatTAV-Statu2", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_STATU2", title: "JD-KValue-Statu2", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_STATU2", title: "ClampAndSatSatI-Statu2", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_STATU2", title: "SatI-Statu2", description: "卫星转动惯量" },
        { field: "CLAMP_TAV_STATU3", title: "ClampTAV-Statu3", description: "夹具平均周期" },
        { field: "J0_KVALUE_STATU3", title: "J0-KValue-Statu3", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_STATU3", title: "ClmpSatI-Statu3", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_STATU3", title: "ClampAndSatTAV-Statu3", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_STATU3", title: "JD-KValue-Statu3", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_STATU3", title: "ClampAndSatSatI-Statu3", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_STATU3", title: "SatI-Statu3", description: "卫星转动惯量" },
        { field: "CLAMP_TAV_STATU4", title: "ClampTAV-Statu4", description: "夹具平均周期" },
        { field: "J0_KVALUE_STATU4", title: "J0-KValue-Statu4", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_STATU4", title: "ClmpSatI-Statu4", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_STATU4", title: "ClampAndSatTAV-Statu4", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_STATU4", title: "JD-KValue-Statu4", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_STATU4", title: "ClampAndSatSatI-Statu4", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_STATU4", title: "SatI-Statu4", description: "卫星转动惯量" },
        { field: "CLAMP_TAV_STATU5", title: "ClampTAV-Statu5", description: "夹具平均周期" },
        { field: "J0_KVALUE_STATU5", title: "J0-KValue-Statu5", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_STATU5", title: "ClmpSatI-Statu5", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_STATU5", title: "ClampAndSatTAV-Statu5", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_STATU5", title: "JD-KValue-Statu5", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_STATU5", title: "ClampAndSatSatI-Statu5", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_STATU5", title: "SatI-Statu5", description: "卫星转动惯量" },
        { field: "SAT_ANGLE", title: "Sat-Angle", description: "卫星倾倒角度" },
        { field: "CLAMP_TAV_STATU6", title: "ClampTAV-Statu6", description: "夹具平均周期" },
        { field: "J0_KVALUE_STATU6", title: "J0-KValue-Statu6", description: "夹具计算时扭摆系数" },
        { field: "CLMP_SAT_I_STATU6", title: "ClmpSatI-Statu6", description: "夹具转动惯量" },
        { field: "CLAMP_AND_SAT_TAV_STATU6", title: "ClampAndSatTAV-Statu6", description: "夹具和卫星平均周期" },
        { field: "JD_KVALUE_STATU6", title: "JD-KValue-Statu6", description: "夹具和卫星计算时扭摆系数" },
        { field: "CLAMP_AND_SAT_I_STATU6", title: "ClampAndSatSatI-Statu6", description: "夹具和卫星转动惯量" },
        { field: "SAT_I_STATU6", title: "SatI-Statu6", description: "卫星转动惯量" },
        { field: "ROTATE_ANGLE_BATE", title: "旋转角度bate", description: "卫星倾倒角度" }
    ]
];

var calculationTableCols = [
    [
        { field: "TEST_ID", title: "测试编号", sort: true, description: "", minWidth: 120, overflow: 'tips' },
        { field: "PRODUCT_MODEL", title: "产品型号", description: "", minWidth: 120, overflow: 'tips' },
        { field: "TEST_DATE", title: "测试日期", sort: true, description: "", minWidth: 150, overflow: 'tips' },
        { field: "XC_TEST", title: "质心Xctest", description: "理论质心坐标系质心坐标X", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.XC_TEST + '">' + d.XC_TEST + '</div>'; } },
        { field: "YC_TEST", title: "质心Yctest", description: "理论质心坐标系质心坐标Y", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.YC_TEST + '">' + d.YC_TEST + '</div>'; } },
        { field: "ZC_TEST", title: "质心Zctest", description: "理论质心坐标系质心坐标Z", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.ZC_TEST + '">' + d.ZC_TEST + '</div>'; } },
        { field: "XC_SAT", title: "质心Xcsat", description: "卫星坐标系质心坐标X", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.XC_SAT + '">' + d.XC_SAT + '</div>'; } },
        { field: "YC_SAT", title: "质心Ycsat", description: "卫星坐标系质心坐标Y", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.YC_SAT + '">' + d.YC_SAT + '</div>'; } },
        { field: "ZC_SAT", title: "质心Zcsat", description: "卫星坐标系质心坐标Z", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.ZC_SAT + '">' + d.ZC_SAT + '</div>'; } },
        { field: "IX_TEST", title: "转动惯量Ixtest", description: "测试坐标系Ix", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IX_TEST + '">' + d.IX_TEST + '</div>'; } },
        { field: "IY_TEST", title: "转动惯量Iytest", description: "测试坐标系Iy", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IY_TEST + '">' + d.IY_TEST + '</div>'; } },
        { field: "IZ_TEST", title: "转动惯量Iztest", description: "测试坐标系Iz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IZ_TEST + '">' + d.IZ_TEST + '</div>'; } },
        { field: "IXY_TEST", title: "惯性积Ixytest", description: "测试坐标系Ixy", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IXY_TEST + '">' + d.IXY_TEST + '</div>'; } },
        { field: "IXZ_TEST", title: "惯性积Ixztest", description: "测试坐标系Ixz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IXZ_TEST + '">' + d.IXZ_TEST + '</div>'; } },
        { field: "IYZ_TEST", title: "惯性积Iyztest", description: "测试坐标系Iyz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IYZ_TEST + '">' + d.IYZ_TEST + '</div>'; } },
        { field: "IX_WCENT", title: "转动惯量IxWcent", description: "质心坐标系Ix", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IX_WCENT + '">' + d.IX_WCENT + '</div>'; } },
        { field: "IY_WCENT", title: "转动惯量IyWcent", description: "质心坐标系Iy", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IY_WCENT + '">' + d.IY_WCENT + '</div>'; } },
        { field: "IZ_WCENT", title: "转动惯量IzWcent", description: "质心坐标系Iz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IZ_WCENT + '">' + d.IZ_WCENT + '</div>'; } },
        { field: "IXY_WCENT", title: "惯性积IxyWcent", description: "质心坐标系Ixy", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IXY_WCENT + '">' + d.IXY_WCENT + '</div>'; } },
        { field: "IXZ_WCENT", title: "惯性积IxzWcent", description: "质心坐标系Ixz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IXZ_WCENT + '">' + d.IXZ_WCENT + '</div>'; } },
        { field: "IYZ_WCENT", title: "惯性积IyzWcent", description: "质心坐标系Iyz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IYZ_WCENT + '">' + d.IYZ_WCENT + '</div>'; } },
        { field: "XC", title: "质心Xc", description: "旋转中心为坐标原点计算得到的Xc", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.XC + '">' + d.XC + '</div>'; } },
        { field: "IX_BOTTOM", title: "转动惯量IxBottom", description: "旋转中心为坐标原点计算得到的Ix", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IX_BOTTOM + '">' + d.IX_BOTTOM + '</div>'; } },
        { field: "IY_BOTTOM", title: "转动惯量IyBottom", description: "旋转中心为坐标原点计算得到的Iy", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IY_BOTTOM + '">' + d.IY_BOTTOM + '</div>'; } },
        { field: "IZ_BOTTOM", title: "转动惯量IzBottom", description: "旋转中心为坐标原点计算得到的Iz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IZ_BOTTOM + '">' + d.IZ_BOTTOM + '</div>'; } },
        { field: "IXY_BOTTOM", title: "惯性积IxyBottom", description: "旋转中心为坐标原点计算得到的Ixy", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IXY_BOTTOM + '">' + d.IXY_BOTTOM + '</div>'; } },
        { field: "IXZ_BOTTOM", title: "惯性积IxzBottom", description: "旋转中心为坐标原点计算得到的Ixz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IXZ_BOTTOM + '">' + d.IXZ_BOTTOM + '</div>'; } },
        { field: "IYZ_BOTTOM", title: "惯性积IyzBottom", description: "旋转中心为坐标原点计算得到的Iyz", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.IYZ_BOTTOM + '">' + d.IYZ_BOTTOM + '</div>'; } },
        { field: "ROTATE_ANGLE", title: "倾倒角度RotatAngle", description: "倾倒角度", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.ROTATE_ANGLE + '">' + d.ROTATE_ANGLE + '</div>'; } },
        { field: "WEIGHT", title: "卫星质量Weight", description: "卫星质量", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.WEIGHT + '">' + d.WEIGHT + '</div>'; } },
        { field: "TEST_XC", title: "testXc", description: "测试坐标系（两轴交点坐标系）下X坐标", minWidth: 150, overflow: 'tips', templet: function (d) { return '<div title="' + d.TEST_XC + '">' + d.TEST_XC + '</div>'; } }
    ]
];

var calibrationTableCols = [
    [
        { field: "ID", title: "编号", description: "", minWidth: 120, overflow: 'tips' },
        { field: "SENSOR_DISTANCE_TO_ORIGIN", title: "SD_传感器到原点距离", description: "", minWidth: 150 },
        { field: "CALIBRATION_LEVEL", title: "SD_标定等级", description: "", minWidth: 120 },
        { field: "PLATFORM_WEIGHT", title: "SD_平台重量", description: "", minWidth: 120 },
        { field: "PLATFORM_P1_COEF", title: "SD_平台P1系数", description: "", minWidth: 120 },
        { field: "PLATFORM_P2_COEF", title: "SD_平台P2系数", description: "", minWidth: 120 },
        { field: "PLATFORM_P3_COEF", title: "SD_平台P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT1", title: "SD_砝码1重量", description: "", minWidth: 120 },
        { field: "WEIGHT1_P1_COEF", title: "SD_砝码1P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT1_P2_COEF", title: "SD_砝码1P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT1_P3_COEF", title: "SD_砝码1P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT2", title: "SD_砝码2重量", description: "", minWidth: 120 },
        { field: "WEIGHT2_P1_COEF", title: "SD_砝码2P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT2_P2_COEF", title: "SD_砝码2P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT2_P3_COEF", title: "SD_砝码2P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT3", title: "SD_砝码3重量", description: "", minWidth: 120 },
        { field: "WEIGHT3_P1_COEF", title: "SD_砝码3P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT3_P2_COEF", title: "SD_砝码3P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT3_P3_COEF", title: "SD_砝码3P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT4", title: "SD_砝码4重量", description: "", minWidth: 120 },
        { field: "WEIGHT4_P1_COEF", title: "SD_砝码4P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT4_P2_COEF", title: "SD_砝码4P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT4_P3_COEF", title: "SD_砝码4P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT5", title: "SD_砝码5重量", description: "", minWidth: 120 },
        { field: "WEIGHT5_P1_COEF", title: "SD_砝码5P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT5_P2_COEF", title: "SD_砝码5P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT5_P3_COEF", title: "SD_砝码5P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT6", title: "SD_砝码6重量", description: "", minWidth: 120 },
        { field: "WEIGHT6_P1_COEF", title: "SD_砝码6P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT6_P2_COEF", title: "SD_砝码6P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT6_P3_COEF", title: "SD_砝码6P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT7", title: "SD_砝码7重量", description: "", minWidth: 120 },
        { field: "WEIGHT7_P1_COEF", title: "SD_砝码7P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT7_P2_COEF", title: "SD_砝码7P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT7_P3_COEF", title: "SD_砝码7P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT8", title: "SD_砝码8重量", description: "", minWidth: 120 },
        { field: "WEIGHT8_P1_COEF", title: "SD_砝码8P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT8_P2_COEF", title: "SD_砝码8P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT8_P3_COEF", title: "SD_砝码8P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT9", title: "SD_砝码9重量", description: "", minWidth: 120 },
        { field: "WEIGHT9_P1_COEF", title: "SD_砝码9P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT9_P2_COEF", title: "SD_砝码9P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT9_P3_COEF", title: "SD_砝码9P3系数", description: "", minWidth: 120 },
        { field: "WEIGHT10", title: "SD_砝码10重量", description: "", minWidth: 120 },
        { field: "WEIGHT10_P1_COEF", title: "SD_砝码10P1系数", description: "", minWidth: 120 },
        { field: "WEIGHT10_P2_COEF", title: "SD_砝码10P2系数", description: "", minWidth: 120 },
        { field: "WEIGHT10_P3_COEF", title: "SD_砝码10P3系数", description: "", minWidth: 120 },
        { field: "PLATFORM_P1_CODE", title: "SD_平台P1码值", description: "", minWidth: 120 },
        { field: "PLATFORM_P2_CODE", title: "SD_平台P2码值", description: "", minWidth: 120 },
        { field: "PLATFORM_P3_CODE", title: "SD_平台P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT1_P1_CODE", title: "SD_砝码1P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT1_P2_CODE", title: "SD_砝码1P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT1_P3_CODE", title: "SD_砝码1P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT2_P1_CODE", title: "SD_砝码2P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT2_P2_CODE", title: "SD_砝码2P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT2_P3_CODE", title: "SD_砝码2P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT3_P1_CODE", title: "SD_砝码3P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT3_P2_CODE", title: "SD_砝码3P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT3_P3_CODE", title: "SD_砝码3P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT4_P1_CODE", title: "SD_砝码4P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT4_P2_CODE", title: "SD_砝码4P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT4_P3_CODE", title: "SD_砝码4P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT5_P1_CODE", title: "SD_砝码5P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT5_P2_CODE", title: "SD_砝码5P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT5_P3_CODE", title: "SD_砝码5P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT6_P1_CODE", title: "SD_砝码6P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT6_P2_CODE", title: "SD_砝码6P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT6_P3_CODE", title: "SD_砝码6P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT7_P1_CODE", title: "SD_砝码7P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT7_P2_CODE", title: "SD_砝码7P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT7_P3_CODE", title: "SD_砝码7P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT8_P1_CODE", title: "SD_砝码8P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT8_P2_CODE", title: "SD_砝码8P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT8_P3_CODE", title: "SD_砝码8P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT9_P1_CODE", title: "SD_砝码9P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT9_P2_CODE", title: "SD_砝码9P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT9_P3_CODE", title: "SD_砝码9P3码值", description: "", minWidth: 120 },
        { field: "WEIGHT10_P1_CODE", title: "SD_砝码10P1码值", description: "", minWidth: 120 },
        { field: "WEIGHT10_P2_CODE", title: "SD_砝码10P2码值", description: "", minWidth: 120 },
        { field: "WEIGHT10_P3_CODE", title: "SD_砝码10P3码值", description: "", minWidth: 120 },
        { field: "CALIBRATION_DATE", title: "SD_标定日期", description: "", minWidth: 150, sort: true },
        { field: "MD_EIGHT_POINT_CONCLUSION", title: "MD_八点标定结论", description: "", minWidth: 150 },
        { field: "EIGHT_POINT_CALIB_DATE", title: "MD_八点标定日期", description: "", minWidth: 150 },
        { field: "MD_MASS_CORRECT_CONCLUSION", title: "MD_质量校正结论", description: "", minWidth: 150 },
        { field: "MASS_CORRECTION_DATE", title: "MD_质量校正日期", description: "", minWidth: 150 },
        { field: "PLATFORM_P1_WEIGHT", title: "Plat_P1重量", description: "", minWidth: 120 },
        { field: "PLATFORM_P2_WEIGHT", title: "Plat_P2重量", description: "", minWidth: 120 },
        { field: "PLATFORM_P3_WEIGHT", title: "Plat_P3重量", description: "", minWidth: 120 },
        { field: "PLATFORM_TOTAL_WEIGHT", title: "Plat_PTotal重量", description: "", minWidth: 120 }
    ]
];

var momentCalibrationTableCols = [
    [
        { field: "ID", title: "编号", description: "", minWidth: 120, overflow: 'tips' },
        { field: "MEASURE_CYCLE_NUM", title: "测量周期数", description: "", minWidth: 120 },
        { field: "MEASURE_COUNT", title: "测量次数", description: "", minWidth: 120 },
        { field: "PLAT_T1", title: "PlatT1", description: "空载周期1", minWidth: 120 },
        { field: "PLAT_T2", title: "PlatT2", description: "空载周期2", minWidth: 120 },
        { field: "PLAT_T3", title: "PlatT3", description: "空载周期3", minWidth: 120 },
        { field: "PLAT_T4", title: "PlatT4", description: "空载周期4", minWidth: 120 },
        { field: "PLAT_TA", title: "PlatTa", description: "空载平均周期", minWidth: 120 },
        { field: "STANDARD1_MI", title: "Standard1MI", description: "一级标定标准惯量值", minWidth: 150 },
        { field: "STANDARD1_T1", title: "Standard1T1", description: "一级标定周期1", minWidth: 120 },
        { field: "STANDARD1_T2", title: "Standard1T2", description: "一级标定周期2", minWidth: 120 },
        { field: "STANDARD1_T3", title: "Standard1T3", description: "一级标定周期3", minWidth: 120 },
        { field: "STANDARD1_T4", title: "Standard1T4", description: "一级标定周期4", minWidth: 120 },
        { field: "STANDARD1_TA", title: "Standard1Ta", description: "一级标定平均周期", minWidth: 150 },
        { field: "STANDARD1_C", title: "Standard1C", description: "一级标定扭摆系数", minWidth: 150 },
        { field: "STANDARD2_MI", title: "Standard2MI", description: "二级标定标准惯量值", minWidth: 150 },
        { field: "STANDARD2_T1", title: "Standard2T1", description: "二级标定周期1", minWidth: 120 },
        { field: "STANDARD2_T2", title: "Standard2T2", description: "二级标定周期2", minWidth: 120 },
        { field: "STANDARD2_T3", title: "Standard2T3", description: "二级标定周期3", minWidth: 120 },
        { field: "STANDARD2_T4", title: "Standard2T4", description: "二级标定周期4", minWidth: 120 },
        { field: "STANDARD2_TA", title: "Standard2Ta", description: "二级标定平均周期", minWidth: 150 },
        { field: "STANDARD2_C", title: "Standard2C", description: "二级标定扭摆系数", minWidth: 150 },
        { field: "STANDARD3_MI", title: "Standard3MI", description: "三级标定标准惯量值", minWidth: 150 },
        { field: "STANDARD3_T1", title: "Standard3T1", description: "三级标定周期1", minWidth: 120 },
        { field: "STANDARD3_T2", title: "Standard3T2", description: "三级标定周期2", minWidth: 120 },
        { field: "STANDARD3_T3", title: "Standard3T3", description: "三级标定周期3", minWidth: 120 },
        { field: "STANDARD3_T4", title: "Standard3T4", description: "三级标定周期4", minWidth: 120 },
        { field: "STANDARD3_TA", title: "Standard3Ta", description: "三级标定平均周期", minWidth: 150 },
        { field: "STANDARD3_C", title: "Standard3C", description: "三级标定扭摆系数", minWidth: 150 },
        { field: "CALIBRATION_DATE", title: "标定日期", description: "", minWidth: 150, sort: true }
    ]
];