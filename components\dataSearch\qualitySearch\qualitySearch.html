<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../../img/favicon.ico">
	<link rel="stylesheet" href="../../../plugins/layui/css/layui.css" media="all">
	<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/contextMenu.css">
	<link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
	<link rel="stylesheet" href="../../../css/icon.css">

	<script src="../../../plugins/layui/layui.js"></script>
	<script src="../../../plugins/easyui/jquery.min.js"></script>
	<script src="../../../plugins/easyui/jquery.jsPlumb-1.7.5.js"></script>
	<script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
	<script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<script src="../../js/config/twxconfig.js"></script>
	<script src="../../js/util.js"></script>

	<!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
	<script type="text/javascript" src="../../js/logUtil.js"></script>
	<style>
		.fieldlabel {
			padding-left: 10px;
		}

		.table-row-a {
			color: blue;
			cursor: pointer;
		}

		.search {
			float: left;
			margin-left: 10px;
			margin-top: 10px;
			margin-bottom: 10px;
		}

		#product_list_table .datagrid-header {
			border-color: #000000;
			border-width: 1px 0 1px 0;
		}

		#product_list_table .datagrid-header td,
		#product_list_table .datagrid-body td {
			border-color: #000000;
		}

		#product_list_table .datagrid-header td {
			font-weight: 600;
		}

		#product_list_table .datagrid-header,
		#product_list_table .datagrid-td-rownumber {
			background-color: transparent !important;
		}

		.v-mult {
			margin: 20px 0px 10px 10px;
			height: 40px;
			width: 120px;
			padding: 0px 2px;
			overflow: hidden;
			font-size: 14px;
			text-align: center;
			background-color: #169BD5;
			color: white;
			cursor: pointer;
		}

		.v-mult-active {
			background-color: #FF9900;
		}

		/* path {
			stroke: #169BD5;
			fill: #169BD5;
			stroke-width: 2px;
		} */

		.v-mult .empty,
		.v-mult .text {
			display: inline-block;
			vertical-align: middle;
		}

		.v-mult .empty {
			height: 100%;
		}

		.layui-col-md2,
		.layui-col-md3 {
			min-height: 1px;
		}
	</style>
	<title>数据查询</title>
</head>
<body>
	<!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
	<!--[if lt IE 9]>
	  <script src="../../../plugins/html5/html5.min.js"></script>
	  <script src="../../../plugins/html5/respond.min.js"></script>
	<![endif]-->
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div data-options="region:'north'" style="height:45px;">
			<table style="margin:2px 10px;font-family: 微软雅黑;font-size: 14px;">
				<tr height="38px">
					<td class="fieldlabel" align="right">型号：</td>
					<td>
						<input class="easyui-combobox" id="product" data-options="editable:false" style="width:250px;">
					</td>
					<td class="fieldlabel" align="right">阶段：</td>
					<td>
						<input class="easyui-combobox" id="phase" data-options="editable:false" style="width:250px;">
					</td>
					<!-- <td>
						<button type="button" class="layui-btn layui-btn-sm  layui-btn-normal " style="margin-left: 10px;" onclick="searchTable()">
							<i class="layui-icon">&#xe615;</i> 搜索
						</button>
					</td> -->
				</tr>
			</table>
		</div>
		<div id="p" data-options="region:'west',split:true" title="产品节点" style="width:350px;padding:10px">
			<div id="bomMsg" style="color: red;">请选择型号和阶段！</div>
			<ul id="bomTree" class="ztree" style="display: none;"></ul>
		</div>
		<div data-options="region:'center'" title="过程节点">
			<div class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
				<div data-options="region:'north'" style="">
					<!-- <table style="margin:10px 10px;font-family: 微软雅黑;font-size: 14px;"> -->
					<!-- <tr height="38px"> -->
					<!-- <td class="fieldlabel1" align="right">专业：</td> -->
					<!-- <td> -->

					<!-- </td> -->
					<!-- <td class="fieldlabel2" align="right">过程：</td> -->
					<!-- <td> -->

					<!-- </td> -->
					<!-- </tr> -->
					<!-- </table> -->
					<div class="search">
						<div id="majorDiv" style="float: left;">专业：<input class="easyui-combobox" id="major" data-options="editable:false" style="width:150px;"></div>
						<div id="processDiv" style="float: left;margin-left: 10px; display: none;">过程：<input class="easyui-combobox" id="process" data-options="editable:false" style="width:200px;"></div>
					</div>
					<div id="productTypeDiv" class="search" style="display: none;margin-left:15px;margin-bottom: 10px; float: left;">
						质量数据类型：<input id="productType">
					</div>
				</div>
				<div data-options="region:'center'" id="product_list_table" style="height:60px;">
					<!-- <div style="height: 40px;"> -->
					<div id="msg" style="color: red;padding-left: 15px;padding-top: 15px;">请选择过程节点查询!</div>
					<div id="diagramContainer" style="position: absolute;width: 100%;height: 1px;">
					</div>
					<!-- </div> -->
					<div id="secondTableDiv" style="position: absolute;width: 100%;">
						<!-- 显示二级表表格 -->
						<div id="secondTable" data-options="border:false"></div>
					</div>

					<!-- 显示三级表excel -->
					<div id="threeExcel" style="position: absolute;width: 100%;"></div>

					<div id="otherTableDiv" style="position: absolute;width: 100%;">
						<!-- 显示其他质量表表格 -->
						<div id="otherTable" data-options="border:false"></div>
					</div>

				</div>
			</div>
		</div>
	</div>
</body>
<script src="../../../plugins/index/jquery.fileDownload.js"></script>
<script src="../tableCol.js"></script>
<script src="../dataSearchTable.js"></script>
<script src="BomTree.js"></script>
<script src="DataTable.js"></script>
<script src="qualitySearch.js"></script>
