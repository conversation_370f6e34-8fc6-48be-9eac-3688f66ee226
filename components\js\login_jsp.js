layui.extend({
	utils: 'components/js/utils'
}).use(['form', 'utils', 'util'], function() {
	var form = layui.form,
		utils = layui.utils,
		util = layui.util;
	form.render();

	var loginSucessAfter = function(data) {
		sessionStorage.clear();
		//记录信息到sessionStorage
		st.s('fullname', data.fullname);
		st.s('username', data.username);
		st.s('roleid', data.roleid);
		st.s('secLevel', data.secLevel);
		st.s('rolename', data.rolename);
		st.s('pwd', data.pwd);
		st.s('previewUrl', data.previewUrl);
		st.s('modelList', data.modelList);
		st.s('updatePwdTime', data.updatePwdTime);
		st.s('myIp', causerip);
		st.s('userIp', causerip);
		var userIp = data.ip;
		if (causerip != '') {
			if (userIp != undefined && userIp != null && userIp != '') {
				if (userIp != causerip) {
					layer.alert('用户ip与本机ip不一致，禁止登录!', {
						icon: 2
					});
					return false;
				}
			}
		}

		//ThingWorx会话认证
		utils.twxconnect(function() {
			logRecord('登录', '登录', 1);
			sessionStorage.setItem('twxsession', true);
			location.href = "index.html?_dc=" + (new Date().getTime());
		});
	};
	form.on('submit(LAY-user-login-submit)', function(data) {
		caLogin(causername);
		return false;
	});

	var caLogin = function(username) {
		//进行ThingWorx和用户的认证
		var cb_success = function(data, textStatus, jqXHR) {
			if (data.success === false) {
				layer.msg(data.message, {
					icon: 2,
					anim: 6
				});
				logRecord('登录', data.message + '登录用户名:' + username, 0);
				return;
			}
			loginSucessAfter(data);
		};
		var cb_error = function(xhr, textStatus, errorThrown) {
			layer.msg('登录失败', {
				icon: 2,
				anim: 6
			});
			logRecord('登录', '登录失败', 0);
		};
		//同步方法调用
		utils.twxajax('Thing.UserLogin', 'caLogin', {
			username: username
		}, cb_success, cb_error);
	}
	caLogin(causername);
});