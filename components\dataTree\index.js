var contextMenuWidth = 115;

$(function () {
    loadTree();
    var bomTree = new BomTree(false, true);
    bomTree.loadTree();
})

//加载树结构
function loadTree() {
    var cb_success = function (data) {
        var datas = data.rows;
        if (datas.length > 0) {
            datas = dealDataIcons(datas);
            datas = dealDataNodeName(datas);
            treeSetting.callback.onClick = function (event, treeId, treeNode) {
                clickNode(treeNode);
            };
            treeSetting.callback.onExpand = function (event, treeId, treeNode) {
                loadTreeMenu();
            };
            var allfuns = sessionStorage.getItem('funcids');
            var funcArr = allfuns.split(',');
            if (!contains(funcArr, 'tree_moveNode')) {
                //禁止拖拽
                treeSetting.callback.beforeDrag = function () {
                    return false;
                };
                treeSetting.callback.beforeDrop = function () {
                    return false;
                };
            }
            ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
            var nodes = ztreeObj.getNodes();
            for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
                ztreeObj.expandNode(nodes[i], true, false, true);
            }
            loadTreeMenu();
        }
    };
    //使用ajax进行异步加载Tree
    twxAjax('Thing.Fn.DataPackage', 'QueryDataPackageTreeRoot', '', true, cb_success);
}

function clickNode(treeNode) {
    if (reloadRefDPTable && typeof reloadRefDPTable == 'function') {
        reloadRefDPTable(treeNode.TREEID);
    }
    // twxAjax('Thing.Fn.DataPackage', 'QueryDataPkg', {
    // 	refTreeId: treeNode.TREEID
    // }, true, function(data) {
    // 	reloadRefDPTable(treeNode.TREEID);
    // });
    // // alert(treeNode.TREEID);
    // var selectedTab = $('#root_layout_tabs').tabs('getSelected');
    // var tabId = selectedTab.panel('options').name;
    // reloadTable(tabId);

    //型号权限233
    if (treeNode.userEditable) {
        $("#design_list_table_tb").show();
        $("#craft_list_table_tb").show();
        $("#processcontrol_list_table_tb").show();
        $("#quanlitycontrol_list_table_tb").show();

    } else {
        $("#design_list_table_tb").hide();
        $("#craft_list_table_tb").hide();
        $("#processcontrol_list_table_tb").hide();
        $("#quanlitycontrol_list_table_tb").hide();
    }
}

//加载树节点右键菜单
function loadTreeMenu() {
    loadTreeEditSetting(); //加载菜单之前先执行权限设置
    $("#dpTree a").each(function (i, n) {
        var menu = [];
        var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
        //型号权限233
        //管理员具有所有权限 2020-03-11
        if (!node.userEditable) {
            $(n).off("conte-xtmenu");
            return;
        }
        menu = getNodeMenu(node);
        if (menu.length != 0) {
            $(n).contextMenu({
                width: contextMenuWidth,
                menu: menu,
                target: function (ele) {
                    var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
                    ztreeObj.selectNode(node, false, true);
                }
            });
        }
    });
}

function isAdmin() {
    var roles = sessionStorage.rolename ? sessionStorage.rolename.split(',') : [];
    for (var i = 0; i < roles.length; i++) {
        if (roles[i] == "质量分管领导") {
            return true;
        }
    }
    return false;
}

//加载树节点右键菜单
function loadTreeEditSetting() {
    var editingMap = {};
    $("#dpTree a").each(function (i, n) {
        var menu = [];
        var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
        //1层级是根（产品数据包），2层级是型号，这层级不应该继承上层级的是否可编辑属性
        if (isAdmin() || node.NODETYPE == 'root') {
            editingMap[node.TREEID] = true;
            node.userEditable = true;
        } else if (node.NODETYPE == "product") {
            if (!node.CODE) {
                editingMap[node.TREEID] = true;
                node.userEditable = true;
            } else if (!sessionStorage.modelList) {
                editingMap[node.TREEID] = false;
                node.userEditable = false;
            } else {
                var arrModel = sessionStorage.modelList.split(',');
                var b = contains(arrModel, node.CODE);
                editingMap[node.TREEID] = b;
                node.userEditable = b;
            }
        } else {
            editingMap[node.TREEID] = editingMap[node.PARENTID];
            node.userEditable = editingMap[node.PARENTID];
        }
    });
}

//校验同一节点下子节点是否重复
function checkNodeNameIsRepeat(parentId, nodeName, oldNodeName) {
    var flag = false;
    var cb_success = function (data) {
        var ds = data.rows;
        for (var i = 0; i < ds.length; i++) {
            var d = ds[i];
            if (nodeName == d.NODENAME) {
                flag = true;
                break;
            }
        }
        if (nodeName == oldNodeName) {
            flag = false;
        }
    };
    twxAjax('Thing.Fn.DataPackage', 'QueryDataPackageTree', {
        TREEID: parentId
    },
        false, cb_success); //同步请求校验
    return flag;
}

//操作完节点之后重新加载节点
function reloadTree(refrushId, selId) {
    if (selId) {

    } else {
        selId = refrushId;
    }
    var refrushTreeNode = ztreeObj.getNodeByParam("TREEID", refrushId, null);
    if (!refrushTreeNode.ISPARENT) {
        refrushTreeNode.ISPARENT = true;
        ztreeObj.updateNode(refrushTreeNode);
    }
    ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false,
        function () {
            ztreeObj.expandNode(refrushTreeNode, true, false, true);
            var newSelNode = ztreeObj.getNodeByParam("TREEID", selId, null);
            ztreeObj.selectNode(newSelNode, false, true);
            loadTreeMenu();
            clickNode(newSelNode);
        });
}

//获取节点右键菜单数组
function getNodeMenu(treeNode) {
    var imgSuffix = '../dataTree/';
    var level = treeNode.level;
    var menu = [];

    var addFolderMenu = {
        text: "添加分类",
        icon: imgSuffix + 'images/add1.png',
        callback: function () {
            layui.use(['layer'],
                function () {
                    var layer = layui.layer;
                    layer.prompt({
                        title: '请输入分类名称'
                    },
                        function (nodeName, index1, elem) {
                            if (nodeName != null && nodeName != "") {
                                var parentId = treeNode.TREEID;
                                var parentName = treeNode.NODENAME;
                                if (!checkNodeNameIsRepeat(parentId, nodeName)) {
                                    var nodeLevel = 1; //代表分类  与产品结构树节点一致
                                    var cb_success = function (res) {
                                        if (res.success) {
                                            layer.closeAll();
                                            logRecord('新增', '策划构建-数据包结构树-(ID：' + parentId + '、名称：' + parentName + ') 下新增分类名称为(' + nodeName + ')', 1);
                                            layer.msg(res.msg);
                                            if (!treeNode.ISPARENT) {
                                                treeNode.ISPARENT = true;
                                                ztreeObj.updateNode(treeNode);
                                            }
                                            reloadTree(treeNode.TREEID);
                                        } else {
                                            logRecord('新增', '策划构建-数据包结构树-(ID：' + parentId + '、名称：' + parentName + ') 下新增分类名称为(' + nodeName + ')', 0);
                                            layer.alert(res.msg, {
                                                icon: 2
                                            });
                                        }
                                    };
                                    var cb_error = function () {
                                        layer.closeAll();
                                        logRecord('新增', '策划构建-数据包结构树-(ID：' + parentId + '、名称：' + parentName + ') 下新增分类名称为(' + nodeName + ')', 0);
                                        layer.alert('添加失败！', {
                                            icon: 2
                                        });
                                    };
                                    twxAjax('Thing.Fn.DataPackage', 'AddFolder', {
                                        parentId: parentId,
                                        nodeName: nodeName,
                                        nodeLevel: nodeLevel
                                    },
                                        true, cb_success, cb_error);
                                } else {
                                    layer.msg('该分类名已存在！')
                                }
                            } else {
                                layer.msg('请输入分类名称！')
                            }
                        });
                });
        }
    };

    var setModelTagMenu = {
        text: "设置标签",
        icon: imgSuffix + 'images/tag.png',
        callback: function () {
            layui.use(['layer', 'form'],
                function () {
                    var layer = layui.layer,
                        form = layui.form;
                    var addTpl = '<form class="layui-form" action="" lay-filter="selectTagForm">\
							<div class="layui-form-item">\
								<label class="fieldlabel1 layui-form-label">型号名称:</label>\
								<div class="layui-input-block">\
									<input type="text" readOnly="readOnly" value="' + treeNode.NODENAME + '" class="layui-input">\
								</div>\
							</div>\
							<div class="layui-form-item" style="">\
								<label class="fieldlabel1 layui-form-label">标签名称:</label>\
								<div class="layui-input-block">\
									<select name="tagId" id="tag" lay-filter="tag" lay-verify="required">\
										<option value=""></option>\
									</select>\
								</div>\
							</div>\
							<div class="layui-form-item" style="display:none;">\
								<div class="layui-input-block">\
									<div class="layui-footer">\
										<button class="layui-btn" id="selectTagSubmit" lay-submit="" lay-filter="selectTagSubmit">确定</button>\
									</div>\
								</div>\
							</div>\
						</form>';
                    layer.open({
                        title: '设置标签',
                        type: 1,
                        fixed: false,
                        maxmin: false,
                        anim: false,
                        openDuration: 200,
                        isOutAnim: false,
                        closeDuration: 200,
                        shadeClose: false,
                        resize: false,
                        //不允许拉伸
                        area: ['500px', '220px'],
                        content: '<div id="selectTagContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                        btn: ['确定', '取消'],
                        yes: function () {
                            $('#selectTagSubmit').click();
                        },
                        btn2: function () {
                            return true;
                        },
                        success: function () {
                            $("#selectTagContent").append(addTpl);
                            twxAjax('Thing.Fn.DataPackage', 'QueryTag', {},
                                false,
                                function (res) {
                                    if (res.success) {
                                        for (var i = 0; i < res.data.length; i++) {
                                            var tagName = res.data[i]['FUNC_NAME'];
                                            var tagId = res.data[i]['FUNC_BTN_ID'];
                                            if (tagId == treeNode.UNIQUECODE) {
                                                $("#tag").append('<option selected value="' + tagId + '">' + tagName + '</option>');
                                            } else {
                                                $("#tag").append('<option value="' + tagId + '">' + tagName + '</option>');
                                            }
                                        }
                                    }

                                });
                        }
                    });
                    form.render(null, 'selectTagForm');
                    form.on('submit(selectTagSubmit)',
                        function (data) {
                            var param = data.field;
                            param.treeId = treeNode.TREEID;
                            twxAjax('Thing.Fn.DataPackage', 'SelectTag', param, false,
                                function (res) {
                                    if (res.success) {
                                        layer.closeAll();
                                        layer.msg(res.msg);
                                        treeNode.UNIQUECODE = param.tagId;
                                        ztreeObj.updateNode(treeNode);
                                    } else {
                                        layer.alert(res.msg, {
                                            icon: 2
                                        })
                                    }
                                })
                        });
                    return false;
                });
        }
    };

    var setAITMenu = {
        text: "设置AIT",
        icon: imgSuffix + 'images/tag.png',
        callback: function () {
            twxAjax('Thing.Fn.AitScreen', 'QueryModelPhaseAIT', {
                treeId: treeNode.TREEID
            }, true, function (res) {
                if (res.success) {
                    var aitNodes = res.data.aitNodes;
                    var modelPhaseName = res.data.modelPhaseName;
                    layui.use(['layer', 'form'],
                        function () {
                            var layer = layui.layer,
                                form = layui.form;
                            var addTpl = '<form class="layui-form" action="" lay-filter="selectAITForm">\
									<div class="layui-form-item">\
										<label style="width:120px" class="fieldlabel1 layui-form-label">型号阶段名称:</label>\
										<div class="layui-input-block" style="margin-left:120px">\
											<input type="text" readOnly="readOnly" value="' + modelPhaseName + '" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item" style="">\
										<label style="width:120px" class="fieldlabel1 layui-form-label">AIT过程名称:</label>\
										<div class="layui-input-block" style="margin-left:120px">\
											<select name="aitNode" id="aitNode" lay-filter="aitNode" lay-verify="required">\
												<option value=""></option>\
											</select>\
										</div>\
									</div>\
									<div class="layui-form-item" style="display:none;">\
										<div class="layui-input-block">\
											<div class="layui-footer">\
												<button class="layui-btn" id="selectAITSubmit" lay-submit="" lay-filter="selectAITSubmit">确定</button>\
											</div>\
										</div>\
									</div>\
								</form>';
                            layer.open({
                                title: '设置AIT过程',
                                type: 1,
                                fixed: false,
                                maxmin: false,
                                anim: false,
                                openDuration: 200,
                                isOutAnim: false,
                                closeDuration: 200,
                                shadeClose: false,
                                resize: false,
                                //不允许拉伸
                                area: ['500px', '220px'],
                                content: '<div id="selectAITContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                                btn: ['确定', '取消'],
                                yes: function () {
                                    $('#selectAITSubmit').click();
                                },
                                btn2: function () {
                                    return true;
                                },
                                success: function () {
                                    $("#selectAITContent").append(addTpl);
                                    for (var i = 0; i < aitNodes.length; i++) {
                                        var aitName = aitNodes[i];
                                        if (aitName == treeNode.CURRENT_AIT_NODE) {
                                            $("#aitNode").append('<option selected value="' + aitName + '">' + aitName + '</option>');
                                        } else {
                                            $("#aitNode").append('<option value="' + aitName + '">' + aitName + '</option>');
                                        }
                                    }
                                }
                            });
                            form.render(null, 'selectAITForm');
                            form.on('submit(selectAITSubmit)',
                                function (data) {
                                    var param = data.field;
                                    param.treeId = treeNode.TREEID;
                                    twxAjax('Thing.Fn.AitScreen', 'SetModelPhaseCurrentAITNode', param, false,
                                        function (res) {
                                            if (res.success) {
                                                layer.closeAll();
                                                layer.msg(res.msg);
                                                treeNode.CURRENT_AIT_NODE = param.aitNode;
                                                ztreeObj.updateNode(treeNode);
                                            } else {
                                                layer.alert(res.msg, {
                                                    icon: 2
                                                })
                                            }
                                        })
                                });
                            return false;
                        });
                } else {
                    layer.alert(res.msg, {
                        icon: 2
                    });
                }
            }, function (xhr, textStatus, errorThrown) {
                layer.alert('请求出错！', {
                    icon: 2
                });
            });
        }
    };

    var selectFolderMenu = {
        text: "选择分类",
        icon: imgSuffix + 'images/folder1.png',
        callback: function () {
            layui.use(['layer', 'form'],
                function () {
                    var layer = layui.layer,
                        form = layui.form;
                    var addTpl = '<form class="layui-form" action="" lay-filter="selectFolderForm">\
							<div class="layui-form-item">\
								<label class="fieldlabel1 layui-form-label">型号名称:</label>\
								<div class="layui-input-block">\
									<input type="text" readOnly="readOnly" value="' + treeNode.NODENAME + '" class="layui-input">\
								</div>\
							</div>\
							<div class="layui-form-item" style="">\
								<label class="fieldlabel1 layui-form-label">分类名称:</label>\
								<div class="layui-input-block">\
									<select name="folder" id="folder" lay-filter="folder" lay-verify="required">\
										<option value=""></option>\
									</select>\
								</div>\
							</div>\
							<div class="layui-form-item" style="display:none;">\
								<div class="layui-input-block">\
									<div class="layui-footer">\
										<button class="layui-btn" id="selectFolderSubmit" lay-submit="" lay-filter="selectFolderSubmit">确定</button>\
									</div>\
								</div>\
							</div>\
						</form>';
                    layer.open({
                        title: '选择分类',
                        type: 1,
                        fixed: false,
                        maxmin: false,
                        anim: false,
                        openDuration: 200,
                        isOutAnim: false,
                        closeDuration: 200,
                        shadeClose: false,
                        resize: false,
                        //不允许拉伸
                        area: ['500px', '220px'],
                        content: '<div id="selectFolderContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                        btn: ['确定', '取消'],
                        yes: function () {
                            $('#selectFolderSubmit').click();
                            return false;
                        },
                        btn2: function () {
                            return true;
                        },
                        success: function () {
                            $("#selectFolderContent").append(addTpl);
                            twxAjax('Thing.Fn.DataPackage', 'QueryFolder', {},
                                false,
                                function (data) {
                                    for (var i = 0; i < data.rows.length; i++) {
                                        var folderName = data.rows[i]['NODENAME'];
                                        var folderId = data.rows[i]['TREEID'];
                                        if (folderName == treeNode.getParentNode().NODENAME) {
                                            $("#folder").append('<option selected value="' + folderId + '">' + folderName + '</option>');
                                        } else {
                                            $("#folder").append('<option value="' + folderId + '">' + folderName + '</option>');
                                        }
                                    }
                                });
                        }
                    });
                    form.render(null, 'selectFolderForm');
                    form.on('submit(selectFolderSubmit)',
                        function (data) {
                            var param = data.field;
                            var folderId = param.folder;
                            twxAjax('Thing.Fn.DataPackage', 'TransferNode', {
                                treeId: treeNode.TREEID,
                                newPId: param.folder
                            }, true, function (res) {
                                if (res.success) {
                                    layer.closeAll();
                                    var rootNode = ztreeObj.getNodesByParam("NODETYPE", "root", null)[0];
                                    ztreeObj.reAsyncChildNodes(rootNode, 'refresh', false, function () {
                                        ztreeObj.reAsyncChildNodes(ztreeObj.getNodeByParam("TREEID", folderId, null), 'refresh', false,
                                            function () {
                                                loadTreeMenu();
                                            });
                                    });

                                    layer.msg(res.msg)
                                } else {
                                    layer.alert(res.msg, {
                                        icon: 2
                                    });
                                }
                            }, function (xhr, textStatus, errorThrown) {
                                layer.alert('请求出错！', {
                                    icon: 2
                                });
                            });

                            // twxAjax('Thing.Fn.DataPackage', 'SelectFolder', param, false,
                            // 	function(data) {
                            // 		if (Number(data.rows[0].result) > 0) {
                            // 			layer.closeAll();
                            // 			layer.msg("成功");
                            // 			var rootNode = ztreeObj.getNodesByParam("NODETYPE", "root", null)[0];
                            // 			ztreeObj.reAsyncChildNodes(rootNode, 'refresh', false,
                            // 				function() {
                            // 					ztreeObj.reAsyncChildNodes(ztreeObj.getNodeByParam("TREEID", folderId, null), 'refresh', false,
                            // 						function() {
                            // 							loadTreeMenu();
                            // 						})
                            // 				});
                            // 		} else {
                            // 			layer.alert('失败！', {
                            // 				icon: 2
                            // 			})
                            // 		}
                            // 	})
                            return false;
                        });

                });
        }
    };
    var addNodeMenu = {
        text: "添加节点",
        icon: imgSuffix + 'images/add1.png',
        callback: function () {
            layui.use(['layer'],
                function () {
                    var layer = layui.layer;
                    layer.prompt({
                        title: '请输入新加的节点名称'
                    },
                        function (nodeName, index1, elem) {
                            if (nodeName != null && nodeName != "") {
                                var parentId = treeNode.TREEID;
                                var parentName = treeNode.NODENAME;
                                if (!checkNodeNameIsRepeat(parentId, nodeName)) {
                                    var nodeLevel = treeNode.NODELEVEL + 1;
                                    var cb_success = function (data) {
                                        layer.closeAll();
                                        logRecord('新增', '策划构建-数据包结构树-(ID：' + parentId + '、名称：' + parentName + ') 下新增节点名称为(' + nodeName + ')', 1);
                                        layer.msg('添加成功！');
                                        if (!treeNode.ISPARENT) {
                                            treeNode.ISPARENT = true;
                                            ztreeObj.updateNode(treeNode);
                                        }
                                        reloadTree(treeNode.TREEID);
                                    };
                                    var cb_error = function () {
                                        layer.closeAll();
                                        logRecord('新增', '策划构建-数据包结构树-(ID：' + parentId + '、名称：' + parentName + ') 下新增节点名称为(' + nodeName + ')', 0);
                                        layer.msg('添加失败！');
                                    };
                                    twxAjax('Thing.Fn.DataPackage', 'AddNode', {
                                        parentId: parentId,
                                        nodeName: nodeName,
                                        nodeLevel: nodeLevel
                                    },
                                        true, cb_success, cb_error);
                                } else {
                                    layer.msg('该节点已存在！')
                                }
                            } else {
                                layer.msg('请输入节点名称！')
                            }
                        });
                });
        }
    };
    var creatDataPackageMenu = {
        text: "创建数据包",
        icon: imgSuffix + 'images/add1.png',
        callback: function () {
            twxAjax('Thing.Fn.DataPackage', 'QueryDataPkg', {
                refTreeId: treeNode.TREEID
            },
                true,
                function (data) {
                    layui.use(['layer', 'form'],
                        function () {
                            var layer = layui.layer,
                                form = layui.form;
                            if (data.rows.length == 0) {
                                twxAjax('Thing.Fn.DataPackage', 'getDataPkgNumber', {
                                    treeId: treeNode.TREEID
                                },
                                    true,
                                    function (data) {
                                        var name = getNodeName(treeNode.NODENAME) + "数据包";
                                        var code = data.rows[0].result;
                                        var addTpl = '<form class="layui-form" action="" lay-filter="component-create-datapkg">\
									<input type="hidden" name="refTreeId" value="">\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">数据包名称:</label>\
										<div class="layui-input-block">\
											<input type="text" name="name" value="' + name + '" lay-verify="required" required autocomplete="off" placeholder="请输入数据包名称" style="width:330px" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">数据包编号:</label>\
										<div class="layui-input-block">\
											<input type="text" readOnly = "readOnly" value="' + code + '"  name="code" autocomplete="off" placeholder="请输入数据包编号" style="width:330px" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">备注:</label>\
										<div class="layui-input-block">\
											<textarea placeholder="请输入备注" name="remark" style="width:330px" style="resize:none" class="layui-textarea"></textarea>\
										</div>\
									</div>\
									<div class="layui-form-item" style="display:none;">\
										<div class="layui-input-block">\
											<div class="layui-footer">\
												<button class="layui-btn" id="createDatapkgSunmit" lay-submit="" lay-filter="component-form-create-datapkg">确认</button>\
												<button type="reset" id="createDatapkgReset" class="layui-btn layui-btn-primary">重置</button>\
											</div>\
										</div>\
									</div>\
								</form>';
                                        layer.open({
                                            title: '创建数据包',
                                            type: 1,
                                            fixed: false,
                                            maxmin: false,
                                            anim: false,
                                            openDuration: 200,
                                            isOutAnim: false,
                                            closeDuration: 200,
                                            shadeClose: false,
                                            resize: false,
                                            //不允许拉伸
                                            area: ['500px', '340px'],
                                            content: '<div id="creatDataPackage" style="padding-top: 15px;padding-right: 15px;"></div>',
                                            btn: ['新增', '重置', '关闭'],
                                            yes: function () {
                                                $('#createDatapkgSunmit').click();
                                            },
                                            btn2: function () {
                                                $('#createDatapkgReset').click();
                                                return false;
                                            },
                                            btn3: function () {
                                                return true;
                                            },
                                            success: function () {
                                                $("#creatDataPackage").append(addTpl);
                                            }
                                        });
                                        form.render(null, 'component-create-datapkg');
                                        form.on('submit(component-form-create-datapkg)',
                                            function (data) {
                                                data.field.refTreeId = treeNode.TREEID;
                                                data.field.creator = sessionStorage.getItem('username');
                                                var name = data.field.name;
                                                var cb_success = function (data) {
                                                    layer.closeAll();
                                                    layer.msg('添加成功!');
                                                    var pNode = treeNode.getParentNode();
                                                    reloadTree(treeNode.PARENTID, treeNode.TREEID);
                                                    logRecord('新增', '策划构建-数据包结构树-(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + ')下创建数据包(名称：' + name + '、编号：' + code + ')', 1);
                                                };
                                                var cb_error = function (data) {
                                                    layer.msg('添加失败，请联系管理员！');
                                                    logRecord('新增', '策划构建-数据包结构树-(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + ')下创建数据包(名称：' + name + '、编号：' + code + ')', 0);
                                                };
                                                twxAjax('Thing.Fn.DataPackage', 'CreateDataPkg', data.field, true, cb_success, cb_error);
                                                return false;
                                            });
                                    });
                            } else {
                                layer.msg('该节点下已经添加过数据包！');
                            }
                        });
                });
        }
    };
    var editNodeMenu = {
        text: "编辑节点",
        icon: imgSuffix + 'images/edit1.png',
        callback: function () {
            twxAjax('Thing.Fn.DataPackage', 'IsAllowEditNode', {
                treeId: treeNode.TREEID
            },
                true,
                function (res) {
                    if (res.success) {
                        var nodeType = treeNode.NODETYPE;
                        var nodeName = treeNode.NODENAME;
                        var testCode = treeNode.TEST_CODE || "";
                        var dlCode = treeNode.DL_CODE || "";
                        var rgCode = treeNode.RG_CODE || "";
                        var nodeNum = '';
                        if (nodeType == 'dir' || nodeType == 'leaf' || nodeType == 'folder') {
                            nodeName = getNodeName(treeNode.NODENAME);
                            nodeNum = getNodeNum(treeNode.NODENAME);
                        }
                        var code = treeNode.CODE == undefined ? '' : treeNode.CODE;
                        var pdmOid = treeNode.PDM_OID == undefined ? '' : treeNode.PDM_OID;
                        var isUseScreen = (treeNode.IS_USE_SCREEN == 1 ? "checked" : "");
                        layui.use(['layer', 'form'],
                            function () {
                                var layer = layui.layer,
                                    form = layui.form;
                                var addTpl = '<form class="layui-form" action="" lay-filter="editNodeForm">\
										<input type="hidden" name="refTreeId" value="">\
										<div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">节点名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="newName" value="' + nodeName + '" lay-verify="required" required autocomplete="off" placeholder="请输入节点名称" class="layui-input">\
											</div>\
										</div>\
										<div class="layui-form-item" style="">\
											<label class="fieldlabel1 layui-form-label">code:</label>\
											<div class="layui-input-block">\
												<select name="code" id="code" lay-filter="code" lay-search >\
													<option value=""></option>\
												</select>\
											</div>\
										</div>\
										<div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">电测试型号:</label>\
											<div class="layui-input-block">\
												<input type="text" id="testCode" name="testCode" value="' + testCode + '"  autocomplete="off" placeholder="请输入电测试型号" class="layui-input">\
											</div>\
										</div>\
                                        <div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">电缆型号:</label>\
											<div class="layui-input-block">\
												<input type="text" id="dlCode" name="dlCode" value="' + dlCode + '"  autocomplete="off" placeholder="请输入电缆型号" class="layui-input">\
											</div>\
										</div>\
                                        <div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">热管型号:</label>\
											<div class="layui-input-block">\
												<input type="text" id="rgCode" name="rgCode" value="' + rgCode + '"  autocomplete="off" placeholder="请输入热管型号" class="layui-input">\
											</div>\
										</div>\
                                        <div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">质测型号:</label>\
											<div class="layui-input-block">\
												<input type="text" id="zcCode" name="zcCode" value="' + (treeNode.ZC_CODE || "") + '"  autocomplete="off" placeholder="请输入质测型号" class="layui-input">\
											</div>\
										</div>\
										<div class="layui-form-item" style="">\
											<label class="fieldlabel1 layui-form-label">PDM产品:</label>\
											<div class="layui-input-block">\
												<select name="oid" id="oid" lay-filter="oid" lay-search >\
													<option value=""></option>\
												</select>\
											</div>\
										</div>\
										<div class="layui-form-item" id="">\
											<label class="fieldlabel1 layui-form-label">是否参与大屏统计:</label>\
											<div class="layui-input-block">\
												<input type="checkbox" id="IS_USE_SCREEN" name="IS_USE_SCREEN" lay-skin="switch" lay-text="是|否" ' + isUseScreen + '>\
											</div>\
										</div>\
										<div class="layui-form-item" style="display:none;">\
											<div class="layui-input-block">\
												<div class="layui-footer">\
													<button class="layui-btn" id="editNodeSubmit" lay-submit="" lay-filter="editNodeSubmit">确认</button>\
													<button type="reset" id="editNodeReset" class="layui-btn layui-btn-primary">重置</button>\
												</div>\
											</div>\
										</div>\
									</form>';
                                var layerH = '160px';
                                if (treeNode.NODETYPE == 'phase') {
                                    layerH = '480px';
                                } else if (treeNode.NODETYPE == 'product') {
                                    layerH = '290px';
                                }
                                layer.open({
                                    title: '编辑节点',
                                    type: 1,
                                    fixed: false,
                                    maxmin: false,
                                    anim: false,
                                    openDuration: 200,
                                    isOutAnim: false,
                                    closeDuration: 200,
                                    shadeClose: false,
                                    resize: false,
                                    //不允许拉伸
                                    area: ['600px', layerH],
                                    content: '<div id="editNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                                    btn: ['确认', '重置', '关闭'],
                                    yes: function () {
                                        $('#editNodeSubmit').click();
                                    },
                                    btn2: function () {
                                        $('#editNodeReset').click();
                                        return false;
                                    },
                                    btn3: function () {
                                        return true;
                                    },
                                    success: function () {
                                        $("#editNodeContent").append(addTpl);
                                        if (treeNode.NODETYPE == 'product') {
                                            $("#IS_USE_SCREEN").parent().parent().remove();
                                            $("#testCode").parent().parent().remove();
                                            $("#dlCode").parent().parent().remove();
                                            $("#rgCode").parent().parent().remove();
                                            $("#zcCode").parent().parent().remove();
                                            var cb_success = function (data) {
                                                var array = data.array;
                                                for (var i = 0; i < array.length; i++) {
                                                    var name = array[i];
                                                    if (name == code) {
                                                        $("#code").append('<option selected value="' + name + '">' + name + '</option>');
                                                    } else {
                                                        $("#code").append('<option value="' + name + '">' + name + '</option>');
                                                    }
                                                }
                                            };
                                            var cb_error = function (data) {

                                            };
                                            twxAjax('Thing.Fn.BOM', 'getProduct', '', false, cb_success, cb_error);

                                            var cb_success1 = function (res) {
                                                if (res.success) {
                                                    var array = res.data;
                                                    for (var i = 0; i < array.length; i++) {
                                                        var pdm = array[i];
                                                        if (pdm.key == pdmOid) {
                                                            $("#oid").append('<option selected value="' + pdm.key + '">' + pdm.text + '</option>');
                                                        } else {
                                                            $("#oid").append('<option value="' + pdm.key + '">' + pdm.text + '</option>');
                                                        }
                                                    }
                                                } else {
                                                    layer.alert(res.msg);
                                                }
                                            };
                                            var cb_error1 = function (data) {

                                            };
                                            twxAjax('Thing.Fn.BOM', 'QueryPdmOid', '', false, cb_success1, cb_error1);

                                        } else if (treeNode.NODETYPE == "phase") {
                                            $("#oid").parent().parent().remove();
                                            var arr = ['C', 'Z', 'M', 'S', 'WX'];
                                            for (var i = 0; i < arr.length; i++) {
                                                if (arr[i] == code) {
                                                    $("#code").append('<option selected value="' + arr[i] + '">' + arr[i] + '</option>');
                                                } else {
                                                    $("#code").append('<option value="' + arr[i] + '">' + arr[i] + '</option>');
                                                }
                                            }
                                        } else {
                                            $("#testCode").parent().parent().remove();
                                            $("#dlCode").parent().parent().remove();
                                            $("#rgCode").parent().parent().remove();
                                            $("#zcCode").parent().parent().remove();
                                            $("#IS_USE_SCREEN").parent().parent().remove();
                                            $("#code").parent().parent().remove();
                                            $("#oid").parent().parent().remove();
                                        }
                                    }
                                });
                                form.render(null, 'editNodeForm');
                                form.on('submit(editNodeSubmit)',
                                    function (data) {
                                        data.field.treeId = treeNode.TREEID;
                                        var newName = data.field.newName;
                                        var newName1 = nodeNum + newName;
                                        var newCode = data.field.code;
                                        var newOid = data.field.oid;
                                        data.field.isUseScreen = (data.field.IS_USE_SCREEN == "on" ? 1 : 0);
                                        var parentId = treeNode.getParentNode().TREEID;
                                        if (!checkNodeNameIsRepeat(parentId, newName, nodeName)) {
                                            var loadingIndex = layer.load(1, {
                                                shade: [0.1, '#fff']
                                            });
                                            var cb_success = function (res) {
                                                layer.close(loadingIndex);
                                                if (res.success) {
                                                    layer.closeAll();
                                                    layer.msg('编辑成功!');
                                                    logRecord('编辑', '策划构建-数据包结构树-(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + ')更名为(' + newName + ')', 1);
                                                    reloadTree(treeNode.PARENTID, treeNode.TREEID);
                                                } else {
                                                    layer.alert(res.msg, {
                                                        icon: 2
                                                    });
                                                }

                                            };
                                            var cb_error = function (data) {
                                                layer.close(loadingIndex);
                                                layer.closeAll();
                                                layer.msg('编辑失败!');
                                                logRecord('编辑', '策划构建-数据包结构树-(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + ')更名为(' + newName + ')', 0);
                                            };
                                            twxAjax('Thing.Fn.DataPackage', 'updateNodeName', data.field, true, cb_success, cb_error);
                                        } else {
                                            layer.msg('该节点已存在！');
                                        }

                                        return false;
                                    });
                            });
                    } else {
                        layer.alert(res.msg, {
                            icon: 2
                        });
                        return false;
                    }
                });
        }
    };
    var deleteNodeMenu = {
        text: "删除节点",
        icon: imgSuffix + 'images/remove1.png',
        callback: function () {
            layui.use(['layer'],
                function () {
                    var layer = layui.layer;
                    twxAjax('Thing.Fn.DataPackage', 'IsAllowDeleteNode', {
                        treeId: treeNode.TREEID
                    },
                        true,
                        function (res) {
                            if (res.success) {
                                var msg = "确认删除 节点 -- " + treeNode.NODENAME + " 吗？";
                                if (treeNode.ISPARENT) {
                                    msg = "该节点下有子节点,确认删除吗?"
                                }
                                twxAjax('Thing.Fn.DataPackage', 'QueryDataPkg', {
                                    refTreeId: treeNode.TREEID
                                },
                                    true,
                                    function (data) {
                                        if (data.rows.length > 0) {
                                            msg = "该节点已经创建数据包,确认删除吗?"
                                        }
                                        layer.confirm(msg, {
                                            icon: 3,
                                            title: '提示'
                                        },
                                            function (index) {
                                                var Iindex = 0;
                                                if (treeNode.NODETYPE == 'product') {
                                                    Iindex = layer.msg('正在删除型号数据,请不要关闭页面,请稍等......', {
                                                        icon: 16,
                                                        shade: 0.01,
                                                        time: 0
                                                    });
                                                }
                                                var cb_success = function (res) {
                                                    if (treeNode.NODETYPE == 'product') {
                                                        layer.close(Iindex);
                                                    }
                                                    if (res.success) {
                                                        // ztreeObj.removeNode(treeNode);
                                                        // var pNode = treeNode.getParentNode().getParentNode();
                                                        // if (pNode == null) {
                                                        // 	pNode = treeNode.getParentNode();
                                                        // }
                                                        // if (pNode.ISPARENT) {
                                                        // 	ztreeObj.reAsyncChildNodes(pNode, 'refresh', false,
                                                        // 		function() {
                                                        // 			loadTreeMenu();
                                                        // 		});
                                                        // }
                                                        reloadTree(treeNode.PARENTID);
                                                        logRecord('删除', '策划构建-数据包结构树-删除节点(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + ')', 1);
                                                        layer.msg(res.msg);
                                                    } else {
                                                        logRecord('删除', '策划构建-数据包结构树-删除节点(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + ')', 0);
                                                        layer.alert(res.msg, {
                                                            icon: 2
                                                        });
                                                    }
                                                };
                                                var cb_error = function (data) {
                                                    if (treeNode.NODETYPE == 'product') {
                                                        layer.close(Iindex);
                                                    }
                                                    logRecord('删除', '策划构建-数据包结构树-删除节点(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + ')', 0);
                                                    layer.msg('删除失败！');
                                                }
                                                twxAjax('Thing.Fn.DataPackage', 'DeleteNode', {
                                                    treeId: treeNode.TREEID,
                                                    nodeType: treeNode.NODETYPE,
                                                    code: treeNode.CODE
                                                },
                                                    true, cb_success, cb_error);

                                            });
                                    });

                            } else {
                                layer.alert(res.msg, {
                                    icon: 2
                                });
                                return false;
                            }
                        });
                });
        }
    };
    var copyNodeMenu = {
        text: "复制节点",
        icon: imgSuffix + 'images/copy1.png',
        callback: function () {
            layui.use(['layer'],
                function () {
                    var layer = layui.layer;
                    layer.confirm("确认复制 节点 -- " + treeNode.NODENAME + " 吗？", {
                        icon: 3,
                        title: '提示'
                    },
                        function (index) {
                            layer.prompt({
                                title: '请输入复制的节点名称'
                            },
                                function (nodeName, index1, elem) {
                                    if (nodeName != null && nodeName != "") {
                                        layer.load();
                                        var cb_success = function (res) {
                                            if (res.success) {
                                                logRecord('复制', '策划构建-数据包结构树-复制节点(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + '),复制后的节点为(名称：' + nodeName + ')', 1);
                                                layer.msg(res.msg);
                                                layer.closeAll();
                                                reloadTree(treeNode.PARENTID);
                                            } else {
                                                layer.alert(res.msg, {
                                                    icon: 2
                                                });
                                            }
                                        };
                                        var cb_error = function () {
                                            logRecord('复制', '策划构建-数据包结构树-复制节点(ID：' + treeNode.TREEID + '、名称：' + treeNode.NODENAME + '),复制后的节点为(名称：' + nodeName + ')', 0);
                                            layer.msg('复制失败!');
                                        }
                                        twxAjax('Thing.Fn.DataPackage', 'copyNode', {
                                            treeId: treeNode.TREEID,
                                            pId: treeNode.PARENTID,
                                            type: treeNode.NODETYPE,
                                            creator: sessionStorage.getItem('username'),
                                            nodeName: nodeName
                                        },
                                            true, cb_success, cb_error);
                                    } else {
                                        layer.msg('请输入节点名称！')
                                    }
                                });
                        });
                });
        }
    };

    var transferNodeMenu = {
        text: "转移节点",
        icon: imgSuffix + 'images/transfer.png',
        callback: function () {
            layui.use(['layer', 'form'], function () {
                var layer = layui.layer,
                    form = layui.form;
                var tpl = `<form class="layui-form" action="">
									<div class="layui-form-item">
										<label class="layui-form-label">分类</label>
										<div class="layui-input-block">
											<select id="transfer-folder" name="folder" lay-search lay-filter="folder">
											</select>
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">型号</label>
										<div class="layui-input-block">
											<select id="transfer-model" name="model" lay-search lay-filter="model">
											</select>
										</div>
									</div>
									<div class="layui-form-item" style="display:none;">
										<button id="transfer_btn" class="layui-btn" lay-submit lay-filter="transfer">提交</button>
									</div>
								</form>`;
                layer.open({
                    title: "编辑",
                    type: 1,
                    shadeClose: false,
                    anim: false,
                    openDuration: 200,
                    isOutAnim: false,
                    closeDuration: 200,
                    // fixed: false,
                    maxmin: false,
                    resize: false, //不允许拉伸
                    area: ["450px", "230px"],
                    content: '<div id="transferContent" style="padding-top: 15px;padding-right: 15px;"></div>',
                    btn: ["确定", "取消"],
                    yes: function () {
                        $("#transfer_btn").click();
                        return false;
                    },
                    btn2: function () {
                        return true;
                    },
                    success: function () {
                        $("#transferContent").append(tpl);

                        //加载型号
                        function loadModel(folderId) {
                            twxAjax("Thing.Fn.DataPackage", "QueryModelByFolder", {
                                folderId: folderId
                            }, true, function (res) {
                                $("#transfer-model").empty();
                                for (var i = 0; i < res.data.length; i++) {
                                    $("#transfer-model").append('<option value="' + res.data[i].TREEID + '">' + res.data[i].NODENAME + '</option>');
                                }
                                form.render();
                            });
                        }

                        //加载分类
                        twxAjax('Thing.Fn.DataPackage', 'QueryFolder', {}, true,
                            function (res) {
                                for (var i = 0; i < res.rows.length; i++) {
                                    var folderName = res.rows[i]['NODENAME'];
                                    var folderId = res.rows[i]['TREEID'];
                                    $("#transfer-folder").append('<option value="' + folderId + '">' + folderName + '</option>');
                                }
                                loadModel(res.rows[0]['TREEID']);
                            },
                            function (xhr, textStatus, errorThrown) {
                                layer.alert('请求出错！', {
                                    icon: 2
                                });
                            });

                        form.on('select(folder)', function (d) {
                            loadModel(d.value)
                        });

                        form.on('submit(transfer)', function (data) {
                            var d = data.field;

                            twxAjax('Thing.Fn.DataPackage', 'TransferNode', {
                                treeId: treeNode.TREEID,
                                newPId: d.model
                            }, true, function (res) {
                                if (res.success) {
                                    layer.closeAll();
                                    ztreeObj.reAsyncChildNodes(ztreeObj.getNodeByParam("TREEID", treeNode.PARENTID, null), 'refresh', false,
                                        function () {
                                            loadTreeMenu();
                                        });

                                    layer.msg(res.msg)
                                } else {
                                    layer.alert(res.msg, {
                                        icon: 2
                                    });
                                }
                            }, function (xhr, textStatus, errorThrown) {
                                layer.alert('请求出错！', {
                                    icon: 2
                                });
                            });
                            return false;
                        })
                    },
                });
            });
        }
    };
    var allfuns = sessionStorage.getItem('funcids');
    var funcArr = allfuns.split(',');

    var type = treeNode.NODETYPE;

    if (type == 'root') {
        if (contains(funcArr, 'tree_addFolder')) {
            menu.push(addFolderMenu);
        }
    }

    if (type == 'folder') {
        if (contains(funcArr, 'tree_addNode')) {
            menu.push(addNodeMenu);
        }
        if (contains(funcArr, 'tree_editNode')) {
            menu.push(editNodeMenu);
        }
        if (contains(funcArr, 'tree_delNode')) {
            if (!treeNode.ISPARENT) {
                menu.push(deleteNodeMenu);
            }
        }
    }

    if (type == 'product') {
        if (contains(funcArr, 'tree_selectFolder')) {
            menu.push(setModelTagMenu);
        }
        if (contains(funcArr, 'tree_selectFolder')) {
            menu.push(selectFolderMenu);
        }
        if (contains(funcArr, 'tree_addNode')) {
            menu.push(addNodeMenu);
        }
        if (contains(funcArr, 'tree_editNode')) {
            menu.push(editNodeMenu);
        }
        if (contains(funcArr, 'tree_delModel')) {
            menu.push(deleteNodeMenu);
        }
        if (contains(funcArr, 'tree_copyNode')) {
            menu.push(copyNodeMenu);
        }
    }

    if (type == 'phase') {
        if (contains(funcArr, 'tree_addNode')) {
            menu.push(addNodeMenu);
        }
        if (contains(funcArr, 'tree_editNode')) {
            menu.push(editNodeMenu);
        }
        if (contains(funcArr, 'tree_delNode')) {
            menu.push(deleteNodeMenu);
        }
        if (contains(funcArr, 'tree_copyNode')) {
            menu.push(copyNodeMenu);
        }
        if (contains(funcArr, 'tree_transferNode')) {
            menu.push(transferNodeMenu);
        }
        menu.push(setAITMenu);

    }

    if (type == 'dir') {
        if (!treeNode.ISPARENT) {
            if (contains(funcArr, 'tree_createPkg')) {
                menu.push(creatDataPackageMenu);
            }
        }
        if (contains(funcArr, 'tree_addNode')) {
            menu.push(addNodeMenu);
        }
        if (contains(funcArr, 'tree_editNode')) {
            menu.push(editNodeMenu);
        }
        if (contains(funcArr, 'tree_delNode')) {
            menu.push(deleteNodeMenu);
        }
        if (contains(funcArr, 'tree_copyNode')) {
            menu.push(copyNodeMenu);
        }
    }

    if (type == 'leaf') {
        if (contains(funcArr, 'tree_createPkg')) {
            menu.push(creatDataPackageMenu);
        }
        if (contains(funcArr, 'tree_editNode')) {
            menu.push(editNodeMenu);
        }
        if (contains(funcArr, 'tree_delNode')) {
            menu.push(deleteNodeMenu);
        }
        if (contains(funcArr, 'tree_copyNode')) {
            menu.push(copyNodeMenu);
        }
    }
    return menu;
}