//二级表对象
var SecondTable = function(treeId, tableConfigId) {
	var othis = this;
	this.tableId = "secondTable";
	this.pageOptions = {
		pageSize: 20,
		pageNumber: 1
	};
	this.renderTable = function() {
		var gridHeight = 770;
		var cb_success = function(data) {
			if (data.success) {
				var dealCol = dealColumns(JSON.parse(data.result));
				$('#' + othis.tableId).datagrid({
					data: [],
					columns: dealCol.col,
					height: gridHeight,
					singleSelect: false,
					remoteSort: false,
					pagination: true,
					emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
					loadMsg: '正在加载数据...',
					striped: false,
					onLoadSuccess: function(data) {
						var rows = data.rows;
						var $datagrid = $('#' + othis.tableId);
						if (rows.length > 0) {
							for (var i = 0; i < rows.length; i++) {
								var row = rows[i];
								var mergedInfo = row.mergedInfo;
								if (mergedInfo != "" && mergedInfo != undefined) {
									var mergeds = mergedInfo.split(",");
									for (var j = 0; j < mergeds.length; j++) {
										var merged = mergeds[j];
										var columnName = merged.split(":")[0];
										var rowspan = merged.split(":")[1];
										$datagrid.datagrid('mergeCells', {
											index: i,
											field: columnName,
											rowspan: rowspan
										});
									}
								}
							}
						}
						changeWidth('secondTable');
						$(".original-table .datagrid-body").css("overflow-x", "auto");
						$('#' + othis.tableId).datagrid('loaded');
					}
				});
			} else {
				layer.alert("表头获取失败！", {
					icon: 2
				});
			}
		}

		var cb_error = function() {
			layer.alert("表头获取失败！", {
				icon: 2
			});
		};

		var parmas = {
			id: tableConfigId
		};
		twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', parmas, false, cb_success, cb_error);
	};
	//初始化分页组件
	this.initPagination = function(data) {
		$('#' + othis.tableId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function() {
					othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions.pageNumber);
				}
			}],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function(pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function(pageNumber, pageSize) {
				//返回false可以在取消刷新操作
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			},
			onRefresh: function(pageNumber, pageSize) {
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function(pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};
	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function() {
		$('#' + othis.tableId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
	};
	//初始化全部的记录条数
	this.initTotalRecords = function() {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function(data) {
			othis.pageLoadFlag = true;
			othis.totalRecords = data.rows[0].result;
			if (othis.dataLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function() {};

		var parmas = {
			table_config_id: tableConfigId,
			processTreeId: treeId,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataCount', parmas, true, cb_success, cb_error);
	};

	//分页查询数据
	this.queryDataByPage = function(pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.tableId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function(data) {
			othis.dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + othis.tableId).datagrid('loadData', data.array);
			if (othis.pageLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function() {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			table_config_id: tableConfigId,
			processTreeId: treeId,
			pageSize: pageSize,
			pageNumber: pageNumber,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataPage', parmas, true, cb_success, cb_error);
	};

	this.renderTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
}