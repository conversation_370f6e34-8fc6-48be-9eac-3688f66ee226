<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../img/favicon.ico">
	<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all">
	<link rel="stylesheet" href="../../css/icon.css">
	<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">

	<script src="../../plugins/layui-lasted/layui.js"></script>

	<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../plugins/easyui/jquery.min.js"></script>
	<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<script src="../js/config/twxconfig.js"></script>
	<script src="../js/util.js"></script>

	<script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
	<script type="text/javascript" src="../dataTree/tree.js"></script>

	<!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
	<script type="text/javascript" src="../js/logUtil.js"></script>

	<style>
		.param-lable {
			font-family: '微软雅黑';
			font-size: 14px;
			width: 160px;
		}

		.layui-input-block {
			margin-left: 160px;
		}

		.table-lable {
			font-family: '微软雅黑';
			font-size: 14px;
			width: 160px;
		}

		.layui-input-block1 {
			margin-left: 160px;
		}

		.layui-input-block1 input {
			width: 212px;
		}

		/* 弹窗不加载滚动条 */
		.layui-layer-page .layui-layer-content {
			overflow: visible !important;
		}

		.layui-btn-mycolor1 {
			background-color: #ffc384;
		}

		.layui-btn-mycolor2 {
			background-color: #5eb95e;
		}

		.layui-btn-mycolor3 {
			background-color: #5a98de;
		}

		.layui-btn-mycolor4 {
			background-color: #BB3D00;
		}

		.layui-btn-mycolor5 {
			background-color: #FBA0A0;
		}

		.layui-btn-mycolor6 {
			background-color: #2B579A;
		}
	</style>

	<title>二三级表配置</title>
</head>

<body>
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div data-options="region:'west',split:true" title="结构树模板" style="width:300px;padding:10px">
			<ul id="dpTree" class="ztree"></ul>
		</div>
		<div data-options="region:'center'" title="模板管理">
			<div id="c_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
				<div data-options="region:'north',split:true,collapsible:false" style="height:240px;">
					<div id="tableTableMsg" style="color:red;display: none;margin: 10px;font-size: 15px; font-weight: 700;"></div>
					<div id="tableTable_tb" style="padding: 5px;">
						<button type="button" class="layui-btn layui-btn-sm" id="table_add">
							<i class="layui-icon">&#xe608;</i> 添加
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4" id="table_copy">
							<i class="layui-icon">&#xe630;</i> 复制
						</button>
						<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" id="table_edit">
							<i class="layui-icon">&#xe642;</i> 编辑
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" func="table-config-delete" id="table_del">
							<i class="layui-icon">&#xe640;</i> 删除
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor2" id="table_save">
							<i class="layui-icon">&#xe605;</i> 保存
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3" id="table_show_all">
							<i class="layui-icon">&#xe62d;</i> 显示全部
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="upload_tpl_file">
							上传模板文件<i class="layui-icon layui-icon-down"></i>
						</button>
						<!-- <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="table_upload_second">
							<i class="layui-icon">&#xe681;</i> 上传二级表模板
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor1" id="table_upload_three">
							<i class="layui-icon">&#xe681;</i> 上传三级表模板
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor5" id="table_upload_plan">
							<i class="layui-icon">&#xe681;</i> 上传策划模板
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor6" id="table_upload_photo">
							<i class="layui-icon">&#xe681;</i> 上传影像记录策划模板
						</button> -->
					</div>
					<div id="tableTable" data-options="border:false"></div>
				</div>
				<div data-options="region:'center'" title="表格配置">
					<div id="paramTableMsg" style="color:red;display: none;margin: 10px;font-size: 15px; font-weight: 700;"></div>
					<div id="paramTable_tb" style="padding: 5px;">
						<button type="button" class="layui-btn layui-btn-sm" id="param_add">
							<i class="layui-icon">&#xe608;</i> 添加
						</button>
						<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" id="param_edit">
							<i class="layui-icon">&#xe642;</i> 编辑
						</button>
						<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="param_del">
							<i class="layui-icon">&#xe640;</i> 删除
						</button>
					</div>
					<table id="paramTable" data-options="border:false"></table>
				</div>
			</div>
		</div>
	</div>
</body>
<script>
	handleFuncBtn();
</script>
<script src="../../plugins/index/jquery.fileDownload.js"></script>
<script src="js/tree.js"></script>
<script src="js/table.js"></script>
<script src="js/param.js"></script>
<script src="js/common.js"></script>
<script src="js/input.js"></script>
<script src="tableConfig.js"></script>

<script type="text/html" id="tableHtml">
	<form class="layui-form" lay-filter="tableForm">
		<input type="hidden" name="ID" class="layui-input" id="ID">
		<div class="layui-form-item">
			<label class="table-lable layui-form-label">模板名称:</label>
			<div class="layui-input-block layui-input-block1">
				<input type="text" name="TREE_NAME" id="tree_name" style="width: 598px;" lay-verify="required|treeNameRepeat" oldname="" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="table-lable layui-form-label">数据来源类型:</label>
				<div class="layui-input-block layui-input-block1">
					<select name="TYPE" lay-filter="type" lay-verify="required" id="type">
						<!-- <option value="1">MES</option> -->
						<option value="2">三级表</option>
						<option value="3">二级表与三级表完全相同</option>
						<option value="4">二级表与三级表表头相同</option>
						<option value="5">结构化三级表</option>
						<option value="6">试验管控系统</option>
					</select>
				</div>
			</div>
			<div class="layui-inline" id="div_mes_interface">
				<label class="table-lable layui-form-label">MES接口:</label>
				<div class="layui-input-block layui-input-block1">
					<input type="text" name="MES_INTERFACE" id="mes_interface" autocomplete="off" placeholder="请输入MES接口" class="layui-input">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline" id="div_three_data_rownum">
				<label class="table-lable layui-form-label">三级表数据起始行数:</label>
				<div class="layui-input-block layui-input-block1">
					<input type="text" name="THREE_DATA_ROWNUM" id="three_data_rownum" lay-verify="number" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline" id="div_second_data_rownum">
				<label class="table-lable layui-form-label">二级表数据起始行数:</label>
				<div class="layui-input-block layui-input-block1">
					<input type="text" name="SECOND_DATA_ROWNUM" id="second_data_rownum" lay-verify="number" autocomplete="off" class="layui-input">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline" id="div_plan_start">
				<label class="table-lable layui-form-label">策划起始列:</label>
				<div class="layui-input-block layui-input-block1">
					<input type="text" name="PLAN_START_COLINDEX" id="plan_start_colindex" lay-verify="number" value="0" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline" id="div_plan_end">
				<label class="table-lable layui-form-label">策划结束列:</label>
				<div class="layui-input-block layui-input-block1">
					<input type="text" name="PLAN_END_COLINDEX" id="plan_end_colindex" lay-verify="number" value="0" autocomplete="off" class="layui-input">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline" id="div_photo_start">
				<label class="table-lable layui-form-label">影像记录策划起始列:</label>
				<div class="layui-input-block layui-input-block1">
					<input type="text" name="PHOTO_START_COLINDEX" id="photo_start_colindex" lay-verify="number" value="0" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline" id="div_photo_end">
				<label class="table-lable layui-form-label">影像记录策划结束列:</label>
				<div class="layui-input-block layui-input-block1">
					<input type="text" name="PHOTO_END_COLINDEX" id="photo_end_colindex" lay-verify="number" value="0" autocomplete="off" class="layui-input">
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="div_is_query">
			<label class="table-lable layui-form-label">是否数据查询:</label>
			<div class="layui-input-block">
				<input type="checkbox" name="IS_QUERY" lay-skin="switch" lay-text="是|否" value="1">
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_table_add" class="layui-btn" lay-submit lay-filter="tableFormAdd">提交</button>
				<button id="btn_table_update" class="layui-btn" lay-submit lay-filter="tableFormUpdate">更新</button>
				<button id="btn_table_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
			</center>
		</div>
	</form>
</script>


<script type="text/html" id="paramHtml">
	<form class="layui-form" lay-filter="paramForm">
		<input type="hidden" name="ID" class="layui-input" id="ID">
		<input type="hidden" name="TABLE_ID" class="layui-input" id="TABLE_ID">
		<div class="layui-form-item">
			<label class="param-lable layui-form-label">参数名称:</label>
			<div class="layui-input-block">
				<input type="text" name="PARAM_NAME" id="param_name" lay-verify="required" autocomplete="off" class="layui-input" placeholder="请输入参数名称">
			</div>
		</div>
		<div class="layui-form-item" id="div_mes_name" style="display: none;">
			<label class="param-lable layui-form-label">MES参数名称:</label>
			<div class="layui-input-block">
				<input type="text" name="MES_NAME" id="mes_name" autocomplete="off" class="layui-input" placeholder="请输入MES参数名称">
			</div>
		</div>
		<div class="layui-form-item" id="div_three_area">
			<label class="param-lable layui-form-label">三级表单元格位置:</label>
			<div class="layui-input-block">
				<input type="text" name="THREE_AREA" id="three_area" autocomplete="off" class="layui-input" placeholder="三级表单元格位置">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="param-lable layui-form-label">二级表单元格位置:</label>
			<div class="layui-input-block">
				<input type="text" name="SECOND_AREA" id="second_area" autocomplete="off" class="layui-input" placeholder="二级表单元格位置">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="param-lable layui-form-label">二级表表格列宽:</label>
			<div class="layui-input-block">
				<input type="text" name="WIDTH" id="width" autocomplete="off" lay-verify="number" value="80" class="layui-input" placeholder="二级表表格列宽">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="param-lable layui-form-label">格式化类型:</label>
			<div class="layui-input-block">
				<select name="FORMAT" lay-filter="type" lay-verify="required" id="type">
					<option value="0">默认显示</option>
					<option value="1">序号</option>
					<option value="2">预览三级表</option>
					<option value="3">影像记录超链接</option>
					<option value="4">跟踪卡超链接</option>
					<option value="5">检验人员</option>
					<option value="6">检验日期</option>
					<option value="7">图片</option>
					<option value="8">热管编号图片</option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="param-lable layui-form-label">值属性:</label>
			<div class="layui-input-block">
				<input type="radio" name="VALUE_TYPE" lay-filter="value-type" value="0" title="无" checked>
				<input type="radio" name="VALUE_TYPE" lay-filter="value-type" value="1" title="设计值">
				<input type="radio" name="VALUE_TYPE" lay-filter="value-type" value="2" title="实测值">
			</div>
		</div>
		<div class="layui-form-item layui-hide" id="relationParamDiv">
			<label class="param-lable layui-form-label">关联属性:</label>
			<div class="layui-input-block">
				<select name="RELATION_PARAM" id="relationParam" lay-filter="type" lay-search id="type">
				</select>
			</div>
		</div>
		<div class="layui-form-item layui-hide" id="indexDiv">
			<label class="param-lable layui-form-label">是否为关键指标:</label>
			<div class="layui-input-block">
				<input type="checkbox" name="IS_INDEX" lay-skin="switch" lay-text="是|否" value="1">
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="param-lable layui-form-label">是否去重:</label>
				<div class="layui-input-block">
					<input type="checkbox" name="IS_REMOVE_REPEAT" lay-filter="IS_REMOVE_REPEAT" lay-skin="switch" lay-text="是|否" value="1">
				</div>
			</div>
			<div class="layui-inline layui-hide" id="INTERFACE_NAME_DIV">
				<label class="table-lable layui-form-label">接口属性:</label>
				<div class="layui-input-block layui-input-block1">
					<select name="INTERFACE_NAME" lay-filter="INTERFACE_NAME" id="INTERFACE_NAME">
						<option value=""></option>
						<option value="OBJECT_NAME">名称</option>
						<option value="OBJECT_CODE">编号</option>
						<option value="OBJECT_BATCH">批次号</option>
					</select>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="param-lable layui-form-label">是否排序:</label>
				<div class="layui-input-block">
					<input type="checkbox" name="IS_SORT" lay-skin="switch" lay-text="是|否" value="1">
				</div>
			</div>
			<div class="layui-inline">
				<label class="param-lable layui-form-label">是否为基本信息:</label>
				<div class="layui-input-block">
					<input type="checkbox" name="IS_BASE" lay-skin="switch" lay-text="是|否" value="1">
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="">
			<div class="layui-inline">
				<label class="param-lable layui-form-label">是否作为查询条件:</label>
				<div class="layui-input-block">
					<input type="checkbox" name="IS_QUERY" lay-skin="switch" lay-text="是|否" value="1">
				</div>
			</div>
			<div class="layui-inline">
				<label class="param-lable layui-form-label">是否在全景图中显示:</label>
				<div class="layui-input-block">
					<input type="checkbox" name="IS_SHOW_IN_360" lay-skin="switch" lay-text="是|否" value="1">
				</div>
			</div>

		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_param_add" class="layui-btn" lay-submit lay-filter="paramFormAdd">提交</button>
				<button id="btn_param_update" class="layui-btn" lay-submit lay-filter="paramFormUpdate">更新</button>
				<button id="btn_param_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="copyHtml">
	<form class="layui-form" lay-filter="copyForm">
		<input type="hidden" name="ID">
		<input type="hidden" name="TYPE">
		<input type="hidden" name="MES_INTERFACE">
		<input type="hidden" name="SECOND_DATA_ROWNUM">
		<input type="hidden" name="THREE_DATA_ROWNUM">
		<input type="hidden" name="SECOND_FILEPATH">
		<input type="hidden" name="THREE_FILEPATH">
		<input type="hidden" name="TABLE_NAME">
		<div class="layui-form-item">
			<label class="table-lable layui-form-label">模板名称:</label>
			<div class="layui-input-block layui-input-block1">
				<input type="text" name="TREE_NAME" id="tree_name" lay-verify="required|treeNameRepeat" oldname="" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_copy_ok" class="layui-btn" lay-submit lay-filter="copyOk">确认</button>
				<button id="btn_copy_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="uploadHtml">
	<form class="layui-form" lay-filter="uploadForm">
		<div class="layui-form-item">
			<label class="param-lable layui-form-label">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
					<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="selectedFile" style="display: none;">
			<label class="param-lable layui-form-label">已选文件:</label>
			<div class="layui-input-block">
				<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>