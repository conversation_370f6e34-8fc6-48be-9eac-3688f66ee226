<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../img/favicon.ico">
	<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all">
	<link rel="stylesheet" type="text/css" href="../../plugins/webuploader/webuploader.css">

	<link rel="stylesheet" href="../../css/icon.css">

	<script src="../../plugins/layui-lasted/layui.js"></script>

	<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../plugins/index/jquery.min.js"></script>
	<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<script src="../js/config/twxconfig.js"></script>
	<script src="../js/util.js"></script>

	<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">
	<script type="text/javascript" src="../../components/dataTree/tree.js"></script>
	<script type="text/javascript" src="../../components/dataTree/bom_tree.js"></script>
	<script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
	<script type="text/javascript" src="../../plugins/webuploader/webuploader.min.js"></script>

	<script type="text/javascript" src="../js/intercept.js"></script>
	<script type="text/javascript" src="../js/logUtil.js"></script>

	<style>
		.layui-form-label {
			font-family: '微软雅黑';
			font-size: 14px;
			width: 90px;
		}

		/*  .datalinklabel{
            width:120px;
        } */
		/* .datalinkinput{
			width:330px !important;
		} */
		/* 弹窗不加载滚动条 */
		/* 	.layui-layer-page .layui-layer-content {
			overflow: visible !important;
		} */

		.layui-btn-mycolor1 {
			background-color: #ffc384;
		}

		.layui-btn-mycolor2 {
			background-color: #99c1fc;
		}

		.layui-btn-mycolor3 {
			background-color: #CCDB75;
		}

		.layui-btn-mycolor4 {
			background-color: #FBA0A0;
		}
	</style>

	<title>采集管理</title>
</head>
<body>
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div id="p" data-options="region:'west',split:true" title="数据包结构树" style="width: 350px;padding:10px">
			<ul id="dpTree" class="ztree"></ul>
		</div>
		<div data-options="region:'center'">
			<div id="sub_layout" class="easyui-layout" style="width:100%;height:100%;"
				data-options="fit:true,border:false">
				<div data-options="region:'north',split:false,closed:true" style="height:200px;">
					<div id="datapkgref" data-options="border:false"></div>
				</div>
				<div data-options="region:'center',border:false">
					<div id="root_layout_tabs" class="easyui-tabs" style="width:100%;height:100%;"
						data-options="border:false">
						<div title="设计类数包据清单"
							data-options="name:'design_list_table',queryType:'design',iconCls:'design_list'"
							style="display:none;">
							<div id="design_list_table_tb" style="padding: 5px;display:none">
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor1"
									func="dc_design_list_table-manual-upload" id="design_list_table-manual-upload">
									<i class="layui-icon">&#xe681;</i> 手工上传
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm layui-hide"
									func="dc_design_list_table_excelimport" id="design_list_table_excelimport">
									<i class="layui-icon">&#xe663;</i> Excel导入
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor2"
									func="dc_design_list_table_manualSync" id="design_list_table_manualSync">
									<i class="layui-icon">&#xe669;</i> 手动同步
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
									func="dc_design_list_table_stateCheck" id="design_list_table_stateCheck">
									<i class="layui-icon">&#xe672;</i> 状态确认
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4"
									func="dc_design_list_table_param" id="design_list_table_param">
									<i class="layui-icon">&#xe642;</i> 编写属性
								</button>
								<button type="button" class="layui-btn layui-btn-sm"
									func="dc_design_list_table_datapkgLink" id="design_list_table_datapkgLink">
									<i class="layui-icon">&#xe64c;</i> 数据包关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3"
									func="dc_design_list_table_procuct_link" id="design_list_table_procuct_link">
									<i class="layui-icon">&#xe64c;</i> 产品结构树关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger"
									func="dc_design_list_table_delete" id="design_list_table_delete">
									<i class="layui-icon">&#xe63c;</i> 删除
								</button>
							</div>
							<div id="design_list_table" data-options="border:false"></div>
						</div>
						<div title="工艺类数据包清单"
							data-options="name:'craft_list_table',queryType:'craft',iconCls:'gongyi_list'"
							style="display:none;">
							<div id="craft_list_table_tb" style="padding: 5px;">
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor1"
									func="dc_craft_list_table-manual-upload" id="craft_list_table-manual-upload">
									<i class="layui-icon">&#xe681;</i> 手工上传
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm layui-hide"
									func="dc_craft_list_table_excelimport" id="craft_list_table_excelimport">
									<i class="layui-icon">&#xe663;</i> Excel导入
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor2"
									func="dc_craft_list_table_manualSync" id="craft_list_table_manualSync">
									<i class="layui-icon">&#xe669;</i> 手动同步
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
									func="dc_craft_list_table_stateCheck" id="craft_list_table_stateCheck">
									<i class="layui-icon">&#xe672;</i> 状态确认
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4"
									func="dc_craft_list_table_param" id="craft_list_table_param">
									<i class="layui-icon">&#xe642;</i> 编写属性
								</button>
								<button type="button" class="layui-btn layui-btn-sm"
									func="dc_craft_list_table_datapkgLink" id="craft_list_table_datapkgLink">
									<i class="layui-icon">&#xe64c;</i> 数据包关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3"
									func="dc_craft_list_table_procuct_link" id="craft_list_table_procuct_link">
									<i class="layui-icon">&#xe64c;</i> 产品结构树关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger"
									func="dc_craft_list_table_delete" id="craft_list_table_delete">
									<i class="layui-icon">&#xe63c;</i> 删除
								</button>
							</div>
							<div id="craft_list_table" data-options="border:false"></div>
						</div>
						<div title="过程控制数据包清单"
							data-options="name:'processcontrol_list_table',queryType:'process',iconCls:'guocheng_list'"
							style="display:none;">
							<div id="processcontrol_list_table_tb" style="padding: 5px;">
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor1"
									func="dc_processcontrol_list_table-manual-upload"
									id="processcontrol_list_table-manual-upload">
									<i class="layui-icon">&#xe681;</i> 手工上传
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm layui-hide"
									func="dc_processcontrol_list_table_excelimport"
									id="processcontrol_list_table_excelimport">
									<i class="layui-icon">&#xe663;</i> Excel导入
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor2"
									func="dc_processcontrol_list_table_manualSync"
									id="processcontrol_list_table_manualSync">
									<i class="layui-icon">&#xe669;</i> 手动同步
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor2 layui-hide"
									id="processcontrol_list_table_syncCableReport">
									<i class="layui-icon">&#xe669;</i> 同步电缆检测报告
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor2 layui-hide"
									id="processcontrol_list_table_syncRgPhoto">
									<i class="layui-icon">&#xe669;</i> 同步热管照片
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
									func="dc_processcontrol_list_table_stateCheck"
									id="processcontrol_list_table_stateCheck">
									<i class="layui-icon">&#xe672;</i> 状态确认
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4"
									func="dc_processcontrol_list_table_param" id="processcontrol_list_table_param">
									<i class="layui-icon">&#xe642;</i> 编写属性
								</button>
								<button type="button" class="layui-btn layui-btn-sm"
									func="dc_processcontrol_list_table_datapkgLink"
									id="processcontrol_list_table_datapkgLink">
									<i class="layui-icon">&#xe64c;</i> 数据包关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3"
									func="dc_processcontrol_list_table_procuct_link"
									id="processcontrol_list_table_procuct_link">
									<i class="layui-icon">&#xe64c;</i> 产品结构树关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger"
									func="dc_processcontrol_list_table_delete" id="processcontrol_list_table_delete">
									<i class="layui-icon">&#xe63c;</i> 删除
								</button>
							</div>
							<div id="processcontrol_list_table" data-options="border:false"></div>
						</div>
						<div title="质量综合数据包清单"
							data-options="name:'quanlitycontrol_list_table',queryType:'quality',iconCls:'quanlity_list'"
							style="display:none;">
							<div id="quanlitycontrol_list_table_tb" style="padding: 5px;">
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor1"
									func="dc_quanlitycontrol_list_table-manual-upload"
									id="quanlitycontrol_list_table-manual-upload">
									<i class="layui-icon">&#xe681;</i> 手工上传
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm layui-hide"
									func="dc_quanlitycontrol_list_table_excelimport"
									id="quanlitycontrol_list_table_excelimport">
									<i class="layui-icon">&#xe663;</i> Excel导入
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor2"
									func="dc_quanlitycontrol_list_table_manualSync"
									id="quanlitycontrol_list_table_manualSync">
									<i class="layui-icon">&#xe669;</i> 手动同步
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4"
									func="dc_quanlitycontrol_list_table_param" id="quanlitycontrol_list_table_param">
									<i class="layui-icon">&#xe642;</i> 编写属性
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
									func="dc_quanlitycontrol_list_table_stateCheck"
									id="quanlitycontrol_list_table_stateCheck">
									<i class="layui-icon">&#xe672;</i> 状态确认
								</button>
								<button type="button" class="layui-btn layui-btn-sm"
									func="dc_quanlitycontrol_list_table_datapkgLink"
									id="quanlitycontrol_list_table_datapkgLink">
									<i class="layui-icon">&#xe64c;</i> 数据包关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3"
									func="dc_quanlitycontrol_list_table_procuct_link"
									id="quanlitycontrol_list_table_procuct_link">
									<i class="layui-icon">&#xe64c;</i> 产品结构树关联
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger"
									func="dc_quanlitycontrol_list_table_delete" id="quanlitycontrol_list_table_delete">
									<i class="layui-icon">&#xe63c;</i> 删除
								</button>
							</div>
							<div id="quanlitycontrol_list_table" data-options="border:false"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
<script>
	handleFuncBtn();
</script>
<script src="uploadManyFile.js"></script>
<script src="../js/listDataOpt.js"></script>
<script src="captureManagement.js"></script>
<script src="tree.js"></script>
<script src="refDataPackage.js"></script>

<script type="text/html" id="manual-upload">
	<form class="layui-form" action="" style="margin:5 5 5 5;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">文件类别</label>
				<div class="layui-input-inline">
					<select name="FILE_TYPE" lay-verify="required" id="file_types">
						<option value=""></option>
					</select>
				</div>
			</div>

			<div class="layui-inline">
				<label class="layui-form-label">密级</label>
				<div class="layui-input-inline">
					<select name="SECURITY_LEVEL" lay-verify="required" id="systems">
						<option value=""></option>
					</select>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">文档编号</label>
				<div class="layui-input-inline">
					<input type="text" name="FILE_NUMBER" lay-verify="title" autocomplete="off" placeholder="请输入标题"
					 class="layui-input" id="FILE_NUMBER">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">文档名称</label>
				<div class="layui-input-inline">
					<input type="text" name="FILE_NAME" lay-verify="title" autocomplete="off" placeholder="请输入标题"
					 class="layui-input" id="FILE_NAME">
				</div>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label">上传文件</label>
			<div class="layui-input-block">
				<!-- <input type="file" name="uploadFile" id="uploadFile"> -->
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="chooseFile">选择文件</button>
					<button type="button" class="layui-btn" id="startUpload" style="display:none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" style="display: none;">
			<center>
				<button id="btn_manual_upload_add" class="layui-btn" lay-submit lay-filter="addData">提交</button>
				<button id="btn_manual_upload_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="many-upload">
	<div style="padding:12px;">
		<!-- <input type="file" name="uploadFile" id="uploadFile"> -->
		<div class="layui-form" style="margin-bottom: 10px;">
			<div class="layui-form-item" style="margin-bottom:0px;">
				<div class="layui-inline" style="margin-bottom:0px;">
					<label class="layui-form-label" style="min-width: 174px;">批量选择文件类别：</label>
					<div class="layui-input-inline">
						<select name="fileType" lay-filter="fileType" id="fileType">
						</select>
					</div>
				</div>
				<div class="layui-inline" style="margin-bottom:0px;">
					<div class="layui-upload" style="margin-bottom:2px;">
						<div id="chooseFile">选择文件</div>
						<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
					</div>
				</div>
			</div>
		</div>
		<table id="file-table" lay-filter="file-table"></table>
	</div>
</script>

<script type="text/html" id="showXmlContent_photo">
	<br><br>
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label" style="min-width: 150px;">照片名称</label>
			<div class="layui-input-inline">
				<input type="text" name="PhotoName" lay-verify="title" autocomplete="off" placeholder="请输入标题"
				 class="layui-input" id="PhotoName">
			</div>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label" style="min-width: 150px;">父对象类型</label>
			<div class="layui-input-inline">
				<input type="text" name="ParentType" lay-verify="title" autocomplete="off" placeholder="请输入标题"
				 class="layui-input" id="ParentType">
			</div>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label" style="min-width: 150px;">父对象名称/代号</label>
			<div class="layui-input-inline">
				<input type="text" name="ParentName" lay-verify="title" autocomplete="off" placeholder="请输入标题"
				 class="layui-input" id="ParentName">
			</div>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label" style="min-width: 150px;">拍摄时间</label>
			<div class="layui-input-inline">
				<input type="text" name="UpLoadTime" lay-verify="title" autocomplete="off" placeholder="请输入标题"
				 class="layui-input" id="UpLoadTime">
			</div>
		</div>
	</div>
</script>

<script type="text/html" id="excelHtml">
	<form class="layui-form" lay-filter="excelForm">
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">文件类型:</label>
			<div class="layui-input-block">
				<select name="fileType" lay-filter="fileType" lay-verify="required" id="fileType">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item" style="display: none;">
			<label class="fieldlabel layui-form-label">文件编号:</label>
			<div class="layui-input-block">
				<input type="text" name="fileNumber" autocomplete="off" placeholder="请输入文件编号" class="layui-input">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="excelChoice">选择文件</button>
					<button type="button" class="layui-btn" id="excelStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="source_div">
			<label class="fieldlabel layui-form-label">文件名称:</label>
			<div class="layui-input-block">
				<input type="text" name="fileName" id="fileName" lay-verify="required" required autocomplete="off"
				 placeholder="请输入文件名称" class="layui-input">
				<input type="hidden" name="fileFormat" id="fileFormat">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">密级:</label>
			<div class="layui-input-block">
				<input type="text" name="securityLevelName" id="securityLevelName" lay-verify="required"
				 readonly="readonly" class="layui-input">
				<input type="hidden" name="securityLevel" id="securityLevel">
				<!-- <select name="securityLevel" lay-verify="required" id="securityLevel">
		            <option value=""></option>
		        </select> -->
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>