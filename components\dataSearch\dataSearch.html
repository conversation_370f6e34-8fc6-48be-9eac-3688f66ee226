<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../img/favicon.ico">
	<link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
	<link rel="stylesheet" href="../../css/icon.css">

	<script src="../../plugins/layui/layui.js"></script>

	<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../plugins/easyui/jquery.min.js"></script>
	<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<script src="../js/config/twxconfig.js"></script>
	<script src="../js/util.js"></script>

	<!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
	<script type="text/javascript" src="../js/logUtil.js"></script>
	<style>
		.fieldlabel{
			padding-left: 10px;
		}
	</style>
	<title>数据查询</title>
</head>
<body>
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div id="tb">
			<form id="condForm">
				<table style="margin:10px 10px;font-family: 微软雅黑;font-size: 14px;">
					<tr height="38px">
						<td class="fieldlabel" align="right">数据类型：</td>
						<td>
							<input class="easyui-combobox" id="dataType" data-options="editable:false" style="width:200px;">
						</td>
						<td class="fieldlabel" align="right">型号：</td>
						<td>
							<input class="easyui-textbox" id="dataModel" style="width:120px;">
						</td>
						<td class="fieldlabel" align="right">名称：</td>
						<td>
							<input class="easyui-textbox" id="dataName" style="width:120px;">
						</td>
						<td>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-normal " style="margin-left: 10px;" onclick="searchTable()">
								<i class="layui-icon">&#xe615;</i> 搜索
							</button>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-danger " style="margin-left: 10px;"  onclick="resetForm()">
								<i class="layui-icon">&#xe639;</i> 清除
							</button>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" style="margin-left: 10px;"  onclick="exportExcel()">
								<i class="layui-icon">&#xe601;</i> 导出
							</button>
						</td>
					</tr>
				</table>
			</form>
		</div>
		<div id="searchDataTable"></div>
	</div>
</body>
<script src="../../plugins/index/jquery.fileDownload.js"></script>
<script src="dataSearchTable.js"></script>
<script src="dataSearch.js"></script>
