/**
 * 产品交接单相关的图表
 * <AUTHOR>
 * @date 2025-04-23
 */


var submitList = [{
    groupType: "ISCERTIFICATE",
    numId: "wjfzx-num",
    chartId: "wjfzx-chart"
}, {
    groupType: "ISLUOHAN",
    numId: "lhdj-num",
    chartId: "lhdj-chart"
}, {
    groupType: "ISSUBMIT",
    numId: "zmcl-num",
    chartId: "zmcl-chart"
}];

var submitTooltipFormatter = function (params) {
    return '<div style="margin: 0px 0 0;line-height:1;">\
							<div style="font-size:14px;color:#666;font-weight:400;line-height:1;">' + params.seriesName + '</div>\
							<div style="margin: 10px 0 0;line-height:1;">\
								<div style="margin: 0px 0 0;line-height:1;">\
									<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:' + params.color + ';"></span>\
									<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' + params.name.split("~~~")[0] + '</span>\
									<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">' + params.value + '</span>\
									<div style="clear:both"></div>\
								</div>\
								<div style="clear:both"></div>\
							</div>\
							<div style="clear:both"></div>\
						</div>';
};


/**
 * 加载产品交接单的柱状图
 *
 */
function loadSubmitChart(modelId, startDate, endDate, groupType, numId, chartId) {
    var chartDom = document.getElementById(chartId);
    var myChart;
    if (echarts.getInstanceByDom(chartDom) == undefined) {
        myChart = echarts.init(chartDom);
    } else {
        myChart = echarts.getInstanceByDom(chartDom);
        myChart.dispose();
        myChart = echarts.init(chartDom);
    }

    myChart.showLoading("default", loadingOption);
    twxAjax(thing, 'QueryModelSubmitCount', {
        username: username,
        treeId: modelId,
        startDate: startDate,
        endDate: endDate,
        groupType: groupType
    }, true, function (res) {
        if (res.success) {
            myChart.hideLoading();
            var total = res.data.total;
            var option = res.data.option;
            var numText = res.data.numText;
            var groupTotal = res.data.groupTotal;

            option.tooltip.formatter = submitTooltipFormatter;
            option.xAxis.axisLabel.formatter = xAxisLabelFormatter;
            option.legend.formatter = function (name) {
                return name + ' (' + groupTotal[name] + ')';
            };
            
            // 对于单机证明材料交付汇总图表(ISSUBMIT)，调整series顺序，将未提交的数据叠放在已提交的数据上方
            if (groupType === "ISSUBMIT" && option.series && option.series.length > 1) {
                // 反转series数组顺序
                option.series = option.series.reverse();
            }
            
            myChart.setOption(option, false, false);
            myChart.off('click');
            myChart.on('click', function (params) {
                submitChartCilck(params, startDate, endDate, groupType);
            });
        }
    }, function (e) {

    });
}


function submitChartCilck(params, startDate, endDate, groupType) {
    var modelId = params.name.split("~~~")[1];
    var seriesName = params.seriesName;
    layer.tab({
        type: 1,
        tab: [{
            title: '提交单位',
            content: '<div id="unitContent" style="height: 620px;padding:50px;"><div id="unitContainer" style="height: 620px;"></div>'
        }, {
            title: '详细表格',
            content: '<div id="tableContent" style="height: 620px;padding:15px 50px 50px 50px;position:relative;"><div style="position:absolute;right:50px;z-index:10;width:100px;top:15px;"><button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="export-submit-table-btn"><i class="layui-icon layui-icon-export"></i> 导出数据</button></div><div style="margin-top:45px;"></div><table id="submit-table"></table></div>'
        }],
        anim: false,
        openDuration: 200,
        skin: 'layui-layer-tab my-layer',
        isOutAnim: false,
        closeDuration: 200,
        closeBtn: 2,
        shadeClose: false,
        maxmin: false,
        resize: false, //不允许拉伸
        area: ['1500px', '770px'],
        success: function () {
            loadSubmitUnitChart(modelId, seriesName, startDate, endDate, groupType);
            loadSubmitTable(modelId, "", seriesName, startDate, endDate, groupType);

            // 添加导出按钮点击事件
            $('#export-submit-table-btn').on('click', function () {
                // 获取当前表格的筛选条件
                var loading;

                // 调用后端导出接口
                $.fileDownload(fileHandlerUrl + '/aitScreen/exportSubmitExcel', {
                    httpMethod: 'POST',
                    data: {
                        treeId: modelId,
                        startDate: startDate,
                        endDate: endDate,
                        groupType: groupType,
                        seriesName: seriesName,
                        unit: ""  // 默认为空，表示不按单位筛选
                    },
                    prepareCallback: function (url) {
                        // 导出准备中
                        loading = layer.msg('正在导出数据，请稍候...', {
                            icon: 16,
                            shade: 0.3,
                            time: 0
                        });
                    },
                    abortCallback: function (url) {
                        layer.close(loading);
                        layer.msg("导出异常！！");
                    },
                    successCallback: function (url) {
                        // 导出成功
                        layer.close(loading);
                        layer.msg('导出成功', { icon: 1 });
                    },
                    failCallback: function (html, url) {
                        // 导出失败
                        layer.close(loading);
                        layer.msg('导出失败', { icon: 2 });
                    }
                });
            });
        }
    });
}




function loadSubmitUnitChart(modelId, seriesName, startDate, endDate, groupType) {

    var chartDom = document.getElementById("unitContainer");
    var myChart;
    if (echarts.getInstanceByDom(chartDom) == undefined) {
        myChart = echarts.init(chartDom);
    } else {
        myChart = echarts.getInstanceByDom(chartDom);
        myChart.dispose();
        myChart = echarts.init(chartDom);
    }
    myChart.showLoading("default", loadingOption);
    twxAjax(thing, 'QuerySubmitUnitCount', {
        treeId: modelId,
        startDate: startDate,
        endDate: endDate,
        seriesName: seriesName,
        groupType: groupType
    }, true, function (res) {
        if (res.success) {
            myChart.hideLoading();
            var option = res.data.option;
            var counts = res.data.counts;
            option.legend.formatter = function (name) {
                var sums = counts[name];
                var t = 0;
                for (var i = 0; i < sums.length; i++) {
                    t += sums[i];
                }
                return name + ' (' + t + ')';
            };
            myChart.setOption(option);

            myChart.off('click');
            myChart.on('click', function (params) {
                var childSeriesName = params.seriesName;
                var unitName = params.name;
                $(".my-layer > div.layui-layer-title > span:nth-child(2)").mousedown();
                table.reload('submit-table', {
                    page: {
                        layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                        groups: 1,
                        first: false,
                        last: false,
                        curr: 1
                    },
                    where: {
                        treeId: modelId,
                        startDate: startDate,
                        endDate: endDate,
                        unit: unitName,
                        groupType: groupType,
                        seriesName: childSeriesName
                    }
                });

                // 更新导出按钮的点击事件，加入单位筛选
                $('#export-submit-table-btn').off('click').on('click', function () {
                    var loading;

                    // 调用后端导出接口
                    $.fileDownload(fileHandlerUrl + '/aitScreen/exportSubmitExcel', {
                        httpMethod: 'POST',
                        data: {
                            treeId: modelId,
                            startDate: startDate,
                            endDate: endDate,
                            groupType: groupType,
                            seriesName: childSeriesName,
                            unit: unitName  // 使用点击的单位名称作为筛选条件
                        },
                        prepareCallback: function (url) {
                            // 导出准备中
                            loading = layer.msg('正在导出数据，请稍候...', {
                                icon: 16,
                                shade: 0.3,
                                time: 0
                            });
                        },
                        abortCallback: function (url) {
                            layer.close(loading);
                            layer.msg("导出异常！！");
                        },
                        successCallback: function (url) {
                            // 导出成功
                            layer.close(loading);
                            layer.msg('导出成功', { icon: 1 });
                        },
                        failCallback: function (html, url) {
                            // 导出失败
                            layer.close(loading);
                            layer.msg('导出失败', { icon: 2 });
                        }
                    });
                });
            });
        }
    }, function (e) {

    });
}

function loadSubmitTable(treeId, unit, seriesName, startDate, endDate, groupType) {
    var groupCol = {};
    var groupCol1 = {
        hide: true
    };
    var groupCol2 = {
        hide: true
    };
    var groupCol3 = {
        hide: true
    };
    var groupCol4 = {
        hide: true
    };
    if (groupType == "ISCERTIFICATE") {
        groupCol = {
            field: groupType,
            title: '交付验收情况',
            width: 150
        };
    } else if (groupType == "ISLUOHAN") {
        groupCol = {
            field: groupType,
            title: '落焊情况',
            width: 130
        };

        groupCol1 = {
            field: "LUOHANPHASE",
            title: '落焊时机',
            width: 170
        };
    } else if (groupType == "ISSUBMIT") {
        groupCol = {
            field: groupType,
            title: '交付情况',
            width: 100
        };

        groupCol1 = {
            field: "CERTIFICATENUMBER",
            title: '产品证明书',
            width: 110
        };
        groupCol2 = {
            field: "RESUMENUMBER",
            title: '产品履历书',
            width: 110
        };
        groupCol3 = {
            field: "OTHER_CERTIFICATE1",
            title: '其他证明1',
            width: 105
        };
        groupCol4 = {
            field: "OTHER_CERTIFICATE2",
            title: '其他证明2',
            width: 105
        };
    }


    table.render({
        id: "submit-table",
        elem: '#submit-table',
        skin: "nob",
        url: getUrl(thing, 'QuerySubmitList', ""),
        width: 1400,
        where: {
            treeId: treeId,
            startDate: startDate,
            endDate: endDate,
            unit: unit,
            groupType: groupType,
            seriesName: seriesName
        },
        page: {
            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
            groups: 1,
            first: false,
            last: false
        },
        cols: [
            [{
                title: '序号',
                type: 'numbers',
                width: 70
            }, {
                field: 'modelName',
                title: '型号',
                width: 100
            }, {
                field: 'PRODUCTNAME',
                title: '产品名称'
            }, {
                field: 'PRODUCTCODE',
                title: '产品代号',
                width: 150
            }, {
                field: 'BATCHCODE',
                title: '批次号',
                width: 150
            }, {
                field: 'SUBMITUNIT',
                title: '提交单位',
                width: 100
            }, groupCol, groupCol1, groupCol2, groupCol3, groupCol4, {
                field: 'FILEPATH',
                title: '操作',
                width: 100,
                templet: function (d) {
                    var html = '<div class="layui-clear-space">';
                    html += '<a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="previewList">查看</a>';
                    html += "</div>";
                    return html;
                }
            }]
        ],
        even: true
    });

    table.on('tool(submit-table)', function (obj) {
        var d = obj.data;
        if (obj.event === 'previewList') {
            previewList(d.ID, d.TABLENAME, d.FILEPATH, d.FILE_NAME, d.FILE_FORMAT, d.FILE_TYPE, d.SOURCE_SYSTEM, d.GATHERING_METHOD);
        }
    });

}