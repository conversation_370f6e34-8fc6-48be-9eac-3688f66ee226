<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="IE=8">
	<link rel="Shortcut Icon" href="../../img/favicon.ico">
	<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all">
	<!-- <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css"> -->

	<!-- <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
	<link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->

	<link rel="stylesheet" href="../../css/icon.css">

	<!-- <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
	<script src="../../plugins/layui-lasted/layui.js"></script>

	<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../plugins/index/jquery.min.js"></script>
	<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<script src="../js/config/twxconfig.js"></script>
	<script src="../js/util.js"></script>

	<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
	<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">
	<script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
	<script type="text/javascript" src="../../plugins/index/jquery.fileDownload.js"></script>
	<script type="text/javascript" src="../dataTree/tree.js"></script>
	<script type="text/javascript" src="../dataTree/bom_tree.js"></script>
	<script type="text/javascript" src="../dataTree/index.js"></script>
	<script type="text/javascript" src="../js/intercept.js"></script>
	<script type="text/javascript" src="../js/logUtil.js"></script>
	<style>
		.fieldlabel {
			font-family: '微软雅黑';
			font-size: 14px;
			min-width: 105px;
		}

		.fieldlabel1 {
			font-family: '微软雅黑';
			font-size: 14px;
			width: 105px;
		}

		/* 弹窗不加载滚动条 */
		.layui-layer-page .layui-layer-content {
			overflow: visible !important;
		}
	</style>
	<title>策划构建</title>
</head>
<body>
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div id="p" data-options="region:'west',split:true" title="数据包结构树" style="width:600px;padding:10px">
			<input class="easyui-combobox" id="tree_type" data-options="editable:false" style="width:200px;">
			<ul id="dpTree" class="ztree"></ul>
			<ul id="bomTree" class="ztree" style="display: none;"></ul>
		</div>
		<div data-options="region:'center',border:false">
			<!-- <div id="root_layout_tabs" class="easyui-tabs" style="width:100%;height:100%;">
				<div title="设计类数据清单" data-options="name:'design_list_table',iconCls:'design_list'" style="display:none;">
					<div id="design_list_table"></div>
				</div>
				<div title="工艺类数据清单" data-options="name:'craft_list_table',iconCls:'gongyi_list'" style="display:none;">
					<div id="craft_list_table"></div>
				</div>
				<div title="过程控制数据清单" data-options="name:'processcontrol_list_table',iconCls:'guocheng_list'" style="display:none;">
					<div id="processcontrol_list_table"></div>
				</div>
				<div title="质量综合数据清单" data-options="name:'quanlitycontrol_list_table',iconCls:'quanlity_list'" style="display:none;">
					<div id="quanlitycontrol_list_table"></div>
				</div>
			</div> -->
			<div id="sub_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
				<div data-options="region:'north',split:false,closed:true,border:false" style="height:200px;">
					<div id="datapkgref"></div>
				</div>
				<div data-options="region:'center',border:false">
					<div id="root_layout_tabs" class="easyui-tabs" style="width:100%;height:100%;">
						<div title="设计类数据包清单" data-options="name:'design_list_table',iconCls:'design_list',border:false" style="display:none;">
							<div id="design_list_table_tb" style="padding:5px;">
								<!-- <a href="javascript:void(0)" class="easyui-linkbutton layui-bg-green" func="design_list_table_new" data-options="iconCls:'icon-myadd', plain:true"
								 onclick="addData()">新增</a> -->
								<button type="button" class="layui-btn layui-btn-sm" func="design_list_table_new" onclick="addData()">
									<i class="layui-icon">&#xe608;</i> 添加
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" func="design_list_table_edit" onclick="editData()">
									<i class="layui-icon">&#xe642;</i> 编辑
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" func="design_list_table_del" onclick="deleteData()">
									<i class="layui-icon">&#xe640;</i> 删除
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" func="design_list_table_save" id="design_list_table_save">
									<i class="layui-icon">&#xe63c;</i> 保存提交
								</button>
								<!-- <a href="javascript:void(0)" class="easyui-linkbutton layui-bg-orange" func="design_list_table_edit" data-options="iconCls:'icon-myedit', plain:true"
								 onclick="editData()">编辑</a>
								<a href="javascript:void(0)" class="easyui-linkbutton layui-bg-red" func="design_list_table_del" data-options="iconCls:'icon-myremove', plain:true"
								 onclick="deleteData()">删除</a>
								<a href="javascript:void(0)" class="easyui-linkbutton layui-bg-blue" func="design_list_table_save" id="design_list_table_save"
								 data-options="iconCls:'icon-cp-save', plain:true">保存提交</a> -->
							</div>
							<div id="design_list_table" data-options="border:false"></div>
						</div>
						<div title="工艺类数据包清单" data-options="name:'craft_list_table',iconCls:'gongyi_list',border:false" style="display:none;">
							<div id="craft_list_table_tb" style="padding:5px;">
								<!-- <a href="javascript:void(0)" class="easyui-linkbutton" func="craft_list_table_new" data-options="iconCls:'icon-myadd', plain:true"
								 onclick="addData()">新增</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="craft_list_table_edit" data-options="iconCls:'icon-myedit', plain:true"
								 onclick="editData()">编辑</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="craft_list_table_del" data-options="iconCls:'icon-myremove', plain:true"
								 onclick="deleteData()">删除</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="craft_list_table_save" id="craft_list_table_save"
								 data-options="iconCls:'icon-cp-save', plain:true">保存提交</a> -->
								<button type="button" class="layui-btn layui-btn-sm" func="craft_list_table_new" onclick="addData()">
									<i class="layui-icon">&#xe608;</i> 添加
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" func="craft_list_table_edit" onclick="editData()">
									<i class="layui-icon">&#xe642;</i> 编辑
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" func="craft_list_table_del" onclick="deleteData()">
									<i class="layui-icon">&#xe640;</i> 删除
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" func="craft_list_table_save" id="craft_list_table_save">
									<i class="layui-icon">&#xe63c;</i> 保存提交
								</button>
							</div>
							<div id="craft_list_table" data-options="border:false"></div>
						</div>
						<div title="过程控制数据包清单" data-options="name:'processcontrol_list_table',iconCls:'guocheng_list',border:false" style="display:none;">
							<div id="processcontrol_list_table_tb" style="padding:5px;">
								<!-- <a href="javascript:void(0)" class="easyui-linkbutton" func="processcontrol_list_table_new" data-options="iconCls:'icon-myadd', plain:true"
								 onclick="addData()">新增</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="processcontrol_list_table_edit" data-options="iconCls:'icon-myedit', plain:true"
								 onclick="editData()">编辑</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="processcontrol_list_table_del" data-options="iconCls:'icon-myremove', plain:true"
								 onclick="deleteData()">删除</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="processcontrol_list_table_save" id="processcontrol_list_table_save"
								 data-options="iconCls:'icon-cp-save', plain:true">保存提交</a> -->
								<button type="button" class="layui-btn layui-btn-sm" func="processcontrol_list_table_new" onclick="addData()">
									<i class="layui-icon">&#xe608;</i> 添加
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" func="processcontrol_list_table_edit" onclick="editData()">
									<i class="layui-icon">&#xe642;</i> 编辑
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" func="processcontrol_list_table_del" onclick="deleteData()">
									<i class="layui-icon">&#xe640;</i> 删除
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" func="processcontrol_list_table_save" id="processcontrol_list_table_save">
									<i class="layui-icon">&#xe63c;</i> 保存提交
								</button>
							</div>
							<div id="processcontrol_list_table" data-options="border:false"></div>
						</div>
						<div title="质量综合数据包清单" data-options="name:'quanlitycontrol_list_table',iconCls:'quanlity_list',border:false" style="display:none;">
							<div id="quanlitycontrol_list_table_tb" style="padding:5px;">
								<!-- <a href="javascript:void(0)" class="easyui-linkbutton" func="quanlitycontrol_list_table_new" data-options="iconCls:'icon-myadd', plain:true"
								 onclick="addData()">新增</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="quanlitycontrol_list_table_edit" data-options="iconCls:'icon-myedit', plain:true"
								 onclick="editData()">编辑</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="quanlitycontrol_list_table_del" data-options="iconCls:'icon-myremove', plain:true"
								 onclick="deleteData()">删除</a>
								<a href="javascript:void(0)" class="easyui-linkbutton" func="quanlitycontrol_list_table_save" id="quanlitycontrol_list_table_save"
								 data-options="iconCls:'icon-cp-save', plain:true">保存提交</a> -->
								<button type="button" class="layui-btn layui-btn-sm" func="quanlitycontrol_list_table_new" onclick="addData()">
									<i class="layui-icon">&#xe608;</i> 添加
								</button>
								<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" func="quanlitycontrol_list_table_edit" onclick="editData()">
									<i class="layui-icon">&#xe642;</i> 编辑
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" func="quanlitycontrol_list_table_del" onclick="deleteData()">
									<i class="layui-icon">&#xe640;</i> 删除
								</button>
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" func="quanlitycontrol_list_table_save" id="quanlitycontrol_list_table_save">
									<i class="layui-icon">&#xe63c;</i> 保存提交
								</button>
							</div>
							<div id="quanlitycontrol_list_table" data-options="border:false"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
<script>
	handleFuncBtn();
</script>
<script src="refDataPackage.js"></script>
<script src="planBuild.js"></script>

<script type="text/html" id="addHtml">
	<form class="layui-form" lay-filter="addForm">
		<input type="hidden" name="ID" class="layui-input" id="ID">
		<div class="layui-form-item">
			<label class="fieldlabel1 layui-form-label">文件类别:</label>
			<div class="layui-input-block">
				<!-- <input type="text" name="FILE_TYPE"  lay-verify="required" required autocomplete="off" placeholder="请输入文件类别" class="layui-input"> -->
				<select name="FILE_TYPE" lay-verify="required" lay-search required id="fileType">
					<option value=""></option>
				</select>

			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel1 layui-form-label">采集方式:</label>
			<div class="layui-input-block">
				<select name="GATHERING_METHOD" lay-filter="gatheringmethod" lay-verify="required" id="collectmethod">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item" id="source_div">
			<label class="fieldlabel1 layui-form-label">来源系统:</label>
			<div class="layui-input-block">
				<select name="SOURCE_SYSTEM" lay-verify="sourcesystem" id="systems">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel1 layui-form-label">交付状态:</label>
			<div class="layui-input-block">
				<select name="DELIVERY_STATE" lay-verify="required" id="dstates">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_add" class="layui-btn" lay-submit lay-filter="addData">提交</button>
				<button id="btn_update" class="layui-btn" lay-submit lay-filter="updateData">更新</button>
				<button id="btn_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="addBomNodeHtml">
	<form class="layui-form" lay-filter="addBomNodeForm">
		<input type="hidden" name="ID" class="layui-input" id="ID">
		<div class="layui-form-item">
			<label class="fieldlabel1 layui-form-label">名称:</label>
			<div class="layui-input-block">
				<input type="text" name="node_name" id="node_name" lay-verify="required|checkNodeNameIsRepeat" required autocomplete="off" placeholder="请输入节点名称" class="layui-input">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel1 layui-form-label">编号:</label>
			<div class="layui-input-block">
				<input type="text" name="node_code" id="node_code" autocomplete="off" placeholder="请输入编号" class="layui-input">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel1 layui-form-label">批次号:</label>
			<div class="layui-input-block">
				<input type="text" name="node_batch" id="node_batch" autocomplete="off" placeholder="请输入批次号" class="layui-input">
			</div>
		</div>
		<!-- 	<div class="layui-form-item">
			<label class="fieldlabel1 layui-form-label">节点类型:</label>
			<div class="layui-input-block">
				<input type="text" name="node_type" id="node_type" lay-verify="" required autocomplete="off" placeholder="请输入节点类型" class="layui-input">
			</div>
		</div>
		<div class="layui-form-item" id="source_div">
			<label class="fieldlabel1 layui-form-label">数据标签:</label>
			<div class="layui-input-block">
				<select name="node_label" multiple="multiple" id="node_label">
					
				</select>
			</div>
		</div> -->
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="addBomNodeSubmit" class="layui-btn" lay-submit lay-filter="addBomNodeSubmit">保存</button>
				<button id="addBomNodeReset" class="layui-btn layui-btn-primary" type="reset">重置</button>
			</center>
		</div>
	</form>
</script>
<script type="text/html" id="uploadHtml">
	<form class="layui-form" lay-filter="uploadForm">
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">模板文件:</label>
			<div class="layui-input-block">
				<button type="button" class="layui-btn layui-btn-normal" id="downloadTpl">下载模板</button>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
					<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="selectedFile" style="display: none;">
			<label class="fieldlabel layui-form-label">已选文件:</label>
			<div class="layui-input-block">
				<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>