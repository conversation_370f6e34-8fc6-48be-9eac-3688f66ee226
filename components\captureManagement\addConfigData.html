<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

    <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">


    <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script>
    <script src="../../plugins/layui/layui.js"></script>
    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>

    <script type="text/javascript" src="../js/intercept.js"></script>
    <script type="text/javascript" src="../js/logUtil.js"></script>
</head>


<body style="padding: 15px">
<form class="layui-form layui-form-pane" action="">

    <div class="layui-form-item">
        <label class="layui-form-label">文件类别</label>
        <div class="layui-input-block" >
            <select name="FILE_TYPE" lay-verify="required" id="file_types">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">采集方式</label>
        <div class="layui-input-block">
            <select name="GATHERING_METHOD" lay-verify="required" >
                <option value=""></option>
                <option value="自动采集">自动采集</option>
                <option value="手工采集">手工采集</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">来源系统</label>
        <div class="layui-input-block">
            <select name="SOURCE_SYSTEM" lay-verify="required" id="systems">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">交付状态</label>
        <div class="layui-input-block">
            <select name="DELIVERY_STATE" lay-verify="required" id="dstates">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <center>
            <button class="layui-btn" lay-submit lay-filter="addData">提交</button>
            <button class="layui-btn layui-btn-primary" type="reset">重置</button>
        </center>
    </div>
</form>

</body>

<script>
    layui.config({
        base: '/DataPackageManagement/build/js/' //假设这是你存放拓展模块的根目录
    }).use(['form','laydate','table','utils','layer'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var table = layui.table;
        var utils = layui.utils;
        var layer = layui.layer;
        //监听提交
        form.on('submit(addData)', function (data) {
            var param = data.field;
            //获取父页面的参数
            var tableName = parent.selectedData.tabName;
            //计算后台应该使用哪张表
            if(tableName === 'design_list_table'){
                param.type = 'DESIGN_DATA_LIST';
            }else if(tableName === 'craft_list_table'){
                param.type = 'CRAFT_DATA_LIST';
            }else if(tableName === 'processcontrol_list_table'){
                param.type = 'PROCESS_CONTROL_LIST';
            }else if(tableName === 'quanlitycontrol_list_table'){
                param.type = 'QUALITY_CONTROL_LIST';
            }

            param.NODECODE = parent.selectedData.node.TREEID;
            param.NODENAME = parent.selectedData.node.TREEID;
            //添加成功的弹窗
            var cb_success = function(data){
                //新增完成后需要刷新界面
                layer.alert("新增完成！",{icon:1},function(index){
                    //提示完成后，点击确定再刷新界面
                    parent.reloadTable(tableName);

                    logRecord('新建','新建项目清单');

                    var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                    parent.layer.close(index); //再执行关闭
                });
            };
            //添加失败的弹窗
            var cb_error = function(xhr){
                layer.alert('新增失败!',{icon:2},function(){
                    
                });
            };
            //同步新增
            twxAjax("publishMissionThing","AddDataToDataListTable",param,false,cb_success,cb_error);
            
            return false;
        });

        var types = twxAjax("publishMissionThing","getDataFromDataDictionary",{type:"文件类别"},false,function (data) {
            for (var i = 0;i<data.rows.length;i++){
                $("#file_types").append('<option value="'+data.rows[i].ITEM_KEY+'">'+data.rows[i].ITEM_VALUE+'</option>');
            }
        });

        var systems = twxAjax("publishMissionThing","getDataFromDataDictionary",{type:"来源系统"},false,function (data) {
            for (var i = 0;i<data.rows.length;i++){
                $("#systems").append('<option value="'+data.rows[i].ITEM_KEY+'">'+data.rows[i].ITEM_VALUE+'</option>');
            }
        });


        var dstates = twxAjax("publishMissionThing","getDataFromDataDictionary",{type:"交付状态"},false,function (data) {
            for (var i = 0;i<data.rows.length;i++){
                $("#dstates").append('<option value="'+data.rows[i].ITEM_KEY+'">'+data.rows[i].ITEM_VALUE+'</option>');
            }
        });

        //渲染form下的下拉框
        form.render('select');

    });
</script>