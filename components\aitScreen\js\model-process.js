/**
 * 型号研制进度流程图
 * <AUTHOR>
 * @date 2025-04-23
 */

function loadModelProcess(treeId) {
    twxAjax(thing, 'QueryModelProcessFromMes', {
        username: username,
        treeId: treeId
    }, true, function (res) {
        if (res.success) {
            renderModelProcess(res.data);
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (xhr, textStatus, errorThrown) {
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}

/**
 * 加载型号研制进度
 */
function renderModelProcess(res) {
    var datas = res.datas;
    var isOnlyModel = res.isOnlyModel;
    var $table = $("#modelProcess table");
    $table.empty();
    var $tr1 = $('<tr style="height:120px;"></tr>');
    var $tr2 = $('<tr></tr>');
    for (var i = 0; i < datas.length; i++) {
        var d = datas[i];

        var $div1 = $('<div class="start-point"><div class="process-name">' + d.name +
            '</div><div class="process-img ' + d.imgCls + '"  id="td1-' + i + '">' + d.models.length + '</div></div>');
        var $td1 = $('<td></td>');
        $td1.append($div1);
        $tr1.append($td1);

        var $div2 = $('<div class="model-table-div"></div>');
        var t = '<table class="layui-table model-table">';
        for (var j = 0; j < d.models.length; j++) {
            var modelName = d.models[j].split("~~~")[0];
            var modelId = d.models[j].split("~~~")[1];
            t = t + '<tr><td class="model-td" modelId = "' + modelId + '">' + modelName + '</td></tr>';
        }
        t = t + '</table>';
        $div2.append(t);
        var $td2 = $('<td align="center"></td>');
        $td2.append($div2);
        $tr2.append($td2);
    }

    $table.append($tr1).append($tr2);
    $(".model-td").unbind('click').bind('click', function (e) {
        var modelId = $(this).attr("modelId");
        loadModelProcess(modelId);
    });
    var container = document.getElementById("modelProcess");
    if (window.jsPlumb) {
        jsPlumb.reset();
    }
    window.jsPlumb = jsPlumbBrowserUI.newInstance({
        container: container
    });

    $(".start-point").each(function (i, e) {
        var $e = $(e);
        var id = $e.find('.process-img').attr("id");
        var nextId = "td1-" + (i + 1);
        var endId = "td2-" + i;

        jsPlumb.connect({
            source: document.getElementById(id),
            target: document.getElementById(nextId),
            anchors: ['Right', 'Left'],
            endpoints: ["Blank", "Blank"],
            // connector: ['Straight'],
            paintStyle: {
                strokeWidth: 2.5,
                stroke: '#00CDFF'
            }
        });
    });

    // 监听容器元素的scroll事件
    container.addEventListener('scroll', function () {
        // 使用setTimeout延迟重绘，确保滚动完成后再重绘
        setTimeout(function() {
            // 重新验证所有连接
            jsPlumb.revalidate();
            // 重绘所有连线
            jsPlumb.repaintEverything();
        }, 10);
    });

}