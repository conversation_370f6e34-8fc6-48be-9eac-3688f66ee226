<!--docxjs library predefined styles--><style>
.docx-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.docx-wrapper>section.docx { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }
.docx { color: black; hyphens: auto; text-underline-position: from-font; }
section.docx { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.docx>article { margin-bottom: auto; z-index: 1; }
section.docx>footer { z-index: 1; }
.docx table { border-collapse: collapse; }
.docx table td, .docx table th { vertical-align: top; }
.docx p { margin: 0pt; min-height: 1em; }
.docx span { white-space: pre-wrap; overflow-wrap: break-word; }
.docx a { color: inherit; text-decoration: inherit; }
</style><!--docxjs document theme values--><style>.docx {
  --docx-majorHAnsi-font: Calibri Light;
  --docx-minorHAnsi-font: Calibri;
  --docx-dk1-color: #000000;
  --docx-lt1-color: #FFFFFF;
  --docx-dk2-color: #44546A;
  --docx-lt2-color: #E7E6E6;
  --docx-accent1-color: #4472C4;
  --docx-accent2-color: #ED7D31;
  --docx-accent3-color: #A5A5A5;
  --docx-accent4-color: #FFC000;
  --docx-accent5-color: #5B9BD5;
  --docx-accent6-color: #70AD47;
  --docx-hlink-color: #0563C1;
  --docx-folHlink-color: #954F72;
}
</style><!--docxjs document styles--><style>.docx span {
  font-family: Calibri, var(--docx-minorHAnsi-font);
  min-height: 11.00pt;
  font-size: 11.00pt;
}
.docx p {
}
.docx p, p.docx_normal {
  margin-top: 0.00pt;
  margin-bottom: 8.00pt;
  line-height: 1.08;
  text-align: left;
}
.docx p, p.docx_normal span {
  font-family: Calibri, var(--docx-minorHAnsi-font);
  color: black;
  min-height: 11.00pt;
  font-size: 11.00pt;
}
.docx span, span.docx_defaultparagraphfont {
}
p.docx_style14 {
  margin-top: 12.00pt;
  margin-bottom: 6.00pt;
  line-height: 1.08;
  text-align: left;
}
p.docx_style14 span {
  font-family: Liberation Sans;
  min-height: 14.00pt;
  font-size: 14.00pt;
  color: black;
}
p.docx_style15 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
  text-align: left;
}
p.docx_style15 span {
  font-family: Calibri, var(--docx-minorHAnsi-font);
  color: black;
  min-height: 11.00pt;
  font-size: 11.00pt;
}
p.docx_style16 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
  text-align: left;
}
p.docx_style16 span {
  font-family: Calibri, var(--docx-minorHAnsi-font);
  color: black;
  min-height: 11.00pt;
  font-size: 11.00pt;
}
p.docx_style17 {
  margin-top: 6.00pt;
  margin-bottom: 6.00pt;
  line-height: 1.08;
  text-align: left;
}
p.docx_style17 span {
  font-style: italic;
  min-height: 12.00pt;
  font-size: 12.00pt;
  font-family: Calibri, var(--docx-minorHAnsi-font);
  color: black;
}
p.docx_style18 {
  margin-top: 0.00pt;
  margin-bottom: 8.00pt;
  line-height: 1.08;
  text-align: left;
}
p.docx_style18 span {
  font-family: Calibri, var(--docx-minorHAnsi-font);
  color: black;
  min-height: 11.00pt;
  font-size: 11.00pt;
}
.docx table, table.docx_tablenormal td {
  padding-top: 0.00pt;
  padding-left: 5.40pt;
  padding-bottom: 0.00pt;
  padding-right: 5.40pt;
}
</style><div class="docx-wrapper"><section class="docx" style="padding: 72pt; width: 612pt; min-height: 792pt;"><article><p class="docx_normal"><span>Test</span></p><p class="docx_normal"><span style="color: rgb(196, 89, 17);">Test color</span></p><p class="docx_normal"><span><span> </span>Tab text</span></p><p class="docx_normal" style="text-align: center;"><span>Text center</span></p><p class="docx_normal" style="text-align: right;"><span>Text right</span></p><p class="docx_normal" style="margin-top: 0pt; margin-bottom: 8pt; text-align: left;"><span>     </span><span>text          text               text</span></p></article></section></div>