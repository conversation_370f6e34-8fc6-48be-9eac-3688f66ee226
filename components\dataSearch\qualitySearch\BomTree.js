//产品结构树的对象
var BomTree = function() {
	var othis = this;
	this.ztreeObj = {};
	this.treeId = 'bomTree';
	//左侧产品结构树选中的code值 默认未选中为-1
	this.selBomTreeId = -1;
	this.treeSetting = bomTreeUtil.treeSetting;
	this.resetSelCode = function() {
		othis.selBomTreeId = -1;
	};
	//加载树结构
	this.loadTree = function(model, stage) {
		othis.resetSelCode();
		var cb_success = function(data) {
			var datas = data.rows;
			if (datas.length > 0) {
				$("#bomTree").show();
				$("#bomMsg").hide();
				datas = bomTreeUtil.dealDataIcons(datas);
				othis.treeSetting.callback.onClick = function(event, treeId, treeNode) {
					othis.selBomTreeId = treeNode.ID;
					dataTable.loadTable(selTreeNode);
				};
				othis.treeSetting.callback.onExpand = function(event, treeId, treeNode) {

				};
				othis.ztreeObj = $.fn.zTree.init($("#" + othis.treeId), othis.treeSetting, datas);
				othis.ztreeObj.expandAll(true);
			} else {
				othis.showBomMsg("数据加载中！");
			}
		};
		//使用ajax进行异步加载Tree
		twxAjax('Thing.Fn.BOM', 'QueryProductTreeByPhase', {
			productOcode: model,
			phaseOcode: stage
		}, true, cb_success);
	};
	//显示产品结构树位置的提示消息
	this.showBomMsg = function(msg) {
		othis.resetSelCode();
		$("#bomTree").hide();
		$("#bomMsg").text(msg).show();
	}
};
