<form id="funcForm" class="layui-form" lay-filter="funcinfo" style="margin:10px">
    <div class="layui-form-item" style="display: none;">
        <div class="layui-input-block">
            <input type="text" name="funcid" value="" >
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label"><font color=red>*</font>功能名称</label>
            <div class="layui-input-inline">
                <input type="text" name="funcname" placeholder="功能名称" lay-reqtext="功能名称为必填项" lay-verify="required" autocomplete="off" class="layui-input"/>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">功能ID</label>
            <div class="layui-input-inline">
                <input type="text" name="funcbtnid" placeholder="界面按钮ID或功能控制描述" autocomplete="off" class="layui-input"/>
            </div>
        </div>
    </div>
    
    <div class="layui-form-item"  style="display: none">
        <div class="layui-input-block">
            <!-- 隐藏提交按钮，在父层中调用 -->
            <button id="btn_func_submit" class="layui-btn" lay-filter="funcformVerify" lay-submit style="display: none"></button>
            <button id="btn_func_update" class="layui-btn" lay-filter="funcupdateVerify" lay-submit style="display: none"></button>
            <button id="btn_func_reset" type="reset" class="layui-btn" style="display: none"></button>
        </div>
    </div>
</form>