/**
 * 试验鉴定看板主模块
 * 负责初始化和协调各个功能模块
 */

layui.use(['element', 'layer', 'form', 'table'], function () {
    var element = layui.element;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    // 将图表实例声明为全局变量
    window.productChart = null;
    window.fileChart = null;
    window.modelProgressChart = null;
    var jsPlumbInstance;
    // 等待DOM加载完成后再初始化jsPlumb和图表
    $(document).ready(function () {
        initCharts();  // 初始化时添加加载状态
        jsPlumbInstance = jsPlumbBrowserUI.newInstance({
            container: document.querySelector('.content'),
            connector: {
                type: 'Straight',
                options: {
                    gap: 10
                }
            },
            anchors: [
                { type: 'Right' },
                { type: 'Left' }
            ],
            endpoints: [
                { type: 'Blank' },
                { type: 'Blank' }
            ],
            paintStyle: {
                stroke: '#1E9FFF',
                strokeWidth: 2
            }
        });
        // 加载型号下拉框
        twxAjax('Thing.Fn.TestEvaluation', 'GetModels', {}, true, function (res) {
            if (res.success) {
                var models = res.data;
                $("#model-select").empty().append('<option value="-1">所有型号</option>');
                for (var i = 0; i < models.length; i++) {
                    $("#model-select").append('<option value="' + models[i].ID_ + '">' + models[i].NAME_ + '</option>');
                }
                form.render('select');
                loadStatistics();
            } else {
                layer.alert(res.msg, {
                    icon: 2
                });
            }
        });

        // 添加全局变量追踪加载状态
        var isLoading = false;

        // 加载统计数据
        function loadStatistics() {
            // 如果正在加载，则不重复加载
            if (isLoading) return;

            isLoading = true;
            var modelId = $("#model-select").val();

            var loadingOption = {
                text: '数据加载中...',
                color: '#3A84F3',
                textColor: '#e0e0e0',
                maskColor: 'rgba(10, 25, 49, 0.9)',
                zlevel: 0
            };

            // 先保留现有图表内容
            window.productChart.showLoading(loadingOption);
            window.fileChart.showLoading(loadingOption);
            // 为modelProgressChart添加加载动画
            if (window.modelProgressChart) {
                window.modelProgressChart.showLoading(loadingOption);
            }
            // 添加表格加载遮罩
            var tableLoadIndex = layer.load(1, {
                shade: [0.3, '#162B4E'],
                content: '<div style="color:#e0e0e0;margin-left:20px">表格加载中...</div>'
            });

            // 使用防抖处理快速切换
            clearTimeout(window.loadStatisticsTimer);
            window.loadStatisticsTimer = setTimeout(function () {
                // 在加载数据前，确保表格区域可见
                $('.status-table').show();
                $('.deployment-type-table').show();
                $('.table-divider').show();

                Promise.all([
                    // 添加新的统计数据请求
                    new Promise(resolve => {
                        twxAjax('Thing.Fn.TestEvaluation', 'GetSystemStatistics', {
                            modelId: modelId
                        }, true, function (res) {
                            if (res.success) updateSystemStatistics(res.data);
                            resolve();
                        });
                    }),
                    new Promise(resolve => {
                        twxAjax('Thing.Fn.TestEvaluation', 'GetProductStatistics', {
                            modelId: modelId
                        }, true, function (res) {
                            if (res.success) drawProductChart(res.data);
                            resolve();
                        });
                    }),
                    new Promise(resolve => {
                        twxAjax('Thing.Fn.TestEvaluation', 'GetStatusStatistics', {
                            modelId: modelId
                        }, true, function (res) {
                            if (res.success) drawStatusTable(res.data);
                            resolve();
                        });
                    }),
                    new Promise(resolve => {
                        twxAjax('Thing.Fn.TestEvaluation', 'GetFileStatistics', {
                            modelId: modelId
                        }, true, function (res) {
                            if (res.success) drawFileChart(res.data);
                            resolve();
                        });
                    }),
                    new Promise(resolve => {
                        twxAjax('Thing.Fn.TestEvaluation', 'GetDeploymentTypeStatistics', {
                            modelId: modelId
                        }, true, function (res) {
                            if (res.success) drawDeploymentTypeTable(res.data);
                            resolve();
                        });
                    }),
                    new Promise(resolve => {
                        twxAjax('Thing.Fn.TestEvaluation', 'GetModelProgressStatistics', {
                            modelId: modelId
                        }, true, function (res) {
                            if (res.success) drawModelProgressChart(res.data);
                            resolve();
                        });
                    })
                ]).then(() => {
                    // 使用requestAnimationFrame确保DOM已更新
                    requestAnimationFrame(function () {
                        // 所有数据加载完成后再连接表头
                        connectHeaders();

                        // 使用MutationObserver检查DOM更新是否完成
                        ensureHeadersVisible();

                        window.productChart.hideLoading();
                        window.fileChart.hideLoading();
                        // 隐藏modelProgressChart的加载动画
                        if (window.modelProgressChart) {
                            window.modelProgressChart.hideLoading();
                        }
                        layer.close(tableLoadIndex);
                        isLoading = false;
                    });
                }).catch(() => {
                    window.productChart.hideLoading();
                    window.fileChart.hideLoading();
                    // 发生错误时也要隐藏modelProgressChart的加载动画
                    if (window.modelProgressChart) {
                        window.modelProgressChart.hideLoading();
                    }
                    layer.close(tableLoadIndex);
                    layer.msg('数据加载失败', { icon: 2 });
                    isLoading = false;
                });
            }, 300);
        }

        // 确保表头可见的函数
        function ensureHeadersVisible() {
            // 检查表头元素是否存在且可见
            if ($('.status-table thead').children().length === 0) {
                console.warn('状态表头不可见，尝试修复');
                // 强制重新绘制表格结构
                $('.status-table thead').html('<tr><th>状态鉴定</th></tr>');
            }

            if ($('.deployment-type-table thead').children().length === 0) {
                console.warn('列装定型表头不可见，尝试修复');
                // 强制重新绘制表格结构
                $('.deployment-type-table thead').html('<tr><th>列装定型</th></tr>');
            }

            // 确保分割线可见
            $('.table-divider').show();
        }

        // 使用外部模块中的updateSystemStatistics函数

        // 型号选择事件
        form.on('select(model-select)', function (data) {
            loadStatistics();
        });

        // 生成产品列表表格
        window.generateProductTable = function (products, title) {
            // 构建表格HTML
            var tableHtml = '<div class="product-list-wrapper">';

            // 表头
            tableHtml += '<div class="table-header">';
            tableHtml += '<table class="product-list-table">';
            tableHtml += '<thead><tr>';
            tableHtml += '<th style="width: 8%">序号</th>';
            tableHtml += '<th style="width: 25%">产品名称</th>';
            tableHtml += '<th style="width: 15%">型号</th>';
            tableHtml += '<th style="width: 15%">当前状态</th>';
            tableHtml += '<th style="width: 20%">计划完成时间</th>';
            tableHtml += '<th style="width: 20%">实际完成时间</th>';
            tableHtml += '</tr></thead>';
            tableHtml += '</table>';
            tableHtml += '</div>';

            // 表格内容
            tableHtml += '<div class="table-body">';
            tableHtml += '<table class="product-list-table">';
            tableHtml += '<tbody>';

            if (products.length === 0) {
                tableHtml += '<tr class="empty-row"><td colspan="6">暂无数据</td></tr>';
            } else {
                products.forEach(function (item, index) {
                    tableHtml += '<tr>';
                    tableHtml += '<td style="width: 8%">' + (index + 1) + '</td>';
                    tableHtml += '<td style="width: 25%">' + (item.NAME || '-') + '</td>';
                    tableHtml += '<td style="width: 15%">' + (item.MODEL_NAME || '-') + '</td>';
                    tableHtml += '<td style="width: 15%" class="status-cell">' + (item.STATUS || '-') + '</td>';
                    tableHtml += '<td style="width: 20%">' + (item.PLANNED_DATE || '-') + '</td>';
                    tableHtml += '<td style="width: 20%">' + (item.ACTUAL_DATE || '-') + '</td>';
                    tableHtml += '</tr>';
                });
            }

            tableHtml += '</tbody></table></div></div>';

            // 显示弹框
            layer.open({
                type: 1,
                title: title,
                area: ['900px', '500px'],  // 增加宽度以适应更多列
                content: tableHtml,
                shadeClose: true
            });
        }

        // 使用外部模块中的drawProductChart函数

        // 使用外部模块中的drawStatusTable函数

        // 使用外部模块中的drawDeploymentTypeTable函数

        // 绘制型号进度图表
        function drawModelProgressChart(data) {
            if (typeof window.drawModelProgressChart === 'function') {
                window.drawModelProgressChart(data);
            }
        }

        // 连接表头
        function connectHeaders() {
            try {
                // 清除之前的连接点和连接
                $('.header-endpoint').remove();
                jsPlumbInstance.reset();

                // 使用更长的延迟，确保DOM已完全更新
                setTimeout(function () {
                    // 检查表头元素是否存在
                    if ($('.status-table th').length <= 1 || $('.deployment-type-table th').length <= 1) {
                        console.warn('表头元素不完整，尝试重新连接');
                        // 再次尝试确保表头可见
                        ensureHeadersVisible();
                        // 延迟后再次尝试连接
                        setTimeout(function () {
                            // 连接状态鉴定表格表头
                            connectTableHeaders('.status-table th');
                            // 连接列装定型表格表头
                            connectTableHeaders('.deployment-type-table th');
                        }, 100);
                    } else {
                        // 正常连接表头
                        connectTableHeaders('.status-table th');
                        connectTableHeaders('.deployment-type-table th');
                    }
                }, 150);
            } catch (error) {
                console.error("连接表头时出错:", error);
            }
        }

        // 连接表格表头的辅助函数
        function connectTableHeaders(selector) {
            var headers = $(selector);
            if (headers.length <= 1) {
                console.warn('表头元素不足，无法连接:', selector);
                return;
            }

            // 为每个表头（除第一个和最后一个）添加连接点
            headers.slice(1, -1).each(function (index) {
                var sourceHeader = $(this);
                var targetHeader = $(this).next('th');
                if (sourceHeader.length && targetHeader.length) {
                    // 确保源和目标元素都存在且包含header-content
                    var sourceContent = sourceHeader.find('.header-content');
                    var targetContent = targetHeader.find('.header-content');

                    if (sourceContent.length && targetContent.length) {
                        // 检查是否是列装定型表格的表头
                        var isDeploymentHeader = sourceContent.hasClass('deployment-header');

                        // 创建连接配置
                        var connectionConfig = {
                            source: sourceContent[0],
                            target: targetContent[0],
                            connector: {
                                type: 'Straight',
                                options: {
                                    gap: 5
                                }
                            },
                            overlays: [
                                {
                                    type: 'Arrow',
                                    options: {
                                        location: 1,
                                        width: 12,
                                        length: 12,
                                        direction: 1,
                                        foldback: 0.8
                                    }
                                }
                            ],
                            paintStyle: {
                                stroke: '#1E9FFF',
                                strokeWidth: 2
                            }
                        };
                        // 为列装定型表格使用特殊的锚点配置
                        if (isDeploymentHeader) {
                            connectionConfig.anchors = [
                                ["Right", "Center", { offsetX: 0, offsetY: 0 }],
                                ["Left", "Center", { offsetX: 0, offsetY: 0 }]
                            ];
                            // 调整连接线的起始点偏移
                            connectionConfig.connector.options.gap = 2;
                        } else {
                            // 状态鉴定表格使用原有的锚点配置
                            connectionConfig.anchors = [
                                ["Right", "Center"],
                                ["Left", "Center"]
                            ];
                        }
                        // 创建连接
                        jsPlumbInstance.connect(connectionConfig);
                    }
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            var date = new Date(dateStr);
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, '0');
            var day = date.getDate().toString().padStart(2, '0');
            var hours = date.getHours().toString().padStart(2, '0');
            var minutes = date.getMinutes().toString().padStart(2, '0');
            var seconds = date.getSeconds().toString().padStart(2, '0');
            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
        }

        // 生成文件列表表格
        window.generateFileTable = function (category, title) {
            var modelId = $("#model-select").val();

            // 构建表格HTML
            var tableHtml = '<div class="file-list-wrapper">' +
                '<table id="file-list-table" lay-filter="file-list-table"></table>' +
                '</div>';

            // 显示弹框
            layer.open({
                type: 1,
                title: title,
                area: ['1410px', '600px'],
                content: tableHtml,
                success: function () {
                    // 计算表格高度
                    var layerHeight = 600;  // 弹窗高度
                    var otherHeight = 50;   // 其他元素高度（标题栏等）
                    var tableHeight = layerHeight - otherHeight;

                    // 渲染表格
                    table.render({
                        elem: '#file-list-table',
                        url: getUrl('Thing.Fn.TestEvaluation', 'GetFilesByCategory'),
                        where: {
                            modelId: modelId,
                            category: category
                        },
                        toolbar: false,
                        defaultToolbar: [],
                        cellMinWidth: 80,
                        height: tableHeight,
                        skin: 'custom-dark-table',
                        even: true,
                        page: {
                            layout: ['limit', 'count', 'prev', 'page', 'next', 'refresh', 'skip'],
                            limit: 20,
                            limits: [10, 20, 30, 40, 50, 100],
                            groups: 1,
                            first: false,
                            last: false
                        },
                        cols: [[{
                            width: 60,
                            align: 'center',
                            title: '序号',
                            type: 'numbers',
                            fixed: 'left'
                        }, {
                            field: 'CATEGORY_',
                            title: '类别',
                            align: 'center',
                            width: 110
                        }, {
                            field: 'NAME_',
                            title: '文件名称',
                            align: 'left',
                            width: 350
                        }, {
                            field: 'CREATOR_',
                            title: '创建人',
                            align: 'center',
                            width: 80
                        }, {
                            field: 'CREATE_TIME_',
                            title: '创建时间',
                            align: 'center',
                            width: 160
                        }, {
                            field: 'FILE_FORMAT_',
                            width: 88,
                            align: 'center',
                            title: '文件类型'
                        }, {
                            field: 'SECURITY_',
                            width: 80,
                            align: 'center',
                            title: '密级',
                            templet: function (d) {
                                return getSecurityName(d.SECURITY_);
                            }
                        }, {
                            field: 'REVIEWER_',
                            width: 80,
                            align: 'center',
                            title: '审核人'
                        }, {
                            field: 'REVIEW_TIME_',
                            width: 160,
                            align: 'center',
                            title: '审核时间'
                        }, {
                            field: 'STATUS_',
                            align: 'center',
                            width: 80,
                            title: '状态',
                            templet: function (d) {
                                if (d.STATUS_ == 1) {
                                    return '已确认';
                                } else {
                                    return '未确认';
                                }
                            }
                        }, {
                            field: 'operation',
                            align: 'center',
                            title: '操作',
                            width: 120,
                            fixed: "right",
                            templet: function (d) {
                                return '<div class="layui-clear-space">\                                    <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="download">下载</a>\                                    <a class="layui-btn layui-btn-xs" lay-event="preview">预览</a>\                                    </div>';
                            }
                        }]]
                    });
                    // 行工具事件
                    table.on('tool(file-list-table)', function (obj) {
                        var data = obj.data;
                        if (obj.event === "preview") {
                            var filePath = data.FILE_PATH_ || "";
                            if (filePath) {
                                previewfile({
                                    FILEPATH: filePath,
                                    FILE_NAME: data.NAME_,
                                    FILE_FORMAT: data.FILE_FORMAT_,
                                    FILE_TYPE: "",
                                    GATHERING_METHOD: ""
                                });
                            } else {
                                layer.alert("未发现文件地址！", { icon: 2 });
                            }
                        } else if (obj.event === "download") {
                            var url = getFileDownloadUrl({
                                FILEPATH: data.FILE_PATH_,
                                FILE_NAME: data.FILE_NAME_,
                                SECURITY_LEVEL_NAME: getSecurityName(data.SECURITY_),
                                FILE_FORMAT: data.FILE_FORMAT_
                            });
                            window.open(url);
                        }
                    });
                }
            });
        }

        // 使用外部模块中的drawFileChart函数

        // 监听窗口大小变化，重绘图表
        window.onresize = function () {
            if (window.productChart) {
                requestAnimationFrame(function () {
                    window.productChart.resize({ animation: { duration: 300 } });
                });
            }
            if (window.fileChart) {
                requestAnimationFrame(function () {
                    window.fileChart.resize({ animation: { duration: 300 } });
                });
            }
            if (window.modelProgressChart) {
                requestAnimationFrame(function () {
                    window.modelProgressChart.resize({ animation: { duration: 300 } });
                });
            }
        };
    });

    // 在图表容器添加初始加载状态
    function initCharts() {
        var chartOptions = {
            backgroundColor: "#162b4e",
            darkMode: true
        };

        var loadingOption = {
            text: "正在初始化...",
            color: "#3A84F3",
            textColor: "#e0e0e0",
            maskColor: "rgba(10, 25, 49, 0.9)",
            zlevel: 0
        };

        window.productChart = echarts.init(document.getElementById("productChart"), null, chartOptions);
        window.fileChart = echarts.init(document.getElementById("fileChart"), null, chartOptions);
        
        // 初始化型号完成进度图表
        if (typeof window.initModelProgressChart === 'function') {
            window.initModelProgressChart();
        }

        productChart.showLoading(loadingOption);
        fileChart.showLoading(loadingOption);
        if (window.modelProgressChart) {
            window.modelProgressChart.showLoading(loadingOption);
        }
    }
});
