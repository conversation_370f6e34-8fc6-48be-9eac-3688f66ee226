function debugPrint(text) {
	text = "DebugPrint[" + text + "]";
	console.log(text);
}

var signPlugin = null;
var blankSign =
	"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAtMAAAFMCAYAAAAJLDA6AAAAAXNSR0IArs4c6QAAAARnQU1BAACx\
jwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAO5SURBVHhe7cEBDQAAAMKg909tDwcEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF2qorgABUDpN1AAAAABJRU5ErkJggg==";

function initPlugin() {
	if (!!signPlugin) {
		signPlugin.DestroyPlugin();
	}
	signPlugin = new PluginNSV();
	signPlugin.InitPlugin(function(state) {
		if (state === 1) {
			//set pen size
			signPlugin.setPenSizeRange(3, 8, null);
			//set pen color
			signPlugin.setPenColor(0, 0, 0, null);
			//sign mode
			signPlugin.setDisplayMapMode(1, null);
			debugPrint("initialize plugin OK.");
		} else {
			// layer.alert('手写板连接失败！', {
			// 	icon: 2
			// });
			debugPrint("initialize plugin fails.");
		}
	});

	/*clear event*/
	signPlugin.onClear = function() {
		clearSign();
	};
	/*cancel event*/
	signPlugin.onCancel = function() {
		endSign();
	};
}

function endSign() {
	if (!!signPlugin) {
		/* signPlugin.endSign(null);*/
		signPlugin.endSign(function(state, args) {
			if (state) {
				//The function call successful.
				debugPrint("endSign OK");
			} else {
				debugPrint("endSign error,description:" + args[0]);
			}
		});
	}
}

function beginSign() {
	if (!!signPlugin) {
		signPlugin.beginSign(function(state, args) {
			if (state) {
				//The function call successful.
				//Play the wizard sound, etc.
				debugPrint("beginSign OK");
			} else {
				debugPrint("beginSign error,description:" + args[0]);
				layer.alert('手写板连接失败！', {
					icon: 2
				});
			}
		});
	}
	//Reset the image.
	document.getElementById('img_sign_result').src = '';
}

function clearSign() {
	if (!!signPlugin) {
		//Reset the signature panel.
		signPlugin.clearSign(function(state, args) {
			if (state) {

			} else {
				debugPrint("clearSign error,description:" + args[0]);
			}
		});
	}
	//Reset the image.
	document.getElementById('img_sign_result').src = '';
}
