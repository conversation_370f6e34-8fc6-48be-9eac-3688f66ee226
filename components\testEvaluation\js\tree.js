//测试产品结构树的对象
var ProductTree = function () {
	var othis = this;
	var curDragNodes;
	this.ztreeObj = {};
	this.treeId = 'tree';
	/**
	 * 处理产品结构树的显示图标以及是否允许拖拽
	 * @param {Object} datas
	 */
	this.dealDatas = function (datas) {
		var imagePrefix = 'img';
		for (var i = 0; i < datas.length; i++) {
			var dtype = datas[i].TYPE_;
			if (dtype === 'root') {
				datas[i].drag = false;
				datas[i].icon = imagePrefix + "/root.png";
			} else if (dtype === 'folder') {
				datas[i].drag = true;
				datas[i].dropInner = false;
				datas[i].childOuter = true;
				datas[i].icon = imagePrefix + "/folder.png";
			} else if (dtype === 'model') {
				datas[i].drag = true;
				datas[i].childOuter = false;
				datas[i].dropInner = true;
				datas[i].icon = imagePrefix + "/model.png";
			} else if (dtype === 'subsystem') {
				datas[i].childOuter = false;
				datas[i].dropInner = true;
				datas[i].icon = imagePrefix + "/subsystem.png";
			} else if (dtype === 'product') {
				datas[i].dropInner = false;
				datas[i].icon = imagePrefix + "/product.png";
			}

		}
		return datas;
	};
	this.treeSetting = {
		view: {
			dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
			showLine: true, //是否显示节点之间的连线
			fontCss: {
				'color': 'black'
			}, //字体样式函数
			selectedMulti: false, //设置是否允许同时选中多个节点,
			txtSelectedEnable: true,
			showTitle: false,  // 禁用默认的title提示，使用自定义的tooltip
			addHoverDom: function(treeId, treeNode) {
				if (treeNode.TYPE_ === 'product') {
					var $a = $("#" + treeNode.tId + "_a");
					
					// 如果不存在tooltip元素，则创建一个
					if ($("#tooltip_" + treeNode.tId).length == 0) {
						var plannedDate = treeNode.PLANNED_DATE_ || '暂无计划时间';
						var actualDate = treeNode.ACTUAL_DATE_ || '暂无实际时间';
						
						var tooltipHtml = '<div id="tooltip_' + treeNode.tId + '" class="node-tooltip" style="display:none;position:absolute;z-index:9999;background:#fff;border:1px solid #ddd;border-radius:4px;padding:8px 12px;box-shadow:0 2px 8px rgba(0,0,0,0.15);font-size:12px;line-height:1.5;">' +
							'<div><strong>计划完成时间：</strong>' + plannedDate + '</div>' +
							'<div><strong>实际完成时间：</strong>' + actualDate + '</div>' +
							'</div>';
						
						$("body").append(tooltipHtml);
						
						// 鼠标移入事件
						$a.mouseover(function(e) {
							var tooltip = $("#tooltip_" + treeNode.tId);
							// 定位tooltip到鼠标位置右侧
							tooltip.css({
								"left": e.pageX + 15 + "px",
								"top": e.pageY - 20 + "px",
								"display": "block"
							});
						});
						
						// 鼠标移动事件
						$a.mousemove(function(e) {
							var tooltip = $("#tooltip_" + treeNode.tId);
							// 跟随鼠标移动
							tooltip.css({
								"left": e.pageX + 15 + "px",
								"top": e.pageY - 20 + "px"
							});
						});
						
						// 鼠标移出事件
						$a.mouseout(function() {
							$("#tooltip_" + treeNode.tId).css("display", "none");
						});
					}
				}
			},
			removeHoverDom: function(treeId, treeNode) {
				if (treeNode.TYPE_ === 'product') {
					// 移除tooltip（可选，这里仅隐藏）
					$("#tooltip_" + treeNode.tId).css("display", "none");
				}
			}
		},
		async: {
			enable: true,
			url: getTreeUrl(THING, "QueryTreeById", ""),
			type: "post",
			autoParam: ["ID_"],
			contentType: "application/json;charset=utf-8",
			dataType: 'json',
			dataFilter: function (treeId, parentNode, responseData) {
				if (responseData.success) {
					var datas = responseData.data;
					if (datas.length > 0) {
						datas = othis.dealDatas(datas);
					}
					return datas;
				} else {
					layer.alert(responseData.msg, {
						icon: 2
					});
				}
			}
		},
		check: {
			chkboxType: {
				"Y": "",
				"N": ""
			},
			chkStyle: "checkbox", //复选框类型
			enable: false //每个节点上是否显示 CheckBox
		},
		edit: {
			enable: true,
			editNameSelectAll: false,
			showRemoveBtn: false,
			showRenameBtn: false,
			drag: {
				autoExpandTrigger: true,
				prev: function (treeId, nodes, targetNode) {
					var pNode = targetNode.getParentNode();
					if (pNode && pNode.dropInner === false) {
						return false;
					} else if (nodes[0].LEVEL_ !== targetNode.LEVEL_) {
						return false;
					} else {
						for (var i = 0, l = curDragNodes.length; i < l; i++) {
							var curPNode = curDragNodes[i].getParentNode();
							if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
								return false;
							}
						}
					}
					return true;
				},
				next: function (treeId, nodes, targetNode) {
					var pNode = targetNode.getParentNode();
					if (pNode && pNode.dropInner === false) {
						return false;
					} else if (nodes[0].LEVEL_ !== targetNode.LEVEL_) {
						return false;
					} else {
						for (var i = 0, l = curDragNodes.length; i < l; i++) {
							var curPNode = curDragNodes[i].getParentNode();
							if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
								return false;
							}
						}
					}
					return true;
				},
				inner: function (treeId, nodes, targetNode) {
					if (targetNode && targetNode.dropInner === false) {
						return false;
					} else if (nodes[0].LEVEL_ <= targetNode.LEVEL_) {
						return false;
					} else {
						for (var i = 0, l = curDragNodes.length; i < l; i++) {
							if (!targetNode && curDragNodes[i].dropRoot === false) {
								return false;
							} else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
								.childOuter === false) {
								return false;
							}
						}
					}
					return true;
				}
			}
		},
		data: {
			simpleData: { //简单数据模式
				enable: true,
				idKey: "ID_",
				pIdKey: "PID_",
				rootPId: -1
			},
			key: {
				name: 'NAME_',
				title: 'NAME_',
				isParent: "ISPARENT"
			}
		},
		callback: {
			beforeDrag: function (treeId, treeNodes) {
				for (var i = 0, l = treeNodes.length; i < l; i++) {
					if (treeNodes[i].drag === false) {
						curDragNodes = null;
						return false;
					} else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
						curDragNodes = null;
						return false;
					}
				}
				curDragNodes = treeNodes;
				return true;
			},
			beforeDrop: function (treeId, treeNodes, targetNode, moveType, isCopy) {
				return true;
			},
			beforeDragOpen: function (treeId, treeNode) {
				autoExpandNode = treeNode;
				return true;
			},
			onDrag: function (event, treeId, treeNodes) { },
			onDrop: function (event, treeId, treeNodes, targetNode, moveType, isCopy) {
				if (targetNode != null) {
					var sourceNodeSort = treeNodes[0].SORT_;
					var sourceNodeId = treeNodes[0].ID_;
					var sourceNodeName = treeNodes[0].NAME_;
					var sourceNodeType = treeNodes[0].TYPE_;

					var targetNodeSort = targetNode.SORT_;
					var targetNodeId = targetNode.ID_;
					var targetNodeName = targetNode.NAME_;

					var parentNode = treeNodes[0].getParentNode();
					var allNode = parentNode.children;
					var arr = [];
					for (var i = 1; i <= allNode.length; i++) {
						arr.push(allNode[i - 1].ID_ + ":" + i);
					}
					var str = arr.join(",");
					twxAjax(THING, 'UpdateTreeNodeSort', {
						str: str
					}, true, function (res) {
						othis.reloadTree(parentNode.ID_, sourceNodeId);
					}, function (res) {
						othis.reloadTree(parentNode.ID_, sourceNodeId);
					});
				}
			},
			onClick: function (event, treeId, treeNode) {
				// 保存当前点击的节点ID到全局筛选条件中
				globalSearchParams.treeId = treeNode.ID_;
				
				if (treeNode.TYPE_ == 'product') {
					renderFileTable(treeNode.ID_, true);
				} else {
					renderFileTable(treeNode.ID_, false);
				}
			},
			onExpand: function (event, treeId, treeNode) {
				othis.loadTreeMenu();
			}
		}
	};
	//加载树结构
	this.loadTree = function () {
		var cb_success = function (res) {
			if (res.success) {
				var datas = res.data;
				if (datas.length > 0) {
					datas = othis.dealDatas(datas);
					othis.ztreeObj = $.fn.zTree.init($("#" + othis.treeId), othis.treeSetting, datas);
					var nodes = othis.ztreeObj.getNodes();
					for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
						othis.ztreeObj.expandNode(nodes[i], true, false, true);
					}
					othis.loadTreeMenu();
				}
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		//使用ajax进行异步加载Tree
		twxAjax(THING, 'QueryTreeRoot', '', true, cb_success);
	};
	//加载树节点右键菜单
	this.loadTreeMenu = function () {
		$("#" + othis.treeId + " a").each(function (i, n) {
			var menu = [];
			var node = othis.ztreeObj.getNodeByTId($(n).parent().attr("id"));

			menu = othis.getNodeMenu(node);
			if (menu.length != 0) {
				$(n).contextMenu({
					width: 115,
					menu: menu,
					target: function (ele) {
						var node = othis.ztreeObj.getNodeByTId($(ele).parent().attr("id"));
						othis.ztreeObj.selectNode(node, false, true);
					}
				});
			}
		});
	};
	//操作完节点之后重新加载节点
	this.reloadTree = function (refrushId, selId) {
		if (selId) {

		} else {
			selId = refrushId;
		}
		var refrushTreeNode = othis.ztreeObj.getNodeByParam("ID_", refrushId, null);
		if (!refrushTreeNode.ISPARENT) {
			refrushTreeNode.ISPARENT = true;
			othis.ztreeObj.updateNode(refrushTreeNode);
		}
		othis.ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false,
			function () {
				othis.ztreeObj.expandNode(refrushTreeNode, true, false, true);
				var newSelNode = othis.ztreeObj.getNodeByParam("ID_", selId, null);
				othis.ztreeObj.selectNode(newSelNode, false, true);
				othis.loadTreeMenu();
			});
	}
	//获取节点右键菜单数组
	this.getNodeMenu = function (treeNode) {
		var imgSuffix = '../dataTree/';
		var menu = [];
		var addName = "",
			editName = "";
		if (treeNode.TYPE_ == 'root') {
			addName = "分类";
		} else if (treeNode.TYPE_ == 'folder') {
			addName = "型号";
			editName = "分类";
		} else if (treeNode.TYPE_ == 'model') {
			addName = "分系统";
			editName = "型号";
		} else if (treeNode.TYPE_ == 'subsystem') {
			addName = "产品";
			editName = "分系统";
		} else if (treeNode.TYPE_ == 'product') {
			editName = "产品";
		}
		var addNodeMenu = {
			text: "添加" + addName,
			icon: imgSuffix + 'images/add.png',
			callback: function () {
				var parentId = treeNode.ID_;
				layer.open({
					title: "添加" + addName,
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					// fixed: false,
					maxmin: false,
					resize: false, //不允许拉伸
					area: ["450px", "185px"],
					content: '<div id="addNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["保存", "重置", "取消"],
					yes: function () {
						$("#addNodeSubmit").click();
					},
					btn2: function () {
						$("#addNodeReset").click();
						return false;
					},
					btn3: function () {
						return true;
					},
					success: function () {
						var addTpl = '<form class="layui-form" lay-filter="addNodeForm">\
										<div class="layui-form-item">\
											<label class="layui-form-label" style="">' + addName + '名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="NAME_" id="NAME_" lay-verify="required" required autocomplete="off" placeholder="请输入' + addName + '名称" class="layui-input">\
											</div>\
										</div>\
										<div class="layui-form-item" style="display:none;">\
											<button id="addNodeSubmit" lay-submit lay-filter="addNodeSubmit">保存</button>\
											<button id="addNodeReset" type="reset">重置</button>\
										</div>\
									</form>';
						$("#addNodeContent").append(addTpl);
						form.render(null, "addNodeForm");

						//监听提交
						form.on("submit(addNodeSubmit)", function (data) {
							var cb_success = function (res) {
								if (res.success) {
									layer.closeAll();
									layer.msg(res.msg);
									othis.reloadTree(treeNode.ID_);
								} else {
									layer.alert(res.msg, {
										icon: 2,
									});
								}
							};
							var cb_error = function (xhr) {
								layer.alert("新增失败！", {
									icon: 2,
								});
							};
							twxAjax(THING, "AddTreeNode", {
								pId: parentId,
								name: data.field.NAME_,
								creator: sessionStorage.getItem('username')
							}, true, cb_success, cb_error);
							return false;
						});
					}
				});
			}
		};
		var editNodeMenu = {
			text: "修改" + editName,
			icon: imgSuffix + 'images/edit.png',
			callback: function () {
				var id = treeNode.ID_;
				var name = treeNode.NAME_;
				var manuUnit = treeNode.MANU_UNIT_ || '';

				// 只有产品类型才显示承制单位	
				var showManuUnit = treeNode.TYPE_ === 'product';
				var formHeight = showManuUnit ? "250px" : "185px";

				layer.open({
					title: "修改" + editName,
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					// fixed: false,
					maxmin: false,
					resize: false, //不允许拉伸
					area: ["450px", formHeight],
					content: '<div id="editNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["保存", "重置", "取消"],
					yes: function () {
						$("#editNodeSubmit").click();
					},
					btn2: function () {
						$("#editNodeReset").click();
						return false;
					},
					btn3: function () {
						return true;
					},
					success: function () {
						$("#editNodeContent").parent().css("overflow", "visible");
						var editTpl = '<form class="layui-form" lay-filter="editNodeForm">\
										<div class="layui-form-item">\
											<label class="layui-form-label" style="">' + editName + '名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="NAME_" id="NAME_" value="' + name + '" lay-verify="required" required autocomplete="off" placeholder="请输入' + editName + '名称" class="layui-input">\
											</div>\
										</div>';

						// 如果是产品类型，添加承制单位下拉框
						if (showManuUnit) {
							editTpl += '<div class="layui-form-item">\
											<label class="layui-form-label" style="">承制单位:</label>\
											<div class="layui-input-block">\
												<input type="text" id="MANU_UNIT" name="MANU_UNIT" autocomplete="off" class="layui-input" value="' + (manuUnit || '') + '">\
												<div class="layui-unselect layui-form-select" id="MANU_UNIT_SELECT">\
													<div class="layui-select-title">\
														<input type="text" placeholder="请输入承制单位" value="' + (manuUnit || '') + '" class="layui-input" id="MANU_UNIT_INPUT">\
														<i class="layui-edge"></i>\
													</div>\
													<dl class="layui-anim layui-anim-upbit" id="MANU_UNIT_OPTIONS">\
													</dl>\
												</div>\
											</div>\
										</div>';
						}

						editTpl += '<div class="layui-form-item" style="display:none;">\
											<button id="editNodeSubmit" lay-submit lay-filter="editNodeSubmit">保存</button>\
											<button id="editNodeReset" type="reset">重置</button>\
										</div>\
									</form>';

						$("#editNodeContent").append(editTpl);

						// 如果是产品类型，加载承制单位数据
						if (showManuUnit) {
							// 隐藏原始输入框，显示自定义下拉框
							$("#MANU_UNIT").hide();

							// 获取承制单位列表
							twxAjax(THING, "GetManufacturerUnits", {}, true, function (res) {
								if (res.success) {
									var units = res.data || [];
									var selectContainer = $("#MANU_UNIT_SELECT");
									var optionsContainer = $("#MANU_UNIT_OPTIONS");
									var inputElem = $("#MANU_UNIT_INPUT");
									var hiddenInput = $("#MANU_UNIT");

									// 添加选项
									optionsContainer.empty();
									for (var i = 0; i < units.length; i++) {
										var unit = units[i] || '';
										if (unit.trim() === '') continue;
										optionsContainer.append('<dd lay-value="' + unit + '" class="' + (unit === manuUnit ? 'layui-this' : '') + '">' + unit + '</dd>');
									}

									// 点击下拉框标题时展开/收起选项
									selectContainer.find('.layui-select-title').on('click', function (e) {
										e.stopPropagation();
										if (selectContainer.hasClass('layui-form-selected')) {
											selectContainer.removeClass('layui-form-selected');
										} else {
											$('.layui-form-select').removeClass('layui-form-selected');
											selectContainer.addClass('layui-form-selected');
										}
									});

									// 点击选项时选中
									optionsContainer.on('click', 'dd', function (e) {
										e.stopPropagation();
										var value = $(this).attr('lay-value');
										inputElem.val(value);
										hiddenInput.val(value);
										selectContainer.removeClass('layui-form-selected');
										optionsContainer.find('dd').removeClass('layui-this');
										$(this).addClass('layui-this');
									});

									// 监听输入框输入事件，实现搜索和自定义输入
									inputElem.on('input', function () {
										var value = $(this).val().trim();
										hiddenInput.val(value);

										// 如果输入为空，显示所有选项
										if (value === '') {
											optionsContainer.find('dd').show();
											return;
										}

										// 搜索匹配的选项
										var found = false;
										optionsContainer.find('dd').each(function () {
											var optionValue = $(this).attr('lay-value');
											if (optionValue.indexOf(value) !== -1) {
												$(this).show();
												found = found || (optionValue === value);
											} else {
												$(this).hide();
											}
										});

										// 如果没有完全匹配的选项，添加当前输入作为新选项
										if (!found) {
											// 检查是否已存在该选项
											var existingOption = optionsContainer.find('dd[lay-value="' + value + '"]');
											if (existingOption.length === 0) {
												optionsContainer.append('<dd lay-value="' + value + '">' + value + '</dd>');
											}
										}
									});

									// 监听输入框回车事件
									inputElem.on('keydown', function (e) {
										if (e.keyCode === 13) {
											e.preventDefault();
											var value = $(this).val().trim();
											if (value !== '') {
												hiddenInput.val(value);

												// 检查是否已存在该选项
												var existingOption = optionsContainer.find('dd[lay-value="' + value + '"]');
												if (existingOption.length === 0) {
													optionsContainer.append('<dd lay-value="' + value + '">' + value + '</dd>');
												}

												// 更新选中状态
												optionsContainer.find('dd').removeClass('layui-this');
												optionsContainer.find('dd[lay-value="' + value + '"]').addClass('layui-this');

												selectContainer.removeClass('layui-form-selected');
											}
										}
									});

									// 点击页面其他地方关闭下拉框
									$(document).on('click', function () {
										selectContainer.removeClass('layui-form-selected');
									});
								}
							});
						}

						form.render(null, "editNodeForm");

						//监听提交
						form.on("submit(editNodeSubmit)", function (data) {
							// 添加loading代码片段
							// 在表单提交时添加loading效果，防止重复点击
							// 显示loading，防止重复点击
							var loadingIndex = layer.load(1, {
								shade: [0.1, '#fff']
							});
							var cb_success = function (res) {

								// 在成功回调和错误回调中关闭loading
								// 关闭loading
								layer.close(loadingIndex);
								if (res.success) {
									layer.closeAll();
									layer.msg(res.msg);
									othis.reloadTree(treeNode.PID_, treeNode.ID_);
								} else {
									layer.alert(res.msg, {
										icon: 2,
									});
								}
							};
							var cb_error = function (xhr) {
								layer.alert("更新请求失败！", {
									icon: 2,
								});
							};

							var params = {
								id: id,
								name: data.field.NAME_
							};

							// 如果是产品类型，添加承制单位参数
							if (showManuUnit) {
								// 使用隐藏输入框的值
								params.manuUnit = $("#MANU_UNIT").val();
							}

							twxAjax(THING, "UpdateTreeNode", params, true, cb_success, cb_error);
							return false;
						});
					}
				});
			}
		};
		var deleteNodeMenu = {
			text: "删除" + editName,
			icon: imgSuffix + 'images/remove.png',
			callback: function () {
				var msg = "确认删除 " + editName + " -- " + treeNode.NAME_ + " 吗？";
				if (treeNode.ISPARENT) {
					msg = "该节点下有子节点，会一并删除，确认删除吗?"
				}
				layer.confirm(msg, {
					icon: 3,
					title: '提示'
				}, function (index) {
					var cb_success = function (data) {
						if (data.success) {
							layer.msg(data.msg);
							othis.reloadTree(treeNode.PID_);
						} else {
							layer.alert(data.msg, {
								icon: 2
							});
						}
					};
					var cb_error = function () {
						layer.msg('删除失败！');
					}
					twxAjax(THING, 'DeleteTreeNode', {
						id: treeNode.ID_
					}, true, cb_success, cb_error);

				});

			}
		};
		var copyNodeMenu = {
			text: "复制" + editName,
			icon: imgSuffix + 'images/copy.png',
			callback: function () {
				layer.open({
					title: "复制" + editName,
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					maxmin: false,
					resize: false,
					area: ["450px", "185px"],
					content: '<div id="copyNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["复制", "重置", "取消"],
					yes: function () {
						$("#copyNodeSubmit").click();
					},
					btn2: function () {
						$("#copyNodeReset").click();
						return false;
					},
					btn3: function () {
						return true;
					},
					success: function () {
						var addTpl = '<form class="layui-form" lay-filter="copyNodeForm">\
							<div class="layui-form-item">\
								<label class="layui-form-label" style="">' + editName + '名称:</label>\
								<div class="layui-input-block">\
									<input type="text" name="NAME_" id="NAME_" lay-verify="required" required autocomplete="off" placeholder="请输入' + editName + '名称" class="layui-input">\
								</div>\
							</div>\
							<div class="layui-form-item" style="display:none;">\
								<button id="copyNodeSubmit" lay-submit lay-filter="copyNodeSubmit">保存</button>\
								<button id="copyNodeReset" type="reset">重置</button>\
							</div>\
						</form>';
						$("#copyNodeContent").append(addTpl);
						form.render(null, "copyNodeForm");

						//监听提交
						form.on("submit(copyNodeSubmit)", function (data) {
							var loadIndex = layer.load(0, {
								shade: [0.1, '#fff']
							});
							twxAjax(THING, "CopyTreeNode", {
								id: treeNode.ID_,
								name: data.field.NAME_,
								creator: sessionStorage.getItem('username')
							}, true, function (res) {
								layer.close(loadIndex);
								if (res.success) {
									layer.closeAll();
									layer.msg(res.msg);
									othis.reloadTree(treeNode.PID_);
								} else {
									layer.alert(res.msg, {
										icon: 2
									});
								}
							}, function (xhr) {
								layer.close(loadIndex);
								layer.alert("复制失败!", {
									icon: 2
								});
							});
							return false;
						});
					}
				});
			}
		};
		var exportNodeMenu = {
			text: "导出数据包",
			icon: imgSuffix + 'images/file-export.png',
			callback: function () {
				exportDataPackage(treeNode.ID_);
			}
		};

		var selectFolderMenu = {
			text: "选择分类",
			icon: imgSuffix + 'images/file-export.png',
			callback: function () {

				var addTpl = '<form class="layui-form" action="" lay-filter="selectFolderForm">\
				<div class="layui-form-item">\
					<label class="fieldlabel1 layui-form-label">型号名称:</label>\
					<div class="layui-input-block">\
						<input type="text" readOnly="readOnly" value="' + treeNode.NAME_ + '" class="layui-input">\
					</div>\
				</div>\
				<div class="layui-form-item" style="">\
					<label class="fieldlabel1 layui-form-label">分类名称:</label>\
					<div class="layui-input-block">\
						<select name="folder" id="folder" lay-filter="folder" lay-verify="required">\
							<option value=""></option>\
						</select>\
					</div>\
				</div>\
				<div class="layui-form-item" style="display:none;">\
					<div class="layui-input-block">\
						<div class="layui-footer">\
							<button class="layui-btn" id="selectFolderSubmit" lay-submit="" lay-filter="selectFolderSubmit">确定</button>\
						</div>\
					</div>\
				</div>\
			</form>';
				var log = {};
				log.operation = '选择分类';
				log.tablePid = treeNode.PID;
				log.tableId = treeNode.ID;

				layer.open({
					title: '选择分类',
					type: 1,
					fixed: false,
					maxmin: false,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					resize: false,
					//不允许拉伸
					area: ['500px', '220px'],
					content: '<div id="selectFolderContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ['确定', '取消'],
					yes: function () {
						$('#selectFolderSubmit').click();
					},
					btn2: function () {
						return true;
					},
					success: function () {
						$("#selectFolderContent").append(addTpl);
						$("#selectFolderContent").parent().css("overflow", "visible");
						twxAjax(THING, 'QueryFolders', {},
							false,
							function (res) {
								if (res.success) {
									var datas = res.data;
									for (var i = 0; i < datas.length; i++) {
										var folderName = datas[i]['NAME_'];
										var folderId = datas[i]['ID_'];
										if (folderName == treeNode.getParentNode().NAME_) {
											$("#folder").append('<option selected value="' +
												folderId + '">' + folderName + '</option>');
										} else {
											$("#folder").append('<option value="' + folderId +
												'">' + folderName + '</option>');
										}
									}
								} else {
									layer.alert(res.msg, {
										icon: 2
									});
								}
							});
					}
				});
				form.render(null, 'selectFolderForm');
				form.on('submit(selectFolderSubmit)',
					function (data) {
						var param = data.field;
						var folderId = param.folder;
						param.nodeId = treeNode.ID_;
						param.newParentId = folderId;
						twxAjax(THING, 'UpdateNodeParent', param, true,
							function (res) {
								if (res.success) {
									layer.closeAll();
									layer.msg(res.msg);
									othis.reloadTree(1);
								} else {
									layer.alert(res.msg, {
										icon: 2
									});
								}
							})
						return false;
					});
			}
		};

		var setStatusMenu = {
			text: "设置状态",
			icon: imgSuffix + 'images/set-status.png',
			callback: function () {
				// 获取状态列表
				twxAjax('Thing.Fn.TestEvaluation', 'GetProductStatus', {
					type: 'list',
					parentName: treeNode.getParentNode().NAME_
				}, true, function (res) {
					if (!res.success) {
						layer.msg(res.msg, { icon: 2 });
						return;
					}
					var statuses = res.data;

					layer.open({
						title: '设置状态',
						type: 1,
						area: ['1000px', '600px'],
						content: '<div class="date-inputs layui-form" style="padding: 15px;">' +
							'  <div class="layui-form-item" style="margin-bottom: 0;">' +
							'    <div class="layui-inline planned-date">' +
							'      <label class="layui-form-label">计划完成时间</label>' +
							'      <div class="layui-input-inline">' +
							'        <input type="text" class="layui-input" id="plannedDate" placeholder="请选择计划完成时间" style="width: 200px;">' +
							'      </div>' +
							'    </div>' +
							'    <div class="layui-inline actual-date" style="display:none;">' +
							'      <label class="layui-form-label">实际完成时间</label>' +
							'      <div class="layui-input-inline">' +
							'        <input type="text" class="layui-input" id="actualDate" placeholder="请选择实际完成时间" style="width: 200px;">' +
							'      </div>' +
							'    </div>' +
							'  </div>' +
							'</div>' +
							'<div id="status-flow" style="padding: 20px; border-top: 1px solid #e6e6e6;"></div>',
						success: function (layero, index) {
							// 初始化日期选择器
							layui.laydate.render({
								elem: '#plannedDate',
								type: 'date',
								format: 'yyyy-MM-dd'
							});
							layui.laydate.render({
								elem: '#actualDate',
								type: 'date',
								format: 'yyyy-MM-dd'
							});

							// 如果节点已有计划完成时间和实际完成时间，则显示在输入框中
							if (treeNode.PLANNED_DATE_) {
								$('#plannedDate').val(treeNode.PLANNED_DATE_);
							}
							if (treeNode.ACTUAL_DATE_) {
								$('#actualDate').val(treeNode.ACTUAL_DATE_);
								// 如果有实际完成时间，显示实际完成时间输入框
								$('.actual-date').show();
							}

							// 如果节点状态是"已完成"，则禁用计划完成时间输入框
							if (treeNode.STATUS_ === '已完成') {
								$('#plannedDate').attr('disabled', 'disabled');
							}

							const STATES_PER_ROW = 4;
							// 计算需要多少行
							const totalRows = Math.ceil(statuses.length / STATES_PER_ROW);

							var flowHtml = '<div class="layui-flow-status">';

							// 添加样式
							var style = document.createElement('style');
							style.textContent = `
								#status-flow {
									display: flex;
									justify-content: center;
									align-items: center;
									height: calc(100% - 140px);
								}
								.layui-form-label {
									width: 110px;
								}
								.layui-input-block {
									margin-left: 140px;
								}
								.layui-flow-status {
									display: flex;
									flex-direction: column;
									align-items: center;
									gap: 8px;
									position: relative;
								}
								.status-row {
									display: grid;
									grid-template-columns: repeat(7, minmax(auto, 150px));
									align-items: center;
									justify-content: center;
									gap: 10px;
									position: relative;
									width: 900px;
								}
								.status-row:not(:last-child) {
									padding-bottom: 40px;
								}
								.status-row:not(:last-child)::after {
									content: "↓";
									position: absolute;
									bottom: 5px;
									color: #1E9FFF;
									font-size: 30px;
								}
								/* 奇数行（第一行）箭头在最后一个非空元素的中间 */
								.status-row:nth-child(odd):not(:last-child)::after {
									right: 60px;
								}
								/* 偶数行（第二行）箭头在第一个非空元素的中间 */
								.status-row:nth-child(even):not(:last-child)::after {
									left: 60px;
								}
								.status-item {
									width: 120px;
									padding: 10px 15px;
									border: 1px solid #e6e6e6;
									border-radius: 4px;
									cursor: pointer;
									background: #fff;
									transition: all 0.3s;
									white-space: nowrap;
									text-align: center;
									justify-self: center;
								}
								.status-item.placeholder {
									cursor: default;
									pointer-events: none;
									visibility: hidden;
								}
								.status-item:not(.placeholder):hover {
									border-color: #1E9FFF;
									color: #1E9FFF;
								}
								.status-item.active {
									background: #1E9FFF;
									color: #fff;
									border-color: #1E9FFF;
								}
								/* 选中状态下的悬停样式 */
								.status-item.active:hover {
									background: #1E9FFF;
									color: #fff;
									border-color: #0d8aff;
									box-shadow: 0 0 5px rgba(30, 159, 255, 0.5);
								}
								.status-arrow {
									color: #1E9FFF;
									font-size: 30px;
									padding: 0 5px;
									text-align: center;
									justify-self: center;
									width: 30px;
								}
								.status-arrow.placeholder {
									visibility: hidden;
								}
							`;
							document.head.appendChild(style);

							// 生成每一行
							for (let rowIndex = 0; rowIndex < totalRows; rowIndex++) {
								const start = rowIndex * STATES_PER_ROW;
								const end = Math.min(start + STATES_PER_ROW, statuses.length);
								let rowStates = statuses.slice(start, end);

								// 如果是最后一行且状态数量不足4个，添加空占位符
								if (rowIndex === totalRows - 1 && rowStates.length < STATES_PER_ROW) {
									const placeholdersNeeded = STATES_PER_ROW - rowStates.length;
									// 根据上一行的方向决定在开头还是结尾添加占位符
									if (rowIndex % 2 === 1) { // 如果是偶数行（从右到左），在开头添加
										for (let i = 0; i < placeholdersNeeded; i++) {
											rowStates.unshift(null);
										}
									} else { // 如果是奇数行（从左到右），在结尾添加
										for (let i = 0; i < placeholdersNeeded; i++) {
											rowStates.push(null);
										}
									}
								}

								// 偶数行倒序显示（第二行开始）
								if (rowIndex % 2 === 1) {
									rowStates = rowStates.reverse();
								}

								flowHtml += `<div class="status-row row-${rowIndex}">`;
								rowStates.forEach((status, idx) => {
									if (status === null) {
										// 添加空的占位元素
										flowHtml += `<div class="status-item placeholder"></div>`;
										flowHtml += `<div class="status-arrow placeholder">→</div>`;
									} else {
										// 如果节点有状态且与当前状态匹配，则添加active类
										const isActive = (treeNode.STATUS_ && treeNode.STATUS_ === status) ? ' active' : '';
										flowHtml += `<div class="status-item${isActive}" data-status="${status}">${status}</div>`;

										// 添加箭头，但最后一个状态不添加横向箭头，且不在空元素前添加箭头
										if (idx < rowStates.length - 1 && rowStates[idx + 1] !== null) {
											// 偶数行用左箭头，奇数行用右箭头
											const arrowDirection = rowIndex % 2 === 1 ? '←' : '→';
											flowHtml += `<div class="status-arrow">${arrowDirection}</div>`;
										}
									}
								});
								flowHtml += '</div>';
							}

							flowHtml += '</div>';

							// 添加样式
							document.head.appendChild(style);

							$('#status-flow').html(flowHtml);

							// 点击状态事件
							$('.status-item').click(function () {
								$('.status-item').removeClass('active');
								$(this).addClass('active');

								// 根据选择的状态显示不同的日期输入框
								var selectedStatus = $(this).data('status');
								if (selectedStatus === '已完成') {
									$('.planned-date').show();
									$('#plannedDate').attr('disabled', 'disabled');
									$('.actual-date').show();
									
									// 自动设置实际完成时间为当前日期
									var today = new Date();
									var year = today.getFullYear();
									var month = String(today.getMonth() + 1).padStart(2, '0');
									var day = String(today.getDate()).padStart(2, '0');
									var currentDate = year + '-' + month + '-' + day;
									$('#actualDate').val(currentDate);
								} else {
									$('.planned-date').show();
									$('#plannedDate').removeAttr('disabled');
									$('.actual-date').hide();
									$('#actualDate').val('');
								}
							});
						},
						btn: ['确定', '取消'],
						yes: function (index) {
							var selectedStatus = $('.status-item.active').data('status');
							if (!selectedStatus) {
								layer.msg('请选择一个状态');
								return;
							}

							// 获取日期值
							var plannedDate = $('#plannedDate').val();
							var actualDate = $('#actualDate').val();

							// 根据状态检查必填项
							if (selectedStatus === '已完成') {
								if (!actualDate) {
									layer.msg('请选择实际完成时间');
									return;
								}
							} else {
								if (!plannedDate) {
									layer.msg('请选择计划完成时间');
									return;
								}
							}

							// 发送请求到后端更新状态
							twxAjax(THING, 'UpdateNodeStatus', {
								nodeId: treeNode.ID_,
								status: selectedStatus,
								plannedDate: plannedDate,
								actualDate: actualDate
							}, true, function (res) {
								if (res.success) {
									layer.close(index);
									layer.msg('状态更新成功');
									othis.reloadTree(treeNode.PID_, treeNode.ID_);
								} else {
									layer.alert(res.msg, {
										icon: 2
									});
								}
							}, function (xhr) {
								layer.alert('状态更新失败', {
									icon: 2
								});
							});
						}
					});
				}, function (xhr) {
					layer.msg('获取状态列表失败！');
				});
			}
		};


		var nodeType = treeNode.TYPE_;
		if (nodeType == 'root') {
			menu.push(addNodeMenu);
		} else if (nodeType == 'folder') {
			menu.push(addNodeMenu);
			menu.push(editNodeMenu);
			menu.push(deleteNodeMenu);
		} else if (nodeType == 'model') {
			menu.push(selectFolderMenu);
			menu.push(addNodeMenu);
			menu.push(editNodeMenu);
			menu.push(deleteNodeMenu);
			menu.push(copyNodeMenu);
			menu.push(exportNodeMenu);
		} else if (nodeType == 'subsystem') {
			menu.push(addNodeMenu);
			menu.push(editNodeMenu);
			menu.push(deleteNodeMenu);
			menu.push(copyNodeMenu);
			menu.push(exportNodeMenu);
		} else if (nodeType == 'product') {
			menu.push(editNodeMenu);
			menu.push(deleteNodeMenu);
			menu.push(copyNodeMenu);
			menu.push(exportNodeMenu);
			menu.push(setStatusMenu);
		}

		return menu;
	}

	othis.selectFolder = function (treeNode) {
		// 获取所有分类
		twxAjax(THING, 'QueryFolders', {}, true, function (res) {
			if (!res.success) {
				layer.msg(res.msg, { icon: 2 });
				return;
			}
			var folders = res.data;

			// 构建下拉框选项
			var options = folders.map(function (folder) {
				return '<option value="' + folder.ID_ + '">' + folder.NAME_ + '</option>';
			}).join('');

			layer.open({
				title: '选择分类',
				type: 1,
				area: ['400px', '200px'],
				content: '<div style="padding: 20px;">' +
					'<div class="layui-form">' +
					'<div class="layui-form-item">' +
					'<div class="layui-form-item">' +
					'<div class="">' +
					'<select id="folderSelect" lay-filter="folder">' +
					'<option value="">请选择或输入分类</option>' +
					options +
					'</select>' +
					'</div>' +
					'</div>' +
					'</div>' +
					'</div>',
				success: function (layero, index) {
					layui.form.render('select');
				},
				btn: ['确定', '取消'],
				yes: function (index) {
					var selectedFolderId = $('#folderSelect').val();
					if (!selectedFolderId) {
						layer.msg('请选择分类');
						return;
					}

					// 更新节点的父节点
					twxAjax(THING, 'UpdateNodeParent', {
						nodeId: treeNode.ID_,
						newParentId: selectedFolderId
					}, true, function (res) {
						if (res.success) {
							layer.close(index);
							layer.msg('更新成功');
							// 刷新树
							othis.reloadTree(1);
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					}, function (xhr) {
						layer.alert('更新失败', {
							icon: 2
						});
					});
				}
			});
		}, function (xhr) {
			layer.msg('获取分类列表失败！');
		});
	};

	othis.loadTree();
};