.container {
    width: 100%;
    height: 100%;
    background: #0a1931;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    position: relative;
}

.page-title {
    font-size: 28px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    padding: 15px 0;
    margin: 0;
    position: relative;
    background: linear-gradient(135deg, #162b4e, #1d3461, #3a84f3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
    text-transform: uppercase;
}

.page-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #3a84f3, #40a9ff);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.header {
    margin-top: 10px;
    padding: 0 5px;
    display: none;
}

.header .layui-form-label {
    width: 80px;
    color: #e0e0e0;
    background: #162b4e;
    border-color: #1d3461;
    font-size: 14px;
    padding: 8px 10px;
    height: 32px;
    line-height: 16px;
    border-radius: 4px 0 0 4px;
    text-align: center;
}

.header .layui-input-inline {
    width: 180px;
    margin-right: 10px;
}

.header .layui-form-select .layui-input {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
}

.header .layui-form-item {
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 5px;
    box-sizing: border-box;
}

.top-section {
    width: 100%;
    padding: 5px;
    background: #162b4e;
    border-radius: 4px;
    overflow: hidden;
    box-sizing: border-box;
    border: 1px solid #1d3461;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.bottom-section {
    display: flex;
    gap: 5px;
    height: 380px;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
}

.bottom-left-model,
.bottom-left,
.bottom-right {
    flex: 1;
    min-width: 0;
    background: #162b4e;
    padding: 15px;
    border-radius: 4px;
    box-sizing: border-box;
    border: 1px solid #1d3461;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.chart-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background: #0a1931;
    color: #ffffff;
    min-height: 350px;
    position: relative;
    transition: background-color 0.3s ease;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #0a1931;
    z-index: -1;
}

/* 确保echarts容器也有背景色 */
.chart-container > div {
    background: #0a1931 !important;
}

/* 添加图表加载时的背景样式 */
.echarts-loading {
    background: #0a1931 !important;
}

/* 确保容器在所有状态下都保持深色背景 */
.container, 
.content,
.bottom-section,
.bottom-left-model,
.bottom-left,
.bottom-right {
    background: #0a1931 !important;
}

.chart-container .echarts-tooltip {
    background: rgba(22, 43, 78, 0.9) !important;
    border: 1px solid #1d3461 !important;
    color: #ffffff !important;
}

.chart-container .echarts-legend {
    color: #ffffff !important;
}

/* 表格样式 */
.status-table, 
.deployment-type-table,
.statistics-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 2px;
    table-layout: fixed;
}

.status-table th,
.status-table td,
.deployment-type-table th,
.deployment-type-table td,
.statistics-table th,
.statistics-table td {
    padding: 10px;
    text-align: center;
    border: none;
    background: #0a1931;
    color: #ffffff;
    white-space: nowrap;
    overflow: hidden;
    border: 1px solid #1d3461;
}

.status-table th,
.deployment-type-table th,
.statistics-table th {
    font-weight: 500;
    position: relative;
    padding: 10px;
    background: #0a1931;
}

.deployment-type-table th,
.statistics-table th {
    text-align: center;
}

.status-table .header-content,
.deployment-type-table .header-content,
.statistics-table .header-content {
    position: relative;
    padding: 6px 12px;
    background: #1d3461;
    border-radius: 4px;
    display: inline-block;
    min-width: 90px;
    max-width: 100%;
    overflow: hidden;
    color: #ffffff;
    font-size: 14px;
}

.status-table .header-content-lang {
    padding: 6px 6px !important;
    max-width: 130px !important;
}

.deployment-type-table .header-content-lang {
    padding: 6px 6px !important;
    max-width: 90px !important;
    white-space: normal !important;
    word-break: break-all;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 1.3;
    margin: 0 auto;
    text-align: center;
}

.status-table td,
.deployment-type-table td,
.statistics-table td {
    padding: 10px;
    text-align: center;
    white-space: nowrap;
    color: #e0e0e0;
}

.status-table td.clickable,
.deployment-type-table td.clickable,
.statistics-table td.clickable {
    cursor: pointer;
}

.status-table td.clickable .number,
.deployment-type-table td.clickable .number,
.statistics-table td.clickable .number {
    display: inline-block;
    min-width: 30px;
    padding: 4px 8px;
    background-color: #3a84f3;
    border-radius: 4px;
    color: #ffffff;
    font-weight: bold;
    transition: all 0.3s;
}

.status-table td.clickable:hover .number,
.deployment-type-table td.clickable:hover .number,
.statistics-table td.clickable:hover .number {
    background-color: #40a9ff;
    transform: scale(1.1);
}

/* 表格分隔线 */
.table-divider {
    height: 10px;
    width: 100%;
    position: relative;
}

.table-divider::after {
    content: "";
    position: absolute;
    top: 4px;
    left: 0;
    width: 100%;
    height: 1px;
    background: #1d3461;
}

/* 弹框表格样式 */
.product-list-wrapper {
    position: relative;
    height: 100%;
}

.product-list-table {
    width: 100%;
    border-collapse: collapse;
}

.table-header {
    position: sticky;
    top: 0;
    z-index: 1;
}

.table-body {
    height: calc(100% - 42px);
    overflow-y: auto;
}

.product-list-table th {
    background-color: #0a1931;
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 2px solid #1d3461;
}

.product-list-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #1d3461;
    background-color: #162b4e;
    color: #e0e0e0;
}

.product-list-table tr:hover td {
    background-color: #1d3461;
}

.product-list-table .status-cell {
    color: #3a84f3;
    font-weight: 500;
}

.product-list-table .empty-row td {
    text-align: center;
    color: #808080;
    padding: 30px 0;
}

/* 弹框样式 */
.layui-layer-content {
    padding: 0 !important;
    overflow: hidden !important;
    background-color: #0a1931 !important;
    color: #ffffff !important;
}

.layui-layer-title {
    background-color: #162b4e !important;
    color: #ffffff !important;
    border-bottom: 1px solid #1d3461 !important;
}

.layui-layer {
    background-color: #0a1931 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
    .layui-layer-setwin {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 0;
        line-height: initial;
    }

    .layui-layer-setwin .layui-layer-close {
        position: relative;
        width: 32px;
        height: 32px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .layui-layer-setwin .layui-layer-close:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: rotate(90deg);
    }

    .layui-layer-setwin .layui-layer-close::before,
    .layui-layer-setwin .layui-layer-close::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 2px;
        background-color: #ffffff;
        transform-origin: center;
    }

    .layui-layer-setwin .layui-layer-close::before {
        transform: translate(-50%, -50%) rotate(45deg);
    }

    .layui-layer-setwin .layui-layer-close::after {
        transform: translate(-50%, -50%) rotate(-45deg);
    }
}

.layui-layer-setwin .layui-layer-close::before,
.layui-layer-setwin .layui-layer-close::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 2px;
    background-color: #ffffff;
    transform-origin: center;
}

.layui-layer-setwin .layui-layer-close::before {
    transform: translate(-50%, -50%) rotate(45deg);
}

.layui-layer-setwin .layui-layer-close::after {
    transform: translate(-50%, -50%) rotate(-45deg);
}

/* 只在主页面隐藏滚动条但保持可滚动 */
body::-webkit-scrollbar {
    display: none;
}

/* 表格滚动条样式 */
.layui-table-main::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.layui-table-main::-webkit-scrollbar-thumb {
    background-color: #3a84f3;
    border-radius: 4px;
}

.layui-table-main::-webkit-scrollbar-track {
    background-color: #1d3461;
}

.table-body::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.table-body::-webkit-scrollbar-thumb {
    background-color: #3a84f3;
    border-radius: 4px;
}

.table-body::-webkit-scrollbar-track {
    background-color: #1d3461;
}

.layui-layer-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.layui-layer-content::-webkit-scrollbar-thumb {
    background-color: #162b4e;
    border-radius: 4px;
}

.layui-layer-content::-webkit-scrollbar-track {
    background-color: #0a1931;
}

* {
    -ms-overflow-style: none;
    /* IE and Edge */
    /* Firefox */
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}
/* 下拉框样式 */
.layui-form-select .layui-input {
    background: #162b4e;
    border-color: #162b4e;
    color: #e0e0e0;
}
.layui-input:hover,
.layui-textarea:hover {
    border-color: #2c374a !important;
}
.layui-input:focus,
.layui-textarea:focus {
    border-color: #1f6de2 !important;
    box-shadow: 0 0 0 3px rgba(22, 183, 119, 0.08);
}

.layui-form-select dl {
    background: #2c2c2c;
    border-color: #3a3a3a;
    color: #e0e0e0;
}

.layui-form-select dl dd.layui-this {
    background-color: #1890ff;
    color: #ffffff;
}

.layui-form-select dl dd:hover {
    background-color: #3a3a3a;
}

* {
    -ms-overflow-style: none;
    /* IE and Edge */
    /* Firefox */
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* 文件列表表格样式 */
.file-list-wrapper {
    background-color: #0a1931 !important;
}

.file-list-wrapper .layui-table {
    background-color: #0a1931;
    color: #e0e0e0;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}
.file-list-wrapper .layui-table-view {
    border: 1px solid #1d3461 !important;
}

.file-list-wrapper .layui-table-fixed-r {
    border: 1px solid #1d3461 !important;
}
.file-list-wrapper .layui-table-header {
    border-bottom: 1px solid #1d3461 !important;
}
.file-list-wrapper .layui-table th {
    background-color: #162b4e !important;
    color: #ffffff !important;
    border: 1px solid #1d3461 !important;
}

.file-list-wrapper .layui-table td {
    background-color: #162b4e !important;
    color: #e0e0e0 !important;
    border: 1px solid #1d3461 !important;
}

.file-list-wrapper .layui-table tr:hover td {
    background-color: #1d3461 !important;
}

.file-list-wrapper .layui-table-page {
    background-color: #0a1931 !important;
    border: none !important;
}

.file-list-wrapper .layui-laypage a {
    color: #e0e0e0 !important;
}

.file-list-wrapper .layui-laypage a:hover {
    color: #40a9ff !important;
}

.file-list-wrapper .layui-laypage-curr .layui-laypage-em {
    background-color: #3a84f3 !important;
}

.file-list-wrapper .layui-laypage-count,
.file-list-wrapper .layui-laypage-limits {
    color: #e0e0e0 !important;
}

.file-list-wrapper .layui-table-view:after {
    width: 0 !important;
}

/* 添加图表加载动画 */
.chart-loading {
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="%233a84f3" stroke-width="8" stroke-linecap="round"><animateTransform attributeName="transform" type="rotate" from="0 50 50" to="360 50 50" dur="1s" repeatCount="indefinite"/></circle></svg>') no-repeat center;
    background-size: 50px;
}

/* 添加全局遮罩层 */
.loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 25, 49, 0.9);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e0e0e0;
    font-size: 18px;
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease;
}

/* 优化表格过渡 */
.file-list-wrapper .layui-table[lay-filter] {
    opacity: 0;
    transition: opacity 0.3s ease;
}
.file-list-wrapper .layui-table[lay-filter].layui-table-rendered {
    opacity: 1;
}

/* 下拉框样式优化 */
.layui-form-select .layui-input {
    background-color: #162b4e !important;
    border-color: #1d3461 !important;
    color: #e0e0e0 !important;
    height: 38px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.layui-form-select dl {
    background-color: #162b4e !important;
    border-color: #1d3461 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-radius: 0 0 4px 4px;
    max-height: 280px;
}

.layui-form-select dl dd {
    color: #e0e0e0 !important;
    transition: all 0.2s;
}

.layui-form-select dl dd.layui-this {
    background-color: #3a84f3 !important;
    color: #ffffff !important;
}

.layui-form-select dl dd:hover {
    background-color: #1d3461 !important;
}

.layui-form-select .layui-select-title i {
    color: #40a9ff !important;
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #0a1931;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #3a84f3;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #40a9ff;
}

::-webkit-scrollbar-corner {
    background: #0a1931;
}

/* 下拉框滚动条样式 */
.layui-form-select dl::-webkit-scrollbar {
    width: 6px;
}

.layui-form-select dl::-webkit-scrollbar-thumb {
    background: rgba(58, 132, 243, 0.8);
    border-radius: 3px;
}

.layui-form-select dl::-webkit-scrollbar-track {
    background: rgba(10, 25, 49, 0.5);
    border-radius: 3px;
}

/* 列装定型表格样式 */
.deployment-type-table th {
    text-align: center;
}

.deployment-type-table .header-content-lang {
    padding: 6px 6px !important;
    max-width: 90px !important;
    white-space: normal !important;
    word-break: break-all;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 1.3;
    margin: 0 auto;
    text-align: center;
}

/* 列装定型表格表头样式 */
.deployment-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 4px;
}

.deployment-header-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.deployment-header-line {
    text-align: center;
    line-height: 1.2;
    white-space: nowrap;
}

/* 统计表格样式 - 美化版本 */
.statistics-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 3px; /* 减小行间距 */
    table-layout: fixed;
}

.statistics-table th {
    padding: 8px 8px; /* 减小内边距 */
    position: relative;
    overflow: visible;
    background: transparent;
}

.statistics-table .system-names th {
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.statistics-table .system-names th .header-content {
    position: relative;
    padding: 6px 10px;
    background: linear-gradient(135deg, #1d3461, #1d4891);
    border-radius: 6px;
    display: inline-block;
    min-width: 90px;
    max-width: 100%;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(58, 132, 243, 0.3);
    transition: all 0.3s ease;
}

.statistics-table .system-names th .header-content:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #1d4891, #3a84f3);
}

.statistics-table .system-names th .header-content::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background: #3a84f3;
    border-radius: 1.5px;
}

.statistics-table td {
    padding: 10px;
    text-align: center;
    background: rgba(10, 25, 49, 0.5);
    border: 1px solid rgba(29, 52, 97, 0.6);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.statistics-table .completion-stats td {
    font-size: 14px;
    padding: 8px 10px;
}

.statistics-table .completion-stats td span {
    font-size: 14px;
    display: inline-block;
    color: #e0e0e0;
    transition: all 0.3s ease;
}

.statistics-table .completion-stats .total {
    color: #a0a0a0;
    font-size: 13px;
    margin-right: 8px;
}

.statistics-table .completion-stats .completed {
    display: inline-block;
    min-width: 30px;
    padding: 5px 10px;
    background: linear-gradient(135deg, #3a84f3, #40a9ff);
    border-radius: 15px;
    color: #ffffff;
    font-weight: bold;
    box-shadow: 0 2px 6px rgba(58, 132, 243, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.statistics-table .completion-stats .completed:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 8px rgba(58, 132, 243, 0.4);
}

/* 进度条样式美化 */
.statistics-table .progress-container {
    position: relative;
    width: 100%;
    height: 18px;  
    background: rgba(10, 25, 49, 0.7);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 4px;
    margin-bottom: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.statistics-table .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3a84f3, #40a9ff);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    transition: width 0.5s ease;
}

.statistics-table .progress-bar::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: progressShine 2s infinite linear;
}

@keyframes progressShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.statistics-table .progress-percentage {
    display: none;
}

.statistics-table .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.7);
    z-index: 2;
    width: 100%;
    text-align: center;
}

/* 表格hover效果和动画 */
.statistics-table td {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.statistics-table td:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.statistics-table td::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(58, 132, 243, 0.05) 0%, 
        rgba(58, 132, 243, 0) 50%, 
        rgba(58, 132, 243, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.statistics-table td:hover::before {
    opacity: 1;
}

/* 数字闪光效果 */
@keyframes numberGlow {
    0% { box-shadow: 0 0 5px rgba(100, 181, 246, 0); }
    50% { box-shadow: 0 0 8px rgba(100, 181, 246, 0.5); }
    100% { box-shadow: 0 0 5px rgba(100, 181, 246, 0); }
}

@keyframes completedNumberGlow {
    0% { box-shadow: 0 0 5px rgba(105, 240, 174, 0); }
    50% { box-shadow: 0 0 8px rgba(105, 240, 174, 0.5); }
    100% { box-shadow: 0 0 5px rgba(105, 240, 174, 0); }
}

.statistics-table .completion-stats .stats-label {
    color: #a0a0a0;
    font-size: 13px;
}

.statistics-table .completion-stats .stats-number {
    font-weight: 600;
    font-size: 15px;
    margin: 0 2px;
    display: inline-block;
    transition: all 0.3s ease;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.statistics-table .completion-stats .total-number {
    color: #64B5F6;
    background: rgba(33, 150, 243, 0.15);
    border-radius: 4px;
    padding: 2px 6px;
    min-width: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
}

.statistics-table .completion-stats .completed-number {
    color: #69F0AE;
    background: rgba(0, 200, 83, 0.15);
    border-radius: 4px;
    padding: 2px 6px;
    min-width: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 200, 83, 0.1);
}

.statistics-table .completion-stats td:hover .completed-number {
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 200, 83, 0.2);
    animation: completedNumberGlow 2s infinite;
}

.statistics-table .completion-stats td:hover .total-number {
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
    animation: numberGlow 2s infinite;
}

/* 加载动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.statistics-table .system-names th {
    animation: fadeIn 0.5s ease-out forwards;
}

.statistics-table .completion-stats td {
    animation: fadeIn 0.5s ease-out 0.2s forwards;
    opacity: 0;
}

.statistics-table .progress-bars td {
    animation: fadeIn 0.4s ease-out 0.3s forwards; /* 减少延迟 */
    opacity: 0;
}

/* 系统名称响应式设计 */
@media (max-width: 1200px) {
    .statistics-table .system-names th .header-content {
        font-size: 14px;
        padding: 8px 12px;
    }
    
    .statistics-table .completion-stats .total {
        font-size: 12px;
    }
    
    .statistics-table .completion-stats .completed {
        font-size: 13px;
        padding: 4px 8px;
    }
}

.statistics-table .progress-bars td {
    padding: 4px 10px 6px 10px; /* 调整内边距以降低高度 */
}

.stats-number.clickable {
    cursor: pointer;
    color: #1E9FFF;
    text-decoration: underline;
}

.stats-number.clickable:hover {
    color: #66B3FF;
}

.title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #162b4e, #1d3461, #3a84f3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    padding: 0 20px;
    position: relative;
}

.title-container .page-title {
    flex: 1;
    background: none;
    box-shadow: none;
    text-align: center;
    padding: 15px 0;
}

.title-container .page-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #3a84f3, #40a9ff);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.model-selector {
    min-width: 280px;
    padding-right: 10px;
}

.model-selector .layui-form-item {
    margin-bottom: 0;
}

.model-selector .layui-form-label {
    width: 80px;
    color: #e0e0e0;
    background: #162b4e;
    border-color: #1d3461;
    font-size: 14px;
    padding: 8px 10px;
    height: 32px;
    line-height: 16px;
    border-radius: 4px 0 0 4px;
    text-align: center;
}

.model-selector .layui-input-inline {
    width: 180px;
    margin-right: 0;
}

.model-selector .layui-form-select .layui-input {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
}
