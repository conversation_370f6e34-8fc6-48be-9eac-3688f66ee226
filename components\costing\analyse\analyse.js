var THING = "Thing.Fn.Costing";
var currentUser = sessionStorage.getItem("username");
var $, form, layer, util, table, laydate, upload;
layui.use(['form', 'util', 'table', 'laydate'], function() {
	$ = layui.$;
	form = layui.form;
	layer = layui.layer;
	util = layui.util;
	table = layui.table;
	laydate = layui.laydate;
	upload = layui.upload;

	renderBtn();
});

function renderBtn() {
	$('#eidt-table').unbind("click").bind('click', function() {
		layer.open({
			title: '编辑表单',
			type: 1,
			fixed: false,
			maxmin: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			zIndex: 1000,
			shadeClose: false,
			resize: false, //不允许拉伸
			area: [$(window).width() + 'px', $(window).height() + 'px'],
			content: '<div id="handsontable" style="width:100%;height:100%;"></div>',
			cancel: function(index, layero) {
				if (confirm('确定要关闭么')) {
					layer.close(index);
				}
				return false;
			},
			btn: ['提交', '取消'],
			btn1: function(index, layero, that) {

			},
			btn2: function(index, layero, that) {
				if (confirm('确定要关闭么')) {
					layer.close(index);
				}
				return false;
			},
			success: function(layero, index) {
				var saveData = formData;
				renderForm(saveData);
			}
		});
	});
}