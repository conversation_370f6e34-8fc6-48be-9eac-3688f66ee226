/**
 * 产品统计图表模块
 * 负责处理产品数量统计图表的展示
 */

// 绘制产品数量统计图
function drawProductChart(data) {
    if (!window.productChart) return;
    
    // 为每个数据项添加渐变色
    var coloredData = data.map(function(item, index) {
        // 根据不同级别设置不同的渐变色
        var colors;
        switch(item.name) {
            case '一级':
                colors = [{ offset: 0, color: '#F5222D' }, { offset: 1, color: '#FF7875' }];
                break;
            case '二级':
                colors = [{ offset: 0, color: '#2B5AED' }, { offset: 1, color: '#5B8FF9' }];
                break;
            case '三级':
                colors = [{ offset: 0, color: '#52C41A' }, { offset: 1, color: '#95DE64' }];
                break;
            case '四级':
                colors = [{ offset: 0, color: '#722ED1' }, { offset: 1, color: '#B37FEB' }];
                break;
            default:
                colors = [{ offset: 0, color: '#FA8C16' }, { offset: 1, color: '#FFC069' }];
        }
        
        return {
            name: item.name,
            value: item.value,
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, colors),
                borderRadius: 10,
                borderColor: '#162b4e',
                borderWidth: 2
            }
        };
    });
    
    // 获取当前选中的型号
    var modelId = $("#model-select").val();
    var modelName = $("#model-select option:selected").text();
    var titleText = '产品数量统计';
    
    // 如果选择了特定型号（不是"所有型号"），则在标题中添加型号名称
    if (modelId !== "-1") {
        titleText = modelName + " - " + titleText;
    }
    
    var option = {
        title: {
            text: titleText,
            left: 'center',
            textStyle: {
                color: '#ffffff'
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                return '<div style="padding: 4px 0; font-weight: 600; color: #fff;">' +
                    params.seriesName + '</div>' +
                    '<div style="display: flex; justify-content: space-between; margin: 5px 0;">' +
                    '<span style="font-size: 13px; margin-right: 15px; color: #eee;">' + params.name + ':</span>' +
                    '<span style="font-size: 14px; font-weight: 600;">' + params.value + ' 个</span>' +
                    '</div>';
            },
            backgroundColor: 'rgba(22, 43, 78, 0.9)',
            borderColor: '#1D3461',
            borderWidth: 1,
            borderRadius: 4,
            padding: [10, 15],
            textStyle: {
                color: '#ffffff'
            },
            extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);'
        },
        series: [{
            name: '产品数量',
            type: 'pie',
            radius: ['35%', '70%'],
            center: ['50%', '50%'],  
            avoidLabelOverlap: false,
            label: {
                show: true,
                position: 'outside',
                formatter: '{b}: {c} ({d}%)',
                color: '#ffffff'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '16',
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: true,
                length: 15,
                length2: 10,
                lineStyle: {
                    color: '#ffffff',
                    opacity: 0.5
                }
            },
            data: coloredData
        }]
    };
    productChart.setOption(option);
    
    // 添加点击事件
    productChart.off('click');
    productChart.on('click', function (params) {
        var modelId = $("#model-select").val();
        var levelMap = { '二级': 2, '三级': 3, '四级': 4, '一级': 1 };
        var level = levelMap[params.data.name];

        // 获取所有产品
        twxAjax('Thing.Fn.TestEvaluation', 'GetProductListV2', {
            modelId: modelId,
            subsystemName: params.data.name  
        }, true, function (res) {
            if (res.success) {
                window.generateProductTable(res.data, params.data.name + ' 产品列表');
            } else {
                layer.msg(res.msg, { icon: 2 });
            }
        });
    });
}

// 导出函数
window.drawProductChart = drawProductChart;