import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.FileVisitResult;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class Deploy {
	private static final String TOMCAT_WEBAPP_PATH = "C:\\Program Files\\Apache Software Foundation\\Tomcat 8.5\\webapps\\DataPackageManagement";
	private static final String FILE_HANDLE_PATH = "E:\\数据包文件服务";
	private static final int FILE_HANDLE_PORT = 7081;
	private static final String TWX_SERVER = "http://127.0.0.1:8011";
	private static final String TWX_IMPORT_URL = TWX_SERVER + "/Thingworx/Importer?IgnoreBadValueStreamData=false&WithSubsystems=false&purpose=import&usedefaultdataprovider=false&appKey=8ea8ace9-1167-4417-939f-a7ddc58d9429";
	private static final String TWX_SQL_URL = TWX_SERVER + "/Thingworx/Things/Thing.DB.Oracle/Services/RunCommand?method=POST&Accept=application/json&appKey=8ea8ace9-1167-4417-939f-a7ddc58d9429";
	private static final String MAVEN_REPOSITORY_PATH = "D:\\soft\\dev\\apache-maven-3.9.9\\repository";
	private static final String WORKSPACE_FILEHANDLE_PATH = "E:\\workspace\\FileHandle";

	// AES解密密钥（与PackageUpdater中的密钥保持一致）
	private static final String DECRYPT_KEY = "CirPoint2025Key!";
	private static final String DECRYPT_ALGORITHM = "AES";

	private static PrintWriter logWriter;

	public static void main(String[] args) throws Exception {
		deploy();
	}

	public static void deploy() {
		// 创建日志文件
		String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
		File logFile = new File("deploy_" + timestamp + ".log");

		try {
			// 初始化日志写入器
			logWriter = new PrintWriter(new FileWriter(logFile, true), true);

			log("开始部署程序...");

			// 1. 更新DataPackageManagement文件夹
			log("开始更新DataPackageManagement文件夹...");
			File sourceDir = new File("DataPackageManagement");
			File targetDir = new File(TOMCAT_WEBAPP_PATH);
			if (!targetDir.exists()) {
				targetDir.mkdirs();
			}
			updateDirectory(sourceDir, targetDir);
			log("DataPackageManagement文件夹更新完成");

			// 1.1 更新FileHandle源码文件夹
			updateFileHandleSource();

			// 1.2 更新Maven仓库
			updateMavenRepository();

			// 2. 关闭file-handle程序
			log("正在关闭file-handle程序...");
			killRemoteProcess();
			// 等待进程完全关闭
			Thread.sleep(2000);

			// 3. 删除旧文件
			log("正在删除旧文件...");
			File resourcesDir = new File(FILE_HANDLE_PATH + "\\resources");
			File jarFile = new File(FILE_HANDLE_PATH + "\\file-handle.jar");
			deleteDirectory(resourcesDir);
			if (jarFile.exists()) {
				jarFile.delete();
			}

			// 4. 拷贝新文件
			log("正在拷贝新文件...");
			File fileHandleSource = new File("file-handle");
			File fileHandleTarget = new File(FILE_HANDLE_PATH);
			updateDirectory(fileHandleSource, fileHandleTarget);

			// 4.1 解密合格证文件
			log("正在解密合格证文件...");
			decryptCertificateFiles();

			// 5. 导入XML文件到Thingworx
			log("开始导入XML文件到Thingworx...");
			File xmlDir = new File("DataPackageManagement\\twx\\_xmls");
			if (xmlDir.exists() && xmlDir.isDirectory()) {
				File[] xmlFiles = xmlDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".xml"));
				if (xmlFiles != null && xmlFiles.length > 0) {
					for (File xmlFile : xmlFiles) {
						log("正在导入: " + xmlFile.getName());
						try {
							importXmlToThingworx(xmlFile);
						} catch (Exception e) {
							logError("导入文件 " + xmlFile.getName() + " 失败: " + e.getMessage());
						}
					}
				} else {
					log("未找到需要导入的XML文件");
				}
			} else {
				log("XML文件目录不存在: " + xmlDir.getAbsolutePath());
			}

			// 6. 执行SQL文件
			log("开始执行SQL文件...");
			File sqlDir = new File("DataPackageManagement\\sql");
			if (sqlDir.exists() && sqlDir.isDirectory()) {
				File[] sqlFiles = sqlDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".sql"));
				if (sqlFiles != null && sqlFiles.length > 0) {
					// 按文件名排序
					Arrays.sort(sqlFiles, Comparator.comparing(File::getName));

					for (File sqlFile : sqlFiles) {
						log("开始执行SQL文件: " + sqlFile.getName());
						try {
							executeSqlFile(sqlFile);
						} catch (Exception e) {
							logError("执行SQL文件 " + sqlFile.getName() + " 失败: " + e.getMessage());
						}
					}
				} else {
					log("未找到需要执行的SQL文件");
				}
			} else {
				log("SQL文件目录不存在: " + sqlDir.getAbsolutePath());
			}

			// 7. 启动新程序
			log("正在启动file-handle程序...");
			startService();
			log("部署完成！");
		} catch (Exception e) {
			logError("部署过程中出现错误: " + e.getMessage());
			e.printStackTrace(logWriter);
		} finally {
			if (logWriter != null) {
				logWriter.close();
			}
		}
	}


	/**
	 * 启动服务
	 */
	private static void startService() throws IOException, InterruptedException {
		String startupScript = FILE_HANDLE_PATH + File.separator + "startup-prod.bat";

		File scriptFile = new File(startupScript);
		if (!scriptFile.exists() || !scriptFile.isFile()) {
			throw new FileNotFoundException("启动脚本不存在: " + startupScript);
		}

		log("开始启动服务: " + startupScript, "info");

		// 使用Runtime.exec()直接执行命令
		// 构建命令字符串，使用start命令打开新窗口
		String command = "cmd.exe /c start \"服务启动窗口\" cmd.exe /k \"cd /d \"" + FILE_HANDLE_PATH + "\" && \"" + startupScript + "\"\"";

		log("执行命令: " + command, "info");

		// 使用Runtime.exec()执行命令
		Runtime.getRuntime().exec(command);

		// 等待一小段时间
		Thread.sleep(2000);

		// 不等待进程结束，因为它会在新窗口中运行
		log("服务启动命令已执行，已在服务器显示命令窗口", "success");
	}

	
	/**
	 * 获取命令行输出的字符编码
	 *
	 * @return 字符编码名称
	 */
	private  static String getCommandOutputEncoding() {
		String osName = System.getProperty("os.name").toLowerCase();
		if (osName.contains("windows")) {
			return "GBK";  // Windows默认命令行编码
		} else {
			return "UTF-8";  // Linux/Mac默认编码
		}
	}

	/**
	 * 创建命令行输出的读取器
	 *
	 * @param process 进程
	 * @return 缓冲读取器
	 * @throws IOException 如果创建读取器失败
	 */
	private static BufferedReader createProcessOutputReader(Process process) throws IOException {
		return new BufferedReader(new InputStreamReader(
				process.getInputStream(), getCommandOutputEncoding()));
	}

	/**
	 * 杀死远程进程
	 *
	 * @return 是否成功停止
	 */
	private static boolean killRemoteProcess() {
		try {

			// 查找并杀死占用jar文件的进程
			String jarPath = FILE_HANDLE_PATH + File.separator + "file-handle.jar";


			// Windows查找占用文件的进程
			List<String> commands = new ArrayList<>();
			commands.add("cmd");
			commands.add("/c");
			commands.add("tasklist /FI \"MODULES eq file-handle.jar\" /FO CSV");

			ProcessBuilder processBuilder = new ProcessBuilder(commands);
			processBuilder.redirectErrorStream(true);

			Process process = processBuilder.start();
			boolean foundProcess = false;
			int pid = -1;

			try (BufferedReader reader = createProcessOutputReader(process)) {
				String line;
				while ((line = reader.readLine()) != null) {
					log("进程信息: " + line, "info");
					// CSV格式，第2列是PID
					if (line.contains("java.exe") || line.contains("javaw.exe")) {
						String[] parts = line.split(",");
						if (parts.length >= 2) {
							// 提取PID
							String pidStr = parts[1].replaceAll("\"", "").trim();
							try {
								pid = Integer.parseInt(pidStr);
								foundProcess = true;
								log("找到占用进程: " + pidStr, "info");
							} catch (NumberFormatException e) {
								log("解析PID失败: " + pidStr, "warn");
							}
						}
					}
				}
			}

			process.waitFor();

			if (!foundProcess) {
				// 通过端口查找进程
				log("通过端口 " + FILE_HANDLE_PORT + " 查找进程", "info");

				commands.clear();
				commands.add("cmd");
				commands.add("/c");
				commands.add("netstat -ano | findstr :" + FILE_HANDLE_PORT);

				processBuilder = new ProcessBuilder(commands);
				processBuilder.redirectErrorStream(true);

				process = processBuilder.start();

				try (BufferedReader reader = createProcessOutputReader(process)) {
					String line;
					while ((line = reader.readLine()) != null) {
						log("端口信息: " + line, "info");
						if (line.contains("LISTENING") || line.contains("ESTABLISHED")) {
							String[] parts = line.trim().split("\\s+");
							if (parts.length >= 5) {
								String pidStr = parts[4].trim();
								try {
									pid = Integer.parseInt(pidStr);
									foundProcess = true;
									log("找到占用端口进程: " + pidStr, "info");
									break;
								} catch (NumberFormatException e) {
									log("解析PID失败: " + pidStr, "warn");
								}
							}
						}
					}
				}

				process.waitFor();
			}

			if (foundProcess && pid > 0) {
				// 尝试多种方式终止进程
				boolean success = false;

				// 方法1: 使用taskkill命令
				log("方法1: 使用taskkill命令终止进程 PID: " + pid, "info");
				success = killProcessWithTaskKill(pid);

				// 如果方法1失败，尝试方法2: 使用PowerShell以管理员权限终止
				if (!success) {
					log("方法2: 尝试使用PowerShell以管理员权限终止进程 PID: " + pid, "info");
					success = killProcessWithPowerShell(pid);
				}

				// 如果方法2也失败，尝试方法3: 使用wmic命令终止
				if (!success) {
					log("方法3: 尝试使用wmic命令终止进程 PID: " + pid, "info");
					success = killProcessWithWmic(pid);
				}

				if (success) {
					log("成功终止进程 PID: " + pid, "success");

					// 查找并关闭相关的CMD窗口
					log("查找并关闭相关的CMD窗口", "info");
					killRelatedCmdWindows();

					// 等待资源释放
					Thread.sleep(3000);
					return true;
				} else {
					log("所有终止方法都失败，无法终止进程 PID: " + pid, "error");
					log("建议: 请尝试手动以管理员身份终止该进程，或者重启系统", "warn");
					return false;
				}
			} else {
				log("未找到占用文件或端口的进程", "warn");
				// 如果没找到进程，可能文件已经没有被占用了，尝试关闭相关的CMD窗口
				killRelatedCmdWindows();
				return true;
			}

		} catch (Exception e) {
			log("终止进程发生异常: " + e.getMessage(), "error");
			return false;
		}
	}

	/**
	 * 查找并关闭相关的CMD窗口
	 */
	private static void killRelatedCmdWindows() {
		try {
			// 查找包含服务启动窗口标题的CMD进程
			List<String> commands = new ArrayList<>();
			commands.add("cmd");
			commands.add("/c");
			commands.add("tasklist /FI \"WINDOWTITLE eq 服务启动窗口*\" /FO CSV");

			ProcessBuilder processBuilder = new ProcessBuilder(commands);
			processBuilder.redirectErrorStream(true);

			Process process = processBuilder.start();
			List<Integer> cmdPids = new ArrayList<>();

			try (BufferedReader reader = createProcessOutputReader(process)) {
				String line;
				while ((line = reader.readLine()) != null) {
					if (line.contains("cmd.exe")) {
						String[] parts = line.split(",");
						if (parts.length >= 2) {
							String pidStr = parts[1].replaceAll("\"", "").trim();
							try {
								int cmdPid = Integer.parseInt(pidStr);
								cmdPids.add(cmdPid);
								log("找到CMD窗口进程: " + cmdPid, "info");
							} catch (NumberFormatException e) {
								log("解析CMD PID失败: " + pidStr, "warn");
							}
						}
					}
				}
			}

			process.waitFor();

			// 如果没有找到匹配窗口标题的CMD，尝试查找所有CMD进程
			if (cmdPids.isEmpty()) {
				// 查找可能与file-handle.jar相关的CMD进程

				commands.clear();
				commands.add("cmd");
				commands.add("/c");
				commands.add("wmic process where \"name='cmd.exe' and commandline like '%file-handle%'\" get processid /format:csv");

				processBuilder = new ProcessBuilder(commands);
				processBuilder.redirectErrorStream(true);

				process = processBuilder.start();

				try (BufferedReader reader = createProcessOutputReader(process)) {
					String line;
					while ((line = reader.readLine()) != null) {
						if (line.contains("ProcessId")) {
							continue; // 跳过标题行
						}
						if (!line.trim().isEmpty() && line.contains(",")) {
							String[] parts = line.split(",");
							if (parts.length >= 2) {
								String pidStr = parts[parts.length - 1].trim();
								try {
									int cmdPid = Integer.parseInt(pidStr);
									cmdPids.add(cmdPid);
									log("找到相关CMD进程: " + cmdPid, "info");
								} catch (NumberFormatException e) {
									// 忽略非数字
								}
							}
						}
					}
				}

				process.waitFor();
			}

			// 终止找到的CMD进程
			for (Integer cmdPid : cmdPids) {
				log("终止CMD窗口进程: " + cmdPid, "info");
				killProcessWithTaskKill(cmdPid);
			}

			if (cmdPids.isEmpty()) {
				log("未找到相关的CMD窗口进程", "warn");
			} else {
				log("成功关闭 " + cmdPids.size() + " 个CMD窗口", "success");
			}

		} catch (Exception e) {
			log("关闭CMD窗口发生异常: " + e.getMessage(), "warn");
		}
	}

	/**
	 * 使用taskkill命令终止进程
	 */
	private static boolean killProcessWithTaskKill(int pid) {
		try {
			List<String> commands = new ArrayList<>();
			commands.add("cmd");
			commands.add("/c");
			commands.add("taskkill /F /PID " + pid);

			ProcessBuilder processBuilder = new ProcessBuilder(commands);
			processBuilder.redirectErrorStream(true);

			Process process = processBuilder.start();

			boolean hasAccessDenied = false;

			try (BufferedReader reader = createProcessOutputReader(process)) {
				String line;
				while ((line = reader.readLine()) != null) {
					log(line, "info");
					if (line.contains("拒绝访问") || line.contains("Access is denied")) {
						hasAccessDenied = true;
					}
				}
			}

			int exitCode = process.waitFor();
			boolean success = exitCode == 0;

			if (hasAccessDenied) {
				log("taskkill命令被拒绝访问，可能需要管理员权限", "warn");
				return false;
			}

			return success;
		} catch (Exception e) {
			log("taskkill命令执行异常: " + e.getMessage(), "error");
			return false;
		}
	}

	/**
	 * 使用PowerShell以管理员权限终止进程
	 */
	private static boolean killProcessWithPowerShell(int pid) {
		try {
			// 创建PowerShell命令
			String psCommand = "powershell -Command \"Start-Process -Verb RunAs powershell -ArgumentList '-NonInteractive -Command \\\"Stop-Process -Id " + pid + " -Force\\\"'\"";

			log("执行PowerShell命令: " + psCommand, "info");

			Process process = Runtime.getRuntime().exec(psCommand);

			try (BufferedReader reader = createProcessOutputReader(process)) {
				String line;
				while ((line = reader.readLine()) != null) {
					log(line, "info");
				}
			}

			// 由于使用了Start-Process -Verb RunAs，可能会弹出UAC提示，我们不确定进程何时结束
			// 给一个合理的等待时间
			process.waitFor(5, TimeUnit.SECONDS);

			// 检查进程是否还存在
			boolean processExists = checkIfProcessExists(pid);
			return !processExists;
		} catch (Exception e) {
			log("PowerShell命令执行异常: " + e.getMessage(), "error");
			return false;
		}
	}

	/**
	 * 使用wmic命令终止进程
	 */
	private static boolean killProcessWithWmic(int pid) {
		try {
			// 创建wmic命令
			String wmicCommand = "wmic process where processid=" + pid + " delete";

			log("执行wmic命令: " + wmicCommand, "info");

			Process process = Runtime.getRuntime().exec(wmicCommand);

			try (BufferedReader reader = createProcessOutputReader(process)) {
				String line;
				while ((line = reader.readLine()) != null) {
					log(line, "info");
				}
			}

			int exitCode = process.waitFor();

			// 检查进程是否还存在
			boolean processExists = checkIfProcessExists(pid);
			return !processExists;
		} catch (Exception e) {
			log("wmic命令执行异常: " + e.getMessage(), "error");
			return false;
		}
	}

	/**
	 * 检查进程是否存在
	 */
	private static boolean checkIfProcessExists(int pid) {
		try {
			String command = "tasklist /FI \"PID eq " + pid + "\" /NH";

			Process process = Runtime.getRuntime().exec(command);

			try (BufferedReader reader = createProcessOutputReader(process)) {
				String line;
				while ((line = reader.readLine()) != null) {
					if (line.contains(String.valueOf(pid))) {
						return true;
					}
				}
			}

			return false;
		} catch (Exception e) {
			log("检查进程存在状态异常: " + e.getMessage(), "warn");
			return false; // 假设进程不存在
		}
	}


	private static void deleteDirectory(File directory) throws IOException {
		if (!directory.exists()) {
			return;
		}

		if (!directory.isDirectory()) {
			throw new IOException("路径不是目录: " + directory.getAbsolutePath());
		}

		File[] files = directory.listFiles();
		if (files != null) {
			for (File file : files) {
				if (file.isDirectory()) {
					deleteDirectory(file);
				} else {
					if (!file.delete()) {
						throw new IOException("无法删除文件: " + file.getAbsolutePath());
					}
				}
			}
		}

		if (!directory.delete()) {
			throw new IOException("无法删除目录: " + directory.getAbsolutePath());
		}
	}

	/**
	 * 递归统计目录中的文件和文件夹数量
	 * @param directory 要统计的目录
	 * @return 文件和文件夹的总数量
	 */
	private static int countFilesRecursively(File directory) {
		if (directory == null || !directory.exists() || !directory.isDirectory()) {
			return 0;
		}

		int count = 0;
		File[] files = directory.listFiles();
		if (files != null) {
			for (File file : files) {
				if (file != null) {
					count++;
					if (file.isDirectory()) {
						count += countFilesRecursively(file);
					}
				}
			}
		}
		return count;
	}

	private static void updateDirectory(File source, File target) throws IOException {
		if (!source.exists()) {
			log("源文件夹不存在: " + source.getAbsolutePath());
			return;
		}

		if (!target.exists()) {
			target.mkdirs();
		}

		File[] files = source.listFiles();
		if (files != null) {
			for (File sourceFile : files) {
				File targetFile = new File(target, sourceFile.getName());

				if (sourceFile.isDirectory()) {
					updateDirectory(sourceFile, targetFile);
				} else {
					// 复制新文件，如果目标文件存在会自动覆盖
					copyFile(sourceFile, targetFile);
				}
			}
		}
	}

	/**
	 * 更新FileHandle源码文件夹
	 * 采用"先删除后替换"策略确保完全同步，避免文件残留问题
	 */
	private static void updateFileHandleSource() throws IOException {
		log("开始更新FileHandle源码文件夹...");
		File sourceDir = new File("FileHandle");
		File targetDir = new File(WORKSPACE_FILEHANDLE_PATH);

		if (!sourceDir.exists() || !sourceDir.isDirectory()) {
			log("源FileHandle目录不存在: " + sourceDir.getAbsolutePath());
			return;
		}

		if (!targetDir.exists()) {
			targetDir.mkdirs();
		}

		// 第一步：完全清理目标目录的src文件夹
		File targetSrcDir = new File(targetDir, "src");
		if (targetSrcDir.exists()) {
			log("警告: 即将执行破坏性操作 - 完全删除目标src目录: " + targetSrcDir.getAbsolutePath());

			// 统计要删除的文件数量
			int fileCount = countFilesRecursively(targetSrcDir);
			log("目标src目录包含 " + fileCount + " 个文件/文件夹");

			// 执行删除操作
			deleteDirectory(targetSrcDir);

			// 验证删除是否成功
			if (targetSrcDir.exists()) {
				throw new IOException("目标src目录删除失败，无法继续更新操作");
			}

			log("目标src目录清理完成，已删除 " + fileCount + " 个文件/文件夹");
		} else {
			log("目标src目录不存在，跳过清理步骤");
		}

		// 第二步：检查源码中是否有src目录
		File sourceSrcDir = new File(sourceDir, "src");
		if (sourceSrcDir.exists() && sourceSrcDir.isDirectory()) {
			// 统计源src目录的文件数量
			int sourceFileCount = countFilesRecursively(sourceSrcDir);
			log("开始复制新的src目录，包含 " + sourceFileCount + " 个文件/文件夹");

			// 重新创建目标src目录
			if (!targetSrcDir.mkdirs() && !targetSrcDir.exists()) {
				throw new IOException("无法创建目标src目录: " + targetSrcDir.getAbsolutePath());
			}

			// 复制源src目录的所有内容到目标src目录
			updateDirectory(sourceSrcDir, targetSrcDir);

			// 验证复制结果
			int targetFileCount = countFilesRecursively(targetSrcDir);
			if (targetFileCount != sourceFileCount) {
				log("警告: 复制后的文件数量不匹配 - 源: " + sourceFileCount + ", 目标: " + targetFileCount);
			}

			log("新的src目录复制完成，成功复制 " + targetFileCount + " 个文件/文件夹");
		} else {
			log("警告: 源FileHandle目录中未找到src文件夹: " + sourceSrcDir.getAbsolutePath());
		}

		// 第三步：复制其他文件和目录（除了src目录）
		log("开始复制其他文件和目录...");
		File[] sourceFiles = sourceDir.listFiles();
		if (sourceFiles != null) {
			for (File sourceFile : sourceFiles) {
				// 跳过src目录，因为已经单独处理了
				if ("src".equals(sourceFile.getName())) {
					continue;
				}

				File targetFile = new File(targetDir, sourceFile.getName());
				if (sourceFile.isDirectory()) {
					updateDirectory(sourceFile, targetFile);
				} else {
					copyFile(sourceFile, targetFile);
				}
			}
		}

		log("FileHandle源码文件夹更新完成");
		log("更新策略: 先删除目标src目录，再完整复制源src目录，确保代码完全同步");
		log("此策略避免了新版本中已删除文件在目标目录中的残留问题");

		// 解密工作空间中的合格证文件
		log("开始解密工作空间中的合格证文件...");
		decryptWorkspaceCertificateFiles();
	}

	/**
	 * 更新Maven仓库
	 */
	private static void updateMavenRepository() throws IOException {
		log("开始更新Maven仓库...");
		File repoSource = new File("repository");
		File repoTarget = new File(MAVEN_REPOSITORY_PATH);
		
		if (!repoSource.exists() || !repoSource.isDirectory()) {
			log("源仓库目录不存在: " + repoSource.getAbsolutePath());
			return;
		}
		
		if (!repoTarget.exists()) {
			repoTarget.mkdirs();
		}
		
		// 复制目录内容
		updateDirectory(repoSource, repoTarget);
		log("Maven仓库更新完成");
	}

	private static void copyFile(File source, File target) throws IOException {
		try (InputStream in = Files.newInputStream(source.toPath());
			 OutputStream out = Files.newOutputStream(target.toPath())) {
			byte[] buffer = new byte[8192];
			int length;
			while ((length = in.read(buffer)) > 0) {
				out.write(buffer, 0, length);
			}
		}
	}

	private static void importXmlToThingworx(File xmlFile) throws Exception {
		if (!xmlFile.exists() || !xmlFile.isFile()) {
			throw new IllegalArgumentException("XML文件不存在: " + xmlFile.getAbsolutePath());
		}

		String boundary = "----WebKitFormBoundary" + Long.toHexString(System.currentTimeMillis());
		URL url = new URL(TWX_IMPORT_URL);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();

		try {
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");

			// 设置请求头
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
			conn.setRequestProperty("Accept", "*/*");
			conn.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9");
			conn.setRequestProperty("X-Requested-With", "XMLHttpRequest");
			conn.setRequestProperty("X-XSRF-TOKEN", "TWX-XSRF-TOKEN-VALUE");
			conn.setRequestProperty("Referer", TWX_SERVER + "/Thingworx/Composer/Apps/Composer/index.html");

			try (OutputStream out = conn.getOutputStream();
				 PrintWriter writer = new PrintWriter(new OutputStreamWriter(out, StandardCharsets.UTF_8), true)) {

				// 写入文件头
				writer.append("--").append(boundary).append("\r\n");
				writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"")
						.append(xmlFile.getName()).append("\"\r\n");
				writer.append("Content-Type: text/xml\r\n\r\n");
				writer.flush();

				// 写入文件内容
				try (FileInputStream fis = new FileInputStream(xmlFile)) {
					byte[] buffer = new byte[8192];
					int length;
					while ((length = fis.read(buffer)) > 0) {
						out.write(buffer, 0, length);
					}
					out.flush();
				}

				// 写入结束标记
				writer.append("\r\n--").append(boundary).append("--\r\n");
				writer.flush();
			}

			// 获取响应
			int responseCode = conn.getResponseCode();
			if (responseCode != HttpURLConnection.HTTP_OK) {
				throw new IOException("导入失败，HTTP错误码: " + responseCode);
			}

			// 读取响应内容
			try (BufferedReader reader = new BufferedReader(
					new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
				StringBuilder response = new StringBuilder();
				String line;
				while ((line = reader.readLine()) != null) {
					response.append(line);
				}
				String responseStr = response.toString();
				log("文件 " + xmlFile.getName() + " 导入结果: " + responseStr);

				// 检查是否包含success
				if (!responseStr.toLowerCase().contains("success")) {
					throw new IOException("导入未成功: " + responseStr);
				}
			}
		} finally {
			conn.disconnect();
		}
	}

	private static void log(String message) {
		String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		String logMessage = String.format("[%s] %s", timestamp, message);
		System.out.println(logMessage);
		if (logWriter != null) {
			logWriter.println(logMessage);
		}
	}

	private static void log(String message, String level) {
		log(message);
	}

	private static void logError(String message) {
		String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		String logMessage = String.format("[%s] ERROR: %s", timestamp, message);
		System.err.println(logMessage);
		if (logWriter != null) {
			logWriter.println(logMessage);
		}
	}

	/**
	 * 执行SQL语句
	 *
	 * @param sql 要执行的SQL语句
	 * @throws Exception 执行过程中的异常
	 */
	private static void executeSql(String sql) throws Exception {
		if (sql == null || sql.trim().isEmpty()) {
			return;
		}

		sql = sql.trim();
		log("执行SQL: " + sql);

		URL url = new URL(TWX_SQL_URL);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();

		try {
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setRequestMethod("POST");

			// 设置请求头
			conn.setRequestProperty("Content-Type", "application/json");
			conn.setRequestProperty("Accept", "application/json");
			conn.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9");
			conn.setRequestProperty("X-Requested-With", "XMLHttpRequest");
			conn.setRequestProperty("Referer", TWX_SERVER + "/Thingworx/Composer/Apps/Composer/index.html");

			// 构建请求体
			String requestBody = String.format("{\"sql\":\"%s\"}",
					sql.replace("\"", "\\\""));  // 转义SQL中的双引号

			// 发送请求
			try (OutputStream out = conn.getOutputStream();
				 PrintWriter writer = new PrintWriter(new OutputStreamWriter(out, StandardCharsets.UTF_8), true)) {
				writer.write(requestBody);
				writer.flush();
			}

			// 获取响应
			int responseCode = conn.getResponseCode();
			if (responseCode != HttpURLConnection.HTTP_OK) {
				throw new IOException("SQL执行失败，HTTP错误码: " + responseCode);
			}

			// 读取响应内容
			try (BufferedReader reader = new BufferedReader(
					new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
				StringBuilder response = new StringBuilder();
				String line;
				while ((line = reader.readLine()) != null) {
					response.append(line);
				}
				String responseStr = response.toString();
				log("SQL执行结果: " + responseStr);

				// 检查响应中是否包含错误信息
				if (responseStr.toLowerCase().contains("error")) {
					throw new IOException("SQL执行失败: " + responseStr);
				}
			}
		} finally {
			conn.disconnect();
		}
	}

	/**
	 * 执行SQL文件
	 *
	 * @param sqlFile SQL文件
	 * @throws Exception 执行过程中的异常
	 */
	private static void executeSqlFile(File sqlFile) throws Exception {
		log("读取SQL文件: " + sqlFile.getName());

		try (BufferedReader reader = new BufferedReader(new InputStreamReader(Files.newInputStream(sqlFile.toPath()), StandardCharsets.UTF_8))) {
			StringBuilder sqlBuilder = new StringBuilder();
			String line;
			int lineNumber = 0;
			boolean insideBeginEnd = false;

			while ((line = reader.readLine()) != null) {
				lineNumber++;

				// 删除行内注释（--和//注释）
				int commentIndex;
				if ((commentIndex = line.indexOf("--")) != -1) {
					line = line.substring(0, commentIndex);
				}
				if ((commentIndex = line.indexOf("//")) != -1) {
					line = line.substring(0, commentIndex);
				}

				line = line.trim();

				// 跳过空行
				if (line.isEmpty()) {
					continue;
				}

				// 检查是否进入BEGIN...END块
				if (line.toUpperCase().contains("BEGIN")) {
					insideBeginEnd = true;
					sqlBuilder.setLength(0); // 清空之前的SQL
					log("跳过BEGIN...END块");
					continue;
				}

				// 检查是否离开BEGIN...END块
				if (insideBeginEnd) {
					if (line.toUpperCase().endsWith("END;")) {
						insideBeginEnd = false;
					}
					continue;
				}

				sqlBuilder.append(line).append(" ");

				// 检查是否是SQL语句的结束
				if (line.endsWith(";") || line.endsWith("/")) {
					String sql = sqlBuilder.toString().trim();
					// 移除结尾的分号或斜杠
					sql = sql.replaceAll("[;/]\\s*$", "");

					try {
						executeSql(sql);
						log("第 " + lineNumber + " 行SQL执行成功");
					} catch (Exception e) {
						logError("第 " + lineNumber + " 行SQL执行失败: " + e.getMessage());
						// 继续执行下一条SQL
					}

					// 清空SQL构建器，准备下一条SQL
					sqlBuilder.setLength(0);
				}
			}

			// 检查是否还有未执行的SQL（不在BEGIN...END块中的）
			if (!insideBeginEnd) {
				String remainingSql = sqlBuilder.toString().trim();
				if (!remainingSql.isEmpty()) {
					try {
						executeSql(remainingSql);
						log("最后一条SQL执行成功");
					} catch (Exception e) {
						logError("最后一条SQL执行失败: " + e.getMessage());
					}
				}
			}
		}
	}

	/**
	 * 解密合格证文件
	 */
	private static void decryptCertificateFiles() {
		try {
			log("开始解密合格证文件...");

			// 构建templates目录路径
			File templatesDir = new File(FILE_HANDLE_PATH + File.separator + "resources" + File.separator + "static" + File.separator + "templates");

			if (!templatesDir.exists()) {
				log("templates目录不存在，跳过解密操作：" + templatesDir.getAbsolutePath());
				return;
			}

			// 使用简单的递归遍历，避免内部类问题
			decryptCertificateFilesInDirectory(templatesDir);

			log("合格证文件解密操作完成");

		} catch (Exception e) {
			logError("解密合格证文件过程中发生错误：" + e.getMessage());
		}
	}

	/**
	 * 在指定目录中递归查找并解密合格证文件
	 */
	private static void decryptCertificateFilesInDirectory(File directory) {
		try {
			File[] files = directory.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isDirectory()) {
						// 递归处理子目录
						decryptCertificateFilesInDirectory(file);
					} else if (file.isFile()) {
						String fileName = file.getName();
						// 检查文件名是否包含"合格证"且为加密文件（.enc后缀）
						if (fileName.contains("合格证") && fileName.toLowerCase().endsWith(".docx.enc")) {
							try {
								// 解密文件
								decryptFile(file.toPath());
								log("已解密合格证文件：" + fileName);
							} catch (Exception e) {
								logError("解密合格证文件失败：" + fileName + "，错误：" + e.getMessage());
							}
						}
					}
				}
			}
		} catch (Exception e) {
			logError("遍历目录时发生错误：" + directory.getAbsolutePath() + "，错误：" + e.getMessage());
		}
	}

	/**
	 * 解密文件
	 * @param encryptedFilePath 加密文件路径
	 */
	private static void decryptFile(Path encryptedFilePath) throws Exception {
		log("开始解密文件：" + encryptedFilePath.getFileName());

		// 读取加密文件内容
		byte[] encryptedContent = Files.readAllBytes(encryptedFilePath);

		// 创建AES密钥
		SecretKeySpec secretKey = new SecretKeySpec(DECRYPT_KEY.getBytes(StandardCharsets.UTF_8), DECRYPT_ALGORITHM);

		// 创建解密器
		Cipher cipher = Cipher.getInstance(DECRYPT_ALGORITHM);
		cipher.init(Cipher.DECRYPT_MODE, secretKey);

		// 解密文件内容
		byte[] decryptedContent = cipher.doFinal(encryptedContent);

		// 生成解密后的文件名（移除.enc后缀）
		String encryptedFileName = encryptedFilePath.getFileName().toString();
		String originalFileName = encryptedFileName.substring(0, encryptedFileName.length() - 4); // 移除.enc
		Path originalFilePath = encryptedFilePath.getParent().resolve(originalFileName);

		// 写入解密后的内容
		Files.write(originalFilePath, decryptedContent);

		// 删除加密文件
		Files.delete(encryptedFilePath);

		log("文件解密完成：" + encryptedFileName + " -> " + originalFileName);
	}

	/**
	 * 解密工作空间中的合格证文件
	 */
	private static void decryptWorkspaceCertificateFiles() {
		try {
			log("开始解密工作空间中的合格证文件...");

			// 构建工作空间templates目录路径
			File templatesDir = new File(WORKSPACE_FILEHANDLE_PATH + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "static" + File.separator + "templates");

			if (!templatesDir.exists()) {
				log("工作空间templates目录不存在，跳过解密操作：" + templatesDir.getAbsolutePath());
				return;
			}

			// 使用简单的递归遍历，避免内部类问题
			decryptCertificateFilesInDirectory(templatesDir);

			log("工作空间合格证文件解密操作完成");

		} catch (Exception e) {
			logError("解密工作空间合格证文件过程中发生错误：" + e.getMessage());
		}
	}

	/**
	 * 工作空间合格证文件访问器
	 */
	private static class WorkspaceCertificateFileVisitor extends SimpleFileVisitor<Path> {
		@Override
		public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
			String fileName = file.getFileName().toString();
			// 检查文件名是否包含"合格证"且为加密文件（.enc后缀）
			if (fileName.contains("合格证") && fileName.toLowerCase().endsWith(".docx.enc")) {
				try {
					// 解密文件
					decryptFile(file);
					log("已解密工作空间合格证文件：" + fileName);
				} catch (Exception e) {
					logError("解密工作空间合格证文件失败：" + fileName + "，错误：" + e.getMessage());
				}
			}
			return FileVisitResult.CONTINUE;
		}
	}

}