//产品结构树的对象
var BomTree = function (isCheck, isDrag) {
	var othis = this;
	this.ztreeObj = {};
	this.treeId = 'bomTree';
	this.treeSetting = bomTreeUtil.treeSetting;
	if (isCheck) {
		this.treeSetting.check = {
			enable: true,
			chkboxType: {
				"Y": "",
				"N": ""
			}
		};
	}
	if (isDrag) {
		this.treeSetting.edit = {
			enable: true,
			editNameSelectAll: false,
			showRemoveBtn: false,
			showRenameBtn: false,
			drag: {
				autoExpandTrigger: true,
				prev: dropPrev,
				inner: dropInner,
				next: dropNext
			}
		}
	}
	this.treeSetting.callback = {
		beforeDrag: function (treeId, treeNodes) {
			for (var i = 0, l = treeNodes.length; i < l; i++) {
				if (treeNodes[i].drag === false) {
					curDragNodes = null;
					return false;
				} else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
					curDragNodes = null;
					return false;
				}
			}
			curDragNodes = treeNodes;
			return true;
		},
		beforeDrop: function (treeId, treeNodes, targetNode, moveType, isCopy) {
			return true;
		},
		beforeDragOpen: function (treeId, treeNode) {
			autoExpandNode = treeNode;
			return true;
		},
		onDrag: function (event, treeId, treeNodes) { },
		onDrop: function (event, treeId, treeNodes, targetNode, moveType, isCopy) {
			if (targetNode != null) {
				var sourceNodeSort = treeNodes[0].SORT;
				var sourceNodeId = treeNodes[0].ID;
				var sourceNodeName = treeNodes[0].NAME;
				var sourceNodeType = treeNodes[0].NODE_TYPE;

				var targetNodeSort = targetNode.SORT;
				var targetNodeId = targetNode.ID;
				var targetNodeName = targetNode.NAME;
				var type = "上面";
				if (sourceNodeSort < targetNodeSort) {
					type = '下面';
				}
				var parentNode = treeNodes[0].getParentNode();
				var allNode = parentNode.children;
				var arr = [];
				for (var i = 1; i <= allNode.length; i++) {
					arr.push(allNode[i - 1].ID + ":" + i);
				}
				var str = arr.join(",");
				twxAjax('Thing.Fn.BOM', 'UpdateTreeNodeSort', {
					str: str
				}, true, function (data) {
					logRecord('移动节点', '策划构建-产品结构树-(ID：' + sourceNodeId + '、名称：' + sourceNodeName + ') 移动到节点(ID：' + targetNodeId + '、名称：' +
						targetNodeName + ')' + type, 1);
					othis.reloadTree(parentNode.ID, sourceNodeId);

				}, function (data) {
					logRecord('移动节点', '策划构建-产品结构树-(ID：' + sourceNodeId + '、名称：' + sourceNodeName + ') 移动到节点(ID：' + targetNodeId + '、名称：' +
						targetNodeName + ')' + type, 0);
					othis.reloadTree(parentNode.ID, sourceNodeId);
				});
			}
		},
		onClick: function (event, treeId, treeNode) {

		},
		onExpand: function (event, treeId, treeNode) {
			othis.loadTreeMenu();
		}
	};
	this.dealDataIcons = bomTreeUtil.dealDataIcons;
	this.dealDataCheck = bomTreeUtil.dealDataCheck;
	//加载树结构
	this.loadTree = function () {
		var cb_success = function (res) {
			if (res.success) {
				var datas = res.data;
				if (datas.length > 0) {
					datas = othis.dealDataIcons(datas);
					datas = othis.dealDataCheck(datas);
					othis.ztreeObj = $.fn.zTree.init($("#" + othis.treeId), othis.treeSetting, datas);
					var nodes = othis.ztreeObj.getNodes();
					for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
						othis.ztreeObj.expandNode(nodes[i], true, false, true);
					}
					othis.loadTreeMenu();
				}
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		//使用ajax进行异步加载Tree
		twxAjax('Thing.Fn.BOM', 'QueryProductTreeRoot', {
			username: sessionStorage.getItem('username')
		}, true, cb_success);
	};
	//加载数据标签的下拉框
	this.loadLabel = function () {
		var cb_success = function (data) {
			var ds = data.rows;
			$("#node_label").empty();
			$("#node_label").append('<option value="">请选择标签</option>');
			for (var i = 0; i < ds.length; i++) {
				var d = ds[i];
				$("#node_label").append('<option value="' + d.NAME + '">' + d.NAME + "</option>");
			}
		};
		twxAjax('Thing.Fn.BOM', 'QueryLabel', {}, false, cb_success);
	};
	//加载树节点右键菜单
	this.loadTreeMenu = function () {
		$("#" + othis.treeId + " a").each(function (i, n) {
			var menu = [];
			var node = othis.ztreeObj.getNodeByTId($(n).parent().attr("id"));

			menu = othis.getNodeMenu(node);
			if (menu.length != 0) {
				$(n).contextMenu({
					width: 115,
					menu: menu,
					target: function (ele) {
						var node = othis.ztreeObj.getNodeByTId($(ele).parent().attr("id"));
						othis.ztreeObj.selectNode(node, false, true);
					}
				});
			}
		});
	};
	//操作完节点之后重新加载节点
	this.reloadTree = function (refrushId, selId) {
		if (selId) {

		} else {
			selId = refrushId;
		}
		var refrushTreeNode = othis.ztreeObj.getNodeByParam("ID", refrushId, null);
		if (!refrushTreeNode.ISPARENT) {
			refrushTreeNode.ISPARENT = true;
			othis.ztreeObj.updateNode(refrushTreeNode);
		}
		othis.ztreeObj.reAsyncChildNodes(refrushTreeNode, 'refresh', false,
			function () {
				othis.ztreeObj.expandNode(refrushTreeNode, true, false, true);
				var newSelNode = othis.ztreeObj.getNodeByParam("ID", selId, null);
				othis.ztreeObj.selectNode(newSelNode, false, true);
				othis.loadTreeMenu();
			});
	}
	//校验同一节点下子节点是否重复
	this.checkNodeNameIsRepeat = function (parentId, nodeName, oldNodeName) {
		var flag = false;
		var cb_success = function (res) {
			if (res.success) {
				var ds = res.data;
				for (var i = 0; i < ds.length; i++) {
					var d = ds[i];
					if (nodeName == d.NAME) {
						flag = true;
						break;
					}
				}
				if (nodeName == oldNodeName) {
					flag = false;
				}
			} else {
				return "校验请求失败";
			}
		};
		twxAjax('Thing.Fn.BOM', 'QueryProductTreeById', {
			ID: parentId
		}, false, cb_success); //同步请求校验
		return flag;
	};
	//获取节点右键菜单数组
	this.getNodeMenu = function (treeNode) {
		var imgSuffix = '../dataTree/';
		var menu = [];
		var addNodeMenu = {
			text: "新增节点",
			icon: imgSuffix + 'images/add.png',
			callback: function () {
				var parentId = treeNode.ID;
				var parentName = treeNode.NAME;
				var parentLevel = treeNode.NODE_LEVEL;
				layer.open({
					title: "添加节点",
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					// fixed: false,
					maxmin: false,
					resize: false, //不允许拉伸
					area: ["700px", "270px"],
					content: '<div id="addBomNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["保存", "重置", "取消"],
					yes: function () {
						$("#addBomNodeSubmit").click();
					},
					btn2: function () {
						$("#addBomNodeReset").click();
						return false;
					},
					btn3: function () {
						return true;
					},
					success: function () {
						var addTpl = $("#addBomNodeHtml")[0].innerHTML;
						$("#addBomNodeContent").append(addTpl);
					}
				});
				layui.form.render(null, "addBomNodeForm");
				layui.form.verify({
					checkNodeNameIsRepeat: function (value, item) {
						var flag = othis.checkNodeNameIsRepeat(parentId, value)
						if (flag) {
							return '该节点已存在！';
						}
					}
				});

				//监听提交
				layui.form.on("submit(addBomNodeSubmit)", function (data) {
					//添加成功的弹窗
					var cb_success = function (json) {
						//新增完成后需要刷新界面
						//提示完成后，点击确定再刷新界面
						layer.closeAll();
						if (json.success) {
							layer.msg("添加成功");
							othis.reloadTree(treeNode.ID);
						} else {
							layer.alert("添加失败，原因：" + json.msg, {
								icon: 2,
							});
						}
					};
					//添加失败的弹窗
					var cb_error = function (xhr) {
						layer.alert("新增失败!", {
							icon: 2,
						});
					};
					var node_label = $("#node_label").val() == null ? '' : $("#node_label").val().join(",");
					//同步新增
					twxAjax("Thing.Fn.BOM", "ManualAddProductNode", {
						pid: parentId,
						name: data.field.node_name,
						code: data.field.node_code,
						batchNumber: data.field.node_batch,
						user: sessionStorage.getItem('username'),
						level: Number(parentLevel) + 1
					}, true, cb_success, cb_error);
					return false;
				});
			}
		};


		var editNodeMenu = {
			text: "编辑节点",
			icon: imgSuffix + 'images/edit.png',
			callback: function () {
				var treeId = treeNode.ID;
				var type = treeNode.NODE_TYPE;
				var nodeName = treeNode.NAME;
				var code = treeNode.CODE;
				var batch = treeNode.BATCH_NUMBER;
				if (type != 'root' && type != 'product' && type != 'folder') {
					nodeName = getNodeName(nodeName);
				}
				var nodeLevel = treeNode.NODE_LEVEL;
				var parentId = treeNode.PID;
				if (type == 'product') {
					parentId = 0;
				}
				layer.open({
					title: "编辑节点",
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					// fixed: false,
					maxmin: false,
					resize: false, //不允许拉伸
					area: ["700px", "270px"],
					content: '<div id="editBomNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
					btn: ["保存", "重置", "取消"],
					yes: function () {
						$("#addBomNodeSubmit").click();
					},
					btn2: function () {
						$("#addBomNodeReset").click();
						return false;
					},
					btn3: function () {
						return true;
					},
					success: function () {
						var addTpl = $("#addBomNodeHtml")[0].innerHTML;
						$("#editBomNodeContent").append(addTpl);
					}
				});
				layui.form.render(null, "addBomNodeForm");
				$("#node_name").val(nodeName);
				$("#node_code").val(code);
				$("#node_batch").val(batch);
				layui.form.verify({
					checkNodeNameIsRepeat: function (value, item) {
						var flag = othis.checkNodeNameIsRepeat(parentId, value, nodeName)
						if (flag) {
							return '该节点已存在！';
						}
					}
				});

				//监听提交
				layui.form.on("submit(addBomNodeSubmit)", function (data) {
					var cb_success = function (res) {
						layer.closeAll();
						if (res.success) {
							layer.msg(res.msg);
							if (String(treeNode.PID).indexOf('P') > -1) {
								othis.loadTree();
							} else {
								othis.reloadTree(treeNode.PID, treeNode.ID);
							}

						} else {
							layer.alert(res.msg, {
								icon: 2,
							});
						}
					};
					var cb_error = function (xhr) {
						layer.alert("更新请求失败!", {
							icon: 2,
						});
					};
					twxAjax("Thing.Fn.BOM", "UpdateProductNode", {
						tree_id: treeId,
						tree_name: data.field.node_name,
						code: data.field.node_code,
						batch: data.field.node_batch
					}, true, cb_success, cb_error);
					return false;
				});
			}
		};
		var deleteNodeMenu = {
			text: "删除节点",
			icon: imgSuffix + 'images/remove.png',
			callback: function () {
				layui.use(['layer'], function () {
					var layer = layui.layer;

					var msg = "确认删除 节点 -- " + treeNode.NAME + " 吗？";
					if (treeNode.ISPARENT) {
						msg = "该节点下有子节点,确认删除吗?"
					}
					layer.confirm(msg, {
						icon: 3,
						title: '提示'
					}, function (index) {
						var cb_success = function (data) {
							if (data.success) {
								layer.msg(data.msg);
								othis.reloadTree(treeNode.PID);
								logRecord('删除', '策划构建-产品结构树-删除节点(ID：' + treeNode.ID + '、名称：' + treeNode.NAME + ')', 1);
							} else {
								layer.alert(data.msg, {
									icon: 2
								});
							}

						};
						var cb_error = function () {
							logRecord('删除', '策划构建-产品结构树-删除节点(ID：' + treeNode.ID + '、名称：' + treeNode.NAME + ')', 0);
							layer.msg('删除失败！');
						}
						twxAjax('Thing.Fn.BOM', 'DeleteProductNode', {
							id: treeNode.ID
						}, true, cb_success, cb_error);

					});
				});
			}
		};
		var copyNodeMenu = {
			text: "复制节点",
			icon: imgSuffix + 'images/copy.png',
			callback: function () {

			}
		};

		var importMenu = {
			text: "批量导入",
			icon: imgSuffix + 'images/import-data.png',
			callback: function () {
				layui.use(['layer', 'form', 'upload'], function () {
					var layer = layui.layer;
					var form = layui.form;
					var upload = layui.upload;
					var fileFlag = false;
					layer.open({
						title: "批量导入",
						type: 1,
						anim: false,
						openDuration: 200,
						isOutAnim: false,
						closeDuration: 200,
						shadeClose: false,
						// fixed: false,
						maxmin: false,
						resize: false, //不允许拉伸
						area: ['350px', '270px'],
						content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
						btn: ['确认', '取消'],
						yes: function () {
							if (!fileFlag) {
								layer.alert('请选择需要导入的数据文件!', {
									icon: 2
								});
								return false;
							}
							$('#uploadStart').click();
						},
						btn2: function () {
							layer.closeAll();
						},
						success: function () {
							var addTpl = $("#uploadHtml")[0].innerHTML;
							$("#uploadContent").append(addTpl);
							$("#downloadTpl").click(function () {
								var url = fileHandlerUrl + "/bom/export/tpl";
								$.fileDownload(url, {
									httpMethod: 'POST',
									prepareCallback: function (url) { },
									abortCallback: function (url) {
										layer.alert("下载失败！", {
											icon: 2
										});
									},
									successCallback: function (url) { },
									failCallback: function (html, url) {
										layer.alert("下载失败！", {
											icon: 2
										});
									}
								});
							});
						}
					});
					form.render(null, 'uploadForm');

					var uploadInst = upload.render({
						elem: '#uploadChoice',
						url: fileHandlerUrl + '/bom/import?pid=' + treeNode.ID + '&user=' + sessionStorage.getItem('username'),
						auto: false,
						accept: 'file',
						exts: 'xlsx',
						field: 'uploadFile',
						bindAction: '#uploadStart',
						dataType: "json",
						choose: function (obj) {
							fileFlag = true;
							var o = obj.pushFile();
							var filename = '';
							for (var k in o) {
								var file = o[k];
								filename = file.name;
							}
							$("#selectedFile").show();
							$("#selectedFileName").text(filename);
						},
						before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
							layer.load(); //上传loading
						},
						done: function (res, index, upload) {
							layer.closeAll();
							if (res.success) {
								layer.msg(res.msg);
								othis.reloadTree(treeNode.ID);
							} else {
								layer.alert(res.msg);
							}
						}
					});
				});
			}
		};

		if (treeNode.NODE_TYPE != 'root' && treeNode.NODE_TYPE != 'folder') {
			if (treeNode.NODE_TYPE != 'level5') {
				menu.push(addNodeMenu);
				menu.push(importMenu);
			}
			menu.push(editNodeMenu);
			if (treeNode.NODE_TYPE != 'product') {
				menu.push(deleteNodeMenu);
			}

		}
		return menu;
	}
};