/**
 * components/costing/flow/flow.js - 成本流程管理
 *
 * 版本升级记录：
 * - 2025-07-11: 升级适配Handsontable 16.0.1版本
 *   * 增强错误处理机制，提高稳定性
 *   * 优化Handsontable配置，利用16.0.1性能改进
 *   * 改进上下文菜单定位
 *   * 增加参数验证和异常捕获
 *   * 保持ES5语法兼容性
 *
 * <AUTHOR>
 * @version 16.0.1-compatible
 */
var THING = "Thing.Fn.Costing";
var currentUser = sessionStorage.getItem("username");
var $, form, layer, util, table, laydate, upload;
layui.use(['form', 'util', 'table', 'laydate'], function() {
	$ = layui.$;
	form = layui.form;
	layer = layui.layer;
	util = layui.util;
	table = layui.table;
	laydate = layui.laydate;
	upload = layui.upload;
	renderFlow();
	initFlowTableTool();
	renderFlowSearchForm();
});


/**
 * 加载格搜索表单
 */
function renderFlowSearchForm() {
	form.render(null, 'flow-table-form');
	laydate.render({
		elem: '#flow-range-date',
		range: ['#flow-start-date', '#flow-end-date'],
		rangeLinked: true // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
	});

	// 搜索提交
	form.on('submit(flow-table-search)', function(data) {
		var field = data.field; // 获得表单字段
		// 执行搜索重载
		table.reload('flow-table', {
			page: {
				curr: 1 // 重新从第 1 页开始
			},
			where: field // 搜索的字段
		});
		return false;
	});
}

/**
 * 加载流程定义列表
 */
function renderFlow() {
	// 创建渲染实例
	table.render({
		elem: '#flow-table',
		id: 'flow-table',
		url: getUrl(THING, 'QueryFlowDef'),
		toolbar: '#flow-toolbar',
		defaultToolbar: ['filter', {
			title: '刷新',
			layEvent: 'flow_reload',
			icon: 'layui-icon-refresh'
		}],
		height: 'full-50', // 最大高度减去其他容器已占有的高度差
		cellMinWidth: 80,
		page: true,
		limit: 15,
		limits: [15, 30, 45, 60, 75, 90, 100],
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'DEPLOYMENTID',
					width: 100,
					title: '流程编号',
					align: 'center'
				},
				{
					field: 'NAME',
					title: '流程名称',
					width: 300
				},
				{
					field: 'FLOWKEY',
					title: '流程标识',
					width: 150
				},
				{
					field: 'FORMNAME',
					title: '表单名称',
					width: 220,
					templet: function(d) {
						if (d.FORMNAME) {
							return '<span lay-event="view-form" style="text-decoration: underline;color: #017dd9;cursor:pointer;">' + d.FORMNAME + '</span>';
						} else {
							return '<span style="color: red;">未关联表单</span>';
						}

					}
				},
				{
					field: 'VERSION',
					width: 100,
					title: '流程版本',
					templet: formatVersion,
					align: 'center'
				},
				{
					field: 'SUSPENSIONSTATE',
					title: '状态',
					width: 130,
					templet: function(d) {
						var html = "";
						if (d.SUSPENSIONSTATE == 1) {
							html = '<span class="layui-badge layui-bg-green">激活</span>';
						} else {
							html = '<span class="layui-badge">挂起</span>';
						}
						return html;
					},
					align: 'center'
				},
				{
					field: 'DEPLOYMENTTIME',
					width: 160,
					title: '部署时间',
					templet: formatTableDate,
					align: 'center'
				},
				{
					fixed: 'right',
					title: '操作',
					width: 220,
					minWidth: 220,
					toolbar: '#flow-rowbar',
					align: 'center'
				}
			]
		],
		done: function() {

		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});
}

/**
 * 查看表单
 */
function viewForm(myForm) {
	var fixedRowsTop = myForm.headerRow || 0;
	var initData = myForm.tableData;
	var merged = myForm.merged;
	var metas = myForm.meta;
	var colWidths = myForm.colWidths || [];
	var rowHeights = myForm.rowHeights || [];

	function myCellRenderer(instance, td, row, col, prop, value, cellProperties) {
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		const stringifiedValue = Handsontable.helper.stringify(value);
	}

	// 16.0.1版本兼容性：增强容器检查
	var container = document.getElementById('handsontable');
	if (!container) {
		console.error('Handsontable容器未找到');
		return;
	}

	// 16.0.1版本优化：增强错误处理的Handsontable实例化
	try {
		window.hot = new Handsontable(container, {
		data: initData,
		fixedRowsTop: fixedRowsTop,
		mergeCells: merged,
		rowHeaders: false,
		colHeaders: false,
		rowHeights: rowHeights,
		dropdownMenu: false,
		customBorders: true,
		comments: true,
		colWidths: colWidths,
		fillHandle: true,
		renderer: myCellRenderer,
		language: 'zh-CN',
		licenseKey: 'non-commercial-and-evaluation',
		className: 'htMiddle htCenter',
		manualColumnResize: true,
		manualRowResize: true,
		// 16.0.1版本性能优化配置
		preventOverflow: 'horizontal',
		preventWheel: true
	});

	// 16.0.1版本上下文菜单定位优化
	if (window.hot && window.hot.addHook) {
		window.hot.addHook('afterContextMenuShow', function() {
			// 调用HandsonTableUtil的菜单定位优化函数
			if (typeof HotUtil !== 'undefined' && HotUtil.adjustContextMenuPosition) {
				HotUtil.adjustContextMenuPosition();
			}
		});
	}

	} catch (error) {
		console.error('Handsontable初始化失败:', error);
		layer.alert('表格初始化失败: ' + error.message, {
			icon: 2,
			title: '初始化错误'
		});
		return;
	}

	// 16.0.1版本优化：增强错误处理的setCellMeta操作
	try {
		for (var m = 0; m < metas.length; m++) {
		var meta = metas[m];
		if (meta) {
			var isReadOnly = false;
			if (meta.row < fixedRowsTop) {
				isReadOnly = true;
				meta.readOnly = isReadOnly;
			}
		}
	}


	for (var m = 0; m < metas.length; m++) {
		var meta = metas[m];
		if (meta && typeof meta.row === 'number' && typeof meta.col === 'number') {
			try {
				meta.className = 'htMiddle htCenter';
				//将表头添加背景颜色
				if (meta.row < fixedRowsTop) {
					meta.className = meta.className + " td-bg";
				}

				// 安全地设置单元格元数据
				if (meta.className) {
					window.hot.setCellMeta(meta.row, meta.col, 'className', meta.className);
				}
				if (meta.readOnly) {
					window.hot.setCellMeta(meta.row, meta.col, 'readOnly', meta.readOnly);
				}
				if (meta.eles) {
					window.hot.setCellMeta(meta.row, meta.col, 'eles', meta.eles);
				}
				if (meta.comment) {
					window.hot.setCellMeta(meta.row, meta.col, 'comment', meta.comment);
				}
				if (meta.type) {
					window.hot.setCellMeta(meta.row, meta.col, 'type', meta.type);
				}
				if (meta.myType) {
					window.hot.setCellMeta(meta.row, meta.col, 'myType', meta.myType);
				}
				if (meta.dateFormat) {
					window.hot.setCellMeta(meta.row, meta.col, 'dateFormat', meta.dateFormat);
				}
				if (meta.source) {
					window.hot.setCellMeta(meta.row, meta.col, 'source', meta.source);
				}
			} catch (metaError) {
				console.warn('设置单元格(' + meta.row + ',' + meta.col + ')元数据时出错:', metaError);
			}
		}
	}

	// 16.0.1版本优化：增强错误处理的渲染操作
	try {
		window.hot.render();
	} catch (renderError) {
		console.error('表格渲染失败:', renderError);
		layer.alert('表格渲染失败，请刷新页面重试', {
			icon: 2,
			title: '渲染错误'
		});
	}

	} catch (error) {
		console.error('设置单元格元数据时发生异常:', error);
		layer.alert('表格配置失败: ' + error.message, {
			icon: 2,
			title: '配置错误'
		});
	}
}


/**
 * 加载表格工具事件
 */
function initFlowTableTool() {
	// 触发单元格工具事件
	table.on('tool(flow-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'view') {
			viewFlowChart(data.DEPLOYMENTID);
		} else if (obj.event === 'view-form') {
			alertForm(data.TABLEDATA);
		} else if (obj.event === 'form') {
			relationForm(data.DEPLOYMENTID, data.FORMID);
		} else if (obj.event === 'suspendOrActivate') {
			twxAjax(THING, 'postFlw', {
				servlet: '/flow',
				params: {
					act: 'updateState',
					state: data.SUSPENSIONSTATE == 1 ? 2 : 1,
					deployId: data.DEPLOYMENTID
				},
			}, true, function(res) {
				if (res.success) {
					reloadFlowTable();
					layer.msg(res.msg)
				} else {
					layer.alert(res.msg);
				}
			}, function(xhr, textStatus, errorThrown) {
				layer.alert('请求出错！', {
					icon: 2
				});
			});
		} else if (obj.event === 'delete') {
			layer.confirm('确定要删除该流程吗？', {
				icon: 3
			}, function(lIndex) {
				twxAjax(THING, 'postFlw', {
					servlet: '/flow',
					params: {
						act: 'delete',
						deployId: data.DEPLOYMENTID
					},
				}, true, function(res) {
					layer.close(lIndex);
					if (res.success) {
						layer.msg(res.msg)
						reloadFlowTable();
					} else {
						layer.alert(res.msg);
					}
				}, function(xhr, textStatus, errorThrown) {
					layer.alert('请求出错！', {
						icon: 2
					});
				});
			}, function(lIndex) {
				layer.close(lIndex);
			});
		}
	});

	// 工具栏事件
	table.on('toolbar(flow-table)', function(obj) {
		var id = obj.config.id;
		var checkStatus = table.checkStatus(id);
		var othis = lay(this);
		switch (obj.event) {
			case 'add-btn':
				addFlowXml();
				break;
			case 'delete-btn':
				break;
			case 'flow_reload':
				reloadFlowTable();
				break;
		};
	});
}

/**
 * 关联表单
 */
function relationForm(deployId, formId) {
	layer.open({
		title: "关联表单",
		type: 1,
		area: ['1000px', "600px"],
		content: '<div id="formContent" style=""><table class="layui-hide" id="form-table" lay-filter="form-table"></table></div>',
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		resize: false,
		btn: ['确定', '取消'],
		yes: function(index, layero, that) {
			var datas = table.checkStatus('form-table').data;
			if (datas.length == 0) {
				layer.alert('请选择一个表单进行关联！', {
					icon: 2
				});
			} else {
				twxAjax(THING, 'RelateForm', {
					deployId: deployId,
					formId: datas[0]['FORM_ID']
				}, true, function(res) {
					if (res.success) {
						layer.close(index);
						reloadFlowTable();
						layer.msg(res.msg)
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}, function(xhr, textStatus, errorThrown) {
					layer.alert('请求出错！', {
						icon: 2
					});
				});
			}
		},
		btn2: function() {
			return true;
		},
		success: function() {
			table.render({
				elem: '#form-table',
				id: 'form-table',
				url: getUrl(THING, 'QueryForm'),
				defaultToolbar: [],
				height: 490,
				cellMinWidth: 80,
				page: true,
				limit: 15,
				limits: [15, 30, 45, 60, 75, 90, 100],
				cols: [
					[{
							type: 'radio',
							fixed: 'left'
						},
						{
							title: '序号',
							type: "numbers",
							width: 60
						},
						{
							field: 'FORM_ID',
							width: 100,
							title: '表单主键',
							align: 'center'
						},
						{
							field: 'FORM_NAME',
							title: '表单名称',
							width: 300
						},
						{
							field: 'CREATE_TIME',
							width: 160,
							title: '创建时间',
							templet: formatTableDate,
							align: 'center'
						},
						{
							fixed: 'right',
							title: '操作',
							width: 180,
							minWidth: 180,
							toolbar: `<div class="layui-clear-space">
										<a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
									  </div>`,
							align: 'center'
						}
					]
				],
				done: function(res, curr, count, origin) {
					if (formId) {
						var idIndex = 0;
						for (var i = 0; i < res.data.length; i++) {
							if (formId == res.data[i]['FORM_ID']) {
								idIndex = i;
								break;
							}
						}
						table.setRowChecked('form-table', {
							type: "radio",
							index: idIndex
						});
					}

					table.on('tool(form-table)', function(obj) {
						var data = obj.data; // 获得当前行数据
						if (obj.event === 'view') {
							alertForm(data.TABLE_DATA);
						}
					});
				},
				error: function(res, msg) {
					console.log(res, msg)
				}
			});
		}
	});
}

function alertForm(tableData) {
	layer.open({
		title: '查看表单',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: [$(window).width() + 'px', $(window).height() + 'px'],
		content: '<div id="handsontable" style="width:100%;height:100%;"></div>',
		success: function(layero, index) {
			viewForm(JSON.parse(tableData));
		}
	});
}

/**
 * 新增导入一个流程的xml文件
 */
function addFlowXml() {
	var flowPostUrl = getFlowPostUrl();
	var fileFlag = false;
	layer.open({
		title: "导入流程XML",
		type: 1,
		area: ['460px', "330px"],
		content: '<div id="importXmlContent" style="padding: 15px 0px 0px 0px;"></div>',
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		resize: false,
		btn: ['确定', '取消'],
		yes: function() {
			if (!fileFlag) {
				layer.alert('请选择需要导入的xml文件!', {
					icon: 2
				});
				return false;
			}
			var processName = $("#processName").val();
			uploadInst.config.url = flowPostUrl + '/flow?act=deploy&name=' + processName;
			$('#uploadStart').click();
		},
		btn2: function() {
			return true;
		},
		success: function() {
			var tpl = '<form class="layui-form" lay-filter="importXmlForm">\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">流程名称:</label>\
										<div class="layui-input-block">\
											<input type="text" name="name" id="processName" lay-verify="required" autocomplete="off" placeholder="请输入流程名称" style="width:330px" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">文件内容:</label>\
										<div class="layui-input-block">\
											<div class="layui-upload">\
												<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>\
												<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
											</div>\
										</div>\
									</div>\
									<div class="layui-form-item" id="selectedFile" style="display: none;">\
										<label class="fieldlabel layui-form-label">已选文件:</label>\
										<div class="layui-input-block">\
											<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
										</div>\
									</div>\
									<div class="layui-form-item" style="display:none;">\
										<center>\
											<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
											<button id="btn_cancel" class="layui-btn">取消</button>\
										</center>\
									</div>\
								</form>'
			$("#importXmlContent").append(tpl);
			form.render(null, 'importXmlForm');
			uploadInst = upload.render({
				elem: '#uploadChoice',
				url: flowPostUrl + '/flow?act=deploy',
				auto: false,
				exts: 'bpmn20.xml',
				field: 'xml',
				bindAction: '#uploadStart',
				dataType: "json",
				choose: function(obj) {
					fileFlag = true;
					var files = obj.pushFile();
					obj.preview(function(index, file, result) {
						var filename = file.name;
						$("#selectedFile").show();
						$("#selectedFileName").text(filename);
					});
				},
				before: function(obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
					layer.load(); //上传loading
				},
				done: function(res, index, upload) {
					if (res.success) {
						layer.closeAll();
						layer.msg(res.msg);
						reloadFlowTable();
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}
			});
		}
	});
}

/**
 * 重新加载
 */
function reloadFlowTable() {
	table.reloadData('flow-table');
}

var bpmViewer = null;

function viewFlowChart(deployId) {
	layer.open({
		title: "查看流程图",
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: true,
		resize: false, //不允许拉伸
		area: ['1040px', '750px'],
		scrollbar: false,
		content: '<div class="form-content" id="chart-content"></div>',
		success: function() {
			var html = '<div class="canvas"  id="js-canvas">\
						</div>';
			$("#chart-content").append(html);

			if (bpmViewer) {
				bpmViewer.destroy();
			}

			bpmViewer = new BpmnJS({
				container: document.querySelector('#js-canvas'),
				height: 650
			});

			/**
			 * 屏幕自适应
			 */
			function fitViewport() {
				bpmViewer.get('canvas').zoom("fit-viewport", "auto");
			}


			twxAjax(THING, 'postFlw', {
				servlet: '/flow',
				params: {
					act: 'flowXml',
					deployId: deployId
				},
			}, true, function(res) {
				if (res.success) {
					var xmlData = res.data.xmlData;
					bpmViewer.importXML(xmlData).then(function() {
						fitViewport();
					}).catch(function(err) {
						console.log(err);
					});

				} else {
					layer.alert(res.msg);
				}
			}, function(xhr, textStatus, errorThrown) {
				layer.alert('请求出错！', {
					icon: 2
				});
			});
		}
	});
}