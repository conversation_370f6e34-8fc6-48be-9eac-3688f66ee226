<!--docxjs library predefined styles--><style>
.docx-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.docx-wrapper>section.docx { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }
.docx { color: black; hyphens: auto; text-underline-position: from-font; }
section.docx { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.docx>article { margin-bottom: auto; z-index: 1; }
section.docx>footer { z-index: 1; }
.docx table { border-collapse: collapse; }
.docx table td, .docx table th { vertical-align: top; }
.docx p { margin: 0pt; min-height: 1em; }
.docx span { white-space: pre-wrap; overflow-wrap: break-word; }
.docx a { color: inherit; text-decoration: inherit; }
</style><!--docxjs document styles--><style>.docx span {
  font-family: Liberation Serif;
}
.docx p {
}
p.docx_normal {
  text-align: left;
}
p.docx_normal span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
span.docx_style14 {
}
span.docx_style15 {
  font-family: OpenSymbol;
}
span.docx_listlabel1 {
}
span.docx_listlabel2 {
}
span.docx_listlabel3 {
}
span.docx_listlabel4 {
}
span.docx_listlabel5 {
}
span.docx_listlabel6 {
}
span.docx_listlabel7 {
}
span.docx_listlabel8 {
}
span.docx_listlabel9 {
}
span.docx_listlabel10 {
}
span.docx_listlabel11 {
}
span.docx_listlabel12 {
}
span.docx_listlabel13 {
}
span.docx_listlabel14 {
}
span.docx_listlabel15 {
}
span.docx_listlabel16 {
}
span.docx_listlabel17 {
}
span.docx_listlabel18 {
}
span.docx_listlabel19 {
}
span.docx_listlabel20 {
}
span.docx_listlabel21 {
}
span.docx_listlabel22 {
}
span.docx_listlabel23 {
}
span.docx_listlabel24 {
}
span.docx_listlabel25 {
}
span.docx_listlabel26 {
}
span.docx_listlabel27 {
}
span.docx_listlabel28 {
}
span.docx_listlabel29 {
}
span.docx_listlabel30 {
}
span.docx_listlabel31 {
}
span.docx_listlabel32 {
}
span.docx_listlabel33 {
}
span.docx_listlabel34 {
}
span.docx_listlabel35 {
}
span.docx_listlabel36 {
}
span.docx_listlabel37 {
}
span.docx_listlabel38 {
}
span.docx_listlabel39 {
}
span.docx_listlabel40 {
}
span.docx_listlabel41 {
}
span.docx_listlabel42 {
}
span.docx_listlabel43 {
}
span.docx_listlabel44 {
}
span.docx_listlabel45 {
}
span.docx_listlabel46 {
}
span.docx_listlabel47 {
}
span.docx_listlabel48 {
}
span.docx_listlabel49 {
}
span.docx_listlabel50 {
}
span.docx_listlabel51 {
}
span.docx_listlabel52 {
}
span.docx_listlabel53 {
}
span.docx_listlabel54 {
}
span.docx_listlabel55 {
}
span.docx_listlabel56 {
}
span.docx_listlabel57 {
}
span.docx_listlabel58 {
}
span.docx_listlabel59 {
}
span.docx_listlabel60 {
}
span.docx_listlabel61 {
}
span.docx_listlabel62 {
}
span.docx_listlabel63 {
}
span.docx_listlabel64 {
}
span.docx_listlabel65 {
}
span.docx_listlabel66 {
}
span.docx_listlabel67 {
}
span.docx_listlabel68 {
}
span.docx_listlabel69 {
}
span.docx_listlabel70 {
}
span.docx_listlabel71 {
}
span.docx_listlabel72 {
}
p.docx_style16 {
  margin-top: 12.00pt;
  margin-bottom: 6.00pt;
  text-align: left;
}
p.docx_style16 span {
  font-family: Liberation Sans;
  min-height: 14.00pt;
  font-size: 14.00pt;
  color: black;
}
p.docx_style17 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
  text-align: left;
}
p.docx_style17 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style18 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
  text-align: left;
}
p.docx_style18 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style19 {
  margin-top: 6.00pt;
  margin-bottom: 6.00pt;
  text-align: left;
}
p.docx_style19 span {
  font-style: italic;
  min-height: 12.00pt;
  font-size: 12.00pt;
  font-family: Liberation Serif;
  color: black;
}
p.docx_style20 {
  text-align: left;
}
p.docx_style20 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style21 {
  text-indent: -0.00pt;
  margin-left: 0.00pt;
  text-align: left;
}
p.docx_style21 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
</style><!--docxjs document numbering styles--><style>p.docx-num-1-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-1-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -0.00pt;
  margin-left: 0.00pt;
}
p.docx-num-2-0:before {
  content: "\9";
  counter-increment: docx-num-2-0;
  font-family: Symbol;
}
p.docx-num-2-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-2-0 {
  counter-reset: docx-num-2-1 0;
}
p.docx-num-2-1:before {
  content: "◦\9";
  counter-increment: docx-num-2-1;
  font-family: OpenSymbol;
}
p.docx-num-2-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-2-1 {
  counter-reset: docx-num-2-2 0;
}
p.docx-num-2-2:before {
  content: "▪\9";
  counter-increment: docx-num-2-2;
  font-family: OpenSymbol;
}
p.docx-num-2-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-2-2 {
  counter-reset: docx-num-2-3 0;
}
p.docx-num-2-3:before {
  content: "\9";
  counter-increment: docx-num-2-3;
  font-family: Symbol;
}
p.docx-num-2-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-2-3 {
  counter-reset: docx-num-2-4 0;
}
p.docx-num-2-4:before {
  content: "◦\9";
  counter-increment: docx-num-2-4;
  font-family: OpenSymbol;
}
p.docx-num-2-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-2-4 {
  counter-reset: docx-num-2-5 0;
}
p.docx-num-2-5:before {
  content: "▪\9";
  counter-increment: docx-num-2-5;
  font-family: OpenSymbol;
}
p.docx-num-2-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-2-5 {
  counter-reset: docx-num-2-6 0;
}
p.docx-num-2-6:before {
  content: "\9";
  counter-increment: docx-num-2-6;
  font-family: Symbol;
}
p.docx-num-2-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-2-6 {
  counter-reset: docx-num-2-7 0;
}
p.docx-num-2-7:before {
  content: "◦\9";
  counter-increment: docx-num-2-7;
  font-family: OpenSymbol;
}
p.docx-num-2-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-2-7 {
  counter-reset: docx-num-2-8 0;
}
p.docx-num-2-8:before {
  content: "▪\9";
  counter-increment: docx-num-2-8;
  font-family: OpenSymbol;
}
p.docx-num-2-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-3-0:before {
  content: ""counter(docx-num-3-0, decimal)")\9";
  counter-increment: docx-num-3-0;
}
p.docx-num-3-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-3-0 {
  counter-reset: docx-num-3-1 0;
}
p.docx-num-3-1:before {
  content: ""counter(docx-num-3-1, decimal)".\9";
  counter-increment: docx-num-3-1;
}
p.docx-num-3-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-3-1 {
  counter-reset: docx-num-3-2 0;
}
p.docx-num-3-2:before {
  content: ""counter(docx-num-3-2, decimal)".\9";
  counter-increment: docx-num-3-2;
}
p.docx-num-3-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-3-2 {
  counter-reset: docx-num-3-3 0;
}
p.docx-num-3-3:before {
  content: ""counter(docx-num-3-3, decimal)".\9";
  counter-increment: docx-num-3-3;
}
p.docx-num-3-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-3-3 {
  counter-reset: docx-num-3-4 0;
}
p.docx-num-3-4:before {
  content: ""counter(docx-num-3-4, decimal)".\9";
  counter-increment: docx-num-3-4;
}
p.docx-num-3-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-3-4 {
  counter-reset: docx-num-3-5 0;
}
p.docx-num-3-5:before {
  content: ""counter(docx-num-3-5, decimal)".\9";
  counter-increment: docx-num-3-5;
}
p.docx-num-3-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-3-5 {
  counter-reset: docx-num-3-6 0;
}
p.docx-num-3-6:before {
  content: ""counter(docx-num-3-6, decimal)".\9";
  counter-increment: docx-num-3-6;
}
p.docx-num-3-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-3-6 {
  counter-reset: docx-num-3-7 0;
}
p.docx-num-3-7:before {
  content: ""counter(docx-num-3-7, decimal)".\9";
  counter-increment: docx-num-3-7;
}
p.docx-num-3-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-3-7 {
  counter-reset: docx-num-3-8 0;
}
p.docx-num-3-8:before {
  content: ""counter(docx-num-3-8, decimal)".\9";
  counter-increment: docx-num-3-8;
}
p.docx-num-3-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-4-0:before {
  content: ""counter(docx-num-4-0, lower-roman)".\9";
  counter-increment: docx-num-4-0;
}
p.docx-num-4-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-4-0 {
  counter-reset: docx-num-4-1 0;
}
p.docx-num-4-1:before {
  content: ""counter(docx-num-4-1, lower-roman)".\9";
  counter-increment: docx-num-4-1;
}
p.docx-num-4-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-4-1 {
  counter-reset: docx-num-4-2 0;
}
p.docx-num-4-2:before {
  content: ""counter(docx-num-4-2, lower-roman)".\9";
  counter-increment: docx-num-4-2;
}
p.docx-num-4-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-4-2 {
  counter-reset: docx-num-4-3 0;
}
p.docx-num-4-3:before {
  content: ""counter(docx-num-4-3, lower-roman)".\9";
  counter-increment: docx-num-4-3;
}
p.docx-num-4-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-4-3 {
  counter-reset: docx-num-4-4 0;
}
p.docx-num-4-4:before {
  content: ""counter(docx-num-4-4, lower-roman)".\9";
  counter-increment: docx-num-4-4;
}
p.docx-num-4-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-4-4 {
  counter-reset: docx-num-4-5 0;
}
p.docx-num-4-5:before {
  content: ""counter(docx-num-4-5, lower-roman)".\9";
  counter-increment: docx-num-4-5;
}
p.docx-num-4-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-4-5 {
  counter-reset: docx-num-4-6 0;
}
p.docx-num-4-6:before {
  content: ""counter(docx-num-4-6, lower-roman)".\9";
  counter-increment: docx-num-4-6;
}
p.docx-num-4-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-4-6 {
  counter-reset: docx-num-4-7 0;
}
p.docx-num-4-7:before {
  content: ""counter(docx-num-4-7, lower-roman)".\9";
  counter-increment: docx-num-4-7;
}
p.docx-num-4-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-4-7 {
  counter-reset: docx-num-4-8 0;
}
p.docx-num-4-8:before {
  content: ""counter(docx-num-4-8, lower-roman)".\9";
  counter-increment: docx-num-4-8;
}
p.docx-num-4-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-5-0:before {
  content: "\9";
  counter-increment: docx-num-5-0;
  font-family: Wingdings;
}
p.docx-num-5-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-5-0 {
  counter-reset: docx-num-5-1 0;
}
p.docx-num-5-1:before {
  content: "\9";
  counter-increment: docx-num-5-1;
  font-family: Wingdings;
}
p.docx-num-5-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-5-1 {
  counter-reset: docx-num-5-2 0;
}
p.docx-num-5-2:before {
  content: "\9";
  counter-increment: docx-num-5-2;
  font-family: Wingdings;
}
p.docx-num-5-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-5-2 {
  counter-reset: docx-num-5-3 0;
}
p.docx-num-5-3:before {
  content: "\9";
  counter-increment: docx-num-5-3;
  font-family: Wingdings;
}
p.docx-num-5-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-5-3 {
  counter-reset: docx-num-5-4 0;
}
p.docx-num-5-4:before {
  content: "\9";
  counter-increment: docx-num-5-4;
  font-family: Wingdings;
}
p.docx-num-5-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-5-4 {
  counter-reset: docx-num-5-5 0;
}
p.docx-num-5-5:before {
  content: "\9";
  counter-increment: docx-num-5-5;
  font-family: Wingdings;
}
p.docx-num-5-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-5-5 {
  counter-reset: docx-num-5-6 0;
}
p.docx-num-5-6:before {
  content: "\9";
  counter-increment: docx-num-5-6;
  font-family: Wingdings;
}
p.docx-num-5-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-5-6 {
  counter-reset: docx-num-5-7 0;
}
p.docx-num-5-7:before {
  content: "\9";
  counter-increment: docx-num-5-7;
  font-family: Wingdings;
}
p.docx-num-5-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-5-7 {
  counter-reset: docx-num-5-8 0;
}
p.docx-num-5-8:before {
  content: "\9";
  counter-increment: docx-num-5-8;
  font-family: Wingdings;
}
p.docx-num-5-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-6-0:before {
  content: "\9";
  counter-increment: docx-num-6-0;
  font-family: Wingdings;
}
p.docx-num-6-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-6-0 {
  counter-reset: docx-num-6-1 0;
}
p.docx-num-6-1:before {
  content: "\9";
  counter-increment: docx-num-6-1;
  font-family: Wingdings;
}
p.docx-num-6-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-6-1 {
  counter-reset: docx-num-6-2 0;
}
p.docx-num-6-2:before {
  content: "\9";
  counter-increment: docx-num-6-2;
  font-family: Wingdings;
}
p.docx-num-6-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-6-2 {
  counter-reset: docx-num-6-3 0;
}
p.docx-num-6-3:before {
  content: "\9";
  counter-increment: docx-num-6-3;
  font-family: Wingdings;
}
p.docx-num-6-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-6-3 {
  counter-reset: docx-num-6-4 0;
}
p.docx-num-6-4:before {
  content: "\9";
  counter-increment: docx-num-6-4;
  font-family: Wingdings;
}
p.docx-num-6-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-6-4 {
  counter-reset: docx-num-6-5 0;
}
p.docx-num-6-5:before {
  content: "\9";
  counter-increment: docx-num-6-5;
  font-family: Wingdings;
}
p.docx-num-6-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-6-5 {
  counter-reset: docx-num-6-6 0;
}
p.docx-num-6-6:before {
  content: "\9";
  counter-increment: docx-num-6-6;
  font-family: Wingdings;
}
p.docx-num-6-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-6-6 {
  counter-reset: docx-num-6-7 0;
}
p.docx-num-6-7:before {
  content: "\9";
  counter-increment: docx-num-6-7;
  font-family: Wingdings;
}
p.docx-num-6-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-6-7 {
  counter-reset: docx-num-6-8 0;
}
p.docx-num-6-8:before {
  content: "\9";
  counter-increment: docx-num-6-8;
  font-family: Wingdings;
}
p.docx-num-6-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-7-0:before {
  content: "\9";
  counter-increment: docx-num-7-0;
  font-family: Wingdings;
}
p.docx-num-7-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-7-0 {
  counter-reset: docx-num-7-1 0;
}
p.docx-num-7-1:before {
  content: "\9";
  counter-increment: docx-num-7-1;
  font-family: Wingdings;
}
p.docx-num-7-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-7-1 {
  counter-reset: docx-num-7-2 0;
}
p.docx-num-7-2:before {
  content: "\9";
  counter-increment: docx-num-7-2;
  font-family: Wingdings;
}
p.docx-num-7-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-7-2 {
  counter-reset: docx-num-7-3 0;
}
p.docx-num-7-3:before {
  content: "\9";
  counter-increment: docx-num-7-3;
  font-family: Wingdings;
}
p.docx-num-7-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-7-3 {
  counter-reset: docx-num-7-4 0;
}
p.docx-num-7-4:before {
  content: "\9";
  counter-increment: docx-num-7-4;
  font-family: Wingdings;
}
p.docx-num-7-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-7-4 {
  counter-reset: docx-num-7-5 0;
}
p.docx-num-7-5:before {
  content: "\9";
  counter-increment: docx-num-7-5;
  font-family: Wingdings;
}
p.docx-num-7-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-7-5 {
  counter-reset: docx-num-7-6 0;
}
p.docx-num-7-6:before {
  content: "\9";
  counter-increment: docx-num-7-6;
  font-family: Wingdings;
}
p.docx-num-7-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-7-6 {
  counter-reset: docx-num-7-7 0;
}
p.docx-num-7-7:before {
  content: "\9";
  counter-increment: docx-num-7-7;
  font-family: Wingdings;
}
p.docx-num-7-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-7-7 {
  counter-reset: docx-num-7-8 0;
}
p.docx-num-7-8:before {
  content: "\9";
  counter-increment: docx-num-7-8;
  font-family: Wingdings;
}
p.docx-num-7-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-8-0:before {
  content: "\9";
  counter-increment: docx-num-8-0;
  font-family: Wingdings;
}
p.docx-num-8-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-8-0 {
  counter-reset: docx-num-8-1 0;
}
p.docx-num-8-1:before {
  content: "\9";
  counter-increment: docx-num-8-1;
  font-family: Wingdings;
}
p.docx-num-8-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-8-1 {
  counter-reset: docx-num-8-2 0;
}
p.docx-num-8-2:before {
  content: "\9";
  counter-increment: docx-num-8-2;
  font-family: Wingdings;
}
p.docx-num-8-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-8-2 {
  counter-reset: docx-num-8-3 0;
}
p.docx-num-8-3:before {
  content: "\9";
  counter-increment: docx-num-8-3;
  font-family: Wingdings;
}
p.docx-num-8-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-8-3 {
  counter-reset: docx-num-8-4 0;
}
p.docx-num-8-4:before {
  content: "\9";
  counter-increment: docx-num-8-4;
  font-family: Wingdings;
}
p.docx-num-8-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-8-4 {
  counter-reset: docx-num-8-5 0;
}
p.docx-num-8-5:before {
  content: "\9";
  counter-increment: docx-num-8-5;
  font-family: Wingdings;
}
p.docx-num-8-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-8-5 {
  counter-reset: docx-num-8-6 0;
}
p.docx-num-8-6:before {
  content: "\9";
  counter-increment: docx-num-8-6;
  font-family: Wingdings;
}
p.docx-num-8-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-8-6 {
  counter-reset: docx-num-8-7 0;
}
p.docx-num-8-7:before {
  content: "\9";
  counter-increment: docx-num-8-7;
  font-family: Wingdings;
}
p.docx-num-8-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-8-7 {
  counter-reset: docx-num-8-8 0;
}
p.docx-num-8-8:before {
  content: "\9";
  counter-increment: docx-num-8-8;
  font-family: Wingdings;
}
p.docx-num-8-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-9-0:before {
  content: "\9";
  counter-increment: docx-num-9-0;
  font-family: Wingdings;
}
p.docx-num-9-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-9-0 {
  counter-reset: docx-num-9-1 0;
}
p.docx-num-9-1:before {
  content: "\9";
  counter-increment: docx-num-9-1;
  font-family: Wingdings;
}
p.docx-num-9-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-9-1 {
  counter-reset: docx-num-9-2 0;
}
p.docx-num-9-2:before {
  content: "\9";
  counter-increment: docx-num-9-2;
  font-family: Wingdings;
}
p.docx-num-9-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-9-2 {
  counter-reset: docx-num-9-3 0;
}
p.docx-num-9-3:before {
  content: "\9";
  counter-increment: docx-num-9-3;
  font-family: Wingdings;
}
p.docx-num-9-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-9-3 {
  counter-reset: docx-num-9-4 0;
}
p.docx-num-9-4:before {
  content: "\9";
  counter-increment: docx-num-9-4;
  font-family: Wingdings;
}
p.docx-num-9-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-9-4 {
  counter-reset: docx-num-9-5 0;
}
p.docx-num-9-5:before {
  content: "\9";
  counter-increment: docx-num-9-5;
  font-family: Wingdings;
}
p.docx-num-9-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-9-5 {
  counter-reset: docx-num-9-6 0;
}
p.docx-num-9-6:before {
  content: "\9";
  counter-increment: docx-num-9-6;
  font-family: Wingdings;
}
p.docx-num-9-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-9-6 {
  counter-reset: docx-num-9-7 0;
}
p.docx-num-9-7:before {
  content: "\9";
  counter-increment: docx-num-9-7;
  font-family: Wingdings;
}
p.docx-num-9-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-9-7 {
  counter-reset: docx-num-9-8 0;
}
p.docx-num-9-8:before {
  content: "\9";
  counter-increment: docx-num-9-8;
  font-family: Wingdings;
}
p.docx-num-9-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-10-0:before {
  content: "\9";
  counter-increment: docx-num-10-0;
  font-family: Wingdings;
}
p.docx-num-10-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-10-0 {
  counter-reset: docx-num-10-1 0;
}
p.docx-num-10-1:before {
  content: "\9";
  counter-increment: docx-num-10-1;
  font-family: Wingdings;
}
p.docx-num-10-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-10-1 {
  counter-reset: docx-num-10-2 0;
}
p.docx-num-10-2:before {
  content: "\9";
  counter-increment: docx-num-10-2;
  font-family: Wingdings;
}
p.docx-num-10-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-10-2 {
  counter-reset: docx-num-10-3 0;
}
p.docx-num-10-3:before {
  content: "\9";
  counter-increment: docx-num-10-3;
  font-family: Wingdings;
}
p.docx-num-10-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-10-3 {
  counter-reset: docx-num-10-4 0;
}
p.docx-num-10-4:before {
  content: "\9";
  counter-increment: docx-num-10-4;
  font-family: Wingdings;
}
p.docx-num-10-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-10-4 {
  counter-reset: docx-num-10-5 0;
}
p.docx-num-10-5:before {
  content: "\9";
  counter-increment: docx-num-10-5;
  font-family: Wingdings;
}
p.docx-num-10-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-10-5 {
  counter-reset: docx-num-10-6 0;
}
p.docx-num-10-6:before {
  content: "\9";
  counter-increment: docx-num-10-6;
  font-family: Wingdings;
}
p.docx-num-10-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-10-6 {
  counter-reset: docx-num-10-7 0;
}
p.docx-num-10-7:before {
  content: "\9";
  counter-increment: docx-num-10-7;
  font-family: Wingdings;
}
p.docx-num-10-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-10-7 {
  counter-reset: docx-num-10-8 0;
}
p.docx-num-10-8:before {
  content: "\9";
  counter-increment: docx-num-10-8;
  font-family: Wingdings;
}
p.docx-num-10-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-11-0:before {
  content: "\9";
  counter-increment: docx-num-11-0;
  font-family: Wingdings;
}
p.docx-num-11-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-11-0 {
  counter-reset: docx-num-11-1 0;
}
p.docx-num-11-1:before {
  content: "\9";
  counter-increment: docx-num-11-1;
  font-family: Wingdings;
}
p.docx-num-11-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-11-1 {
  counter-reset: docx-num-11-2 0;
}
p.docx-num-11-2:before {
  content: "\9";
  counter-increment: docx-num-11-2;
  font-family: Wingdings;
}
p.docx-num-11-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-11-2 {
  counter-reset: docx-num-11-3 0;
}
p.docx-num-11-3:before {
  content: "\9";
  counter-increment: docx-num-11-3;
  font-family: Wingdings;
}
p.docx-num-11-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-11-3 {
  counter-reset: docx-num-11-4 0;
}
p.docx-num-11-4:before {
  content: "\9";
  counter-increment: docx-num-11-4;
  font-family: Wingdings;
}
p.docx-num-11-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-11-4 {
  counter-reset: docx-num-11-5 0;
}
p.docx-num-11-5:before {
  content: "\9";
  counter-increment: docx-num-11-5;
  font-family: Wingdings;
}
p.docx-num-11-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-11-5 {
  counter-reset: docx-num-11-6 0;
}
p.docx-num-11-6:before {
  content: "\9";
  counter-increment: docx-num-11-6;
  font-family: Wingdings;
}
p.docx-num-11-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-11-6 {
  counter-reset: docx-num-11-7 0;
}
p.docx-num-11-7:before {
  content: "\9";
  counter-increment: docx-num-11-7;
  font-family: Wingdings;
}
p.docx-num-11-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-11-7 {
  counter-reset: docx-num-11-8 0;
}
p.docx-num-11-8:before {
  content: "\9";
  counter-increment: docx-num-11-8;
  font-family: Wingdings;
}
p.docx-num-11-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-12-0:before {
  content: ""counter(docx-num-12-0, decimal)".\9";
  counter-increment: docx-num-12-0;
}
p.docx-num-12-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-12-0 {
  counter-reset: docx-num-12-1 0;
}
p.docx-num-12-1:before {
  content: ""counter(docx-num-12-1, decimal)".\9";
  counter-increment: docx-num-12-1;
}
p.docx-num-12-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-12-1 {
  counter-reset: docx-num-12-2 0;
}
p.docx-num-12-2:before {
  content: ""counter(docx-num-12-2, decimal)".\9";
  counter-increment: docx-num-12-2;
}
p.docx-num-12-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-12-2 {
  counter-reset: docx-num-12-3 0;
}
p.docx-num-12-3:before {
  content: ""counter(docx-num-12-3, decimal)".\9";
  counter-increment: docx-num-12-3;
}
p.docx-num-12-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-12-3 {
  counter-reset: docx-num-12-4 0;
}
p.docx-num-12-4:before {
  content: ""counter(docx-num-12-4, decimal)".\9";
  counter-increment: docx-num-12-4;
}
p.docx-num-12-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-12-4 {
  counter-reset: docx-num-12-5 0;
}
p.docx-num-12-5:before {
  content: ""counter(docx-num-12-5, decimal)".\9";
  counter-increment: docx-num-12-5;
}
p.docx-num-12-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-12-5 {
  counter-reset: docx-num-12-6 0;
}
p.docx-num-12-6:before {
  content: ""counter(docx-num-12-6, decimal)".\9";
  counter-increment: docx-num-12-6;
}
p.docx-num-12-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-12-6 {
  counter-reset: docx-num-12-7 0;
}
p.docx-num-12-7:before {
  content: ""counter(docx-num-12-7, decimal)".\9";
  counter-increment: docx-num-12-7;
}
p.docx-num-12-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-12-7 {
  counter-reset: docx-num-12-8 0;
}
p.docx-num-12-8:before {
  content: ""counter(docx-num-12-8, decimal)".\9";
  counter-increment: docx-num-12-8;
}
p.docx-num-12-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-13-0:before {
  content: "("counter(docx-num-13-0, decimal)".\9";
  counter-increment: docx-num-13-0;
}
p.docx-num-13-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-13-0 {
  counter-reset: docx-num-13-1 0;
}
p.docx-num-13-1:before {
  content: "("counter(docx-num-13-1, decimal)".\9";
  counter-increment: docx-num-13-1;
}
p.docx-num-13-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-13-1 {
  counter-reset: docx-num-13-2 0;
}
p.docx-num-13-2:before {
  content: "("counter(docx-num-13-2, decimal)".\9";
  counter-increment: docx-num-13-2;
}
p.docx-num-13-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-13-2 {
  counter-reset: docx-num-13-3 0;
}
p.docx-num-13-3:before {
  content: "("counter(docx-num-13-3, decimal)".\9";
  counter-increment: docx-num-13-3;
}
p.docx-num-13-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-13-3 {
  counter-reset: docx-num-13-4 0;
}
p.docx-num-13-4:before {
  content: "("counter(docx-num-13-4, decimal)".\9";
  counter-increment: docx-num-13-4;
}
p.docx-num-13-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-13-4 {
  counter-reset: docx-num-13-5 0;
}
p.docx-num-13-5:before {
  content: "("counter(docx-num-13-5, decimal)".\9";
  counter-increment: docx-num-13-5;
}
p.docx-num-13-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-13-5 {
  counter-reset: docx-num-13-6 0;
}
p.docx-num-13-6:before {
  content: "("counter(docx-num-13-6, decimal)".\9";
  counter-increment: docx-num-13-6;
}
p.docx-num-13-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-13-6 {
  counter-reset: docx-num-13-7 0;
}
p.docx-num-13-7:before {
  content: "("counter(docx-num-13-7, decimal)".\9";
  counter-increment: docx-num-13-7;
}
p.docx-num-13-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-13-7 {
  counter-reset: docx-num-13-8 0;
}
p.docx-num-13-8:before {
  content: "("counter(docx-num-13-8, decimal)".\9";
  counter-increment: docx-num-13-8;
}
p.docx-num-13-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-14-0:before {
  content: ""counter(docx-num-14-0, upper-alpha)")\9";
  counter-increment: docx-num-14-0;
}
p.docx-num-14-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-14-0 {
  counter-reset: docx-num-14-1 0;
}
p.docx-num-14-1:before {
  content: ""counter(docx-num-14-1, upper-alpha)")\9";
  counter-increment: docx-num-14-1;
}
p.docx-num-14-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-14-1 {
  counter-reset: docx-num-14-2 0;
}
p.docx-num-14-2:before {
  content: ""counter(docx-num-14-2, upper-alpha)")\9";
  counter-increment: docx-num-14-2;
}
p.docx-num-14-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-14-2 {
  counter-reset: docx-num-14-3 0;
}
p.docx-num-14-3:before {
  content: ""counter(docx-num-14-3, upper-alpha)")\9";
  counter-increment: docx-num-14-3;
}
p.docx-num-14-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-14-3 {
  counter-reset: docx-num-14-4 0;
}
p.docx-num-14-4:before {
  content: ""counter(docx-num-14-4, upper-alpha)")\9";
  counter-increment: docx-num-14-4;
}
p.docx-num-14-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-14-4 {
  counter-reset: docx-num-14-5 0;
}
p.docx-num-14-5:before {
  content: ""counter(docx-num-14-5, upper-alpha)")\9";
  counter-increment: docx-num-14-5;
}
p.docx-num-14-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-14-5 {
  counter-reset: docx-num-14-6 0;
}
p.docx-num-14-6:before {
  content: ""counter(docx-num-14-6, upper-alpha)")\9";
  counter-increment: docx-num-14-6;
}
p.docx-num-14-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-14-6 {
  counter-reset: docx-num-14-7 0;
}
p.docx-num-14-7:before {
  content: ""counter(docx-num-14-7, upper-alpha)")\9";
  counter-increment: docx-num-14-7;
}
p.docx-num-14-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-14-7 {
  counter-reset: docx-num-14-8 0;
}
p.docx-num-14-8:before {
  content: ""counter(docx-num-14-8, upper-alpha)")\9";
  counter-increment: docx-num-14-8;
}
p.docx-num-14-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-15-0:before {
  content: ""counter(docx-num-15-0, lower-alpha)")\9";
  counter-increment: docx-num-15-0;
}
p.docx-num-15-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-15-0 {
  counter-reset: docx-num-15-1 0;
}
p.docx-num-15-1:before {
  content: ""counter(docx-num-15-1, lower-alpha)")\9";
  counter-increment: docx-num-15-1;
}
p.docx-num-15-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-15-1 {
  counter-reset: docx-num-15-2 0;
}
p.docx-num-15-2:before {
  content: ""counter(docx-num-15-2, lower-alpha)")\9";
  counter-increment: docx-num-15-2;
}
p.docx-num-15-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-15-2 {
  counter-reset: docx-num-15-3 0;
}
p.docx-num-15-3:before {
  content: ""counter(docx-num-15-3, lower-alpha)")\9";
  counter-increment: docx-num-15-3;
}
p.docx-num-15-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-15-3 {
  counter-reset: docx-num-15-4 0;
}
p.docx-num-15-4:before {
  content: ""counter(docx-num-15-4, lower-alpha)")\9";
  counter-increment: docx-num-15-4;
}
p.docx-num-15-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-15-4 {
  counter-reset: docx-num-15-5 0;
}
p.docx-num-15-5:before {
  content: ""counter(docx-num-15-5, lower-alpha)")\9";
  counter-increment: docx-num-15-5;
}
p.docx-num-15-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-15-5 {
  counter-reset: docx-num-15-6 0;
}
p.docx-num-15-6:before {
  content: ""counter(docx-num-15-6, lower-alpha)")\9";
  counter-increment: docx-num-15-6;
}
p.docx-num-15-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-15-6 {
  counter-reset: docx-num-15-7 0;
}
p.docx-num-15-7:before {
  content: ""counter(docx-num-15-7, lower-alpha)")\9";
  counter-increment: docx-num-15-7;
}
p.docx-num-15-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-15-7 {
  counter-reset: docx-num-15-8 0;
}
p.docx-num-15-8:before {
  content: ""counter(docx-num-15-8, lower-alpha)")\9";
  counter-increment: docx-num-15-8;
}
p.docx-num-15-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-16-0:before {
  content: "("counter(docx-num-16-0, lower-alpha)")\9";
  counter-increment: docx-num-16-0;
}
p.docx-num-16-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-16-0 {
  counter-reset: docx-num-16-1 0;
}
p.docx-num-16-1:before {
  content: "("counter(docx-num-16-1, lower-alpha)")\9";
  counter-increment: docx-num-16-1;
}
p.docx-num-16-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-16-1 {
  counter-reset: docx-num-16-2 0;
}
p.docx-num-16-2:before {
  content: "("counter(docx-num-16-2, lower-alpha)")\9";
  counter-increment: docx-num-16-2;
}
p.docx-num-16-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-16-2 {
  counter-reset: docx-num-16-3 0;
}
p.docx-num-16-3:before {
  content: "("counter(docx-num-16-3, lower-alpha)")\9";
  counter-increment: docx-num-16-3;
}
p.docx-num-16-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-16-3 {
  counter-reset: docx-num-16-4 0;
}
p.docx-num-16-4:before {
  content: "("counter(docx-num-16-4, lower-alpha)")\9";
  counter-increment: docx-num-16-4;
}
p.docx-num-16-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-16-4 {
  counter-reset: docx-num-16-5 0;
}
p.docx-num-16-5:before {
  content: "("counter(docx-num-16-5, lower-alpha)")\9";
  counter-increment: docx-num-16-5;
}
p.docx-num-16-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-16-5 {
  counter-reset: docx-num-16-6 0;
}
p.docx-num-16-6:before {
  content: "("counter(docx-num-16-6, lower-alpha)")\9";
  counter-increment: docx-num-16-6;
}
p.docx-num-16-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-16-6 {
  counter-reset: docx-num-16-7 0;
}
p.docx-num-16-7:before {
  content: "("counter(docx-num-16-7, lower-alpha)")\9";
  counter-increment: docx-num-16-7;
}
p.docx-num-16-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-16-7 {
  counter-reset: docx-num-16-8 0;
}
p.docx-num-16-8:before {
  content: "("counter(docx-num-16-8, lower-alpha)")\9";
  counter-increment: docx-num-16-8;
}
p.docx-num-16-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-17-0:before {
  content: ""counter(docx-num-17-0, upper-roman)".\9";
  counter-increment: docx-num-17-0;
}
p.docx-num-17-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-17-0 {
  counter-reset: docx-num-17-1 0;
}
p.docx-num-17-1:before {
  content: ""counter(docx-num-17-1, upper-roman)".\9";
  counter-increment: docx-num-17-1;
}
p.docx-num-17-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-17-1 {
  counter-reset: docx-num-17-2 0;
}
p.docx-num-17-2:before {
  content: ""counter(docx-num-17-2, upper-roman)".\9";
  counter-increment: docx-num-17-2;
}
p.docx-num-17-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-17-2 {
  counter-reset: docx-num-17-3 0;
}
p.docx-num-17-3:before {
  content: ""counter(docx-num-17-3, upper-roman)".\9";
  counter-increment: docx-num-17-3;
}
p.docx-num-17-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-17-3 {
  counter-reset: docx-num-17-4 0;
}
p.docx-num-17-4:before {
  content: ""counter(docx-num-17-4, upper-roman)".\9";
  counter-increment: docx-num-17-4;
}
p.docx-num-17-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-17-4 {
  counter-reset: docx-num-17-5 0;
}
p.docx-num-17-5:before {
  content: ""counter(docx-num-17-5, upper-roman)".\9";
  counter-increment: docx-num-17-5;
}
p.docx-num-17-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-17-5 {
  counter-reset: docx-num-17-6 0;
}
p.docx-num-17-6:before {
  content: ""counter(docx-num-17-6, upper-roman)".\9";
  counter-increment: docx-num-17-6;
}
p.docx-num-17-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-17-6 {
  counter-reset: docx-num-17-7 0;
}
p.docx-num-17-7:before {
  content: ""counter(docx-num-17-7, upper-roman)".\9";
  counter-increment: docx-num-17-7;
}
p.docx-num-17-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-17-7 {
  counter-reset: docx-num-17-8 0;
}
p.docx-num-17-8:before {
  content: ""counter(docx-num-17-8, upper-roman)".\9";
  counter-increment: docx-num-17-8;
}
p.docx-num-17-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-18-0:before {
  content: ""counter(docx-num-18-0, decimal)".\9";
  counter-increment: docx-num-18-0;
}
p.docx-num-18-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-18-0 {
  counter-reset: docx-num-18-1 0;
}
p.docx-num-18-1:before {
  content: ""counter(docx-num-18-1, decimal)".\9";
  counter-increment: docx-num-18-1;
}
p.docx-num-18-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-18-1 {
  counter-reset: docx-num-18-2 0;
}
p.docx-num-18-2:before {
  content: ""counter(docx-num-18-2, decimal)".\9";
  counter-increment: docx-num-18-2;
}
p.docx-num-18-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-18-2 {
  counter-reset: docx-num-18-3 0;
}
p.docx-num-18-3:before {
  content: ""counter(docx-num-18-3, decimal)".\9";
  counter-increment: docx-num-18-3;
}
p.docx-num-18-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-18-3 {
  counter-reset: docx-num-18-4 0;
}
p.docx-num-18-4:before {
  content: ""counter(docx-num-18-4, decimal)".\9";
  counter-increment: docx-num-18-4;
}
p.docx-num-18-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-18-4 {
  counter-reset: docx-num-18-5 0;
}
p.docx-num-18-5:before {
  content: ""counter(docx-num-18-5, decimal)".\9";
  counter-increment: docx-num-18-5;
}
p.docx-num-18-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-18-5 {
  counter-reset: docx-num-18-6 0;
}
p.docx-num-18-6:before {
  content: ""counter(docx-num-18-6, decimal)".\9";
  counter-increment: docx-num-18-6;
}
p.docx-num-18-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-18-6 {
  counter-reset: docx-num-18-7 0;
}
p.docx-num-18-7:before {
  content: ""counter(docx-num-18-7, decimal)".\9";
  counter-increment: docx-num-18-7;
}
p.docx-num-18-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-18-7 {
  counter-reset: docx-num-18-8 0;
}
p.docx-num-18-8:before {
  content: ""counter(docx-num-18-8, decimal)".\9";
  counter-increment: docx-num-18-8;
}
p.docx-num-18-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-19-0:before {
  content: ""counter(docx-num-19-0, decimal)".\a0";
  counter-increment: docx-num-19-0;
}
p.docx-num-19-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 37.70pt;
}
p.docx-num-19-0 {
  counter-reset: docx-num-19-1 0;
}
p.docx-num-19-1:before {
  content: ""counter(docx-num-19-1, decimal)".\9";
  counter-increment: docx-num-19-1;
}
p.docx-num-19-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 57.55pt;
}
p.docx-num-19-1 {
  counter-reset: docx-num-19-2 0;
}
p.docx-num-19-2:before {
  content: ""counter(docx-num-19-2, decimal)".\9";
  counter-increment: docx-num-19-2;
}
p.docx-num-19-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 77.40pt;
}
p.docx-num-19-2 {
  counter-reset: docx-num-19-3 0;
}
p.docx-num-19-3:before {
  content: ""counter(docx-num-19-3, decimal)".\9";
  counter-increment: docx-num-19-3;
}
p.docx-num-19-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 97.25pt;
}
p.docx-num-19-3 {
  counter-reset: docx-num-19-4 0;
}
p.docx-num-19-4:before {
  content: ""counter(docx-num-19-4, decimal)".\9";
  counter-increment: docx-num-19-4;
}
p.docx-num-19-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 117.10pt;
}
p.docx-num-19-4 {
  counter-reset: docx-num-19-5 0;
}
p.docx-num-19-5:before {
  content: ""counter(docx-num-19-5, decimal)".\9";
  counter-increment: docx-num-19-5;
}
p.docx-num-19-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 136.90pt;
}
p.docx-num-19-5 {
  counter-reset: docx-num-19-6 0;
}
p.docx-num-19-6:before {
  content: ""counter(docx-num-19-6, decimal)".\9";
  counter-increment: docx-num-19-6;
}
p.docx-num-19-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 156.75pt;
}
p.docx-num-19-6 {
  counter-reset: docx-num-19-7 0;
}
p.docx-num-19-7:before {
  content: ""counter(docx-num-19-7, decimal)".\9";
  counter-increment: docx-num-19-7;
}
p.docx-num-19-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 176.60pt;
}
p.docx-num-19-7 {
  counter-reset: docx-num-19-8 0;
}
p.docx-num-19-8:before {
  content: ""counter(docx-num-19-8, decimal)".\9";
  counter-increment: docx-num-19-8;
}
p.docx-num-19-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -19.85pt;
  margin-left: 196.45pt;
}
p.docx-num-20-0:before {
  content: ""counter(docx-num-20-0, decimal)".\9";
  counter-increment: docx-num-20-0;
}
p.docx-num-20-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-20-0 {
  counter-reset: docx-num-20-1 0;
}
p.docx-num-20-1:before {
  content: ""counter(docx-num-20-0, decimal)"."counter(docx-num-20-1, decimal)".\9";
  counter-increment: docx-num-20-1;
}
p.docx-num-20-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-20-1 {
  counter-reset: docx-num-20-2 0;
}
p.docx-num-20-2:before {
  content: ""counter(docx-num-20-2, decimal)".\9";
  counter-increment: docx-num-20-2;
}
p.docx-num-20-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-20-2 {
  counter-reset: docx-num-20-3 0;
}
p.docx-num-20-3:before {
  content: ""counter(docx-num-20-3, decimal)".\9";
  counter-increment: docx-num-20-3;
}
p.docx-num-20-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-20-3 {
  counter-reset: docx-num-20-4 0;
}
p.docx-num-20-4:before {
  content: ""counter(docx-num-20-4, decimal)".\9";
  counter-increment: docx-num-20-4;
}
p.docx-num-20-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-20-4 {
  counter-reset: docx-num-20-5 0;
}
p.docx-num-20-5:before {
  content: ""counter(docx-num-20-5, decimal)".\9";
  counter-increment: docx-num-20-5;
}
p.docx-num-20-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-20-5 {
  counter-reset: docx-num-20-6 0;
}
p.docx-num-20-6:before {
  content: ""counter(docx-num-20-6, decimal)".\9";
  counter-increment: docx-num-20-6;
}
p.docx-num-20-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-20-6 {
  counter-reset: docx-num-20-7 0;
}
p.docx-num-20-7:before {
  content: ""counter(docx-num-20-7, decimal)".\9";
  counter-increment: docx-num-20-7;
}
p.docx-num-20-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-20-7 {
  counter-reset: docx-num-20-8 0;
}
p.docx-num-20-8:before {
  content: ""counter(docx-num-20-8, decimal)".\9";
  counter-increment: docx-num-20-8;
}
p.docx-num-20-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-21-0:before {
  content: ""counter(docx-num-21-0, decimal)".\9";
  counter-increment: docx-num-21-0;
}
p.docx-num-21-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-21-0 {
  counter-reset: docx-num-21-1 0;
}
p.docx-num-21-1:before {
  content: ""counter(docx-num-21-1, decimal)".\9";
  counter-increment: docx-num-21-1;
}
p.docx-num-21-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-21-1 {
  counter-reset: docx-num-21-2 0;
}
p.docx-num-21-2:before {
  content: ""counter(docx-num-21-2, decimal)".\9";
  counter-increment: docx-num-21-2;
}
p.docx-num-21-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-21-2 {
  counter-reset: docx-num-21-3 0;
}
p.docx-num-21-3:before {
  content: ""counter(docx-num-21-3, decimal)".\9";
  counter-increment: docx-num-21-3;
}
p.docx-num-21-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-21-3 {
  counter-reset: docx-num-21-4 0;
}
p.docx-num-21-4:before {
  content: ""counter(docx-num-21-4, decimal)".\9";
  counter-increment: docx-num-21-4;
}
p.docx-num-21-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-21-4 {
  counter-reset: docx-num-21-5 0;
}
p.docx-num-21-5:before {
  content: ""counter(docx-num-21-5, decimal)".\9";
  counter-increment: docx-num-21-5;
}
p.docx-num-21-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-21-5 {
  counter-reset: docx-num-21-6 0;
}
p.docx-num-21-6:before {
  content: ""counter(docx-num-21-6, decimal)".\9";
  counter-increment: docx-num-21-6;
}
p.docx-num-21-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-21-6 {
  counter-reset: docx-num-21-7 0;
}
p.docx-num-21-7:before {
  content: ""counter(docx-num-21-7, decimal)".\9";
  counter-increment: docx-num-21-7;
}
p.docx-num-21-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-21-7 {
  counter-reset: docx-num-21-8 0;
}
p.docx-num-21-8:before {
  content: ""counter(docx-num-21-8, decimal)".\9";
  counter-increment: docx-num-21-8;
}
p.docx-num-21-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
p.docx-num-22-0:before {
  content: ""counter(docx-num-22-0, decimal)".";
  counter-increment: docx-num-22-0;
}
p.docx-num-22-0 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 36.00pt;
}
p.docx-num-22-0 {
  counter-reset: docx-num-22-1 0;
}
p.docx-num-22-1:before {
  content: ""counter(docx-num-22-1, decimal)".\9";
  counter-increment: docx-num-22-1;
}
p.docx-num-22-1 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 54.00pt;
}
p.docx-num-22-1 {
  counter-reset: docx-num-22-2 0;
}
p.docx-num-22-2:before {
  content: ""counter(docx-num-22-2, decimal)".\9";
  counter-increment: docx-num-22-2;
}
p.docx-num-22-2 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 72.00pt;
}
p.docx-num-22-2 {
  counter-reset: docx-num-22-3 0;
}
p.docx-num-22-3:before {
  content: ""counter(docx-num-22-3, decimal)".\9";
  counter-increment: docx-num-22-3;
}
p.docx-num-22-3 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 90.00pt;
}
p.docx-num-22-3 {
  counter-reset: docx-num-22-4 0;
}
p.docx-num-22-4:before {
  content: ""counter(docx-num-22-4, decimal)".\9";
  counter-increment: docx-num-22-4;
}
p.docx-num-22-4 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 108.00pt;
}
p.docx-num-22-4 {
  counter-reset: docx-num-22-5 0;
}
p.docx-num-22-5:before {
  content: ""counter(docx-num-22-5, decimal)".\9";
  counter-increment: docx-num-22-5;
}
p.docx-num-22-5 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 126.00pt;
}
p.docx-num-22-5 {
  counter-reset: docx-num-22-6 0;
}
p.docx-num-22-6:before {
  content: ""counter(docx-num-22-6, decimal)".\9";
  counter-increment: docx-num-22-6;
}
p.docx-num-22-6 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 144.00pt;
}
p.docx-num-22-6 {
  counter-reset: docx-num-22-7 0;
}
p.docx-num-22-7:before {
  content: ""counter(docx-num-22-7, decimal)".\9";
  counter-increment: docx-num-22-7;
}
p.docx-num-22-7 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 162.00pt;
}
p.docx-num-22-7 {
  counter-reset: docx-num-22-8 0;
}
p.docx-num-22-8:before {
  content: ""counter(docx-num-22-8, decimal)".\9";
  counter-increment: docx-num-22-8;
}
p.docx-num-22-8 {
  display: list-item;
  list-style-position: inside;
  list-style-type: none;
  text-indent: -18.00pt;
  margin-left: 180.00pt;
}
.docx-wrapper {
  counter-reset: docx-num-2-0 0 docx-num-2-1 0 docx-num-2-2 0 docx-num-2-3 0 docx-num-2-4 0 docx-num-2-5 0 docx-num-2-6 0 docx-num-2-7 0 docx-num-2-8 0 docx-num-3-0 0 docx-num-3-1 0 docx-num-3-2 0 docx-num-3-3 0 docx-num-3-4 0 docx-num-3-5 0 docx-num-3-6 0 docx-num-3-7 0 docx-num-3-8 0 docx-num-4-0 0 docx-num-4-1 0 docx-num-4-2 0 docx-num-4-3 0 docx-num-4-4 0 docx-num-4-5 0 docx-num-4-6 0 docx-num-4-7 0 docx-num-4-8 0 docx-num-5-0 0 docx-num-5-1 0 docx-num-5-2 0 docx-num-5-3 0 docx-num-5-4 0 docx-num-5-5 0 docx-num-5-6 0 docx-num-5-7 0 docx-num-5-8 0 docx-num-6-0 0 docx-num-6-1 0 docx-num-6-2 0 docx-num-6-3 0 docx-num-6-4 0 docx-num-6-5 0 docx-num-6-6 0 docx-num-6-7 0 docx-num-6-8 0 docx-num-7-0 0 docx-num-7-1 0 docx-num-7-2 0 docx-num-7-3 0 docx-num-7-4 0 docx-num-7-5 0 docx-num-7-6 0 docx-num-7-7 0 docx-num-7-8 0 docx-num-8-0 0 docx-num-8-1 0 docx-num-8-2 0 docx-num-8-3 0 docx-num-8-4 0 docx-num-8-5 0 docx-num-8-6 0 docx-num-8-7 0 docx-num-8-8 0 docx-num-9-0 0 docx-num-9-1 0 docx-num-9-2 0 docx-num-9-3 0 docx-num-9-4 0 docx-num-9-5 0 docx-num-9-6 0 docx-num-9-7 0 docx-num-9-8 0 docx-num-10-0 0 docx-num-10-1 0 docx-num-10-2 0 docx-num-10-3 0 docx-num-10-4 0 docx-num-10-5 0 docx-num-10-6 0 docx-num-10-7 0 docx-num-10-8 0 docx-num-11-0 0 docx-num-11-1 0 docx-num-11-2 0 docx-num-11-3 0 docx-num-11-4 0 docx-num-11-5 0 docx-num-11-6 0 docx-num-11-7 0 docx-num-11-8 0 docx-num-12-0 0 docx-num-12-1 0 docx-num-12-2 0 docx-num-12-3 0 docx-num-12-4 0 docx-num-12-5 0 docx-num-12-6 0 docx-num-12-7 0 docx-num-12-8 0 docx-num-13-0 0 docx-num-13-1 0 docx-num-13-2 0 docx-num-13-3 0 docx-num-13-4 0 docx-num-13-5 0 docx-num-13-6 0 docx-num-13-7 0 docx-num-13-8 0 docx-num-14-0 0 docx-num-14-1 0 docx-num-14-2 0 docx-num-14-3 0 docx-num-14-4 0 docx-num-14-5 0 docx-num-14-6 0 docx-num-14-7 0 docx-num-14-8 0 docx-num-15-0 0 docx-num-15-1 0 docx-num-15-2 0 docx-num-15-3 0 docx-num-15-4 0 docx-num-15-5 0 docx-num-15-6 0 docx-num-15-7 0 docx-num-15-8 0 docx-num-16-0 0 docx-num-16-1 0 docx-num-16-2 0 docx-num-16-3 0 docx-num-16-4 0 docx-num-16-5 0 docx-num-16-6 0 docx-num-16-7 0 docx-num-16-8 0 docx-num-17-0 0 docx-num-17-1 0 docx-num-17-2 0 docx-num-17-3 0 docx-num-17-4 0 docx-num-17-5 0 docx-num-17-6 0 docx-num-17-7 0 docx-num-17-8 0 docx-num-18-0 0 docx-num-18-1 0 docx-num-18-2 0 docx-num-18-3 0 docx-num-18-4 0 docx-num-18-5 0 docx-num-18-6 0 docx-num-18-7 0 docx-num-18-8 0 docx-num-19-0 0 docx-num-19-1 0 docx-num-19-2 0 docx-num-19-3 0 docx-num-19-4 0 docx-num-19-5 0 docx-num-19-6 0 docx-num-19-7 0 docx-num-19-8 0 docx-num-20-0 0 docx-num-20-1 0 docx-num-20-2 0 docx-num-20-3 0 docx-num-20-4 0 docx-num-20-5 0 docx-num-20-6 0 docx-num-20-7 0 docx-num-20-8 0 docx-num-21-0 0 docx-num-21-1 0 docx-num-21-2 0 docx-num-21-3 0 docx-num-21-4 0 docx-num-21-5 0 docx-num-21-6 0 docx-num-21-7 0 docx-num-21-8 0 docx-num-22-0 0 docx-num-22-1 0 docx-num-22-2 0 docx-num-22-3 0 docx-num-22-4 0 docx-num-22-5 0 docx-num-22-6 0 docx-num-22-7 0 docx-num-22-8 0;
}
</style><div class="docx-wrapper"><section class="docx" style="padding: 56.7pt; width: 595.3pt; min-height: 841.9pt;"><article><p class="docx_normal"><span lang="en-US">Bullets</span></p><p class="docx_normal docx-num-2-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-2-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-2-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-5-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-5-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-5-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-6-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-6-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-6-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-7-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-7-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-7-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-8-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-8-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-8-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-9-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-9-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-9-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-10-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-10-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-10-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-11-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-11-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-11-0"><span lang="en-US">three</span></p><p class="docx_normal docx-num-0-0" style="text-indent: 0pt; margin-left: 36pt;"><span lang="en-US"></span><span></span></p></article></section><section class="docx" style="padding: 56.7pt; width: 595.3pt; min-height: 841.9pt;"><article><p class="docx_normal"><span lang="en-US">Numbers</span></p><p class="docx_normal docx-num-3-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-3-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-3-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-12-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-12-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-12-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-13-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-13-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-13-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-17-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-17-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-17-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-4-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-4-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-4-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-14-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-14-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-14-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-15-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-15-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-15-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-16-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-16-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-16-0"><span lang="en-US">three</span></p><p class="docx_normal"><span></span><span></span></p></article></section><section class="docx" style="padding: 56.7pt; width: 595.3pt; min-height: 841.9pt;"><article><p class="docx_normal"><span lang="en-US">Nested numbering</span></p><p class="docx_normal docx-num-18-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-18-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-18-0"><span lang="en-US">three</span></p><p class="docx_normal docx-num-18-1"><span lang="en-US">four</span></p><p class="docx_normal docx-num-18-1"><span lang="en-US">five</span></p><p class="docx_normal"><span></span></p><p class="docx_normal docx-num-20-0"><span>one</span></p><p class="docx_normal docx-num-20-0"><span>two</span></p><p class="docx_normal docx-num-20-0"><span>three</span></p><p class="docx_normal docx-num-20-1"><span>four</span></p><p class="docx_normal docx-num-20-1"><span>five</span></p><p class="docx_normal"><span></span><span></span></p></article></section><section class="docx" style="padding: 56.7pt; width: 595.3pt; min-height: 841.9pt;"><article><p class="docx_normal"><span lang="en-US">Suff</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-21-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-21-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-21-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-19-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-19-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-19-0"><span lang="en-US">three</span></p><p class="docx_normal"><span lang="en-US"></span></p><p class="docx_normal docx-num-22-0"><span lang="en-US">one</span></p><p class="docx_normal docx-num-22-0"><span lang="en-US">two</span></p><p class="docx_normal docx-num-22-0"><span lang="en-US">three</span></p></article></section></div>