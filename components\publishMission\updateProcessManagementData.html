<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

    <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">


    <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script>
    <script src="../../plugins/layui/layui.js"></script>
    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>
</head>


<body style="padding: 15px">
<form class="layui-form layui-form-pane" action="">

    <div class="layui-form-item">
        <label class="layui-form-label">文件类别</label>
        <div class="layui-input-block" >
            <select name="FILE_TYPE" lay-verify="required" id="file_types">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">采集方式</label>
        <div class="layui-input-block">
            <select name="GATHERING_METHOD" lay-verify="required" id='gathering_method'>
                <option value=""></option>
                <option value="自动采集">自动采集</option>
                <option value="手工采集">手工采集</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">来源系统</label>
        <div class="layui-input-block">
            <select name="SOURCE_SYSTEM" lay-verify="required" id="systems">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">交付状态</label>
        <div class="layui-input-block">
            <select name="DELIVERY_STATE" lay-verify="required" id="dstates">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">修改时间</label>
        <div class="layui-input-block"> <!-- 注意：这一层元素并不是必须的 -->
            <input type="text" name="MODIFIED_TIMESTAMP" class="layui-input" id="updateTime">
        </div>
        <!--<div class="layui-form-mid layui-word-aux">辅助文字</div>-->
    </div>


    <div class="layui-form-item">
        <center>
            <button class="layui-btn" lay-submit lay-filter="formTask">提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </center>
    </div>
</form>



</body>

<script>
    layui.config({
        base: '/DataPackageManagement/build/js/' //假设这是你存放拓展模块的根目录
    }).use(['form','laydate','table','utils','layer'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var table = layui.table;
        var utils = layui.utils;
        var layer = layui.layer;
        laydate.render({
            elem: '#updateTime' //或 elem: document.getElementById('test')、elem: lay('#test') 等
        });

        //监听提交
        form.on('submit(formTask)', function (data) {
            layer.msg(JSON.stringify(data.field));
            var param = data.field;
            param.type = "PROCESS_CONTROL_DATA_LIST";
            twxAjax("publishMissionThing","AddDataToDataListTable",data.field);
            layer.alert("新增完成！");
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
            parent.layer.close(index); //再执行关闭
            return false;
        });

        var editedData = parent.selectedData;
        var initComponent = {
            file_types:{
                type:'文件类别'
            },
            systems:{
                type:'来源系统'
            },
            dstates:{
                type:'交付状态'
            }
        };

        for(var x in initComponent){
            //初始化下拉列表
            var type = initComponent[x].type;
            var compId = x;

            var prop = '';
            if(x === 'file_types'){
                prop = 'FILE_TYPE';
            }else if(x === 'systems'){
                prop = 'SOURCE_SYSTEM';
            }else if(x === 'dstates'){
                prop = 'DELIVERY_STATE';
            }

            var types = twxAjax("publishMissionThing","getDataFromDataDictionary",{type:type},false,function (data) {
                for (var i = 0;i<data.rows.length;i++){
                    var key = data.rows[i].ITEM_KEY;
                    var value = data.rows[i].ITEM_VALUE;
                    var html = '<option value="'+ key +'" ';
                        if(editedData[prop] === value){
                            html += ' selected ';
                        }
                        html += '>'+value+'</option>';
                    $("#" + compId).append(html);
                }

                form.render();
            });
        }

        //设定采集方式
        $('#gathering_method').val(editedData.GATHERING_METHOD);
        $('#updateTime').val(editedData.MODIFIED_TIMESTAMP);
        form.render();

    });
    

</script>