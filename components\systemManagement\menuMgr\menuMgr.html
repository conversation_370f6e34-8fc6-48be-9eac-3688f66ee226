<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
    <!-- <link href="../../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css"> -->
    <link rel="stylesheet" href="../../../plugins/layui/css/layui.css">
    

    <!-- <script src="../../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../../plugins/InsdepUI/jquery.easyui.min.js"></script> -->
    <!-- <script src="../../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
    <script src="../../../plugins/layui/layui.js"></script>
    <link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
	<link rel="stylesheet" href="../../../css/icon.css">
	
   <!-- <link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->
    <link rel="stylesheet" type="text/css" href="../../../plugins/font-awesome-4.7.0/css/font-awesome.min.css">

    <script src="../../../plugins/easyui/jquery.min.js"></script>
    <script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
    <script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
	<script src="../../../plugins/easyui/treegrid-dnd.js"></script>
    <script src="../../js/config/twxconfig.js"></script>
    <script src="../../js/util.js"></script>

    <script src="../../js/intercept.js"></script>
    <title>菜单管理</title>
    <style>
        .layui-form-label{
            width: 95px;
        }
		.datagrid-header td, .datagrid-body td, .datagrid-footer td{
			padding: 0 0;
		}
    </style>
</head>

<body>
    <div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
        <div data-options="region:'north',split:true,collapsible:false" style="height:50%;">
			<div id="menuTree_tb" style="padding: 5px;">
				 <button type="button"  class="layui-btn layui-btn-sm" id="menuadd">
				   <i class="layui-icon">&#xe608;</i> 添加
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm  layui-btn-warm" id="menuedit">
				   <i class="layui-icon">&#xe642;</i> 编辑
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm layui-btn-danger" id="menudel">
				   <i class="layui-icon">&#xe640;</i> 删除
				 </button>
			</div>
            <table id="menuTree"></table>
        </div>
        <div data-options="region:'center'" title="功能配置">
			<div id="funcTable_tb" style="padding: 5px;">
				 <button type="button"  class="layui-btn layui-btn-sm" id="addfunc">
				   <i class="layui-icon">&#xe608;</i> 添加
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm  layui-btn-warm" id="editfunc">
				   <i class="layui-icon">&#xe642;</i> 编辑
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm layui-btn-danger" id="delfunc">
				   <i class="layui-icon">&#xe640;</i> 删除
				 </button>
			</div>
            <div id="funcTable"></div>
        </div>
    </div>
</body>
<script src="menu.js"></script>
<script src="func.js"></script>