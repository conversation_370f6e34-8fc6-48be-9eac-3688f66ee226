/**
 * 处理单相关的图表 现场问题处理单和临时问题处理单的公共部分
 * <AUTHOR>
 * @date 2025-04-23
 */

var handlerList = [{
    numId: "xcwt-num",
    chartId: "xcwt-chart",
    fileType: "现场问题处理单",
    service: "QueryProblemCount"
}, {
    numId: "xcls-num",
    chartId: "xcls-chart",
    fileType: "现场临时处理单",
    service: "QueryTemporaryCount"
}];

/**
 * 显示详情标签页
 * @param {String} modelId 模型ID
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 * @param {String} fileType 文件类型
 * @param {Object} filterBtns 筛选按钮配置
 */
function showDetailTab(modelId, startDate, endDate, fileType, filterBtns) {
    // 存储当前fileType，用于后续筛选
    var curFileType = fileType;

    layer.tab({
        type: 1,
        tab: [{
            title: '流程图',
            content: '<div id="flowContent" style="height: 620px;padding:15px 50px 50px 50px;position:relative;">' + (filterBtns ? filterBtns.flow : '') + '<div id="diagramContainer" style="position: relative;width: 100%;height: 1px;"></div>'
        }, {
            title: '详细表格',
            content: '<div id="tableContent" style="height: 620px;padding:15px 50px 50px 50px;position:relative;">' + (filterBtns ? filterBtns.table : '') + '<div style="margin-top:45px;"></div><table id="list-table"></table></div>'
        }],
        anim: false,
        openDuration: 200,
        skin: 'layui-layer-tab my-layer',
        isOutAnim: false,
        closeDuration: 200,
        closeBtn: 2,
        shadeClose: false,
        maxmin: false,
        resize: false, //不允许拉伸
        area: ['1500px', '770px'],
        success: function () {
            /* 弹窗不加载滚动条 */
            loadProcess(modelId, startDate, endDate, curFileType);
            loadTable(modelId, startDate, endDate, curFileType);

            // 初始化表单组件
            form.render();

            // 添加导出按钮点击事件
            $('#export-table-btn').on('click', function () {
                // 获取当前表格的筛选条件
                // 优先使用全局变量中的当前选中节点processId
                var currentModelId = window.currentProcessId || modelId;
                var startDate = $('#start-date').val() || '';
                var endDate = $('#end-date').val() || '';
                var fileType = curFileType || '';
                var statusType = $('#table-status-select').val() || 'all';

                // 根据不同的文件类型处理状态筛选
                if (fileType.indexOf('现场临时处理单') > -1) {
                    if (statusType === 'finished' && fileType.indexOf('已完成') === -1) {
                        fileType = fileType + '|已完成';
                    } else if (statusType === 'unfinished' && fileType.indexOf('未完成') === -1) {
                        fileType = fileType + '|未完成';
                    }
                } else if (fileType.indexOf('现场问题处理单') > -1) {
                    // 处理现场问题处理单的状态筛选
                    if (fileType.indexOf('全部') > -1) {
                        // 如果是"全部"柱状图
                        if (statusType === 'closed' && fileType.indexOf('已闭环') === -1) {
                            fileType = fileType + '|已闭环';
                        } else if (statusType === 'open' && fileType.indexOf('未闭环') === -1) {
                            fileType = fileType + '|未闭环';
                        }
                    } else if (fileType.indexOf('更改设计文件') > -1 || fileType.indexOf('更改工艺文件') > -1) {
                        // 如果是"更改设计文件"或"更改工艺文件"柱状图
                        if (statusType === 'finished' && fileType.indexOf('已完成') === -1) {
                            fileType = fileType + '|已完成';
                        } else if (statusType === 'unfinished' && fileType.indexOf('未完成') === -1) {
                            fileType = fileType + '|未完成';
                        }
                    }
                }

                // 显示加载提示
                var loading;

                // 调用后端导出接口
                $.fileDownload(fileHandlerUrl + '/aitScreen/exportExcel', {
                    httpMethod: 'POST',
                    data: {
                        treeId: currentModelId,
                        startDate: startDate,
                        endDate: endDate,
                        fileType: fileType
                    },
                    prepareCallback: function (url) {
                        // 导出准备中
                        loading = layer.msg('正在导出数据，请稍候...', {
                            icon: 16,
                            shade: 0.3,
                            time: 0
                        });
                    },
                    abortCallback: function (url) {
                        layer.close(loading);
                        layer.msg("导出异常！！");
                    },
                    successCallback: function (url) {
                        // 导出成功
                        layer.close(loading);
                        layer.msg('导出成功', { icon: 1 });
                    },
                    failCallback: function (html, url) {
                        // 导出失败
                        layer.close(loading);
                        layer.msg('导出失败', { icon: 2 });
                    }
                });
            });

            // 添加状态筛选下拉框选择事件
            form.on('select(status-select)', function (data) {
                var statusType = data.value;
                var selectId = $(data.elem).attr('id');

                // 同步另一个下拉框的选中状态
                if (selectId === 'flow-status-select') {
                    $('#table-status-select').val(statusType);
                } else if (selectId === 'table-status-select') {
                    $('#flow-status-select').val(statusType);
                }
                form.render('select'); // 重新渲染下拉框

                // 根据当前状态更新fileType
                var baseFileType = curFileType.split('|');
                var newFileType = '';

                if (baseFileType.length >= 2) {
                    // 基本文件类型和分类
                    newFileType = baseFileType[0] + '|' + baseFileType[1];

                    // 添加状态标识，根据不同的文件类型和图表类型
                    if (newFileType.indexOf('现场临时处理单') > -1) {
                        // 现场临时处理单
                        if (statusType === 'finished') {
                            newFileType += '|已完成';
                        } else if (statusType === 'unfinished') {
                            newFileType += '|未完成';
                        }
                    } else if (newFileType.indexOf('现场问题处理单') > -1) {
                        // 现场问题处理单
                        if (filterBtns && filterBtns.chartType) {
                            if (filterBtns.chartType === 'whole') {
                                // 全部柱状图
                                if (statusType === 'closed') {
                                    newFileType += '|已闭环';
                                } else if (statusType === 'open') {
                                    newFileType += '|未闭环';
                                }
                            } else if (filterBtns.chartType === 'design' || filterBtns.chartType === 'tech') {
                                // 设计文件或工艺文件柱状图
                                if (statusType === 'finished') {
                                    newFileType += '|已完成';
                                } else if (statusType === 'unfinished') {
                                    newFileType += '|未完成';
                                }
                            }
                        }
                    }
                    // 全部状态不需要添加状态标识
                }

                // 更新全局当前fileType
                curFileType = newFileType;

                // 根据当前激活的标签页决定是否需要重新加载流程图
                var activeTabIndex = $('.layui-layer-tabmain').children('.layui-layer-tabli.layui-this').index();

                // 只有在流程图页签中选择状态时，才会重置流程节点的选中样式
                if (selectId === 'flow-status-select') {
                    window.currentProcessId = null;
                    // 清除流程图中的选中状态
                    $("#diagramContainer .v-mult").removeClass('v-mult-active');
                }

                // 检查是否有选中的流程节点
                var useProcessId = window.currentProcessId || null;
                var targetTreeId = useProcessId || modelId;

                // 无论在哪个标签页，都需要更新表格数据
                // 使用当前选中节点的processId或者默认的modelId
                table.reload('list-table', {
                    page: {
                        layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                        groups: 1,
                        first: false,
                        last: false,
                        curr: 1
                    },
                    where: {
                        treeId: targetTreeId,
                        startDate: startDate,
                        endDate: endDate,
                        fileType: curFileType
                    }
                });

                // 如果当前标签页是流程图，或者是从流程图标签页触发的事件，则重新加载流程图
                if (activeTabIndex === 0 || selectId === 'flow-status-select') {
                    // 保存当前选中节点的processId，避免加载过程中丢失
                    var savedProcessId = window.currentProcessId;
                    console.log("保存选中节点ID: ", savedProcessId);

                    loadProcess(modelId, startDate, endDate, curFileType);

                    // 确保在流程图加载完成后恢复节点选中状态
                    if (savedProcessId) {
                        // 使用更长的延时确保流程图完全加载
                        setTimeout(function () {
                            console.log("尝试恢复节点选中状态: ", savedProcessId);
                            $("#diagramContainer .v-mult").each(function () {
                                var nodeData = $(this).data("treeNode");
                                if (nodeData && nodeData.processId === savedProcessId) {
                                    $(this).addClass('v-mult-active');
                                    console.log("节点已选中: " + savedProcessId);
                                }
                            });
                        }, 800);
                    }
                }
            });
        }
    });
}

/**
 * 加载处理单的柱状图
 * @param {Object} modelId
 * @param {Object} startDate
 * @param {Object} endDate
 * @param {Object} fileType
 * @param {Object} chartId
 * @param {Object} service
 */
function loadHandleChart(modelId, startDate, endDate, fileType, chartId, service) {
    var chartDom = document.getElementById(chartId);
    var myChart = echarts.init(chartDom);
    myChart.showLoading("default", loadingOption);
    twxAjax(thing, service, {
        username: username,
        treeId: modelId,
        startDate: startDate,
        endDate: endDate
    }, true, function (res) {
        if (res.success) {
            myChart.hideLoading();
            var total = res.data.total;
            var option = res.data.option;

            // 存储完成状态数据
            if (service === "QueryTemporaryCount") {
                // 使用后端返回的真实完成状态数据
                window.completionStatusData = res.data.completionStatus || {};
                window.completionDataReady = true;
            } else if (service === "QueryProblemCount") {
                // 存储问题处理单状态数据
                window.problemStatusData = res.data.problemStatus || {};
            }

            option.tooltip.formatter = service === "QueryTemporaryCount" ? tempTooltipFormatter : problemTooltipFormatter;
            option.xAxis.axisLabel.formatter = xAxisLabelFormatter;
            option.legend.formatter = function (name) {
                return name + ' (' + total[name] + ')';
            };
            myChart.setOption(option);

            myChart.off('click');
            myChart.on('click', function (params) {
                // 根据不同的图表类型调用不同的点击处理函数
                if (fileType === "现场问题处理单") {
                    problemChartClick(params, startDate, endDate, params.seriesName);
                } else if (fileType === "现场临时处理单") {
                    tempChartClick(params, startDate, endDate, params.seriesName);
                }
            });
        }
    }, function (e) {

    });
}