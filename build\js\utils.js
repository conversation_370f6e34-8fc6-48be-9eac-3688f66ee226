"use strict";
layui.define(['jquery'], function(exports) {
	var $ = layui.jquery;
	//	var domainName = 'http://'+location.hostname;
	var utils = {
		domainName:'',
		appkey:"de0281ac-f0eb-4d7c-bd36-00db985cb6b7",
		twxajaxasync:function(thingName,serviceName,requestData,cb_success,cb_error){
			var url = utils.domainName + "/Thingworx/Things/" + thingName;
				url += "/Services/" + serviceName;
				url += "?method=POST&Accept=application/json&appKey=" + utils.appkey;
            $.ajax({
                type: "POST",
                url: url,
                async: true,
                dataType: "json",
                data: JSON.stringify(requestData),
                contentType: "application/json;charset=utf-8",
                success: cb_success,
                error: cb_error
            });
		},
		twxajax: function(ThingName, ServiceName, RequestData) {
			var url = '';
			var async = false;
			//			var domainName = 'http://127.0.0.1';

			//			if((location.origin == domainName || location.origin.indexOf('localhost') > -1 || location.origin.indexOf('127.0.0.1') > -1) && (location.origin.indexOf('8020') == -1)) {
			//				url = "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?method=POST&Accept=application/json";
			//			} else {
			url = utils.domainName + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?method=POST&Accept=application/json&appKey=" + utils.appkey;
			//			}
			var responseData = new Object();
			$.ajax({
				type: "POST",
				url: url,
				async: async,
				dataType: "json",
				data: JSON.stringify(RequestData),
				contentType: "application/json;charset=utf-8",
				success: function(data) {
					responseData = {
						data: data.rows,
						success: true
					};
				},
				error: function(error) {
					responseData = {
						data: error,
						success: false
					};
				}
			});
			return responseData;
		},
		twxajax2: function(ThingName, ServiceName, RequestData) {
			var url = '';
			var async = false;
			//			var domainName = 'http://127.0.0.1';

			//			if((location.origin == domainName || location.origin.indexOf('localhost') > -1 || location.origin.indexOf('127.0.0.1') > -1) && (location.origin.indexOf('8020') == -1)) {
			//				url = "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?method=POST&Accept=application/json";
			//			} else {
			url = utils.domainName + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?method=POST&Accept=application/json&appKey=" + utils.appkey;
			//			}
			var responseData = new Object();
			$.ajax({
				type: "POST",
				url: url,
				async: async,
				dataType: "json",
				data: JSON.stringify(RequestData),
				contentType: "application/json;charset=utf-8",
				success: function(data) {
					responseData = {
						data: data,
						success: true
					};
				},
				error: function(error) {
					responseData = {
						data: error,
						success: false
					};
				}
			});
			return responseData;
		},
		getUrl: function(ThingName, ServiceName, RequestData) {
			var url = '';
			if(RequestData == undefined) {
				RequestData = '';
			}
			//          var domainName = 'http://127.0.0.1';
			url = utils.domainName + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?method=POST&Accept=application/json" + RequestData + "&appKey=" + utils.appkey;
			return url;
		},
		getMenuListData: function(list) {
			var item, result = [];
			var getAllChildrens = function(list1, item1) {
				var childrens = getNextLevelChildrens(list1, item1);
				for(var i = 0, ii = childrens.length; i < ii; i++) {
					getAllChildrens(list1, childrens[i]);
				}
			}
			//遍历list剩下的数据，找到item的下一层的子节点
			var getNextLevelChildrens = function(list2, item2) {
				var childrens = [];
				for(var i = list2.length - 1; i >= 0; i--) {
					var mid = list2[i];
					if(mid.menupid === item2.menuid) {
						//						delete mid.menupid;
						childrens.push(mid);
						list2.splice(i, 1);
					}
				}
				if(childrens.length > 0) {
					item2.list = childrens;
				}
				return childrens;
			}
			//遍历根节点，递归处理其所有子节点的数据
			//每处理完一个根节点，就将其及其所有子节点从list中删除，加快递归速度
			while(list.length) {
				item = list[0];
				list.splice(0, 1);
				delete item.menupid;
				getAllChildrens(list, item);
				result.push(item);
			}
			return result;
		},
		getUserNameById: function(ids) {
			/**
			 * 通过id获取姓名
			 */
			if(ids.trim() == '') {
				return '';
			} else {
				var result = utils.twxajax('MYSQL_CONNECT', 'QueryUserByids', {
					ids: ids
				});
				if(result.success) {
					var namearr = [];
					var data = result.data;
					for(var i = 0; i < data.length; i++) {
						namearr.push(data[i].fullname);
					}
					var names = namearr.join(',');
					return names;
				} else {
					return '请求失败';
				}
			}
		},
		getTaskInfoById: function(taskid) {
			var result = utils.twxajax('MYSQL_CONNECT', 'query_task_info_by_taskid', {
				task_id: taskid
			});
			if(result.success) {
				return result.data;
			} else {
				return '请求失败';
			}
		},
		getAllStations: function() {
			var result = utils.twxajax('MYSQL_CONNECT', 'query_bas_station_by_id', {
				station_id: sessionStorage.getItem('station_id')
			});
			if(result.success) {

				return result.data;
			} else {
				return '请求失败';
			}
		},
		/**
		 * 判断一个元素是否在一个数组中
		 * @param arr
		 * @param val
		 * @returns {boolean}
		 */
		contains: function(arr, val) {
			return utils.indexOf(arr, val) != -1 ? true : false;
		},

		/**
		 * 获取数组的下标
		 * @param arr
		 * @param val
		 * @returns {number}
		 */
		indexOf: function(arr, val) {
			for(var i = 0; i < arr.length; i++) {
				if(arr[i] == val) {
					return i;
				}
			}
			return -1;
		},
		removeFunc: function() {
			var func = JSON.parse(sessionStorage.getItem('func'));
			var idarr = [];
			if(func != null) {
				for(var i = 0; i < func.length; i++) {
					var id = func[i].id;
					idarr.push(id);
				}
			}
			$("[func-id]").each(function(i, e) {
				var func_id = $(e).attr('func-id');
				if(utils.contains(idarr, func_id)) {
					$(e).show();
				}
			})
		},
		getFormatDate: function(date, fmt) {
			//author: meizz 
			var o = {
				"M+": date.getMonth() + 1, //月份 
				"d+": date.getDate(), //日 
				"h+": date.getHours(), //小时 
				"m+": date.getMinutes(), //分 
				"s+": date.getSeconds(), //秒 
				"q+": Math.floor((date.getMonth() + 3) / 3), //季度 
				"S": date.getMilliseconds() //毫秒 
			};
			if(/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
			for(var k in o)
				if(new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
			return fmt;
		}
	};
	exports('utils', utils);
});