/**
 * 确认表统计功能
 * <AUTHOR>
 */

// 统计类型常量
var STATISTICS_TYPE = {
    LOCK: 'lock',
    SIGN: 'sign'
};

/**
 * 显示确认表锁定统计
 * @param {Object} treeNode 树节点
 */
function showLockStatistics(treeNode) {
    // 显示加载中
    var loading = layer.load(1, {
        shade: [0.3, '#000']
    });

    // 调用后端服务查询未锁定表
    twxAjax(THING, 'QueryUnlockedTables', {
        nodeId: treeNode.ID.split('_')[1]
    }, true,
        function (res) {
            layer.close(loading);
            if (res.success) {
                if (res.data && res.data.length > 0) {
                    // 渲染数据到新窗口
                    openStatisticsWindow(res.data, treeNode, STATISTICS_TYPE.LOCK);
                } else {
                    layer.msg('未发现未锁定的确认表！');
                }
            } else {
                layer.alert(res.msg, {
                    icon: 2
                });
            }
        },
        function (xhr, textStatus, errorThrown) {
            layer.close(loading);
            layer.alert('请求出错！', {
                icon: 2
            });
        });
}

/**
 * 显示确认表签署统计
 * @param {Object} treeNode 树节点
 */
function showSignStatistics(treeNode) {
    // 显示加载中
    var loading = layer.load(1, {
        shade: [0.3, '#000']
    });

    // 调用后端服务查询应签未签表
    twxAjax(THING, 'QueryUnsignedTables', {
        nodeId: treeNode.ID.split('_')[1]
    }, true,
        function (res) {
            layer.close(loading);
            if (res.success) {
                if (res.data && res.data.length > 0) {
                    // 渲染数据到新窗口
                    openStatisticsWindow(res.data, treeNode, STATISTICS_TYPE.SIGN);
                } else {
                    layer.msg('未发现应签未签的确认表！');
                }
            } else {
                layer.alert(res.msg, {
                    icon: 2
                });
            }
        },
        function (xhr, textStatus, errorThrown) {
            layer.close(loading);
            layer.alert('请求出错！', {
                icon: 2
            });
        });
}

/**
 * 打开统计窗口
 * @param {Array} data 统计数据
 * @param {Object} treeNode 树节点
 * @param {String} type 统计类型（lock/sign）
 */
function openStatisticsWindow(data, treeNode, type) {
    // 将数据和节点ID存储到sessionStorage以便新窗口访问
    var statData = {
        data: data,
        nodeId: treeNode.ID,
        originWindow: window.name || 'mainWindow',
        type: type
    };

    // 使用唯一ID和类型前缀确保每次都创建新窗口
    var windowId = type + 'Stats_' + new Date().getTime();
    sessionStorage.setItem(windowId, JSON.stringify(statData));

    // 打开新窗口
    var url = type === STATISTICS_TYPE.LOCK ? 'lockStatistics.html' : 'signStatistics.html';
    var windowSize = type === STATISTICS_TYPE.LOCK ? 'width=1200,height=700' : 'width=1400,height=740';
    window.open(url + '?id=' + windowId, windowId, windowSize + ',scrollbars=yes');
}

/**
 * 导出未锁定表Excel
 * @param {Number} nodeId 节点ID
 */
function exportUnlockedTablesExcel(nodeId) {
    var url = fileHandlerUrl + "/report/export/unlocked/excel";
    $.fileDownload(url, {
        httpMethod: 'POST',
        data: {
            "nodeId": nodeId.split('_')[1]
        },
        prepareCallback: function (url) {
            layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        successCallback: function (url) {
            layer.closeAll();
            layer.msg("导出成功！");
        },
        failCallback: function (html, url) {
            layer.closeAll();
            layer.alert("导出失败！", {
                icon: 2
            });
        }
    });
}

/**
 * 导出应签未签表Excel
 * @param {Number} nodeId 节点ID
 */
function exportUnsignedTablesExcel(nodeId) {
    var url = fileHandlerUrl + "/report/export/unsigned/excel";
    $.fileDownload(url, {
        httpMethod: 'POST',
        data: {
            "nodeId": nodeId.split('_')[1]
        },
        prepareCallback: function (url) {
            layer.msg("正在导出...", {
                icon: 16,
                shade: 0.3,
                time: 0
            });
        },
        successCallback: function (url) {
            layer.closeAll();
            layer.msg("导出成功！");
        },
        failCallback: function (html, url) {
            layer.closeAll();
            layer.alert("导出失败！", {
                icon: 2
            });
        }
    });
}

function locateNode() {
    var bpmTaskInfo = sessionStorage.getItem("bpmTaskInfo");
    if (bpmTaskInfo) {
        var taskInfo = JSON.parse(bpmTaskInfo);
        var tableId = taskInfo.TABLE_ID;
        locateToNodeFromMessage(tableId);
    }
}


/**
 * 从消息事件中定位到节点
 * @param {Number} nodeId 节点ID
 */
function locateToNodeFromMessage(nodeId) {
    var cb_success = function (res) {
        if (res.success) {
            var trees = res.data;
            locationAitTreeNode(trees, function (thisNode) {
                $("#" + thisNode.tId + "_a").click();
            });
        } else {
            layer.msg(res.msg);
        }
    };
    twxAjax('Thing.Fn.QualityReport', 'QueryAllPNode', {
        nodeId: nodeId
    }, true, cb_success);
}

// 添加消息监听器，处理跨窗口通信
window.addEventListener('message', function (event) {
    // 确保消息来源可信
    if (event.data && event.data.action) {
        switch (event.data.action) {
            case 'locateNode':
                if (event.data.nodeId) {
                    locateToNodeFromMessage(event.data.nodeId);
                }
                break;
            case 'exportLockExcel':
                if (event.data.nodeId) {
                    exportUnlockedTablesExcel(event.data.nodeId);
                }
                break;
            case 'exportSignExcel':
                if (event.data.nodeId) {
                    exportUnsignedTablesExcel(event.data.nodeId);
                }
                break;
        }
    }
}); 