<!--docxjs library predefined styles--><style>
.docx-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.docx-wrapper>section.docx { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }
.docx { color: black; hyphens: auto; text-underline-position: from-font; }
section.docx { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.docx>article { margin-bottom: auto; z-index: 1; }
section.docx>footer { z-index: 1; }
.docx table { border-collapse: collapse; }
.docx table td, .docx table th { vertical-align: top; }
.docx p { margin: 0pt; min-height: 1em; }
.docx span { white-space: pre-wrap; overflow-wrap: break-word; }
.docx a { color: inherit; text-decoration: inherit; }
</style><!--docxjs document styles--><style>.docx span {
  font-family: Liberation Serif;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
.docx p {
}
p.docx_normal {
}
p.docx_normal span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style14 {
  margin-top: 12.00pt;
  margin-bottom: 6.00pt;
}
p.docx_style14 span {
  font-family: Liberation Sans;
  min-height: 14.00pt;
  font-size: 14.00pt;
  color: black;
}
p.docx_style15 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
}
p.docx_style15 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style16 {
  margin-top: 0.00pt;
  margin-bottom: 7.00pt;
  line-height: 1.15;
}
p.docx_style16 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
p.docx_style17 {
  margin-top: 6.00pt;
  margin-bottom: 6.00pt;
}
p.docx_style17 span {
  font-style: italic;
  min-height: 12.00pt;
  font-size: 12.00pt;
  font-family: Liberation Serif;
  color: black;
}
p.docx_style18 {
}
p.docx_style18 span {
  font-family: Liberation Serif;
  color: black;
  min-height: 12.00pt;
  font-size: 12.00pt;
}
</style><div class="docx-wrapper"><section class="docx" style="padding: 56.7pt; width: 595.3pt; min-height: 841.9pt;"><article><p class="docx_normal"><span lang="en-US">page1</span></p></article></section><section class="docx" style="padding: 56.7pt; width: 841.9pt; min-height: 595.3pt;"><article><p class="docx_normal"><span lang="en-US">page2</span></p></article></section></div>