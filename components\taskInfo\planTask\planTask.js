var tree, table, upload, form, taskId, optType;
var THING = 'Thing.Fn.Interface';
layui.use(function() {
	tree = layui.tree;
	table = layui.table;
	upload = layui.upload;
	form = layui.form;
	taskId = getQueryString("taskId");
	optType = getQueryString("type");
	twxAjax(THING, "QueryPlanTaskInfo", {
		taskId: taskId
	}, true, function(res) {
		if (res.success) {
			renderTree(res.data);
			renderDataInfo(res.data.planningDataInfo);
			//睡眠一秒再执行
			setTimeout(function() {
				renderPlanListTable(res.data.planningDataInfo);
			}, 100);
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});
});

function getTreeData(taskInfo) {
	//组建tree数据
	var planningDataInfo = taskInfo.planningDataInfo;
	var treeList = [];
	planningDataInfo.pid = '-1';
	planningDataInfo.id = planningDataInfo.MODEL_ID;
	planningDataInfo.type = 'model';
	planningDataInfo.spread = true;
	planningDataInfo.title = planningDataInfo.MODEL_NAME;
	treeList.push(planningDataInfo);
	var treeNodeList = planningDataInfo.treeNodeList;
	for (var i = 0; i < treeNodeList.length; i++) {
		var treeNode = treeNodeList[i];
		treeList.push({
			pid: treeNode.PID,
			id: treeNode.ID,
			title: treeNode.NAME,
			type: treeNode.TREE_NODE_TYPE,
			spread: true
		});
	}
	return transformTree(treeList);
}

// 定义一个函数，用于将树形结构的数据转换为平铺结构
function transformTree(data) {
	// 创建一个空对象，用于存储每个节点的信息
	var map = {};
	// 创建一个空数组，用于存储根节点
	var root = [];
	// 遍历数据
	data.forEach(function(item) {
		// 如果map中没有当前节点的id，则将当前节点添加到map中，并创建一个空数组，用于存储当前节点的子节点
		if (!map[item.id]) {
			map[item.id] = item;
			map[item.id].children = [];
			// 如果当前节点的pid为-1，则将当前节点添加到根节点数组中
			if (item.pid === '-1') {
				root.push(map[item.id]);
			} else {
				// 如果map中没有当前节点的pid，则将当前节点的pid添加到map中，并创建一个空数组，用于存储当前节点的父节点的子节点
				if (!map[item.pid]) {
					map[item.pid] = {};
					map[item.pid].children = [];
				}
				// 将当前节点添加到当前节点的父节点的子节点数组中
				map[item.pid].children.push(map[item.id]);
			}
		}
	});
	// 返回根节点数组
	return root;
}

function renderTree(taskInfo) {
	var treeData = getTreeData(taskInfo);
	// 渲染
	tree.render({
		elem: '#tree',
		data: treeData,
		showLine: true,
		showCheckbox: false, // 是否显示复选框
		onlyIconControl: true, // 是否仅允许节点左侧图标控制展开收缩
		id: 'tree',
		isJump: false, // 是否允许点击节点时弹出新窗口跳转
		click: function(obj) {
			var data = obj.data; //获取当前点击的节点数据
			$('.layui-tree-entry').css('background-color', '');
			$(obj.elem[0]).children('.layui-tree-entry').css('background-color', 'aliceblue');
			// clickTree(data);
		}
	});
}

/**
 * 加载策划数据
 */
function renderDataInfo(node) {
	var forms = [{
		name: "型号类型",
		value: node.MODEL_TYPE
	}, {
		name: "型号领域",
		value: node.MODEL_FIELD
	}, {
		name: "所属系列",
		value: node.BELONG_SERIES
	}, {
		name: "型号名称",
		value: node.MODEL_NAME
	}, {
		name: "型号代号",
		value: node.MODEL_CODE
	}, {
		name: "来源单位",
		value: node.SOURCE_UNIT
	}, {
		name: "抓总单位",
		value: node.GRASPING_UNIT
	}, {
		name: "节点创建人",
		value: node.NODE_CREATE_USER
	}, {
		name: "节点负责人",
		value: node.NODE_OWNER
	}, {
		name: "策划人",
		value: node.DATA_PACKET_PLAN_USER
	}];
	$("#list-body").append(get$Form(forms, 5));
}

/**
 * 加载策划清单列表
 */
function renderPlanListTable(node) {
	var inst = table.render({
		elem: '#planListTable',
		cols: [
			[{
				field: 'CODE',
				title: '编号',
				width: 100,
				align: 'center'
			}, {
				field: 'NAME',
				title: '名称',
				width: 180,
				align: 'center'
			}, {
				field: 'PLANNING_UNIT',
				title: '策划单位',
				width: 100,
				align: 'center'
			}, {
				field: 'RECEIVING_UNIT',
				title: '策划接收单位',
				width: 115,
				align: 'center'
			}, {
				field: 'DESIGN_TYPE',
				title: '节点策划类型',
				width: 115,
				align: 'center'
			}, {
				field: 'CREATE_TIME',
				title: '创建日期',
				width: 160,
				align: 'center'
			}, {
				field: 'DATA_PACKET_REQUEST',
				title: '数据包要求',
				align: 'left'
			}, {
				field: 'ASSIGN_PERSON',
				title: '分配人',
				align: 'left',
				width: 150
			}, {
				field: 'STATUS',
				title: '状态',
				align: 'center',
				width: 75
			}, {
				field: 'operation',
				align: 'center',
				title: '操作',
				width: 120,
				templet: function(d) {
					var btns = '';
					if (optType == 'toDo') {
						btns += '<a class="layui-btn layui-btn-xs" lay-event="assign">分配</a>';
						if (d.ASSIGN_PERSON.indexOf('(' + sessionStorage.getItem('username') + ')') > -1) {
							btns += '<a class="layui-btn layui-btn-xs" lay-event="handle">办理</a>';
						}
					} else {
						btns += '<a class="layui-btn layui-btn-xs" lay-event="handle">查看</a>';
					}

					return ' <div class="layui-clear-space">' + btns + '</div>';
				}
			}]
		],
		defaultToolbar: [],
		data: node.planningListInfo,
		page: false
	});

	table.on('tool(planListTable)', function(obj) {
		var data = obj.data;
		if (obj.event === 'assign') {
			layer.open({
				title: "分配任务",
				type: 1,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				shadeClose: false,
				resize: false,
				area: ['350px', '175px'],
				scrollbar: false,
				btn: ['确定', '取消'],
				btn1: function(index, layero, that) {
					var user = $("#user").val();
					if (user) {
						twxAjax(THING, 'AssignPlanTask', {
							"planListId": data.UUID,
							"user": user
						}, true, function(res) {
							if (res.success) {
								layer.msg("分配成功！");
								table.updateRow('planListTable', {
									index: obj.index,
									data: {
										ASSIGN_PERSON: user,
										STATUS: '已分配'
									},
									// 是否更新关联的列视图
									related: function(field, index) {
										return true;
									}
								});
								layer.close(index);
							} else {
								layer.msg("分配失败！");
							}
						});
					} else {
						layer.msg("请选择用户！");
						return false;
					}
				},
				btn2: function(index, layero, that) {
					return true;
				},
				content: '<div class="user-form-content" id="user-form-content">\
							<form class="layui-form" style="padding:15px;" action="" lay-filter="user-form">\
								<div class="layui-form-item" style="margin-bottom: 0;">\
									<select name="user" lay-filter="user" id="user" lay-verify="required" lay-search>\
									</select>\
								</div>\
							</form>\
						</div>',
				success: function(layero, userLayerIndex, that) {
					$(layero).find('.layui-layer-content').css("overflow", "visible");
					twxAjax(THING, 'QueryAllUser', {}, true, function(res) {
						if (res.success) {
							$("#user").append('<option value="">请选择<option>');
							var users = res.data;
							for (var i = 0; i < users.length; i++) {
								var userDisplay = users[i]["USER_FULLNAME"] + '(' + users[i]
									["USER_NAME"] + ')';
								$("#user").append('<option value="' + userDisplay + '">' +
									userDisplay + '<option>');
							}
							form.render("select");
						} else {
							layer.alert(res.msg);
						}
					}, function(xhr, textStatus, errorThrown) {
						layer.alert('请求出错！', {
							icon: 2
						});
					});
				}
			});
		} else if (event = 'handle') {
			var header = data.DATA_PACKET_REQUEST;
			var btn = ['取消', '提交'];
			if (optType != 'toDo') {
				btn = ['取消', '确定'];
			}
			layer.open({
				type: 2,
				title: [header, 'font-size: 20px;'],
				shadeClose: true,
				maxmin: true,
				area: ['1500px', '800px'],
				content: '../../../components/taskInfo/planTask/handlePlanTask/handlePlanTask.html?type=' + optType + '&listId=' + data.UUID,
				success: function(layero, index) {
					$($(layero).find('.layui-layer-btn').children('a')[0]).removeClass(
						'layui-layer-btn0').addClass('layui-layer-btn1');
					$($(layero).find('.layui-layer-btn').children('a')[1]).removeClass(
						'layui-layer-btn1').addClass('layui-layer-btn0');
					layer.full(index);
				},
				btn: btn,
				btn1: function(index, layero, that) {
					layer.close(index);
				},
				btn2: function(index, layero, that) {
					if (optType == 'toDo') {
						var loadIndex = layer.msg('提交中', {
							icon: 16,
							shade: 0.01,
							time: 0
						});
						twxAjax(THING, 'SubmitPkg', {
							planningListId: data.UUID,
							optUser: sessionStorage.getItem("username")
						}, true, function(res) {
							layer.close(loadIndex);
							if (res.success) {
								layer.msg(res.msg);
								layer.close(index);
							} else {
								layer.alert(res.msg, {
									icon: 2
								});
							}
						}, function(e) {
							layer.alert("提交失败！");
						});
						return false;
					} else {
						layer.close(index);
					}
				}
			});

		}
	});
}