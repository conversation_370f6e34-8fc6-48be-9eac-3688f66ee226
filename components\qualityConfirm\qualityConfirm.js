var THING = 'Thing.Fn.QualityOnlineConfirm';
var funcIdent = "confirm";

function generateApocalypse(treeNode) {
	var loading;
	var url = fileHandlerUrl + '/apocalypse/generate';
	$.fileDownload(url, {
		httpMethod: 'POST',
		data: {
			treeId: treeNode.ID
		},
		prepareCallback: function (url) {
			loading = layer.msg("正在生成问题启示录...", {
				icon: 16,
				shade: 0.3,
				time: 0
			});
		},
		abortCallback: function (url) {
			layer.close(loading);
			layer.msg("生成问题启示录异常！");
		},
		successCallback: function (url) {
			layer.close(loading);
			layer.msg("生成问题启示录成功！", {
				icon: 1
			});
		},
		failCallback: function (html, url, error) {
			layer.close(loading);
			layer.alert(html || "生成问题启示录失败！", {
				icon: 2
			});
		}
	});
}

function syncQualityProblemData(treeNode) {
	var loading = layer.msg("正在同步质量问题数据...", {
		icon: 16,
		shade: 0.3,
		time: 0
	});
	twxAjax('Thing.Fn.BPM', "SyncQualityProblemData", {}, true, function (res) {
		layer.close(loading);
		if (res.success) {
			layer.msg(res.msg);
			reloadTree(treeNode.ID);
		} else {
			layer.alert({ icon: 2 }, res.msg || '同步失败');
		}
	}, function () {
		layer.close(loading);
		layer.alert({ icon: 2 }, '同步失败');
	});
}