/**
 * 查看流程详细信息的
 */

/**
 * 查看详细的表单信息
 */
function viewForm(formData, pEl) {
	var html = $("#zero-form-html")[0].innerHTML;
	$("#" + pEl).append(html);
	dealForm("viewProcess");
	form.render(null, 'zero-form');
	$("p[name]").each(function (i, n) {
		$(n).text(formData[$(n).attr("name")]);
	});
	form.val('zero-form', formData);
}

/**
 * 查询流转记录
 */
function viewTransferRecords(rowData, pEl) {

	var procInstId = rowData["PROCESS_INSTANCE_ID"];

	twxAjax(THING, 'QueryFlwRecords', {
		procInstId: procInstId,
	}, true, function (res) {
		if (res.success) {
			var timeLineHtml = getTimeLineHtml(res.data);
			$("#" + pEl).append(timeLineHtml);
		} else {
			layer.alert(res.msg);
		}
	}, function (xhr, textStatus, errorThrown) {
		layer.alert('请求流转记录出错！', {
			icon: 2
		});
	});
}

var bpmViewer = null;
/**
 * 查询流程图节点
 */
function viewFlowChart(rowData, pEl) {
	var html = '<div class="canvas"  id="js-canvas">\
				</div>';
	$("#" + pEl).append(html);
	var procInstId = rowData["PROCESS_INSTANCE_ID"];
	var procDefId = rowData["PROC_DEF_ID_"];

	if (bpmViewer) {
		bpmViewer.destroy();
	}

	bpmViewer = new BpmnJS({
		container: document.querySelector('#js-canvas'),
		height: 650
	});

	/**
	 * 屏幕自适应
	 */
	function fitViewport() {
		bpmViewer.get('canvas').zoom("fit-viewport", "auto");
	}


	/**
	 * 设置高亮颜色
	 */
	function fillColor(nodeData) {
		var canvas = bpmViewer.get('canvas');
		var flowElements = bpmViewer.getDefinitions().rootElements[0].flowElements;
		flowElements.forEach(n => {
			var completeTask = nodeData.find(m => m.key === n.id);
			var todoTask = nodeData.find(m => !m.completed);
			var endTask = nodeData[nodeData.length - 1];
			if (n.$type === 'bpmn:StartEvent' && completeTask) {
				canvas.addMarker(n.id, 'highlight');
			} else if (n.$type === 'bpmn:EndEvent' && endTask.key === n.id && endTask.completed) {
				canvas.addMarker(n.id, 'highlight');
			} else if (n.$type === 'bpmn:UserTask' || n.$type === 'bpmn:ExclusiveGateway' || n.$type === 'bpmn:ParallelGateway') {
				if (completeTask) {
					canvas.addMarker(n.id, completeTask.completed ? 'highlight' : 'highlight-todo');
				}
			}
		});
	}


	twxAjax(THING, 'postFlw', {
		servlet: '/zero',
		params: {
			act: "flowXmlAndNode",
			procInstId: procInstId,
			procDefId: procDefId
		},
	}, true, function (res) {
		if (res.success) {
			var xmlData = res.data.xmlData;
			var nodeData = res.data.nodeData;
			bpmViewer.importXML(xmlData).then(function () {
				fitViewport();
				fillColor(nodeData);
			}).catch(function (err) {
				console.log(err);
			});

		} else {
			layer.alert(res.msg);
		}
	}, function (xhr, textStatus, errorThrown) {
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}