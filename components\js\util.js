//IE8兼容
window.HTMLElement = window.HTMLElement || Element;
//禁止选中复制
// document.oncontextmenu = function() {
// 	event.returnValue = false;
// };
// document.onselectstart = function() {
// 	event.returnValue = false;
// };

(function (global, factory) {
	typeof exports === "object" && typeof module !== "undefined" ? module.exports = factory(global) :
		typeof define === "function" && define.amd ? define(factory) : factory(global)
}((typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ?
	global : this), function (global) {
		global = global || {};
		var _Base64 = global.Base64;
		var version = "2.6.3";
		var b64chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
		var b64tab = function (bin) {
			var t = {};
			for (var i = 0, l = bin.length; i < l; i++) {
				t[bin.charAt(i)] = i
			}
			return t
		}(b64chars);
		var fromCharCode = String.fromCharCode;
		var cb_utob = function (c) {
			if (c.length < 2) {
				var cc = c.charCodeAt(0);
				return cc < 128 ? c : cc < 2048 ? (fromCharCode(192 | (cc >>> 6)) + fromCharCode(128 | (cc &
					63))) : (fromCharCode(224 | ((cc >>> 12) & 15)) + fromCharCode(128 | ((cc >>> 6) &
						63)) + fromCharCode(128 | (cc & 63)))
			} else {
				var cc = 65536 + (c.charCodeAt(0) - 55296) * 1024 + (c.charCodeAt(1) - 56320);
				return (fromCharCode(240 | ((cc >>> 18) & 7)) + fromCharCode(128 | ((cc >>> 12) & 63)) +
					fromCharCode(128 | ((cc >>> 6) & 63)) + fromCharCode(128 | (cc & 63)))
			}
		};
		var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
		var utob = function (u) {
			return u.replace(re_utob, cb_utob)
		};
		var cb_encode = function (ccc) {
			var padlen = [0, 2, 1][ccc.length % 3],
				ord = ccc.charCodeAt(0) << 16 | ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8) | ((ccc.length >
					2 ? ccc.charCodeAt(2) : 0)),
				chars = [b64chars.charAt(ord >>> 18), b64chars.charAt((ord >>> 12) & 63), padlen >= 2 ? "=" :
					b64chars.charAt((ord >>> 6) & 63), padlen >= 1 ? "=" : b64chars.charAt(ord & 63)
				];
			return chars.join("")
		};
		var btoa = global.btoa && typeof global.btoa == "function" ? function (b) {
			return global.btoa(b)
		} :
			function (b) {
				if (b.match(/[^\x00-\xFF]/)) {
					throw new RangeError("The string contains invalid characters.")
				}
				return b.replace(/[\s\S]{1,3}/g, cb_encode)
			};
		var _encode = function (u) {
			return btoa(utob(String(u)))
		};
		var mkUriSafe = function (b64) {
			return b64.replace(/[+\/]/g, function (m0) {
				return m0 == "+" ? "-" : "_"
			}).replace(/=/g, "")
		};
		var encode = function (u, urisafe) {
			return urisafe ? mkUriSafe(_encode(u)) : _encode(u)
		};
		var encodeURI = function (u) {
			return encode(u, true)
		};
		var fromUint8Array;
		if (global.Uint8Array) {
			fromUint8Array = function (a, urisafe) {
				var b64 = "";
				for (var i = 0, l = a.length; i < l; i += 3) {
					var a0 = a[i],
						a1 = a[i + 1],
						a2 = a[i + 2];
					var ord = a0 << 16 | a1 << 8 | a2;
					b64 += b64chars.charAt(ord >>> 18) + b64chars.charAt((ord >>> 12) & 63) + (typeof a1 !=
						"undefined" ? b64chars.charAt((ord >>> 6) & 63) : "=") + (typeof a2 != "undefined" ?
							b64chars.charAt(ord & 63) : "=")
				}
				return urisafe ? mkUriSafe(b64) : b64
			}
		}
		var re_btou = /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g;
		var cb_btou = function (cccc) {
			switch (cccc.length) {
				case 4:
					var cp = ((7 & cccc.charCodeAt(0)) << 18) | ((63 & cccc.charCodeAt(1)) << 12) | ((63 & cccc
						.charCodeAt(2)) << 6) | (63 & cccc.charCodeAt(3)),
						offset = cp - 65536;
					return (fromCharCode((offset >>> 10) + 55296) + fromCharCode((offset & 1023) + 56320));
				case 3:
					return fromCharCode(((15 & cccc.charCodeAt(0)) << 12) | ((63 & cccc.charCodeAt(1)) << 6) | (
						63 & cccc.charCodeAt(2)));
				default:
					return fromCharCode(((31 & cccc.charCodeAt(0)) << 6) | (63 & cccc.charCodeAt(1)))
			}
		};
		var btou = function (b) {
			return b.replace(re_btou, cb_btou)
		};
		var cb_decode = function (cccc) {
			var len = cccc.length,
				padlen = len % 4,
				n = (len > 0 ? b64tab[cccc.charAt(0)] << 18 : 0) | (len > 1 ? b64tab[cccc.charAt(1)] << 12 :
					0) | (len > 2 ? b64tab[cccc.charAt(2)] << 6 : 0) | (len > 3 ? b64tab[cccc.charAt(3)] : 0),
				chars = [fromCharCode(n >>> 16), fromCharCode((n >>> 8) & 255), fromCharCode(n & 255)];
			chars.length -= [0, 0, 2, 1][padlen];
			return chars.join("")
		};
		var _atob = global.atob && typeof global.atob == "function" ? function (a) {
			return global.atob(a)
		} :
			function (a) {
				return a.replace(/\S{1,4}/g, cb_decode)
			};
		var atob = function (a) {
			return _atob(String(a).replace(/[^A-Za-z0-9\+\/]/g, ""))
		};
		var _decode = function (a) {
			return btou(_atob(a))
		};
		var _fromURI = function (a) {
			return String(a).replace(/[-_]/g, function (m0) {
				return m0 == "-" ? "+" : "/"
			}).replace(/[^A-Za-z0-9\+\/]/g, "")
		};
		var decode = function (a) {
			return _decode(_fromURI(a))
		};
		var toUint8Array;
		if (global.Uint8Array) {
			toUint8Array = function (a) {
				return Uint8Array.from(atob(_fromURI(a)), function (c) {
					return c.charCodeAt(0)
				})
			}
		}
		var noConflict = function () {
			var Base64 = global.Base64;
			global.Base64 = _Base64;
			return Base64
		};
		global.Base64 = {
			VERSION: version,
			atob: atob,
			btoa: btoa,
			fromBase64: decode,
			toBase64: encode,
			utob: utob,
			encode: encode,
			encodeURI: encodeURI,
			btou: btou,
			decode: decode,
			noConflict: noConflict,
			fromUint8Array: fromUint8Array,
			toUint8Array: toUint8Array
		};
		if (typeof Object.defineProperty === "function") {
			var noEnum = function (v) {
				return {
					value: v,
					enumerable: false,
					writable: true,
					configurable: true
				}
			};
			global.Base64.extendString = function () {
				Object.defineProperty(String.prototype, "fromBase64", noEnum(function () {
					return decode(this)
				}));
				Object.defineProperty(String.prototype, "toBase64", noEnum(function (urisafe) {
					return encode(this, urisafe)
				}));
				Object.defineProperty(String.prototype, "toBase64URI", noEnum(function () {
					return encode(this, true)
				}))
			}
		}
		if (global["Meteor"]) {
			Base64 = global.Base64
		}
		if (typeof module !== "undefined" && module.exports) {
			module.exports.Base64 = global.Base64
		} else {
			if (typeof define === "function" && define.amd) {
				define([], function () {
					return global.Base64
				})
			}
		}
		return {
			Base64: global.Base64
		}
	}));


//IE8兼容 console
window.console = window.console || (function () {
	var c = {};
	c.log = c.warn = c.debug = c.info = c.error = c.time = c.dir = c.profile = c.clear = c.exception = c.trace =
		c.assert =
		function () { };
	return c;
})();

String.prototype.replaceAll = function (s1, s2) {
	return this.replace(new RegExp(s1, "gm"), s2);
}

//IE8兼容代码段foreach
if (!Array.prototype.forEach) {

	Array.prototype.forEach = function (callback) {

		var T, k;

		if (this == null) {
			throw new TypeError('this is null or not defined');
		}

		var O = Object(this);

		var len = O.length >>> 0;

		if (typeof callback !== 'function') {
			throw new TypeError(callback + ' is not a function');
		}

		if (arguments.length > 1) {
			T = arguments[1];
		}

		k = 0;

		while (k < len) {

			var kValue;
			if (k in O) {
				kValue = O[k];
				callback.call(T, kValue, k, O);
			}
			k++;
		}
	};
}

if (!Array.prototype.indexOf) {
	Array.prototype.indexOf = function (elt /*, from*/) {
		var len = this.length >>> 0;

		var from = Number(arguments[1]) || 0;
		from = (from < 0) ?
			Math.ceil(from) :
			Math.floor(from);
		if (from < 0)
			from += len;

		for (; from < len; from++) {
			if (from in this && this[from] === elt)
				return from;
		}
		return -1;
	};
}

function dealPrefix(sort, num) {
	if (!num) {
		num = 2;
	}
	var sortStr = sort + "";
	if (sortStr.length < num) {
		var temp = "";
		for (var i = 0; i < (num - sortStr.length); i++) {
			temp += "0";
		}
		sortStr = temp + sort;
	}
	return sortStr;
}

function twxlogout(cb_error, cb_success) {
	if (sessionStorage.getItem('twxsession') === true) {
		$.ajax({
			type: "POST",
			url: logoutApi,
			dataType: 'json',
			data: [],
			success: cb_success,
			error: cb_error
		});
	}
}

function twxAjax(ThingName, ServiceName, RequestData, async, cb_success, cb_error) {
	var opTime = new Date().getTime();
	var url = '';
	url = twxserver + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?" + serviceSuffix;
	$.ajax({
		type: "POST",
		url: url,
		async: async,
		dataType: 'json',
		data: JSON.stringify(RequestData),
		contentType: "application/json;charset=utf-8",
		success: function (data) {
			cb_success(data);
		},
		error: cb_error
	});
}

function browser() {
	return (navigator.userAgent.indexOf("Trident") > -1 || navigator.userAgent.indexOf("MSIE") > -1) ? "ie" : "Chrome";
}

function getUrl(ThingName, ServiceName, RequestData) {
	var url = '';
	if (RequestData == undefined) {
		RequestData = '';
	}
	url = twxserver + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?" + serviceSuffix + RequestData;
	return url;
}

function getTreeUrl(ThingName, ServiceName, params) {
	return twxserver + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?" + serviceSuffix + params;
}
//二维数组转换为字符串
function getStringFrom2DArray(objarr) {
	var typeNO = objarr.length;
	var tree = "[";
	for (var i = 0; i < typeNO; i++) {
		tree += "[";
		for (var j = 0; j < objarr[i].length; j++) {
			tree += "\\'" + objarr[i][j] + "\\'";
			if (j < objarr[i].length - 1) {
				tree += ",";
			}
		}

		tree += "]";
		if (i < typeNO - 1) {
			tree += ",";
		}
	}
	tree += "]";
	return tree;
}
//字符串转换为二维数组
function get2DArrayFromString(str) {
	var a = eval(str);
	return a;
}

function getQueryString(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
	var r = window.location.search.substr(1).match(reg);
	if (r != null) return decodeURI(r[2]);
	return null;
}

function getFormatDate(date, fmt) {
	//author: meizz
	var o = {
		"M+": date.getMonth() + 1, //月份
		"d+": date.getDate(), //日
		"h+": date.getHours(), //小时
		"m+": date.getMinutes(), //分
		"s+": date.getSeconds(), //秒
		"q+": Math.floor((date.getMonth() + 3) / 3), //季度
		"S": date.getMilliseconds() //毫秒
	};
	if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
	for (var k in o)
		if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : ((
			"00" + o[k])
			.substr(("" + o[k]).length)));
	return fmt;
}

/**
 * 获取数组的下标
 * @param arr
 * @param val
 * @returns {number}
 */
function indexOf(arr, val) {
	for (var i = 0; i < arr.length; i++) {
		if (arr[i] == val) {
			return i;
		}
	}
	return -1;
}
/**
 * 判断一个元素是否在一个数组中
 * @param arr
 * @param val
 * @returns {boolean}
 */
function contains(arr, val) {
	return indexOf(arr, val) != -1 ? true : false;
}

function handleFuncBtn() {
	var allfuns = sessionStorage.getItem('funcids');
	var funcArr = allfuns.split(',');
	$("[func]").each(function (i, e) {
		var func_id = $(e).attr('func');
		if (!contains(funcArr, func_id)) {
			$(e).remove();
		}
	})
}

//去除数组空元素
function trimSpace(array) {
	for (var i = 0; i < array.length; i++) {
		if (array[i] == " " || array[i] == null || typeof (array[i]) == "undefined" || array[i].length == 0) {
			array.splice(i, 1);
			i = i - 1;
		}
	}
	return array;
}

//以数组中的对象的某一个key值删除元素
function removeArray(key, value, array) {
	for (var i = 0; i < array.length; i++) {
		if (array[i][key] == value) {
			array.splice(i, 1);
			i = i - 1;
		}
	}
	return array;
}

//输入框宽度bug修复
function resizeInputWidth() {
	$(".textbox .textbox-text").each(function (i, n) {
		var width = $(n).width();
		$(n).width(width + 20)
	})
}

//Combobox宽度bug修复
function resizeComboboxWidth(evt) {
	var $box = $(evt).next().find('.textbox-text');
	var width = $box.width();
	$box.width(width + 20);
}

function getSecurityName(security) {
	var securityName = "";
	if (security == 0) {
		securityName = '公开';
	} else if (security == 1) {
		securityName = '内部';
	} else if (security == 2) {
		securityName = '秘密';
	} else if (security == 3) {
		securityName = '机密';
	}
	return securityName;
}

//获取下载链接
var getFileDownloadUrl = function (rec) {
	var path = rec.FILEPATH;
	if (path === undefined) {
		path = "";
	}
	path = path.replace(/\\/g, "/");
	var filename = rec.FILE_NAME;
	if (rec.SECURITY_LEVEL_NAME == undefined) {
		filename = filename + "(" + rec.SECURITY_LEVEL + ")";
	} else {
		filename = filename + "(" + rec.SECURITY_LEVEL_NAME + ")";
	}
	var url = fileHandlerUrl + "/file/download?filePath=" + encodeURIComponent(path) + "&fileName=" + encodeURIComponent(
		filename) +
		"." + rec.FILE_FORMAT;
	return url;
};

//获取预览链接
var getFilePreUrl = function (rec) {
	var path = rec.FILEPATH;
	if (path === undefined) {
		path = "";
	}
	path = path.replace(/\\/g, "/");

	var url = fileHandlerUrl + "/system/open/file?filePath=" + path + "&fileName=" + encodeURIComponent(
		"预览") +
		"." + rec.FILE_FORMAT;
	return url;
};

//行内下载
function downloadLocaleFile(rec) {
	if (rec.FILE_TYPE == '影像记录' && rec.GATHERING_METHOD == '自动采集') {
		var url = rec.FILEPATH;
		var flag = true;
		twxAjax("Thing.Fn.DataSearch", "GetPhotoIsExist", {
			url: url,
			appkey: twxAppKey
		}, false, function (data) {
			if (!data.success) {
				flag = false;
			}
		});
		if (flag) {
			url += "&fullfilename=" + rec.FILE_NAME + "." + rec.FILE_FORMAT;
			window.open(url);
		} else {
			layui.use(['layer'], function () {
				layui.layer.msg('服务器找不到该文件');
			});
		}
	} else {
		var url = getFileDownloadUrl(rec);
		window.open(url);
	}
};

var userSecLevel = sessionStorage.getItem('secLevel');
var userRolename = sessionStorage.getItem('rolename');
var currentUsername = sessionStorage.getItem('username');
//格式化表格中的空数据显示
var formatterEmptyValue = function (value, row, index) {
	if (value == undefined || value == '') {
		return '/';
	} else {
		return value;
	}
}

//格式化表格中的文件类别显示
var formatterFileType = function (value, row, index) {
	if (row.STATUS == 'new') {
		return '<span class="layui-badge">New</span>  ' + value;
	} else {
		return value;
	}
}

var getTableName = function (type) {
	var tableName = '';
	if (type === '设计类') {
		tableName = 'DESIGN_DATA_RESULT';
	} else if (type === '工艺类') {
		tableName = 'CRAFT_DATA_RESULT';
	} else if (type === '过程控制') {
		tableName = 'PROCESS_CONTROL_RESULT';
	} else if (type === '质量综合') {
		tableName = 'QUALITY_CONTROL_RESULT';
	}
	return tableName;
}

var getTableType = function (name) {
	var typeName = '';
	if (name === 'DESIGN_DATA_RESULT') {
		typeName = '设计类';
	} else if (name === 'CRAFT_DATA_RESULT') {
		typeName = '工艺类';
	} else if (name === 'PROCESS_CONTROL_RESULT') {
		typeName = '过程控制';
	} else if (name === 'QUALITY_CONTROL_RESULT') {
		typeName = '质量综合';
	}
	return typeName;
}

function previewList(id, tableName, filePath, fileName, fileFormat, fileType, sourceSystem, gatheringMethod) {
	if (sourceSystem == 'PDM') {
		showPDMFile({
			ID: id,
			TABLE_NAME: tableName,
			FILEPATH: filePath,
			FILE_NAME: fileName,
			FILE_FORMAT: fileFormat,
			FILE_TYPE: fileType,
			GATHERING_METHOD: gatheringMethod
		});
	} else {
		previewfile({
			FILEPATH: filePath,
			FILE_NAME: fileName,
			FILE_FORMAT: fileFormat,
			FILE_TYPE: fileType,
			GATHERING_METHOD: gatheringMethod
		});
	}
}


//行内预览功能
var previewfile = function (rec) {
	var url = '';
	var previewURL = "";
	if (rec.FILE_FORMAT === 'PDF' || rec.FILE_FORMAT === 'pdf' || rec.FILE_FORMAT === 'docx') {
		previewURL = '/File' + rec.FILEPATH;
		if (rec.FILE_FORMAT === 'docx') {
			sessionStorage.setItem('docxurl', previewURL);
			previewURL = '/DataPackageManagement/components/preview/docxjs-master/index.html';
		}
	} else {
		if (rec.FILE_TYPE == '影像记录' && rec.GATHERING_METHOD == '自动采集') {
			twxAjax("Thing.Integration.DataCollect", "downloadPhoto", {
				url: rec.FILEPATH
			}, false, function (data) {
				rec.FILEPATH = data.filePath;
			});
		}

		url = getFilePreUrl(rec);
		url += "&fullfilename=" + encodeURIComponent(rec.FILE_NAME) + "." + rec.FILE_FORMAT + "&browser=" +
			browser();
		url = encodeURIComponent(url);
		previewURL = sessionStorage.getItem('previewUrl') + '/onlinePreview?url=' + url;
	}
	var urlKey = parseInt(new Date().getTime() * Math.random());
	sessionStorage.setItem(urlKey, previewURL);
	window.open("/DataPackageManagement/components/preview/preview.html?key=" + urlKey);
};

function pdfsee(uul) {
	layui.use('layer', function () {
		var layer = layui.layer;
		layer.open({
			type: 2,
			area: ['1000px', '450px'],
			fixed: false, //不固定
			maxmin: true,
			content: uul
		});
	});
};

//格式化操作按钮一栏
var opFomatter = function (value, rec) {
	var rowData = JSON.stringify(rec).replace(/"/g, "\'");
	//判断一下文件类别
	var filetype = rec.FILE_TYPE;
	var fileName = rec.FILE_NAME;
	var html = '';
	//此处需要判断文件形式是否统计报告
	var fileformat = rec.FILE_FORMAT;
	if (rec.PARAM) {
		html += '<button type="button" class="layui-btn layui-btn-xs" onclick="showParam(' + rowData +
			')">属性</button>';
	}
	if (rec.SOURCE_SYSTEM == 'PDM') {

		html += '<button type="button" class="layui-btn layui-btn-xs" onclick="showPDMFile(' + rowData +
			')">查看</button>';
		// if (rec.DOC_INFO) {
		// 	html += '<button type="button" class="layui-btn layui-btn-xs" onclick="showPDMFile(\'' + rec.DOC_INFO + '\')">更改单</button>';
		// }
		var path = rec.FILEPATH || "";

		if (path.indexOf('//2') == 0) {
			html +=
				'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="downloadLocaleFile(' +
				rowData + ')">下载</button>';
		}

		return html;
	}
	if (rec.FILEPATH != undefined) {
		if (filetype.indexOf('电子元器件汇总表') > -1 || filetype === '电缆导通绝缘测试汇总表' ||
			filetype === '加热片入所复验' || filetype === '加热回路测试') {
			html +=
				'<button type="button" class="layui-btn layui-btn-xs" onclick="showExcelData(' +
				rowData + ')">查看</button>';
		} else if (filetype == '全景影像' && (fileformat == 'zip' || fileformat == 'rar')) {
			html +=
				'<button type="button" class="layui-btn layui-btn-xs" onclick="show360Img(' +
				rowData + ')">查看</button>';
		} else {
			html += '<button type="button" class="layui-btn layui-btn-xs" onclick="previewfile(' + rowData +
				')">查看</button>';
		}
		html +=
			'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="downloadLocaleFile(' +
			rowData + ')">下载</button>';
		if (filetype == '通用跟踪卡' && rec.GATHERING_METHOD == '自动采集') {
			html +=
				'<br><button type="button" style="margin-top:5px" class="layui-btn layui-btn-xs" onclick="viewAddendum(' +
				rowData + ')">查看附表</button>';
		}
		if ((filetype == '现场问题处理单' || filetype == '产品交接单') && rec.GATHERING_METHOD == '自动采集') {
			html +=
				'<button type="button" class="layui-btn layui-btn-xs layui-btn-warm" onclick="viewAttachment(' +
				rowData +
				')">附件</button>';
		}
	} else {
		//'结构装配检测表','单机装星情况','接插件连接固封情况','接插件插拔次数统计','星上加热器','热敏电阻实施','正样多层装配情况'
		if (filetype === '质量统计报表') {
			if (fileName === '结构装配检测表' || fileName === '单机装星情况' ||
				fileName === '接插件连接固封情况' || fileName === '接插件插拔次数统计' ||
				fileName === '星上加热器' || fileName === '热敏电阻实施' || fileName === '正样多层装配情况') {
				html +=
					'<button type="button" class="layui-btn layui-btn-xs" onclick="showStaticticsReport(' +
					rowData + ')">查看</button>';
				html +=
					'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="downloadStaticticsReport(' +
					rowData + ')">下载</button>';
			}
		} else {
			if (filetype == '领料记录' && rec.GATHERING_METHOD == '自动采集') {
				html +=
					'<button type="button" class="layui-btn layui-btn-xs" onclick="viewMateriel(' +
					rowData + ')">查看物料</button>';
			} else if ((filetype == '现场问题处理单' || filetype == '产品交接单') && rec.GATHERING_METHOD == '自动采集') {
				html +=
					'<button type="button" class="layui-btn layui-btn-xs layui-btn-warm" onclick="viewAttachment(' +
					rowData +
					')">附件</button>';
			} else {
				html = '<font color="red">无实体文件</font>';
			}
		}
	}

	if (filetype == '跟踪卡' && rec.GATHERING_METHOD == '自动采集') {
		html +=
			'<br><button type="button" style="margin-top:5px" class="layui-btn layui-btn-xs layui-btn-warm" onclick="viewRecord(' +
			rowData + ')">查看记录表</button>';
	}
	if (filetype == '现场问题处理单') {
		html +=
			'<br><button type="button" class="layui-btn layui-btn-xs" style="margin-top:5px;background-color:#3569FE" onclick="viewLinkData(' +
			rowData +
			')">查看更改单</button>';
	}
	if (rec.IS_FOLDER == "1") {
		html = '<button type="button" class="layui-btn layui-btn-xs" onclick="viewFolder(' +
			rowData + ')">查看合集</button>';
	}
	return html;
}

var viewLinkData = function (rec) {
	var srcId = rec.ID;
	var srcTableName = rec.TABLE_NAME;
	twxAjax('Thing.Fn.ListData', 'QueryLinkListData', {
		srcId: srcId,
		srcTableName: srcTableName
	}, true, function (res) {
		if (res.success) {
			var datas = res.data;
			if (datas.length > 0) {
				layer.open({
					title: '查看关联更改单',
					type: 1,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					shadeClose: false,
					maxmin: false,
					resize: false,
					area: ['1500px', '700px'],
					content: '<div id="viewLinkTable"></div>',
					success: function () {
						listTable = new ListDataGrid({
							eleId: 'viewLinkTable',
							tableHeight: 657,
							colType: 'all',
							toolbar: '',
							searchParams: {},
							pageSize: 10,
							isCheck: false,
							initDatas: datas
						});
					}
				});
			} else {
				layer.alert('未发现关联的更改单！', {
					icon: 2
				});
			}
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	}, function (xhr, textStatus, errorThrown) {
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}

//查看影像记录信息
var viewRecord = function (rec) {
	layui.use(['layer'], function () {
		var layer = layui.layer;
		var techcardId = rec.ID;
		var tableName = rec.TABLE_NAME;
		var url = "/DataPackageManagement/components/excelDataView/record.html"
		window.record = {
			techcardId: techcardId,
			tableName: tableName
		}

		var index = layer.open({
			title: '查看记录表',
			type: 2,
			area: ['1500px', '700px'],
			content: [url, 'no'],
			anim: false,
			// maxmin: true,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
	});
}


//查看合集信息
var viewFolder = function (rec) {
	var pageType = window.location.href.indexOf('captureManagement') > -1 ? 1 : 0;
	layui.use(['layer'], function () {
		var layer = layui.layer;
		var query = JSON.parse(rec.QUERY_PARAMS.replace(/'/g, "\""));
		query.fileType = rec.FILE_TYPE;
		query.isMergeFolder = '0';
		var queryType = query.queryType || "";
		var dataTableName = "";
		var typeName = "";
		if (queryType === 'design') {
			dataTableName = 'DESIGN_DATA_RESULT';
			typeName = "设计类";
		} else if (queryType === 'craft') {
			dataTableName = 'CRAFT_DATA_RESULT';
			typeName = "工艺类";
		} else if (queryType === 'process') {
			dataTableName = 'PROCESS_CONTROL_RESULT';
			typeName = "过程控制";
		} else if (queryType === 'quality') {
			dataTableName = 'QUALITY_CONTROL_RESULT';
			typeName = "质量综合";
		}
		var datapkg = {};
		if (pageType == 1) {
			datapkg = $('#datapkgref').datagrid('getSelections')[0];
		}
		window.listDataInfo = {
			listDataQuery: query,
			dataTableName: dataTableName,
			datapkg: datapkg,
			typeName: typeName,
			pageType: pageType
		};
		var url = "/DataPackageManagement/components/excelDataView/listData.html"
		var index = layer.open({
			title: '查看合集',
			type: 2,
			area: ['1600px', '810px'],
			content: [url, 'no'],
			anim: false,
			// maxmin: true,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
	});
}

//查看影像记录信息
var viewPhoto = function (rec) {
	layui.use(['layer'], function () {
		var layer = layui.layer;
		var nodecode = rec.NODECODE;
		var secLevel = rec.SECURITY_LEVEL;
		var tableName = rec.TYPE;
		var pageType = rec.RR; // 页面类型 1 表示 采集管理页面 2 表示过程结构树查询
		var url = "";
		if (pageType == '1') {
			var datapkg = $('#datapkgref').datagrid('getSelections')[0];
			window.cphoto = { //采集管理查询
				datapkgid: nodecode,
				secLevel: secLevel,
				tableName: tableName,
				datapkg: datapkg
			}
			url = "/DataPackageManagement/components/excelDataView/cphoto.html"
		} else if (pageType == '2') {
			window.gphoto = { //过程结构树查询
				treeId: nodecode,
				type: tableName,
				secLevel: secLevel
			}
			url = "/DataPackageManagement/components/excelDataView/gphoto.html"
		}

		var index = layer.open({
			title: '查看合集',
			type: 2,
			area: ['1500px', '700px'],
			content: [url, 'no'],
			anim: false,
			// maxmin: true,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
	});
}

//查看附表信息
var viewAddendum = function (rec) {
	layui.use(['layer', 'form'], function () {
		var layer = layui.layer;
		var filename = rec.FILE_NAME;
		var type = rec.TYPE;
		var tableName = '';
		if (type == '设计类') {
			tableName = 'DESIGN_DATA_RESULT';
		} else if (type == '工艺类') {
			tableName = 'CRAFT_DATA_RESULT';
		} else if (type == '过程控制') {
			tableName = 'PROCESS_CONTROL_RESULT';
		} else if (type == '质量综合') {
			tableName = 'QUALITY_CONTROL_RESULT';
		}
		window.addendumTableType = tableName;
		window.addendumResultId = rec.ID;
		var url = "/DataPackageManagement/components/excelDataView/addendum.html";
		var index = layer.open({
			title: filename + '-附表信息',
			type: 2,
			area: ['800px', '600px'],
			content: [url, 'no'],
			anim: false,
			// maxmin: true,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
	});
}

//查看附件信息
var viewAttachment = function (rec) {
	layui.use(['layer', 'form'], function () {
		var layer = layui.layer;
		var filename = rec.FILE_NAME;
		var type = rec.TYPE;
		var tableName = '';
		if (type == '设计类') {
			tableName = 'DESIGN_DATA_RESULT';
		} else if (type == '工艺类') {
			tableName = 'CRAFT_DATA_RESULT';
		} else if (type == '过程控制') {
			tableName = 'PROCESS_CONTROL_RESULT';
		} else if (type == '质量综合') {
			tableName = 'QUALITY_CONTROL_RESULT';
		}
		window.attachmentTableType = tableName;
		window.attachmentResultId = rec.ID;
		var url = "/DataPackageManagement/components/excelDataView/attachment.html";
		var index = layer.open({
			title: filename + '-附件信息',
			type: 2,
			area: ['800px', '600px'],
			content: [url, 'no'],
			anim: false,
			// maxmin: true,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
	});
}

//查看物料信息
var viewMateriel = function (rec) {
	layui.use(['layer', 'form'], function () {
		var layer = layui.layer;
		var filename = rec.FILE_NAME;
		window.materielCode = filename;
		var url = "/DataPackageManagement/components/excelDataView/materiel.html";
		var index = layer.open({
			title: filename + '-物料信息',
			type: 2,
			area: ['1200px', '600px'],
			content: [url, 'no'],
			anim: false,
			// maxmin: true,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
	});
}

//查看属性
var showParam = function (rec) {
	var param = rec.PARAM;
	layui.use(['layer', 'form'], function () {
		var layer = layui.layer;
		if (param) {
			layer.alert(param, {
				title: "查看属性"
			});
		} else {
			layer.msg('暂未编辑属性！');
		}
	})

}

//查看PDM文件 
var showPDMFile = function (rec) {
	var path = rec.FILEPATH || "";

	if (path.indexOf('//2') == 0) {
		previewfile(rec);
	} else {
		twxAjax('Thing.Fn.ListData', 'GetPDMPreviewUrl', {
			id: rec.ID,
			tableName: rec.TABLE_NAME
		}, true, function (res) {
			if (res.success) {
				var fileData = res.data;
				previewfile({
					FILEPATH: fileData.filePath,
					FILE_NAME: fileData.fileName,
					FILE_FORMAT: fileData.fileFormat,
					FILE_TYPE: "",
					GATHERING_METHOD: ""
				});
			} else {
				window.open(path);
			}
		}, function (xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	}
}

//查看360全景影像
var show360Img = function (rec) {
	var path = rec.FILEPATH;
	show360ImgByPath(path);
}

var show360ImgByPath = function (path) {
	path = path.replace(/\\/g, "/");
	path = encodeURIComponent(path);
	var port = window.location.port;
	var load = layui.layer.load();
	$.ajax({
		type: "POST",
		url: fileHandlerUrl + "/list/get/360/path?path=" + path,
		async: true,
		contentType: "application/x-www-form-urlencoded; charset=utf-8",
		success: function (res) {
			layui.layer.close(load);
			if (res.success) {
				window.open("/File" + res.path);
			} else {
				layui.layer.alert(res.msg, {
					icon: 2
				});
			}
		}
	});
}

//查看excel结构化数据
var showExcelData = function (rec) {
	layui.use(['layer', 'form'], function () {
		var layer = layui.layer;
		var filetype = rec.FILE_TYPE;
		var type = rec.TYPE;
		var id = rec.ID;
		var tableName = '';
		if (type == '设计类') {
			tableName = 'DESIGN_DATA_RESULT';
		} else if (type == '工艺类') {
			tableName = 'CRAFT_DATA_RESULT';
		} else if (type == '过程控制') {
			tableName = 'PROCESS_CONTROL_RESULT';
		} else if (type == '质量综合') {
			tableName = 'QUALITY_CONTROL_RESULT';
		}
		window.excelPid = tableName + '_' + id;
		var url = "";
		if (filetype.indexOf('电子元器件汇总表') > -1) {
			url = '/DataPackageManagement/components/excelDataView/electronicComponents.html';
		} else if (filetype === '电缆导通绝缘测试汇总表') {
			url = '/DataPackageManagement/components/excelDataView/cableTest.html';
		} else if (filetype === '加热片入所复验') {
			url = '/DataPackageManagement/components/excelDataView/heatingReinspection.html';
		} else if (filetype === '加热回路测试') {
			url = '/DataPackageManagement/components/excelDataView/heatingTest.html';
		}

		if (url === '') {
			layer.alert('未定义该类型,请联系管理员', {
				icon: 2
			})
			return;
		}

		var index = layer.open({
			title: filetype,
			type: 2,
			area: ['1200px', '600px'],
			content: [url, 'no'],
			anim: false,
			// maxmin: true,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
		// layer.full(index);
	});
};

//下载统计分析报告
var downloadStaticticsReport = function (rec) {
	layui.use(['layer', 'form'], function () {
		// var layer = layui.layer;
		var rsId = rec.ID;
		var type = rec.FILE_NAME;

		var url = fileHandlerUrl + "/first/phase/export/excel";
		var form = $("<form></form>").attr("action", url).attr("method", "post");
		form.append($("<input></input>").attr("type", "hidden").attr("name", "rsId").attr("value", rsId));
		form.append($("<input></input>").attr("type", "hidden").attr("name", "type").attr("value", type));
		form.appendTo('body').submit().remove();
	});
};

//查看统计分析报告
var showStaticticsReport = function (rec) {
	layui.use(['layer', 'form'], function () {
		var layer = layui.layer;

		var dpid = rec.NODECODE;
		var filetype = rec.FILE_NAME;

		var url = "";
		if (filetype === '单机装星情况') {
			url = '/DataPackageManagement/components/staticticsReport/standAlong.html';
		} else if (filetype === '正样多层装配情况') {
			url = '/DataPackageManagement/components/staticticsReport/layersonoff.html';
		} else if (filetype === '热敏电阻实施') {
			url = '/DataPackageManagement/components/staticticsReport/heatresist.html';
		} else if (filetype === '星上加热器') {
			url = '/DataPackageManagement/components/staticticsReport/heater.html';
		} else if (filetype === '接插件插拔次数统计') {
			url = '/DataPackageManagement/components/staticticsReport/connectoronofftimes.html';
		} else if (filetype === '接插件连接固封情况') {
			url = '/DataPackageManagement/components/staticticsReport/connectoronoff.html';
		}

		if (url === '') {
			layer.alert('未定义该类型,请联系管理员', {
				icon: 2
			})
			return;
		}
		window.tjfxRsID = rec.ID;
		var index = layer.open({
			title: filetype,
			type: 2,
			area: ['1200px', '600px'],
			content: [url, 'no'],
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200
		});
		layer.iframeAuto(index);
	});
};

//大小写、字符、数字，三选二 长度8位以上
var checkPwd = function (pwd) {
	var flag = true;
	var pwdRegex1 = new RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z]).{8,30}');
	var pwdRegex2 = new RegExp('(?=.*[0-9])(?=.*[^a-zA-Z0-9]).{8,30}');
	var pwdRegex3 = new RegExp('(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,30}');

	if (!(pwdRegex1.test(pwd) || pwdRegex2.test(pwd) || pwdRegex3.test(pwd))) {
		flag = false;
	}
	return flag;
}


if ($.fn.datagrid) {
	$.extend($.fn.datagrid.methods, {
		lockColumn: function (jq, field) {
			return jq.each(function () {
				var p = $(this).datagrid('getPanel'); // 获取数据表格面板
				var cell = p.find('div.datagrid-header td[field=' + field +
					'] > div.datagrid-cell'); // 获取数据表格监听改变列宽事件的节点
				cell.resizable({
					disabled: true
				}); // 禁止改变列宽
			});
		},
		unlockColumn: function (jq, field) {
			return jq.each(function () {
				var p = $(this).datagrid('getPanel'); // 获取数据表格面板
				var cell = p.find('div.datagrid-header td[field=' + field +
					'] > div.datagrid-cell'); // 获取数据表格监听改变列宽事件的节点
				cell.resizable({
					disabled: false
				}); // 允许改变列宽
			});
		}
	});
}
//表格自适应方法
function changeWidth(agstr) {
	var dg = $('#' + agstr);
	//表格撑满屏幕的宽度
	var maxWidth = dg.parent().find('.datagrid-view2').width() - 18;
	// dg.datagrid("loading"); //显示加载状态$$$
	var fn = function () {
		var opts = dg.datagrid('getColumnFields'); //获取表头所有field
		var data = dg.datagrid('getData'); //获取数据表格请求的数据
		var role = data.rows; //数据表格请求的数据,即每行的数据
		var updateColObj = {};
		for (var i = 0; i < opts.length; i++) { //循环每一列的数据内容
			var field = opts[i];
			var ro_width = 0;
			if (field != '' && field != 'ROWNUM' && field != 'ck' && field != 'op') {
				var col = dg.datagrid('getColumnOption', field);
				if (col.hidden) {
					continue;
				}
				var col_title = col.title;
				for (j = 0; j < role.length; j++) {
					var cellValue = String(role[j][field]) || "";
					if (cellValue.indexOf('{"images"') > -1) {
						continue;
					}
					var valWidth = 0;
					// 处理包含dataSeparator的情况
					if (cellValue.indexOf("!@#$%^") > -1) {
						// 分割文本和照片
						var parts = cellValue.split("!@#$%^");
						// 第一部分是文字
						var text = parts[0];
						// 统计照片数量
						var photoCount = parts.length - 1;
						// 计算宽度：文字宽度 + "x张照片"的宽度 + 10
						valWidth = StringTolog(text) + StringTolog(photoCount + "张照片");
					} else {
						valWidth = StringTolog(cellValue);
						if (valWidth > 20) {
							if (col.formatter) {
								if (col.formatter.toString().indexOf('StringTolog') > -1) {
									valWidth = StringTolog(cellValue.substring(0, 20) + "...");
								}
							}
						}
					}
					if (valWidth > ro_width) {
						ro_width = valWidth; //比较当前field列的每条数据长度，取最大值
					}
				}
				var titleWidth = 0;
				if (col_title) {
					if (col_title.indexOf('<br>') > -1) {
						var titles = col_title.split('<br>');
						for (var x = 0; x < titles.length; x++) {
							var w1 = StringTolog(titles[x]);
							if (w1 > titleWidth) {
								titleWidth = w1;
							}
						}
					} else {
						titleWidth = StringTolog(col_title);
					}
				}
				if (ro_width < titleWidth) { //如果当前列数据长度小于当前列表头长度则取表头长度
					ro_width = titleWidth;
				}
				var ro_length = ro_width * 14 + 10; //14是页面字体像素大小 10是单元格左右内边距大小

				updateColObj[field] = ro_length;
			}
			// if (field == 'op') {
			// 	updateColObj[field] = 150;
			// }
		}
		var nowWidth = 0;
		for (var i = 0; i < opts.length; i++) {
			var field = opts[i];
			var col = dg.datagrid('getColumnOption', field);
			if (col.hidden) {
				continue;
			}
			var col_width = col.width;
			if (field == 'ck') {
				col_width = 28;
			}
			if (typeof col_width == 'string') {
				col_width = Number(col_width.split('px')[0]);
			}
			if (updateColObj[field]) {
				col_width = updateColObj[field];
			}
			nowWidth += col_width;
		}
		var diff = maxWidth - nowWidth;
		if (diff > 0) {
			var updateColNum = Object.keys(updateColObj).length;
			var avg = diff / updateColNum;
			for (var key in updateColObj) {
				var ro_length = updateColObj[key] + avg + 20;
				dg.parent().find("td[field='" + key + "'] div").css("width", ro_length - 1 + "px"); //设置列宽样式
				dg.datagrid('lockColumn', key); //禁止数据表格改变列宽※※※
			}
		} else {
			for (var key in updateColObj) {
				var ro_length = updateColObj[key] + 20;
				dg.parent().find("td[field='" + key + "'] div").css("width", ro_length - 1 + "px"); //设置列宽样式
				dg.datagrid('lockColumn', key); //禁止数据表格改变列宽※※※
			}
		}
		// dg.datagrid("loaded"); //隐藏加载状态$$$
	}
	setTimeout(fn, 0);
}

function changeWidth1(agstr) {
	var dg = $('#' + agstr);
	// dg.datagrid("loading"); //显示加载状态$$$
	var fn = function () {
		var opts = dg.datagrid('getColumnFields'); //获取表头所有field
		var data = dg.datagrid('getData'); //获取数据表格请求的数据
		var role = data.rows; //数据表格请求的数据,即每行的数据
		for (var i = 0; i < opts.length; i++) { //循环每一列的数据内容
			var field = opts[i];
			var ro_width = 0;
			if (field != '' && field != 'ROWNUM' && field != 'ck' && field != 'op') {
				var col = dg.datagrid('getColumnOption', field);
				var col_title = col.title;
				for (j = 0; j < role.length; j++) {
					var valWidth = StringTolog(role[j][field]);
					if (valWidth > 20) {
						valWidth = StringTolog(role[j][field].substring(0, 20) + "...");
					}
					if (valWidth > ro_width) {
						ro_width = valWidth; //比较当前field列的每条数据长度，取最大值
					}
				}
				var titleWidth = 0;
				if (col_title) {
					if (col_title.indexOf('<br>') > -1) {
						var titles = col_title.split('<br>');
						for (var x = 0; x < titles.length; x++) {
							var w1 = StringTolog(titles[x]);
							if (w1 > titleWidth) {
								titleWidth = w1;
							}
						}
					} else {
						titleWidth = StringTolog(col_title);
					}
				}
				if (ro_width < titleWidth) { //如果当前列数据长度小于当前列表头长度则取表头长度
					ro_width = titleWidth;
				}
				var ro_length = ro_width * 14 + 10; //14是页面字体像素大小 10是单元格左右内边距大小
				$("td[field='" + field + "'] div").width(ro_length); //设置列宽样式
				dg.datagrid('lockColumn', field); //禁止数据表格改变列宽※※※
			}
		}
		// dg.datagrid("loaded"); //隐藏加载状态$$$
	}
	setTimeout(fn, 0);
}

//字符串的粗略换算
function StringTolog(Str) {
	if (Str == null) {
		return 0;
	}
	Str = Str.toString(); //该方法将取到的数据转为String类型
	Str = Str.replace(/\s+/g, ''); //替换空格
	//两个字节为长度1，一个字节为长度0.5，计算字符串总长度
	var newStr = (Str.length - Str.replace(/[\x00-\xff]+/g, '').length) / 2 +
		Str.replace(/[\x00-\xff]+/g, '').length;
	return newStr;
}


var bomTreeUtil = {
	dealDataIcons: function (datas) {
		var imagePrefix = '/DataPackageManagement/components/dataTree/';
		for (var i = 0; i < datas.length; i++) {
			var dtype = datas[i].NODE_TYPE;
			if (dtype === 'root') {
				datas[i].drag = false;
				datas[i].icon = imagePrefix + "images/root.png";
			} else if (dtype === 'folder') {
				datas[i].drag = false;
				datas[i].dropInner = false;
				datas[i].childOuter = false;
				datas[i].icon = imagePrefix + "images/folder.png";
			} else if (dtype === 'product') {
				datas[i].drag = false;
				datas[i].icon = imagePrefix + "images/卫星.png";
				datas[i].childOuter = false;
				datas[i].dropInner = true;
			} else if (dtype === 'level2') {
				datas[i].childOuter = false;
				datas[i].dropInner = true;
				datas[i].icon = imagePrefix + "images/dir.png";
			} else if (dtype === 'level3') {
				datas[i].childOuter = false;
				datas[i].dropInner = true;
				datas[i].icon = imagePrefix + "images/dir.png";
			} else if (dtype === 'level4') {
				datas[i].childOuter = false;
				datas[i].dropInner = true;
				datas[i].icon = imagePrefix + "images/dir.png";
			} else if (dtype === 'level5') {
				datas[i].childOuter = false;
				datas[i].dropInner = false;
				datas[i].icon = imagePrefix + "images/dir.png";
			}
			if (dtype != 'root' && dtype != 'product' && dtype != 'folder') {
				datas[i].NAME = dealPrefix(datas[i].SORT) + "-" + datas[i].NAME;
			}

		}
		return datas;
	},
	dealDataCheck: function (datas) {
		for (var i = 0; i < datas.length; i++) {
			var dtype = datas[i].NODE_TYPE;
			if (dtype === 'level4' || dtype === 'level5') {
				datas[i].nocheck = false;
			} else {
				datas[i].nocheck = true;
			}
		}
		return datas;
	},
	treeSetting: {
		view: {
			dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
			showLine: true, //是否显示节点之间的连线
			fontCss: {
				'color': 'black'
			}, //字体样式函数
			selectedMulti: false, //设置是否允许同时选中多个节点,
			txtSelectedEnable: true,
			showTitle: true
		},
		async: {
			enable: true,
			url: getTreeUrl("Thing.Fn.BOM", "QueryProductTreeById", ""),
			type: "post",
			autoParam: ["ID"],
			contentType: "application/json;charset=utf-8",
			dataType: 'json',
			dataFilter: function (treeId, parentNode, responseData) {
				if (responseData.success) {
					var datas = responseData.data;
					if (datas.length > 0) {
						datas = bomTreeUtil.dealDataIcons(datas);
						datas = bomTreeUtil.dealDataCheck(datas);
					}
					return datas;
				} else {
					layer.alert(responseData.msg, {
						icon: 2
					});
				}
			}
		},
		check: {
			chkboxType: {
				"Y": "",
				"N": ""
			},
			chkStyle: "checkbox", //复选框类型
			enable: false //每个节点上是否显示 CheckBox
		},
		edit: {
			enable: false
		},
		data: {
			simpleData: { //简单数据模式
				enable: true,
				idKey: "ID",
				pIdKey: "PID",
				rootPId: -1
			},
			key: {
				name: 'NAME',
				title: 'NAME',
				isParent: "ISPARENT"
			}
		},
		callback: {}
	}
};

/**
 * 查看合格证
 * @param {Object} tableConfigId
 * @param {Object} dataId
 */
function viewCertificate(tableConfigId, dataId) {
	var loading = layer.load(1);
	twxAjax("Thing.Fn.Certificate", 'QueryCertificatePath', {
		tableConfigId: tableConfigId,
		dataId: dataId,
		username: sessionStorage.getItem('username')
	}, true, function (res) {
		layer.close(loading);
		if (res.success) {
			var filePath = res.data;
			previewfile({
				FILEPATH: '//' + filePath,
				FILE_NAME: '合格证',
				FILE_FORMAT: 'pdf',
				FILE_TYPE: "",
				GATHERING_METHOD: ""
			});
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	}, function (xhr, textStatus, errorThrown) {
		layer.close(loading);
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}

/**
 * 加载二级表中的图片上传按钮
 */
function renderTableImageUpload() {
	$(".table-image").each(function (i, n) {
		var menu = [{
			text: "上传图片",
			icon: '../dataTree/images/upload.png',
			callback: function () {
				uploadTableImage($(n).attr("tableName"), $(n).parent().parent().attr("field"), $(n)
					.attr("dataId"));
			}
		}];
		if ($(n).attr("type") == 'img') {
			var src = $(n).attr("src");
			menu.push({
				text: "查看图片",
				icon: '../dataTree/images/view.png',
				callback: function () {
					viewTableImage(src);
				}
			});
		}
		$(n).parent().contextMenu({
			width: 155,
			menu: menu,
			target: function (ele) {

			}
		});
	});
}

function uploadTableImage(tableName, colName, dataId) {
	var fileFlag = false;
	layer.open({
		title: "上传图片",
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['350px', '320px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要上传的图片!', {
					icon: 2
				});
				return false;
			}
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = `<form class="layui-form" lay-filter="uploadForm">
								<div class="layui-form-item">
									<label class="param-lable layui-form-label">文件内容:</label>
									<div class="layui-input-block">
										<div class="layui-upload">
											<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
											<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
										</div>
									</div>
								</div>
								<div class="layui-form-item" id="selectedFile" style="display: none;">
									<label class="param-lable layui-form-label">已选文件:</label>
									<div class="layui-input-block">
										<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
									</div>
								</div>
								<div class="layui-form-item" id="previewFile" style="display: none;">
									<label class="fieldlabel layui-form-label">图片预览:</label>
									<div class="layui-input-block">
										<img style="max-width: 130px;max-height: 56px;" id="previewImg" />
									</div>
								</div>
								<div class="layui-form-item" style="display:none;">
									<center>
										<button id="btn_ok" class="layui-btn" lay-submit>确认</button>
										<button id="btn_cancel" class="layui-btn">取消</button>
									</center>
								</div>
							</form>`;
			$("#uploadContent").append(addTpl);
		}
	});

	form.render(null, 'uploadForm');

	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + '/file/upload',
		auto: false,
		accept: 'images',
		field: 'file',
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;

			var files = obj.pushFile();
			obj.preview(function (index, file, result) {
				$("#selectedFile").show();
				$("#selectedFileName").text(file.name);
				$("#previewFile").show();
				$("#previewImg").attr("src", result);
			});

		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			// layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			twxAjax("Thing.Fn.SecondTable", 'UploadTableImage', {
				tableName: tableName,
				filePath: res.data.filePath,
				fileFormat: res.data.fileFormat,
				fileName: res.data.fileName,
				colName: colName,
				dataId: dataId,
				username: sessionStorage.getItem('username')
			}, true, function (res) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					window.secondTable.queryDataByPage(window.secondTable.pageOptions.pageSize,
						window.secondTable.pageOptions.pageNumber);
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}, function (xhr, textStatus, errorThrown) {
				layer.alert('请求出错！', {
					icon: 2
				});
			});
		}
	});
	if (device.ie && device.ie < 10) {
		$("input[name='uploadFile']").change(function () {
			var filename = $(this).val();
			filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		});
	}
}

/**
 * 查看表格中的图片
 * @param {Object} src
 */
function viewTableImage(src) {
	layer.photos({
		photos: {
			"title": "查看图片",
			"start": 0,
			"data": [{
				"alt": "",
				"pid": 1,
				"src": src
			}]
		},
		toolbar: true,
		footer: true
	});
}

//格式化二级表的显示列
/**
 * @param {Object} col
 * @param {Object} hasCertificate 是否显示合格证的按钮
 */
var dealColumns = function (col, hasCertificate, showModel = false) {
	var res = {};
	// var data = {};
	// // var sortName = '';
	// if (datas && datas.length > 0) {
	// 	data = datas[0];
	// }
	for (var i = 0; i < col.length; i++) {
		var carr = col[i];

		if (i == 0) {
			if (hasCertificate) {
				carr.unshift({
					field: 'STATUS',
					colspan: 1,
					title: "合格证",
					align: "center",
					width: 40,
					rowspan: col.length,
					formatter: function (value, rec) {
						if (value == '1') {
							return '<button type="button" class="layui-btn layui-btn-xs" onclick="viewCertificate(\'' +
								rec.TABLE_CONFIG_ID + '\',\'' + rec.ID + '\')">查看</button>';
						} else {
							return '<span style="color:red">未确认</span>';
						}
					}
				});
			}


			// carr.unshift({
			// 	field: 'STATUS',
			// 	colspan: 1,
			// 	title: "状态",
			// 	align: "center",
			// 	hidden: true,
			// 	width: 40,
			// 	rowspan: col.length,
			// 	formatter: function(value, rec) {
			// 		if (value == '') {
			// 			return '<span style="color:red">未推送</span>';
			// 		} else if (value == '已推送') {
			// 			return '<span style="color:#FFB800">已推送</span>';
			// 		} else {
			// 			return value;
			// 		}
			// 	}
			// });
			if (showModel == true) {
				carr.unshift({
					field: 'LEAF',
					colspan: 1,
					title: "过程",
					align: "center",
					hidden: false,
					width: 40,
					rowspan: col.length
				});
				carr.unshift({
					field: 'DIR',
					colspan: 1,
					title: "专业",
					align: "center",
					hidden: false,
					width: 40,
					rowspan: col.length
				});
				carr.unshift({
					field: 'PHASE',
					colspan: 1,
					title: "阶段",
					align: "center",
					hidden: false,
					width: 40,
					rowspan: col.length
				});
				carr.unshift({
					field: 'MODEL',
					colspan: 1,
					title: "型号",
					align: "center",
					hidden: false,
					width: 40,
					rowspan: col.length
				});
			}
			carr.unshift({
				field: 'ck',
				checkbox: true,
				width: 40,
				colspan: 1,
				rowspan: col.length
			});
		}
		for (var j = 0; j < carr.length; j++) {
			var c = carr[j];
			if (c.field == "STATUS") {
				continue;
			}
			c.formatter = function (value, rec, index) {
				var cellId = this.id || this.cellClass;
				if (String(value).indexOf("-~!-false") > -1) {
					//超差报警
					value = String(value).replace("-~!-false", "");
				}
				if (StringTolog(value) > 20) {
					var temp = value.substring(0, 20);
					return temp + '<a class="table-row-a" onclick = "viewMoreValue(\'' + createMoreValues(value,
						cellId) + '\')">...</a>';
				} else {
					return value;
				}
			}
			if (c.format) {
				if (c.format == 2) {
					c.formatter = function (value, rec) {
						var filepath = rec.FILEPATH;
						return '<a class="table-row-a" onclick = "viewThreeTable(\'' + filepath + '\')">' +
							value + '</a>'
					}
				} else if (c.format == 7) {
					//附件上传
					c.formatter = function (value, rec) {
						var tableName = rec.TABLE_NAME,
							dataId = rec.ID;
						if (value.indexOf('{"images":') > -1) {
							value = String(value).replace("-~!-false", "");
							var fileObj = JSON.parse(value);
							var path = '/File' + fileObj.images[0].filePath;
							return '<img class="table-image" type="img" tableName = "' + tableName +
								'" dataId = "' + dataId + '" src="' + path + '" width=65 height=28 />';
						} else {
							return '<span class="table-image" type="text" tableName = "' + tableName +
								'" dataId = "' + dataId + '">' + value + '</span>';
						}
					}
				} else if (c.format == 8) {
					c.formatter = function (value, rec) {
						var dataSeparator = "!@#$%^";
						//定义文件路径与名称之间的分隔符
						var fileSeparator = "^%$#@!";
						//value:合格!@#$%^//2025-03//8cf7935a-a8e6-41f0-b048-e65fd8cb806d!@#$%^//2025-03//254216d9-0b79-4833-adf2-34e3ad2aeb58
						if (!value) return '';

						// 分割文本和照片
						var parts = value.split(dataSeparator);
						if (parts.length <= 1) return value;

						// 第一部分是文字
						var text = parts[0];

						// 统计照片数量
						var photoCount = parts.length - 1;

						// 构建照片数据数组用于layer.photos
						var photos = [];
						for (var i = 1; i < parts.length; i++) {
							if (parts[i] && parts[i].trim() !== '') {
								photos.push({
									src: "/File" + parts[i].split(fileSeparator)[0],
									alt: parts[i].split(fileSeparator)[1]
								});
							}
						}

						// 创建唯一ID用于存储照片数据
						var photoId = 'photo_' + new Date().getTime() + '_' + Math.floor(Math.random() * 1000);
						window[photoId] = photos;

						// 返回文字和照片数量，点击时显示照片
						return text + ' <a class="layui-btn layui-btn-xs layui-btn-normal" onclick="showPhotos(\'' + photoId + '\')">' + photoCount + '张照片</a>';
					}
				}
			}
			c.styler = function (value, rec, index) {
				if (String(value).indexOf('{"images":') == -1) {
					if (String(value).indexOf("-~!-false") > -1) {
						return 'background-color:red';
					}
				}
			}
		}
	}
	res.col = col;
	// res.sortName = sortName;
	return res;
};



//处理表格数据量多的时候显示格式
var dealGridColumns = function (cols) {
	for (var i = 0; i < cols.length; i++) {
		var col = cols[i];
		for (var j = 0; j < col.length; j++) {
			col[j].formatter = function (value, rec) {
				var cellId = this.id || this.cellClass;
				if (StringTolog(value) > 20) {
					var temp = value.substring(0, 20);
					return temp + '<a class="table-row-a" onclick = "viewMoreValue(\'' + createMoreValues(value,
						cellId) + '\')">...</a>';
				} else {
					return value;
				}
			}
		}
	}
	return cols;
};

//查看三级表
var viewThreeTable = function (filepath) {
	var path = filepath.replace(/\\/g, "/");
	var port = window.location.port;
	var url = "/DataPackageManagement/";
	$.ajax({
		type: "POST",
		url: fileHandlerUrl + "/table/excel/to/html?filepath=" + filepath,
		async: true,
		contentType: "application/x-www-form-urlencoded; charset=utf-8",
		success: function (data) {
			var myWindow = window.open('');
			var html = '<head>\
							<link rel="stylesheet" href="' + url + 'plugins/layui/css/layui.css" media="all">\
							<style type="text/css">\
								.layui-table td,\
								.layui-table th,\
								.layui-table-col-set,\
								.layui-table-fixed-r,\
								.layui-table-grid-down,\
								.layui-table-header,\
								.layui-table-page,\
								.layui-table-tips-main,\
								.layui-table-tool,\
								.layui-table-total,\
								.layui-table-view,\
								.layui-table[lay-skin=line],\
								.layui-table[lay-skin=row] {\
									border-width: 1 px;\
									border-style: solid;\
									border-color: #333;\
								}\
							</style>\
						</head>\
						<body>' + data + '\
						</body>';
			myWindow.document.write(html)
			myWindow.focus();
		}
	});

};

var createMoreValues = function (value, cellId) {
	var newTime = new Date().getTime() + cellId;
	if (window.moreValues) {
		window.moreValues[newTime] = value;
	} else {
		window.moreValues = {};
		window.moreValues[newTime] = value;
	}
	return newTime;
}

var viewMoreValue = function (newTime) {
	var value = window.moreValues[newTime];
	layer.alert(value, {
		title: '查看更多',
		shadeClose: true
	});
	// layer.open({
	// 	title: '查看更多',
	// 	type: 1,
	// 	anim: false,
	// 	openDuration: 200,
	// 	isOutAnim: false,
	// 	closeDuration: 200,
	// 	shadeClose: false,
	// 	// fixed: false,
	// 	maxmin: false,
	// 	resize: false, //不允许拉伸
	// 	area: ['500px', '600px'],
	// 	content: '<div style="padding: 15px;">' + value + '</div>',
	// 	btn: ['关闭'],
	// 	yes: function() {
	// 		layer.closeAll();
	// 	},
	// 	success: function() {

	// 	}
	// });
}

var windowH = window.innerHeight || document.documentElement.clientHeight;


var gridUtil = {
	createColumnMenu: function (tableId) {
		var cmenu = $('<div/>').appendTo('body');
		cmenu.menu({
			onClick: function (item) {
				if (item.iconCls == 'icon-ok') {
					$('#' + tableId).datagrid('hideColumn', item.name);
					var childField = item.childField;
					cmenu.menu('setIcon', {
						target: item.target,
						iconCls: 'icon-empty'
					});
					for (var i = 0; i < childField.length; i++) {
						$('#' + tableId).datagrid('hideColumn', childField[i]);
					}
				} else {
					$('#' + tableId).datagrid('showColumn', item.name);
					var childField = item.childField;
					cmenu.menu('setIcon', {
						target: item.target,
						iconCls: 'icon-ok'
					});
					for (var i = 0; i < childField.length; i++) {
						$('#' + tableId).datagrid('showColumn', childField[i]);
					}
				}
			}
		});
		var columns = $('#' + tableId).datagrid('options').columns;
		var columns1 = [];
		var columns2 = [];
		if (columns.length == 1) {
			columns1 = columns[0];
		} else if (columns.length == 2) {
			columns1 = columns[0];
			columns2 = columns[1];
		}
		for (var i = 0; i < columns1.length; i++) {
			var column = columns1[i];
			if (column.field == 'ck') {
				continue;
			}
			var c1Colspan = column.colspan;
			var childField = [];
			if (c1Colspan > 1) {
				var c1ColIndex = column.cellColumnIndex;
				for (var j = 0; j < columns2.length; j++) {
					var c2 = columns2[j];
					for (var x = 0; x < c1Colspan; x++) {
						if (c1ColIndex + x == c2.cellColumnIndex) {
							childField.push(c2.field);
						}
					}
				}
			}
			cmenu.menu('appendItem', {
				text: column.title.replace(new RegExp("<br>", "gm"), ""),
				name: column.field,
				childField: childField,
				iconCls: 'icon-ok'
			});
		}
		return cmenu;
	}
};

var locationTreeNodeUtil = function (allDatas, ztreeObj, idName, callbackFn) {
	function recursionTree(allDatasIndex) {
		var treeId = allDatas[allDatasIndex][idName];
		if (treeId !== undefined) {
			var thisNode = ztreeObj.getNodeByParam(idName, treeId, null);
			if (allDatasIndex == allDatas.length - 1) {
				ztreeObj.selectNode(thisNode);
				callbackFn(thisNode);
				// reloadTable(thisNode);
			} else {
				//如果这个节点是父节点的话
				if (thisNode.ISPARENT) {
					//判断是否是展开的状态
					if (thisNode.open) {
						var newIndex = allDatasIndex + 1;
						recursionTree(newIndex);
					} else {
						//如果没有展开的话需要请求该节点下的子数据
						ztreeObj.reAsyncChildNodes(thisNode, "refresh", false, function () {
							//展开之后再判断下一层级的节点
							var newIndex = allDatasIndex + 1;
							recursionTree(newIndex);
						});
					}
				}
			}
		}
	}
	recursionTree(0);
}

//清单数据表相关
var listTableUtil = {
	getColumns: function (queryType) {
		var columns = [
			[{
				field: 'FILE_TYPE',
				title: '文件类别',
				width: 150,
				align: 'center'
			},
			{
				field: 'FILE_NUMBER',
				title: '文件编号',
				hidden: true
			},
			{
				field: 'FILE_NAME',
				title: '文件名称',
				width: 300,
				align: 'center'
			},
			{
				field: 'GATHERING_METHOD',
				title: '采集方式',
				width: 100,
				align: 'center'
			},
			{
				field: 'SOURCE_SYSTEM',
				title: '来源系统',
				width: 100,
				align: 'center'
			},
			{
				field: 'SECURITY_LEVEL',
				title: '密级',
				width: 100,
				align: 'center'
			},
			{
				field: 'STATE_CHECK',
				title: '状态',
				width: 100,
				align: 'center'
			},
			{
				field: 'DELIVERY_STATE',
				title: '交付状态',
				width: 100,
				align: 'center'
			},
			{
				field: 'USER_FULLNAME',
				title: '创建人',
				width: 100,
				align: 'center'
			},
			{
				field: 'CREATE_TIMESTAMP',
				title: '创建日期',
				width: 150,
				align: 'center'
			},
			{
				field: 'FILE_FORMAT',
				title: '文件形式',
				width: 100,
				align: 'center',
				formatter: formatterEmptyValue
			}, {
				field: 'op',
				title: '操作',
				align: 'center',
				width: '150px',
				formatter: opFomatter
			}
			]
		];
		if (queryType == 'all') {
			columns[0].unshift({
				field: 'TYPE',
				title: '类别',
				width: 80,
				align: 'center'
			});
		} else if (queryType == 'photo') {
			columns = [
				[{
					field: 'FILE_TYPE',
					title: '文件类别',
					width: 150,
					align: 'center'
				},
				{
					field: 'FILE_NUMBER',
					title: '文件编号',
					hidden: true
				},
				{
					field: 'FILE_NAME',
					title: '文件名称',
					width: 300,
					align: 'center'
				},
				{
					field: 'CABLE_CODE',
					title: '电缆编号',
					width: 300,
					align: 'center',
					hidden: true,
					formatter: formatterEmptyValue
				},
				{
					field: 'COMPONENT_CODE',
					title: '接插件编号',
					width: 300,
					align: 'center',
					hidden: true,
					formatter: formatterEmptyValue
				},
				{
					field: 'SECURITY_LEVEL',
					title: '密级',
					width: 100,
					align: 'center'
				},
				{
					field: 'STATE_CHECK',
					title: '状态',
					width: 100,
					align: 'center'
				},
				{
					field: 'DELIVERY_STATE',
					title: '交付状态',
					width: 100,
					align: 'center'
				},
				{
					field: 'CREATE_TIMESTAMP',
					title: '创建日期',
					width: 150,
					align: 'center'
				},
				{
					field: 'op',
					title: '操作',
					align: 'center',
					width: '150px',
					formatter: opFomatter
				}
				]
			];
		}
		return columns;
	}
}

function clearUserEdit(successFn) {
	var user = sessionStorage.getItem('fullname') + '[' + sessionStorage.getItem('username') + ']';
	var cb_success = function (res) {
		if (res.success) {
			successFn();
		}
	};
	var cb_error = function (xhr) {
		// layer.alert('清空用户的所有确认表编辑状态信息失败!', {
		// 	icon: 2
		// });
	};
	twxAjax('Thing.Util.HandsonTable', "ClearUserEdit", {
		user: user
	}, false, cb_success, cb_error);
}

window.onbeforeunload = function () {
	clearUserEdit(function () { });
};

/**
 * 初始化用户ip
 */
function initUserIp() {
	$.ajax({
		type: "GET",
		url: fileHandlerUrl + "/system/get/ip",
		async: false,
		contentType: "application/x-www-form-urlencoded; charset=utf-8",
		success: function (res) {
			var userIp = "未知IP";
			if (res.success) {
				userIp = res.data;
			}
			sessionStorage.setItem("userIp", userIp);
		}
	});
}

/**
 * 增加一条确认表操作日志
 */
function addConfirmLog(json) {
	json.userIp = sessionStorage.getItem("userIp");
	json.username = sessionStorage.getItem("username");
	if (funcIdent == "report") {
		json.tableName = "QUALITY_REPORT";
		json.moduleType = "AIT质量确认";
	} else if (funcIdent == "confirm") {
		json.tableName = "QUALITY_REPORT_C";
		json.moduleType = "产品质量确认";
	} else if (funcIdent == "launch") {
		json.tableName = "LAUNCH_CONFIRM";
		json.moduleType = "发射场确认";
	}
	twxAjax("Thing.Util.HandsonTable", "AddLog", {
		json: json
	}, true, function (res) { });
}

/**
 * 清单数据表格
 * @param {Object} options
 * {eleId:'',tableHeight:'',colType:'',toolbar:'',searchParams:{},isCheck:false,pageSize:10}
 */
var ListDataGrid = function (options) {
	var t = this;
	var $el = $('#' + options.eleId);
	var columns = listTableUtil.getColumns(options.colType);
	if (options.isCheck) {
		columns[0].unshift({
			field: 'ck',
			checkbox: true
		});
	}
	var initDatas = options.initDatas;
	var pageOptions = {
		pageSize: options.pageSize,
		pageNumber: 1
	};
	t.searchParams = options.searchParams;

	var totalRecords = 0;
	var dataLoadFlag = false;
	var pageLoadFlag = false;
	var tableLoadFlag = false;

	/**
	 * 用一个空数组初始化表格
	 */
	t.renderTable = function () {
		if (!tableLoadFlag) {
			$el.datagrid({
				data: initDatas,
				fitColumns: true,
				height: options.tableHeight,
				toolbar: '#' + options.toolbar,
				columns: columns,
				emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>数据加载中...</font></div>',
				pagination: true,
				loadMsg: '正在加载数据...',
				rownumbers: true,
				singleSelect: false,
				striped: true,
				onLoadSuccess: function (data) {
					changeWidth(options.eleId);
				}
			});
			tableLoadFlag = true;
		}
	}
	t.loadEmptyData = function () {
		$el.datagrid('loadData', []);
	}

	//初始化分页组件
	t.initPagination = function (data) {
		$el.datagrid('getPager').pagination({
			total: data.total,
			pageSize: pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function () {
					t.queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
				}
			}],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function (pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				pageOptions.pageNumber = pageNumber;
				t.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function (pageNumber, pageSize) { },
			onRefresh: function (pageNumber, pageSize) {
				t.queryDataByPage(pageSize, pageOptions.pageNumber);
			},
			onChangePageSize: function (pageSize) {
				//改变pageSize时触发
				pageOptions.pageSize = pageSize;
				t.queryDataByPage(pageSize, pageOptions.pageNumber);
			}
		})
	};

	t.loadData = function () {
		t.initPagination({
			total: 0
		});
		t.queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	};
	//分页查询数据
	t.queryDataByPage = function (pageSize, pageNumber) {
		totalRecords = 0;
		dataLoadFlag = false;
		pageLoadFlag = false;
		$el.datagrid('loading');
		t.initTotalRecords();
		var cb_success = function (res) {
			if (res.success) {
				dataLoadFlag = true;
				//调用成功后，渲染数据
				$el.datagrid('loadData', res.data);
				if (pageLoadFlag) {
					t.paginationShow();
				}
				$el.datagrid('loaded');
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function () {
			layui.use(['layer'], function () {
				var layer = layui.layer;
				layer.alert('加载出错...', {
					icon: 2
				});
			});
		};
		var params = {};
		params.query = t.searchParams;
		params.pageSize = pageOptions.pageSize;
		params.pageNumber = pageOptions.pageNumber;
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.ListData', 'QueryListDataPage', params, true, cb_success, cb_error);
	};

	t.paginationShow = function () {
		$el.datagrid('getPager').pagination('refresh', {
			total: totalRecords,
			pageSize: pageOptions.pageSize,
			pageNumber: pageOptions.pageNumber
		});
		//重新初始化行号
		t.initLineNumbers();
	}

	//初始化全部的记录条数
	t.initTotalRecords = function () {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function (res) {
			if (res.success) {
				pageLoadFlag = true;
				totalRecords = res.data;
				if (dataLoadFlag) {
					t.paginationShow();
				}
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function () { };
		var params = {};
		params.query = t.searchParams;
		twxAjax('Thing.Fn.ListData', 'QueryListDataCount', params, true, cb_success, cb_error);
	};

	//初始化行号
	t.initLineNumbers = function () {
		var rowNumbers = $('.datagrid-cell-rownumber');
		var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
		$(rowNumbers).each(function (index) {
			var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
			$(rowNumbers[index]).html("");
			$(rowNumbers[index]).html(row);
		});
	};

	t.getSelections = function () {
		return $el.datagrid('getSelections');
	}
	t.renderTable();
}

/**
 * 单个大文件上传
 */
function oneBigFileUpload(options) {
	var layerTitle = options.layerTitle;
	var emptyMsg = options.emptyMsg;
	var extraData = options.extraData || "{}";
	var uploadSuccess = options.uploadSuccess;
	var uploadComplete = options.uploadComplete || function () { };
	var fileQueued = options.fileQueued || function () { };
	var url = options.url;
	var accept = options.accept || null;

	var loadIndex = 0;
	var fileFlag = false;
	layer.open({
		title: layerTitle,
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['350px', '240px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert(emptyMsg, {
					icon: 2
				});
				return false;
			}
			$("#uploadStart").click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = `<form class="layui-form" lay-filter="uploadForm">
	                            <div class="layui-form-item">
	                                <label class="fieldlabel layui-form-label">文件内容:</label>
	                                <div class="layui-input-block">
	                                    <div class="layui-upload">
	                                        <div id="uploadChoice">选择文件</div>
	                                        <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="layui-form-item" id="selectedFile" style="display: none;">
	                                <label class="fieldlabel layui-form-label">已选文件:</label>
	                                <div class="layui-input-block">
	                                    <div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
	                                </div>
	                            </div>
	                            <div class="layui-form-item" style="display:none;">
	                                <center>
	                                    <button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
	                                    <button id="btn_cancel" class="layui-btn">取消</button>
	                                </center>
	                            </div>
	                        </form>`;
			$("#uploadContent").append(addTpl);
		}
	});
	form.render(null, 'uploadForm');
	var reqIdent = new Date().getTime();
	var uploader = WebUploader.create({
		// 选完文件后，是否自动上传。
		auto: false,
		// 文件接收服务端。
		server: url,
		// 选择文件的按钮。可选。
		pick: {
			id: '#uploadChoice',
			multiple: false // 设置multiple为false
		},
		accept: accept,
		timeout: 30 * 60 * 1000,
		threads: 5,
		// 配置分片上传
		chunked: true,
		chunkSize: 20 * 1024 * 1024,
		fileNumLimit: 1,
		formData: {
			reqIdent: reqIdent,
			extraData: extraData == "{}" ? "{}" : JSON.stringify(extraData)
		}
	});
	uploader.on('uploadBeforeSend', function (object, data, headers) {

	});

	// 当有文件被添加进队列之前触发
	uploader.on('beforeFileQueued', function (file) {
		// 检查队列中是否已经有文件
		if (uploader.getFiles().length > 0) {
			// 如果有文件，先移除旧的文件
			uploader.removeFile(uploader.getFiles()[0], true);
		}
	});

	// 当有文件被添加进队列的时候
	uploader.on('fileQueued', function (file) {
		fileFlag = true;
		$("#selectedFile").show();
		$("#selectedFileName").text(file.name);
		fileQueued(file.name);
	});

	uploader.on('uploadSuccess', function (file, res) {
		if (res.success) {
			layer.closeAll();
			uploadSuccess(res);
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});

	// 文件上传失败，显示上传出错。
	uploader.on('uploadError', function (file) {

	});

	// 完成上传完毕，成功或者失败，先删除进度条。
	uploader.on('uploadComplete', function (file) {
		uploadComplete();
	});

	// 当所有文件上传结束时触发
	uploader.on('uploadFinished', function () {
		layer.close(loadIndex);
	});

	$("#uploadStart").on('click', function () {
		loadIndex = layer.msg('导入中', {
			icon: 16,
			shade: 0.01,
			time: 0
		});
		uploader.upload(); // 手动触发上传操作
	});

}

/**
 * 对移动节点的树的数据进行处理
 * @param {} datas
 * @param parentNode 需要转移的节点的父节点
 */
function dealMoveTreeData(datas, parentNode) {
	datas = dealDataIcons(datas);
	datas = dealDataNodeName(datas);
	var parentType = parentNode['TYPE'];
	var parentLevel = parentNode['LEVEL_NUM'];
	var parentId = parentNode['ID'];
	for (var i = 0; i < datas.length; i++) {
		var dLevel = datas[i]['LEVEL_NUM'];
		var dType = datas[i]['TYPE'];
		if (parentLevel <= dLevel) {
			datas[i]['ISPARENT'] = false;
		}
		if (dType == parentType) {
			datas[i]['nocheck'] = false;
			if (datas[i]['ID'] == parentId) {
				datas[i]['checked'] = true;
			} else {
				datas[i]['checked'] = false;
			}
		} else {
			datas[i]['nocheck'] = true;
		}
	}
	return datas;
}

function downloadFile(filePath, fileName) {
	// 对路径和文件名进行编码
	var encodedPath = encodeURIComponent(filePath.replace(/\\/g, '/'));
	var encodedFileName = encodeURIComponent(fileName);
	var url = fileHandlerUrl + "/file/download?filePath=" + encodedPath + "&fileName=" + encodedFileName;
	window.open(url);
}

// 添加显示照片的函数
function showPhotos(photoId) {
	var photos = window[photoId];
	if (!photos || photos.length === 0) {
		layer.msg('没有可显示的照片');
		return;
	}

	layui.use(['layer'], function () {
		layer.photos({
			photos: {
				"title": "查看照片",
				"start": 0,
				"data": photos
			},
			anim: 5
		});
	});
}