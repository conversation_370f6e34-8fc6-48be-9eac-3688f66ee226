var THING = "Thing.Fn.BigScreen";
var username = sessionStorage.getItem("username");
var layer, form, laydate, table, element, carousel;

/**
 * 加载型号数据包采集数量TOP10饼状图
 */
function PieChart(treeId) {
	var othis = this;
	this.option = {
		tooltip: {
			trigger: 'item'
		},
		series: [{
			type: 'pie',
			radius: ['40%', '70%'],
			label: {
				color: "rgba(255, 255, 255, 1)",
				formatter: function(param) {
					return param.name + '(' + param.value + ')';
				}
			},
			data: []
		}]
	};

	this.elId = "pie-chart";
	this.showMsg = function(msg) {
		$('#' + othis.elId).append('<span style="color:red">' + msg + '<span>');
	};
	this.loadChart = function() {
		twxAjax(THING, 'QueryModelSort', {
			username: username,
			treeId: treeId
		}, true, function(res) {
			if (res.success) {
				othis.chartDom = document.getElementById(othis.elId);
				othis.chart = echarts.init(othis.chartDom);
				othis.option.series[0].data = res.data;
				othis.chart.setOption(othis.option);
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function(e) {
			othis.showMsg("查询型号数据包采集数量TOP10出错！");
		});
		othis.chartDom = document.getElementById(othis.elId);
		othis.chart = echarts.init(othis.chartDom);
		othis.chart.setOption(othis.option);
	}
	this.loadChart();
}

/**
 * 加载左上角区域的数据
 */
function loadNum(treeId) {
	var cb_success = function(res) {
		if (res.success) {
			var data = res.data;
			for (var id in data) {
				$("#" + id).text(data[id]);
			}
		} else {
			layer.alert(res.msg);
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.alert("查询左上角数量出错！", {
			icon: 2
		});
	};
	//同步新增
	twxAjax(THING, "QueryNum", {
		username: username,
		treeId: treeId
	}, true, cb_success, cb_error);
}



function renderClick() {
	$("#process-row").bind("click", function() {
		processClick("过程记录", $("#model-select").val());
	});
	$("#document-row").bind("click", function() {
		processClick("技术文件", $("#model-select").val());
	});

	$("#refresh-btn").bind("click", function() {
		layer.alert('统计正在进行中，请稍后刷新页面查看！', {
			icon: 1
		});
		twxAjax(THING, 'UpdateAllData', {}, true, function(res) {});
	});
}

function showProcessChart(name, treeId) {
	var option = getBarChartOption(name);
	/* 弹窗不加载滚动条 */
	var chartDom = document.getElementById("chartContainer");
	var chart = echarts.init(chartDom);

	var cb_success = function(res) {
		if (res.success) {
			option.xAxis.data = res.data.name;
			option.series[0].data = res.data.value;
			chart.setOption(option);

			chart.on('click', function(params) {
				$(".my-layer > div.layui-layer-title > span:nth-child(2)").mousedown();
				table.reload('list-table', {
					page: {
						layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
						groups: 1,
						first: false,
						last: false,
						curr: 1
					},
					where: {
						treeId: params.name.split("~~~")[1],
						queryType: name,
						username: username
					}
				});
			});
		} else {
			layer.alert(res.msg);
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.alert("查询型号的过程控制数量出错！", {
			icon: 2
		});
	};
	//同步新增
	twxAjax(THING, "QueryModelProcessCount", {
		username: username,
		treeId: treeId,
		typeName: name
	}, true, cb_success, cb_error);
}

function showProcessTable(name, modelId) {
	table.render({
		id: "list-table",
		elem: '#list-table',
		skin: "nob",
		url: getUrl(THING, 'QueryListTable', ""),
		where: {
			treeId: modelId,
			queryType: name,
			username: username
		},
		page: {
			layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
			groups: 1,
			first: false,
			last: false
		},
		cols: [
			[{
				title: '序号',
				type: 'numbers',
				width: 60
			}, {
				field: 'FILE_TYPE',
				title: '文件类别',
				width: 150
			}, {
				field: 'FILE_NAME',
				title: '文件名称',
				width: 320
			}, {
				field: 'GATHERING_METHOD',
				title: '采集方式',
				width: 100
			}, {
				field: 'SOURCE_SYSTEM',
				title: '来源系统',
				width: 100
			}, {
				field: 'SECURITY_LEVEL',
				title: '密级',
				width: 80
			}, {
				field: 'USER_FULLNAME',
				title: '创建人',
				width: 100
			}, {
				field: 'CREATE_TIMESTAMP',
				title: '采集时间',
				width: 180
			}, {
				field: 'FILEPATH',
				title: '操作',
				width: 100,
				templet: function(d) {
					return '<span style="cursor: pointer;" class="layui-badge layui-bg-blue" onClick = "previewList(\'' + d.ID + '\',\'' + d.TABLENAME + '\',\'' + d.FILEPATH + '\',\'' + d.FILE_NAME + '\',\'' + d.FILE_FORMAT + '\',\'' + d.FILE_TYPE + '\',\'' + d.SOURCE_SYSTEM + '\',\'' + d.GATHERING_METHOD + '\')">查看</span>';
				}
			}]
		],
		even: true
	});
}

function processClick(name, treeId) {
	layer.tab({
		type: 1,
		skin: 'layui-layer-tab my-layer',
		tab: [{
			title: name,
			content: '<div id="chartContent" style="height: 570px;padding:50px;"><div id="chartContainer" style="height: 570px;"></div>'
		}, {
			title: '详细列表',
			content: '<div id="tableContent" style="height: 570px;padding:10px 50px 0px 50px;"><table id="list-table"></table></div>'
		}],
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		closeBtn: 2,
		shadeClose: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['1300px', '720px'],
		success: function() {
			showProcessChart(name, treeId);
			showProcessTable(name, treeId);
		}
	});
}

/**
 * 加载型号照片排行数据
 */
function loadPhotoSort(treeId) {
	var cb_success = function(res) {
		if (res.success) {
			var data = res.data;
			//清空原有数据
			$(".table-row").remove();
			for (var i = 0; i < data.length; i++) {
				var d = data[i];
				var index = i + 1;
				var modelName = d.name;
				var per = d.per;
				var value = d.value;
				var sortCls = "sort1";
				if (i > 2) {
					sortCls = "sort2";
				}
				var tpl = '<div class="layui-row table-row">\
								<div class="layui-col-md2">\
									<div class="' + sortCls + ' sort">' + index + '</div>\
								</div>\
								<div class="layui-col-md3 photo-model">' + modelName + '</div>\
								<div class="layui-col-md5 photo-process">\
									<div class="layui-progress">\
										<div class="layui-progress-bar" lay-percent="' + per + '%" style="width: ' + per + '%;"></div>\
									</div>\
								</div>\
								<div class="layui-col-md2 photo-num">' + value + '</div>\
							</div>';
				$("#photo-table").append(tpl);
			}
		} else {
			layer.alert(res.msg);
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.alert("查询型号照片数量排行榜出错！", {
			icon: 2
		});
	};
	//同步新增
	twxAjax(THING, "QueryPhotoSort", {
		username: username,
		treeId: treeId
	}, true, cb_success, cb_error);
}

//加载型号下拉框
function loadModelSelect(successFn) {
	var cb_success = function(res) {
		if (res.success) {
			var data = res.data;
			//清空原有数据
			$("#model-select").empty();
			$("#model-select").append('<option value="-1">所有型号</option>');

			for (var i = 0; i < data.length; i++) {
				var d = data[i];
				var $option = $('<option value="' + d.TREEID + '">' + d.NODENAME + '</option>');
				$option.data("data", d);
				$("#model-select").append($option);
			}
			successFn();
		} else {
			layer.alert(res.msg);
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.alert("查询所有的型号出错！", {
			icon: 2
		});
	};
	//同步新增
	twxAjax(THING, "QueryAllModel", {
		username: username
	}, true, cb_success, cb_error);
}


/**
 * 加载质量数据 使用canvas划线
 */
function loadQuality(treeId) {

	var cb_success = function(res) {
		if (res.success) {
			var datas = res.data;
			drawQuality(datas, treeId);
		} else {
			layer.alert(res.msg);
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.alert("查询质量数据出错！", {
			icon: 2
		});
	};
	//同步新增
	twxAjax(THING, "QueryQualityData", {
		treeId: treeId,
		username: username
	}, true, cb_success, cb_error);

}

/**
 * 绘制质量数据的样式
 * @param {Object} datas
 */
function drawQuality(datas, treeId) {
	//获取canvas父元素的宽高
	var $canvas = $("#canvas");
	var height = $canvas.parent().height();
	var width = $canvas.parent().width();
	$canvas.attr("height", height).attr("width", width);

	var canvas = document.getElementById('canvas');
	canvas.onclick = null;
	// 使用2d绘图
	var ctx = canvas.getContext('2d');
	var font = "bold 15px 微软雅黑";
	// 使用context绘制
	ctx.strokeStyle = '#00CDFF';

	ctx.font = font;
	ctx.fillStyle = '#00CDFF';
	//先画中心圆
	//获取圆心，canvas 的 中心点
	var centerX = width / 2;
	var centerY = height / 2;
	var r = height / 5;
	// ctx.beginPath();
	// ctx.arc(centerX, centerY, r, 0, 2 * Math.PI);
	// ctx.stroke();

	var points = [];
	var pointsNum = datas.length;
	for (var i = 0; i < pointsNum; i++) {
		var angle = i * Math.PI / (pointsNum / 2);
		var x = r * Math.cos(angle) + centerX;
		var y = r * Math.sin(angle) + centerY;
		ctx.arc(x, y, 3, 0, 2 * Math.PI);
		points.push({
			x: x,
			y: y
		});
		// ctx.fillText(i, x, y);
	}

	var texts = [];
	var eR = r + 90;
	for (var i = 0; i < pointsNum; i++) {
		var angle = i * Math.PI / (pointsNum / 2);
		var x = eR * Math.cos(angle) + centerX;
		var y = eR * Math.sin(angle) + centerY;
		ctx.beginPath();
		ctx.moveTo(points[i].x, points[i].y)
		ctx.lineTo(x, y);
		var text = datas[i].name + '(' + datas[i].num + ')';
		var textW = ctx.measureText(text).width;
		var lineL = textW + 20;
		var textH = 20;
		var textX = 0,
			textY = y - 5,
			endX = 0,
			endY = y;
		if (i == 3 || i == 4 || i == 5) {
			endX = x - lineL;
			textX = x - lineL + 10;
		} else {
			endX = x + lineL;
			textX = x + 10;
		}
		ctx.lineTo(endX, endY);
		ctx.stroke();
		ctx.fillText(text, textX, textY);
		// ctx.strokeRect(textX, textY - textH + 5, textW, textH);


		texts.push({
			text: text,
			name: datas[i].name,
			tableId: datas[i].tableId,
			x: textX,
			y: textY - textH + 5,
			w: textW,
			h: textH
		});
	}



	/**
	 * 鼠标位置是否在文字区域中
	 * @param {Object} ex
	 * @param {Object} ey
	 */
	function isInTextArea(ex, ey) {
		var flag = false;
		var text = undefined;
		for (var i = 0; i < texts.length; i++) {
			var t = texts[i];
			if ((ex > t.x && ex < (t.x + t.w)) && (ey > t.y && ey < (t.y + t.h))) {
				flag = true;
				text = t;
				break;
			}
		}
		return {
			flag: flag,
			t: text
		};
	}

	//更改鼠标样式
	canvas.addEventListener("mousemove", function(event) {
		const rect = canvas.getBoundingClientRect();
		const ex = event.clientX - rect.left;
		const ey = event.clientY - rect.top;
		if (isInTextArea(ex, ey).flag) {
			canvas.style.cursor = "pointer";
		} else {
			canvas.style.cursor = "default";
		}
	});

	canvas.onclick = function(event) {
		const rect = canvas.getBoundingClientRect();
		const ex = event.clientX - rect.left;
		const ey = event.clientY - rect.top;

		// 判断点击的位置是否在字体内
		var t = isInTextArea(ex, ey);
		if (t.flag) {
			if (treeId == -1) {
				showQualityChart(t.t.name, t.t.tableId);
			} else {
				showQualityTable(treeId, t.t.tableId)
			}
		}
	}
}


/**
 * 点击质量数据 弹出各个型号的数量统计柱状图
 */
function showQualityChart(name, tableId) {
	var option = getBarChartOption(name);

	layer.open({
		title: false,
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		closeBtn: 2,
		shadeClose: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['1200px', '500px'],
		content: '<div id="chartContent" style="height: 500px;"></div>',
		success: function() {
			/* 弹窗不加载滚动条 */
			$("#chartContent").parent().css('overflow', 'visible').css("background-color", "#0b338b");
			var chartDom = document.getElementById("chartContent");
			var chart = echarts.init(chartDom);

			var cb_success = function(res) {
				if (res.success) {
					option.xAxis.data = res.data.name;
					option.series[0].data = res.data.value;
					chart.setOption(option);

					chart.on('click', function(params) {
						showQualityTable(params.name.split("~~~")[1], tableId);
					});
				} else {
					layer.alert(res.msg);
				}
			}
			//请求失败的回调
			var cb_error = function(xhr, textStatus, errorThrown) {
				layer.alert("查询质量数据出错！", {
					icon: 2
				});
			};
			//同步新增
			twxAjax(THING, "QueryTypeQuality", {
				tableId: tableId,
				username: username
			}, true, cb_success, cb_error);
		}
	});
}

function showQualityTable(treeId, tableId) {
	layer.open({
		title: false,
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		closeBtn: 2,
		shadeClose: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['1500px', '770px'],
		content: '<div id="tableContent" class="original-table" style="height: 770px;"><div id="secondTable"></div></div>',
		success: function() {
			/* 弹窗不加载滚动条 */
			$("#tableContent").parent().css('overflow', 'visible').css("background-color", "#0b338b");
			new SecondTable(treeId, tableId);
		}
	});
}

/**
 * 加载最近的5张照片
 */
function loadRecentPhoto(treeId) {
	var cb_success = function(res) {
		if (res.success) {
			$("#carousel-item").empty();
			var datas = res.data;
			for (var i = 0; i < datas.length; i++) {
				$("#carousel-item").append('<div><img src="' + datas[i].path + '" alt=""></div>');
			}

			//建造实例
			carousel.render({
				elem: '#carousel',
				width: '100%',
				height: $(".content-right-bottom-card").height() + "px",
				arrow: 'none',
				indicator: "inside",
				interval: 2500,
				autoplay: true
				//,anim: 'updown' //切换动画方式
			});
		} else {
			layer.alert(res.msg);
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.alert("查询最近照片出错！", {
			icon: 2
		});
	};
	//同步新增
	twxAjax(THING, "QueryRecentPhoto", {
		treeId: treeId
	}, true, cb_success, cb_error);
}

function loadModelAll(treeId) {
	loadNum(treeId);
	loadPhotoSort(treeId);
	PieChart(treeId);
	loadQuality(treeId);
	loadRecentPhoto(treeId);
}

function loadAll() {
	loadModelSelect(function() {
		form.render('select');
		form.on('select(model-select)', function(data) {
			var treeId = data.value;
			loadModelAll(treeId);
		});
	});
	loadModelAll(-1);
}

function loadFoot() {
	var cb_success = function(res) {
		if (res.success) {
			var datas = res.data;
			renderFoot(datas);
		} else {
			layer.alert(res.msg);
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		layer.alert("查询型号进度出错！", {
			icon: 2
		});
	};
	//同步新增
	twxAjax(THING, "QueryModelProcess", {
		username: username
	}, true, cb_success, cb_error);
}

function renderFoot(datas) {
	var $table = $("#diagramContainer table");
	$table.empty();
	var $tr1 = $('<tr></tr>');
	var $tr2 = $('<tr></tr>');
	for (var i = 0; i < datas.length; i++) {
		var d = datas[i];
		var $div1 = $('<div class="start-point layui-form-select" id="td1-' + i + '"><span>' + d.num + '</span><i class="layui-edge"></i></div>');
		$div1.data("models", d.models);
		var $td1 = $('<td></td>');
		$td1.append($div1);
		$tr1.append($td1);
		var $div2 = $('<div class="end-point" id="td2-' + i + '"><div class="process-img"><img src="img/icon_single_board.png" ></div><div class="process-name">' + d.name + '</div></div>');
		var $td2 = $('<td></td>');
		$td2.append($div2);
		$tr2.append($td2);
	}

	$table.append($tr1).append($tr2);

	$(".start-point").click(function() {
		var models = $(this).data("models");
		var id = $(this).attr("id");
		var $i = $(this).find("i");
		var num = parseInt($(this).find('span').text());
		var isShow = $i.hasClass("select-i");
		$(".start-point i").removeClass("select-i");
		layer.closeAll();
		if (isShow) {
			// layer.closeAll();
		} else {
			if (num > 0) {
				$i.addClass("select-i");
				var t = '<table class="layui-table model-table">';
				for (var i = 0; i < models.length; i++) {
					t = t + '<tr><td>' + models[i] + '</td></tr>';
				}
				t = t + '</table>';
				layer.tips(t, '#' + id, {
					tips: [1, '#00003B'],
					skin: 'model-list',
					time: 0,
					success: function(layero, index) {
						if (models.length < 8) {
							$(".layui-layer-tips .layui-layer-content").css("overflow", "hidden");
						} else {
							$(".layui-layer-tips .layui-layer-content").css("overflow", "auto");
						}
					}
				});
			}
		}
	});
	var container = document.getElementById("diagramContainer");
	window.jsPlumb = jsPlumbBrowserUI.newInstance({
		container: container
	});

	$(".start-point").each(function(i, e) {
		var $e = $(e);
		var id = $e.attr("id");
		var nextId = "td1-" + (i + 1);
		var endId = "td2-" + i;

		jsPlumb.connect({
			source: document.getElementById(id),
			target: document.getElementById(nextId),
			anchors: ['Right', 'Left'],
			endpoints: ["Blank", "Blank"],
			// connector: ['Straight'],
			paintStyle: {
				strokeWidth: 1,
				stroke: '#00CDFF'
			}
		});

		jsPlumb.connect({
			source: document.getElementById(id),
			target: document.getElementById(endId),
			anchors: ['Bottom', 'Top'],
			endpoints: ["Blank", "Blank"],
			// connector: ['Straight'],
			paintStyle: {
				strokeWidth: 1,
				stroke: '#00CDFF'
			}
		});
	});

	// 监听容器元素的scroll事件
	container.addEventListener('scroll', function() {
		jsPlumb.repaintEverything();
	});

	// var body = document.getElementsByTagName("body")[0];
	// body.addEventListener('scroll', function() {
	// 	// 获取滚动条的位置
	// 	var scrollTop = body.scrollTop;
	// 	for (var i = 0; i < connectors.length; i++) {
	// 		var connector = connectors[i];
	// 		var oTop = $(connector).attr("oTop");
	// 		// connector.style.top = (oTop - scrollTop) + 'px';
	// 	}
	// });
}

layui.use(['layer', 'form', 'laydate', 'carousel', 'table', 'element'], function() {
	layer = layui.layer;
	form = layui.form;
	laydate = layui.laydate;
	table = layui.table;
	element = layui.element;
	carousel = layui.carousel;
	renderClick();
	loadAll();
	loadFoot();
});