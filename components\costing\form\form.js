var THING = "Thing.Fn.Costing";
var currentUser = sessionStorage.getItem("username");
var form, layer, util, table, laydate, upload;
layui.use(['form', 'util', 'table', 'laydate'], function() {
	form = layui.form;
	layer = layui.layer;
	util = layui.util;
	table = layui.table;
	laydate = layui.laydate;
	upload = layui.upload;

	renderFormTable();
	initFormTableTool();
	renderFormSearchForm();
});

/**
 * 加载表单列表
 */
function renderFormTable() {
	// 创建渲染实例
	table.render({
		elem: '#form-table',
		id: 'form-table',
		url: getUrl(THING, 'QueryForm'),
		toolbar: '#form-toolbar',
		defaultToolbar: ['filter', {
			title: '刷新',
			layEvent: 'form_reload',
			icon: 'layui-icon-refresh'
		}],
		height: 'full-50', // 最大高度减去其他容器已占有的高度差
		cellMinWidth: 80,
		page: true,
		limit: 15,
		limits: [15, 30, 45, 60, 75, 90, 100],
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'FORM_ID',
					width: 100,
					title: '表单主键',
					align: 'center'
				},
				{
					field: 'FORM_NAME',
					title: '表单名称',
					width: 300
				},
				{
					field: 'FORM_REMARK',
					title: '备注',
					width: 150
				},
				{
					field: 'CREATE_TIME',
					width: 160,
					title: '创建时间',
					templet: formatTableDate,
					align: 'center'
				},
				{
					fixed: 'right',
					title: '操作',
					width: 180,
					minWidth: 180,
					toolbar: '#form-rowbar',
					align: 'center'
				}
			]
		],
		done: function() {

		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});
}


/**
 * 加载表格工具事件
 */
function initFormTableTool() {
	// 触发单元格工具事件
	table.on('tool(form-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'edit') {
			editForm(data['TABLE_DATA'], data['FORM_ID']);
		} else if (obj.event === 'download') {
			var loading;
			$.fileDownload(flowPostUrl + "/form", {
				httpMethod: 'POST',
				data: {
					"act": "download",
					"formId": data['FORM_ID']
				},
				prepareCallback: function(url) {
					// loading = layer.msg("正在下载...", {
					// 	icon: 16,
					// 	shade: 0.3,
					// 	time: 0
					// });
				},
				abortCallback: function(url) {
					// layer.close(loading);
					layer.msg("导出异常！！");
				},
				successCallback: function(url) {
					// layer.close(loading);
				},
				failCallback: function(html, url) {
					// layer.close(loading);
					layer.msg("导出失败！！");
				}
			});
		} else if (obj.event === 'delete') {
			layer.confirm('确定要删除该表单吗？', {
				icon: 3
			}, function(lIndex) {
				twxAjax(THING, 'DeleteForm', {
					formId: data['FORM_ID']
				}, true, function(res) {
					layer.close(lIndex);
					if (res.success) {
						layer.msg(res.msg)
						reloadFormTable();
					} else {
						layer.alert(res.msg);
					}
				}, function(xhr, textStatus, errorThrown) {
					layer.alert('请求出错！', {
						icon: 2
					});
				});
			}, function(lIndex) {
				layer.close(lIndex);
			});
		}
	});

	// 工具栏事件
	table.on('toolbar(form-table)', function(obj) {
		var id = obj.config.id;
		var checkStatus = table.checkStatus(id);
		var othis = lay(this);
		switch (obj.event) {
			case 'add-btn':
				addForm();
				break;
			case 'form_reload':
				reloadFormTable();
				break;
		};
	});
}

/**
 * 编辑表单
 * @param {Object} tableData
 */
function editForm(tableData, formId) {
	layer.open({
		title: '编辑表单',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		zIndex: 1000,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: [$(window).width() + 'px', $(window).height() + 'px'],
		content: '<div id="handsontable" style="width:100%;height:100%;"></div>',
		cancel: function(index, layero) {
			if (confirm('确定要关闭么')) {
				layer.close(index);
			}
			return false;
		},
		btn: ['保存', '取消'],
		btn1: function(index, layero, that) {
			saveForm(formId);
		},
		btn2: function(index, layero, that) {
			if (confirm('确定要关闭么')) {
				layer.close(index);
			}
			return false;
		},
		success: function(layero, index) {
			renderForm(JSON.parse(tableData));
		}
	});
}
/**
 * 保存表单数据
 */
function saveForm(formId) {
	var tableData = window.hot.getData();
	var meta = HotUtil.myGetCellsMeta();
	var merged = window.hot.getPlugin('MergeCells').mergedCellsCollection.mergedCells;
	var headerRow = window.hot.getSettings().fixedRowsTop;
	var data = {
		tableData: tableData,
		merged: merged,
		meta: meta,
		headerRow: headerRow,
		colWidths: HotUtil.getColWidths(),
		rowHeights: HotUtil.getRowHieghts()
	};

	data = JSON.stringify(data);
	//替换英文引号为中文引号
	data = data.replace(/'(.[^']*)'/g, "‘$1’");

	var param = {};
	param.formId = formId;
	param.tableData = data;
	var cb_success = function(res) {
		if (res.success) {
			reloadFormTable();
			layer.closeAll();
			layer.msg(res.msg);
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	};
	var cb_error = function(res) {
		layer.alert("保存失败！");
	};
	twxAjax(THING, 'SaveForm', param, true, cb_success, cb_error);
}


/**
 * 重新加载
 */
function reloadFormTable() {
	table.reloadData('form-table');
}

function renderFormSearchForm() {
	form.render(null, 'form-table-form');
	// 搜索提交
	form.on('submit(form-table-search)', function(data) {
		var field = data.field; // 获得表单字段
		// 执行搜索重载
		table.reload('form-table', {
			page: {
				curr: 1 // 重新从第1页开始
			},
			where: field // 搜索的字段
		});
		return false;
	});
}

/**
 * 新增一个表单
 */
function addForm() {
	var fileFlag = false;
	layer.open({
		title: "新增表单",
		type: 1,
		area: ['460px', "450px"],
		content: '<div id="addContent" style="padding: 15px 0px 0px 0px;"></div>',
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		resize: false,
		btn: ['确定', '取消'],
		yes: function() {
			if (!fileFlag) {
				layer.alert('请选择需要导入的Excel文件!', {
					icon: 2
				});
				return false;
			}
			var formName = $("#formName").val();
			var formRemark = $("#formRemark").val();
			var creator = sessionStorage.getItem("username");
			uploadInst.config.url = flowPostUrl + '/form?act=add&formName=' + formName + '&creator=' + creator + '&formRemark=' + formRemark;
			$('#uploadStart').click();
		},
		btn2: function() {
			return true;
		},
		success: function() {
			var tpl = '<form class="layui-form" lay-filter="addForm">\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">表单名称:</label>\
										<div class="layui-input-block">\
											<input type="text" name="formName" id="formName" lay-verify="required" autocomplete="off" placeholder="请输入表单名称" style="width:330px" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">备注:</label>\
										<div class="layui-input-block">\
											<input type="text" name="formRemark" id="formRemark" autocomplete="off"style="width:330px" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">文件内容:</label>\
										<div class="layui-input-block">\
											<div class="layui-upload">\
												<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>\
												<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
											</div>\
										</div>\
									</div>\
									<div class="layui-form-item" id="selectedFile" style="display: none;">\
										<label class="fieldlabel layui-form-label">已选文件:</label>\
										<div class="layui-input-block">\
											<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
										</div>\
									</div>\
									<div class="layui-form-item" style="display:none;">\
										<center>\
											<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
											<button id="btn_cancel" class="layui-btn">取消</button>\
										</center>\
									</div>\
								</form>'
			$("#addContent").append(tpl);
			form.render(null, 'addForm');
			uploadInst = upload.render({
				elem: '#uploadChoice',
				url: flowPostUrl + '/form?act=add',
				auto: false,
				exts: 'xls|xlsx',
				field: 'file',
				bindAction: '#uploadStart',
				dataType: "json",
				choose: function(obj) {
					fileFlag = true;
					var files = obj.pushFile();
					obj.preview(function(index, file, result) {
						var filename = file.name;
						$("#selectedFile").show();
						$("#selectedFileName").text(filename);
					});
				},
				before: function(obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
					layer.load(); //上传loading
				},
				done: function(res, index, upload) {
					if (res.success) {
						layer.closeAll();
						layer.msg(res.msg);
						reloadFormTable();
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}
			});
		}
	});
}