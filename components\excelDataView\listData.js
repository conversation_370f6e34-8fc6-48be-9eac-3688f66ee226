var curInfo = {
	tableId: 'dataTable',
	tableType: '查看合集',
	listDataInfo: {}
};
curInfo.listDataInfo = parent.window.listDataInfo;
if (curInfo.listDataInfo.pageType == 1) {
	//页面类型 1表示采集管理页面 0 表示其他页面
	$("#list_table_table_tb").show();
} else {
	$("#list_table_table_tb").hide();
}
$(document).ready(function() {
	layui.use(['layer', 'form'], function() {
		//初始化easyui的表格
		initTableComp();
		initStateCheckBtn(layui);
		initDataRefBtn(layui);
		initProductRefBtn(layui);
		initParamBtn(layui);
		initDeleteBtn(layui);
		initChangeRefBtn(layui);
		//加载数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	});
});

//初始化状态确认按钮
var initStateCheckBtn = function(layui) {
	$('#list_table_stateCheck').bind('click', function() {
		stateCheck(layui, curInfo.tableId, curInfo.listDataInfo.dataTableName, curInfo.listDataInfo.datapkg, curInfo.listDataInfo.typeName, function() {
			queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
		});
	});
};

//初始化数据包关联按钮
var initDataRefBtn = function(layui) {
	$('#list_table_datapkgLink').bind('click', function() {
		reassociateProcessTree(layui, curInfo.tableId, curInfo.listDataInfo.dataTableName, curInfo.listDataInfo.datapkg, curInfo.listDataInfo.typeName, function() {
			queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
		});
	});
};

var initParamBtn = function(layui) {
	$('#list_table_param').bind('click', function() {
		editParam(layui, curInfo.tableId, curInfo.listDataInfo.dataTableName, function() {
			queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
		});
	});
}

//初始化产品结构树关联按钮
var initProductRefBtn = function(layui) {
	$('#list_table_procuct_link').bind('click', function() {
		productTreeRelation(layui, curInfo.tableId, curInfo.listDataInfo.dataTableName, function() {
			queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
		});
	});
};

//初始化更改单关联按钮
var initChangeRefBtn = function(layui) {
	if (curInfo.listDataInfo.listDataQuery.fileType == '现场问题处理单') {
		$('#list_table_link_change').removeClass('layui-hide');
		$('#list_table_link_change').bind('click', function() {
			linkListData(layui, curInfo.tableId, curInfo.listDataInfo.listDataQuery.processTreeId, function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			});
		});
	} else {
		$('#list_table_link_change').addClass('layui-hide');
	}

};

//初始化删除按钮
var initDeleteBtn = function(layui) {
	$('#list_table_delete').bind('click', function() {
		deleteDataList(layui, curInfo.tableId, curInfo.listDataInfo.dataTableName, curInfo.listDataInfo.datapkg, curInfo.listDataInfo.typeName, function() {
			queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
		});
	});
};

//初始化表格 - 
var initTableComp = function(layui) {
	var columns = listTableUtil.getColumns('all');
	if (curInfo.listDataInfo.pageType == 1) {
		columns[0].unshift({
			field: 'ck',
			checkbox: true
		});
	}

	$('#' + curInfo.tableId).datagrid({
		data: [],
		singleSelect: false,
		fitColumns: true,
		striped: true,
		// fit: true,
		height: 718,
		rownumbers: true,
		pagination: true,
		columns: columns,
		loadMsg: '正在加载数据...',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>',
		onLoadSuccess: function(data) {
			changeWidth(curInfo.tableId);
			if (curInfo.listDataInfo.pageType != 1) {
				$('#' + curInfo.tableId).datagrid('resize', {
					height: 758
				});
			}
		}
	});

	//初始化分页控件
	initPagination(curInfo.tableId, []);
};

//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var getFieldValue = function() {
	return {
		query: curInfo.listDataInfo.listDataQuery
	};
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	$('#' + curInfo.tableId).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
}


//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(res) {
		pageLoadFlag = true;
		totalRecords = res.data;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function() {};

	//传递参数
	var param = getFieldValue();
	twxAjax('Thing.Fn.ListData', 'QueryListDataCount', param, true, cb_success, cb_error);
};

//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + curInfo.tableId).datagrid('loading');
	initTotalRecords();
	var cb_success = function(res) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#' + curInfo.tableId).datagrid('loadData', res.data);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#' + curInfo.tableId).datagrid('loaded');
	};
	var cb_error = function() {
		$('#' + curInfo.tableId).datagrid('loaded');
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	//传递的参数
	var param = getFieldValue();
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.ListData', 'QueryListDataPage', param, true, cb_success, cb_error);
};

//初始化分页组件
var initPagination = function(tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {

		},
		onRefresh: function(pageNumber, pageSize) {
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	});
};