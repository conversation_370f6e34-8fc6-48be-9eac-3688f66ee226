/**
 * 过程结构树中的质量影像记录
 */
var QualityPhoto = function () {
	var othis = this;
	this.qualityPlanId = 0;
	//初始化layui对象
	layui.use(['layer', 'form', 'upload', 'table'], function () {
		layer = layui.layer;
		upload = layui.upload;
		table = layui.table;
		form = layui.form;
		device = layui.device();
	});
	//获取左侧选中的节点  默认选中根节点
	this.getSelTreeNode = function () {
		var ztreeObj = $.fn.zTree.getZTreeObj("dpTree");
		var selNodes = ztreeObj.getSelectedNodes();
		var treeNode;
		if (selNodes.length > 0) {
			treeNode = selNodes[0];
		} else {
			treeNode = ztreeObj.getNodeByParam("TREEID", 1, null);
		}
		return treeNode;
	};
	//初始化显示
	this.init = function () {
		var selNode = othis.getSelTreeNode();
		//判断是否为过程节点
		if ((selNode.NODETYPE == 'leaf') || (selNode.NODETYPE == 'dir') && (!selNode.ISPARENT)) {
			othis.hideMsg();
			othis.loadTypeSelect(selNode.TREEID);
		} else {
			othis.showMsg("请选择过程节点！");
		}
	};
	//显示提示消息 并且隐藏其他元素
	this.showMsg = function (msg) {
		$("#qualityPhotoMsg").text(msg).show();
		$('#qualityPhotoTypeDiv').hide();
		$('#qualityPhotoTbr').hide();
		$('#qualityPhotoDiv').hide();
		$('#photo_tabs').hide();
	};
	//隐藏提示消息 并且显示其他元素
	this.hideMsg = function () {
		$("#qualityPhotoMsg").text(msg).hide();
		$('#qualityPhotoTypeDiv').show();
		$('#qualityPhotoTbr').show();
		$('#qualityPhotoDiv').show();
		$('#photo_tabs').show();
	};
	this.showTableMsg = function (msg) {
		$('#qualityPhotoDiv').empty();
		$('#qualityPhotoDiv').append('<div style="float: left; width: 100%;color: red;padding-left: 15px;padding-top: 15px;">' + msg + '</div>');
	};
	//初始化表格上方的操作按钮
	this.initTbr = function (record, treeId) {

		//上传策划表
		$('#quality-photo-upload-plan').unbind("click").bind('click', function () {
			othis.uploadFile(record, treeId, "photo");
		});
		//下载策划表模板
		$('#quality-photo-download-planTpl').unbind("click").bind('click', function () {
			othis.downloadTplFile(record, treeId, "photo");
		});

	};

	//下载模板文件
	this.downloadTplFile = function (record, treeId, type) {
		var path = "";
		var name = "";
		if (type = 'photo') {
			path = record.photoPath;
			name = "影像记录策划";
		}
		if (path != undefined && path != '' && path != null) {
			var fileName = record.name + "_" + name + "表模板.xlsx";
			var url = fileHandlerUrl + "/first/phase/download/file";
			var form = $("<form></form>").attr("action", url).attr("method", "post");
			form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", fileName));
			form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", path));
			form.appendTo('body').submit().remove();
		} else {
			layer.alert("该节点未上传" + name + "表模板！", {
				icon: 2
			})
		}
	};
	//上传策划表
	this.uploadFile = function (record, treeId, type) {
		var name = "";
		var url = "";
		var fileDone;
		if (type = 'photo') {
			name = "影像记录策划";
			var params = '';
			params += 'type=2'; //代表上传的是策划表类型是影像记录
			params += '&tableConfigId=' + record.id;
			params += '&treeId=' + treeId;
			params += '&creator=' + sessionStorage.getItem('username');
			url = fileHandlerUrl + '/table/import/plan?' + params;
			fileDone = function (res, index, upload) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					othis.loadTableData(res.data, treeId, record.id, []);
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			};
		}
		var area = ['350px', '220px'];

		var fileFlag = false;

		layer.open({
			title: '导入' + record.name + name + '表',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: area,
			content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			success: function () {
				var addTpl = $("#uploadHtml")[0].innerHTML;
				$("#uploadContent").append(addTpl);
			},
			btn: ['确认', '取消'],
			yes: function () {
				if (!fileFlag) {
					layer.alert('请选择需要导入的excel文件!', {
						icon: 2
					});
					return false;
				}
				uploadInst.config.url = url;
				if (device.ie && device.ie < 10) {
					$("form[target]")[0].action = url;
				}
				$('#uploadStart').click();
			},
			btn2: function () {
				layer.closeAll();
			}
		});

		form.render(null, 'uploadForm');

		var uploadInst = upload.render({
			elem: '#uploadChoice',
			url: url,
			auto: false,
			accept: 'file',
			field: 'uploadFile',
			exts: 'xls|xlsx',
			bindAction: '#uploadStart',
			dataType: "json",
			choose: function (obj) {
				fileFlag = true;
				var o = obj.pushFile();
				var filename = '';
				for (var k in o) {
					var file = o[k];
					filename = file.name;
				}
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			},
			before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
				// layer.load(); //上传loading
			},
			done: fileDone
		});
		if (device.ie && device.ie < 10) {
			$("input[name='uploadFile']").change(function () {
				var filename = $(this).val();
				filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			});
		}
	};
	//根据数据和名称 获取对应的 名称、代号和批次号
	this.getInfo = function (cols, row) {
		var nameStr = "名称";
		var codeStr = "代号";
		var batchNumberStr = "要求批次号";
		var photoCountStr = "照片数量";
		var name = "",
			code = "",
			batchNumber = "",
			photoCount = 0;
		for (var i = 0; i < row.length; i++) {
			if (i == cols.indexOf(nameStr)) {
				name = row[i];
			} else if (i == cols.indexOf(codeStr)) {
				code = row[i];
			} else if (i == cols.indexOf(batchNumberStr)) {
				batchNumber = row[i];
			} else if (i == cols.indexOf(photoCountStr)) {
				photoCount = row[i];
			}
		}
		return {
			name: name,
			code: code,
			batchNumber: batchNumber,
			photoCount: photoCount
		}
	};

	//单行的质量数据确认
	this.singleLineConfirm = function (btn, treeId, tableConfigId) {
		var cols = $(btn).data("cols");
		var row = $(btn).data("row");
		var paramName = $(btn).data("paramName");
		var res = othis.getInfo(cols, row);
		if (res.photoCount == 0) {
			layer.alert("暂无照片，不可确认！", {
				icon: 2
			});
			return false;
		}
		var confirmer = sessionStorage.getItem('username');
		var cb_success = function (res) {
			if (res.success) {
				layer.msg(res.msg);
				othis.postTableData(tableConfigId, treeId);
			} else {
				layer.alert(res.msg);
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("确认质量影像记录出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "QualityDataConfirm", {
			name: res.name,
			code: res.code,
			batchNumber: res.batchNumber,
			tableConfigId: tableConfigId,
			treeId: treeId,
			paramName: paramName,
			confirmer: confirmer,
			type: 2
		}, false, cb_success, cb_error);

	};
	// 预览图片
	this.previewPhoto = function (tableConfigId, treeId, isConfirm) {
		var photos = contextMenuTarget.data("photos");
		var confirmer = contextMenuTarget.data("confirmer");
		layer.open({
			title: '查看图片',
			type: 1,
			area: ['1100px', '560px'],
			content: '<div id="previewContent"></div>',
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			cancel: function (index, layero) {
				if ($(".picView-magnify-list").data("delete")) {
					//如果删除过照片则需要在关闭预览窗口的时候刷新整个表格
					othis.postTableData(tableConfigId, treeId);
				}
			},
			success: function () {
				var $ul = $('<ul class="picView-magnify-list" style="padding: 30px"></ul>');
				for (var i = 0; i < photos.length; i++) {
					var p = photos[i];
					var photoName = p['PHOTO_NUMBER'];
					var photoPath = p['PHOTO_PATH'];
					var photoId = p['ID'];
					photoPath = "/File" + photoPath;
					var deleteDiv = '<div style="text-align: right;color:red;"><i class="layui-icon" photo-id="' + photoId + '" photo-path="' + p['PHOTO_PATH'] + '" style="cursor: pointer;font-size: 20px;">&#xe640;</i></div>';
					var li = '<li>\
								<a href="javascript:void(0)" data-magnify="gallery" data-group="g1" data-src="' + photoPath + '" data-caption="' + photoName + '">\
									<img src="' + photoPath + '">\
									<div style="text-align: center;font-size: 20px;">' + photoName + '</div>\
								</a>\
							</li>';
					var $li = $(li);
					//如果没有确认 可以删除影像记录
					if (!confirmer) {
						$li.prepend(deleteDiv);
					}
					$ul.append($li);
				}
				$('#previewContent').append($ul);

				if (!confirmer) {
					//删除事件
					$ul.find('i').click(function () {
						var $i = $(this);
						var photoPath = $i.attr("photo-path");
						var photoId = $i.attr("photo-id");
						twxAjax("Thing.Fn.SecondTable", "DeletePhoto", {
							id: photoId,
							photoPath: photoPath
						}, true, function (res) {
							if (res.success) {
								layer.msg(res.msg);
								$i.parent().parent().remove();
								$ul.data("delete", true);
							} else {
								layer.alert(res.msg);
							}
						}, function () {
							layer.alert("删除失败");
						});
					});
				}

				$('[data-magnify]').Magnify({
					Toolbar: [
						'prev',
						'next',
						'rotateLeft',
						'rotateRight',
						'zoomIn',
						'actualSize',
						'zoomOut'
					],
					keyboard: true,
					draggable: true,
					movable: true,
					modalSize: [800, 600],
					beforeOpen: function (obj, data) {
						console.log('beforeOpen')
					},
					opened: function (obj, data) {
						console.log('opened')
					},
					beforeClose: function (obj, data) {
						console.log('beforeClose')
					},
					closed: function (obj, data) {
						console.log('closed')
					},
					beforeChange: function (obj, data) {
						console.log('beforeChange')
					},
					changed: function (obj, data) {
						console.log('changed')
					}
				});
			}
		});
	};
	//上传照片
	this.uploadPhoto = function (params) {
		params.paramId = contextMenuTarget.data("paramId");
		params.name = contextMenuTarget.data("name");
		params.code = contextMenuTarget.data("code");
		params.batchNumber = contextMenuTarget.data("batchNumber");
		var tableName = contextMenuTarget.data("tableName");
		var photoIndex = 0;
		var photos = contextMenuTarget.data("photos");
		if (photos.length > 0) {
			photoIndex = Number(photos[photos.length - 1]['PHOTO_NUMBER'].split('-').reverse()[0]);
		}
		var tpl = '<div style="padding:12px;">\
						<div class="layui-upload" style="margin-bottom:2px;">\
							<button type="button" class="layui-btn layui-btn-normal" id="chooseFile">选择照片</button>\
							<button type="button" class="layui-btn" id="startUpload" style="display:none;">开始上传</button>\
						</div>\
						<table id="photo-table" lay-filter="photo-table"></table>\
					</div>';
		if (device.ie && device.ie < 10) {
			layer.open({
				title: '手动上传',
				type: 1,
				area: ['1100px', '560px'],
				content: tpl,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				resize: false,
				btn: ['上传', '取消'],
				yes: function () {
					var photoDatas = trimSpace(layui.table.cache["photo-table-id"]);
					if (photoDatas.length == 0) {
						layer.msg('请选择照片!');
						return false;
					}
					var flag = true;
					for (var i = 0; i < photoDatas.length; i++) {
						var d = photoDatas[i];
						if (d.photoName == '' || d.photoNumber == '') {
							flag = false;
							break;
						}
					}
					if (!flag) {
						layer.msg('照片名称和照片编号为必填项！');
						return false;
					} else {
						var datas = [];
						for (var i = 0; i < photoDatas.length; i++) {
							var obj = {};
							obj.tableConfigId = params.tableConfigId;
							obj.treeId = params.treeId;
							obj.paramId = contextMenuTarget.data("paramId");
							obj.name = contextMenuTarget.data("name");
							obj.code = contextMenuTarget.data("code");
							obj.batchNumber = contextMenuTarget.data("batchNumber");
							var d = photoDatas[i];
							obj.photoPath = d.photoPath;
							obj.photoFormat = d.photoFormat;
							obj.photoName = d.photoName;
							obj.photoNumber = d.photoNumber;
							obj.photoSize = d.photoSize;
							obj.creator = sessionStorage.getItem('username');
							datas.push(obj);
						}
						var cb_success = function (res) {
							if (res.success) {
								layer.closeAll();
								layui.table.cache["photo-table-id"] = [];
								layer.msg('上传成功！');
								othis.postTableData(params.tableConfigId, params.treeId);
							} else {
								layer.alert(res.msg);
							}
						}
						//请求失败的回调
						var cb_error = function (xhr, textStatus, errorThrown) {
							layer.alert("增加质量影像记录出错！", {
								icon: 2
							});
						};
						//同步新增
						twxAjax("Thing.Fn.SecondTable", "AddQualityPhotos", {
							datas: datas
						}, false, cb_success, cb_error);


					}
				},
				btn2: function () {
					layui.table.cache["photo-table-id"] = [];
					return true;
				},
				success: function () {
					var photoTable = table.render({
						elem: '#photo-table',
						data: [],
						height: 375,
						cellMinWidth: 80, //全局定义常规单元格的最小宽度
						id: 'photo-table-id',
						limit: 999,
						cols: [
							[{
								field: 'photoName',
								title: '照片名称',
								templet: '<div>{{d.photoName}}</div>',
								width: 200
							}, {
								field: 'photoNumber',
								title: '照片编号',
								templet: '<div>{{d.photoNumber}}</div>'
							}, {
								field: 'photoSize',
								title: '照片大小',
								templet: '<div>{{d.photoSize}}</div>',
								width: 100
							}, {
								field: 'index',
								hide: true
							}, {
								field: 'photoFormat',
								title: '照片格式',
								templet: '<div>{{d.photoFormat}}</div>',
								width: 100
							}, {
								field: 'photoPath',
								title: '照片地址',
								templet: '<div>{{d.photoPath}}</div>',
								hide: true
							}, {
								field: 'fileStatus',
								title: '状态',
								templet: '<div>{{d.fileStatus}}</div>',
								width: 100
							}, {
								field: 'operation',
								title: '操作',
								templet: '<div>\
												<a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>\
											</div>',
								width: 100
							}]
						]
					});
					table.on('tool(photo-table)', function (obj) {
						if (obj.event === 'del') {
							obj.del();
							layui.table.cache["photo-table-id"] = trimSpace(layui.table.cache["photo-table-id"]);
						};
					});

					var photoUploadIns = upload.render({
						elem: '#chooseFile',
						url: fileHandlerUrl + '/table/upload/photo',
						auto: false,
						accept: 'images',
						field: 'photo',
						bindAction: '#startUpload',
						dataType: "json",
						before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
							loadIndex = layer.load(); //上传loading
						},
						done: function (res, fileIndex) {
							if (res.success) {
								photoTableData.photoPath = res.data[0].photoPath;
								photoTableData.photoSize = res.data[0].photoSize;
							}
							photoTableData.fileStatus = res.msg;
							layui.table.cache["photo-table-id"].push(photoTableData);
							photoTable.reload({
								data: layui.table.cache["photo-table-id"]
							});
							photoUploadIns.reload();
							layer.close(loadIndex);
						}
					});

					$("input[name='photo']").change(function () {
						var filename = $(this).val();
						photoIndex++
						var o = {};
						o.photoName = filename.substring(filename.lastIndexOf('\\') + 1, filename.lastIndexOf('.'));
						o.photoNumber = params.name + "-" + tableName.substr(tableName.indexOf('）') + 1) + "-" + dealPrefix(photoIndex, 3);
						o.photoFormat = filename.substring(filename.lastIndexOf('.') + 1, filename.length);
						o.photoSize = '';
						photoTableData = o;
						$("#startUpload").click();
					});

				}
			});
		} else {
			layer.open({
				title: '上传照片',
				type: 1,
				area: ['1100px', '560px'],
				content: tpl,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				resize: false,
				btn: ['上传', '取消'],
				yes: function () {
					var photoDatas = trimSpace(layui.table.cache["photo-table-id"]);
					if (photoDatas.length == 0) {
						layer.msg('请选择照片!');
						return false;
					}
					var flag = true;
					for (var i = 0; i < photoDatas.length; i++) {
						var d = photoDatas[i];
						if (d.photoName == '' || d.photoNumber == '') {
							flag = false;
							break;
						}
					}
					if (!flag) {
						layer.msg('照片名称和照片编号为必填项！');
						return false;
					} else {
						$('#startUpload').click();
					}
				},
				btn2: function () {
					layui.table.cache["photo-table-id"] = [];
					existPhotos = {};
					return true;
				},
				success: function () {
					var photoTable = table.render({
						elem: '#photo-table',
						data: [],
						height: 375,
						cellMinWidth: 80, //全局定义常规单元格的最小宽度
						id: 'photo-table-id',
						limit: 999,
						cols: [
							[{
								field: 'photoName',
								title: '照片名称',
								templet: '<div>{{d.photoName}}</div>',
								width: 200
							}, {
								field: 'photoNumber',
								title: '照片编号',
								templet: '<div>{{d.photoNumber}}</div>'
							}, {
								field: 'photoSize',
								title: '照片大小',
								templet: '<div>{{d.photoSize}}</div>',
								width: 100
							}, {
								field: 'index',
								hide: true
							},
							{
								field: 'photoFormat',
								title: '照片格式',
								templet: '<div>{{d.photoFormat}}</div>',
								width: 100
							}, {
								field: 'operation',
								title: '操作',
								templet: '<div>\
										<a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>\
									</div>',
								width: 100
							}
							]
						]
					});
					table.on('tool(photo-table)', function (obj) {
						if (obj.event === 'del') {
							obj.del();
							if (device.ie && device.ie < 10) {

							} else {
								var index = obj.tr.find('td[data-field="index"]').children('div').text();
								layui.table.cache["photo-table-id"] = trimSpace(layui.table.cache["photo-table-id"]);
								removeArray('index', index, layui.table.cache["photo-table-id"]);
								delete existPhotos[index];
							}
						};
					});

					function getRowData(fileIndex) {
						var d = {};
						var datas = layui.table.cache["photo-table-id"];
						for (var i = 0; i < datas.length; i++) {
							if (datas[i].index == fileIndex) {
								d = datas[i];
							}
						}
						return d;
					}

					var photoUploadIns = upload.render({
						elem: '#chooseFile',
						url: fileHandlerUrl + '/table/upload/photo',
						auto: false,
						multiple: true,
						accept: 'images',
						field: 'photo',
						bindAction: '#startUpload',
						dataType: "json",
						before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
							loadIndex = layer.load(); //上传loading
						},
						allDone: function (obj) {
							//提示完成后，点击确定再刷新界面
							if (obj.aborted > 0) {
								layer.close(loadIndex);
								layer.msg('上传失败！');
							} else {
								layer.closeAll();
								layui.table.cache["photo-table-id"] = [];
								existPhotos = {};
								layer.msg('上传成功！');
								othis.postTableData(params.tableConfigId, params.treeId);
							}

						},
						choose: function (obj) {
							layui.table.cache["photo-table-id"] = trimSpace(layui.table.cache["photo-table-id"]); //重置表格数据
							var tableData = layui.table.cache["photo-table-id"];
							//将每次选择的文件追加到文件队列
							var files = obj.pushFile();
							existPhotos = files;

							function isInTable(key, tableData) {
								for (var i = 0; i < tableData.length; i++) {
									if (key == tableData[i].index) {
										return true;
									}
								}
								return false;
							}

							var allConut = Object.keys(existPhotos).length;
							for (var key in existPhotos) {
								if (!isInTable(key, tableData)) {
									var file = existPhotos[key];
									var fileName = file.name;
									var o = {};
									photoIndex++
									o.photoNumber = params.name + "-" + tableName.substr(tableName.indexOf('）') + 1) + "-" + dealPrefix(photoIndex, 3);
									o.photoName = fileName.substr(0, fileName.lastIndexOf('.'));
									o.photoFormat = fileName.substr(fileName.lastIndexOf('.') + 1);
									o.photoSize = (file.size / 1024).toFixed(1) + 'kb';
									o.index = key;
									o.file = file;
									layui.table.cache["photo-table-id"].push(o);
									photoTable.reload({
										data: layui.table.cache["photo-table-id"]
									});

								}
							}
							photoUploadIns.reload();
						},
						done: function (res, fileIndex) {
							if (res.success) {
								var rowData = getRowData(fileIndex);
								params.photoPath = res.data[0].photoPath;
								params.photoFormat = rowData.photoFormat;
								params.photoName = rowData.photoName;
								params.photoNumber = rowData.photoNumber;
								params.photoSize = rowData.photoSize;
								params.creator = sessionStorage.getItem('username');
								var cb_success = function (res) {
									if (res.success) {

									} else {
										layer.alert(res.msg);
									}
								}
								//请求失败的回调
								var cb_error = function (xhr, textStatus, errorThrown) {
									layer.alert("增加质量影像记录出错！", {
										icon: 2
									});
								};
								//同步新增
								twxAjax("Thing.Fn.SecondTable", "AddQualityPhoto", {
									data: params
								}, false, cb_success, cb_error);
							} else {
								layer.alert(res.msg);
							}
						}
					});
				}
			});
		}
	};
	//加载质量数据确认表
	this.loadTableData = function (data, treeId, tableConfigId, signs) {
		$("#qualityPhotoDiv").empty();
		$("#qualityPhotoDiv").css('height', (windowH - 90) + 'px');
		$("#qualityPhotoDiv").show();
		var statusStr = "状态";
		var confirmerStr = "确认人";
		var actualPhotoStr = "实际照片";
		var $table = $('<table class="my-table layui-table"></table>');
		for (var i = 0; i < data.length; i++) {
			var table = data[i];
			var datas = table.datas;
			var cols = table.cols;
			var statusIndex = cols.indexOf(statusStr);
			var confirmerIndex = cols.indexOf(confirmerStr);
			var photoIndex = cols.indexOf(actualPhotoStr);
			var relationParam = table.relationParam;
			//添加合并行
			$table.append('<tr><td class="table-name" colspan=' + cols.length + '>' + table.name + '</td></tr>');
			//添加表头
			var $headerTr = $('<tr></tr>');
			for (var j = 0; j < cols.length; j++) {
				$headerTr.append('<td class="table-head" align="center">' + cols[j] + '</td>');
			}
			$table.append($headerTr);
			//添加表数据
			for (var j = 0; j < datas.length; j++) {
				var $tr = $('<tr></tr>');
				var d = datas[j];
				for (var x = 0; x < d.length; x++) {

					if (x == confirmerIndex) {
						//确认人一栏  添加确认按钮
						if (d[x] == '') {
							var $td = $('<td align="center"></td>');
							var $btn = $('<button class="layui-btn layui-btn-sm layui-btn-normal">确认</button>');
							$btn.data('row', d).data('cols', cols).data('paramName', relationParam["PARAM_NAME"]);
							$btn.bind('click', function () {
								othis.singleLineConfirm(this, treeId, tableConfigId);
							});
							$td.append($btn);
							$tr.append($td);

						} else {
							$tr.append('<td align="center">' + d[x] + '</td>');
						}
					} else if (x == statusIndex) {
						//状态一栏 可编辑
						if (d[x] != '冻结') {
							var $td = $('<td align="center" data-field="status">' + d[x] + '</td>');
							$td.data('row', d).data('cols', cols).data('paramName', relationParam["PARAM_NAME"]);;
							$tr.append($td);
						} else {
							$tr.append('<td align="center">' + d[x] + '</td>');
						}

					} else if (x == photoIndex) {
						var menus = [];
						if (d[d.length - 1] == '') {
							var uploadMenu = {
								text: "上传照片",
								icon: '../dataTree/images/upload.png',
								callback: function (btn) {
									othis.uploadPhoto({
										treeId: treeId,
										tableConfigId: tableConfigId
									});
								}
							};
							menus.push(uploadMenu);
						}
						var photoStr = "";
						var photos = [];
						if (d[x] != '[]') {
							photos = JSON.parse(d[x]);
							menus.push({
								text: "查看照片",
								icon: '../dataTree/images/view.png',
								callback: function () {
									othis.previewPhoto(tableConfigId, treeId);
								}
							});
							for (var p = 0; p < photos.length; p++) {
								var photo = photos[p];
								photoStr += ";<br>" + photo['PHOTO_NUMBER'];
							}
							photoStr = photoStr.substr(5);
						}
						var $td = $('<td align="center">' + photoStr + '</td>');
						$td.data('photos', photos);
						$td.data('tableName', table.name);
						$td.data("confirmer", d[confirmerIndex]);
						$td.data('paramId', relationParam["ID"]);
						var info = othis.getInfo(cols, d);
						$td.data('name', info.name);
						$td.data('code', info.code);
						$td.data('batchNumber', info.batchNumber);
						$tr.append($td);
						if (menus.length > 0) {
							$td.contextMenu({
								width: 100,
								menu: menus,
								target: function (ele) {
									contextMenuTarget = ele;
								}
							});
						}
					} else {
						$tr.append('<td align="center">' + d[x] + '</td>');
					}
				}
				$table.append($tr);
			}
		}
		$("#qualityPhotoDiv").append($table);

		$('#qualityPhotoDiv td[data-field="status"]').dblclick(function () {
			var $td = $(this);
			// 根据表格文本创建文本框 并加入表表中--文本框的样式自己调整
			var text = $td.text();
			$td.text("");
			var statusArr = ['监测', '固封', '冻结'];
			var $form = $('<div class="layui-form"></div>');
			var $select = $('<select name="status" style="width:60px;" lay-filter="status"></select>');
			for (var m = 0; m < statusArr.length; m++) {
				if (text == statusArr[m]) {
					$select.append('<option value = "' + statusArr[m] + '" selected >' + statusArr[m] + '</option>');
				} else {
					$select.append('<option value = "' + statusArr[m] + '">' + statusArr[m] + '</option>');
				}
			}
			$form.append($select);
			$td.append($form);
			form.render('select');
			$form.find('.layui-form-select').css('width', '80px');
			form.on('select(status)', function (data) {
				$form.remove();
				$td.text(data.value);
				if (text != data.value) {
					var cols = $td.data("cols");
					var row = $td.data("row");
					var paramName = $td.data("paramName");
					var info = othis.getInfo(cols, row);

					var cb_success = function (res) {
						if (res.success) {
							layer.msg(res.msg);
							othis.postTableData(tableConfigId, treeId);
						} else {
							layer.alert(res.msg);
						}
					}
					//请求失败的回调
					var cb_error = function (xhr, textStatus, errorThrown) {
						layer.alert("更新质量数据状态出错！", {
							icon: 2
						});
					};
					twxAjax("Thing.Fn.SecondTable", "UpdateQualityDataStatus", {
						name: info.name,
						code: info.code,
						batchNumber: info.batchNumber,
						tableConfigId: tableConfigId,
						treeId: treeId,
						paramName: paramName,
						status: data.value,
						creator: sessionStorage.getItem('username'),
						type: 2
					}, false, cb_success, cb_error);
				}
			});
		});


		var $signDiv = $('<div class="layui-row"></div>');
		var $innerDiv = $('<div class="layui-col-md6"></div>');
		var $outerDiv = $('<div class="layui-col-md6"></div>');
		var $innerBtn = $('<button class="layui-btn layui-btn-sm">所内人员签字</button>');
		var $outerBtn = $('<button class="layui-btn layui-btn-sm layui-btn-normal">外所人员签字</button>');
		$innerBtn.bind('click', function () {
			othis.sign(1);
		});

		$outerBtn.bind('click', function () {
			othis.sign(2);
		});

		$innerDiv.append($innerBtn);
		$outerDiv.append($outerBtn);
		$signDiv.append($innerDiv).append($outerDiv);

		var $signImgDiv = $('<div class="layui-row"></div>');
		var $innerImgDiv = $('<div class="layui-col-md6" style = "height:1px;" id="photo-innerImgDiv"></div>');
		var $outerImgDiv = $('<div class="layui-col-md6" id="photo-outerImgDiv"></div>');
		for (var i = 0; i < signs.length; i++) {
			var sign = signs[i];
			var type = sign['TYPE'];
			var img = sign['IMG'];
			var $img = $('<img>').attr('src', img).attr('class', 'sign-img');
			if (type == 1) {
				$innerImgDiv.append($img);
			} else {
				$outerImgDiv.append($img);
			}
		}
		$signImgDiv.append($innerImgDiv).append($outerImgDiv);
		$("#qualityPhotoDiv").append($signDiv).append($signImgDiv);
	};

	this.sign = function (type) {
		var title = type == 1 ? '所内签名' : '所外签名';
		var imgDiv = type == 1 ? 'photo-innerImgDiv' : 'photo-outerImgDiv';
		if (device.ie && device.ie < 10) {
			var objHtml = '<object id="DWebSignSeal" style="display:none;" classid="CLSID:77709A87-71F9-41AE-904F-886976F99E3E"\
				        codebase="http://www.XXX.com.cn/demo/websign/WebSign.ocx#version=4,4,9,6" width="100" height="100"></object>';
			if ($("body").find("#DWebSignSeal").length == 0) {
				$('body').prepend(objHtml);
			}
			var strSealName = document.all.DWebSignSeal.HandWrite(8, 0, "signName");
			document.all.DWebSignSeal.ShowWebSeals();
			var data = document.all.DWebSignSeal.GetSealBmpString(strSealName, "jpg");
			var dataUrl = "data:image/jpg;base64," + data;
			var $img = $('<img>').attr('src', dataUrl).attr('class', 'sign-img');
			// document.all.DWebSignSeal.GetSealBmpToFile(strSealName, "jpg", "C:\\signTest\\test.jpg");
			document.all.DWebSignSeal.DelSeal(strSealName);
			var cb_success = function (res) {
				if (res.success) {
					$("#" + imgDiv).prepend($img);
					layer.closeAll();
					layer.msg('签名成功！');
				} else {
					layer.alert(res.msg);
				}
			}
			//请求失败的回调
			var cb_error = function (xhr, textStatus, errorThrown) {
				layer.alert("签名出错！", {
					icon: 2
				});
			};
			//同步新增
			twxAjax("Thing.Fn.SecondTable", "AddQualitySign", {
				qualityPlanId: othis.qualityPlanId,
				type: type,
				img: dataUrl,
				creator: sessionStorage.getItem('username')
			}, false, cb_success, cb_error);
		} else {
			layer.open({
				title: title,
				type: 1,
				area: ['900px', '560px'],
				content: '<div id = "signContent"></div>',
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				resize: false,
				btn: ['确定', '重签', '取消'],
				yes: function () {
					var dataUrl = $('.js-signature').eq(0).jqSignature('getDataURL');
					var $img = $('<img>').attr('src', dataUrl).attr('class', 'sign-img');

					var cb_success = function (res) {
						if (res.success) {
							$("#" + imgDiv).prepend($img);
							layer.closeAll();
							layer.msg('签名成功！');
						} else {
							layer.alert(res.msg);
						}
					}
					//请求失败的回调
					var cb_error = function (xhr, textStatus, errorThrown) {
						layer.alert("签名出错！", {
							icon: 2
						});
					};
					//同步新增
					twxAjax("Thing.Fn.SecondTable", "AddQualitySign", {
						qualityPlanId: othis.qualityPlanId,
						type: type,
						img: dataUrl,
						creator: sessionStorage.getItem('username')
					}, false, cb_success, cb_error);
				},
				btn2: function () {
					$('.js-signature').eq(0).jqSignature('clearCanvas');
					return false;
				},
				btn3: function () {
					return true;
				},
				success: function () {
					var tpl = '<div class="js-signature" data-width="900" data-height="450" data-border="1px solid #333"\
										data-line-color="#000" data-line-width="3" data-auto-fit="true">\
									</div>'
					$("#signContent").append(tpl);
					$('.js-signature').jqSignature();

				}
			});
		}
	};
	//导出质量影像记录确认表excel
	this.exportQualityPhotoExcel = function (tableConfigId, treeId) {
		var loading;
		var url = fileHandlerUrl + "/table/quality/photo/export";
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"treeId": treeId,
				"tableConfigId": tableConfigId,
				"fileName": $('#qualityPhotoType').combobox("getValue") + "质量影像记录确认表"
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				layer.close(loading);
			},
			failCallback: function (html, url) {
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});
	};
	//从服务器请求质量数据
	this.postTableData = function (tableConfigId, treeId) {
		var cb_success = function (res) {
			if (res.success) {
				othis.loadTableData(res.data, treeId, tableConfigId, res.signs);
				othis.qualityPlanId = res.id;
				//下载策划表
				$('#quality-photo-download-plan').show().unbind("click").bind('click', function () {
					downloadFile(res.filepath, res.filename);
				});
				//导出质量表
				$('#quality-photo-export').show().unbind("click").bind('click', function () {
					othis.exportQualityPhotoExcel(tableConfigId, treeId);
				});
			} else {
				othis.qualityPlanId = 0;
				$('#quality-photo-download-plan').hide();
				$('#quality-photo-export').hide();
				othis.showTableMsg(res.msg);
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("加载质量影像记录出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "QueryProcessQualityPhotoData", {
			treeId: treeId,
			tableConfigId: tableConfigId
		}, false, cb_success, cb_error);
	};

	//初始化汇总表
	this.renderSummaryTable = function (cols, datas) {
		$('#photoSummaryTable').datagrid({
			data: datas,
			columns: dealGridColumns(cols),
			height: windowH - 130,
			singleSelect: true,
			remoteSort: false,
			// pagination: true,
			emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
			loadMsg: '正在加载数据...',
			striped: false,
			onLoadSuccess: function (data) {
				changeWidth('photoSummaryTable');
				$("#photoSummaryDiv .datagrid-body").css("overflow-x", "auto");
				$('#photoSummaryTable').datagrid('loaded');
			}
		});
	};

	//请求汇总表数据
	this.postSummaryData = function (treeId, tableConfigId) {
		var cb_success = function (data) {
			if (data.success) {
				$("#photoSummaryDiv").empty().append('<div id="photoSummaryTable" style="float: left;" data-options="border:false"></div>');
				othis.renderSummaryTable(data.data.gridCols, data.data.gridDatas);
			} else {
				$("#photoSummaryDiv").empty().append('<div style="float: left; width: 100%;color: red;padding-left: 30px;padding-top: 5px;">' + data.msg + '</div>');
			}
		};
		var cb_error = function () {
			layer.alert("汇总表获取失败！", {
				icon: 2
			});
		};
		twxAjax('Thing.Fn.SecondTable', 'QueryQualityPhotoSummary', {
			tree_id: treeId,
			table_config_id: tableConfigId
		}, true, cb_success, cb_error);
	};

	this.renderPhotoTabs = function (treeId, tableConfigId) {
		$('#photo_tabs').tabs({
			onSelect: function (title, index) {
				var tab = $('#photo_tabs').tabs('getTab', index);
				var tableId = tab.panel('options').tableId;
				if (tableId == 'photo_summary_tab') {
					othis.postSummaryData(treeId, tableConfigId);
				}
			}
		});
	};

	//加载质量数据类型下拉框
	this.loadTypeSelect = function (treeId) {
		var cb_success = function (data) {
			if (data.array.length > 0) {
				$('#qualityPhotoType').combobox({
					data: data.array,
					valueField: 'name',
					textField: 'name',
					editable: false,
					width: 300,
					panelHeight: 400,
					onSelect: function (record) {
						othis.initTbr(record, treeId);
						othis.postTableData(record.id, treeId);
						othis.renderPhotoTabs(treeId, record.id);
						othis.postSummaryData(treeId, record.id);
					}
				});
				$('#qualityPhotoType').combobox("select", data.array[0].name);
			} else {
				othis.showMsg("暂无质量影像记录！");
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("加载质量影像记录类型下拉框出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "GetQualityPhotoTypeByTreeId", {
			treeId: treeId
		}, false, cb_success, cb_error);
	};

	this.init();
}