/** 配置信息 */
var twxserver = 'http://twx:8011';
var twxserverport = '8080';
// var twxAppKey = 'c3612c84-5322-45a5-90a5-b61057d09d78';
var twxAppKey = '8ea8ace9-1167-4417-939f-a7ddc58d9429';
//ThingWorx用户Session认证
var loginApi = twxserver + '/Thingworx/Things/Thing.UserLogin/Services/GetIdentifier?method=POST&Accept=application/json';
//ThingWorx用户Session注销
var logoutApi = twxserver + '/Thingworx/Resources/CurrentSessionInfo/Services/TerminateUserSessions?method=POST';

//函数调用的尾缀
var serviceSuffix = 'method=POST&Accept=application/json&appKey=' + twxAppKey;

var fileHandlerUrl = 'http://' + location.hostname + ":7081";
var fileHandlerUrl1 = '/FileHandle';