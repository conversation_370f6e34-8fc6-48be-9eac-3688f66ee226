layui.extend({
	utils: 'components/js/utils' // {/}的意思即代表采用自有路径，即不跟随 base 路径
}).use(['form', 'utils', 'util'], function () {
	var form = layui.form,
		utils = layui.utils,
		util = layui.util;
	//表单验证
	form.verify({
		loginname: function (value, item) {
			if (value == '') {
				return '用户名不能为空';
			}
		},

		loginpass: function (value, item) {
			if (value == '') {
				return '密码不能为空';
			}
		}
	});

	form.render();

	var loginSucessAfter = function (data) {
		//记录信息到sessionStorage
		st.s('fullname', data.fullname);
		st.s('username', data.username);
		st.s('workno', data.workno);
		st.s('roleid', data.roleid);
		st.s('secLevel', data.secLevel);
		st.s('rolename', data.rolename);
		st.s('pwd', data.pwd);
		st.s('modelList', data.modelList);
		st.s('previewUrl', data.previewUrl);
		st.s('updatePwdTime', data.updatePwdTime);
		initUserIp();
		st.s('myIp', myIp);
		var userIp = data.ip;
		if (myIp != '') {
			if (userIp != undefined && userIp != null && userIp != '') {
				if (userIp != myIp) {
					layer.alert('用户ip与本机ip不一致，禁止登录', {
						icon: 2
					});
					return false;
				}
			}
		}

		//ThingWorx会话认证
		utils.twxconnect(function () {
			logRecord('登录', '登录', 1);
			sessionStorage.setItem('twxsession', true);
			location.href = "index.html?_dc=" + (new Date().getTime());
		});
	};

	var caLogin = function (username) {
		//进行ThingWorx和用户的认证
		var cb_success = function (data, textStatus, jqXHR) {
			if (data.success === false) {
				layer.msg(data.message, {
					icon: 2,
					anim: 6
				});
				logRecord('登录', data.message + '登录用户名:' + username, 0);
				return;
			}
			loginSucessAfter(data);
		};
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.msg('登录失败', {
				icon: 2,
				anim: 6
			});
			logRecord('登录', '登录失败', 0);
		};


		//同步方法调用
		utils.twxajax('Thing.UserLogin', 'caLogin', {
			username: username
		}, cb_success, cb_error);
	}
	$("#forgetPwd").click(function () {
		layer.msg('请联安全管理员!', {
			icon: 0,
			anim: 6
		});
	});
	form.on('checkbox(caLogin)', function (data) {
		if (data.elem.checked) {
			$("input[name='username']").removeAttr('lay-verify');
			$("input[name='password']").removeAttr('lay-verify');
		} else {
			$("input[name='username']").attr('lay-verify', 'loginname|required');
			$("input[name='password']").attr('lay-verify', 'loginpass|required');
		}
	});

	form.on('submit(LAY-user-login-submit)', function (data) {
		if ($("#caLogin")[0].checked) {
			if (!(navigator.userAgent.indexOf("Trident") > -1 || navigator.userAgent.indexOf("MSIE") > -1)) {
				layer.msg('当前浏览器暂不支持usb-key登录，请使用IE8及以上版本登录！', {
					icon: 2,
					anim: 6
				});
				return false;
			}
			// if (!usb_ocx.VgetnameFirst) {
			// 	layer.msg('请检查浏览器是否安装usb-key插件!', {
			// 		icon: 2,
			// 		anim: 6
			// 	});
			// 	return false;
			// }
			rtn = usb_ocx.VgetnameFirst("", "1");

			if (rtn == "#2") {
				layer.msg('暂未检测到智能卡,请刷新重试或者使用其它方式登录!', {
					icon: 2,
					anim: 6
				});
				window.location.href = window.location.href;
				return false;
			}
			// var keyVal = "154\\812a\\admin";
			var keyVal = rtn;
			var valArr = keyVal.split("\\");
			var username = valArr[valArr.length - 1];
			caLogin(username);

			return false;
		} else {

			var param = data.field;
			var errorUsername = param.username;
			//提交表单认证
			param.type = 1;
			//进行ThingWorx和用户的认证
			var cb_success = function (data, textStatus, jqXHR) {
				if (data.success === false) {
					var errorNum = st.a(errorUsername);
					if (errorNum == null) {
						errorNum = 1;
					} else {
						errorNum++;
					}
					st.s(errorUsername, errorNum);
					if (errorNum == 5) {
						st.s(errorUsername + 'lockTime', new Date().getTime());
					}
					layer.msg('密码错误' + errorNum + '次,超过5次之后将冻结用户!', {
						icon: 2,
						anim: 6
					});
					logRecord('登录', '登录用户名:' + param.username + ',密码错误' + errorNum + '次', 0);
					return;
				}
				loginSucessAfter(data);
			};
			var cb_error = function (xhr, textStatus, errorThrown) {
				layer.msg('登录失败', {
					icon: 2,
					anim: 6
				});
				logRecord('登录', '登录失败', 0);
			};
			var errorNum1 = st.a(errorUsername);
			if (errorNum1 == null || errorNum1 < 5) {
				//同步方法调用
				utils.twxajax('Thing.UserLogin', 'login', param, cb_success, cb_error);
			} else {
				var lockTime = st.a(errorUsername + 'lockTime');
				var currentTime = new Date().getTime(); //更新当前时间
				var lockTimeOut = 10 * 60 * 1000; //设置超时时长： 10分钟
				var timeDifference = currentTime - lockTime;
				if (timeDifference < lockTimeOut) {
					layer.msg('您的账户密码输入错误5次,请' + (10 - Math.round(timeDifference / 60 / 1000)) + '分钟后再试!', {
						icon: 2,
						anim: 6
					});
				} else {
					st.s(errorUsername, 0);
					utils.twxajax('Thing.UserLogin', 'login', param, cb_success, cb_error);
				}
			}
			return false;
		}
	});

	var userName = getQueryString('userName');
	if (userName != null) {
		caLogin(userName);
	}

	function getBpmTaskInfo(bpmTaskId) {
		var taskInfo = null;
		twxAjax('Thing.Util.SignPushTask', 'QueryTaskInfoById', { taskId: bpmTaskId }, false, function (res) {
			if (res.success) {
				taskInfo = res.data;
			} else {
				// {{ wanghq: Add - 增加错误信息提示，提升用户体验 }}
				console.error('获取BPM任务信息失败: ' + (res.msg || '未知错误'));
				// 可选：显示用户友好的错误提示
				if (typeof layer !== 'undefined') {
					layer.alert('获取任务信息失败: ' + (res.msg || '请检查任务ID是否正确'));
				}
				taskInfo = null;
			}
		});
		return taskInfo;
	}

	var bpmTaskId = getQueryString('bpmTaskId');
	if (bpmTaskId != null) {
		var taskInfo = getBpmTaskInfo(bpmTaskId);
		if (taskInfo != null) {
			userName = taskInfo.USER_NAME;
			sessionStorage.clear();
			st.s('bpmTaskInfo', JSON.stringify(taskInfo));
			caLogin(userName);
		}
	}
});