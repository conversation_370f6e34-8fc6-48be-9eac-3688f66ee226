<head>
	<meta http-equiv="content-type" content="txt/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="stylesheet" href="../../../plugins/layui/css/layui.css">
	<!-- <link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->
	<link rel="stylesheet" href="../../../css/icon.css">

	<script src="../../../plugins/layui/layui.js"></script>
	<!-- <script src="../../../plugins/layui/layui_exts/xm-select.js"></script> -->
	<link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../../plugins/easyui/jquery.min.js"></script>
	<script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
	<script src="../../js/config/twxconfig.js"></script>
	<script src="../../js/util.js"></script>
	<script src="../../js/logUtil.js"></script>

	<script src="../../js/intercept.js"></script>
	<title>用户管理</title>
	<style>
		.layui-form-label {
			width: 100px;
		}
		.layui-btn+.layui-btn {
			margin-left: 1px;
		}

		/* 弹窗不加载滚动条 */

		.layui-layer-page .layui-layer-content {
			overflow: visible !important;
		}
	</style>
</head>

<body>
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div data-options="region:'west',split:true,collapsible:false" title="用户管理" style="width:40%;">
			<div id="userTable_tb" style="padding: 5px;">
				<input class="easyui-textbox" id="searchUser_fullname" data-options="prompt:'全名'" style="width:110px;">
				<!-- <input type="text" id="searchUser_fullname" placeholder="全名" class="layui-input" style="width:110px;display: inline;height: 30px;"/> -->
				<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="searchUser_linkBtn">
					<i class="layui-icon">&#xe615;</i> 搜索
				</button>
				<button type="button" class="layui-btn layui-btn-sm" id="addUser_linkBtn">
					<i class="layui-icon">&#xe608;</i> 添加
				</button>
				<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm" id="editUser_linkBtn">
					<i class="layui-icon">&#xe642;</i> 编辑
				</button>
				<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="deleteUser_linkBtn">
					<i class="layui-icon">&#xe640;</i> 删除
				</button>
				<button type="button" class="layui-btn layui-btn-sm" style="background-color: #0070a9;" id="downloadUser_linkBtn">
					<i class="layui-icon">&#xe601;</i> 导出
				</button>
				<button type="button" class="layui-btn layui-btn-sm" style="background-color: burlywood;" id="downloadTpl_linkBtn">
					<i class="layui-icon">&#xe601;</i> 模板
				</button>
				<button type="button" class="layui-btn layui-btn-sm" style="background-color: coral;" id="import_linkBtn">
					<i class="layui-icon">&#xe681;</i> 导入
				</button>
				<button type="button" class="layui-btn layui-btn-sm" style="background-color: #009688;" id="syncBpmUser_linkBtn">
					<i class="layui-icon">&#xe669;</i> 同步BPM
				</button>
			</div>
			<div id="userTable" data-options="border:false"></div>
		</div>
		<div data-options="split:true,region:'center'" id="role" title="所属角色">
			<div id="roleTable_tb" style="padding: 5px;">
				<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="assignrole">
					<i class="layui-icon">&#xe608;</i> 分配角色
				</button>
				<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="removerole">
					<i class="layui-icon">&#xe640;</i> 删除分配
				</button>
			</div>
			<div id="roleTable" data-options="border:false"></div>
		</div>
		<div data-options="split:true,region:'east',collapsible:false" id="model" title="型号权限" style="width:20%;">
			<div id="modelTable_tb" style="padding: 5px;">
				<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="assignmodel">
					<i class="layui-icon">&#xe608;</i> 分配
				</button>
				<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="removemodel">
					<i class="layui-icon">&#xe640;</i> 删除分配
				</button>
			</div>
			<div id="modelTable" data-options="border:false"></div>
		</div>
	</div>
</body>


<script type="text/html" id="userInfoForm">
	<form id="userForm" class="layui-form" lay-filter="userinfo" style="margin:10px">
		<div class="layui-form-item" style="display: none;">
			<div class="layui-input-block">
				<input type="text" name="userid" value="">
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">用户名</label>
				<div class="layui-input-inline">
					<input type="text" name="username" placeholder="用户名必填" lay-reqtext="用户名是必填项" old="" lay-verify="userNameRepeat" autocomplete="off" class="layui-input" />
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">全名</label>
				<div class="layui-input-inline">
					<input type="text" name="fullname" placeholder="请输入中文名" lay-verify="userFullNameRepeat" old="" autocomplete="off" class="layui-input" />
				</div>
			</div>
		</div>
		<div class="layui-form-item layui-hide">
			<div class="layui-inline">
				<label class="layui-form-label">密码</label>
				<div class="layui-input-inline">
					<input type="password" name="password" placeholder="请输入密码" lay-verify="required" old="" autocomplete="off" class="layui-input" value="1" />
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">确认密码</label>
				<div class="layui-input-inline">
					<input type="password" name="confirmpwd" placeholder="请输入确认密码" lay-verify="confirmpwd" old="" autocomplete="off" class="layui-input" value="1" />
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">性别</label>
				<div class="layui-input-inline">
					<input type="radio" name="sex" value="男" title="男" old="" checked="">
					<input type="radio" name="sex" value="女" title="女" old="">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">密级</label>
				<div class="layui-input-inline">
					<select name="usersecuritylevel" id="securitylevel" old="">

					</select>
				</div>
			</div>
		</div>
		<!-- <div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">电话</label>
				<div class="layui-input-inline">
					<input type="text" name="phone" placeholder="请输入电话" old='' lay-verify="phone" autocomplete="off" class="layui-input" />
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">邮箱</label>
				<div class="layui-input-inline">
					<input type="text" name="email" placeholder="请输入邮箱" old='' lay-verify="email" autocomplete="off" class="layui-input" />
				</div>
			</div>
		</div> -->
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">IP地址</label>
				<div class="layui-input-inline">
					<input type="text" name="ip" placeholder="请输入IP地址" old='' lay-verify="ip" autocomplete="off" class="layui-input" />
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">单位</label>
				<div class="layui-input-inline">
					<select name="unit" id="unit" old="">

					</select>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">部门</label>
				<div class="layui-input-inline">
					<select name="department" id="department" old="">

					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">岗位</label>
				<div class="layui-input-inline">
					<select name="job" id="job" old="">

					</select>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">关联用户名</label>
				<div class="layui-input-inline">
					<input type="text" name="code" placeholder="请输入关联用户名" old='' autocomplete="off" class="layui-input" />
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">工号</label>
				<div class="layui-input-inline">
					<input type="text" name="workno" placeholder="请输入工号" old='' autocomplete="off" class="layui-input" />
				</div>
			</div>
			<!-- <div class="layui-inline">
				<label class="layui-form-label">邮箱</label>
				<div class="layui-input-inline">
					<input type="text" name="email" placeholder="请输入邮箱" old='' lay-verify="email" autocomplete="off" class="layui-input" />
				</div>
			</div> -->
		</div>
		<!--        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">型号</label>
                <div class="layui-input-inline">
                    <div id="model-select"></div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline">
                </div>
            </div>
        </div> -->
		<div class="layui-form-item" style="display: none">
			<div class="layui-input-block">
				<!-- 隐藏提交按钮，在父层中调用 -->
				<button id="btn_submit" class="layui-btn" lay-filter="formVerify" lay-submit style="display: none"></button>
				<button id="btn_update" class="layui-btn" lay-filter="updateVerify" lay-submit style="display: none"></button>
				<button id="btn_reset" type="reset" class="layui-btn" style="display: none"></button>
			</div>
		</div>
	</form>
</script>

<script type="text/html" id="roleSelect">
	<form class="layui-form">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">角色名称</label>
				<div class="layui-input-inline">
					<input type="text" name="role_name" autocomplete="off" class="layui-input" />
				</div>
			</div>
			<div class="layui-inline">
				<button class="layui-btn" lay-filter="search" lay-submit>查询</button>
			</div>
		</div>
	</form>
	<table id="assignRoleTable"></table>
</script>

<script type="text/html" id="modelSelect">
	<form class="layui-form">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">过滤:</label>
				<div class="layui-input-inline">
					<input type="text" id="modelSearchIpt" autocomplete="off" class="layui-input" />
				</div>
			</div>
			<!-- <div class="layui-inline">
                <button class="layui-btn" lay-filter="search" lay-submit>查询</button>
            </div> -->
		</div>
	</form>
	<table id="assignModelTable"></table>
</script>
<script type="text/html" id="importHtml">
	<form class="layui-form" lay-filter="importForm">
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="importChoice">选择文件</button>
					<button type="button" class="layui-btn" id="importStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="selectedFile" style="display: none;">
			<label class="fieldlabel layui-form-label">已选文件:</label>
			<div class="layui-input-block">
				<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="uploadHtml">
	<form class="layui-form" lay-filter="uploadForm">
		<div class="layui-form-item">
			<label class="param-lable layui-form-label">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
					<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="selectedFile" style="display: none;">
			<label class="param-lable layui-form-label">已选文件:</label>
			<div class="layui-input-block">
				<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>
			</div>
		</div>
		<div class="layui-form-item" id="previewFile" style="display: none;">
			<label class="fieldlabel layui-form-label">签章预览:</label>
			<div class="layui-input-block">
				<img style="max-width: 130px;max-height: 56px;" id="previewImg" />
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>

<script type="text/html" id="syncResultTemplate">
	<div id="syncResultContent" style="padding: 15px;">
		<style>
			.sync-stats-table {
				width: 100%;
				border-collapse: collapse;
				margin-bottom: 20px;
			}
			.sync-stats-table th, .sync-stats-table td {
				border: 1px solid #e6e6e6;
				padding: 12px;
				text-align: center;
			}
			.sync-stats-table th {
				background-color: #f2f2f2;
				font-weight: bold;
			}
			.sync-success { color: #5FB878; font-weight: bold; }
			.sync-failure { color: #FF5722; font-weight: bold; }
			.sync-skip { color: #FFB800; font-weight: bold; }
			.sync-user-list {
				max-height: 300px;
				overflow-y: auto;
				padding: 15px;
				background-color: #fafafa;
				border-radius: 4px;
			}
			.sync-user-item {
				padding: 8px 0;
				border-bottom: 1px solid #eeeeee;
				line-height: 1.5;
			}
			.sync-user-item:last-child {
				border-bottom: none;
			}
			.sync-empty {
				text-align: center;
				color: #999;
				padding: 40px 0;
				font-size: 14px;
			}
			.layui-tab-content {
				padding: 15px 0;
			}
		</style>

		<h3 style="margin-top: 0; margin-bottom: 20px;">BPM用户工号同步结果</h3>

		<!-- 统计信息表格 -->
		<table class="sync-stats-table">
			<thead>
				<tr>
					<th>总处理用户数</th>
					<th class="sync-success">成功同步</th>
					<th class="sync-failure">同步失败</th>
					<th class="sync-skip">跳过处理</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td id="totalRecords">-</td>
					<td id="successCount" class="sync-success">-</td>
					<td id="failureCount" class="sync-failure">-</td>
					<td id="skipCount" class="sync-skip">-</td>
				</tr>
			</tbody>
		</table>

		<!-- Tab选项卡 -->
		<div class="layui-tab layui-tab-brief" id="syncResultTabs">
			<ul class="layui-tab-title">
				<li class="layui-this">
					<span class="sync-success">✓ 成功同步</span>
					<span id="successTabCount" class="sync-success">(0)</span>
				</li>
				<li>
					<span class="sync-failure">✗ 同步失败</span>
					<span id="failureTabCount" class="sync-failure">(0)</span>
				</li>
				<li>
					<span class="sync-skip">⚠ 跳过处理</span>
					<span id="skipTabCount" class="sync-skip">(0)</span>
				</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<div id="successList" class="sync-user-list">
						<div class="sync-empty">暂无数据</div>
					</div>
				</div>
				<div class="layui-tab-item">
					<div id="failureList" class="sync-user-list">
						<div class="sync-empty">暂无数据</div>
					</div>
				</div>
				<div class="layui-tab-item">
					<div id="skipList" class="sync-user-list">
						<div class="sync-empty">暂无数据</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</script>
<script src="../../../plugins/index/jquery.fileDownload.js"></script>
<script src="user.js"></script>
<script src="userMgr.js"></script>
<script src="role.js"></script>
<script src="modelMgr.js"></script>