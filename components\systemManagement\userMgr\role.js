var roleThingName = 'Thing.Fn.SystemManagement';

var initUGRoleComp = function() {
	$('#roleTable').datagrid({
		data: [],
		singleSelect: false,
		striped: true,
		rownumbers: true,
		toolbar: '#roleTable_tb',
		fit: true,
		columns: [
			[{
				field: 'ck',
				checkbox: true
			}, {
				field: 'UG_ID',
				title: '关系ID',
				hidden: true
			}, {
				field: 'USER_ID',
				title: '用户ID',
				hidden: true
			}, {
				field: 'ROLE_ID',
				title: '角色ID',
				width: 100,
				hidden: true
			}, {
				field: 'ROLE_NAME',
				title: '角色名称',
				width: 150
			}, {
				field: 'ROLE_DESC',
				title: '角色描述',
				width: 450
			}]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...'
	});
};

var getUserSelectedData = function() {
	var sels = $('#userTable').datagrid('getSelections');
	return sels;
};

var getRoleSelectedData = function() {
	var sels = $('#roleTable').datagrid('getSelections');
	return sels;
};

var getRoleAllData = function() {
	var datas = $('#roleTable').datagrid('getData');
	return datas.rows;
};

//加载角色表格数据
var initUGRoleData = function() {
	$('#roleTable').datagrid('loading');
	var sels = getUserSelectedData();
	var param = {};
	param.userid = sels[0].USER_ID;
	var cb_success = function(data) {
		$('#roleTable').datagrid('loadData', data.rows);
		$('#roleTable').datagrid('loaded');
	};
	var cb_error = function() {

	};
	twxAjax(roleThingName, 'QueryRolesByUserId', param, true, cb_success, cb_error);
};

var getAssignRoleSelectedData = function() {
	var sels = $('#assignRoleTable').datagrid('getSelections');
	return sels;
};


var initBtnAssign = function(layui) {
	$('#assignrole').bind('click', function() {
		var layer = layui.layer;
		var table = layui.table;
		var sels = getUserSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择待分配的用户...', {
				icon: 2,
				anim: 6
			});
			return;
		}else{
			var u = sels[0];
			var name = u.USER_NAME;
			if(name=='audit'||name=='admin'||name=='securityadmin'){
				layer.msg('三员无法更改角色', {
					icon: 2,
					anim: 6
				});
				return;
			}
		}
		layer.open({
			title: '分配角色',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ['500px', '400px'],
			content: '<div id="addContent" style="padding-top:15px;"></div>',
			btn: ['分配', '关闭'],
			yes: function() {
				var roleSels = getAssignRoleSelectedData();
				if (roleSels.length == 0) {
					layer.msg('请选择角色...', {
						icon: 2,
						anim: 6
					});
					return;
				}

				var param = {};
				param.userid = sels[0].USER_ID;
				var roleids = '';
				var logs = ''
				for (var i = 0; i < roleSels.length; i++) {
					roleids += "," + roleSels[i].ROLE_ID;
					logs += "," + '(ID：' + roleSels[i].ROLE_ID + '、角色名：' + roleSels[i].ROLE_NAME + ')'
				}
				logs = logs.substring(1);
				param.roleids = roleids.substring(1);

				var cb_success = function(data) {
					if (data.success === false) {
						layer.msg(data.message, {
							icon: 2,
							anim: 6
						});
						logRecord('编辑', '用户管理-给用户(ID：' + sels[0].USER_ID + '、用户名：' + sels[0].USER_NAME + '、全名：' + sels[0].USER_FULLNAME +
							')分配了' + roleSels.length + '个角色【' + logs + '】', 0);
						return;
					}
					initUGRoleData();
					layer.closeAll();
					logRecord('编辑', '用户管理-给用户(ID：' + sels[0].USER_ID + '、用户名：' + sels[0].USER_NAME + '、全名：' + sels[0].USER_FULLNAME +
						')分配了' + roleSels.length + '个角色【' + logs + '】', 1);
					layer.msg('分配成功');
				};
				var cb_error = function() {};

				twxAjax(roleThingName, 'AddUserGroup', param, true, cb_success, cb_error);
			},
			btn2: function() {
				return true;
			},
			success: function() {
				var addTpl = $("#roleSelect")[0].innerHTML;
				$("#addContent").append(addTpl);
				//初始化assignRoleTable
				initAssignRoleComp();
				initAssignRoleData();
				$.parser.parse();
			}
		});
	});
};

var initAssignRoleComp = function() {
	$('#assignRoleTable').datagrid({
		data: [],
		singleSelect: false,
		fitColumns: true,
		striped: true,
		rownumbers: true,
		height: 225,
		// fit: true,
		columns: [
			[{
				field: 'ck',
				checkbox: true
			}, {
				field: 'ROLE_NAME',
				title: '角色名称',
				width: 250
			}, {
				field: 'ROLE_DESC',
				title: '角色描述',
				width: 500
			}, {
				field: 'ROLE_ID',
				title: '角色ID',
				hidden: true
			}]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>无可分配的角色...</font></div>',
		loadMsg: '正在加载数据...'
	});
};

var getRoleids = function() {
	var assignedSels = getRoleAllData();
	var roleids = '';
	for (var i = 0; i < assignedSels.length; i++) {
		roleids += ',' + assignedSels[i].ROLE_ID;
	}
	roleids = roleids.substring(1);
	return roleids;
}
//初始化可用分配的角色
var initAssignRoleData = function() {
	$('#assignRoleTable').datagrid('loading');
	var cb_success = function(data) {
		$('#assignRoleTable').datagrid('loadData', data.rows);
		$('#assignRoleTable').datagrid('loaded');
	};
	var cb_error = function() {};
	var param = {};
	param.roleids = getRoleids();
	twxAjax(roleThingName, 'getAllToAssignRoles', param, true, cb_success, cb_error);
};

var initBtnRemove = function(layui) {
	$('#removerole').bind('click', function() {
		var layer = layui.layer;
		var userSels = getUserSelectedData();
		var sels = getRoleSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择需要移除的角色...', {
				icon: 2,
				anim: 6
			});
			return;
		}

		layer.confirm('确认移除该分配吗？', {
			icon: 3,
			title: '提示'
		}, function(index) {
			//调用ajax进行关系的移除
			var param = {};
			param.ugids = '';
			var logs = '';
			for (var i = 0; i < sels.length; i++) {
				param.ugids += ',' + sels[i].UG_ID;
				logs += "," + '(ID：' + sels[i].ROLE_ID + '、角色名：' + sels[i].ROLE_NAME + ')'
			}
			logs = logs.substring(1);
			param.ugids = param.ugids.substring(1);
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					logRecord('编辑', '用户管理-给用户(ID：' + userSels[0].USER_ID + '、用户名：' + userSels[0].USER_NAME + '、全名：' + userSels[0]
						.USER_FULLNAME +
						')取消分配了' + sels.length + '个角色【' + logs + '】', 0);
					return;
				}
				initUGRoleData();
				layer.closeAll();
				logRecord('编辑', '用户管理-给用户(ID：' + userSels[0].USER_ID + '、用户名：' + userSels[0].USER_NAME + '、全名：' + userSels[0]
					.USER_FULLNAME +
					')取消分配了' + sels.length + '个角色【' + logs + '】', 1);
				layer.msg('移除成功');
			};
			var cb_error = function() {};
			twxAjax(roleThingName, 'DeleteAssignRole', param, true, cb_success, cb_error);
		});
	});
};

$(document).ready(function() {
	layui.use(['layer', 'form', 'jquery', 'table'], function() {
		var layer = layui.layer,
			form = layui.form;


		form.on('submit(search)', function(data) {
			$('#assignRoleTable').datagrid('loading');
			var datas = data.field;
			datas.roleids = getRoleids();
			var cb_success = function(data) {
				$('#assignRoleTable').datagrid('loadData', data.rows);
				$('#assignRoleTable').datagrid('loaded');
			};
			var cb_error = function() {};
			twxAjax(roleThingName, 'getAllToAssignRoles', datas, true, cb_success, cb_error);
			return false;
		});

		initUGRoleComp();

		initBtnAssign(layui);
		initBtnRemove(layui);
	});
});
