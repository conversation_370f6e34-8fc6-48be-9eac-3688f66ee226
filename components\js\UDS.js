﻿var url = "ws://127.0.0.1:8341";
var websocket;
var connected = false;

//初始化WebSocket连接
function ConnectServer(callback, value) {
	if ('WebSocket' in window) {
		websocket = new WebSocket(url);
	} else if (window.WebSocket) {
		websocket = new WebSocket(url);
	} else if ('MozWebSocket' in window) {
		websocket = new MozWebSocket(url);
	} else {
		layer.alert("浏览器版本太低！请使用Chrome、Firefox、IE10+浏览器！", {
			icon: 2
		});
	}

	websocket.onopen = function() {
		connected = true;
		callback(value);
	}
	websocket.onclose = function(e) {
		connected = false;
		onMessage({
			data: JSON.stringify({
				FuncName: 'Connect',
				result: 0
			})
		});
	}
	websocket.onmessage = function(e) {
		onMessage(e);
	}
	websocket.onerror = function(e) {
		if (websocket.readyState != WebSocket.OPEN || websocket.readyState == WebSocket.CLOSED) {
			layer.alert("浏览器版本太低！请使用Chrome、Firefox、IE10+浏览器！", {
				icon: 2
			});
		}
	};
}

//接收并处理后台发送的信息,不要修改函数名称及参数定义,根据函数名和返回值进行流程控制即可，以下为几个示例
function produceMessage(functionName, Result) {
	if (functionName == "Connect") {
		if (Result == "0") {
			layer.alert("websocket服务器连接失败，请确保已运行紫图外设服务!", {
				icon: 2
			});
		} else {
			// StartRunMain(); //连接成功后直接打开主头
		}

	} else if (functionName == "ShowVideo") {
		var myimg = document.getElementById("video");
		myimg.src = "data:image/jpeg;base64," + Result;
		return false;
	} else if (functionName == "GetVideoDevices") {
		if (Result != "none") {
			document.getElementById("MainCamDevice").innerHTML = "";
			var deviceArray = new Array();
			deviceArray = Result.split("|");
			for (var i = 0; i < deviceArray.length; i++) {
				var objOption = document.createElement("option");
				objOption.text = deviceArray[i];
				objOption.value = i;
				document.getElementById("MainCamDevice").options.add(objOption);
			}

		} else {
			var objOption = document.createElement("option");
			objOption.text = "未找到合适的设备";
			objOption.value = 0;
			document.getElementById("MainCamDevice").options.add(objOption);
		}
	} else if (functionName == "StartVideo") {
		if (Result == 1) {
			ChangeVideoResolution(window.maxResolutionIndex);
		}
	} else if (functionName == "GetResolution") {
		if (Result != "none") {
			window.resolutionArray = new Array();
			window.resolutionArray = Result.split("|");
			for (var i = 0; i < window.resolutionArray.length; i++) {
				console.log(window.resolutionArray[i]);
			}
			window.maxResolutionIndex = resolutionArray.indexOf(resolutionArray.reduce(function(a, b) {
				return eval(a) > eval(b) ? a : b
			}));

		} else {
			var objOption = document.createElement("option");
			objOption.text = "无分辨率";
			objOption.value = 0;
			document.getElementById("MainResolution").options.add(objOption);
		}
	} else if (functionName == "CaptureFile") {
		//alert(Result);
		var myimg = document.getElementById("showPic");
		myimg.src = Result;
		return false;
	} else if (functionName == "CaptureBase64") {
		console.log(Result);
		var myimg = document.getElementById("canvas");
		// myimg.src = "data:image/bmp;base64," + Result;
		var dataURL = "data:image/bmp;base64," + Result;
		HotUtil.base64Upload(dataURL, '/online/upload/takePhoto', function(filePath) {
			$(myimg).data("filePath", filePath);
			myimg.src = "/File" + filePath;
			$("#video").hide();
			$("#canvas").show();
			window.isTake = true;
			layer.close(window.takePhotoLoadingIndex);
			return false;
		});

	}
}


//接收服务器发送的信息
function onMessage(event) {
	if (typeof(event.data) == "string") {
		var str = event.data;
		var jsonOBJ = JSON.parse(str);
		var name = jsonOBJ.FuncName;
		var re = jsonOBJ.result;
		if (name == "Connect" && re == 1) {
			connected = true;
		}
		produceMessage(name, re);
	}
}

//向服务器发送信息的回调方法
function sendMessage(data) {
	if (websocket.readyState == 0) {
		setTimeout(function() {
			var jsData = JSON.stringify(data);
			websocket.send(jsData);
		}, 50);
	} else if (websocket.readyState == 1) {
		var jsData = JSON.stringify(data);
		websocket.send(jsData);
	} else {
		layer.alert("未连接websocket服务器，请确保已运行服务端！", {
			icon: 2
		});
	}
}

/*==============以下是扫描仪功能======================================================*/
//设置保存路径
function SetSavePath(savePath) {
	var data = {
		"Func": "SetSavePath",
		"arg": savePath
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//设置文件前缀
function SetFilePre(pre) {
	var data = {
		"Func": "SetFilePre",
		"arg": pre
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//设置Twain源名称
function SetDevice(twainName) {
	var data = {
		"Func": "SetDevice",
		"arg": twainName
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//获取所有twain源名称
function GetDeviceList() {
	var data = {
		"Func": "GetDeviceList",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置色彩模式
function SetColorType(colorType) {
	var data = {
		"Func": "SetColorType",
		"arg": colorType
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置底板颜色
function SetBoardColor(colorType) {
	var data = {
		"Func": "SetBoardColor",
		"arg": colorType
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置分辨率
function SetDPI(dpi) {
	var data = {
		"Func": "SetDPI",
		"arg": dpi
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置是否弹出twainui
function ShowTwainUI(showUI) {
	var data = {
		"Func": "ShowTwainUI",
		"arg": showUI
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//设置文件类型
function SetFileType(fileType) {
	var data = {
		"Func": "SetFileType",
		"arg": fileType
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//设置单双面
function SetDuplex(duplex) {
	var data = {
		"Func": "SetDuplex",
		"arg": duplex
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置是否传输每一张图像的base64
function SetTransPerPageBase64(trans) {
	var data = {
		"Func": "SetTransPerPageBase64",
		"arg": trans
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置JPG图片压缩率
function SetJPGQuality(quality) {
	var data = {
		"Func": "SetJPGQuality",
		"arg": quality
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//获取扫描仪序列号
function GetScannerSN() {
	var data = {
		"Func": "GetScannerSN",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//空白页删除
function DeleteBlankPage(del, threshold) {
	var data = {
		"Func": "DeleteBlankPage",
		"arg": {
			"del": del,
			"threshold": threshold
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置进纸超时等待时间
function WaitPaperTime(time) {
	var data = {
		"Func": "WaitPaperTime",
		"arg": time
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//重张检测
function MultiFeedDetect(open) {
	var data = {
		"Func": "MultiFeedDetect",
		"arg": open
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//开始扫描
function StartScan(pageNum) {
	var data = {
		"Func": "StartScan",
		"arg": pageNum
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//获取本次扫描的文件列表
function GetFilesList() {
	var data = {
		"Func": "GetFilesList",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//图片转BASE64
function ImageToBase64(filePath) {
	var data = {
		"Func": "ImageToBase64",
		"arg": filePath
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//文件转BASE64
function FileToBase64(filePath) {
	var data = {
		"Func": "FileToBase64",
		"arg": filePath
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//图片http上传
function httpUpload(serverAddress, filePath, del) {
	var data = {
		"Func": "httpUpload",
		"arg": {
			"serverAddress": serverAddress,
			"filePath": filePath,
			"del": del
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//图片ftp上传
function ftpUpload(ftpServerIP, ftpRemotePath, ftpUserID, ftpPassword, filename, del, passive) {
	var data = {
		"Func": "ftpUpload",
		"arg": {
			"ftpServerIP": ftpServerIP,
			"ftpRemotePath": ftpRemotePath,
			"ftpUserID": ftpUserID,
			"ftpPassword": ftpPassword,
			"filename": filename,
			"del": del,
			"passive": passive
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//图片转pdf
function ImagesToPDF(imageList, pdfFile) {
	var data = {
		"Func": "ImagesToPDF",
		"arg": {
			"imageList": imageList,
			"pdfFile": pdfFile
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//图片转pdf base64方式
function ImagesToPDFBase64(base64Str) {
	var data = {
		"Func": "ImagesToPDFBase64",
		"arg": base64Str
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//开始活体检测双目，参数为超时时间，单位秒
function StartLiveDetect(outtime) {
	var data = {
		"Func": "StartLiveDetect",
		"arg": outtime
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//开始活体检测双目SS，参数为超时时间，单位秒
function StartLiveDetectSS(outtime) {
	var data = {
		"Func": "StartLiveDetectSS",
		"arg": outtime
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//开始活体检测单目，参数为超时时间，单位秒
function StartLiveDetectSingle(outtime) {
	var data = {
		"Func": "StartLiveDetectSingle",
		"arg": outtime
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//获取指定文件夹下的文件列表
function GetFolderFileListJson(folderPath) {
	var data = {
		"Func": "GetFolderFileListJson",
		"arg": folderPath
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//删除指定文件夹下的所有文件
function DeleteImagesInFolder(folderPath) {
	var data = {
		"Func": "DeleteImagesInFolder",
		"arg": folderPath
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//删除指定文件
function DeleteImage(filePath) {
	var data = {
		"Func": "DeleteImage",
		"arg": filePath
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
/*==============以下是读卡\指纹功能======================================================*/

//读身份证
function ReadIDCard() {
	var data = {
		"Func": "ReadIDCard",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//读身份证模块ID
function GetSAMID() {
	var data = {
		"Func": "GetSAMID",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//读二代社保卡
function Read2thSSCard() {
	var data = {
		"Func": "Read2thSSCard",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//读三代社保卡（PSAM）
function Read3thSSCardBasPSAM(type) {
	var data = {
		"Func": "Read3thSSCardBasPSAM",
		"arg": type
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//读三代社保卡步骤1（加密机）
function Read3thSSCardBasHSM1(type) {
	var data = {
		"Func": "Read3thSSCardBasHSM1",
		"arg": type
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//读三代社保卡步骤2（加密机）
function Read3thSSCardBasHSM2(key) {
	var data = {
		"Func": "Read3thSSCardBasHSM2",
		"arg": key
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//读IC银行卡
function ReadICCard() {
	var data = {
		"Func": "ReadICCard",
		"arg": 0
	};
	sendMessage(data);
}
//读磁条银行卡
function ReadMagCard() {
	var data = {
		"Func": "ReadMagCard",
		"arg": 0
	};
	sendMessage(data);
}
//采集指纹-文件方式
function CapFingerFile(fingerPic) {
	var data = {
		"Func": "CapFingerFile",
		"arg": fingerPic
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//采集指纹-base64方式
function CapFingerBASE64() {
	var data = {
		"Func": "CapFingerBASE64",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//比对指纹-文件方式
function MatchFingerByFile(fingerPic1, fingerPic2) {
	var data = {
		"Func": "MatchFingerByFile",
		"arg": {
			"fingerPic1": fingerPic1,
			"fingerPic2": fingerPic2
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//比对指纹-base64方式
function MatchFingerByBASE64(fingerPic1, fingerPic2) {
	var data = {
		"Func": "MatchFingerByBASE64",
		"arg": {
			"fingerPic1": fingerPic1,
			"fingerPic2": fingerPic2
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//与身份证比对指纹-文件方式
function MatchCardFingerByFile(fingerPic1) {
	var data = {
		"Func": "MatchCardFingerByFile",
		"arg": {
			"fingerPic1": fingerPic1
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//与身份证比对指纹-base64方式
function MatchCardFingerByBase64(fingerBase64) {
	var data = {
		"Func": "MatchCardFingerByBase64",
		"arg": fingerBase64
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

/*==============以下是手写屏功能======================================================*/
//开始签字
function StartSign(title) {
	var data = {
		"Func": "StartSign",
		"arg": title
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//开启评价器
function StartEvalute() {
	var data = {
		"Func": "StartEvalute",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//展示工牌
function StartStaff(headPic, staffName, staffNum, staffStar) {
	var data = {
		"Func": "StartStaff",
		"arg": {
			"headPic": headPic,
			"staffName": staffName,
			"staffNum": staffNum,
			"staffStar": staffStar
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//关闭工牌
function CloseStaff() {
	var data = {
		"Func": "CloseStaff",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//打开PDF
function OpenPDF(filePath, withSign) {
	var data = {
		"Func": "OpenPDF",
		"arg": {
			"filePath": filePath,
			"withSign": withSign
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//关闭PDF窗口
function ClosePDF() {
	var data = {
		"Func": "ClosePDF",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//打开网页
function OpenWebPage(url) {
	var data = {
		"Func": "OpenWebPage",
		"arg": url
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//关闭网页
function CloseWebPage() {
	var data = {
		"Func": "CloseWebPage",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//开启密码输入
function StartPWDInput(key, iv, title, showText) {
	var data = {
		"Func": "StartPWDInput",
		"arg": {
			"key": key,
			"iv": iv,
			"title": title,
			"showText": showText
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//关闭密码输入
function ClosePWDInput() {
	var data = {
		"Func": "ClosePWDInput",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//密文解密
function DecryptPWD(chiperText, key, iv) {
	var data = {
		"Func": "DecryptPWD",
		"arg": {
			"chiperText": chiperText,
			"key": key,
			"iv": iv
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
/*==============以下是高拍仪功能======================================================*/
function GetVideoDevInfo() {
	var a = {
		Func: "GetVideoDevInfo",
		arg: 0
	};
	connected ? sendMessage(a) : ConnectServer(sendMessage, a)
}

function CheckLicense() {
	var a = {
		Func: "CheckLicense",
		arg: 0
	};
	connected ? sendMessage(a) : ConnectServer(sendMessage, a)
}

function GetSpecialFolder(index) {
	var a = {
		Func: "GetSpecialFolder",
		arg: index
	};
	connected ? sendMessage(a) : ConnectServer(sendMessage, a)
}

function GetCurrentResolutionIndex() {
	var a = {
		Func: "GetCurrentResolutionIndex",
		arg: 0
	};
	connected ? sendMessage(a) : ConnectServer(sendMessage, a)
}
//打开视频窗口
function StartVideo(camIndex) {
	var data = {
		"Func": "StartVideo",
		"arg": camIndex
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//打开主头视频窗口
function StartRunMain() {
	var data = {
		"Func": "StartRunMain",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//打开副头视频窗口
function StartRunSub() {
	var data = {
		"Func": "StartRunSub",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//按指定PID\VID打开设备
function StartRunByID(vid, pid) {
	var data = {
		"Func": "StartRunByID",
		"arg": {
			"VID": vid,
			"PID": pid
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//打开主副头视频窗口
function StartRunDual(x, y, w, h) {
	var data = {
		"Func": "StartRunDual",
		"arg": {
			"x": x,
			"y": y,
			"w": w,
			"h": h
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

function ZoomIn() {
	var data = {
		"Func": "ZoomIn",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

function ZoomOut() {
	var data = {
		"Func": "ZoomOut",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//左旋转
function RotateLeft() {
	var data = {
		"Func": "RotateLeft",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//右旋转
function RotateRight() {
	var data = {
		"Func": "RotateRight",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//关闭视频窗口
function StopVideo() {
	var data = {
		"Func": "StopVideo",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//获取视频设备列表
function GetVideoDevices() {
	var data = {
		"Func": "GetVideoDevices",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//获取音频设备列表
function GetAudioDevices() {
	var data = {
		"Func": "GetAudioDevices",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//开始录像
function StartRecord(videoIndex, audioIndex, fileName, resolution, fps) {
	var data = {
		"Func": "StartRecord",
		"arg": {
			"videoIndex": videoIndex,
			"audioIndex": audioIndex,
			"fileName": fileName,
			"resolution": resolution,
			"fps": fps
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//停止录像
function StopRecord() {
	var data = {
		"Func": "StopRecord",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//切换视频设备
function ChangeVideoDevice(camIndex) {
	var data = {
		"Func": "ChangeVideoDevice",
		"arg": camIndex
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//切换视频分辨率
function ChangeVideoResolution(resIndex) {
	var data = {
		"Func": "ChangeVideoResolution",
		"arg": resIndex
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//切换裁剪方式
function ChangeCutType(type) {
	var data = {
		"Func": "ChangeCutType",
		"arg": type
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//获取高拍仪序列号
function GetVideoDeviceSN() {
	var data = {
		"Func": "GetVideoDeviceSN",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//文件方式拍照
function CaptureFile(fileName) {
	var data = {
		"Func": "CaptureFile",
		"arg": fileName
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//base64方式拍照
function CaptureBase64(format) {
	var data = {
		"Func": "CaptureBase64",
		"arg": format
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//双头base64方式拍照
function CaptureDualBase64(format, combinetype) {
	var data = {
		"Func": "CaptureDualBase64",
		"arg": {
			"format": format,
			"combinetype": combinetype
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//采集发票二维码
function CaptureTaxQR() {
	var data = {
		"Func": "CaptureTaxQR",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//人脸比对
function VerifyFace(face1, face2) {
	var data = {
		"Func": "VerifyFace",
		"arg": {
			"face1": face1,
			"face2": face2
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//人脸比对-Base64
function VerifyFaceByBase64(face1, face2) {
	var data = {
		"Func": "VerifyFaceByBase64",
		"arg": {
			"face1": face1,
			"face2": face2
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//人脸比对
function VerifyFacePro(face1, face2) {
	var data = {
		"Func": "VerifyFacePro",
		"arg": {
			"face1": face1,
			"face2": face2
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//人脸比对-Base64
function VerifyFaceByBase64Pro(face1, face2) {
	var data = {
		"Func": "VerifyFaceByBase64Pro",
		"arg": {
			"face1": face1,
			"face2": face2
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//OCR识别文件
function OCRImage(fileName) {
	var data = {
		"Func": "OCRImage",
		"arg": fileName
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//OCR识别文件Pro
function OCRImagePro(fileName, txt, json, rtf, pdf) {
	var data = {
		"Func": "OCRImagePro",
		"arg": {
			"fileName": fileName,
			"txt": txt,
			"json": json,
			"rtf": rtf,
			"pdf": pdf
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//打印图片文件
function PrintImage(fileName) {
	var data = {
		"Func": "PrintImage",
		"arg": fileName
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置水印
function SetWaterMark(watertext, waterfont, position, fontsize, rvalue, gvalue, bvalue, sapceing, Opacity, open) {
	var data = {
		"Func": "SetWaterMark",
		"arg": {
			"watertext": watertext,
			"waterfont": waterfont,
			"position": position,
			"fontsize": fontsize,
			"rvalue": rvalue,
			"gvalue": gvalue,
			"bvalue": bvalue,
			"sapceing": sapceing,
			"Opacity": Opacity,
			"open": open
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//合并图片
function CombineImages(pic1, pic2, savePic, type) {
	var data = {
		"Func": "CombineImages",
		"arg": {
			"pic1": pic1,
			"pic2": pic2,
			"savePic": savePic,
			"type": type
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//合并图片
function CombineImagesPro(picList, savePic, type) {
	var data = {
		"Func": "CombineImagesPro",
		"arg": {
			"picList": picList,
			"savePic": savePic,
			"type": type
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//合并图片-base64
function CombineImagesProByBase64(picList, type) {
	var data = {
		"Func": "CombineImagesProByBase64",
		"arg": {
			"picList": picList,
			"type": type
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//扫码条码、二维码
function ScanBarCode(timeout) {
	var data = {
		"Func": "ScanBarCode",
		"arg": timeout
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//扫码条码、二维码
function ScanBarCodePro(timeout) {
	var data = {
		"Func": "ScanBarCodePro",
		"arg": timeout
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//识别二维码、一维码
function GetBarCode(fileName, id) {
	var data = {
		"Func": "GetBarCode",
		"arg": {
			"fileName": fileName,
			"id": id
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

function GetBarCodeBase64(base64Str, id) {
	var data = {
		"Func": "GetBarCodeBase64",
		"arg": {
			"base64Str": base64Str,
			"id": id
		}
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}

//校准高拍仪DPI
function AjustCamDPI() {
	var data = {
		"Func": "AjustCamDPI",
		"arg": 0
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}
//设置高拍仪DPI
function SetCamDPI(dpi) {
	var data = {
		"Func": "SetCamDPI",
		"arg": dpi
	};
	connected ? sendMessage(data) : ConnectServer(sendMessage, data);
}