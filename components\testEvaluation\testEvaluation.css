/* 节点tooltip样式 */
.node-tooltip {
  position: absolute;
  z-index: 9999;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 10px 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  font-size: 13px;
  line-height: 1.6;
  color: #333;
  min-width: 200px;
}

.node-tooltip strong {
  color: #009688;
  font-weight: 600;
  margin-right: 5px;
}

.node-tooltip div {
  margin-bottom: 5px;
}

.node-tooltip div:last-child {
  margin-bottom: 0;
}

/* 搜索筛选区域样式 */
.layui-search-area {
  background-color: #fff;
  padding: 8px;
  border-bottom: 1px solid #f2f2f2;
  margin-bottom: 0;
  position: relative;
  z-index: 10; /* 确保下拉框和日期选择器能正常显示 */
}

.search-inline-container {
  display: inline-block;
  vertical-align: middle;
}

.search-inline-container .layui-inline {
  margin-right: 2px;
  margin-bottom: 0;
  position: relative;
}

.search-inline-container .layui-input {
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
  border-radius: 2px;
}

.search-inline-container .layui-btn {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  font-size: 12px;
}

.search-inline-container .layui-input-inline {
  width: auto;
  margin-right: 0;
}

.search-inline-container select {
  height: 30px;
  line-height: 30px;
  min-width: 120px; /* 确保宽度足够 */
}

/* 确保日期选择器可见 */
.layui-laydate {
  z-index: 999999 !important;
}
