.layout {
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 0;
}
.layout-panel {
  position: absolute;
  overflow: hidden;
}
.layout-body {
  min-width: 1px;
  min-height: 1px;
}
.layout-panel-east,
.layout-panel-west {
  z-index: 2;
}
.layout-panel-north,
.layout-panel-south {
  z-index: 3;
}
.layout-expand {
  position: absolute;
  padding: 0px;
  font-size: 1px;
  cursor: pointer;
  z-index: 1;
}
.layout-expand .panel-header,
.layout-expand .panel-body {
  background: transparent;
  filter: none;
  overflow: hidden;
}
.layout-expand .panel-header {
  border-bottom-width: 0px;
}
.layout-expand .panel-body {
  position: relative;
}
.layout-expand .panel-body .panel-icon {
  margin-top: 0;
  top: 0;
  left: 50%;
  margin-left: -8px;
}
.layout-expand-west .panel-header .panel-icon,
.layout-expand-east .panel-header .panel-icon {
  display: none;
}
.layout-expand-title {
  position: absolute;
  top: 0;
  left: 21px;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(90deg);
  -o-transform-origin: 0 0;
  transform: rotate(90deg);
  transform-origin: 0 0;
}
.layout-expand-title-up {
  position: absolute;
  top: 0;
  left: 0;
  text-align: right;
  padding-left: 5px;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(-90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(-90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(-90deg);
  -o-transform-origin: 0 0;
  transform: rotate(-90deg);
  transform-origin: 0 0;
}
.layout-expand-with-icon {
  top: 18px;
}
.layout-expand .panel-body-noheader .layout-expand-title,
.layout-expand .panel-body-noheader .panel-icon {
  top: 5px;
}
.layout-expand .panel-body-noheader .layout-expand-with-icon {
  top: 23px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  position: absolute;
  font-size: 1px;
  display: none;
  z-index: 5;
}
.layout-split-proxy-h {
  width: 5px;
  cursor: e-resize;
}
.layout-split-proxy-v {
  height: 5px;
  cursor: n-resize;
}
.layout-mask {
  position: absolute;
  background: #fafafa;
  filter: alpha(opacity=10);
  opacity: 0.10;
  z-index: 4;
}
.layout-button-up {
  background: url('images/layout_arrows.png') no-repeat -16px -16px;
}
.layout-button-down {
  background: url('images/layout_arrows.png') no-repeat -16px 0;
}
.layout-button-left {
  background: url('images/layout_arrows.png') no-repeat 0 0;
}
.layout-button-right {
  background: url('images/layout_arrows.png') no-repeat 0 -16px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  background-color: #ccc;
}
.layout-split-north {
  border-bottom: 5px solid #ffffff;
}
.layout-split-south {
  border-top: 5px solid #ffffff;
}
.layout-split-east {
  border-left: 5px solid #ffffff;
}
.layout-split-west {
  border-right: 5px solid #ffffff;
}
.layout-expand {
  background-color: #f5f5f5;
}
.layout-expand-over {
  background-color: #f5f5f5;
}
