/**
 * <AUTHOR>
 * @datetime 2025年4月2日10:17:34
 * @function	structureComponent
 * @description	结构件处理
 */

/**
 * 结构件同步
 * @param {Object} treeNode - 当前节点
 */
function syncBTable(treeNode) {
    layer.confirm('同步将覆盖现有数据，是否继续？', {
        btn: ['确定', '取消']
    }, function (index) {
        layer.close(index);
        twxAjax(THING, 'SyncBTableData', {
            treeId: treeNode.ID,
            saveUser: sessionStorage.getItem('username')
        }, true, function (res) {
            if (res.success) {
                reloadTree(treeNode.ID);
                layer.msg(res.msg);
            } else {
                layer.alert(res.msg);
            }
        });
    });
}

/**
 * 结构件批量标记
 * @param {Object} treeNode - 当前节点
 */
function batchMarkStructureComponents(treeNode) {
    twxAjax(THING, 'AsyncQueryTree', {
        ID: treeNode.ID
    }, true, function (res) {
        if (!res.success) {
            layer.alert(res.msg);
            return;
        }

        // 构建弹窗内容
        var content = '<div style="padding: 20px;">';
        content += '<form class="layui-form" lay-filter="batch-mark-form">';

        // 添加全选checkbox - 增加上边距
        content += '<div class="layui-form-item" style="margin-bottom: 20px;">';
        content += '<input type="checkbox" lay-filter="checkAll" title="全选" lay-skin="primary">';
        content += '</div>';

        // 添加B表节点checkbox列表 - 使用栅格布局，三列布局并增加间距
        content += '<div class="layui-row layui-col-space20">';

        res.data.forEach(function (node, index) {
            // 每个checkbox占据三分之一宽度
            content += '<div class="layui-col-xs4">';
            content += '<div class="layui-form-item" style="margin-bottom: 5px;">'; // 增加垂直间距
            // 如果IS_GENERAL_COMPONENT为1，则添加checked属性
            var checkedAttr = node.IS_GENERAL_COMPONENT == 1 ? 'checked' : '';
            content += '<input type="checkbox" name="nodeIds" value="' + node.ID +
                '" title="' + node.TABLE_NUM + '：' + node.NAME + '" lay-skin="primary" ' + checkedAttr + '>';
            content += '</div>';
            content += '</div>';
        });

        content += '</div></form></div>';

        // 弹出层 - 增加宽度
        layer.open({
            type: 1,
            title: '选择需要标记的结构件',
            area: ['1200px', '600px'], // 增加宽度到1200px
            content: content,
            btn: ['确定', '取消'],
            success: function () {
                form.render('checkbox');

                // 监听全选，需要检查是否所有checkbox都已选中
                form.on('checkbox(checkAll)', function (data) {
                    var checked = data.elem.checked;
                    $('input[name="nodeIds"]').each(function () {
                        this.checked = checked;
                    });
                    form.render('checkbox');
                });

                // 初始化全选框状态
                var allChecked = true;
                $('input[name="nodeIds"]').each(function () {
                    if (!this.checked) {
                        allChecked = false;
                        return false; // break each loop
                    }
                });
                $('input[lay-filter="checkAll"]').prop('checked', allChecked);
                form.render('checkbox');
            },
            yes: function (index) {
                // 获取选中和未选中的节点ID
                var selectedIds = [];
                var unselectedIds = [];

                $('input[name="nodeIds"]').each(function () {
                    if (this.checked) {
                        selectedIds.push(this.value);
                    } else {
                        unselectedIds.push(this.value);
                    }
                });

                // 请求后台执行标记
                twxAjax(THING, 'BatchMarkNodes', {
                    selectedIds: selectedIds.join(','),
                    unselectedIds: unselectedIds.join(',')
                }, true, function (res) {
                    if (res.success) {
                        layer.msg(res.msg || '标记成功');
                        layer.close(index);
                        // 刷新树节点
                        reloadTree(treeNode.ID);
                    } else {
                        layer.alert(res.msg || '标记失败');
                    }
                }, function () {
                    layer.alert('请求失败');
                });
            }
        });
    }, function () {
        layer.alert('获取节点数据失败');
    });
}


/**
 * 结构件标记
 * @param {Object} treeNode - 当前节点
 */
function markStructureComponent(treeNode) {
    // 构建弹窗内容
    var content = '<div style="padding: 20px;">';
    content += '<form class="layui-form" lay-filter="mark-form">';
    content += '<div class="layui-form-item">';

    // 根据节点的IS_GENERAL_COMPONENT设置是否选中
    var checkedAttr = treeNode.IS_GENERAL_COMPONENT == 1 ? 'checked' : '';
    content += '<input type="checkbox" name="isMarked" title="是否标记" lay-skin="primary" ' + checkedAttr + '>';

    content += '</div></form></div>';

    // 弹出层
    layer.open({
        type: 1,
        title: '结构件标记',
        area: ['300px', '200px'],
        content: content,
        btn: ['确定', '取消'],
        success: function () {
            form.render('checkbox');
        },
        yes: function (index) {
            // 获取是否标记
            var isMarked = $('input[name="isMarked"]').prop('checked');

            // 请求后台执行标记
            twxAjax(THING, 'BatchMarkNodes', {
                selectedIds: isMarked ? treeNode.ID : '',
                unselectedIds: isMarked ? '' : treeNode.ID
            }, true, function (res) {
                if (res.success) {
                    layer.msg(res.msg || '标记成功');
                    layer.close(index);
                    // 刷新树节点
                    reloadTree(treeNode.PID, treeNode.ID);
                } else {
                    layer.alert(res.msg || '标记失败');
                }
            }, function () {
                layer.alert('请求失败');
            });
        }
    });
}


/**
 * 创建开合格证弹窗的公共方法
 * @param {Object} options - 选项
 */
function createCertificateDialog(options) {
    var service = 'AsyncQueryTree';
    var params = {
        ID: options.treeNode.ID
    };
    if (options.treeNode.TYPE == 'table') {
        service = 'QueryNodeById';
        params = {
            id: options.treeNode.ID
        };
    }
    // 首先获取B表节点数据
    twxAjax(THING, service, params, true, function (res) {
        if (!res.success) {
            layer.alert(res.msg);
            return;
        }
        var data = options.treeNode.TYPE == 'table' ? [res.data] : res.data;

        // 构建弹窗内容
        var content = '<div style="padding: 20px;">';
        content += '<form class="layui-form" lay-filter="certificate-form">';

        // 添加操作模式和合格证类型在同一行
        content += '<div class="layui-row layui-col-space10">';
        
        // 操作模式单选框（左侧）
        content += '<div class="layui-col-md3">';
        content += '<div class="layui-form-item">';
        content += '<label class="layui-form-label" style="width: 100px;">操作模式：</label>';
        content += '<div class="layui-input-block" style="margin-left: 100px;">';
        content += '<input type="radio" name="operationMode" value="download" title="下载" lay-filter="operationMode">';
        content += '<input type="radio" name="operationMode" value="print" title="打印" checked lay-filter="operationMode">';
        content += '</div>';
        content += '</div>';
        content += '</div>';
        
        // 文件类型单选框（中间）
        content += '<div class="layui-col-md3">';
        content += '<div class="layui-form-item">';
        content += '<label class="layui-form-label" style="width: 100px;">文件类型：</label>';
        content += '<div class="layui-input-block" style="margin-left: 100px;">';
        content += '<input type="radio" name="fileType" value="pdf" title="PDF" checked lay-filter="fileType">';
        content += '<input type="radio" name="fileType" value="qrcode" title="二维码" lay-filter="fileType">';
        content += '</div>';
        content += '</div>';
        content += '</div>';
        
        // 合格证类型多选框（右侧）
        content += '<div class="layui-col-md3" id="certificateTypeContainer">';
        content += '<div class="layui-form-item">';
        content += '<label class="layui-form-label" style="width: 115px;">合格证类型：</label>';
        content += '<div class="layui-input-block" style="margin-left: 100px;">';
        content += '<input type="checkbox" name="certificateType" value="internal" title="内部" lay-skin="primary" lay-filter="certificateType">';
        content += '<input type="checkbox" name="certificateType" value="public" title="公开" lay-skin="primary" checked lay-filter="certificateType">';
        content += '</div>';
        content += '</div>';
        content += '</div>';
        
        content += '</div>';

        // 添加全选checkbox
        content += '<div class="layui-form-item" style="margin-bottom: 20px; margin-top: 10px;">';
        content += '<input type="checkbox" lay-filter="checkAll" title="全选" lay-skin="primary">';
        content += '</div>';

        // 添加B表节点checkbox列表
        content += '<div class="layui-row layui-col-space20">';
        data.forEach(function (node) {
            // 只显示已标记为结构件的节点
            if (node.IS_GENERAL_COMPONENT == 1) {
                content += '<div class="layui-col-xs4">';
                content += '<div class="layui-form-item" style="margin-bottom: 15px;">';
                content += '<input type="checkbox" name="nodeIds" value="' + node.ID +
                    '" title="' + node.TABLE_NUM + '：' + node.NAME + '" lay-skin="primary" checked>';
                content += '</div>';
                content += '</div>';
            }
        });
        content += '</div></form></div>';

        // 弹出层
        layer.open({
            type: 1,
            title: options.title,
            area: ['1200px', '600px'],
            content: content,
            btn: ['确定', '取消'],
            success: function () {
                form.render();  // 渲染所有表单元素

                // 初始化打印模式下的合格证类型单选状态
                var isPrintMode = $('input[name="operationMode"][value="print"]').prop('checked');
                if (isPrintMode) {
                    // 确保只有一个合格证类型被选中（默认是"公开"）
                    $('input[name="certificateType"][value="internal"]').prop('checked', false);
                }
                form.render('checkbox');

                // 监听文件类型变化
                form.on('radio(fileType)', function(data) {
                    var isQrCode = data.value === 'qrcode';
                    if (isQrCode) {
                        // 二维码模式下，隐藏合格证类型选择框
                        $('#certificateTypeContainer').hide();
                    } else {
                        // PDF模式下，显示合格证类型选择框
                        $('#certificateTypeContainer').show();
                    }
                });

                // 监听操作模式变化
                form.on('radio(operationMode)', function(data) {
                    var isPrintMode = data.value === 'print';
                    if (isPrintMode) {
                        // 打印模式下，只保留一个选中的合格证类型（保留最后选中的或默认公开）
                        var checkedTypes = $('input[name="certificateType"]:checked');
                        if (checkedTypes.length > 1) {
                            // 仅保留公开选中
                            $('input[name="certificateType"][value="internal"]').prop('checked', false);
                            $('input[name="certificateType"][value="public"]').prop('checked', true);
                            form.render('checkbox');
                        }
                    }
                });

                // 监听合格证类型变化，实现打印模式下的单选逻辑
                form.on('checkbox(certificateType)', function(data) {
                    var isPrintMode = $('input[name="operationMode"][value="print"]').prop('checked');
                    if (isPrintMode) {
                        // 打印模式下，确保只有一个合格证类型被选中
                        if (data.elem.checked) {
                            // 当前复选框被选中，取消其他复选框的选中状态
                            $('input[name="certificateType"]').not(data.elem).prop('checked', false);
                            form.render('checkbox');
                        } else {
                            // 当前复选框被取消选中，确保至少有一个被选中
                            if ($('input[name="certificateType"]:checked').length === 0) {
                                // 如果没有其他选中的，则重新选中当前
                                $(data.elem).prop('checked', true);
                                form.render('checkbox');
                            }
                        }
                    }
                });

                // 监听全选
                form.on('checkbox(checkAll)', function (data) {
                    var checked = data.elem.checked;
                    $('input[name="nodeIds"]').each(function () {
                        this.checked = checked;
                    });
                    form.render('checkbox');
                });
                
                // 初始化全选框状态为选中
                $('input[lay-filter="checkAll"]').prop('checked', true);
                form.render('checkbox');
            },
            yes: function (index) {
                // 获取选中的操作模式
                var operationMode = $('input[name="operationMode"]:checked').val();
                
                // 获取选中的文件类型
                var fileType = $('input[name="fileType"]:checked').val();
                
                // 获取选中的类型
                var selectedTypes = [];
                $('input[name="certificateType"]:checked').each(function () {
                    selectedTypes.push(this.value);
                });

                // 获取选中的节点ID
                var selectedIds = [];
                $('input[name="nodeIds"]:checked').each(function () {
                    selectedIds.push(this.value);
                });

                // 验证是否至少选中一个类型（仅PDF模式下检查）
                if (fileType === 'pdf' && selectedTypes.length === 0) {
                    layer.msg('请至少选择一种合格证类型');
                    return;
                }

                // 验证是否至少选中一个节点
                if (selectedIds.length === 0) {
                    layer.msg('请至少选择一个结构件');
                    return;
                }

                //日志记录
                var log = {};
                log.operation = options.logOperation;
                log.tablePid = options.treeNode.PID;
                log.tableId = options.treeNode.ID;
                log.content = "在节点【" + options.treeNode.NAME + "（" + options.treeNode.ID + "）】上" + options.logOperation +
                    "，操作模式：" + operationMode + 
                    "，文件类型：" + fileType + 
                    (fileType === 'pdf' ? "，合格证类型：" + selectedTypes.join(',') : "") + 
                    "，选中节点：" + selectedIds.join(',');

                // 打印模式暂不实现，仍使用下载功能
                var loading;
                
                // 根据操作模式和文件类型决定请求URL和处理方式
                if (operationMode === 'download') {
                    // 下载模式 - 使用文件下载
                    var downloadUrl;
                    
                    if (fileType === 'qrcode') {
                        // 二维码下载接口
                        downloadUrl = selectedIds.length === 1 ? 
                            '/report/create/qrcode' :   // 单个二维码下载
                            '/report/batch/create/qrcode';  // 批量二维码下载
                    } else {
                        // PDF合格证下载
                        downloadUrl = options.requestUrl;
                    }
                        
                    var data = {
                        "id": options.treeNode.ID,
                        "operationMode": operationMode,
                        "fileType": fileType,
                        "selectedIds": selectedIds.join(',')
                    };
                    
                    // 如果是PDF类型，添加合格证类型参数
                    if (fileType === 'pdf') {
                        data.certificateTypes = selectedTypes.join(',');
                    }
                    
                    $.fileDownload(fileHandlerUrl + downloadUrl, {
                        httpMethod: 'POST',
                        data: data,
                        prepareCallback: function (url) {
                            loading = layer.msg("正在导出...", {
                                icon: 16,
                                shade: 0.3,
                                time: 0
                            });
                        },
                        abortCallback: function (url) {
                            log.reqResult = 0;
                            addConfirmLog(log);
                            layer.close(loading);
                            layer.msg(options.logOperation + "异常！！");
                        },
                        successCallback: function (url) {
                            log.reqResult = 1;
                            addConfirmLog(log);
                            layer.close(loading);
                            layer.close(index);
                        },
                        failCallback: function (html, url) {
                            var msg = options.logOperation + '失败！';
                            log.reqResult = 0;
                            log.content = log.content + ",报错：" + msg;
                            addConfirmLog(log);
                            layer.close(loading);
                            layer.alert(msg, {
                                icon: 2
                            });
                        }
                    });
                } else {
                    // 打印模式
                    loading = layer.msg("正在生成预览...", {
                        icon: 16,
                        shade: 0.3,
                        time: 0
                    });
                    
                    if (fileType === 'qrcode') {
                        // 二维码打印模式
                        var requestUrl = selectedIds.length === 1 ? 
                            '/report/create/qrcode/print' :    // 单个二维码打印
                            '/report/batch/create/qrcode/print';  // 批量二维码打印
                            
                        var params = {
                            id: options.treeNode.ID,
                            selectedIds: selectedIds.join(',')
                        };
                        
                        // 发起AJAX请求获取二维码PDF文件路径
                        $.ajax({
                            url: fileHandlerUrl + requestUrl,
                            type: 'POST',
                            data: params,
                            success: function(res) {
                                if (res.success) {
                                    layer.close(loading);
                                    layer.close(index);
                                    
                                    // 获取PDF文件URL
                                    var pdfUrl = window.location.origin + res.filePath;
                                    
                                    // 确保先移除可能存在的旧iframe
                                    $("#print-iframe").remove();
                                    
                                    // 生成唯一ID，避免缓存问题
                                    var uniqueId = 'print-iframe-' + new Date().getTime();
                                    
                                    // 在页面中创建一个隐藏的iframe来加载PDF
                                    var printIframe = $('<iframe id="' + uniqueId + '" style="position:fixed; top:0; left:0; width:1px; height:1px; opacity:0; z-index:-1;"></iframe>');
                                    
                                    // 添加随机参数防止缓存
                                    printIframe.attr('src', pdfUrl + '?t=' + new Date().getTime());
                                    
                                    // 添加iframe到页面并设置加载完成后的处理
                                    printIframe.on('load', function() {
                                        // 自动触发打印
                                        try {
                                            var iframe = document.getElementById(uniqueId);
                                            if (iframe && iframe.contentWindow) {
                                                iframe.contentWindow.focus();
                                                iframe.contentWindow.print();
                                                
                                                // 添加打印后清理逻辑
                                                if (iframe.contentWindow.matchMedia) {
                                                    var mediaQueryList = iframe.contentWindow.matchMedia('print');
                                                    mediaQueryList.addListener(function(mql) {
                                                        if (!mql.matches) {
                                                            // 打印结束后移除iframe
                                                            setTimeout(function() {
                                                                $(iframe).remove();
                                                            }, 1000);
                                                        }
                                                    });
                                                }
                                            } else {
                                                console.error("无法找到打印窗口");
                                                layer.msg("打印失败：无法找到打印窗口", {icon: 2});
                                            }
                                        } catch(e) {
                                            console.error("打印失败:", e);
                                            layer.msg("打印失败：" + e.message, {icon: 2});
                                            // 出错时移除iframe
                                            printIframe.remove();
                                        }
                                    });
                                    
                                    $('body').append(printIframe);
                                    
                                    // 记录日志
                                    log.reqResult = 1;
                                    addConfirmLog(log);
                                } else {
                                    log.reqResult = 0;
                                    log.content = log.content + ",报错：" + (res.msg || "生成二维码失败");
                                    addConfirmLog(log);
                                    layer.close(loading);
                                    layer.alert(res.msg || "生成二维码失败", {
                                        icon: 2
                                    });
                                }
                            },
                            error: function() {
                                log.reqResult = 0;
                                log.content = log.content + ",报错：请求失败";
                                addConfirmLog(log);
                                layer.close(loading);
                                layer.alert("请求失败", {
                                    icon: 2
                                });
                            }
                        });
                    } else {
                        // PDF打印模式 - 在新窗口中打开PDF文档
                        // 获取单个选中的证书类型（打印模式下只能选一个）
                        var certificateType = selectedTypes[0];
                        
                        // 构建请求URL
                        var requestUrl = '/report/batch/create/certificate/print';
                        if (selectedIds.length === 1) {
                            // 如果只选择了一个节点，使用单个证书打印接口
                            requestUrl = '/report/create/certificate/print';
                        }
                        
                        // 请求参数
                        var params = {
                            id: options.treeNode.ID,
                            certificateType: certificateType,
                            fileType: fileType
                        };
                        
                        // 添加selectedIds参数（如果有多个ID）
                        if (selectedIds.length > 1 || (requestUrl === '/report/batch/create/certificate/print' && selectedIds.length === 1)) {
                            params.selectedIds = selectedIds.join(',');
                        }
                        
                        // 发起AJAX请求获取PDF文件路径
                        $.ajax({
                            url: fileHandlerUrl + requestUrl,
                            type: 'POST',
                            data: params,
                            success: function(res) {
                                if (res.success) {
                                    // 关闭原来的loading，显示打印准备中的提示
                                    layer.close(loading);
                                    
                                    // 新的loading提示，表明正在准备打印
                                    var printLoading = layer.msg("正在准备打印，请稍候...", {
                                        icon: 16,
                                        shade: 0.3,
                                        time: 0
                                    });
                                    
                                    // 关闭原弹窗
                                    layer.close(index);
                                    
                                    // 获取PDF文件URL
                                    var pdfUrl = window.location.origin + res.filePath;
                                    
                                    // 确保先移除可能存在的旧iframe
                                    $("#print-iframe").remove();
                                    
                                    // 生成唯一ID，避免缓存问题
                                    var uniqueId = 'print-iframe-' + new Date().getTime();
                                    
                                    // 在页面中创建一个隐藏的iframe来加载PDF
                                    var printIframe = $('<iframe id="' + uniqueId + '" style="position:fixed; top:0; left:0; width:1px; height:1px; opacity:0; z-index:-1;"></iframe>');
                                    
                                    // 添加随机参数防止缓存
                                    printIframe.attr('src', pdfUrl + '?t=' + new Date().getTime());
                                    
                                    // 添加iframe到页面并设置加载完成后的处理
                                    printIframe.on('load', function() {
                                        // 关闭打印准备中的loading
                                        layer.close(printLoading);
                                        
                                        // 自动触发打印
                                        try {
                                            var iframe = document.getElementById(uniqueId);
                                            if (iframe && iframe.contentWindow) {
                                                iframe.contentWindow.focus();
                                                iframe.contentWindow.print();
                                                
                                                // 添加打印后清理逻辑
                                                if (iframe.contentWindow.matchMedia) {
                                                    var mediaQueryList = iframe.contentWindow.matchMedia('print');
                                                    mediaQueryList.addListener(function(mql) {
                                                        if (!mql.matches) {
                                                            // 打印结束后移除iframe
                                                            setTimeout(function() {
                                                                $(iframe).remove();
                                                            }, 1000);
                                                        }
                                                    });
                                                }
                                                
                                                // 注意：不再自动移除iframe，让打印对话框保持直到用户手动关闭
                                            } else {
                                                console.error("无法找到打印窗口");
                                                layer.msg("打印失败：无法找到打印窗口", {icon: 2});
                                            }
                                        } catch(e) {
                                            console.error("打印失败:", e);
                                            layer.msg("打印失败：" + e.message, {icon: 2});
                                            // 出错时移除iframe
                                            printIframe.remove();
                                        }
                                    });
                                    
                                    $('body').append(printIframe);
                                    
                                    // 记录日志
                                    log.reqResult = 1;
                                    addConfirmLog(log);
                                } else {
                                    log.reqResult = 0;
                                    log.content = log.content + ",报错：" + (res.msg || "生成PDF失败");
                                    addConfirmLog(log);
                                    layer.alert(res.msg || "生成PDF失败", {
                                        icon: 2
                                    });
                                    // 关闭加载提示和弹窗
                                    layer.close(loading);
                                    layer.close(index);
                                }
                            },
                            error: function() {
                                log.reqResult = 0;
                                log.content = log.content + ",报错：请求失败";
                                addConfirmLog(log);
                                layer.close(loading);
                                layer.alert("请求失败", {
                                    icon: 2
                                });
                            }
                        });
                    }
                }
            }
        });
    }, function () {
        layer.alert('获取节点数据失败');
    });
}
