var treeId = 'dpTree';
var ztreeObj;
var curDragNodes, autoExpandNode;
//tree setting
var treeSetting = {
	view: {
		dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
		showLine: true, //是否显示节点之间的连线
		fontCss: {
			'color': 'black'
		}, //字体样式函数
		selectedMulti: false, //设置是否允许同时选中多个节点,
		txtSelectedEnable: true,
		showTitle: true
	},
	async: {
		enable: true,
		url: getTreeUrl("Thing.Fn.DataPackage", "QueryDataPackageTree", ""),
		type: "post",
		autoParam: ["TREEID"],
		contentType: "application/json;charset=utf-8",
		dataType: 'json',
		dataFilter: function(treeId, parentNode, responseData) {
			var datas = responseData.rows;
			if (datas.length > 0) {
				datas = dealDataIcons(datas);
				datas = dealDataNodeName(datas);
			}
			return datas;
		}
	},
	check: {
		//chkboxType: { "Y": "ps", "N": "ps" },
		chkboxType: {
			"Y": "",
			"N": ""
		},
		chkStyle: "checkbox", //复选框类型
		enable: false //每个节点上是否显示 CheckBox
	},
	edit: {
		enable: true,
		editNameSelectAll: false,
		showRemoveBtn: false,
		showRenameBtn: false,
		removeTitle: "删除",
		renameTitle: "重命名",
		drag: {
			autoExpandTrigger: true,
			prev: dropPrev,
			inner: dropInner,
			next: dropNext
		}
	},
	data: {
		simpleData: { //简单数据模式
			enable: true,
			idKey: "TREEID",
			pIdKey: "PARENTID",
			rootPId: 1
		},
		key: {
			name: 'NODENAME',
			title: '',
			isParent: "ISPARENT"
		}
	},
	callback: {
		beforeDrag: beforeDrag,
		beforeDrop: beforeDrop,
		beforeDragOpen: beforeDragOpen,
		onDrag: onDrag,
		onDrop: onDrop,
		beforeEditName: function(treeId, treeNode) {
			treeNode.NODENAME = getNodeName(treeNode.NODENAME);
			return true;
		},
		onRemove: function(event, treeId, treeNode) {}
	}
};

function dropPrev(treeId, nodes, targetNode) {
	var pNode = targetNode.getParentNode();
	if (pNode && pNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			var curPNode = curDragNodes[i].getParentNode();
			if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function dropInner(treeId, nodes, targetNode) {
	if (targetNode && targetNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			if (!targetNode && curDragNodes[i].dropRoot === false) {
				return false;
			} else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
				.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function dropNext(treeId, nodes, targetNode) {
	var pNode = targetNode.getParentNode();
	if (pNode && pNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			var curPNode = curDragNodes[i].getParentNode();
			if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function beforeDrag(treeId, treeNodes) {
	for (var i = 0, l = treeNodes.length; i < l; i++) {
		if (treeNodes[i].drag === false) {
			curDragNodes = null;
			return false;
		} else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
			curDragNodes = null;
			return false;
		}
	}
	curDragNodes = treeNodes;
	return true;
}

function beforeDragOpen(treeId, treeNode) {
	autoExpandNode = treeNode;
	return true;
}

function beforeDrop(treeId, treeNodes, targetNode, moveType, isCopy) {
	return true;
}

function onDrag(event, treeId, treeNodes) {}


function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
	if (targetNode != null) {
		var sourceNodeSort = treeNodes[0].NODESORT;
		var sourceNodeId = treeNodes[0].TREEID;
		var sourceNodeName = treeNodes[0].NODENAME;

		var targetNodeSort = targetNode.NODESORT;
		var targetNodeId = targetNode.TREEID;
		var targetNodeName = targetNode.NODENAME;
		var type = "上面";
		if (sourceNodeSort < targetNodeSort) {
			type = '下面';
		}
		var parentNode = treeNodes[0].getParentNode();
		var allNode = parentNode.children;
		var arr = [];
		for (var i = 1; i <= allNode.length; i++) {
			arr.push(allNode[i - 1].TREEID + ":" + i);
		}
		var str = arr.join(",");
		twxAjax('Thing.Fn.DataPackage', 'updateNodeSort', {
			str: str
		}, true, function(data) {
			logRecord('移动节点', '策划构建-数据包结构树-(ID：' + sourceNodeId + '、名称：' + sourceNodeName + ') 移动到节点(ID：' + targetNodeId + '、名称：' +
				targetNodeName + ')' + type, 1);
			reloadTree(parentNode.TREEID, sourceNodeId);
		}, function(data) {
			logRecord('移动节点', '策划构建-数据包结构树-(ID：' + sourceNodeId + '、名称：' + sourceNodeName + ') 移动到节点(ID：' + targetNodeId + '、名称：' +
				targetNodeName + ')' + type, 0);
			reloadTree(parentNode.TREEID, sourceNodeId);
		});
	}
}


/**
 * 添加节点
 * @param obj
 */
function addZTreeNode(obj) {
	var treeObj = $.fn.zTree.getZTreeObj(treeId);
	var parentZNode = treeObj.getSelectedNodes(); //获取父节点
	var newNode = obj;
	newNode.nodeFlg = 1; // 可以自定义节点标识
	newNode = treeObj.addNodes(parentZNode[0], newNode, true);
}
/**
 * 修改子节点
 * @param obj
 */
function editZTreeNode(obj) {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getSelectedNodes();
	for (var i = 0; i < nodes.length; i++) {
		nodes[i].name = obj;
		zTree.updateNode(nodes[i]);
	}
}

/**
 *  删除子节点 --选中节点
 * @param obj
 */
function removeZTreeNodeBySelect() {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getSelectedNodes(); //获取选中节点
	for (var i = 0; i < nodes.length; i++) {
		zTree.removeNode(nodes[i]);
	}
}

/**
 *  删除子节点 --勾选节点
 * @param obj
 */
function removeZTreeNodeByChecked() {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getCheckedNodes(true); //获取勾选节点
	for (var i = 0; i < nodes.length; i++) {
		zTree.removeNode(nodes[i]);
	}
}

/**
 *  根据节点id 批量删除子节点
 * @param obj
 */
function removeZTreeNodebPi(obj) {
	var idnodes = obj.split(",");
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getSelectedNodes();
	for (var i = 0; i < nodes.length; i++) {
		var nodes = zTree.getNodeByParam("id", nodes[i]);
		zTree.removeNode(nodes);
	}
}
/**
 * 选择节点
 * @param obj
 */
function selectzTreeNode(obj) {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var node = zTree.getNodeByParam("id", obj);
	if (node != null) {
		zTree.selectNode(node, true); //指定选中ID的节点
	}
}

/**
 * 对数据进行处理
 * @param {} datas 
 */
function dealDataIcons(datas) {
	var imagePrefix = '../dataTree/';
	for (var i = 0; i < datas.length; i++) {
		var dtype = datas[i].NODETYPE;
		if (dtype === 'root') {
			datas[i].icon = imagePrefix + "images/root.png";
			datas[i].drag = false;
			datas[i].childOuter = false;
		} else if (dtype === 'folder') {
			datas[i].icon = imagePrefix + "images/folder.png";
			datas[i].dropInner = true;
			datas[i].childOuter = false;
		} else if (dtype === 'product') {
			datas[i].childOuter = false;
			datas[i].dropInner = true;
			datas[i].icon = imagePrefix + "images/卫星.png";
			// datas[i].drag = false;
		} else if (dtype === 'phase') {
			datas[i].drag = true;
			datas[i].childOuter = false;
			datas[i].dropInner = true;
			if (datas[i].CODE === 'C') {
				datas[i].icon = imagePrefix + "images/phase.png";
			} else if (datas[i].CODE === 'Z') {
				datas[i].icon = imagePrefix + "images/phase_z.png";
			} else {
				datas[i].icon = imagePrefix + "images/phase.png";
			}
		} else if (dtype === 'dir') {
			datas[i].icon = imagePrefix + "images/dir.png";
			datas[i].childOuter = false;
			datas[i].dropInner = true;
		} else if (dtype === 'leaf') {
			var status = 'start';
			if (datas[i].NODESTATUS != undefined && datas[i].NODESTATUS != "undefined") {
				status = datas[i].NODESTATUS;
			}
			datas[i].icon = imagePrefix + "images/" + status + ".png";
			// datas[i].childOuter = true;
			datas[i].dropInner = false;
		}
	}
	return datas;
}

//处理节点数据名称
function dealDataNodeName(datas) {
	for (var i = 0; i < datas.length; i++) {
		var dtype = datas[i].NODETYPE;
		if (dtype === 'folder' || dtype === 'dir' || dtype === 'leaf') {
			datas[i].NODENAME = dealPrefix(datas[i].NODESORT) + "-" + datas[i].NODENAME;
		}
	}
	return datas;
}

//去除显示节点前的序号
function getNodeName(name) {
	if (name.indexOf("-") > -1) {
		var arr = name.split("-");
		var arr2 = [];
		for (var i = 1; i < arr.length; i++) {
			arr2.push(arr[i]);
		}
		return arr2.join("-")
	}
	return name;
}

//获取显示节点前的序号
function getNodeNum(name) {
	if (name.indexOf("-") > -1) {
		var arr = name.split("-");
		return arr[0] + "-";
	}
	return "";
}