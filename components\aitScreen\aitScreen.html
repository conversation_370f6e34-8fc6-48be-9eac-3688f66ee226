<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" type="text/css" />
		<link rel="stylesheet" href="aitScreen.css" type="text/css" />
		<title>卫星AIT质量数据看板</title>
		<script src="../../plugins/easyui/jquery.min.js"></script>
		<script src="../../plugins/index/jquery.fileDownload.js"></script>
		<script src="../../plugins/jsPlumb/jsplumb.bundle.js"></script>
		<script src="../../plugins/echarts/echarts.min.js"></script>
		<script src="../../plugins/layui-lasted/layui.js"></script>
		<script src="../js/config/twxconfig.js"></script>
		<script src="../js/util.js"></script>
	</head>
	<body>
		<div class="header">
			卫星AIT状态看板
		</div>
		<div class="search">
			<div class="model-select layui-form">
				<div class="layui-form-item">
					<div class="layui-inline">
						<select id="model-select" name="model-select" lay-verify="" lay-search lay-filter="model-select">
							<option value="-1">所有型号</option>
						</select>
					</div>
					<div class="layui-inline">
						<div class="layui-inline" id="date-range">
							<div class="layui-input-inline">
								<input type="text" autocomplete="off" id="start-date" class="layui-input" placeholder="开始日期">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" autocomplete="off" id="end-date" class="layui-input" placeholder="结束日期">
							</div>
						</div>
					</div>
					<div class="layui-inline" id="search-btn" style="margin-left: -15px;cursor: pointer;">
						<i class="layui-icon layui-icon-search"></i>
					</div>
					<div class="layui-inline" id="export-model-process" title="导出型号研制进度" style="float: right;cursor: pointer;padding-top: 5px;margin-right: 100px;margin-left: 5px;">
						<i class="layui-icon layui-icon-download-circle"></i>
					</div>
					<div class="layui-inline" id="refresh-btn" title="重新统计" style="float: right;cursor: pointer;padding-top: 5px;">
						<i class="layui-icon layui-icon-refresh"></i>
					</div>
					<div class="layui-inline" id="update-time" style="float: right;color: white;padding: 5.5px;letter-spacing: 1px;">
					</div>


				</div>
			</div>
		</div>
		<div class="top">
			<div class="card">
				<div class="card-header">
					型号研制进度
					<div id="refresh-model-process-btn" title="刷新型号研制进度" style="float: right;cursor: pointer;font-weight: 500; margin-right: 20px;">
						<i class="layui-icon layui-icon-refresh"></i>
					</div>
				</div>
				<div class="card-body">
					<div class="model-process-boundary">
						<div id="modelProcess">
							<table></table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="content">
			<div class="content-left">
				<div class="content-left-top">
					<div class="card">
						<div class="card-header">
							现场问题处理单（总装单据）
							<div id="export-problem-btn" title="导出现场问题处理单" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;">
								<i class="layui-icon layui-icon-download-circle"></i>
							</div>
						</div>
						<div class="card-body">
							<div id="xcwt-chart" style="height:100%;"></div>
						</div>
					</div>
				</div>

				<div class="content-left-top">
					<div class="card">
						<div class="card-header">
							现场临时处理单（测试/试验单据）
							<div id="export-temp-btn" title="导出现场临时处理单" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;">
								<i class="layui-icon layui-icon-download-circle"></i>
							</div>
							<div id="refresh-temp-btn" title="更新数据" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;">
								<i class="layui-icon layui-icon-refresh"></i>
							</div>
						</div>
						<div class="card-body">
							<div id="xcls-chart" style="height:100%;"></div>
						</div>
					</div>
				</div>

				<div class="content-left-middle">
					<div class="content-left-middle-left">
						<div class="card">
							<div class="card-header">
								技术状态更改单
								<div id="export-change-order-btn" title="导出技术状态更改单" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;">
									<i class="layui-icon layui-icon-download-circle"></i>
								</div>
							</div>
							<div class="card-body">
								<div id="jszt-chart" style="height:100%;"></div>
							</div>
						</div>
					</div>
					<div class="content-left-middle-right">
						<div class="card">
							<div class="card-header">
								不合格品审理单
							</div>
							<div class="card-body">
								<div id="bhgp-chart" style="height:100%;"></div>
							</div>
						</div>
					</div>
				</div>

				<!-- <div class="content-left-middle">
					<div class="content-left-middle-left">
						<div class="card">
							<div class="card-header">
								工艺更改单(<span id="gygg-num"></span>)
							</div>
							<div class="card-body">
								<div id="gygg-chart" style="height:100%;"></div>
							</div>
						</div>
					</div>
					<div class="content-left-middle-right">
						<div class="card">
							<div class="card-header">
								设计更改单(<span id="sjgg-num"></span>)
							</div>
							<div class="card-body">
								<div id="sjgg-chart" style="height:100%;"></div>
							</div>
						</div>
					</div>
				</div> -->

				<!-- <div class="content-left-bottom">
            <div class="content-left-bottom-left">
                <div class="card">
                    <div class="card-header">
                        工艺偏离单(<span id="gypl-num"></span>)
                    </div>
                    <div class="card-body">
                        <div id="gypl-chart" style="height:100%;"></div>
                    </div>
                </div>
            </div>
            <div class="content-left-bottom-right">
                <div class="card">
                    <div class="card-header">
                        设计偏离单(<span id="sjpl-num"></span>)
                    </div>
                    <div class="card-body">
                        <div id="sjpl-chart" style="height:100%;"></div>
                    </div>
                </div>
            </div>
        </div> -->
			</div>
			<div class="content-right">
				<div class="content-right-top">
					<div class="card">
						<div class="card-header">
							未交付装星单机汇总
							<div id="export-wjfzx-btn" title="导出未交付装星单机汇总" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;">
								<i class="layui-icon layui-icon-download-circle"></i>
							</div>
						</div>
						<div class="card-body">
							<div id="wjfzx-chart" style="height:100%;"></div>
						</div>
					</div>
				</div>
				<div class="content-right-bottom">
					<div class="card">
						<div class="card-header">
							落焊单机汇总
							<div id="export-lhdj-btn" title="导出落焊单机汇总" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;">
								<i class="layui-icon layui-icon-download-circle"></i>
							</div>
						</div>
						<div class="card-body">
							<div id="lhdj-chart" style="height:100%;"></div>
						</div>
					</div>
				</div>
				<div class="content-right-bottom">
					<div class="card">
						<div class="card-header">
							单机证明材料交付汇总
							<div id="export-zmcl-btn" title="导出单机证明材料交付汇总" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;">
								<i class="layui-icon layui-icon-download-circle"></i>
							</div>
						</div>
						<div class="card-body">
							<div id="zmcl-chart" style="height:100%;"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="unband-container" style="display: none;"></div>
	</body>
	<script src="js/common.js"></script>
	<script src="js/submit-chart.js"></script>
	<script src="js/model-process.js"></script>
	<script src="js/list-chart.js"></script>
	<script src="js/handle-chart.js"></script>
	<script src="js/problem-chart.js"></script>
	<script src="js/temp-chart.js"></script>
	<script src="js/change-order-chart.js"></script>
	<script src="js/nonconformity-chart.js"></script>
	<script src="js/init.js"></script>
	<script src="aitScreen.js"></script>
</html>