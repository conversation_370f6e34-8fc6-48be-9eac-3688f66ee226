var layer;
layui.use(function () {
    var layer = layui.layer;
});

function renderStandAlone() {

    // console.log("222：" + $(".stand-alone").length);
    //找到所有的单机热点元素


    pano.addListener('changenode', function (args) {

        //当前页面的所有热点
        var hotpots = pano.L;
        for (var i = 0; i < hotpots.length; i++) {
            var hotpot = hotpots[i];
            var skinid = hotpot.skinid;
            //是单机的才做操作
            if (skinid == 'stand-alone') {
                //单机的div元素
                var divEle = hotpot.j.__div;
                var description = hotpot.description;
                var title = hotpot.title;
                $(divEle).data("description", description).data("title", title);
                $(divEle).unbind('click').bind('click', function (e) {
                    showInfo($(this).data("description"));
                });
            }
        }
    });
}

/**
 * 查询显示单机信息

 * @param {Object} description
 */
function showInfo(description) {

    twxAjax("Thing.Fn.SecondTable", 'QueryStandAlone', {
        info: description,
        queryUser: sessionStorage.getItem("username")
    }, true, function (res) {
        if (res.success) {
            var baseParamObjs = res.data.baseParamObjs;
            var testParamObjs = res.data.testParamObjs;
            layer.open({
                title: false,
                type: 1,
                fixed: false,
                maxmin: false,
                anim: false,
                openDuration: 200,
                isOutAnim: false,
                closeDuration: 200,
                shadeClose: false,
                resize: false,
                //不允许拉伸
                area: ['1000px', '580px'],
                content: '<div id="infoContent" style=""></div>',
                success: function (layero, index) {
                    var $baseTableHtml = $('<table class = "layui-table"></table>');
                    for (var i = 0; i < baseParamObjs.length; i += 2) {
                        var $tr = $('<tr></tr>');
                        $tr.append('<td style="font-weight:bold;width:150px;">' + baseParamObjs[i]['paramName'] + '</td><td style="min-width:200px;">' + baseParamObjs[i]['paramValue'] + '</td>');
                        if ((i + 1) < baseParamObjs.length) {
                            $tr.append('<td style="font-weight:bold;width:150px;">' + baseParamObjs[i + 1]['paramName'] + '</td><td style="min-width:200px;">' + baseParamObjs[i + 1]['paramValue'] + '</td>');
                        }
                        $baseTableHtml.append($tr);
                    }
                    $('#infoContent').append($baseTableHtml);

                    var $testTableHtml = $('<table class = "layui-table"></table>');
                    for (var i = 0; i < testParamObjs.length; i++) {
                        var $tr = $('<tr></tr>');
                        $tr.append('<td style="font-weight:bold;width:150px;">' + testParamObjs[i]['paramName'] + '</td><td style="min-width:200px;">' + testParamObjs[i]['paramValue'] + '</td>');
                        $tr.append('<td style="font-weight:bold;width:150px;">' + testParamObjs[i]['relationParam']['paramName'] + '</td><td style="min-width:200px;">' + testParamObjs[i]['relationParam']['paramValue'] + '</td>');
                        $testTableHtml.append($tr);
                    }
                    $('#infoContent').append($testTableHtml);
                    layer.style(index, {
                        height: ($('#infoContent').height() + 25) + 'px'
                    });
                }
            });
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function () {
    });
}