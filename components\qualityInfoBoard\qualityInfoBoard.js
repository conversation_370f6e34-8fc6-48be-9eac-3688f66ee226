/**
 * 质量信息看板主要功能模块
 * 包含数据加载、动画效果、时间显示等功能
 */

// 全局配置对象
var QualityBoard = {
  // 配置参数
  config: {
    animationDuration: 2000,        // 数字动画持续时间
    loadingMinDuration: 1000,       // 最小加载时间
    retryMaxCount: 3,               // 最大重试次数
    retryDelay: 2000                // 重试延迟时间
  },

  // 状态管理
  state: {
    isLoading: false,               // 是否正在加载
    retryCount: 0,                  // 当前重试次数
    loadStartTime: 0                // 加载开始时间
  }
};

/**
 * 更新日期和时间显示
 */
function updateDateTime() {
  var now = new Date();
  var dateOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
  var timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false };

  document.getElementById('date').textContent = now.toLocaleDateString('zh-CN', dateOptions);
  document.getElementById('time').textContent = now.toLocaleTimeString('zh-CN', timeOptions);
}

/**
 * 显示加载动画
 */
function showLoading() {
  QualityBoard.state.isLoading = true;
  QualityBoard.state.loadStartTime = Date.now();
  var loadingOverlay = document.getElementById('loadingOverlay');
  if (loadingOverlay) {
    loadingOverlay.classList.remove('hidden');
  }
}

/**
 * 隐藏加载动画
 * @param {Function} callback - 隐藏完成后的回调函数
 */
function hideLoading(callback) {
  var loadingOverlay = document.getElementById('loadingOverlay');
  if (!loadingOverlay) {
    QualityBoard.state.isLoading = false;
    if (callback) callback();
    return;
  }

  // 确保最小加载时间，提供更好的用户体验
  var elapsedTime = Date.now() - QualityBoard.state.loadStartTime;
  var remainingTime = Math.max(0, QualityBoard.config.loadingMinDuration - elapsedTime);

  setTimeout(function () {
    loadingOverlay.classList.add('hidden');
    QualityBoard.state.isLoading = false;

    // 等待CSS过渡动画完成后执行回调
    setTimeout(function () {
      if (callback) callback();
    }, 500);
  }, remainingTime);
}

// 初始化时间显示并定时更新
updateDateTime();
setInterval(updateDateTime, 1000);

/**
 * 数字动画效果
 * 为所有数值元素添加递增动画效果
 */
function animateNumbers() {
  var valueElements = document.querySelectorAll('.item-value');

  // 使用ES5兼容的循环方式
  for (var i = 0; i < valueElements.length; i++) {
    (function (element) {
      var finalValue = parseInt(element.getAttribute('data-value')) || 0;
      var duration = QualityBoard.config.animationDuration;
      var startTime = null;

      function step(timestamp) {
        if (!startTime) startTime = timestamp;
        var progress = timestamp - startTime;
        var percentage = Math.min(progress / duration, 1);

        // 使用缓动函数使动画更自然
        var easeOutQuart = 1 - Math.pow(1 - percentage, 4);
        var currentValue = Math.floor(easeOutQuart * finalValue);

        element.textContent = currentValue;

        if (percentage < 1) {
          window.requestAnimationFrame(step);
        }
      }

      // 延迟启动动画，与卡片淡入动画配合
      setTimeout(function () {
        window.requestAnimationFrame(step);
      }, 1000);
    })(valueElements[i]);
  }
}

/**
 * 更新页面数据显示
 * @param {Object} data - 从服务器获取的数据对象
 */
function updatePageData(data) {
  try {
    // 更新现场问题数据
    if (data.qualityProblem) {
      $("#groupCount").attr('data-value', data.qualityProblem.groupCount || 0);
      $("#instituteCount").attr('data-value', data.qualityProblem.instituteCount || 0);
      $("#factoryCount").attr('data-value', data.qualityProblem.factoryCount || 0);
      $("#otherCount").attr('data-value', data.qualityProblem.otherCount || 0);
      $("#qualityProblemTotalCount").text("现场问题(" + (data.qualityProblem.qualityProblemTotalCount || 0) + ")");
    }

    // 更新测试异常数据
    if (data.testEvent) {
      $("#starProductCount").attr('data-value', data.testEvent.starProductCount || 0);
      $("#testDesignCount").attr('data-value', data.testEvent.testDesignCount || 0);
      $("#testImplementationCount").attr('data-value', data.testEvent.testImplementationCount || 0);
      $("#telemetryAlarmCount").attr('data-value', data.testEvent.telemetryAlarmCount || 0);
      $("#testEventTotalCount").text("测试异常(" + (data.testEvent.testEventTotalCount || 0) + ")");
    }

    // 更新不合格品审理数据
    if (data.nqc) {
      $("#reworkCount").attr('data-value', data.nqc.reworkCount || 0);
      $("#waiverCount").attr('data-value', data.nqc.waiverCount || 0);
      $("#downgradeCount").attr('data-value', data.nqc.downgradeCount || 0);
      $("#scrapCount").attr('data-value', data.nqc.scrapCount || 0);
      $("#nqcTotalCount").text("不合格品审理单(" + (data.nqc.nqcTotalCount || 0) + ")");
    }

    // 更新现场临时处理数据
    if (data.temp) {
      $("#assemblyCount").attr('data-value', data.temp.assemblyCount || 0);
      $("#testCount").attr('data-value', data.temp.testCount || 0);
      $("#tempTotalCount").text("现场临时处理单(" + (data.temp.tempTotalCount || 0) + ")");
    }

    // 启动数字动画效果
    animateNumbers();
  } catch (error) {
    console.error('更新页面数据时发生错误:', error);
    showErrorMessage('数据更新失败，请刷新页面重试');
  }
}

/**
 * 显示错误消息
 * @param {string} message - 错误消息内容
 */
function showErrorMessage(message) {
  // 创建错误提示元素
  var errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.innerHTML = '<i class="ri-error-warning-line"></i>' + message;
  errorDiv.style.cssText = 'position:fixed;top:20px;right:20px;background:#ff4444;color:white;padding:15px 20px;border-radius:8px;z-index:10000;box-shadow:0 4px 12px rgba(255,68,68,0.3);';

  document.body.appendChild(errorDiv);

  // 3秒后自动移除
  setTimeout(function () {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 3000);
}

/**
 * 数据加载成功回调函数
 * @param {Object} res - 服务器响应对象
 */
function onDataLoadSuccess(res) {
  try {
    if (res && res.success && res.data) {
      // 重置重试计数
      QualityBoard.state.retryCount = 0;

      // 更新页面数据
      updatePageData(res.data);

      // 隐藏加载动画
      hideLoading();
    } else {
      throw new Error(res.message || '数据格式错误');
    }
  } catch (error) {
    console.error('数据加载成功回调处理错误:', error);
    onDataLoadError(error.message || '数据处理失败');
  }
}

/**
 * 数据加载失败回调函数
 * @param {string} errorMsg - 错误消息
 */
function onDataLoadError(errorMsg) {
  console.error('数据加载失败:', errorMsg);

  // 隐藏加载动画
  hideLoading();

  // 检查是否需要重试
  if (QualityBoard.state.retryCount < QualityBoard.config.retryMaxCount) {
    QualityBoard.state.retryCount++;
    showErrorMessage('数据加载失败，正在重试(' + QualityBoard.state.retryCount + '/' + QualityBoard.config.retryMaxCount + ')...');

    // 延迟重试
    setTimeout(function () {
      loadQualityData();
    }, QualityBoard.config.retryDelay);
  } else {
    // 重试次数用完，显示最终错误
    showErrorMessage('数据加载失败，请检查网络连接或联系管理员');
  }
}

/**
 * 加载质量信息数据
 * 主要的数据获取函数
 */
function loadQualityData() {
  // 防止重复加载
  if (QualityBoard.state.isLoading) {
    return;
  }

  // 显示加载动画
  showLoading();

  try {
    // 调用ThingWorx服务获取数据
    twxAjax('Thing.Fn.AitScreen', 'QueryQualityInfo', '', true, onDataLoadSuccess, onDataLoadError);
  } catch (error) {
    console.error('调用数据服务时发生错误:', error);
    onDataLoadError('服务调用失败');
  }
}

/**
 * 页面初始化函数
 * 在页面加载完成后执行
 */
function initializePage() {
  try {
    // 初始化页面状态
    QualityBoard.state.retryCount = 0;
    QualityBoard.state.isLoading = false;

    // 开始加载数据
    loadQualityData();

    console.log('质量信息看板初始化完成');
  } catch (error) {
    console.error('页面初始化失败:', error);
    showErrorMessage('页面初始化失败，请刷新页面重试');
  }
}

/**
 * 手动刷新数据
 * 提供给外部调用的刷新接口
 */
function refreshData() {
  if (!QualityBoard.state.isLoading) {
    QualityBoard.state.retryCount = 0;
    loadQualityData();
  }
}

// 页面加载完成后初始化
window.addEventListener('load', initializePage);

// 为了调试方便，将主要函数暴露到全局
window.QualityBoard = QualityBoard;
window.refreshData = refreshData;