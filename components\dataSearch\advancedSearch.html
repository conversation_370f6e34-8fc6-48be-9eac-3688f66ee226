<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../img/favicon.ico">
	<link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
	<!-- <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css"> -->

	<!-- <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
	<link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">
 -->
	<link rel="stylesheet" href="../../css/icon.css">

	<!-- <script src="../../plugins/InsdepUI/jquery.min.js"></script>
	<script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
	<script src="../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
	<script src="../../plugins/layui/layui.js"></script>

	<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
	<script src="../../plugins/easyui/jquery.min.js"></script>
	<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<script src="../js/config/twxconfig.js"></script>
	<script src="../js/util.js"></script>

	<!-- <script src="../../plugins/loading/jquery.loading.min.js"></script> -->


	<script type="text/javascript" src="../js/intercept.js"></script>
	<script type="text/javascript" src="../js/logUtil.js"></script>
	<style>
		.fieldlabel {
			width: 75px;
		}

		.fieldlabel1 {
			width: 50px;
		}

		.fieldlabel2 {
			width: 60px;
		}
	</style>
	<title>高级查询</title>
</head>
<body>
	<div id="root_layout" class="easyui-panel" style="width:100%;height:100%;overflow: hidden;" data-options="fit:true">
		<div id="tb">
			<form id="condForm">
				<table style="margin:10px 10px;font-family: 微软雅黑;font-size: 14px;">
					<tr height="38px">
						<td class="fieldlabel" align="right">型号：</td>
						<td>
							<input class="easyui-combobox" id="product" data-options="editable:true" style="width:120px;">
						</td>
						<td class="fieldlabel" align="right">阶段：</td>
						<td>
							<input class="easyui-combobox" id="phase" data-options="editable:false" style="width:150px;">
						</td>
						<td class="fieldlabel1" align="right">专业：</td>
						<td>
							<input class="easyui-combobox" id="major" data-options="editable:false" style="width:150px;">
						</td>
						<td class="fieldlabel2" align="right">过程：</td>
						<td>
							<input class="easyui-combobox" id="process" data-options="editable:false" style="width:150px;">
						</td>
						<td class="fieldlabel" align="right">类别：</td>
						<td>
							<select class="easyui-combobox" data-options="editable:false,panelHeight:'auto'" id="searchType" style="width:150px;">
								<option value="">所有</option>
								<option value="design">设计类</option>
								<option value="craft">工艺类</option>
								<option value="process">过程控制</option>
								<option value="quality">质量综合</option>
							</select>
						</td>
						<td>

						</td>
					</tr>
					<tr height="38px">
						<td class="fieldlabel" align="right">文件类别：</td>
						<td>
							<input class="easyui-textbox" id="searchFileType" style="width:120px;">
						</td>

						<td class="fieldlabel" align="right">采集方式：</td>
						<td>
							<select class="easyui-combobox" data-options="editable:false,panelHeight:'auto'" id="searchModel" style="width:150px;">
								<option value="">所有</option>
								<option value="自动采集">自动采集</option>
								<option value="手动采集">手动采集</option>
								<option value="EXCEL导入">EXCEL导入</option>
							</select>
						</td>
						<td class="fieldlabel1" align="right">密级：</td>
						<td>
							<input class="easyui-combobox" id="secLevel" data-options="editable:false" style="width:150px;">
						</td>
						<td class="fieldlabel2" align="right">创建人：</td>
						<td>
							<input class="easyui-textbox" id="searchCreator" style="width:150px;">
						</td>
						<td class="fieldlabel" align="right">文件名称：</td>
						<td>
							<input class="easyui-textbox" id="searchName" style="width:150px;">
						</td>
						<td>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-normal " style="margin-left: 10px;" onclick="searchTable()">
								<i class="layui-icon">&#xe615;</i> 搜索
							</button>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-danger " style="margin-left: 10px;" onclick="resetForm()">
								<i class="layui-icon">&#xe639;</i> 清除
							</button>
							<button type="button" class="layui-btn layui-btn-sm" style="margin-left: 10px;" onclick="exportExcel()">
								<i class="layui-icon">&#xe62d;</i> 导出
							</button>
						</td>
					</tr>
				</table>
			</form>
		</div>
		<div id="searchResultTable"></div>
	</div>
</body>
<script src="../../plugins/index/jquery.fileDownload.js"></script>
<script src="advancedSearch.js"></script>
