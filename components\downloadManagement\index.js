$(function () {
	// resizeInputWidth();
	loadTree();
	loadProductCombobox();
	loadPhaseCombobox(0);
	loadMajorCombobox(0);
	loadProcessCombobox(0);
	loadTable(parentId);

	$("#root_layout").layout("panel", "center").panel({
		onResize: function (width, height) {
			//2是边框宽度
			var gridWidth = width - 2;
			$("#" + tableName).datagrid('resize', {
				width: gridWidth
			});
			changeWidth(tableName);
		}
	});

});
var layer, table;
layui.use('layer', function () {
	layer = layui.layer;
	table = layui.table;
});

//表格名称
var tableName = 'downloadTable';
//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};
var parentId = 1;

var emptyOption = {
	TREEID: '',
	NODENAME: '--请选择--'
};
//renderTable
var renderTable = function () {
	$('#' + tableName).datagrid({
		data: [],
		toolbar: "#tb",
		fitColumns: true,
		height: windowH - 5,
		columns: [
			[{
				field: 'ck',
				checkbox: true
			}, {
				field: 'NAME',
				title: '数据包名称',
				width: 300,
				align: 'center'
			}, {
				field: 'CODE',
				title: '数据包编号',
				width: 500,
				align: 'center'
			},
			{
				field: 'CREATOR_NAME',
				title: '创建人',
				width: 100,
				align: 'center'
			},
			{
				field: 'CREATETIME',
				title: '创建时间',
				width: 200,
				align: 'center'
			}
			]
		],
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有下载数据...</font></div>',
		pagination: true,
		loadMsg: '正在加载数据...',
		rownumbers: true,
		striped: true,
		onLoadSuccess: function (data) {
			changeWidth(tableName);
		}
	});
}

//初始化分页组件
var initPagination = function (data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function () {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function (pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function (pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function (pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function (pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};

//分页查询数据
var queryDataByPage = function (pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + tableName).datagrid('loading');
	initTotalRecords();
	var cb_success = function (data) {
		dataLoadFlag = true;
		//调用成功后，渲染数据
		$('#' + tableName).datagrid('loadData', data.rows);
		if (pageLoadFlag) {
			paginationShow();
		}
		$('#' + tableName).datagrid('loaded');
	};
	var cb_error = function () {
		layui.use(['layer'], function () {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.DataDownload', 'QueryDataPkg', {
		parentId: parentId,
		pageSize: pageSize,
		pageNumber: pageNumber,
		username: sessionStorage.getItem('username')
	}, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function () {
	//initPagination('logtable',{total:data.rows[0].COUNT});
	$('#' + tableName).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
}

//初始化全部的记录条数
var initTotalRecords = function () {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function (data) {
		pageLoadFlag = true;
		totalRecords = data.rows[0].COUNT;
		if (dataLoadFlag) {
			paginationShow();
		}
	};
	var cb_error = function () { };

	twxAjax('Thing.Fn.DataDownload', 'QueryDataPkgCount', {
		parentId: parentId,
		username: sessionStorage.getItem('username')
	}, true, cb_success, cb_error);
};


//初始化行号
var initLineNumbers = function () {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function (index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//加载表格数据
function loadTable() {
	//渲染表格
	renderTable();
	//初始化分页组件
	initPagination({
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}

//重新加载表格数据
function reloadTable() {
	//渲染表格
	renderTable();
	//初始化分页组件
	initPagination({
		total: 0
	});
	pageOptions.pageNumber = 1;
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}
//加载型号列表
function loadProductCombobox() {
	twxAjax('Thing.Fn.DataDownload', 'QueryProduct', {
		username: sessionStorage.getItem('username')
	}, true, function (data) {
		data.rows.unshift(emptyOption);
		$("#product").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			panelHeight: '400px',
			data: data.rows,
			editable: true,
			onSelect: function (record) {
				loadPhaseCombobox(record.TREEID);
				$("#major").combobox('loadData', [emptyOption]);
				$("#major").combobox('setValue', '');
				$("#process").combobox('loadData', [emptyOption]);
				$("#process").combobox('setValue', '');
			},
			onLoadSuccess: function () {
				// resizeComboboxWidth(this);
			}
		});
	});
}
//加载阶段列表
function loadPhaseCombobox(parentId1) {
	twxAjax('Thing.Fn.DataDownload', 'QueryPhase', {
		parentId: parentId1 == '' ? 0 : parentId1
	}, true, function (data) {
		data.rows.unshift(emptyOption);
		$("#phase").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			panelHeight: 'auto',
			data: data.rows,
			editable: true,
			onSelect: function (record) {
				loadMajorCombobox(record.TREEID);
				$("#process").combobox('loadData', [emptyOption]);
				$("#process").combobox('setValue', '');
			},
			onLoadSuccess: function () {
				// resizeComboboxWidth(this);
			}
		});
	});
}
//加载专业列表
function loadMajorCombobox(parentId1) {
	twxAjax('Thing.Fn.DataDownload', 'QueryMajor', {
		parentId: parentId1 == '' ? 0 : parentId1
	}, true, function (data) {
		data.rows.unshift(emptyOption);
		$("#major").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			panelHeight: 'auto',
			data: data.rows,
			editable: true,
			onSelect: function (record) {
				loadProcessCombobox(record.TREEID);
			},
			onLoadSuccess: function () {
				// resizeComboboxWidth(this);
			}
		});
	});
}
//加载过程列表
function loadProcessCombobox(parentId1) {
	twxAjax('Thing.Fn.DataDownload', 'QueryProcess', {
		parentId: parentId1 == '' ? 0 : parentId1
	}, true, function (data) {
		data.rows.unshift(emptyOption);
		$("#process").combobox({
			valueField: 'TREEID',
			// panelHeight: 'auto',
			panelHeight: 300,
			textField: 'NODENAME',
			data: data.rows,
			editable: true,
			onLoadSuccess: function () {
				// resizeComboboxWidth(this);
			}
		});
	});
}
var contextMenuWidth = 115;

//加载树结构
function loadTree() {
	var cb_success = function (res) {
		var datas = res.data;
		if (datas.length > 0) {
			datas = dealDataIcons(datas);
			datas = dealDataNodeName(datas);
			treeSetting.callback.onClick = function (event, treeId, treeNode) {
				parentId = treeNode.TREEID;
				reloadTable();
			};
			treeSetting.callback.onExpand = function (event, treeId, treeNode) {
				loadTreeMenu();
			};
			//禁止拖拽
			treeSetting.callback.beforeDrag = function () {
				return false;
			};
			treeSetting.callback.beforeDrop = function () {
				return false;
			};

			ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
			var nodes = ztreeObj.getNodes();
			for (var i = 0; i < nodes.length; i++) { //设置节点展开
				ztreeObj.expandNode(nodes[i], true, false, true);
			}
			loadTreeMenu();
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax('Thing.Fn.ProcessTree', 'QueryTreeRoot', {
		username: sessionStorage.getItem('username')
	}, true, cb_success);
}

//轮询推送列表信息，如果获取列表信息，则通知用户,并取消轮询
function QueryPushListInfo(treeId, pushKey) {
	//1.调用queryCompletedManualSyncTask方法获取是否存在当前处理人的后台任务；
	var fg_confirming = false;
	var pushInfo = "未开始";

	function queryInfo() {
		twxAjax('Thing.Fn.PushFiles', 'QueryPushInfo', {
			pushKey: pushKey
		}, true, cb_sucess, cb_err);
	}

	function cb_sucess(res) {
		if (res.success) {
			pushInfo = res.data.pushInfo;
			var pushId = res.data.pushId;
			if (pushInfo != "未开始") {
				window.clearInterval(pushListTimer);
				layui.use(['layer', 'table'], function () {
					var layer = layui.layer;
					var table = layui.table;
					layer.closeAll();

					var info = pushInfo + '，具体信息如下：';
					layer.open({
						title: info,
						type: 1,
						anim: false,
						openDuration: 200,
						isOutAnim: false,
						closeDuration: 200,
						shadeClose: false,
						// fixed: false,
						maxmin: false,
						resize: false, //不允许拉伸
						area: ['1400px', '700px'],
						content: '<table class="layui-table" id="list-table"></table>'
					});

					var inst = table.render({
						elem: '#list-table',
						id: "list-table",
						url: getUrl("Thing.Fn.PushFiles", 'QueryPushListPage', "&pushId=" + pushId),
						height: 630,
						cols: [
							[{
								title: '序号',
								type: "numbers",
								width: 60
							},
							{
								field: 'MODEL_NAME',
								title: '型号',
								width: 100
							},
							{
								field: 'PHASE_NAME',
								title: '阶段',
								width: 100
							},
							{
								field: 'DIR_NAME',
								title: '专业',
								width: 130
							},
							{
								field: 'LEAF_NAME',
								title: '过程',
								width: 150
							},
							{
								field: 'PUSH_TYPE',
								title: '类型',
								width: 100
							},
							{
								field: 'TITLE',
								title: '名称',
								width: 200
							},
							{
								field: 'FILE_NAME',
								title: '文件',
								width: 300
							},
							{
								field: 'CREATE_TIME',
								title: '时间',
								minwidth: 150
							}
							]
						],
						page: { //支持传入 laypage 组件的所有参数（某些参数除外，如：jump/elem） - 详见文档
							layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
							groups: 1,
							first: false,
							last: false
						},
						limits: [10, 15, 20, 30, 40, 50],
						limit: 10
					});
				});
			}
		} else {
			console.log(res.msg);
		}
	}


	function cb_err(data) {
		console.log("操作出错:" + data);
	}

	this.loop = function () {
		pushListTimer = window.setInterval(queryInfo, 1000);
	}
}



//加载树节点右键菜单
function loadTreeMenu() {
	$("#dpTree a").each(function (i, n) {
		var menu = [];
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		var imgSuffix = '../dataTree/';
		var pushMenu = {
			text: "推送档案",
			icon: imgSuffix + 'images/push.png',
			callback: function () {
				var treeId = node.TREEID;
				pushDataPackage(treeId);
			}
		};

		var viewPushStatusMenu = {
			text: "推送状态",
			icon: imgSuffix + 'images/refresh.png',
			callback: function () {
				var treeId = node.TREEID;
				viewPushStatus(treeId);
			}
		};

		var exportDkgMenu = {
			text: "导出数据包",
			icon: imgSuffix + 'images/file-export.png',
			callback: function () {
				layui.use('layer', function () {
					var layer = layui.layer;
					var loading = layer.msg("数据包导出中...", {
						icon: 16,
						shade: 0.3,
						time: 0
					});
					var cb_success = function (data) {
						if (data.rows.length > 0) {
							var id = data.rows[0].ID;
							var log = '下载管理-下载数据包(ID：' + data.rows[0]
								.ID + '、名称：' + data.rows[0].NAME +
								'、编号：' +
								data.rows[0].CODE + ')';
							var cb_success1 = function (data1) {
								if (data1.rows[0].COUNT > 0) {
									var url = fileHandlerUrl + "/list/package/export";
									// var form = $("<form></form>").attr("action", url).attr("method", "post");
									// form.append($("<input></input>").attr("type", "hidden").attr("name", "ids").attr("value", id));
									// form.append($("<input></input>").attr("type", "hidden").attr("name", "appkey").attr("value",
									//     twxAppKey));
									// form.appendTo('body').submit().remove();

									fileDownload(url, id, twxAppKey,
										loading);

									logRecord('下载', log, 1);
								} else {
									layer.msg('所选数据包下无采集数据！');
								}
							};
							twxAjax('Thing.Fn.DataDownload',
								'QueryDataPkgHasResult', {
								ids: id
							}, true, cb_success1);
						} else {
							layer.msg('该节点下未创建数据包！');
						}
					};
					//判断当前节点下有没有创建数据包
					twxAjax('Thing.Fn.DataPackage', 'QueryDataPkg', {
						refTreeId: node.TREEID,
						username: sessionStorage.getItem('username')
					}, true, cb_success);
				})

			}
		};


		if (node.NODETYPE == 'leaf' || ((node.NODETYPE == 'dir') && (!node.ISPARENT))) {
			$(n).contextMenu({
				width: contextMenuWidth,
				menu: [exportDkgMenu],
				target: function (ele) {
					var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
					ztreeObj.selectNode(node, false, true);
				}
			});
		} else if (node.NODETYPE == 'phase') {
			$(n).contextMenu({
				width: contextMenuWidth,
				menu: [pushMenu, viewPushStatusMenu],
				target: function (ele) {
					var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
					ztreeObj.selectNode(node, false, true);
				}
			});
		}
	});
}

function searchDataPkg() {
	if ($("#product").combobox('getValue') != '') {
		if ($("#phase").combobox('getValue') != '') {
			if ($("#major").combobox('getValue') != '') {
				if ($("#process").combobox('getValue') != '') {
					parentId = $("#process").combobox('getValue');
				} else {
					parentId = $("#major").combobox('getValue');
				}
			} else {
				parentId = $("#phase").combobox('getValue');
			}
		} else {
			parentId = $("#product").combobox('getValue');
		}
	}
	reloadTable();
}


function downloadDataPkg() {
	var sels = $('#' + tableName).datagrid('getSelections');
	layui.use('layer', function () {
		var layer = layui.layer;
		if (sels.length > 0) {
			var loading = layer.msg("数据包导出中...", {
				icon: 16,
				shade: 0.3,
				time: 0
			});
			var arr = [];
			var logArr = [];
			for (var i = 0; i < sels.length; i++) {
				arr.push(sels[i].ID);
				var str = '（ID：' + sels[i].ID + '、名称：' + sels[i].NAME + '、编号：' + sels[i].CODE + '）';
				logArr.push(str);
			}
			var logs = logArr.join('，');

			var ids = arr.join(',');
			var cb_success1 = function (data1) {
				if (data1.rows[0].COUNT > 0) {
					var url = fileHandlerUrl + "/list/package/export";
					// var form = $("<form></form>").attr("action", url).attr("method", "post");
					// form.append($("<input></input>").attr("type", "hidden").attr("name", "ids").attr("value", ids));
					// form.append($("<input></input>").attr("type", "hidden").attr("name", "appkey").attr("value",twxAppKey));
					// form.appendTo('body');
					// form.submit();

					fileDownload(url, ids, twxAppKey, loading);
					logRecord('下载', '下载管理-批量下载数据包【' + logs + '】', 1);
				} else {
					layer.msg('所选数据包下无采集数据！');
				}
			};
			twxAjax('Thing.Fn.DataDownload', 'QueryDataPkgHasResult', {
				ids: ids
			}, true, cb_success1);

		} else {
			layer.msg('请选择需要下载的数据包！');
		}
	});
}

function fileDownload(url, ids, twxAppKey, loading) {
	$.fileDownload(url, {
		httpMethod: 'POST',
		data: {
			"ids": ids,
			"appkey": twxAppKey,
			"secLevel": sessionStorage.getItem('secLevel'),
			"username": sessionStorage.getItem('username')
		},
		prepareCallback: function (url) { },
		abortCallback: function (url) {
			layer.close(loading);
			layer.msg("数据包导出异常！！");
		},
		successCallback: function (url) {
			layer.close(loading);
		},
		failCallback: function (html, url) {
			layer.close(loading);
			layer.msg("数据包导出失败！！");
		}
	});
}

function resetForm() {
	$("#product").combobox('setValue', '');
	$("#phase").combobox('setValue', '');
	$("#major").combobox('setValue', '');
	$("#process").combobox('setValue', '');
	$("#phase").combobox('loadData', [emptyOption]);
	$("#major").combobox('loadData', [emptyOption]);
	$("#process").combobox('loadData', [emptyOption]);
	parentId = 1;
}