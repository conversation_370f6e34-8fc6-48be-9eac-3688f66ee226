/**
 * components/costing/analyse/table.js - 成本分析表格
 *
 * 版本升级记录：
 * - 2025-07-11: 升级适配Handsontable 16.0.1版本
 *   * 增强错误处理机制，提高稳定性
 *   * 优化Handsontable配置，利用16.0.1性能改进
 *   * 改进上下文菜单定位
 *   * 增加参数验证和异常捕获
 *   * 保持ES5语法兼容性
 *
 * <AUTHOR>
 * @version 16.0.1-compatible
 */
function renderForm(myForm) {
	var fixedRowsTop = myForm.headerRow;
	var initData = myForm.tableData;
	var merged = myForm.merged;
	var metas = myForm.meta;
	var colWidths = myForm.colWidths;

	function myCellRenderer(instance, td, row, col, prop, value, cellProperties) {
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		const stringifiedValue = Handsontable.helper.stringify(value);
	}

	// 16.0.1版本兼容性：增强容器检查
	var container = document.getElementById('handsontable');
	if (!container) {
		console.error('Handsontable容器未找到');
		return;
	}

	// 16.0.1版本优化：增强错误处理的Handsontable实例化
	try {
		window.hot = new Handsontable(container, {
		data: initData,
		fixedRowsTop: fixedRowsTop,
		mergeCells: merged,
		rowHeaders: false,
		colHeaders: false,
		rowHeights: 40,
		dropdownMenu: false,
		customBorders: true,
		comments: true,
		colWidths: colWidths,
		fillHandle: true,
		renderer: myCellRenderer,
		language: 'zh-CN',
		licenseKey: 'non-commercial-and-evaluation',
		className: 'htMiddle htCenter',
		manualColumnResize: true,
		manualRowResize: true,
		// 16.0.1版本性能优化配置
		preventOverflow: 'horizontal',
		preventWheel: true,
		contextMenu: {
			callback(key, selection, clickEvent) {
				// Common callback for all options
				console.log(key, selection, clickEvent);
			},
			items: {
				alignment: {
					name: '对齐'
				},
				fontSize: {
					name() {
						return '字体大小';
					},
					submenu: {
						items: HotUtil.fontSizeItems()
					}
				},
				bold: { // Own custom option
					name() { // `name` can be a string or a function
						return '加粗'; // Name can contain HTML
					},
					callback(key, selection, clickEvent) { // Callback for specific option
						HotUtil.dealClass("c-bold", "add");
					}
				},
				cancelBold: { // Own custom option
					name() { // `name` can be a string or a function
						return '取消加粗'; // Name can contain HTML
					},
					callback(key, selection, clickEvent) { // Callback for specific option
						HotUtil.dealClass("c-bold", "cancel");
					}
				},
				quote_formula: {
					name() {
						return '引用公式';
					},
					callback(key, selection, clickEvent) {
						quoteFormula(key, selection, clickEvent);
					},
					disabled() {
						var isDisabled = false;
						//只有选中一个单元格的时候才允许
						if (HotUtil.getSelecteds().length != 1) {
							isDisabled = true;
						}
						if (!isDisabled) {
							isDisabled = HotUtil.atLeastOneReadOnly(this)
						}
						return isDisabled;
					}
				},
			}
		}
	});

	// 16.0.1版本上下文菜单定位优化
	if (window.hot && window.hot.addHook) {
		window.hot.addHook('afterContextMenuShow', function() {
			// 调用HandsonTableUtil的菜单定位优化函数
			if (typeof HotUtil !== 'undefined' && HotUtil.adjustContextMenuPosition) {
				HotUtil.adjustContextMenuPosition();
			}
		});
	}

	} catch (error) {
		console.error('Handsontable初始化失败:', error);
		layer.alert('表格初始化失败: ' + error.message, {
			icon: 2,
			title: '初始化错误'
		});
		return;
	}

	// 16.0.1版本优化：增强错误处理的setCellMeta操作
	try {
		for (var m = 0; m < metas.length; m++) {
		var meta = metas[m];
		if (meta) {
			var isReadOnly = false;
			if (meta.row < fixedRowsTop) {
				isReadOnly = true;
			}
			for (var i = 0; i < myForm.cols.length; i++) {
				var col = myForm.cols[i];
				if (col.readOnly) {
					if (meta.col == col.col) {
						isReadOnly = true;
						break;
					}
				}
			}
			meta.readOnly = isReadOnly;
		}
	}


	for (var m = 0; m < metas.length; m++) {
		var meta = metas[m];
		if (meta && typeof meta.row === 'number' && typeof meta.col === 'number') {
			try {
				meta.className = 'htMiddle htCenter';
				//将表头添加背景颜色
				if (meta.row < fixedRowsTop) {
					meta.className = meta.className + " td-bg";
				}

				// 安全地设置单元格元数据
				if (meta.className) {
					window.hot.setCellMeta(meta.row, meta.col, 'className', meta.className);
				}
				if (meta.readOnly) {
					window.hot.setCellMeta(meta.row, meta.col, 'readOnly', meta.readOnly);
				}
				if (meta.eles) {
					window.hot.setCellMeta(meta.row, meta.col, 'eles', meta.eles);
				}
				if (meta.comment) {
					window.hot.setCellMeta(meta.row, meta.col, 'comment', meta.comment);
				}
			} catch (metaError) {
				console.warn('设置单元格(' + meta.row + ',' + meta.col + ')元数据时出错:', metaError);
			}
		}
	}

	// 16.0.1版本优化：增强错误处理的渲染操作
	try {
		window.hot.render();
	} catch (renderError) {
		console.error('表格渲染失败:', renderError);
		layer.alert('表格渲染失败，请刷新页面重试', {
			icon: 2,
			title: '渲染错误'
		});
	}

	} catch (error) {
		console.error('设置单元格元数据时发生异常:', error);
		layer.alert('表格配置失败: ' + error.message, {
			icon: 2,
			title: '配置错误'
		});
	}
}

//引用公式的单元格
var modelCell;
/**
 * 引用公式
 */
function quoteFormula(key, selection, clickEvent) {
	modelCell = selection[0].start;
	layer.open({
		title: '引用公式',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		zIndex: 1001,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: ['1200px', '650px'],
		content: `<div id="formulaContent" style="width:100%;height:98%;overflow:hidden" class="layui-col-space10">
					 <div class="layui-row" style="height:100%">
						<div class="layui-col-md5" style="height:100%">
							<table class="layui-hide" id="model-table" lay-filter="model-table"></table>
						</div>
						<div class="layui-col-md7" style="height:100%">
							<div class="layui-row" id="calc-div" style="width:100%;height:100%;overflow:auto">
							</div>
						</div>
					  </div>
				</div>`,
		cancel: function(index, layero) {
			if (confirm('确定要关闭么')) {
				layer.close(index);
			}
			return false;
		},
		btn: ['确定', '取消'],
		btn1: function(index, layero, that) {

		},
		btn2: function(index, layero, that) {
			if (confirm('确定要关闭么')) {
				layer.close(index);
			}
			return false;
		},
		success: function(layero, index) {
			renderModelTable();
		}
	});
}


/**
 * 加载模型表
 */
function renderModelTable() {
	var models = window.hot.getCellMeta(modelCell.row, modelCell.col).models || [];
	table.render({
		elem: '#model-table',
		id: 'model-table',
		data: models,
		toolbar: `<div class="layui-btn-container">
					<button class="layui-btn layui-btn-sm" lay-event="add-btn">添加</button>
					<button class="layui-btn layui-btn-sm layui-bg-red" lay-event="delete-btn">删除</button>
				</div>`,
		defaultToolbar: [],
		height: 520, // 最大高度减去其他容器已占有的高度差
		cellMinWidth: 80,
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'modelId',
					hide: true
				},
				{
					field: 'modelName',
					width: 100,
					title: '模型名称',
					align: 'center'
				},
				{
					field: 'creator',
					title: '创建人',
					width: 100,
					align: 'center',
					hide: true
				},
				{
					field: 'createTime',
					width: 160,
					title: '创建时间',
					align: 'center',
					hide: true
				},
				{
					fixed: 'right',
					title: '操作',
					width: 180,
					minWidth: 180,
					toolbar: `<div class="layui-clear-space">
								<a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="edit">编辑</a>
								<a class="layui-btn layui-btn-xs layui-bg-red" lay-event="delete">删除</a>
							</div>`,
					align: 'center'
				}
			]
		],
		done: function() {

		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});


	// 触发单元格工具事件
	table.on('tool(model-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'edit') {
			editModel(data);
		} else if (obj.event === 'delete') {
			deleteModel([data.modelId]);
		}
	});

	// 工具栏事件
	table.on('toolbar(model-table)', function(obj) {
		var id = obj.config.id;
		var checkStatus = table.checkStatus(id);
		var othis = lay(this);
		switch (obj.event) {
			case 'add-btn':
				addModel();
				break;
			case 'delete-btn':
				var checkDatas = checkStatus.data;
				var ids = [];
				for (var i = 0; i < checkDatas.length; i++) {
					ids.push(checkDatas[i].modelId);
				}
				deleteModel(ids);
				break;
		};
	});
}

/**
 * 添加计算模型引用
 */
function addModel() {
	layer.open({
		title: '选择模型',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		maxmin: false,
		resize: false, //不允许拉伸
		// maxmin: true,
		area: ["500px", '600px'],
		content: '<div id="selectModelContent" style="padding-top: 15px;padding-right: 15px;"><ul id="tree" class="ztree"></ul></div>',
		btn: ['确定', '关闭'],
		yes: function(index, layero) {
			//获取选中的模型数据
			var checkedNodes = ztreeObj.getCheckedNodes();
			if (checkedNodes.length == 0) {
				layer.alert('请选择一个计算模型！', {
					icon: 2
				});
			} else {
				var node = checkedNodes[0];
				addModelDataToTable(node, function() {
					layer.close(index);
				});
			}
		},
		btn2: function(index, layero) {
			return true;
		},
		success: function() {
			loadTree(false, true);
		}
	});
}

/**
 * 将选中的模型数据添加到模型表格中
 */
function addModelDataToTable(node, successFn) {
	//获取当前表格的缓存数据
	var datas = table.cache['model-table'];
	//根据id校验选中的模型是否已经存在
	var isExist = false;
	for (var i = 0; i < datas.length; i++) {
		if (node.ID == datas[i].modelId) {
			isExist = true;
			break;
		}
	}
	if (!isExist) {
		var newData = {
			modelId: node.ID,
			modelName: node.NAME,
			inputParams: node.inputParams,
			outputParams: node.outputParams,
			creator: sessionStorage.getItem("username"),
			createTime: layui.util.toDateString()
		};
		datas.push(newData);
		table.renderData('model-table');
		saveAllModel();
		editModel(newData);
		successFn();
	} else {
		layer.alert("所选模型已经存在，请重新选择！", {
			icon: 2
		});
	}
}

/**
 * 删除引用模型表中的模型
 * @param {Object} ids
 */
function deleteModel(ids) {
	if (ids.length == 0) {
		layer.alert("至少选择一个模型！", {
			icon: 2
		});
	} else {
		var msg = "确认删除模型吗？";
		layer.confirm(msg, {
			icon: 3,
			title: '提示'
		}, function(index) {
			//获取当前表格的缓存数据
			var oldDatas = table.cache['model-table'];
			var newDatas = [];
			for (var j = 0; j < oldDatas.length; j++) {
				var modelId = oldDatas[j].modelId;
				var isDelete = false;
				for (var i = 0; i < ids.length; i++) {
					if (ids[i] == modelId) {
						isDelete = true;
						break;
					}
				}
				if (!isDelete) {
					newDatas.push(oldDatas[j]);
				}
			}
			table.cache['model-table'] = newDatas;
			table.renderData('model-table');
			saveAllModel();
			layer.close(index);
		});
	}
}

/**
 * 保存所有的模型信息到单元格信息中
 */
function saveAllModel() {
	window.hot.setCellMeta(modelCell.row, modelCell.col, 'models', table.cache['model-table']);
}

/**
 * 编辑模型的输入参数
 */
function editModel(rowData) {
	$("#calc-div").empty();
	var params = rowData.inputParams;
	var results = rowData.outputParams;
	var $form = $('<form class="layui-form" style="margin-top:15px" lay-filter="calc-form"></form>');
	var paramsHtml = createFormItemHtmlStr(params, false);
	$form.append(paramsHtml);

	var btn = `<div class="layui-form-item">
			<div class="layui-input-block">
				<div class="layui-footer">
					<button class="layui-btn" id="calc-submit" lay-submit="" lay-filter="calc-submit">计算</button>
					<button type="reset" id="calc-reset" class="layui-btn layui-btn-primary">清空</button>
				</div>
			</div>
		</div>`;
	$form.append(btn)

	var resultsHtml = createFormItemHtmlStr(results, true);
	$form.append(resultsHtml);

	//自定义输出语句
	var outputStr = '<div class="layui-form-item">\
								<label class="layui-form-label">自定义输出:</label>\
								<div class="layui-input-block" style="margin-left:120px" >\
									<textarea id="outputStr" style="width: calc(100% - 90px);height:150px" class="layui-textarea"></textarea>\
									<div class="layui-input-suffix" style="width:90px">\
										<button id="copy" class="layui-btn layui-bg-blue output-btn" style="display:none;margin-top:7px">复制</button>\
										<button id="rewrite" class="layui-btn layui-btn-primary output-btn" style="display:none;margin-top:10px;margin-left:0">重写</button>\
										<button id="save" class="layui-btn output-btn" style="display:none;margin-top:10px;margin-left:0">保存</button>\
									</div>\
								</div>\
							</div>';

	$("#calc-div").append($form);
	$("#calc-div").append(outputStr);

	var outputStr = rowData.outputStr || "";
	if (outputStr) {
		$("#outputStr").val(outputStr);
		renderOutBtn(rowData, params, results, function(str) {});
	}

	form.on('submit(calc-submit)', function(formData) {
		var fields = formData.field;
		try {
			calcResult(fields, results);
			//生成自定义语句
			generateOutputStr(rowData, params, results);
			return false;
		} catch (e) {
			console.error(e);
			return false;
		} finally {
			return false;
		}
	});
}

/**
 * 初始化输出语句旁边的三个按钮
 */
function renderOutBtn(rowData, params, results, fn) {
	$(".output-btn").show();

	var inputs = [];
	for (var i = 0; i < params.length; i++) {
		var name = params[i]['NAME'];
		var code = params[i]['CODE'];
		var value = $("#calc_" + code).val();
		params[i]['VALUE'] = value
		inputs.push(name + ':' + value);
	}

	var outputs = [];
	for (var i = 0; i < results.length; i++) {
		var name = results[i]['NAME'];
		var code = results[i]['CODE'];
		var value = $("#calc_" + code).val();
		results[i]['VALUE'] = value;
		outputs.push(name + ':' + value);
	}

	var str = inputs.join("、") + "通过模型《" + rowData.modelName + "》计算得出" + outputs.join("、");
	fn(str);

	$("#rewrite").unbind("click").bind("click", function() {
		$("#outputStr").val(str);
	});

	$("#copy").unbind("click").bind("click", function() {
		$("#outputStr").val();
		// 获取textarea元素
		var textarea = document.getElementById("outputStr");
		// 选择文本
		textarea.select();
		// 复制文本到剪贴板
		document.execCommand("copy");
		// 清除选择
		window.getSelection().removeAllRanges();
		// 提示用户已复制文本
		layer.msg("文本已复制到剪贴板");
	});

	//保存
	$("#save").unbind("click").bind("click", function() {
		//将所有信息保存
		var allDatas = table.cache["model-table"];
		for (var i = 0; i < allDatas.length; i++) {
			if (allDatas[i].modelId == rowData.modelId) {
				allDatas[i].inputParams = params;
				allDatas[i].outputParams = results;
				allDatas[i].outputStr = $("#outputStr").val();
				break;
			}
		}
		saveAllModel();
		layer.msg('保存成功！');
	});
}
/**
 * 生成输出语句
 */
function generateOutputStr(rowData, params, results) {
	renderOutBtn(rowData, params, results, function(str) {
		$("#outputStr").val(str);
	});
}