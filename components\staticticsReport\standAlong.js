var curInfo = {
	tableId: 'dataTable',
	tableType: '单机装星情况',
	rsId: parent.window.tjfxRsID,
	columns: [
		[{
			field: 'TJFX_ID',
			title: '统计分析ID',
			hidden: true
		}, {
			field: 'DATA_ID',
			title: 'MES DATAID',
			hidden: true
		}, {
			field: 'REF_DPID',
			title: '关联的数据包ID',
			hidden: true
		}, {
			field: 'MODELCODE',
			title: '型号代号',
			width: 100,
			align: 'center'
		}, {
			field: 'ISNAME',
			title: '单机名称',
			width: 100,
			align: 'center'
		}, {
			field: 'ISCODE',
			title: '单机代号',
			width: 100,
			align: 'center'
		}, {
			field: 'BATCH',
			title: '装星批次',
			width: 120,
			align: 'center'
		}, {
			field: 'HEAT',
			title: '热控实施',
			width: 100,
			align: 'center'
		}, {
			field: 'INSTALLAREA',
			title: '接地位置',
			width: 100,
			align: 'center'
		}, {
			field: 'RESISTANCE',
			title: '实际接地电阻',
			width: 120,
			align: 'center'
		}, {
			field: 'MOMENT',
			title: '紧固件力矩要求',
			width: 140,
			align: 'center'
		}, {
			field: 'TORQUE',
			title: '实测力矩',
			width: 100,
			align: 'center'
		}, {
			field: 'WEIGHT',
			title: '重量',
			width: 80,
			align: 'center'
		}, {
			field: 'CREATEDATE',
			title: '操作时间',
			width: 150,
			align: 'center'
		}, {
			field: 'NOTE',
			title: '备注',
			width: 200,
			align: 'center'
		}]
	]
};
/** 重置搜索条件 */
var resetSearchCondition = function() {
	$('#isname').textbox('setValue', '');
	$('#iscode').textbox('setValue', '');
	$('#batch').textbox('setValue', '');
	$('#heat').textbox('setValue', '');
	$('#installarea').textbox('setValue', '');
	$('#resistance').textbox('setValue', '');
	$('#moment').textbox('setValue', '');
	$('#torque').textbox('setValue', '');
	$('#weight').textbox('setValue', '');
	$('#note').textbox('setValue', '');
};

var getFieldValue = function() {
	var param = {};
	param.type = curInfo.tableType;
	//数据包的ID
	param.rsId = curInfo.rsId;
	
	var isname = $('#isname').textbox('getValue');
	var iscode = $('#iscode').textbox('getValue');
	var batch = $('#batch').textbox('getValue');
	var heat = $('#heat').textbox('getValue');
	var installarea = $('#installarea').textbox('getValue');
	var resistance = $('#resistance').textbox('getValue');
	var moment = $('#moment').textbox('getValue');
	var torque = $('#torque').textbox('getValue');
	var weight = $('#weight').textbox('getValue');
	var note = $('#note').textbox('getValue');
	param.conditionData = {
		isname: isname,
		iscode: iscode,
		batch: batch,
		heat: heat,
		installarea: installarea,
		resistance: resistance,
		moment: moment,
		torque: torque,
		weight: weight,
		note: note
	};
	return param;
};
