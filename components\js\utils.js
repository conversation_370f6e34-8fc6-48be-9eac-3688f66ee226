"use strict";
layui.define(['jquery'], function(exports) {
	var $ = layui.jquery;
	var utils = {
		//连接ThingWorx并保持会话
		twxconnect:function(cb_success,cb_error){
			var val =  sessionStorage.getItem('twxsession');
			// if(!val || val === false){
				$.ajax({
					url: loginApi + '&appKey=' + twxAppKey,
					type:'GET',
					async:true,
					dataType:'json',
					data:{
						'x-thingworx-session':true
					},
					success:cb_success,
					error: cb_error
				});
			// }
		},
		//当用户退出时中断用户的会话
		twxterminate:function(cb_success,cb_error){
			if(sessionStorage.getItem('twxsession') === true){
				$.ajax({
					url: logoutApi,
					type:'GET',
					async:true,
					dataType:'json',
					data:{},
					success:cb_success,
					error : cb_error
				});
			}
		},
		//异步方法的函数调用
		twxajaxasync:function(thingName,serviceName,requestData,cb_success,cb_error){
			var url = twxserver + "/Thingworx/Things/" + thingName;
				url += "/Services/" + serviceName;
				url += "?" + serviceSuffix;
            $.ajax({
                type: "POST",
                url: url,
                async: true,
                dataType: "json",
                data: JSON.stringify(requestData),
                contentType: "application/json;charset=utf-8",
                success: cb_success,
                error: cb_error
            });
		},
		//ajax方法的函数调用
		twxajax: function(ThingName, ServiceName, RequestData,cb_success,cb_error) {
			var url = '';
			url = twxserver + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?" + serviceSuffix;
			$.ajax({
				type: "POST",
				url: url,
				async: false,
				dataType: "json",
				data: JSON.stringify(RequestData),
				contentType: "application/json;charset=utf-8",
				success: cb_success,
				error: cb_error
			});
		},
		getUrl: function(ThingName, ServiceName, RequestData) {
			var url = '';
			if(RequestData == undefined) {
				RequestData = '';
			}
			url = twxserver + "/Thingworx/Things/" + ThingName + "/Services/" + ServiceName + "?" + serviceSuffix;
			return url;
		},
		getMenuListData: function(list) {
			var item, result = [];
			var getAllChildrens = function(list1, item1) {
				var childrens = getNextLevelChildrens(list1, item1);
				for(var i = 0, ii = childrens.length; i < ii; i++) {
					getAllChildrens(list1, childrens[i]);
				}
			}
			//遍历list剩下的数据，找到item的下一层的子节点
			var getNextLevelChildrens = function(list2, item2) {
				var childrens = [];
				for(var i = list2.length - 1; i >= 0; i--) {
					var mid = list2[i];
					if(mid.menupid === item2.menuid) {
						childrens.push(mid);
						list2.splice(i, 1);
					}
				}
				if(childrens.length > 0) {
					item2.list = childrens;
				}
				return childrens;
			}
			//遍历根节点，递归处理其所有子节点的数据
			//每处理完一个根节点，就将其及其所有子节点从list中删除，加快递归速度
			while(list.length) {
				item = list[0];
				list.splice(0, 1);
				delete item.menupid;
				getAllChildrens(list, item);
				result.push(item);
			}
			return result;
		},
		/**
		 * 判断一个元素是否在一个数组中
		 * @param arr
		 * @param val
		 * @returns {boolean}
		 */
		contains: function(arr, val) {
			return utils.indexOf(arr, val) != -1 ? true : false;
		},

		/**
		 * 获取数组的下标
		 * @param arr
		 * @param val
		 * @returns {number}
		 */
		indexOf: function(arr, val) {
			for(var i = 0; i < arr.length; i++) {
				if(arr[i] == val) {
					return i;
				}
			}
			return -1;
		},
		removeFunc: function() {
			var func = JSON.parse(sessionStorage.getItem('func'));
			var idarr = [];
			if(func != null) {
				for(var i = 0; i < func.length; i++) {
					var id = func[i].id;
					idarr.push(id);
				}
			}
			$("[func-id]").each(function(i, e) {
				var func_id = $(e).attr('func-id');
				if(utils.contains(idarr, func_id)) {
					$(e).show();
				}
			})
		},
		//格式化日期
		getFormatDate: function(date, fmt) {
			//author: meizz 
			var o = {
				"M+": date.getMonth() + 1, //月份 
				"d+": date.getDate(), //日 
				"h+": date.getHours(), //小时 
				"m+": date.getMinutes(), //分 
				"s+": date.getSeconds(), //秒 
				"q+": Math.floor((date.getMonth() + 3) / 3), //季度 
				"S": date.getMilliseconds() //毫秒 
			};
			if(/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
			for(var k in o)
				if(new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
			return fmt;
		}
	};
	exports('utils', utils);
});