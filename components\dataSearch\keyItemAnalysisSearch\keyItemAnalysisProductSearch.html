<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<link rel="Shortcut Icon" href="../../../img/favicon.ico">
		<link rel="stylesheet" href="../../../plugins/layui/css/layui.css" media="all">
		<!-- <link href="../../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

		<link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
		<link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> 

		<link rel="stylesheet" href="../../../css/icon.css">

		<script src="../../../plugins/InsdepUI/jquery.min.js"></script>
		<script src="../../../plugins/InsdepUI/jquery.easyui.min.js"></script>
		<script src="../../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
		<script src="../../../plugins/layui/layui.js"></script>

		<link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
		<script src="../../../plugins/easyui/jquery.min.js"></script>
		<script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
		<script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

		<!-- <script>
			sessionStorage.setItem('secLevel','1');
			sessionStorage.setItem('username','adm');
			sessionStorage.setItem('roleid','4,29');
		</script> -->

		<script src="../../js/config/twxconfig.js"></script>
		<script src="../../js/util.js"></script>

		<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/metroStyle/metroStyle.css">
		<link rel="stylesheet" type="text/css" href="../../../plugins/ztree/css/contextMenu.css">
		<script type="text/javascript" src="../../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
		<script type="text/javascript" src="../../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
		<script type="text/javascript" src="../../dataTree/tree.js"></script>
		<script type="text/javascript" src="../../dataTree/bom_tree.js"></script>
		<!-- <script type="text/javascript" src="../../../plugins/loading/jquery.loading.min.js"></script> -->

		<!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
		<script type="text/javascript" src="../../js/logUtil.js"></script>
		<script type="text/javascript">
			layui.use(['layer', 'form'], function() {
				//此处省略一万字
				//.........................

				var form=layui.form;
				//form.render();//没有写这个，操作后没有效果
				form.render();
			});
			$(function() {
				twxAjax("Thing.Fn.DataDownload", "QueryProduct", {

				}, false, function(data) {
					if (data == undefined || data.rows == undefined || data.rows.length == 0) {

						layer.alert("该未查询到型号数据！", {
							icon: 2
						});
					} else {
						console.log(data);
						modelRender(data.rows);
						console.log("render 结束");
						$('#modelSelect').combobox("loadData", data.rows);
					}
				});
			});

			function modelRender(data) {
				$('#modelSelect').combobox({
					valueField: 'NODENAME',
					textField: 'NODENAME',
					data: data.rows,
					multiple: true,
					filter: function(q, row) { //filter属性模糊查询
						var opts = $(this).combobox('options');
						//return row[opts.textField].indexOf(q) == 0; 
						return row[opts.textField].indexOf(q) > -1; //将从头位置匹配改为任意匹配 
					}
				});
			}

			function doSearch() {
				var models = $('#modelSelect').combobox('getValue');
				var prodName = $('#prodName').val();
				var datatype = getQueryString('datatype');
				console.log("=====param start=====")
				console.log(models);
				console.log(prodName);
				console.log(datatype);
				console.log("=====param end=====");
				twxAjax("Thing.Fn.KeyAttrAnalyze", "getProductByTableName", {
						tableName: datatype,
						models: models,
						prodName: prodName
					}, false, function(data) {

						if (data == undefined || data.rows.length == 0) {
							$("#productSelections").empty();
							layer.alert("该类型没有配置[名称]参数，请配置后重试！", {
								icon: 2
							});
						} else {
							$("#productSelections").empty();
							for (var i = 0; i < data.rows.length; i++) {
								if (i % 5 == 0) {
									var targetObj = $("#productSelections").append('<br/><input style="margin-right:10px" type="checkbox" lay-filter="product" name="product" title="' + data.rows[i].PN + '" value="' + data.rows[i].PN + '">'+'<label style="margin-right:20px">'+data.rows[i].PN+'</label>');
									//form.render();
								} else {
									var targetObj = $("#productSelections").append('<input style="margin-right:10px" type="checkbox" lay-filter="product" name="product" title="' + data.rows[i].PN + '" value="' + data.rows[i].PN + '">'+'<label style="margin-right:20px">'+data.rows[i].PN+'</label>');
									//form.render();
								}

							}
						}
						// form.render();
					
				});

			}

			function getQueryString(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
				var r = window.location.search.substr(1).match(reg);
				if (r != null) return unescape(r[2]);
				return null;
			}
		</script>
	</head>
	<body>
		<br />
		
			<div id="more-container" style="margin-left: 20px;">
				&nbsp;&nbsp;请选择型号：<select id="modelSelect" class="easyui-combobox" name="model" style="width:40%;">
					
				</select><br /><br />
				&nbsp;&nbsp;产&nbsp;品&nbsp;名&nbsp;称：<input id="prodName" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:300px">&nbsp;(使用*模糊查询)<br /><br />
				<button type="button" class="layui-btn layui-btn-primary" style="margin-left: 10%;" onclick="doSearch()">查询</button>

				<div id="productSelections" class="layui-input-block">
					<!-- <input type="checkbox" name="" lay-filter="type" lay-skin="primary" label="是否允许跳过" value="0">是否允许跳过 -->
				</div>
			</div>
		




	</body>
</html>
