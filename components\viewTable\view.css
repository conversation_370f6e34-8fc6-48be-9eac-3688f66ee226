#table {
    padding: 0px 10px;
}

.sticky-thead {
    position: sticky;
    top: -1px;
    z-index: 100;
}

.sticky-thead tr {
    font-weight: bold;
    background-color: #e6e6e6 !important;
}

.sign-img {
    max-width: 90px !important;
    max-height: 100px;
    margin-right: 10px;
    margin-top: 10px;
}

.data-table.layui-table td,
.data-table.layui-table th,
.data-table .layui-table-col-set,
.data-table .layui-table-fixed-r,
.data-table .layui-table-grid-down,
.data-table .layui-table-header,
.data-table .layui-table-page,
.data-table .layui-table-tips-main,
.data-table .layui-table-tool,
.data-table .layui-table-total,
.data-table .layui-table-view,
.data-table.layui-table[lay-skin="line"],
.data-table.layui-table[lay-skin="row"] {
    border-color: #333;
}

.data-table.layui-table td,
.data-table.layui-table th {
    color: #333;
    font-size: 13px;
}

.c-bold {
    font-weight: bold;
}

.htLeft {
    text-align: left;
}

.htCenter {
    text-align: center;
}

.htRight {
    text-align: right;
}

.htJustify {
    text-align: justify;
}

.htTop {
    vertical-align: top;
}

.htMiddle {
    vertical-align: middle;
}

.htBottom {
    vertical-align: bottom;
}

.font-size-12 {
    font-size: 12px !important;
    line-height: 19px !important;
}

.font-size-13 {
    font-size: 13px !important;
    line-height: 20px !important;
}

.font-size-14 {
    font-size: 14px !important;
    line-height: 21px !important;
}

.font-size-15 {
    font-size: 15px !important;
    line-height: 22px !important;
}

.font-size-16 {
    font-size: 16px !important;
    line-height: 23px !important;
}

.font-size-17 {
    font-size: 17px !important;
    line-height: 24px !important;
}

.font-size-18 {
    font-size: 18px !important;
    line-height: 25px !important;
}

.font-size-19 {
    font-size: 19px !important;
    line-height: 26px !important;
}

.font-size-20 {
    font-size: 20px !important;
    line-height: 27px !important;
}

.font-size-21 {
    font-size: 21px !important;
    line-height: 28px !important;
}

.font-size-22 {
    font-size: 22px !important;
    line-height: 29px !important;
}

.font-size-23 {
    font-size: 23px !important;
    line-height: 30px !important;
}

.font-size-24 {
    font-size: 24px !important;
    line-height: 31px !important;
}

.font-size-25 {
    font-size: 25px !important;
    line-height: 32px !important;
}

.font-size-26 {
    font-size: 26px !important;
    line-height: 33px !important;
}

.font-size-27 {
    font-size: 27px !important;
    line-height: 34px !important;
}

.font-size-28 {
    font-size: 28px !important;
    line-height: 35px !important;
}

.font-size-29 {
    font-size: 29px !important;
    line-height: 36px !important;
}

.font-size-30 {
    font-size: 30px !important;
    line-height: 37px !important;
}

.font-size-31 {
    font-size: 31px !important;
    line-height: 38px !important;
}

.font-size-32 {
    font-size: 32px !important;
    line-height: 39px !important;
}

.font-size-33 {
    font-size: 33px !important;
    line-height: 40px !important;
}

.font-size-34 {
    font-size: 34px !important;
    line-height: 41px !important;
}

.font-size-35 {
    font-size: 35px !important;
    line-height: 42px !important;
}

.font-size-36 {
    font-size: 36px !important;
    line-height: 43px !important;
}

.font-size-37 {
    font-size: 37px !important;
    line-height: 44px !important;
}

.font-size-38 {
    font-size: 38px !important;
    line-height: 45px !important;
}

.font-size-39 {
    font-size: 39px !important;
    line-height: 46px !important;
}

.font-size-40 {
    font-size: 40px !important;
    line-height: 47px !important;
}

.font-color-purple {
    color: purple !important;
}

.font-color-green {
    color: green !important;
}

.font-color-orange {
    color: orange !important;
}

.font-color-deeppink {
    color: deeppink !important;
}

.image-thumbnails {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
    justify-content: center;
    overflow-y: auto;
}

.image-item {
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    width: 130px;
    transition: all 0.3s;
    margin-bottom: 5px;
}

.image-item:hover {
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
    transform: scale(1.05);
}

.thumbnail {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 2px;
}

.image-info {
    text-align: center;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

td[data-hasimg="true"] {
    position: relative;
    cursor: context-menu;
}

td[data-hasimg="true"]:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.img-link {
    color: red !important;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-block;
    margin: 2px 0;
}

.img-link:hover {
    color: #ff5722 !important;
    text-decoration: underline;
}

.layui-menu-body-title {
    padding: 0px 5px;
    margin: 0;
}

/* 图片提示区域样式 */
#image-notice {
    margin: 20px;
    max-width: 800px;
}

#missing-files li {
    margin-bottom: 5px;
    font-family: monospace;
    word-break: break-all;
}
