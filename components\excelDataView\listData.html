<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">

		<link rel="Shortcut Icon" href="../../img/favicon.ico">
		<link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">

		<link rel="stylesheet" href="../../css/icon.css">

		<script src="../../plugins/layui/layui.js"></script>

		<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
		<script src="../../plugins/easyui/jquery.min.js"></script>
		<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
		<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

		<script src="../js/config/twxconfig.js"></script>
		<script src="../js/util.js"></script>

		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">
		<script type="text/javascript" src="../../components/dataTree/bom_tree.js"></script>
		<script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
		<script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>

		<script type="text/javascript" src="../js/intercept.js"></script>
		<script type="text/javascript" src="../js/logUtil.js"></script>

		<title>查看合集页面</title>
		<style>
			/* 弹窗不加载滚动条 */
			.datalinklabel {
				width: 90px;
			}

			.tablelabeltd {
				width: 80px;
				text-align: right;
				padding: 5px;
			}

			.layui-btn-mycolor3 {
				background-color: #CCDB75;
			}

			.layui-btn-mycolor4 {
				background-color: #FBA0A0;
			}

			.layui-btn-mycolor5 {
				background-color: #F5B81E;
			}

			.search-form .layui-input {
				height: 30px;
			}

			.search-form .layui-form-label {
				padding: 5px 15px;
			}

			.search-form .layui-form-mid {
				padding: 5px 0px !important;
			}

			.search-form .layui-form-item {
				margin: 10px 0px;
			}

			.search-form .layui-form-item .layui-input-inline {
				margin-right: 0px;
			}

			.search-form .layui-form-item .layui-inline {
				margin-bottom: 0px;
				margin-right: 0px;
			}

			.search-form .layui-form-label {
				width: 86px;
			}

			.search-form .layui-btn {
				height: 28px;
			}
		</style>
	</head>
	<body>
		<div class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
			<div data-options="region:'center',border:false">
				<div id="list_table_table_tb" style="padding: 5px;display: none;">
					<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" func="" id="list_table_stateCheck">
						<i class="layui-icon">&#xe672;</i> 状态确认
					</button>
					<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor4" func="" id="list_table_param">
						<i class="layui-icon">&#xe642;</i> 编写属性
					</button>
					<button type="button" class="layui-btn layui-btn-sm" func="" id="list_table_datapkgLink">
						<i class="layui-icon">&#xe64c;</i> 数据包关联
					</button>
					<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor3" func="" id="list_table_procuct_link">
						<i class="layui-icon">&#xe64c;</i> 产品结构树关联
					</button>
					<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" func="" id="list_table_delete">
						<i class="layui-icon">&#xe63c;</i> 删除
					</button>
					<button type="button" class="layui-btn layui-btn-sm layui-btn-mycolor5 layui-hide" func="" id="list_table_link_change">
						<i class="layui-icon">&#xe64c;</i> 关联更改单
					</button>
				</div>
				<div id="dataTable"></div>
			</div>
		</div>
	</body>
</html>

<script src="../js/listDataOpt.js"></script>
<script src="listData.js"></script>