import*as e from"jszip";var t={522:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OpenXmlPackage=void 0;const a=r(943),s=r(472),n=r(593),l=r(461);class o{constructor(e,t){this._zip=e,this.options=t,this.xmlParser=new s.XmlParser}get(e){return this._zip.files[function(e){return e.startsWith("/")?e.substr(1):e}(e)]}update(e,t){this._zip.file(e,t)}static async load(e,t){const r=await a.loadAsync(e);return new o(r,t)}save(e="blob"){return this._zip.generateAsync({type:e})}load(e,t="string"){return this.get(e)?.async(t)??Promise.resolve(null)}async loadRelationships(e=null){let t="_rels/.rels";if(null!=e){const[r,a]=(0,n.splitPath)(e);t=`${r}_rels/${a}.rels`}const r=await this.load(t);return r?(0,l.parseRelationships)(this.parseXmlDocument(r).firstElementChild,this.xmlParser):null}parseXmlDocument(e){return(0,s.parseXmlString)(e,this.options.trimXmlDeclaration)}}t.OpenXmlPackage=o},530:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Part=void 0;const a=r(472);t.Part=class{constructor(e,t){this._package=e,this.path=t}async load(){this.rels=await this._package.loadRelationships(this.path);const e=await this._package.load(this.path),t=this._package.parseXmlDocument(e);this._package.options.keepOrigin&&(this._xmlDocument=t),this.parseXml(t.firstElementChild)}save(){this._package.update(this.path,(0,a.serializeXmlString)(this._xmlDocument))}parseXml(e){}}},461:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.parseRelationships=t.RelationshipTypes=void 0,function(e){e.OfficeDocument="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",e.FontTable="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable",e.Image="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",e.Numbering="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering",e.Styles="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",e.StylesWithEffects="http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects",e.Theme="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",e.Settings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings",e.WebSettings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings",e.Hyperlink="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",e.Footnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes",e.Endnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes",e.Footer="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer",e.Header="http://schemas.openxmlformats.org/officeDocument/2006/relationships/header",e.ExtendedProperties="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",e.CoreProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",e.CustomProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties"}(r||(t.RelationshipTypes=r={})),t.parseRelationships=function(e,t){return t.elements(e).map((e=>({id:t.attr(e,"Id"),type:t.attr(e,"Type"),target:t.attr(e,"Target"),targetMode:t.attr(e,"TargetMode")})))}},168:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentParser=t.autos=void 0;const a=r(120),s=r(109),n=r(59),l=r(472),o=r(488),i=r(172),c=r(149),p=r(320);t.autos={shd:"inherit",color:"black",borderColor:"black",highlight:"transparent"};const u=[],d={oMath:a.DomType.MmlMath,oMathPara:a.DomType.MmlMathParagraph,f:a.DomType.MmlFraction,func:a.DomType.MmlFunction,fName:a.DomType.MmlFunctionName,num:a.DomType.MmlNumerator,den:a.DomType.MmlDenominator,rad:a.DomType.MmlRadical,deg:a.DomType.MmlDegree,e:a.DomType.MmlBase,sSup:a.DomType.MmlSuperscript,sSub:a.DomType.MmlSubscript,sPre:a.DomType.MmlPreSubSuper,sup:a.DomType.MmlSuperArgument,sub:a.DomType.MmlSubArgument,d:a.DomType.MmlDelimiter,nary:a.DomType.MmlNary,eqArr:a.DomType.MmlEquationArray,lim:a.DomType.MmlLimit,limLow:a.DomType.MmlLimitLower,m:a.DomType.MmlMatrix,mr:a.DomType.MmlMatrixRow,box:a.DomType.MmlBox,bar:a.DomType.MmlBar,groupChr:a.DomType.MmlGroupChar};t.DocumentParser=class{constructor(e){this.options={ignoreWidth:!1,debug:!1,...e}}parseNotes(e,t,r){var a=[];for(let s of l.default.elements(e,t)){const e=new r;e.id=l.default.attr(s,"id"),e.noteType=l.default.attr(s,"type"),e.children=this.parseBodyElements(s),a.push(e)}return a}parseDocumentFile(e){var t=l.default.element(e,"body"),r=l.default.element(e,"background"),s=l.default.element(t,"sectPr");return{type:a.DomType.Document,children:this.parseBodyElements(t),props:s?(0,n.parseSectionProperties)(s,l.default):{},cssStyle:r?this.parseBackground(r):{}}}parseBackground(e){var t={},r=h.colorAttr(e,"color");return r&&(t["background-color"]=r),t}parseBodyElements(e){var t=[];for(let r of l.default.elements(e))switch(r.localName){case"p":t.push(this.parseParagraph(r));break;case"tbl":t.push(this.parseTable(r));break;case"sdt":t.push(...this.parseSdt(r,(e=>this.parseBodyElements(e))))}return t}parseStylesFile(e){var t=[];return h.foreach(e,(e=>{switch(e.localName){case"style":t.push(this.parseStyle(e));break;case"docDefaults":t.push(this.parseDefaultStyles(e))}})),t}parseDefaultStyles(e){var t={id:null,name:null,target:null,basedOn:null,styles:[]};return h.foreach(e,(e=>{switch(e.localName){case"rPrDefault":var r=l.default.element(e,"rPr");r&&t.styles.push({target:"span",values:this.parseDefaultProperties(r,{})});break;case"pPrDefault":var a=l.default.element(e,"pPr");a&&t.styles.push({target:"p",values:this.parseDefaultProperties(a,{})})}})),t}parseStyle(e){var t={id:l.default.attr(e,"styleId"),isDefault:l.default.boolAttr(e,"default"),name:null,target:null,basedOn:null,styles:[],linked:null};switch(l.default.attr(e,"type")){case"paragraph":t.target="p";break;case"table":t.target="table";break;case"character":t.target="span"}return h.foreach(e,(e=>{switch(e.localName){case"basedOn":t.basedOn=l.default.attr(e,"val");break;case"name":t.name=l.default.attr(e,"val");break;case"link":t.linked=l.default.attr(e,"val");break;case"next":t.next=l.default.attr(e,"val");break;case"aliases":t.aliases=l.default.attr(e,"val").split(",");break;case"pPr":t.styles.push({target:"p",values:this.parseDefaultProperties(e,{})}),t.paragraphProps=(0,s.parseParagraphProperties)(e,l.default);break;case"rPr":t.styles.push({target:"span",values:this.parseDefaultProperties(e,{})}),t.runProps=(0,o.parseRunProperties)(e,l.default);break;case"tblPr":case"tcPr":t.styles.push({target:"td",values:this.parseDefaultProperties(e,{})});break;case"tblStylePr":for(let r of this.parseTableStyle(e))t.styles.push(r);break;case"rsid":case"qFormat":case"hidden":case"semiHidden":case"unhideWhenUsed":case"autoRedefine":case"uiPriority":break;default:this.options.debug&&console.warn(`DOCX: Unknown style element: ${e.localName}`)}})),t}parseTableStyle(e){var t=[],r=l.default.attr(e,"type"),a="",s="";switch(r){case"firstRow":s=".first-row",a="tr.first-row td";break;case"lastRow":s=".last-row",a="tr.last-row td";break;case"firstCol":s=".first-col",a="td.first-col";break;case"lastCol":s=".last-col",a="td.last-col";break;case"band1Vert":s=":not(.no-vband)",a="td.odd-col";break;case"band2Vert":s=":not(.no-vband)",a="td.even-col";break;case"band1Horz":s=":not(.no-hband)",a="tr.odd-row";break;case"band2Horz":s=":not(.no-hband)",a="tr.even-row";break;default:return[]}return h.foreach(e,(e=>{switch(e.localName){case"pPr":t.push({target:`${a} p`,mod:s,values:this.parseDefaultProperties(e,{})});break;case"rPr":t.push({target:`${a} span`,mod:s,values:this.parseDefaultProperties(e,{})});break;case"tblPr":case"tcPr":t.push({target:a,mod:s,values:this.parseDefaultProperties(e,{})})}})),t}parseNumberingFile(e){var t=[],r={},a=[];return h.foreach(e,(e=>{switch(e.localName){case"abstractNum":this.parseAbstractNumbering(e,a).forEach((e=>t.push(e)));break;case"numPicBullet":a.push(this.parseNumberingPicBullet(e));break;case"num":var s=l.default.attr(e,"numId"),n=l.default.elementAttr(e,"abstractNumId","val");r[n]=s}})),t.forEach((e=>e.id=r[e.id])),t}parseNumberingPicBullet(e){var t=l.default.element(e,"pict"),r=t&&l.default.element(t,"shape"),a=r&&l.default.element(r,"imagedata");return a?{id:l.default.intAttr(e,"numPicBulletId"),src:l.default.attr(a,"id"),style:l.default.attr(r,"style")}:null}parseAbstractNumbering(e,t){var r=[],a=l.default.attr(e,"abstractNumId");return h.foreach(e,(e=>{"lvl"===e.localName&&r.push(this.parseNumberingLevel(a,e,t))})),r}parseNumberingLevel(e,t,r){var a={id:e,level:l.default.intAttr(t,"ilvl"),start:1,pStyleName:void 0,pStyle:{},rStyle:{},suff:"tab"};return h.foreach(t,(e=>{switch(e.localName){case"start":a.start=l.default.intAttr(e,"val");break;case"pPr":this.parseDefaultProperties(e,a.pStyle);break;case"rPr":this.parseDefaultProperties(e,a.rStyle);break;case"lvlPicBulletId":var t=l.default.intAttr(e,"val");a.bullet=r.find((e=>e.id==t));break;case"lvlText":a.levelText=l.default.attr(e,"val");break;case"pStyle":a.pStyleName=l.default.attr(e,"val");break;case"numFmt":a.format=l.default.attr(e,"val");break;case"suff":a.suff=l.default.attr(e,"val")}})),a}parseSdt(e,t){const r=l.default.element(e,"sdtContent");return r?t(r):[]}parseInserted(e,t){return{type:a.DomType.Inserted,children:t(e)?.children??[]}}parseDeleted(e,t){return{type:a.DomType.Deleted,children:t(e)?.children??[]}}parseParagraph(e){var t={type:a.DomType.Paragraph,children:[]};for(let r of l.default.elements(e))switch(r.localName){case"pPr":this.parseParagraphProperties(r,t);break;case"r":t.children.push(this.parseRun(r,t));break;case"hyperlink":t.children.push(this.parseHyperlink(r,t));break;case"bookmarkStart":t.children.push((0,i.parseBookmarkStart)(r,l.default));break;case"bookmarkEnd":t.children.push((0,i.parseBookmarkEnd)(r,l.default));break;case"oMath":case"oMathPara":t.children.push(this.parseMathElement(r));break;case"sdt":t.children.push(...this.parseSdt(r,(e=>this.parseParagraph(e).children)));break;case"ins":t.children.push(this.parseInserted(r,(e=>this.parseParagraph(e))));break;case"del":t.children.push(this.parseDeleted(r,(e=>this.parseParagraph(e))))}return t}parseParagraphProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,(e=>{if((0,s.parseParagraphProperty)(e,t,l.default))return!0;switch(e.localName){case"pStyle":t.styleName=l.default.attr(e,"val");break;case"cnfStyle":t.className=f.classNameOfCnfStyle(e);break;case"framePr":this.parseFrame(e,t);break;case"rPr":break;default:return!1}return!0}))}parseFrame(e,t){"drop"==l.default.attr(e,"dropCap")&&(t.cssStyle.float="left")}parseHyperlink(e,t){var r={type:a.DomType.Hyperlink,parent:t,children:[]},s=l.default.attr(e,"anchor"),n=l.default.attr(e,"id");return s&&(r.href="#"+s),n&&(r.id=n),h.foreach(e,(e=>{"r"===e.localName&&r.children.push(this.parseRun(e,r))})),r}parseRun(e,t){var r={type:a.DomType.Run,parent:t,children:[]};return h.foreach(e,(e=>{switch((e=this.checkAlternateContent(e)).localName){case"t":r.children.push({type:a.DomType.Text,text:e.textContent});break;case"delText":r.children.push({type:a.DomType.DeletedText,text:e.textContent});break;case"fldSimple":r.children.push({type:a.DomType.SimpleField,instruction:l.default.attr(e,"instr"),lock:l.default.boolAttr(e,"lock",!1),dirty:l.default.boolAttr(e,"dirty",!1)});break;case"instrText":r.fieldRun=!0,r.children.push({type:a.DomType.Instruction,text:e.textContent});break;case"fldChar":r.fieldRun=!0,r.children.push({type:a.DomType.ComplexField,charType:l.default.attr(e,"fldCharType"),lock:l.default.boolAttr(e,"lock",!1),dirty:l.default.boolAttr(e,"dirty",!1)});break;case"noBreakHyphen":r.children.push({type:a.DomType.NoBreakHyphen});break;case"br":r.children.push({type:a.DomType.Break,break:l.default.attr(e,"type")||"textWrapping"});break;case"lastRenderedPageBreak":r.children.push({type:a.DomType.Break,break:"lastRenderedPageBreak"});break;case"sym":r.children.push({type:a.DomType.Symbol,font:l.default.attr(e,"font"),char:l.default.attr(e,"char")});break;case"tab":r.children.push({type:a.DomType.Tab});break;case"footnoteReference":r.children.push({type:a.DomType.FootnoteReference,id:l.default.attr(e,"id")});break;case"endnoteReference":r.children.push({type:a.DomType.EndnoteReference,id:l.default.attr(e,"id")});break;case"drawing":let t=this.parseDrawing(e);t&&(r.children=[t]);break;case"pict":r.children.push(this.parseVmlPicture(e));break;case"rPr":this.parseRunProperties(e,r)}})),r}parseMathElement(e){const t=`${e.localName}Pr`,r={type:d[e.localName],children:[]};for(const n of l.default.elements(e))if(d[n.localName])r.children.push(this.parseMathElement(n));else if("r"==n.localName){var s=this.parseRun(n);s.type=a.DomType.MmlRun,r.children.push(s)}else n.localName==t&&(r.props=this.parseMathProperies(n));return r}parseMathProperies(e){const t={};for(const r of l.default.elements(e))switch(r.localName){case"chr":t.char=l.default.attr(r,"val");break;case"vertJc":t.verticalJustification=l.default.attr(r,"val");break;case"pos":t.position=l.default.attr(r,"val");break;case"degHide":t.hideDegree=l.default.boolAttr(r,"val");break;case"begChr":t.beginChar=l.default.attr(r,"val");break;case"endChr":t.endChar=l.default.attr(r,"val")}return t}parseRunProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,(e=>{switch(e.localName){case"rStyle":t.styleName=l.default.attr(e,"val");break;case"vertAlign":t.verticalAlign=f.valueOfVertAlign(e,!0);break;default:return!1}return!0}))}parseVmlPicture(e){const t={type:a.DomType.VmlPicture,children:[]};for(const r of l.default.elements(e)){const e=(0,p.parseVmlElement)(r,this);e&&t.children.push(e)}return t}checkAlternateContent(e){if("AlternateContent"!=e.localName)return e;var t=l.default.element(e,"Choice");if(t){var r=l.default.attr(t,"Requires"),a=e.lookupNamespaceURI(r);if(u.includes(a))return t.firstElementChild}return l.default.element(e,"Fallback")?.firstElementChild}parseDrawing(e){for(var t of l.default.elements(e))switch(t.localName){case"inline":case"anchor":return this.parseDrawingWrapper(t)}}parseDrawingWrapper(e){var t={type:a.DomType.Drawing,children:[],cssStyle:{}},r="anchor"==e.localName;let s=null,n=l.default.boolAttr(e,"simplePos"),o={relative:"page",align:"left",offset:"0"},i={relative:"page",align:"top",offset:"0"};for(var p of l.default.elements(e))switch(p.localName){case"simplePos":n&&(o.offset=l.default.lengthAttr(p,"x",c.LengthUsage.Emu),i.offset=l.default.lengthAttr(p,"y",c.LengthUsage.Emu));break;case"extent":t.cssStyle.width=l.default.lengthAttr(p,"cx",c.LengthUsage.Emu),t.cssStyle.height=l.default.lengthAttr(p,"cy",c.LengthUsage.Emu);break;case"positionH":case"positionV":if(!n){let e="positionH"==p.localName?o:i;var u=l.default.element(p,"align"),d=l.default.element(p,"posOffset");e.relative=l.default.attr(p,"relativeFrom")??e.relative,u&&(e.align=u.textContent),d&&(e.offset=h.sizeValue(d,c.LengthUsage.Emu))}break;case"wrapTopAndBottom":s="wrapTopAndBottom";break;case"wrapNone":s="wrapNone";break;case"graphic":var m=this.parseGraphic(p);m&&t.children.push(m)}return"wrapTopAndBottom"==s?(t.cssStyle.display="block",o.align&&(t.cssStyle["text-align"]=o.align,t.cssStyle.width="100%")):"wrapNone"==s?(t.cssStyle.display="block",t.cssStyle.position="relative",t.cssStyle.width="0px",t.cssStyle.height="0px",o.offset&&(t.cssStyle.left=o.offset),i.offset&&(t.cssStyle.top=i.offset)):!r||"left"!=o.align&&"right"!=o.align||(t.cssStyle.float=o.align),t}parseGraphic(e){var t=l.default.element(e,"graphicData");for(let e of l.default.elements(t))if("pic"===e.localName)return this.parsePicture(e);return null}parsePicture(e){var t={type:a.DomType.Image,src:"",cssStyle:{}},r=l.default.element(e,"blipFill"),s=l.default.element(r,"blip");t.src=l.default.attr(s,"embed");var n=l.default.element(e,"spPr"),o=l.default.element(n,"xfrm");for(var i of(t.cssStyle.position="relative",l.default.elements(o)))switch(i.localName){case"ext":t.cssStyle.width=l.default.lengthAttr(i,"cx",c.LengthUsage.Emu),t.cssStyle.height=l.default.lengthAttr(i,"cy",c.LengthUsage.Emu);break;case"off":t.cssStyle.left=l.default.lengthAttr(i,"x",c.LengthUsage.Emu),t.cssStyle.top=l.default.lengthAttr(i,"y",c.LengthUsage.Emu)}return t}parseTable(e){var t={type:a.DomType.Table,children:[]};return h.foreach(e,(e=>{switch(e.localName){case"tr":t.children.push(this.parseTableRow(e));break;case"tblGrid":t.columns=this.parseTableColumns(e);break;case"tblPr":this.parseTableProperties(e,t)}})),t}parseTableColumns(e){var t=[];return h.foreach(e,(e=>{"gridCol"===e.localName&&t.push({width:l.default.lengthAttr(e,"w")})})),t}parseTableProperties(e,t){switch(t.cssStyle={},t.cellStyle={},this.parseDefaultProperties(e,t.cssStyle,t.cellStyle,(e=>{switch(e.localName){case"tblStyle":t.styleName=l.default.attr(e,"val");break;case"tblLook":t.className=f.classNameOftblLook(e);break;case"tblpPr":this.parseTablePosition(e,t);break;case"tblStyleColBandSize":t.colBandSize=l.default.intAttr(e,"val");break;case"tblStyleRowBandSize":t.rowBandSize=l.default.intAttr(e,"val");break;default:return!1}return!0})),t.cssStyle["text-align"]){case"center":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto",t.cssStyle["margin-right"]="auto";break;case"right":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto"}}parseTablePosition(e,t){var r=l.default.lengthAttr(e,"topFromText"),a=l.default.lengthAttr(e,"bottomFromText"),s=l.default.lengthAttr(e,"rightFromText"),n=l.default.lengthAttr(e,"leftFromText");t.cssStyle.float="left",t.cssStyle["margin-bottom"]=f.addSize(t.cssStyle["margin-bottom"],a),t.cssStyle["margin-left"]=f.addSize(t.cssStyle["margin-left"],n),t.cssStyle["margin-right"]=f.addSize(t.cssStyle["margin-right"],s),t.cssStyle["margin-top"]=f.addSize(t.cssStyle["margin-top"],r)}parseTableRow(e){var t={type:a.DomType.Row,children:[]};return h.foreach(e,(e=>{switch(e.localName){case"tc":t.children.push(this.parseTableCell(e));break;case"trPr":this.parseTableRowProperties(e,t)}})),t}parseTableRowProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,(e=>{switch(e.localName){case"cnfStyle":t.className=f.classNameOfCnfStyle(e);break;case"tblHeader":t.isHeader=l.default.boolAttr(e,"val");break;default:return!1}return!0}))}parseTableCell(e){var t={type:a.DomType.Cell,children:[]};return h.foreach(e,(e=>{switch(e.localName){case"tbl":t.children.push(this.parseTable(e));break;case"p":t.children.push(this.parseParagraph(e));break;case"tcPr":this.parseTableCellProperties(e,t)}})),t}parseTableCellProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,(e=>{switch(e.localName){case"gridSpan":t.span=l.default.intAttr(e,"val",null);break;case"vMerge":t.verticalMerge=l.default.attr(e,"val")??"continue";break;case"cnfStyle":t.className=f.classNameOfCnfStyle(e);break;default:return!1}return!0}))}parseDefaultProperties(e,r=null,a=null,s=null){return r=r||{},h.foreach(e,(n=>{if(!s?.(n))switch(n.localName){case"jc":r["text-align"]=f.valueOfJc(n);break;case"textAlignment":r["vertical-align"]=f.valueOfTextAlignment(n);break;case"color":r.color=h.colorAttr(n,"val",null,t.autos.color);break;case"sz":r["font-size"]=r["min-height"]=l.default.lengthAttr(n,"val",c.LengthUsage.FontSize);break;case"shd":r["background-color"]=h.colorAttr(n,"fill",null,t.autos.shd);break;case"highlight":r["background-color"]=h.colorAttr(n,"val",null,t.autos.highlight);break;case"vertAlign":break;case"position":r.verticalAlign=l.default.lengthAttr(n,"val",c.LengthUsage.FontSize);break;case"tcW":if(this.options.ignoreWidth)break;case"tblW":r.width=f.valueOfSize(n,"w");break;case"trHeight":this.parseTrHeight(n,r);break;case"strike":r["text-decoration"]=l.default.boolAttr(n,"val",!0)?"line-through":"none";break;case"b":r["font-weight"]=l.default.boolAttr(n,"val",!0)?"bold":"normal";break;case"i":r["font-style"]=l.default.boolAttr(n,"val",!0)?"italic":"normal";break;case"caps":r["text-transform"]=l.default.boolAttr(n,"val",!0)?"uppercase":"none";break;case"smallCaps":r["text-transform"]=l.default.boolAttr(n,"val",!0)?"lowercase":"none";break;case"u":this.parseUnderline(n,r);break;case"ind":case"tblInd":this.parseIndentation(n,r);break;case"rFonts":this.parseFont(n,r);break;case"tblBorders":this.parseBorderProperties(n,a||r);break;case"tblCellSpacing":r["border-spacing"]=f.valueOfMargin(n),r["border-collapse"]="separate";break;case"pBdr":this.parseBorderProperties(n,r);break;case"bdr":r.border=f.valueOfBorder(n);break;case"tcBorders":this.parseBorderProperties(n,r);break;case"vanish":l.default.boolAttr(n,"val",!0)&&(r.display="none");break;case"kern":case"noWrap":break;case"tblCellMar":case"tcMar":this.parseMarginProperties(n,a||r);break;case"tblLayout":r["table-layout"]=f.valueOfTblLayout(n);break;case"vAlign":r["vertical-align"]=f.valueOfTextAlignment(n);break;case"spacing":"pPr"==e.localName&&this.parseSpacing(n,r);break;case"wordWrap":l.default.boolAttr(n,"val")&&(r["overflow-wrap"]="break-word");break;case"suppressAutoHyphens":r.hyphens=l.default.boolAttr(n,"val",!0)?"none":"auto";break;case"lang":r.$lang=l.default.attr(n,"val");break;case"bCs":case"iCs":case"szCs":case"tabs":case"outlineLvl":case"contextualSpacing":case"tblStyleColBandSize":case"tblStyleRowBandSize":case"webHidden":case"pageBreakBefore":case"suppressLineNumbers":case"keepLines":case"keepNext":case"widowControl":case"bidi":case"rtl":case"noProof":break;default:this.options.debug&&console.warn(`DOCX: Unknown document element: ${e.localName}.${n.localName}`)}})),r}parseUnderline(e,t){var r=l.default.attr(e,"val");if(null!=r){switch(r){case"dash":case"dashDotDotHeavy":case"dashDotHeavy":case"dashedHeavy":case"dashLong":case"dashLongHeavy":case"dotDash":case"dotDotDash":t["text-decoration"]="underline dashed";break;case"dotted":case"dottedHeavy":t["text-decoration"]="underline dotted";break;case"double":t["text-decoration"]="underline double";break;case"single":case"thick":case"words":t["text-decoration"]="underline";break;case"wave":case"wavyDouble":case"wavyHeavy":t["text-decoration"]="underline wavy";break;case"none":t["text-decoration"]="none"}var a=h.colorAttr(e,"color");a&&(t["text-decoration-color"]=a)}}parseFont(e,t){var r=[l.default.attr(e,"ascii"),f.themeValue(e,"asciiTheme")].filter((e=>e)).join(", ");r.length>0&&(t["font-family"]=r)}parseIndentation(e,t){var r=l.default.lengthAttr(e,"firstLine"),a=l.default.lengthAttr(e,"hanging"),s=l.default.lengthAttr(e,"left"),n=l.default.lengthAttr(e,"start"),o=l.default.lengthAttr(e,"right"),i=l.default.lengthAttr(e,"end");r&&(t["text-indent"]=r),a&&(t["text-indent"]=`-${a}`),(s||n)&&(t["margin-left"]=s||n),(o||i)&&(t["margin-right"]=o||i)}parseSpacing(e,t){var r=l.default.lengthAttr(e,"before"),a=l.default.lengthAttr(e,"after"),s=l.default.intAttr(e,"line",null),n=l.default.attr(e,"lineRule");if(r&&(t["margin-top"]=r),a&&(t["margin-bottom"]=a),null!==s)switch(n){case"auto":t["line-height"]=`${(s/240).toFixed(2)}`;break;case"atLeast":t["line-height"]=`calc(100% + ${s/20}pt)`;break;default:t["line-height"]=t["min-height"]=s/20+"pt"}}parseMarginProperties(e,t){h.foreach(e,(e=>{switch(e.localName){case"left":t["padding-left"]=f.valueOfMargin(e);break;case"right":t["padding-right"]=f.valueOfMargin(e);break;case"top":t["padding-top"]=f.valueOfMargin(e);break;case"bottom":t["padding-bottom"]=f.valueOfMargin(e)}}))}parseTrHeight(e,t){l.default.attr(e,"hRule"),t.height=l.default.lengthAttr(e,"val")}parseBorderProperties(e,t){h.foreach(e,(e=>{switch(e.localName){case"start":case"left":t["border-left"]=f.valueOfBorder(e);break;case"end":case"right":t["border-right"]=f.valueOfBorder(e);break;case"top":t["border-top"]=f.valueOfBorder(e);break;case"bottom":t["border-bottom"]=f.valueOfBorder(e)}}))}};const m=["black","blue","cyan","darkBlue","darkCyan","darkGray","darkGreen","darkMagenta","darkRed","darkYellow","green","lightGray","magenta","none","red","white","yellow"];class h{static foreach(e,t){for(var r=0;r<e.childNodes.length;r++){let a=e.childNodes[r];a.nodeType==Node.ELEMENT_NODE&&t(a)}}static colorAttr(e,t,r=null,a="black"){var s=l.default.attr(e,t);if(s)return"auto"==s?a:m.includes(s)?s:`#${s}`;var n=l.default.attr(e,"themeColor");return n?`var(--docx-${n}-color)`:r}static sizeValue(e,t=c.LengthUsage.Dxa){return(0,c.convertLength)(e.textContent,t)}}class f{static themeValue(e,t){var r=l.default.attr(e,t);return r?`var(--docx-${r}-font)`:null}static valueOfSize(e,t){var r=c.LengthUsage.Dxa;switch(l.default.attr(e,"type")){case"dxa":break;case"pct":r=c.LengthUsage.Percent;break;case"auto":return"auto"}return l.default.lengthAttr(e,t,r)}static valueOfMargin(e){return l.default.lengthAttr(e,"w")}static valueOfBorder(e){if("nil"==l.default.attr(e,"val"))return"none";var r=h.colorAttr(e,"color");return`${l.default.lengthAttr(e,"sz",c.LengthUsage.Border)} solid ${"auto"==r?t.autos.borderColor:r}`}static valueOfTblLayout(e){return"fixed"==l.default.attr(e,"val")?"fixed":"auto"}static classNameOfCnfStyle(e){const t=l.default.attr(e,"val");return["first-row","last-row","first-col","last-col","odd-col","even-col","odd-row","even-row","ne-cell","nw-cell","se-cell","sw-cell"].filter(((e,r)=>"1"==t[r])).join(" ")}static valueOfJc(e){var t=l.default.attr(e,"val");switch(t){case"start":case"left":return"left";case"center":return"center";case"end":case"right":return"right";case"both":return"justify"}return t}static valueOfVertAlign(e,t=!1){var r=l.default.attr(e,"val");switch(r){case"subscript":return"sub";case"superscript":return t?"sup":"super"}return t?null:r}static valueOfTextAlignment(e){var t=l.default.attr(e,"val");switch(t){case"auto":case"baseline":return"baseline";case"top":return"top";case"center":return"middle";case"bottom":return"bottom"}return t}static addSize(e,t){return null==e?t:null==t?e:`calc(${e} + ${t})`}static classNameOftblLook(e){const t=l.default.hexAttr(e,"val",0);let r="";return(l.default.boolAttr(e,"firstRow")||32&t)&&(r+=" first-row"),(l.default.boolAttr(e,"lastRow")||64&t)&&(r+=" last-row"),(l.default.boolAttr(e,"firstColumn")||128&t)&&(r+=" first-col"),(l.default.boolAttr(e,"lastColumn")||256&t)&&(r+=" last-col"),(l.default.boolAttr(e,"noHBand")||512&t)&&(r+=" no-hband"),(l.default.boolAttr(e,"noVBand")||1024&t)&&(r+=" no-vband"),r.trim()}}},162:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CorePropsPart=void 0;const a=r(530),s=r(614);class n extends a.Part{parseXml(e){this.props=(0,s.parseCoreProps)(e,this._package.xmlParser)}}t.CorePropsPart=n},614:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseCoreProps=void 0,t.parseCoreProps=function(e,t){const r={};for(let a of t.elements(e))switch(a.localName){case"title":r.title=a.textContent;break;case"description":r.description=a.textContent;break;case"subject":r.subject=a.textContent;break;case"creator":r.creator=a.textContent;break;case"keywords":r.keywords=a.textContent;break;case"language":r.language=a.textContent;break;case"lastModifiedBy":r.lastModifiedBy=a.textContent;break;case"revision":a.textContent&&(r.revision=parseInt(a.textContent))}return r}},177:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CustomPropsPart=void 0;const a=r(530),s=r(821);class n extends a.Part{parseXml(e){this.props=(0,s.parseCustomProps)(e,this._package.xmlParser)}}t.CustomPropsPart=n},821:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseCustomProps=void 0,t.parseCustomProps=function(e,t){return t.elements(e,"property").map((e=>{const r=e.firstChild;return{formatId:t.attr(e,"fmtid"),name:t.attr(e,"name"),type:r.nodeName,value:r.textContent}}))}},665:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExtendedPropsPart=void 0;const a=r(530),s=r(668);class n extends a.Part{parseXml(e){this.props=(0,s.parseExtendedProps)(e,this._package.xmlParser)}}t.ExtendedPropsPart=n},668:(e,t)=>{function r(e){if(void 0!==e)return parseInt(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.parseExtendedProps=void 0,t.parseExtendedProps=function(e,t){const a={};for(let s of t.elements(e))switch(s.localName){case"Template":a.template=s.textContent;break;case"Pages":a.pages=r(s.textContent);break;case"Words":a.words=r(s.textContent);break;case"Characters":a.characters=r(s.textContent);break;case"Application":a.application=s.textContent;break;case"Lines":a.lines=r(s.textContent);break;case"Paragraphs":a.paragraphs=r(s.textContent);break;case"Company":a.company=s.textContent;break;case"AppVersion":a.appVersion=s.textContent}return a}},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseBookmarkEnd=t.parseBookmarkStart=void 0;const a=r(120);t.parseBookmarkStart=function(e,t){return{type:a.DomType.BookmarkStart,id:t.attr(e,"id"),name:t.attr(e,"name"),colFirst:t.intAttr(e,"colFirst"),colLast:t.intAttr(e,"colLast")}},t.parseBookmarkEnd=function(e,t){return{type:a.DomType.BookmarkEnd,id:t.attr(e,"id")}}},191:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseBorders=t.parseBorder=void 0;const a=r(149);function s(e,t){return{type:t.attr(e,"val"),color:t.attr(e,"color"),size:t.lengthAttr(e,"sz",a.LengthUsage.Border),offset:t.lengthAttr(e,"space",a.LengthUsage.Point),frame:t.boolAttr(e,"frame"),shadow:t.boolAttr(e,"shadow")}}t.parseBorder=s,t.parseBorders=function(e,t){var r={};for(let a of t.elements(e))switch(a.localName){case"left":r.left=s(a,t);break;case"top":r.top=s(a,t);break;case"right":r.right=s(a,t);break;case"bottom":r.bottom=s(a,t)}return r}},149:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseCommonProperty=t.convertPercentage=t.convertBoolean=t.convertLength=t.LengthUsage=t.ns=void 0,t.ns={wordml:"http://schemas.openxmlformats.org/wordprocessingml/2006/main",drawingml:"http://schemas.openxmlformats.org/drawingml/2006/main",picture:"http://schemas.openxmlformats.org/drawingml/2006/picture",compatibility:"http://schemas.openxmlformats.org/markup-compatibility/2006",math:"http://schemas.openxmlformats.org/officeDocument/2006/math"},t.LengthUsage={Dxa:{mul:.05,unit:"pt"},Emu:{mul:1/12700,unit:"pt"},FontSize:{mul:.5,unit:"pt"},Border:{mul:.125,unit:"pt"},Point:{mul:1,unit:"pt"},Percent:{mul:.02,unit:"%"},LineHeight:{mul:1/240,unit:""},VmlEmu:{mul:1/12700,unit:""}},t.convertLength=function(e,r=t.LengthUsage.Dxa){return null==e||/.+(p[xt]|[%])$/.test(e)?e:`${(parseInt(e)*r.mul).toFixed(2)}${r.unit}`},t.convertBoolean=function(e,t=!1){switch(e){case"1":case"on":case"true":return!0;case"0":case"off":case"false":return!1;default:return t}},t.convertPercentage=function(e){return e?parseInt(e)/100:null},t.parseCommonProperty=function(e,r,a){if(e.namespaceURI!=t.ns.wordml)return!1;switch(e.localName){case"color":r.color=a.attr(e,"val");break;case"sz":r.fontSize=a.lengthAttr(e,"val",t.LengthUsage.FontSize);break;default:return!1}return!0}},448:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentPart=void 0;const a=r(530);class s extends a.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.body=this._documentParser.parseDocumentFile(e)}}t.DocumentPart=s},120:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OpenXmlElementBase=t.DomType=void 0,function(e){e.Document="document",e.Paragraph="paragraph",e.Run="run",e.Break="break",e.NoBreakHyphen="noBreakHyphen",e.Table="table",e.Row="row",e.Cell="cell",e.Hyperlink="hyperlink",e.Drawing="drawing",e.Image="image",e.Text="text",e.Tab="tab",e.Symbol="symbol",e.BookmarkStart="bookmarkStart",e.BookmarkEnd="bookmarkEnd",e.Footer="footer",e.Header="header",e.FootnoteReference="footnoteReference",e.EndnoteReference="endnoteReference",e.Footnote="footnote",e.Endnote="endnote",e.SimpleField="simpleField",e.ComplexField="complexField",e.Instruction="instruction",e.VmlPicture="vmlPicture",e.MmlMath="mmlMath",e.MmlMathParagraph="mmlMathParagraph",e.MmlFraction="mmlFraction",e.MmlFunction="mmlFunction",e.MmlFunctionName="mmlFunctionName",e.MmlNumerator="mmlNumerator",e.MmlDenominator="mmlDenominator",e.MmlRadical="mmlRadical",e.MmlBase="mmlBase",e.MmlDegree="mmlDegree",e.MmlSuperscript="mmlSuperscript",e.MmlSubscript="mmlSubscript",e.MmlPreSubSuper="mmlPreSubSuper",e.MmlSubArgument="mmlSubArgument",e.MmlSuperArgument="mmlSuperArgument",e.MmlNary="mmlNary",e.MmlDelimiter="mmlDelimiter",e.MmlRun="mmlRun",e.MmlEquationArray="mmlEquationArray",e.MmlLimit="mmlLimit",e.MmlLimitLower="mmlLimitLower",e.MmlMatrix="mmlMatrix",e.MmlMatrixRow="mmlMatrixRow",e.MmlBox="mmlBox",e.MmlBar="mmlBar",e.MmlGroupChar="mmlGroupChar",e.VmlElement="vmlElement",e.Inserted="inserted",e.Deleted="deleted",e.DeletedText="deletedText"}(r||(t.DomType=r={})),t.OpenXmlElementBase=class{constructor(){this.children=[],this.cssStyle={}}}},931:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseLineSpacing=void 0,t.parseLineSpacing=function(e,t){return{before:t.lengthAttr(e,"before"),after:t.lengthAttr(e,"after"),line:t.intAttr(e,"line"),lineRule:t.attr(e,"lineRule")}}},109:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseNumbering=t.parseTabs=t.parseParagraphProperty=t.parseParagraphProperties=void 0;const a=r(149),s=r(59),n=r(931),l=r(488);function o(e,t,r){if(e.namespaceURI!=a.ns.wordml)return!1;if((0,a.parseCommonProperty)(e,t,r))return!0;switch(e.localName){case"tabs":t.tabs=i(e,r);break;case"sectPr":t.sectionProps=(0,s.parseSectionProperties)(e,r);break;case"numPr":t.numbering=c(e,r);break;case"spacing":return t.lineSpacing=(0,n.parseLineSpacing)(e,r),!1;case"textAlignment":return t.textAlignment=r.attr(e,"val"),!1;case"keepLines":t.keepLines=r.boolAttr(e,"val",!0);break;case"keepNext":t.keepNext=r.boolAttr(e,"val",!0);break;case"pageBreakBefore":t.pageBreakBefore=r.boolAttr(e,"val",!0);break;case"outlineLvl":t.outlineLevel=r.intAttr(e,"val");break;case"pStyle":t.styleName=r.attr(e,"val");break;case"rPr":t.runProps=(0,l.parseRunProperties)(e,r);break;default:return!1}return!0}function i(e,t){return t.elements(e,"tab").map((e=>({position:t.lengthAttr(e,"pos"),leader:t.attr(e,"leader"),style:t.attr(e,"val")})))}function c(e,t){var r={};for(let a of t.elements(e))switch(a.localName){case"numId":r.id=t.attr(a,"val");break;case"ilvl":r.level=t.intAttr(a,"val")}return r}t.parseParagraphProperties=function(e,t){let r={};for(let a of t.elements(e))o(a,r,t);return r},t.parseParagraphProperty=o,t.parseTabs=i,t.parseNumbering=c},488:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseRunProperty=t.parseRunProperties=void 0;const a=r(149);function s(e,t,r){return!!(0,a.parseCommonProperty)(e,t,r)}t.parseRunProperties=function(e,t){let r={};for(let a of t.elements(e))s(a,r,t);return r},t.parseRunProperty=s},59:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseSectionProperties=t.SectionType=void 0;const a=r(472),s=r(191);var n;function l(e,t){return{numberOfColumns:t.intAttr(e,"num"),space:t.lengthAttr(e,"space"),separator:t.boolAttr(e,"sep"),equalWidth:t.boolAttr(e,"equalWidth",!0),columns:t.elements(e,"col").map((e=>({width:t.lengthAttr(e,"w"),space:t.lengthAttr(e,"space")})))}}function o(e,t){return{chapSep:t.attr(e,"chapSep"),chapStyle:t.attr(e,"chapStyle"),format:t.attr(e,"fmt"),start:t.intAttr(e,"start")}}function i(e,t){return{id:t.attr(e,"id"),type:t.attr(e,"type")}}!function(e){e.Continuous="continuous",e.NextPage="nextPage",e.NextColumn="nextColumn",e.EvenPage="evenPage",e.OddPage="oddPage"}(n||(t.SectionType=n={})),t.parseSectionProperties=function(e,t=a.default){var r={};for(let a of t.elements(e))switch(a.localName){case"pgSz":r.pageSize={width:t.lengthAttr(a,"w"),height:t.lengthAttr(a,"h"),orientation:t.attr(a,"orient")};break;case"type":r.type=t.attr(a,"val");break;case"pgMar":r.pageMargins={left:t.lengthAttr(a,"left"),right:t.lengthAttr(a,"right"),top:t.lengthAttr(a,"top"),bottom:t.lengthAttr(a,"bottom"),header:t.lengthAttr(a,"header"),footer:t.lengthAttr(a,"footer"),gutter:t.lengthAttr(a,"gutter")};break;case"cols":r.columns=l(a,t);break;case"headerReference":(r.headerRefs??(r.headerRefs=[])).push(i(a,t));break;case"footerReference":(r.footerRefs??(r.footerRefs=[])).push(i(a,t));break;case"titlePg":r.titlePage=t.boolAttr(a,"val",!0);break;case"pgBorders":r.pageBorders=(0,s.parseBorders)(a,t);break;case"pgNumType":r.pageNumber=o(a,t)}return r}},667:(e,t,r)=>{Object.defineProperty(t,"X$",{value:!0}),t.j3=t.DN=t.bk=t.vC=void 0;const a=r(213),s=r(168),n=r(932);function l(e,r){const n={...t.vC,...r};return a.WordDocument.load(e,new s.DocumentParser(n),n)}function o(e,r,a,s){const l={...t.vC,...s};new n.HtmlRenderer(window.document).render(e,r,a,l)}t.vC={ignoreHeight:!1,ignoreWidth:!1,ignoreFonts:!1,breakPages:!0,debug:!1,experimental:!1,className:"docx",inWrapper:!0,trimXmlDeclaration:!0,ignoreLastRenderedPageBreak:!0,renderHeaders:!0,renderFooters:!0,renderFootnotes:!0,renderEndnotes:!0,useBase64URL:!1,renderChanges:!1},t.bk=l,t.DN=o,t.j3=async function(e,t,r,a){const s=await l(e,a);return o(s,t,r,a),s}},380:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FontTablePart=void 0;const a=r(530),s=r(512);class n extends a.Part{parseXml(e){this.fonts=(0,s.parseFonts)(e,this._package.xmlParser)}}t.FontTablePart=n},512:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseEmbedFontRef=t.parseFont=t.parseFonts=void 0;const r={embedRegular:"regular",embedBold:"bold",embedItalic:"italic",embedBoldItalic:"boldItalic"};function a(e,t){let r={name:t.attr(e,"name"),embedFontRefs:[]};for(let a of t.elements(e))switch(a.localName){case"family":r.family=t.attr(a,"val");break;case"altName":r.altName=t.attr(a,"val");break;case"embedRegular":case"embedBold":case"embedItalic":case"embedBoldItalic":r.embedFontRefs.push(s(a,t))}return r}function s(e,t){return{id:t.attr(e,"id"),key:t.attr(e,"fontKey"),type:r[e.localName]}}t.parseFonts=function(e,t){return t.elements(e).map((e=>a(e,t)))},t.parseFont=a,t.parseEmbedFontRef=s},984:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WmlFooter=t.WmlHeader=void 0;const a=r(120);class s extends a.OpenXmlElementBase{constructor(){super(...arguments),this.type=a.DomType.Header}}t.WmlHeader=s;class n extends a.OpenXmlElementBase{constructor(){super(...arguments),this.type=a.DomType.Footer}}t.WmlFooter=n},985:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FooterPart=t.HeaderPart=t.BaseHeaderFooterPart=void 0;const a=r(530),s=r(984);class n extends a.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.rootElement=this.createRootElement(),this.rootElement.children=this._documentParser.parseBodyElements(e)}}t.BaseHeaderFooterPart=n,t.HeaderPart=class extends n{createRootElement(){return new s.WmlHeader}},t.FooterPart=class extends n{createRootElement(){return new s.WmlFooter}}},932:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlRenderer=void 0;const a=r(120),s=r(593),n=r(630),l="http://www.w3.org/1998/Math/MathML";function o(e,t,r){return c(void 0,e,t,r)}function i(e,t,r){return c("http://www.w3.org/2000/svg",e,t,r)}function c(e,t,r,a){var s=e?document.createElementNS(e,t):document.createElement(t);return Object.assign(s,r),a&&u(s,a),s}function p(e){e.innerHTML=""}function u(e,t){t.forEach((t=>e.appendChild((0,s.isString)(t)?document.createTextNode(t):t)))}function d(e){return o("style",{innerHTML:e})}function m(e,t){e.appendChild(document.createComment(t))}t.HtmlRenderer=class{constructor(e){this.htmlDocument=e,this.className="docx",this.styleMap={},this.currentPart=null,this.tableVerticalMerges=[],this.currentVerticalMerge=null,this.tableCellPositions=[],this.currentCellPosition=null,this.footnoteMap={},this.endnoteMap={},this.currentEndnoteIds=[],this.usedHederFooterParts=[],this.currentTabs=[],this.tabsTimeout=0,this.createElement=o}render(e,t,r=null,a){this.document=e,this.options=a,this.className=a.className,this.rootSelector=a.inWrapper?`.${this.className}-wrapper`:":root",this.styleMap=null,p(r=r||t),p(t),m(r,"docxjs library predefined styles"),r.appendChild(this.renderDefaultStyle()),e.themePart&&(m(r,"docxjs document theme values"),this.renderTheme(e.themePart,r)),null!=e.stylesPart&&(this.styleMap=this.processStyles(e.stylesPart.styles),m(r,"docxjs document styles"),r.appendChild(this.renderStyles(e.stylesPart.styles))),e.numberingPart&&(this.prodessNumberings(e.numberingPart.domNumberings),m(r,"docxjs document numbering styles"),r.appendChild(this.renderNumbering(e.numberingPart.domNumberings,r))),e.footnotesPart&&(this.footnoteMap=(0,s.keyBy)(e.footnotesPart.notes,(e=>e.id))),e.endnotesPart&&(this.endnoteMap=(0,s.keyBy)(e.endnotesPart.notes,(e=>e.id))),e.settingsPart&&(this.defaultTabSize=e.settingsPart.settings?.defaultTabStop),!a.ignoreFonts&&e.fontTablePart&&this.renderFontTable(e.fontTablePart,r);var n=this.renderSections(e.documentPart.body);this.options.inWrapper?t.appendChild(this.renderWrapper(n)):u(t,n),this.refreshTabStops()}renderTheme(e,t){const r={},a=e.theme?.fontScheme;a&&(a.majorFont&&(r["--docx-majorHAnsi-font"]=a.majorFont.latinTypeface),a.minorFont&&(r["--docx-minorHAnsi-font"]=a.minorFont.latinTypeface));const s=e.theme?.colorScheme;if(s)for(let[e,t]of Object.entries(s.colors))r[`--docx-${e}-color`]=`#${t}`;const n=this.styleToString(`.${this.className}`,r);t.appendChild(d(n))}renderFontTable(e,t){for(let r of e.fonts)for(let e of r.embedFontRefs)this.document.loadFont(e.id,e.key).then((a=>{const s={"font-family":r.name,src:`url(${a})`};"bold"!=e.type&&"boldItalic"!=e.type||(s["font-weight"]="bold"),"italic"!=e.type&&"boldItalic"!=e.type||(s["font-style"]="italic"),m(t,`docxjs ${r.name} font`);const n=this.styleToString("@font-face",s);t.appendChild(d(n)),this.refreshTabStops()}))}processStyleName(e){return e?`${this.className}_${(0,s.escapeClassName)(e)}`:this.className}processStyles(e){const t=(0,s.keyBy)(e.filter((e=>null!=e.id)),(e=>e.id));for(const a of e.filter((e=>e.basedOn))){var r=t[a.basedOn];if(r){a.paragraphProps=(0,s.mergeDeep)(a.paragraphProps,r.paragraphProps),a.runProps=(0,s.mergeDeep)(a.runProps,r.runProps);for(const e of r.styles){const t=a.styles.find((t=>t.target==e.target));t?this.copyStyleProperties(e.values,t.values):a.styles.push({...e,values:{...e.values}})}}else this.options.debug&&console.warn(`Can't find base style ${a.basedOn}`)}for(let t of e)t.cssName=this.processStyleName(t.id);return t}prodessNumberings(e){for(let t of e.filter((e=>e.pStyleName))){const e=this.findStyle(t.pStyleName);e?.paragraphProps?.numbering&&(e.paragraphProps.numbering.level=t.level)}}processElement(e){if(e.children)for(var t of e.children)t.parent=e,t.type==a.DomType.Table?this.processTable(t):this.processElement(t)}processTable(e){for(var t of e.children)for(var r of t.children)r.cssStyle=this.copyStyleProperties(e.cellStyle,r.cssStyle,["border-left","border-right","border-top","border-bottom","padding-left","padding-right","padding-top","padding-bottom"]),this.processElement(r)}copyStyleProperties(e,t,r=null){if(!e)return t;for(var a of(null==t&&(t={}),null==r&&(r=Object.getOwnPropertyNames(e)),r))e.hasOwnProperty(a)&&!t.hasOwnProperty(a)&&(t[a]=e[a]);return t}createSection(e,t){var r=this.createElement("section",{className:e});return t&&(t.pageMargins&&(r.style.paddingLeft=t.pageMargins.left,r.style.paddingRight=t.pageMargins.right,r.style.paddingTop=t.pageMargins.top,r.style.paddingBottom=t.pageMargins.bottom),t.pageSize&&(this.options.ignoreWidth||(r.style.width=t.pageSize.width),this.options.ignoreHeight||(r.style.minHeight=t.pageSize.height)),t.columns&&t.columns.numberOfColumns&&(r.style.columnCount=`${t.columns.numberOfColumns}`,r.style.columnGap=t.columns.space,t.columns.separator&&(r.style.columnRule="1px solid black"))),r}renderSections(e){const t=[];this.processElement(e);const r=this.splitBySection(e.children);let a=null;for(let n=0,l=r.length;n<l;n++){this.currentFootnoteIds=[];const o=r[n],i=o.sectProps||e.props,c=this.createSection(this.className,i);this.renderStyleValues(e.cssStyle,c),this.options.renderHeaders&&this.renderHeaderFooter(i.headerRefs,i,t.length,a!=i,c);var s=this.createElement("article");this.renderElements(o.elements,s),c.appendChild(s),this.options.renderFootnotes&&this.renderNotes(this.currentFootnoteIds,this.footnoteMap,c),this.options.renderEndnotes&&n==l-1&&this.renderNotes(this.currentEndnoteIds,this.endnoteMap,c),this.options.renderFooters&&this.renderHeaderFooter(i.footerRefs,i,t.length,a!=i,c),t.push(c),a=i}return t}renderHeaderFooter(e,t,r,s,n){if(e){var l=(t.titlePage&&s?e.find((e=>"first"==e.type)):null)??(r%2==1?e.find((e=>"even"==e.type)):null)??e.find((e=>"default"==e.type)),o=l&&this.document.findPartByRelId(l.id,this.document.documentPart);if(o){this.currentPart=o,this.usedHederFooterParts.includes(o.path)||(this.processElement(o.rootElement),this.usedHederFooterParts.push(o.path));const[e]=this.renderElements([o.rootElement],n);t?.pageMargins&&(o.rootElement.type===a.DomType.Header?(e.style.marginTop=`calc(${t.pageMargins.header} - ${t.pageMargins.top})`,e.style.minHeight=`calc(${t.pageMargins.top} - ${t.pageMargins.header})`):o.rootElement.type===a.DomType.Footer&&(e.style.marginBottom=`calc(${t.pageMargins.footer} - ${t.pageMargins.bottom})`,e.style.minHeight=`calc(${t.pageMargins.bottom} - ${t.pageMargins.footer})`)),this.currentPart=null}}}isPageBreakElement(e){return e.type==a.DomType.Break&&("lastRenderedPageBreak"==e.break?!this.options.ignoreLastRenderedPageBreak:"page"==e.break)}splitBySection(e){var t={sectProps:null,elements:[]},r=[t];for(let c of e){if(c.type==a.DomType.Paragraph){const e=this.findStyle(c.styleName);e?.paragraphProps?.pageBreakBefore&&(t.sectProps=s,t={sectProps:null,elements:[]},r.push(t))}if(t.elements.push(c),c.type==a.DomType.Paragraph){const e=c;var s=e.sectionProps,n=-1,l=-1;if(this.options.breakPages&&e.children&&(n=e.children.findIndex((e=>-1!=(l=e.children?.findIndex(this.isPageBreakElement.bind(this))??-1)))),(s||-1!=n)&&(t.sectProps=s,t={sectProps:null,elements:[]},r.push(t)),-1!=n){let r=e.children[n],a=l<r.children.length-1;if(n<e.children.length-1||a){var o=c.children,i={...c,children:o.slice(n)};if(c.children=o.slice(0,n),t.elements.push(i),a){let e=r.children,t={...r,children:e.slice(0,l)};c.children.push(t),r.children=e.slice(l)}}}}}let c=null;for(let e=r.length-1;e>=0;e--)null==r[e].sectProps?r[e].sectProps=c:c=r[e].sectProps;return r}renderWrapper(e){return this.createElement("div",{className:`${this.className}-wrapper`},e)}renderDefaultStyle(){var e=this.className;return d(`\n.${e}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } \n.${e}-wrapper>section.${e} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }\n.${e} { color: black; hyphens: auto; text-underline-position: from-font; }\nsection.${e} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }\nsection.${e}>article { margin-bottom: auto; z-index: 1; }\nsection.${e}>footer { z-index: 1; }\n.${e} table { border-collapse: collapse; }\n.${e} table td, .${e} table th { vertical-align: top; }\n.${e} p { margin: 0pt; min-height: 1em; }\n.${e} span { white-space: pre-wrap; overflow-wrap: break-word; }\n.${e} a { color: inherit; text-decoration: inherit; }\n`)}renderNumbering(e,t){var r="",a=[];for(var s of e){var n=`p.${this.numberingClass(s.id,s.level)}`,l="none";if(s.bullet){let e=`--${this.className}-${s.bullet.src}`.toLowerCase();r+=this.styleToString(`${n}:before`,{content:"' '",display:"inline-block",background:`var(${e})`},s.bullet.style),this.document.loadNumberingImage(s.bullet.src).then((r=>{var a=`${this.rootSelector} { ${e}: url(${r}) }`;t.appendChild(d(a))}))}else if(s.levelText){let e=this.numberingCounter(s.id,s.level);const t=e+" "+(s.start-1);s.level>0&&(r+=this.styleToString(`p.${this.numberingClass(s.id,s.level-1)}`,{"counter-reset":t})),a.push(t),r+=this.styleToString(`${n}:before`,{content:this.levelTextToContent(s.levelText,s.suff,s.id,this.numFormatToCssValue(s.format)),"counter-increment":e,...s.rStyle})}else l=this.numFormatToCssValue(s.format);r+=this.styleToString(n,{display:"list-item","list-style-position":"inside","list-style-type":l,...s.pStyle})}return a.length>0&&(r+=this.styleToString(this.rootSelector,{"counter-reset":a.join(" ")})),d(r)}renderStyles(e){var t="";const r=this.styleMap,a=(0,s.keyBy)(e.filter((e=>e.isDefault)),(e=>e.target));for(const s of e){var n=s.styles;if(s.linked){var l=s.linked&&r[s.linked];l?n=n.concat(l.styles):this.options.debug&&console.warn(`Can't find linked style ${s.linked}`)}for(const e of n){var o=`${s.target??""}.${s.cssName}`;s.target!=e.target&&(o+=` ${e.target}`),a[s.target]==s&&(o=`.${this.className} ${s.target}, `+o),t+=this.styleToString(o,e.values)}}return d(t)}renderNotes(e,t,r){var a=e.map((e=>t[e])).filter((e=>e));if(a.length>0){var s=this.createElement("ol",null,this.renderElements(a));r.appendChild(s)}}renderElement(e){switch(e.type){case a.DomType.Paragraph:return this.renderParagraph(e);case a.DomType.BookmarkStart:return this.renderBookmarkStart(e);case a.DomType.BookmarkEnd:return null;case a.DomType.Run:return this.renderRun(e);case a.DomType.Table:return this.renderTable(e);case a.DomType.Row:return this.renderTableRow(e);case a.DomType.Cell:return this.renderTableCell(e);case a.DomType.Hyperlink:return this.renderHyperlink(e);case a.DomType.Drawing:return this.renderDrawing(e);case a.DomType.Image:return this.renderImage(e);case a.DomType.Text:case a.DomType.Text:return this.renderText(e);case a.DomType.DeletedText:return this.renderDeletedText(e);case a.DomType.Tab:return this.renderTab(e);case a.DomType.Symbol:return this.renderSymbol(e);case a.DomType.Break:return this.renderBreak(e);case a.DomType.Footer:return this.renderContainer(e,"footer");case a.DomType.Header:return this.renderContainer(e,"header");case a.DomType.Footnote:case a.DomType.Endnote:return this.renderContainer(e,"li");case a.DomType.FootnoteReference:return this.renderFootnoteReference(e);case a.DomType.EndnoteReference:return this.renderEndnoteReference(e);case a.DomType.NoBreakHyphen:return this.createElement("wbr");case a.DomType.VmlPicture:return this.renderVmlPicture(e);case a.DomType.VmlElement:return this.renderVmlElement(e);case a.DomType.MmlMath:return this.renderContainerNS(e,l,"math",{xmlns:l});case a.DomType.MmlMathParagraph:return this.renderContainer(e,"span");case a.DomType.MmlFraction:return this.renderContainerNS(e,l,"mfrac");case a.DomType.MmlBase:return this.renderContainerNS(e,l,e.parent.type==a.DomType.MmlMatrixRow?"mtd":"mrow");case a.DomType.MmlNumerator:case a.DomType.MmlDenominator:case a.DomType.MmlFunction:case a.DomType.MmlLimit:case a.DomType.MmlBox:return this.renderContainerNS(e,l,"mrow");case a.DomType.MmlGroupChar:return this.renderMmlGroupChar(e);case a.DomType.MmlLimitLower:return this.renderContainerNS(e,l,"munder");case a.DomType.MmlMatrix:return this.renderContainerNS(e,l,"mtable");case a.DomType.MmlMatrixRow:return this.renderContainerNS(e,l,"mtr");case a.DomType.MmlRadical:return this.renderMmlRadical(e);case a.DomType.MmlSuperscript:return this.renderContainerNS(e,l,"msup");case a.DomType.MmlSubscript:return this.renderContainerNS(e,l,"msub");case a.DomType.MmlDegree:case a.DomType.MmlSuperArgument:case a.DomType.MmlSubArgument:return this.renderContainerNS(e,l,"mn");case a.DomType.MmlFunctionName:return this.renderContainerNS(e,l,"ms");case a.DomType.MmlDelimiter:return this.renderMmlDelimiter(e);case a.DomType.MmlRun:return this.renderMmlRun(e);case a.DomType.MmlNary:return this.renderMmlNary(e);case a.DomType.MmlPreSubSuper:return this.renderMmlPreSubSuper(e);case a.DomType.MmlBar:return this.renderMmlBar(e);case a.DomType.MmlEquationArray:return this.renderMllList(e);case a.DomType.Inserted:return this.renderInserted(e);case a.DomType.Deleted:return this.renderDeleted(e)}return null}renderChildren(e,t){return this.renderElements(e.children,t)}renderElements(e,t){if(null==e)return null;var r=e.flatMap((e=>this.renderElement(e))).filter((e=>null!=e));return t&&u(t,r),r}renderContainer(e,t,r){return this.createElement(t,r,this.renderChildren(e))}renderContainerNS(e,t,r,a){return c(t,r,a,this.renderChildren(e))}renderParagraph(e){var t=this.createElement("p");const r=this.findStyle(e.styleName);e.tabs??(e.tabs=r?.paragraphProps?.tabs),this.renderClass(e,t),this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),this.renderCommonProperties(t.style,e);const a=e.numbering??r?.paragraphProps?.numbering;return a&&t.classList.add(this.numberingClass(a.id,a.level)),t}renderRunProperties(e,t){this.renderCommonProperties(e,t)}renderCommonProperties(e,t){null!=t&&(t.color&&(e.color=t.color),t.fontSize&&(e["font-size"]=t.fontSize))}renderHyperlink(e){var t=this.createElement("a");if(this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),e.href)t.href=e.href;else if(e.id){const r=this.document.documentPart.rels.find((t=>t.id==e.id&&"External"===t.targetMode));t.href=r?.target}return t}renderDrawing(e){var t=this.createElement("div");return t.style.display="inline-block",t.style.position="relative",t.style.textIndent="0px",this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),t}renderImage(e){let t=this.createElement("img");return this.renderStyleValues(e.cssStyle,t),this.document&&this.document.loadDocumentImage(e.src,this.currentPart).then((e=>{t.src=e})),t}renderText(e){return this.htmlDocument.createTextNode(e.text)}renderDeletedText(e){return this.options.renderEndnotes?this.htmlDocument.createTextNode(e.text):null}renderBreak(e){return"textWrapping"==e.break?this.createElement("br"):null}renderInserted(e){return this.options.renderChanges?this.renderContainer(e,"ins"):this.renderChildren(e)}renderDeleted(e){return this.options.renderChanges?this.renderContainer(e,"del"):null}renderSymbol(e){var t=this.createElement("span");return t.style.fontFamily=e.font,t.innerHTML=`&#x${e.char};`,t}renderFootnoteReference(e){var t=this.createElement("sup");return this.currentFootnoteIds.push(e.id),t.textContent=`${this.currentFootnoteIds.length}`,t}renderEndnoteReference(e){var t=this.createElement("sup");return this.currentEndnoteIds.push(e.id),t.textContent=`${this.currentEndnoteIds.length}`,t}renderTab(e){var t=this.createElement("span");if(t.innerHTML="&emsp;",this.options.experimental){t.className=this.tabStopClass();var r=function(e,t){for(var r=e.parent;null!=r&&r.type!=t;)r=r.parent;return r}(e,a.DomType.Paragraph)?.tabs;this.currentTabs.push({stops:r,span:t})}return t}renderBookmarkStart(e){var t=this.createElement("span");return t.id=e.name,t}renderRun(e){if(e.fieldRun)return null;const t=this.createElement("span");if(e.id&&(t.id=e.id),this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),e.verticalAlign){const r=this.createElement(e.verticalAlign);this.renderChildren(e,r),t.appendChild(r)}else this.renderChildren(e,t);return t}renderTable(e){let t=this.createElement("table");return this.tableCellPositions.push(this.currentCellPosition),this.tableVerticalMerges.push(this.currentVerticalMerge),this.currentVerticalMerge={},this.currentCellPosition={col:0,row:0},e.columns&&t.appendChild(this.renderTableColumns(e.columns)),this.renderClass(e,t),this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),this.currentVerticalMerge=this.tableVerticalMerges.pop(),this.currentCellPosition=this.tableCellPositions.pop(),t}renderTableColumns(e){let t=this.createElement("colgroup");for(let r of e){let e=this.createElement("col");r.width&&(e.style.width=r.width),t.appendChild(e)}return t}renderTableRow(e){let t=this.createElement("tr");return this.currentCellPosition.col=0,this.renderClass(e,t),this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),this.currentCellPosition.row++,t}renderTableCell(e){let t=this.createElement("td");const r=this.currentCellPosition.col;return e.verticalMerge?"restart"==e.verticalMerge?(this.currentVerticalMerge[r]=t,t.rowSpan=1):this.currentVerticalMerge[r]&&(this.currentVerticalMerge[r].rowSpan+=1,t.style.display="none"):this.currentVerticalMerge[r]=null,this.renderClass(e,t),this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),e.span&&(t.colSpan=e.span),this.currentCellPosition.col+=t.colSpan,t}renderVmlPicture(e){var t=o("div");return this.renderChildren(e,t),t}renderVmlElement(e){var t=i("svg");t.setAttribute("style",e.cssStyleText);const r=this.renderVmlChildElement(e);return e.imageHref?.id&&this.document?.loadDocumentImage(e.imageHref.id,this.currentPart).then((e=>r.setAttribute("href",e))),t.appendChild(r),requestAnimationFrame((()=>{const e=t.firstElementChild.getBBox();t.setAttribute("width",`${Math.ceil(e.x+e.width)}`),t.setAttribute("height",`${Math.ceil(e.y+e.height)}`)})),t}renderVmlChildElement(e){const t=i(e.tagName);Object.entries(e.attrs).forEach((([e,r])=>t.setAttribute(e,r)));for(let r of e.children)r.type==a.DomType.VmlElement?t.appendChild(this.renderVmlChildElement(r)):t.appendChild(...(0,s.asArray)(this.renderElement(r)));return t}renderMmlRadical(e){const t=e.children.find((e=>e.type==a.DomType.MmlBase));if(e.props?.hideDegree)return c(l,"msqrt",null,this.renderElements([t]));const r=e.children.find((e=>e.type==a.DomType.MmlDegree));return c(l,"mroot",null,this.renderElements([t,r]))}renderMmlDelimiter(e){const t=[];return t.push(c(l,"mo",null,[e.props.beginChar??"("])),t.push(...this.renderElements(e.children)),t.push(c(l,"mo",null,[e.props.endChar??")"])),c(l,"mrow",null,t)}renderMmlNary(e){const t=[],r=(0,s.keyBy)(e.children,(e=>e.type)),n=r[a.DomType.MmlSuperArgument],o=r[a.DomType.MmlSubArgument],i=n?c(l,"mo",null,(0,s.asArray)(this.renderElement(n))):null,p=o?c(l,"mo",null,(0,s.asArray)(this.renderElement(o))):null,u=c(l,"mo",null,[e.props?.char??"∫"]);return i||p?t.push(c(l,"munderover",null,[u,p,i])):i?t.push(c(l,"mover",null,[u,i])):p?t.push(c(l,"munder",null,[u,p])):t.push(u),t.push(...this.renderElements(r[a.DomType.MmlBase].children)),c(l,"mrow",null,t)}renderMmlPreSubSuper(e){const t=[],r=(0,s.keyBy)(e.children,(e=>e.type)),n=r[a.DomType.MmlSuperArgument],o=r[a.DomType.MmlSubArgument],i=n?c(l,"mo",null,(0,s.asArray)(this.renderElement(n))):null,p=o?c(l,"mo",null,(0,s.asArray)(this.renderElement(o))):null,u=c(l,"mo",null);return t.push(c(l,"msubsup",null,[u,p,i])),t.push(...this.renderElements(r[a.DomType.MmlBase].children)),c(l,"mrow",null,t)}renderMmlGroupChar(e){const t="bot"===e.props.verticalJustification?"mover":"munder",r=this.renderContainerNS(e,l,t);return e.props.char&&r.appendChild(c(l,"mo",null,[e.props.char])),r}renderMmlBar(e){const t=this.renderContainerNS(e,l,"mrow");switch(e.props.position){case"top":t.style.textDecoration="overline";break;case"bottom":t.style.textDecoration="underline"}return t}renderMmlRun(e){const t=c(l,"ms");return this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),this.renderChildren(e,t),t}renderMllList(e){const t=c(l,"mtable");this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),this.renderChildren(e);for(let r of this.renderChildren(e))t.appendChild(c(l,"mtr",null,[c(l,"mtd",null,[r])]));return t}renderStyleValues(e,t){for(let r in e)r.startsWith("$")?t.setAttribute(r.slice(1),e[r]):t.style[r]=e[r]}renderClass(e,t){e.className&&(t.className=e.className),e.styleName&&t.classList.add(this.processStyleName(e.styleName))}findStyle(e){return e&&this.styleMap?.[e]}numberingClass(e,t){return`${this.className}-num-${e}-${t}`}tabStopClass(){return`${this.className}-tab-stop`}styleToString(e,t,r=null){let a=`${e} {\r\n`;for(const e in t)e.startsWith("$")||(a+=`  ${e}: ${t[e]};\r\n`);return r&&(a+=r),a+"}\r\n"}numberingCounter(e,t){return`${this.className}-num-${e}-${t}`}levelTextToContent(e,t,r,a){return`"${e.replace(/%\d*/g,(e=>{let t=parseInt(e.substring(1),10)-1;return`"counter(${this.numberingCounter(r,t)}, ${a})"`}))}${{tab:"\\9",space:"\\a0"}[t]??""}"`}numFormatToCssValue(e){return{none:"none",bullet:"disc",decimal:"decimal",lowerLetter:"lower-alpha",upperLetter:"upper-alpha",lowerRoman:"lower-roman",upperRoman:"upper-roman",decimalZero:"decimal-leading-zero",aiueo:"katakana",aiueoFullWidth:"katakana",chineseCounting:"simp-chinese-informal",chineseCountingThousand:"simp-chinese-informal",chineseLegalSimplified:"simp-chinese-formal",chosung:"hangul-consonant",ideographDigital:"cjk-ideographic",ideographTraditional:"cjk-heavenly-stem",ideographLegalTraditional:"trad-chinese-formal",ideographZodiac:"cjk-earthly-branch",iroha:"katakana-iroha",irohaFullWidth:"katakana-iroha",japaneseCounting:"japanese-informal",japaneseDigitalTenThousand:"cjk-decimal",japaneseLegal:"japanese-formal",thaiNumbers:"thai",koreanCounting:"korean-hangul-formal",koreanDigital:"korean-hangul-formal",koreanDigital2:"korean-hanja-informal",hebrew1:"hebrew",hebrew2:"hebrew",hindiNumbers:"devanagari",ganada:"hangul",taiwaneseCounting:"cjk-ideographic",taiwaneseCountingThousand:"cjk-ideographic",taiwaneseDigital:"cjk-decimal"}[e]??e}refreshTabStops(){this.options.experimental&&(clearTimeout(this.tabsTimeout),this.tabsTimeout=setTimeout((()=>{const e=(0,n.computePixelToPoint)();for(let t of this.currentTabs)(0,n.updateTabStop)(t.span,t.stops,this.defaultTabSize,e)}),500))}}},630:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.updateTabStop=t.computePixelToPoint=void 0;const r={pos:0,leader:"none",style:"left"};function a(e){return parseFloat(e)}t.computePixelToPoint=function(e=document.body){const t=document.createElement("div");t.style.width="100pt",e.appendChild(t);const r=100/t.offsetWidth;return e.removeChild(t),r},t.updateTabStop=function(e,t,s,n=.75){const l=e.closest("p"),o=e.getBoundingClientRect(),i=l.getBoundingClientRect(),c=getComputedStyle(l),p=t?.length>0?t.map((e=>({pos:a(e.position),leader:e.leader,style:e.style}))).sort(((e,t)=>e.pos-t.pos)):[r],u=p[p.length-1],d=i.width*n,m=a(s);let h=u.pos+m;if(h<d)for(;h<d&&p.length<50;h+=m)p.push({...r,pos:h});const f=parseFloat(c.marginLeft),g=i.left+f,b=(o.left-g)*n,y=p.find((e=>"clear"!=e.style&&e.pos>b));if(null==y)return;let v=1;if("right"==y.style||"center"==y.style){const t=Array.from(l.querySelectorAll(`.${e.className}`)),r=t.indexOf(e)+1,a=document.createRange();a.setStart(e,1),r<t.length?a.setEndBefore(t[r]):a.setEndAfter(l);const s="center"==y.style?.5:1,o=a.getBoundingClientRect(),c=o.left+s*o.width-(i.left-f);v=y.pos-c*n}else v=y.pos-b;switch(e.innerHTML="&nbsp;",e.style.textDecoration="inherit",e.style.wordSpacing=`${v.toFixed(0)}pt`,y.leader){case"dot":case"middleDot":e.style.textDecoration="underline",e.style.textDecorationStyle="dotted";break;case"hyphen":case"heavy":case"underscore":e.style.textDecoration="underline"}}},881:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WmlEndnote=t.WmlFootnote=t.WmlBaseNote=void 0;const a=r(120);class s{}t.WmlBaseNote=s,t.WmlFootnote=class extends s{constructor(){super(...arguments),this.type=a.DomType.Footnote}},t.WmlEndnote=class extends s{constructor(){super(...arguments),this.type=a.DomType.Endnote}}},735:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EndnotesPart=t.FootnotesPart=t.BaseNotePart=void 0;const a=r(530),s=r(881);class n extends a.Part{constructor(e,t,r){super(e,t),this._documentParser=r}}t.BaseNotePart=n,t.FootnotesPart=class extends n{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"footnote",s.WmlFootnote)}},t.EndnotesPart=class extends n{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"endnote",s.WmlEndnote)}}},527:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NumberingPart=void 0;const a=r(530),s=r(682);class n extends a.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){Object.assign(this,(0,s.parseNumberingPart)(e,this._package.xmlParser)),this.domNumberings=this._documentParser.parseNumberingFile(e)}}t.NumberingPart=n},682:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseNumberingBulletPicture=t.parseNumberingLevelOverrride=t.parseNumberingLevel=t.parseAbstractNumbering=t.parseNumbering=t.parseNumberingPart=void 0;const a=r(109),s=r(488);function n(e,t){let r={id:t.attr(e,"numId"),overrides:[]};for(let a of t.elements(e))switch(a.localName){case"abstractNumId":r.abstractId=t.attr(a,"val");break;case"lvlOverride":r.overrides.push(i(a,t))}return r}function l(e,t){let r={id:t.attr(e,"abstractNumId"),levels:[]};for(let a of t.elements(e))switch(a.localName){case"name":r.name=t.attr(a,"val");break;case"multiLevelType":r.multiLevelType=t.attr(a,"val");break;case"numStyleLink":r.numberingStyleLink=t.attr(a,"val");break;case"styleLink":r.styleLink=t.attr(a,"val");break;case"lvl":r.levels.push(o(a,t))}return r}function o(e,t){let r={level:t.intAttr(e,"ilvl")};for(let n of t.elements(e))switch(n.localName){case"start":r.start=t.attr(n,"val");break;case"lvlRestart":r.restart=t.intAttr(n,"val");break;case"numFmt":r.format=t.attr(n,"val");break;case"lvlText":r.text=t.attr(n,"val");break;case"lvlJc":r.justification=t.attr(n,"val");break;case"lvlPicBulletId":r.bulletPictureId=t.attr(n,"val");break;case"pStyle":r.paragraphStyle=t.attr(n,"val");break;case"pPr":r.paragraphProps=(0,a.parseParagraphProperties)(n,t);break;case"rPr":r.runProps=(0,s.parseRunProperties)(n,t)}return r}function i(e,t){let r={level:t.intAttr(e,"ilvl")};for(let a of t.elements(e))switch(a.localName){case"startOverride":r.start=t.intAttr(a,"val");break;case"lvl":r.numberingLevel=o(a,t)}return r}function c(e,t){var r=t.element(e,"pict"),a=r&&t.element(r,"shape"),s=a&&t.element(a,"imagedata");return s?{id:t.attr(e,"numPicBulletId"),referenceId:t.attr(s,"id"),style:t.attr(a,"style")}:null}t.parseNumberingPart=function(e,t){let r={numberings:[],abstractNumberings:[],bulletPictures:[]};for(let a of t.elements(e))switch(a.localName){case"num":r.numberings.push(n(a,t));break;case"abstractNum":r.abstractNumberings.push(l(a,t));break;case"numPicBullet":r.bulletPictures.push(c(a,t))}return r},t.parseNumbering=n,t.parseAbstractNumbering=l,t.parseNumberingLevel=o,t.parseNumberingLevelOverrride=i,t.parseNumberingBulletPicture=c},472:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.XmlParser=t.serializeXmlString=t.parseXmlString=void 0;const a=r(149);t.parseXmlString=function(e,t=!1){var r;t&&(e=e.replace(/<[?].*[?]>/,"")),e=65279===(r=e).charCodeAt(0)?r.substring(1):r;const a=(new DOMParser).parseFromString(e,"application/xml"),s=(n=a,n.getElementsByTagName("parsererror")[0]?.textContent);var n;if(s)throw new Error(s);return a},t.serializeXmlString=function(e){return(new XMLSerializer).serializeToString(e)};class s{elements(e,t=null){const r=[];for(let a=0,s=e.childNodes.length;a<s;a++){let s=e.childNodes.item(a);1!=s.nodeType||null!=t&&s.localName!=t||r.push(s)}return r}element(e,t){for(let r=0,a=e.childNodes.length;r<a;r++){let a=e.childNodes.item(r);if(1==a.nodeType&&a.localName==t)return a}return null}elementAttr(e,t,r){var a=this.element(e,t);return a?this.attr(a,r):void 0}attrs(e){return Array.from(e.attributes)}attr(e,t){for(let r=0,a=e.attributes.length;r<a;r++){let a=e.attributes.item(r);if(a.localName==t)return a.value}return null}intAttr(e,t,r=null){var a=this.attr(e,t);return a?parseInt(a):r}hexAttr(e,t,r=null){var a=this.attr(e,t);return a?parseInt(a,16):r}floatAttr(e,t,r=null){var a=this.attr(e,t);return a?parseFloat(a):r}boolAttr(e,t,r=null){return(0,a.convertBoolean)(this.attr(e,t),r)}lengthAttr(e,t,r=a.LengthUsage.Dxa){return(0,a.convertLength)(this.attr(e,t),r)}}t.XmlParser=s;const n=new s;t.default=n},287:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SettingsPart=void 0;const a=r(530),s=r(846);class n extends a.Part{constructor(e,t){super(e,t)}parseXml(e){this.settings=(0,s.parseSettings)(e,this._package.xmlParser)}}t.SettingsPart=n},846:(e,t)=>{function r(e,t){var r={defaultNoteIds:[]};for(let a of t.elements(e))switch(a.localName){case"numFmt":r.nummeringFormat=t.attr(a,"val");break;case"footnote":case"endnote":r.defaultNoteIds.push(t.attr(a,"id"))}return r}Object.defineProperty(t,"__esModule",{value:!0}),t.parseNoteProperties=t.parseSettings=void 0,t.parseSettings=function(e,t){var a={};for(let s of t.elements(e))switch(s.localName){case"defaultTabStop":a.defaultTabStop=t.lengthAttr(s,"val");break;case"footnotePr":a.footnoteProps=r(s,t);break;case"endnotePr":a.endnoteProps=r(s,t);break;case"autoHyphenation":a.autoHyphenation=t.boolAttr(s,"val")}return a},t.parseNoteProperties=r},240:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StylesPart=void 0;const a=r(530);class s extends a.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.styles=this._documentParser.parseStylesFile(e)}}t.StylesPart=s},893:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ThemePart=void 0;const a=r(530),s=r(55);class n extends a.Part{constructor(e,t){super(e,t)}parseXml(e){this.theme=(0,s.parseTheme)(e,this._package.xmlParser)}}t.ThemePart=n},55:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseFontInfo=t.parseFontScheme=t.parseColorScheme=t.parseTheme=t.DmlTheme=void 0;class r{}function a(e,t){var r={name:t.attr(e,"name"),colors:{}};for(let n of t.elements(e)){var a=t.element(n,"srgbClr"),s=t.element(n,"sysClr");a?r.colors[n.localName]=t.attr(a,"val"):s&&(r.colors[n.localName]=t.attr(s,"lastClr"))}return r}function s(e,t){var r={name:t.attr(e,"name")};for(let a of t.elements(e))switch(a.localName){case"majorFont":r.majorFont=n(a,t);break;case"minorFont":r.minorFont=n(a,t)}return r}function n(e,t){return{latinTypeface:t.elementAttr(e,"latin","typeface"),eaTypeface:t.elementAttr(e,"ea","typeface"),csTypeface:t.elementAttr(e,"cs","typeface")}}t.DmlTheme=r,t.parseTheme=function(e,t){var n=new r,l=t.element(e,"themeElements");for(let e of t.elements(l))switch(e.localName){case"clrScheme":n.colorScheme=a(e,t);break;case"fontScheme":n.fontScheme=s(e,t)}return n},t.parseColorScheme=a,t.parseFontScheme=s,t.parseFontInfo=n},593:(e,t)=>{function r(e){return e&&"object"==typeof e&&!Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.asArray=t.formatCssRules=t.parseCssRules=t.mergeDeep=t.isString=t.isObject=t.blobToBase64=t.keyBy=t.resolvePath=t.splitPath=t.escapeClassName=void 0,t.escapeClassName=function(e){return e?.replace(/[ .]+/g,"-").replace(/[&]+/g,"and").toLowerCase()},t.splitPath=function(e){let t=e.lastIndexOf("/")+1;return[0==t?"":e.substring(0,t),0==t?e:e.substring(t)]},t.resolvePath=function(e,t){try{const r="http://docx/";return new URL(e,r+t).toString().substring(r.length)}catch{return`${t}${e}`}},t.keyBy=function(e,t){return e.reduce(((e,r)=>(e[t(r)]=r,e)),{})},t.blobToBase64=function(e){return new Promise(((t,r)=>{const a=new FileReader;a.onloadend=()=>t(a.result),a.onerror=()=>r(),a.readAsDataURL(e)}))},t.isObject=r,t.isString=function(e){return"string"==typeof e||e instanceof String},t.mergeDeep=function e(t,...a){if(!a.length)return t;const s=a.shift();if(r(t)&&r(s))for(const a in s)r(s[a])?e(t[a]??(t[a]={}),s[a]):t[a]=s[a];return e(t,...a)},t.parseCssRules=function(e){const t={};for(const r of e.split(";")){const[e,a]=r.split(":");t[e]=a}return t},t.formatCssRules=function(e){return Object.entries(e).map(((e,t)=>`${e}: ${t}`)).join(";")},t.asArray=function(e){return Array.isArray(e)?e:[e]}},320:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseVmlElement=t.VmlElement=void 0;const a=r(149),s=r(120),n=r(472);class l extends s.OpenXmlElementBase{constructor(){super(...arguments),this.type=s.DomType.VmlElement,this.attrs={}}}function o(e){return{stroke:n.default.attr(e,"color"),"stroke-width":n.default.lengthAttr(e,"weight",a.LengthUsage.Emu)??"1px"}}function i(e){return e.split(",")}t.VmlElement=l,t.parseVmlElement=function e(t,r){var a=new l;switch(t.localName){case"rect":a.tagName="rect",Object.assign(a.attrs,{width:"100%",height:"100%"});break;case"oval":a.tagName="ellipse",Object.assign(a.attrs,{cx:"50%",cy:"50%",rx:"50%",ry:"50%"});break;case"line":a.tagName="line";break;case"shape":a.tagName="g";break;case"textbox":a.tagName="foreignObject",Object.assign(a.attrs,{width:"100%",height:"100%"});break;default:return null}for(const e of n.default.attrs(t))switch(e.localName){case"style":a.cssStyleText=e.value;break;case"fillcolor":a.attrs.fill=e.value;break;case"from":const[t,r]=i(e.value);Object.assign(a.attrs,{x1:t,y1:r});break;case"to":const[s,n]=i(e.value);Object.assign(a.attrs,{x2:s,y2:n})}for(const s of n.default.elements(t))switch(s.localName){case"stroke":Object.assign(a.attrs,o(s));break;case"fill":Object.assign(a.attrs,{});break;case"imagedata":a.tagName="image",Object.assign(a.attrs,{width:"100%",height:"100%"}),a.imageHref={id:n.default.attr(s,"id"),title:n.default.attr(s,"title")};break;case"txbxContent":a.children.push(...r.parseBodyElements(s));break;default:const t=e(s,r);t&&a.children.push(t)}return a}},213:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deobfuscate=t.WordDocument=void 0;const a=r(461),s=r(380),n=r(522),l=r(448),o=r(593),i=r(527),c=r(240),p=r(985),u=r(665),d=r(162),m=r(893),h=r(735),f=r(287),g=r(177),b=[{type:a.RelationshipTypes.OfficeDocument,target:"word/document.xml"},{type:a.RelationshipTypes.ExtendedProperties,target:"docProps/app.xml"},{type:a.RelationshipTypes.CoreProperties,target:"docProps/core.xml"},{type:a.RelationshipTypes.CustomProperties,target:"docProps/custom.xml"}];class y{constructor(){this.parts=[],this.partsMap={}}static async load(e,t,r){var a=new y;return a._options=r,a._parser=t,a._package=await n.OpenXmlPackage.load(e,r),a.rels=await a._package.loadRelationships(),await Promise.all(b.map((e=>{const t=a.rels.find((t=>t.type===e.type))??e;return a.loadRelationshipPart(t.target,t.type)}))),a}save(e="blob"){return this._package.save(e)}async loadRelationshipPart(e,t){if(this.partsMap[e])return this.partsMap[e];if(!this._package.get(e))return null;let r=null;switch(t){case a.RelationshipTypes.OfficeDocument:this.documentPart=r=new l.DocumentPart(this._package,e,this._parser);break;case a.RelationshipTypes.FontTable:this.fontTablePart=r=new s.FontTablePart(this._package,e);break;case a.RelationshipTypes.Numbering:this.numberingPart=r=new i.NumberingPart(this._package,e,this._parser);break;case a.RelationshipTypes.Styles:this.stylesPart=r=new c.StylesPart(this._package,e,this._parser);break;case a.RelationshipTypes.Theme:this.themePart=r=new m.ThemePart(this._package,e);break;case a.RelationshipTypes.Footnotes:this.footnotesPart=r=new h.FootnotesPart(this._package,e,this._parser);break;case a.RelationshipTypes.Endnotes:this.endnotesPart=r=new h.EndnotesPart(this._package,e,this._parser);break;case a.RelationshipTypes.Footer:r=new p.FooterPart(this._package,e,this._parser);break;case a.RelationshipTypes.Header:r=new p.HeaderPart(this._package,e,this._parser);break;case a.RelationshipTypes.CoreProperties:this.corePropsPart=r=new d.CorePropsPart(this._package,e);break;case a.RelationshipTypes.ExtendedProperties:this.extendedPropsPart=r=new u.ExtendedPropsPart(this._package,e);break;case a.RelationshipTypes.CustomProperties:r=new g.CustomPropsPart(this._package,e);break;case a.RelationshipTypes.Settings:this.settingsPart=r=new f.SettingsPart(this._package,e)}if(null==r)return Promise.resolve(null);if(this.partsMap[e]=r,this.parts.push(r),await r.load(),r.rels?.length>0){const[e]=(0,o.splitPath)(r.path);await Promise.all(r.rels.map((t=>this.loadRelationshipPart((0,o.resolvePath)(t.target,e),t.type))))}return r}async loadDocumentImage(e,t){const r=await this.loadResource(t??this.documentPart,e,"blob");return this.blobToURL(r)}async loadNumberingImage(e){const t=await this.loadResource(this.numberingPart,e,"blob");return this.blobToURL(t)}async loadFont(e,t){const r=await this.loadResource(this.fontTablePart,e,"uint8array");return r?this.blobToURL(new Blob([v(r,t)])):r}blobToURL(e){return e?this._options.useBase64URL?(0,o.blobToBase64)(e):URL.createObjectURL(e):null}findPartByRelId(e,t=null){var r=(t.rels??this.rels).find((t=>t.id==e));const a=t?(0,o.splitPath)(t.path)[0]:"";return r?this.partsMap[(0,o.resolvePath)(r.target,a)]:null}getPathById(e,t){const r=e.rels.find((e=>e.id==t)),[a]=(0,o.splitPath)(e.path);return r?(0,o.resolvePath)(r.target,a):null}loadResource(e,t,r){const a=this.getPathById(e,t);return a?this._package.load(a,r):Promise.resolve(null)}}function v(e,t){const r=t.replace(/{|}|-/g,""),a=new Array(16);for(let e=0;e<16;e++)a[16-e-1]=parseInt(r.substr(2*e,2),16);for(let t=0;t<32;t++)e[t]=e[t]^a[t%16];return e}t.WordDocument=y,t.deobfuscate=v},943:t=>{t.exports=e}},r={};var a=function e(a){var s=r[a];if(void 0!==s)return s.exports;var n=r[a]={exports:{}};return t[a](n,n.exports,e),n.exports}(667),s=a.X$,n=a.vC,l=a.bk,o=a.j3,i=a.DN;export{s as __esModule,n as defaultOptions,l as praseAsync,o as renderAsync,i as renderDocument};
//# sourceMappingURL=docx-preview.min.mjs.map