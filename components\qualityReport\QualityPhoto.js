/**
 * 过程结构树中的质量影像记录
 */
var QualityPhoto = function () {
	var othis = this;
	this.qualityPlanId = 0;
	this.contextMenuTarget = {};
	//获取左侧选中的节点  默认选中根节点
	this.getSelTreeNode = function () {
		var ztreeObj = $.fn.zTree.getZTreeObj("dpTree");
		var selNodes = ztreeObj.getSelectedNodes();
		var treeNode;
		if (selNodes.length > 0) {
			treeNode = selNodes[0];
		} else {
			treeNode = ztreeObj.getNodeByParam("TREEID", 1, null);
		}
		return treeNode;
	};
	//初始化显示
	this.init = function () {
		var selNode = othis.getSelTreeNode();
		//判断是否为过程节点
		if (selNode.IS_PROCESS == 'true') {
			othis.hideMsg();
			othis.loadTypeSelect(selNode.TREEID);
		} else {
			othis.showMsg("请选择过程节点！");
		}
	};
	//显示提示消息 并且隐藏其他元素
	this.showMsg = function (msg) {
		$("#qualityPhotoMsg").text(msg).show();
		$('#qualityPhotoTypeDiv').hide();
		$('#qualityPhotoTbr').hide();
		$('#qualityPhotoDiv').hide();
		$('#photo_tabs').hide();
	};
	//隐藏提示消息 并且显示其他元素
	this.hideMsg = function () {
		$("#qualityPhotoMsg").hide();
		$('#qualityPhotoTypeDiv').show();
		$('#qualityPhotoTbr').show();
		$('#qualityPhotoDiv').show();
		$('#photo_tabs').show();
	};
	this.showTableMsg = function (msg) {
		$('#qualityPhotoDiv').empty();
		$('#qualityPhotoDiv').append(
			'<div style="float: left; width: 100%;color: red;padding-left: 15px;padding-top: 15px;">' +
			msg + '</div>');
	};
	//初始化表格上方的操作按钮
	this.initTbr = function (record, treeId) {

		//上传策划表
		$('#quality-photo-upload-plan').unbind("click").bind('click', function () {
			othis.uploadFile(record, treeId, "photo");
		});
		//下载策划表模板
		$('#quality-photo-download-planTpl').unbind("click").bind('click', function () {
			othis.downloadTplFile(record, treeId, "photo");
		});

		//下载所有照片
		$('#quality-photo-download-all-photo').unbind("click").bind('click', function () {
			othis.downloadAllPhoto(record.id, treeId);
		});

		//下载列表
		$('#quality-photo-download-list').unbind("click").bind('click', function () {
			qualityDownloadList(record.id, treeId, 2);
		});
	};

	//下载模板文件
	this.downloadTplFile = function (record, treeId, type) {
		var path = "";
		var name = "";
		if (type = 'photo') {
			path = record.photoPath;
			name = "影像记录策划";
		}
		if (path != undefined && path != '' && path != null) {
			var fileName = record.name + "_" + name + "表模板.xlsx";
			var url = fileHandlerUrl + "/first/phase/download/file";
			var form = $("<form></form>").attr("action", url).attr("method", "post");
			form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value",
				fileName));
			form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value",
				path));
			form.appendTo('body').submit().remove();
		} else {
			layer.alert("该节点未上传" + name + "表模板！", {
				icon: 2
			})
		}
	};
	//上传策划表
	this.uploadFile = function (record, treeId, type) {
		var name = "";
		var url = "";
		var fileDone;
		if (type = 'photo') {
			name = "影像记录策划";
			var params = '';
			params += 'type=2'; //代表上传的是策划表类型是影像记录
			params += '&tableConfigId=' + record.id;
			params += '&treeId=' + treeId;
			params += '&creator=' + sessionStorage.getItem('username');
			url = fileHandlerUrl + '/table/import/plan?' + params;
			fileDone = function (res, index, upload) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					othis.loadTableData(res.data, treeId, record.id, []);
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			};
		}
		var area = ['350px', '220px'];

		var fileFlag = false;

		layer.open({
			title: '导入' + record.name + name + '表',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: area,
			content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			success: function () {
				var addTpl = $("#uploadHtml")[0].innerHTML;
				$("#uploadContent").append(addTpl);
			},
			btn: ['确认', '取消'],
			yes: function () {
				if (!fileFlag) {
					layer.alert('请选择需要导入的excel文件!', {
						icon: 2
					});
					return false;
				}
				uploadInst.config.url = url;
				if (device.ie && device.ie < 10) {
					$("form[target]")[0].action = url;
				}
				$('#uploadStart').click();
			},
			btn2: function () {
				layer.closeAll();
			}
		});

		form.render(null, 'uploadForm');

		var uploadInst = upload.render({
			elem: '#uploadChoice',
			url: url,
			auto: false,
			accept: 'file',
			field: 'uploadFile',
			exts: 'xls|xlsx',
			bindAction: '#uploadStart',
			dataType: "json",
			choose: function (obj) {
				fileFlag = true;
				var o = obj.pushFile();
				var filename = '';
				for (var k in o) {
					var file = o[k];
					filename = file.name;
				}
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			},
			before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
				// layer.load(); //上传loading
			},
			done: fileDone
		});
		if (device.ie && device.ie < 10) {
			$("input[name='uploadFile']").change(function () {
				var filename = $(this).val();
				filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
				$("#selectedFile").show();
				$("#selectedFileName").text(filename);
			});
		}
	};
	//根据数据和名称 获取对应的 名称、代号和批次号
	this.getInfo = function (cols, row) {
		var photoCountStr = "照片数量";
		var photoCount = 0;
		for (var i = 0; i < row.length; i++) {
			if (i == cols.indexOf(photoCountStr)) {
				photoCount = row[i];
			}
		}
		return {
			photoCount: photoCount
		}
	};

	//下载单元格照片
	this.downloadAllPhoto = function (tableConfigId, treeId) {
		twxAjax('Thing.Fn.SecondTable', 'ReqGenerateFile', {
			tableConfigId: tableConfigId,
			treeId: treeId,
			exportType: 2,
			creator: sessionStorage.getItem("username"),
			type_: 2
		}, true, function (res) { }, function (xhr, textStatus, errorThrown) { });
		layer.alert("文件正在生成中，请稍后在下载列表中查看下载！");
	};

	//下载单元格照片
	this.downloadTdPhoto = function (tableConfigId, treeId) {
		var photos = othis.contextMenuTarget.data("photos");
		var onlyValue = othis.contextMenuTarget.data("onlyValue");
		var paramId = othis.contextMenuTarget.data("paramId");


		var loading;
		$.fileDownload(fileHandlerUrl + "/table/download/td/photos", {
			httpMethod: 'POST',
			data: {
				onlyValue: JSON.stringify(onlyValue),
				tableConfigId: tableConfigId,
				treeId: treeId,
				paramId: paramId
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在下载...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				layer.close(loading);
				layer.msg("下载异常！！");
			},
			successCallback: function (url) {
				layer.close(loading);
			},
			failCallback: function (html, url) {
				layer.close(loading);
				layer.alert(html, {
					icon: 2
				});
			}
		});

		// var cb_success = function(res) {
		// 	console.log(res);
		// 	if (res.success) {

		// 	} else {
		// 		layer.alert(res.msg);
		// 	}
		// }
		// //请求失败的回调
		// var cb_error = function(xhr, textStatus, errorThrown) {
		// 	layer.alert("查询单元格的照片信息出错！", {
		// 		icon: 2
		// 	});
		// };

		// twxAjax("Thing.Fn.SecondTable", "QueryTdPhoto", {
		// 	onlyValue: JSON.stringify(onlyValue),
		// 	tableConfigId: tableConfigId,
		// 	treeId: treeId,
		// 	paramId: paramId
		// }, true, cb_success, cb_error);
	};
	this.getPhotoStr = function (photos) {
		var photoStr = "";
		var uploadNum = 0;
		var photo360Num = 0;
		var autoNum = 0;
		for (var p = 0; p < photos.length; p++) {
			var photo = photos[p];
			if (photo.TYPE == 'auto') {
				autoNum++;
			} else {
				if (photo.PHOTO_FORMAT == 'zip' || photo.PHOTO_FORMAT == 'rar') {
					photo360Num++;
				} else {
					uploadNum++;
				}
			}
		}
		if (uploadNum > 0) {
			photoStr += "<br>手动上传(" + uploadNum + ")";
		}
		if (autoNum > 0) {
			photoStr += "<br>自动采集(" + autoNum + ")";
		}
		if (photo360Num > 0) {
			photoStr += "<br>全景影像(" + photo360Num + ")";
		}
		if (photoStr != "") {
			photoStr = photoStr.substr(4);
		}
		return photoStr;
	};
	this.tdIsConfirm = function () {
		var tdConfirmer = othis.contextMenuTarget.next().next().next().text();
		var confirmer = othis.contextMenuTarget.data("confirmer") || (tdConfirmer == "确认" ? "" : tdConfirmer);
		if (confirmer) {
			return true;
		} else {
			return false;
		}
	};
	// 预览图片
	this.previewPhoto = function (tableConfigId, treeId, isConfirm) {
		var photos = othis.contextMenuTarget.data("photos");
		photos.sort(function (x, y) {
			return x['PHOTO_NUMBER'].localeCompare(y['PHOTO_NUMBER'], "zh");
		});
		//删除过的照片
		var deletePhotos = [];
		layer.open({
			title: '查看图片',
			type: 1,
			area: ['1130px', '560px'],
			content: '<div id="previewContent"></div>',
			anim: false,
			zIndex: 500,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			cancel: function (index, layero) {
				if ($(".picView-magnify-list").data("delete")) {
					//如果删除过照片则需要在关闭预览窗口的时候刷新整个表格
					othis.reloadPhotoTd(deletePhotos, "delete", treeId, tableConfigId);
					// othis.postTableData(tableConfigId, treeId);
				}
			},
			success: function () {
				var $ul = $('<ul class="picView-magnify-list" style="padding: 30px"></ul>');
				for (var i = 0; i < photos.length; i++) {
					var p = photos[i];
					var photoName = p['PHOTO_NUMBER'];
					var photoPath = p['PHOTO_PATH'];
					var srcPath = p['PHOTO_PATH'];
					var photoFormat = p['PHOTO_FORMAT'];
					var photoType = p['TYPE'];
					var photoId = p['ID'];
					if (photoType == 'upload') {
						srcPath = "/File" + srcPath;
						if (photoFormat == 'zip' || photoFormat == 'rar') {
							srcPath = "../../img/全景图片.png";
						}
					}

					var deleteDiv =
						'<div style="text-align: right;color:red;"><i class="layui-icon" photo-id="' +
						photoId + '" photo-path="' + photoPath +
						'" style="cursor: pointer;font-size: 20px;">&#xe640;</i></div>';
					var li = '<li>\
								<a href="javascript:void(0)" data-magnify="gallery" data-group="g1" data-src="' + srcPath +
						'" data-caption="' + photoName + '">\
									<img src="' + srcPath + '">\
								</a>\
								<div class="img-title" photo-path="' + photoPath + '">' + photoName + '</div>\
							</li>';
					var $li = $(li);
					if (photoFormat == 'zip' || photoFormat == 'rar') {
						$li.find(".img-title").css({
							'cursor': "pointer",
							'color': 'blue',
							'text-decoration': 'underline'
						}).click(function () {
							show360ImgByPath($(this).attr("photo-path"));
						});
					}

					//如果没有确认 可以删除影像记录
					if (isConfirm) {

					} else {
						if (!othis.tdIsConfirm() && photoType == 'upload') {
							$li.prepend(deleteDiv);
						}
					}
					$ul.append($li);
				}
				$('#previewContent').append($ul);

				if (!othis.tdIsConfirm()) {
					//删除事件
					$ul.find('i').click(function () {
						var $i = $(this);
						var photoPath = $i.attr("photo-path");
						var photoId = $i.attr("photo-id");
						twxAjax("Thing.Fn.SecondTable", "DeletePhoto", {
							id: photoId,
							photoPath: photoPath
						}, true, function (res) {
							if (res.success) {
								layer.msg(res.msg);
								$i.parent().parent().remove();
								deletePhotos.push({
									"id": photoId,
									"photoPath": photoPath
								});
								$ul.data("delete", true);
							} else {
								layer.alert(res.msg);
							}
						}, function () {
							layer.alert("删除失败");
						});
					});
				}

				$('[data-magnify]').magnify({
					resizable: false,
					initMaximized: true,
					headerToolbar: [
						'close'
					],
					i18n: {
						minimize: '最小化',
						maximize: '最大化',
						close: '关闭',
						zoomIn: '缩小',
						zoomOut: '放大',
						prev: '上一张',
						next: '下一张',
						fullscreen: '全屏',
						actualSize: '实际大小',
						rotateLeft: '向左旋转',
						rotateRight: '向右旋转',
					}
				});
			}
		});
	};

	this.getPhotoName = function () {
		var onlyValue = othis.contextMenuTarget.data("onlyValue");
		var nameArr = [];
		for (var key in onlyValue) {
			nameArr.push(onlyValue[key]);
		}
		var names = nameArr.join("_");
		return names;
	};
	this.getPhotoIndex = function () {
		var photoIndex = 0;
		var photos = othis.contextMenuTarget.data("photos");
		var uploadPhotos = [];
		for (var i = 0; i < photos.length; i++) {
			if (photos[i]['TYPE'] == 'upload') {
				uploadPhotos.push(photos[i]);
			}
		}
		if (uploadPhotos.length > 0) {
			photoIndex = Number(uploadPhotos[uploadPhotos.length - 1]['PHOTO_NUMBER'].split('-').reverse()[0]);
		}
		return photoIndex;
	};
	this.upload360Photo = function (params) {
		params.paramId = othis.contextMenuTarget.data("paramId");
		var onlyValue = othis.contextMenuTarget.data("onlyValue");
		var tableName = othis.contextMenuTarget.data("tableName");
		var names = othis.getPhotoName();
		var photoIndex = othis.getPhotoIndex() + 1;
		var photoNumber = names + "-" + tableName.substr(tableName.indexOf(')') + 1) + "-" + dealPrefix(
			photoIndex, 3);
		oneBigFileUpload({
			url: fileHandlerUrl1 + '/table/upload/360/photo',
			layerTitle: "上传全景影像",
			emptyMsg: "请选择需要导入的zip文件!",
			accept: {
				title: 'ZIP文件', // 显示在文件选择对话框中的标题
				extensions: 'zip', // 允许的文件扩展名
				mimeTypes: 'application/zip,application/x-zip-compressed,application/x-compressed' // 允许的MIME类型
			},
			uploadSuccess: function (uploadRes) {
				params.photoPath = uploadRes.data.photoPath;
				params.photoSize = uploadRes.data.photoSize;
				params.photoFormat = uploadRes.data.photoFormat;
				params.photoName = uploadRes.data.photoName;
				params.photoNumber = photoNumber;
				params.creator = sessionStorage.getItem('username');
				params.onlyValue = JSON.stringify(onlyValue);
				var cb_success = function (res) {
					if (res.success) {
						layer.closeAll();
						layer.msg('上传成功！');
						othis.reloadPhotoTd([{
							"ID": res.data,
							"PHOTO_FORMAT": params.photoFormat,
							"PHOTO_NUMBER": params.photoNumber,
							"PHOTO_PATH": params.photoPath,
							"TYPE": "upload"
						}], "add", params.treeId, params.tableConfigId);
					} else {
						layer.alert(res.msg);
					}
				}
				//请求失败的回调
				var cb_error = function (xhr, textStatus, errorThrown) {
					layer.alert("增加质量影像记录出错！", {
						icon: 2
					});
				};
				//同步新增
				twxAjax("Thing.Fn.SecondTable", "AddQualityPhoto", {
					data: params
				}, false, cb_success, cb_error);

			}
		})
	};
	this.reloadPhotoTd = function (modifyPhotos, type, treeId, tableConfigId) {
		var oldPhotos = othis.contextMenuTarget.data("photos");
		var newPhotos = [];
		if (type == "add") {
			newPhotos = oldPhotos.concat(modifyPhotos);
		} else if (type == "delete") {
			var isInModifyPhotos = function (photoId) {
				var flag = false;
				for (var i = 0; i < modifyPhotos.length; i++) {
					if (photoId == modifyPhotos[i].id) {
						flag = true;
						break;
					}
				}
				return flag;
			}

			var newPhotos = [];
			for (var i = 0; i < oldPhotos.length; i++) {
				var photo = oldPhotos[i];
				if (!isInModifyPhotos(photo.ID)) {
					newPhotos.push(photo);
				}
			}
		}
		othis.contextMenuTarget.data("photos", newPhotos);
		othis.contextMenuTarget.empty().html(othis.getPhotoStr(newPhotos));
		othis.photoTdMenus(othis.contextMenuTarget, othis.tdIsConfirm(), (newPhotos.length > 0), treeId,
			tableConfigId);
		//更新照片数量
		othis.contextMenuTarget.next().empty().text(newPhotos.length);
	}
	//上传照片
	this.uploadPhoto = function (params) {
		params.paramId = othis.contextMenuTarget.data("paramId");
		var onlyValue = othis.contextMenuTarget.data("onlyValue");
		var tableName = othis.contextMenuTarget.data("tableName");
		var names = othis.getPhotoName();
		var photoIndex = othis.getPhotoIndex();

		var tpl = '<div style="padding:12px;">\
						<div class="layui-upload" style="margin-bottom:2px;">\
							<button type="button" class="layui-btn layui-btn-normal" id="chooseFile">选择照片</button>\
							<button type="button" class="layui-btn" id="startUpload" style="display:none;">开始上传</button>\
						</div>\
						<table id="photo-table" lay-filter="photo-table"></table>\
					</div>';
		if (device.ie && device.ie < 10) {
			layer.alert("请使用Chrome浏览器操作！", {
				icon: 2
			});
		} else {
			layer.open({
				title: '上传照片',
				type: 1,
				area: ['1100px', '560px'],
				content: tpl,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				resize: false,
				btn: ['上传', '取消'],
				yes: function () {
					var photoDatas = trimSpace(layui.table.cache["photo-table-id"]);
					if (photoDatas.length == 0) {
						layer.msg('请选择照片!');
						return false;
					}
					var flag = true;
					for (var i = 0; i < photoDatas.length; i++) {
						var d = photoDatas[i];
						if (d.photoName == '' || d.photoNumber == '') {
							flag = false;
							break;
						}
					}
					if (!flag) {
						layer.msg('照片名称和照片编号为必填项！');
						return false;
					} else {
						$('#startUpload').click();
					}
				},
				btn2: function () {
					layui.table.cache["photo-table-id"] = [];
					existPhotos = {};
					return true;
				},
				success: function () {
					var photoTable = table.render({
						elem: '#photo-table',
						data: [],
						height: 375,
						cellMinWidth: 80, //全局定义常规单元格的最小宽度
						id: 'photo-table-id',
						limit: 999,
						cols: [
							[{
								field: 'photoName',
								title: '照片名称',
								templet: '<div>{{d.photoName}}</div>',
								width: 200
							}, {
								field: 'photoNumber',
								title: '照片编号',
								templet: '<div>{{d.photoNumber}}</div>'
							}, {
								field: 'photoSize',
								title: '照片大小',
								templet: '<div>{{d.photoSize}}</div>',
								width: 100
							}, {
								field: 'index',
								hide: true
							},
							{
								field: 'photoFormat',
								title: '照片格式',
								templet: '<div>{{d.photoFormat}}</div>',
								width: 100
							}, {
								field: 'operation',
								title: '操作',
								templet: '<div>\
										<a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>\
									</div>',
								width: 100
							}
							]
						]
					});
					table.on('tool(photo-table)', function (obj) {
						if (obj.event === 'del') {
							obj.del();
							if (device.ie && device.ie < 10) {

							} else {
								var index = obj.tr.find('td[data-field="index"]').children(
									'div').text();
								layui.table.cache["photo-table-id"] = trimSpace(layui.table
									.cache["photo-table-id"]);
								removeArray('index', index, layui.table.cache[
									"photo-table-id"]);
								delete existPhotos[index];
							}
						};
					});

					function getRowData(fileIndex) {
						var d = {};
						var datas = layui.table.cache["photo-table-id"];
						for (var i = 0; i < datas.length; i++) {
							if (datas[i].index == fileIndex) {
								d = datas[i];
							}
						}
						return d;
					}

					//上传成功的照片合集
					var uploadSuccessPhotos = [];

					var photoUploadIns = upload.render({
						elem: '#chooseFile',
						url: fileHandlerUrl + '/table/upload/photo',
						auto: false,
						multiple: true,
						accept: 'images',
						field: 'photo',
						bindAction: '#startUpload',
						dataType: "json",
						before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
							loadIndex = layer.load(); //上传loading
						},
						allDone: function (obj) {
							//提示完成后，点击确定再刷新界面
							if (obj.aborted > 0) {
								layer.close(loadIndex);
								layer.msg('上传失败！');
							} else {
								layer.closeAll();
								layui.table.cache["photo-table-id"] = [];
								existPhotos = {};
								layer.msg('上传成功！');
								othis.reloadPhotoTd(uploadSuccessPhotos, "add", params
									.treeId, params.tableConfigId);
							}

						},
						choose: function (obj) {
							layui.table.cache["photo-table-id"] = trimSpace(layui.table
								.cache["photo-table-id"]); //重置表格数据
							var tableData = layui.table.cache["photo-table-id"];
							//将每次选择的文件追加到文件队列
							var files = obj.pushFile();
							existPhotos = files;

							function isInTable(key, tableData) {
								for (var i = 0; i < tableData.length; i++) {
									if (key == tableData[i].index) {
										return true;
									}
								}
								return false;
							}

							var allConut = Object.keys(existPhotos).length;
							for (var key in existPhotos) {
								if (!isInTable(key, tableData)) {
									var file = existPhotos[key];
									var fileName = file.name;
									var o = {};
									//2022-06-09 图片名称删除要素名称 --- "-" + tableName.substr(tableName.indexOf(')') + 1) + 
									// o.photoNumber = names + "-" + dealPrefix(photoIndex, 3);
									o.photoName = fileName.substr(0, fileName
										.lastIndexOf('.'));
									o.photoFormat = fileName.substr(fileName
										.lastIndexOf('.') + 1);
									o.photoSize = (file.size / 1024).toFixed(1) + 'kb';
									o.index = key;
									o.file = file;
									layui.table.cache["photo-table-id"].push(o);
									layui.table.cache["photo-table-id"].sort(function (x,
										y) {
										return x['photoName'].localeCompare(y[
											'photoName'], "zh");
									});
									layui.table.cache["photo-table-id"].sort(function (x,
										y) {
										return x['photoName'].localeCompare(y[
											'photoName'], "zh");
									});
									for (var i = 0; i < layui.table.cache[
										"photo-table-id"].length; i++) {
										layui.table.cache["photo-table-id"][i]
											.photoNumber = names + "-" + dealPrefix(
												photoIndex + i + 1, 3);
									}
									photoTable.reload({
										data: layui.table.cache[
											"photo-table-id"]
									});

								}
							}
							photoUploadIns.reload();
						},
						done: function (res, fileIndex) {
							if (res.success) {
								var rowData = getRowData(fileIndex);
								params.photoPath = res.data[0].photoPath;
								params.photoFormat = rowData.photoFormat;
								params.photoName = rowData.photoName;
								params.photoNumber = rowData.photoNumber;
								params.photoSize = rowData.photoSize;
								params.onlyValue = JSON.stringify(onlyValue);
								params.creator = sessionStorage.getItem('username');
								var cb_success = function (res1) {
									if (res1.success) {
										uploadSuccessPhotos.push({
											"ID": res1.data,
											"PHOTO_PATH": params.photoPath,
											"PHOTO_NUMBER": params
												.photoNumber,
											"PHOTO_FORMAT": params
												.photoFormat,
											"TYPE": "upload"
										});
									} else {
										// layer.alert(res.msg);
									}
								}
								//请求失败的回调
								var cb_error = function (xhr, textStatus, errorThrown) {
									layer.alert("增加质量影像记录出错！", {
										icon: 2
									});
								};
								//同步新增
								twxAjax("Thing.Fn.SecondTable", "AddQualityPhoto", {
									data: params
								}, false, cb_success, cb_error);
							} else {
								layer.alert(res.msg);
							}
						}
					});
				}
			});
		}
	};

	this.photoTdMenus = function (td, isConfirm, hasPhotos, treeId, tableConfigId) {
		var menus = [];
		var uploadMenu = {
			text: "上传照片",
			icon: '../dataTree/images/upload.png',
			callback: function (btn) {
				othis.uploadPhoto({
					treeId: treeId,
					tableConfigId: tableConfigId
				});
			}
		};
		var upload360Menu = {
			text: "上传全景影像",
			icon: '../dataTree/images/upload.png',
			callback: function (btn) {
				othis.upload360Photo({
					treeId: treeId,
					tableConfigId: tableConfigId
				});
			}
		};
		var viewMenu = {
			text: "查看照片",
			icon: '../dataTree/images/view.png',
			callback: function () {
				othis.previewPhoto(tableConfigId, treeId);
			}
		};
		var downloadMenu = {
			text: "下载照片",
			icon: '../dataTree/images/view.png',
			callback: function () {
				othis.downloadTdPhoto(tableConfigId, treeId);
			}
		};

		if (!isConfirm) {
			menus.push(uploadMenu);
			menus.push(upload360Menu);
		}

		if (hasPhotos) {
			menus.push(viewMenu);
			menus.push(downloadMenu);
		}

		if (menus.length > 0) {
			td.contextMenu({
				width: 125,
				menu: menus,
				target: function (ele) {
					othis.contextMenuTarget = ele;
				}
			});
		}
	};

	//单行的质量数据确认
	this.singleLineConfirm = function (btn, treeId, tableConfigId, d, x, cols) {
		var cols = $(btn).data("cols");
		var row = $(btn).data("row");
		var paramName = $(btn).data("paramName");
		var paramId = $(btn).data("paramId");
		var onlyValue = $(btn).data("onlyValue");
		var $td = $(btn).parent();
		var res = othis.getInfo(cols, row);
		if (res.photoCount == 0) {
			layer.alert("暂无照片，不可确认！", {
				icon: 2
			});
			return false;
		}
		var confirmer = sessionStorage.getItem('username');
		var cb_success = function (res) {
			if (res.success) {
				layer.msg(res.msg);
				$td.empty().text(sessionStorage.getItem("fullname"));
				othis.photoTdMenus($td.prev().prev().prev(), true, true, treeId, tableConfigId);
				othis.initCancelConfirmBtn($td, tableConfigId, treeId, $td.parent(), d, x, cols, {
					ID: paramId,
					PARAM_NAME: paramName
				}, onlyValue);
				// othis.postTableData(tableConfigId, treeId);
			} else {
				layer.alert(res.msg);
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("确认质量影像记录出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "QualityDataConfirm", {
			onlyValue: JSON.stringify(onlyValue),
			tableConfigId: tableConfigId,
			treeId: treeId,
			paramName: paramName,
			paramId: paramId,
			paramValue: "",
			confirmer: confirmer,
			type: 2
		}, false, cb_success, cb_error);

	};

	//初始化确认按钮
	this.initConfirmBtn = function ($tr, d, x, cols, relationParam, onlyValue, treeId, tableConfigId) {
		var $td = $('<td align="' + planTableAlign + '"></td>');
		var $btn = $('<button class="layui-btn layui-btn-sm layui-btn-normal">确认</button>');
		$btn.data('row', d)
			.data('cols', cols)
			.data('paramName', relationParam["PARAM_NAME"])
			.data('paramId', relationParam["ID"])
			.data("onlyValue", onlyValue);
		$btn.bind('click', function () {
			othis.singleLineConfirm(this, treeId, tableConfigId, d, x, cols);
		});
		$td.append($btn);
		$tr.append($td);
	};
	//初始化取消确认按钮
	this.initCancelConfirmBtn = function ($td, tableConfigId, treeId, $tr, d, x, cols, relationParam, onlyValue) {
		//如果有权限的话 允许取消确认
		var allfuns = sessionStorage.getItem('funcids');
		var funcArr = allfuns.split(',');
		var isCancel;
		if (contains(funcArr, 'cancel-photo-confirm')) {
			isCancel = true;
		} else {
			isCancel = false;
		}
		if (isCancel) {
			$td.data('paramId', relationParam['ID'])
				.data("onlyValue", onlyValue);
			$td.contextMenu({
				width: 125,
				menu: [{
					text: "取消确认",
					icon: '../dataTree/images/view.png',
					callback: function () {
						twxAjax("Thing.Fn.SecondTable", 'CancelDataConfirm', {
							onlyValue: JSON.stringify($td.data('onlyValue')),
							tableConfigId: tableConfigId,
							treeId: treeId,
							paramId: $td.data('paramId'),
							optUser: sessionStorage.getItem("username"),
							type: 2
						}, true, function (res) {
							if (res.success) {
								othis.photoTdMenus($td.prev().prev().prev(), false,
									true, treeId, tableConfigId);
								$td.remove();
								othis.initConfirmBtn($tr, d, x, cols, relationParam,
									onlyValue, treeId, tableConfigId);
								layer.msg(res.msg)
							} else {
								layer.alert(res.msg);
							}
						}, function (xhr, textStatus, errorThrown) {
							layer.alert('请求出错！', {
								icon: 2
							});
						});
					}

				}],
				target: function (ele) {
					othis.contextMenuTarget = ele;
				}
			});
		}
	};

	//初始化确认人单元格
	this.initConfirmerTd = function ($tr, d, x, cols, relationParam, onlyValue, treeId, tableConfigId) {
		var $td = $('<td align="' + planTableAlign + '">' + d[x] + '</td>');
		$tr.append($td);
		othis.initCancelConfirmBtn($td, tableConfigId, treeId, $tr, d, x, cols, relationParam, onlyValue);
	};
	//加载质量数据确认表
	this.loadTableData = function (data, treeId, tableConfigId, signs) {
		if (data.length > 0) {
			$("#qualityPhotoDiv").empty();
			$("#qualityPhotoDiv").css('height', (windowH - 120) + 'px');
			$("#qualityPhotoDiv").show();
			var statusStr = "状态";
			var confirmerStr = "确认人";
			var actualPhotoStr = "实际照片";
			var $table = $('<table class="my-table layui-table"></table>');
			var $thead = $('<thead class="sticky-thead"></thead>');
			var cols = data[0].cols;
			//添加表头
			var $headerTr = $('<tr></tr>');
			for (var j = 0; j < cols.length; j++) {
				$headerTr.append('<td class="table-head" align="' + planTableAlign + '">' + cols[j] + '</td>');
			}
			$thead.append($headerTr);
			$table.append($thead);

			for (var i = 0; i < data.length; i++) {
				var table = data[i];
				var datas = table.datas;

				var onlyValues = table.onlyValues;
				var statusIndex = cols.indexOf(statusStr);
				var confirmerIndex = cols.indexOf(confirmerStr);
				var photoIndex = cols.indexOf(actualPhotoStr);
				var relationParam = table.relationParam;
				//添加合并行
				$table.append('<tr><td class="table-name" colspan=' + cols.length + '>' + table.name +
					'</td></tr>');
				//添加表数据
				for (var j = 0; j < datas.length; j++) {
					var $tr = $('<tr></tr>');
					var d = datas[j];
					var onlyValue = onlyValues[j];
					for (var x = 0; x < d.length; x++) {
						var paramId = relationParam["ID"];
						if (x == confirmerIndex) {
							//确认人一栏  添加确认按钮
							if (d[x] == '') {
								othis.initConfirmBtn($tr, d, x, cols, relationParam, onlyValue, treeId,
									tableConfigId);
							} else {
								othis.initConfirmerTd($tr, d, x, cols, relationParam, onlyValue, treeId,
									tableConfigId);
							}
						} else if (x == statusIndex) {
							//状态一栏 可编辑 如果确认了不可以编辑  但是确认之后有权限的人可以继续修改
							var isEidt = false;
							//
							if (d[confirmerIndex] == '') {
								isEidt = true;
							} else {
								var allfuns = sessionStorage.getItem('funcids');
								var funcArr = allfuns.split(',');
								if (contains(funcArr, 'modify-plan-status')) {
									isEidt = true;
								} else {
									isEidt = false;
								}
							}
							if (isEidt) {
								var $td = $('<td align="' + planTableAlign + '" data-field="status">' + d[x] +
									'</td>');
								$td.data('row', d)
									.data('cols', cols)
									.data('paramName', relationParam["PARAM_NAME"])
									.data('paramId', relationParam["ID"])
									.data("onlyValue", onlyValue);
								$tr.append($td);
							} else {
								$tr.append('<td align="' + planTableAlign + '">' + d[x] + '</td>');
							}
						} else if (x == photoIndex) {
							var isConfirm = true;
							var hasPhotos = false;
							if (d[d.length - 1] == '') {
								isConfirm = false;
							}
							var photos = [];
							if (d[x] != '[]') {
								photos = JSON.parse(d[x]);
								hasPhotos = true;
							}
							var $td = $('<td align="' + planTableAlign + '">' + othis.getPhotoStr(photos) +
								'</td>');
							$td.data('photos', photos);
							$td.data('tableName', table.name);
							$td.data("confirmer", d[confirmerIndex]);
							$td.data('paramId', relationParam["ID"]);
							$td.data("onlyValue", onlyValue);
							$tr.append($td);
							othis.photoTdMenus($td, isConfirm, hasPhotos, treeId, tableConfigId);
						} else {
							var html = String(d[x]);
							if (html.indexOf(":noKey") > -1) {
								html = '<span style="color:red;">' + html.replace(":noKey", "") + "</span>";
							}
							$tr.append('<td align="' + planTableAlign + '">' + html + '</td>');
						}
					}
					$table.append($tr);
				}
			}
			$("#qualityPhotoDiv").append($table);

			$('#qualityPhotoDiv td[data-field="status"]').dblclick(function () {
				var $td = $(this);
				// 根据表格文本创建文本框 并加入表表中--文本框的样式自己调整
				var text = $td.text();
				$td.text("");
				var statusArr = ['监测', '固封', '冻结'];
				var $form = $('<div class="layui-form"></div>');
				var $select = $(
					'<select name="status" style="width:60px;" lay-filter="status"></select>');
				for (var m = 0; m < statusArr.length; m++) {
					if (text == statusArr[m]) {
						$select.append('<option value = "' + statusArr[m] + '" selected >' + statusArr[
							m] +
							'</option>');
					} else {
						$select.append('<option value = "' + statusArr[m] + '">' + statusArr[m] +
							'</option>');
					}
				}
				$form.append($select);
				$td.append($form);
				form.render('select');
				$form.find('.layui-form-select').css('width', '80px');
				form.on('select(status)', function (data) {
					$form.remove();
					$td.text(data.value);
					if (text != data.value) {
						var cols = $td.data("cols");
						var row = $td.data("row");
						var paramName = $td.data("paramName");
						var paramId = $td.data("paramId");
						var onlyValue = $td.data("onlyValue");

						var cb_success = function (res) {
							if (res.success) {
								layer.msg(res.msg);
							} else {
								layer.alert(res.msg);
							}
						}
						//请求失败的回调
						var cb_error = function (xhr, textStatus, errorThrown) {
							layer.alert("更新质量数据状态出错！", {
								icon: 2
							});
						};
						twxAjax("Thing.Fn.SecondTable", "UpdateQualityDataStatus", {
							onlyValue: JSON.stringify(onlyValue),
							tableConfigId: tableConfigId,
							treeId: treeId,
							paramName: paramName,
							paramId: paramId,
							status: data.value,
							paramValue: "",
							creator: sessionStorage.getItem('username'),
							type: 2
						}, false, cb_success, cb_error);
					}
				});
			});


			var $signDiv = $('<div class="layui-row"></div>');
			var $innerDiv = $('<div class="layui-col-md6"></div>');
			var $outerDiv = $('<div class="layui-col-md6"></div>');
			var $innerWacomBtn = $(
				'<button class="layui-btn layui-btn-sm layui-btn-normal">所内人员手写板签字</button>');
			var $innerMouseBtn = $('<button class="layui-btn layui-btn-sm">所内人员鼠标签字</button>');
			var $outerWacomBtn = $(
				'<button class="layui-btn layui-btn-sm layui-btn-normal">所外人员手写板签字</button>');
			var $outerMouseBtn = $('<button class="layui-btn layui-btn-sm">所外人员鼠标签字</button>');
			$innerWacomBtn.bind('click', function () {
				othis.sign(1, "wacom");
			});
			$innerMouseBtn.bind('click', function () {
				othis.sign(1, "mouse");
			});
			$outerWacomBtn.bind('click', function () {
				othis.sign(2, "wacom");
			});
			$outerMouseBtn.bind('click', function () {
				othis.sign(2, "mouse");
			});


			$innerDiv.append($innerWacomBtn).append($innerMouseBtn);
			$outerDiv.append($outerWacomBtn).append($outerMouseBtn);
			$signDiv.append($innerDiv).append($outerDiv);

			var $signImgDiv = $('<div class="layui-row"></div>');
			var $innerImgDiv = $(
				'<div class="layui-col-md6" style = "height:1px;" id="photo-innerImgDiv"></div>');
			var $outerImgDiv = $('<div class="layui-col-md6" id="photo-outerImgDiv"></div>');
			for (var i = 0; i < signs.length; i++) {
				var sign = signs[i];
				var type = sign['TYPE'];
				var img = sign['IMG'];
				var $img = $('<img>').attr('src', img).attr('class', 'sign-img');
				if (type == 1) {
					$innerImgDiv.append($img);
				} else {
					$outerImgDiv.append($img);
				}
			}
			$signImgDiv.append($innerImgDiv).append($outerImgDiv);
			$("#qualityPhotoDiv").append($signDiv).append($signImgDiv);
		} else {
			othis.showTableMsg("未查询到数据，有可能是策划表与模板不匹配导致的！");
		}
	};

	this.sign = function (type, mode) {
		var title = type == 1 ? '所内签名' : '所外签名';
		var imgDiv = type == 1 ? 'photo-innerImgDiv' : 'photo-outerImgDiv';
		if (device.ie && device.ie < 10) {
			layer.alert('请在Chrome浏览器中使用！', {
				icon: 2
			});
			// var objHtml = '<object id="DWebSignSeal" style="display:none;" classid="CLSID:77709A87-71F9-41AE-904F-886976F99E3E"\
			// 	        codebase="http://www.XXX.com.cn/demo/websign/WebSign.ocx#version=4,4,9,6" width="100" height="100"></object>';
			// if ($("body").find("#DWebSignSeal").length == 0) {
			// 	$('body').prepend(objHtml);
			// }
			// var strSealName = document.all.DWebSignSeal.HandWrite(8, 0, "signName");
			// document.all.DWebSignSeal.ShowWebSeals();
			// var data = document.all.DWebSignSeal.GetSealBmpString(strSealName, "jpg");
			// var dataUrl = "data:image/jpg;base64," + data;
			// var $img = $('<img>').attr('src', dataUrl).attr('class', 'sign-img');
			// // document.all.DWebSignSeal.GetSealBmpToFile(strSealName, "jpg", "C:\\signTest\\test.jpg");
			// document.all.DWebSignSeal.DelSeal(strSealName);
			// var cb_success = function(res) {
			// 	if (res.success) {
			// 		$("#" + imgDiv).prepend($img);
			// 		layer.closeAll();
			// 		layer.msg('签名成功！');
			// 	} else {
			// 		layer.alert(res.msg);
			// 	}
			// }
			// //请求失败的回调
			// var cb_error = function(xhr, textStatus, errorThrown) {
			// 	layer.alert("签名出错！", {
			// 		icon: 2
			// 	});
			// };
			// //同步新增
			// twxAjax("Thing.Fn.SecondTable", "AddQualitySign", {
			// 	qualityPlanId: othis.qualityPlanId,
			// 	type: type,
			// 	img: dataUrl,
			// 	creator: sessionStorage.getItem('username')
			// }, false, cb_success, cb_error);
		} else {
			if (mode == 'wacom') {

				function saveSignToBase64() {
					if (!!signPlugin) {
						//Get the signPlugin's signature image data.
						signPlugin.saveSignToBase64( /*615, 272*/ 0, 0, function (state, args) {
							if (state) {
								var img_base64_data = args[0];
								//Show the signature image.
								var img_base64 = "data:image/png;base64," + img_base64_data;
								if (img_base64 == blankSign) {
									layer.alert('获取签名图形失败！', {
										icon: 2
									});
								} else {
									document.getElementById("img_sign_result").src = img_base64;

									var dataUrl = $('#img_sign_result').attr("src");

									if (dataUrl) {
										var cb_success = function (res) {
											if (res.success) {
												var $img = $('<img>').attr('src', dataUrl).attr(
													'class', 'sign-img');
												$("#" + imgDiv).prepend($img);
												layer.closeAll();
												layer.msg('签名成功！');
											} else {
												layer.alert(res.msg);
											}
										}
										//请求失败的回调
										var cb_error = function (xhr, textStatus, errorThrown) {
											layer.alert("签名出错！", {
												icon: 2
											});
										};
										//同步新增
										twxAjax("Thing.Fn.SecondTable", "AddQualitySign", {
											qualityPlanId: othis.qualityPlanId,
											type: type,
											img: dataUrl,
											creator: sessionStorage.getItem('username')
										}, false, cb_success, cb_error);
									} else {
										layer.alert('请先签名！', {
											icon: 2
										});
									}
								}
								debugPrint("saveSignToBase64 OK");
								//Submit the signature base64 string to the server
								//...
							} else {
								debugPrint("saveSignToBase64 error,description:" + args[0]);
							}
						});
					}
				}

				/*confirm event*/
				signPlugin.onConfirm = function () {
					saveSignToBase64();
					endSign();
				};

				//在页面中构建一个隐藏的img标签用来存储签名图片 
				var $signImg = $('<img style="display:none;" src="" id="img_sign_result"/>');
				$signImg.appendTo($('body'));
				beginSign();
			} else {
				layer.open({
					title: title,
					type: 1,
					area: ['900px', '560px'],
					content: '<div id = "photo-signContent"></div>',
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					resize: false,
					btn: ['确定', '重签', '取消'],
					yes: function () {
						var dataUrl = $('.js-signature').eq(0).jqSignature('getDataURL');
						var $img = $('<img>').attr('src', dataUrl).attr('class', 'sign-img');

						var cb_success = function (res) {
							if (res.success) {
								$("#" + imgDiv).prepend($img);
								layer.closeAll();
								layer.msg('签名成功！');
							} else {
								layer.alert(res.msg);
							}
						}
						//请求失败的回调
						var cb_error = function (xhr, textStatus, errorThrown) {
							layer.alert("签名出错！", {
								icon: 2
							});
						};
						//同步新增
						twxAjax("Thing.Fn.SecondTable", "AddQualitySign", {
							qualityPlanId: othis.qualityPlanId,
							type: type,
							img: dataUrl,
							creator: sessionStorage.getItem('username')
						}, false, cb_success, cb_error);
					},
					btn2: function () {
						$('.js-signature').eq(0).jqSignature('clearCanvas');
						return false;
					},
					btn3: function () {
						return true;
					},
					success: function () {
						var tpl = '<div class="js-signature" data-width="900" data-height="450" data-border="1px solid #333"\
										data-line-color="#000" data-line-width="3" data-auto-fit="true">\
									</div>'
						$("#photo-signContent").append(tpl);
						$('.js-signature').jqSignature();

					}
				});
			}
		}
	};
	//导出质量影像记录确认表excel
	this.exportQualityPhotoExcel = function (tableConfigId, treeId) {

		twxAjax('Thing.Fn.SecondTable', 'ReqGenerateFile', {
			tableConfigId: tableConfigId,
			treeId: treeId,
			exportType: 1,
			creator: sessionStorage.getItem("username"),
			type_: 2,
			fileName: $('#qualityPhotoType').combobox("getValue") + "质量影像记录确认表"
		}, true, function (res) { }, function (xhr, textStatus, errorThrown) { });
		layer.alert("文件正在生成中，请稍后在下载列表中查看下载！");
	};
	//从服务器请求质量数据
	this.postTableData = function (tableConfigId, treeId) {
		var loadIndex = layer.load();
		var cb_success = function (res) {
			if (res.success) {
				othis.loadTableData(res.data, treeId, tableConfigId, res.signs);
				othis.qualityPlanId = res.id;
				//下载策划表
				$('#quality-photo-download-plan').show().unbind("click").bind('click', function () {
					var loading;
					var url = fileHandlerUrl + "/table/export/photo/plan";
					$.fileDownload(url, {
						httpMethod: 'POST',
						data: {
							"processTreeId": treeId,
							"tableId": tableConfigId
						},
						prepareCallback: function (url) {
							loading = layer.msg("正在下载...", {
								icon: 16,
								shade: 0.3,
								time: 0
							});
						},
						abortCallback: function (url) {
							layer.close(loading);
							layer.msg("下载异常！！");
						},
						successCallback: function (url) {
							layer.close(loading);
						},
						failCallback: function (html, url) {
							layer.close(loading);
							layer.alert(html, {
								icon: 2
							})
						}
					});
				});
				//导出质量表
				$('#quality-photo-export').show().unbind("click").bind('click', function () {
					othis.exportQualityPhotoExcel(tableConfigId, treeId);
				});
			} else {
				othis.qualityPlanId = 0;
				$('#quality-photo-download-plan').hide();
				$('#quality-photo-export').hide();
				othis.showTableMsg(res.msg);
			}
			layer.close(loadIndex);
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("加载质量影像记录出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "QueryProcessQualityPhotoData", {
			treeId: treeId,
			tableConfigId: tableConfigId
		}, true, cb_success, cb_error);
	};

	//初始化汇总表
	this.renderSummaryTable = function (cols, datas) {
		$('#photoSummaryTable').datagrid({
			data: datas,
			columns: dealGridColumns(cols),
			height: windowH - 130,
			singleSelect: true,
			remoteSort: false,
			// pagination: true,
			emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
			loadMsg: '正在加载数据...',
			striped: false,
			onLoadSuccess: function (data) {
				changeWidth('photoSummaryTable');
				$("#photoSummaryDiv .datagrid-body").css("overflow-x", "auto");
				var tableW = $("#photoSummaryDiv").parent().width();
				$("#photoSummaryDiv .datagrid-wrap").css("width", tableW + "px");
				$('#photoSummaryTable').datagrid('loaded');
			}
		});
	};

	//请求汇总表数据
	this.postSummaryData = function (treeId, tableConfigId) {
		var loadIndex = layer.load();
		var cb_success = function (data) {
			if (data.success) {
				$("#photoSummaryDiv").empty().append(
					'<div id="photoSummaryTable" style="float: left;" data-options="border:false"></div>'
				);
				othis.renderSummaryTable(data.data.gridCols, data.data.gridDatas);
				layer.close(loadIndex);
			} else {
				$("#photoSummaryDiv").empty().append(
					'<div style="float: left; width: 100%;color: red;padding-left: 30px;padding-top: 5px;">' +
					data.msg + '</div>');
			}
		};
		var cb_error = function () {
			layer.alert("汇总表获取失败！", {
				icon: 2
			});
		};
		twxAjax('Thing.Fn.SecondTable', 'QueryQualityPhotoSummary', {
			tree_id: treeId,
			table_config_id: tableConfigId
		}, true, cb_success, cb_error);
	};

	this.renderPhotoTabs = function (treeId, tableConfigId) {
		$('#photo_tabs').tabs({
			onSelect: function (title, index) {
				var tab = $('#photo_tabs').tabs('getTab', index);
				var tableId = tab.panel('options').tableId;
				if (tableId == 'photo_summary_tab') {
					othis.postSummaryData(treeId, tableConfigId);
				}
			}
		});
	};

	//加载质量数据类型下拉框
	this.loadTypeSelect = function (treeId) {
		var cb_success = function (data) {
			if (data.array.length > 0) {
				$('#qualityPhotoType').combobox({
					data: data.array,
					valueField: 'name',
					textField: 'name',
					editable: false,
					width: 300,
					panelHeight: 400,
					onSelect: function (record) {
						othis.initTbr(record, treeId);
						othis.postTableData(record.id, treeId);
						othis.renderPhotoTabs(treeId, record.id);
						// othis.postSummaryData(treeId, record.id);
					}
				});
				$('#qualityPhotoType').combobox("select", data.array[0].name);
			} else {
				othis.showMsg("暂无质量影像记录！");
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {
			layer.alert("加载质量影像记录类型下拉框出错！", {
				icon: 2
			});
		};
		twxAjax("Thing.Fn.SecondTable", "GetQualityPhotoTypeByTreeId", {
			treeId: treeId
		}, false, cb_success, cb_error);
	};

	// this.init();
}