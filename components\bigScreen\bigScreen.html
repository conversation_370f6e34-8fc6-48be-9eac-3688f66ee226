<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
		<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" type="text/css" />
		<link rel="stylesheet" href="bigScreen.css" type="text/css" />
		<title>产品数据包数据看板</title>

		<script src="../../plugins/easyui/jquery.min.js"></script>

		<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
		<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

		<!-- <script src="../../plugins/easyui/jquery.jsPlumb-1.7.5.js"></script> -->
		<script src="../../plugins/jsPlumb/jsplumb.bundle.js"></script>

		<script src="../../plugins/echarts/echarts.min.js"></script>
		<script src="../../plugins/layui-lasted/layui.js"></script>

		<script src="../js/config/twxconfig.js"></script>
		<script src="../js/util.js"></script>
	</head>
	<body>
		<div class="header">
			产品数据包数据看板
		</div>
		<div class="content">
			<div class="content-left">
				<div class="content-left-top">
					<div class="content-left-top-card card">
						<div class="card-header">
							<div class="card-header-name">型号过程数据包统计</div>
							<div class="card-header-btn" id="refresh-btn" title="重新统计"><i class="layui-icon layui-icon-refresh"></i></div>
							<div class="card-header-time" id="update-time"></div>
						</div>
						<div class="card-body">
							<div class="num-col">
								<div class="num-row">
									<div class="num-icon">
										<img src="img/icon_statistics.png">
									</div>
									<div class="num-text">
										<div>型号数量统计</div>
										<div id="model-num"></div>
									</div>
								</div>
								<div class="num-row" id="process-row">
									<div class="num-icon">
										<img src="img/icon_record.png">
									</div>
									<div class="num-text">
										<div>过程记录</div>
										<div id="process-num"></div>
									</div>
								</div>
								<div class="num-row">
									<div class="num-icon">
										<img src="img/icon_quality.png">
									</div>
									<div class="num-text">
										<div>质量数据</div>
										<div id="quality-num"></div>
									</div>
								</div>
							</div>
							<div class="num-split">
								<div></div>
								<div></div>
								<div></div>
							</div>
							<div class="num-col">
								<div class="num-row" id="document-row">
									<div class="num-icon">
										<img src="img/icon_file.png">
									</div>
									<div class="num-text">
										<div>技术文件</div>
										<div id="document-num"></div>
									</div>
								</div>
								<div class="num-row">
									<div class="num-icon">
										<img src="img/icon_image.png">
									</div>
									<div class="num-text">
										<div>影像记录</div>
										<div id="photo-num"></div>
									</div>
								</div>
								<div class="num-row">
									<div class="num-icon">
										<img src="img/icon_entity.png">
									</div>
									<div class="num-text">
										<div>实体文件量级</div>
										<div id="file-num"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="content-left-bottom">
					<div class="content-left-bottom-card card">
						<div class="card-header">
							型号数据包采集数量TOP10
						</div>
						<div class="card-body">
							<div id="pie-chart" style="height: 240px;"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="content-center">
				<div class="model-select layui-form">
					<select id="model-select" name="model-select" lay-verify="" lay-search lay-filter="model-select">
						<option value="">所有型号</option>
					</select>
				</div>
				<div class="quality-data">
					<div>
						<canvas id="canvas">当前浏览器不支持canvas，请更换Chrome浏览器后再试</canvas>
					</div>
				</div>
			</div>
			<div class="content-right">
				<div class="content-right-top">
					<div class="content-right-top-card card">
						<div class="card-header">
							型号影像记录采集排行榜
						</div>
						<div class="card-body photo-card" id="photo-table">
							<div class="layui-row table-header">
								<div class="layui-col-md2">排序</div>
								<div class="layui-col-md3">型号</div>
								<div class="layui-col-md5">&nbsp;</div>
								<div class="layui-col-md2">影像记录数量</div>
							</div>
						</div>
					</div>
				</div>
				<div class="content-right-bottom">
					<div class="content-right-bottom-card">
						<div class="layui-carousel" id="carousel">
							<div carousel-item id="carousel-item">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="footer">
			<div class="footer-content" id="diagramContainer">
				<table></table>
			</div>
		</div>
	</body>
	<script src="SecondTable.js"></script>
	<script src="chartOption.js"></script>
	<script src="bigScreen.js"></script>
</html>