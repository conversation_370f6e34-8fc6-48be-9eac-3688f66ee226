/**
 * 技术状态更改单相关图表处理
 * <AUTHOR>
 * @date 2025年5月22日
 */

/**
 * 技术状态更改单柱状图提示格式化函数
 * @param {Object} params
 */
var changeOrderTooltipFormatter = function (params) {
    var value = params.value[params.dimensionNames.indexOf(params.seriesName)];
    if (!value || value === '0') {
        return '';
    }
    // 兼容渐变色和纯色
    var dotColor = (typeof params.color === 'object' && params.color.colorStops)
        ? params.color.colorStops[0].color
        : params.color;

    // 创建一个唯一ID，用于饼图容器
    var pieId = 'pie-chart-' + new Date().getTime();

    // 构建包含饼图的tooltip HTML
    var tooltipHtml = '<div style="padding:10px;background:rgba(255,255,255,0.95);border-radius:5px;box-shadow:0 0 10px rgba(0,0,0,0.1);">\
						<div style="font-size:15px;color:#333;font-weight:bold;margin-bottom:8px;border-bottom:1px solid #eee;padding-bottom:6px;">' +
        '<span style="display:inline-block;width:12px;height:12px;border-radius:50%;background:' + dotColor + ';margin-right:6px;vertical-align:middle;"></span>' +
        params.seriesName + '</div>\
						<div style="text-align:center;margin-bottom:10px;">\
							<span style="font-size:16px;color:#333;font-weight:bold;">' + params.name.split("~~~")[0] + '</span>\
							<span style="font-size:16px;color:#333;font-weight:bold;margin-left:5px;">' + value + '</span>\
						</div>\
						<div id="' + pieId + '" style="width:240px;height:180px;margin:0 auto;"></div>\
					</div>';

    // 使用setTimeout创建饼图（因为需要等待DOM元素创建完成）
    setTimeout(function () {
        var pieContainer = document.getElementById(pieId);
        if (!pieContainer) return;

        // 获取状态数据
        var finishedCount = 0;
        var unfinishedCount = 0;
        var total = parseInt(value);
        var legendTitle = '';
        var modelId = params.name.split("~~~")[1];
        var seriesName = params.seriesName;

        // 根据不同系列选择不同的数据和标题
        if (window.changeOrderStatusData) {
            if (seriesName === '总数量') {
                legendTitle = '完成情况';
                finishedCount = window.changeOrderStatusData.finished[modelId] || 0;
                unfinishedCount = window.changeOrderStatusData.unfinished[modelId] || 0;
                // 使用真实数据，不再生成随机数据
                // 如果没有数据，则使用0
            } else if (seriesName === '分支总数量') {
                legendTitle = '分支情况';
                finishedCount = window.changeOrderStatusData.branchFinished[modelId] || 0;
                unfinishedCount = window.changeOrderStatusData.branchUnfinished[modelId] || 0;
                // 使用真实数据，不再生成随机数据
                // 如果没有数据，则使用0
            } else {
                legendTitle = seriesName + '情况';
                if (window.changeOrderStatusData.situationStatus &&
                    window.changeOrderStatusData.situationStatus[seriesName]) {
                    finishedCount = window.changeOrderStatusData.situationStatus[seriesName].finished[modelId] || 0;
                    unfinishedCount = window.changeOrderStatusData.situationStatus[seriesName].unfinished[modelId] || 0;
                    // 使用真实数据，不再生成随机数据
                    // 如果没有数据，则使用0
                } else {
                    // 使用真实数据，不再生成随机数据
                    finishedCount = 0;
                    unfinishedCount = total;
                }
            }
        } else {
            // 使用真实数据，不再生成随机数据
            finishedCount = 0;
            unfinishedCount = total;
            legendTitle = seriesName === '总数量' ? '完成情况' : (seriesName === '分支总数量' ? '分支情况' : seriesName + '情况');
        }

        var finishedPercent = total > 0 ? Math.round((finishedCount / total) * 100) : 0;
        var unfinishedPercent = 100 - finishedPercent;

        var finishedLabel = '已完成';
        var unfinishedLabel = '未完成';

        var pieChart = echarts.init(pieContainer);
        pieChart.setOption({
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                formatter: '{b}: {c} ({d}%)',
                backgroundColor: 'rgba(255,255,255,0.8)',
                borderColor: '#ccc',
                borderWidth: 1,
                textStyle: {
                    color: '#333'
                }
            },
            legend: {
                orient: 'vertical',
                right: '0%',
                top: 'middle',
                itemWidth: 10,
                itemHeight: 10,
                itemGap: 10,
                textStyle: {
                    color: '#666',
                    fontSize: 12
                },
                formatter: function (name) {
                    if (name === finishedLabel) {
                        return name + ' ' + finishedCount + '(' + finishedPercent + '%)';
                    } else {
                        return name + ' ' + unfinishedCount + '(' + unfinishedPercent + '%)';
                    }
                },
                data: [finishedLabel, unfinishedLabel]
            },
            series: [{
                name: legendTitle,
                type: 'pie',
                radius: ['40%', '65%'],
                center: ['28%', '50%'],
                avoidLabelOverlap: true,
                itemStyle: {
                    borderRadius: 4,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: finishedCount, name: finishedLabel, itemStyle: { color: '#67C23A' } },
                    { value: unfinishedCount, name: unfinishedLabel, itemStyle: { color: '#F56C6C' } }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }],
            animation: true
        });
    }, 10);

    return tooltipHtml;
}

/**
 * 处理技术状态更改单柱状图点击事件
 * @param {Object} params 图表点击参数
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 * @param {String} seriesName 系列名称
 */
function changeOrderChartClick(params, startDate, endDate, seriesName) {
    // 获取型号ID
    var modelId, modelName;

    // 处理不同格式的name
    if (typeof params.name === 'string' && params.name.indexOf("~~~") !== -1) {
        // 旧格式：name包含~~~分隔符
        modelName = params.name.split("~~~")[0];
        modelId = params.name.split("~~~")[1];
    } else {
        // 新格式：直接使用name
        modelName = params.name;
        // 尝试从数据中获取modelId
        if (window.changeOrderStatusData && window.changeOrderStatusData.modelMap) {
            modelId = window.changeOrderStatusData.modelMap[modelName];
        }

        if (!modelId) {
            modelId = "unknown";
            return;
        }
    }

    // 定义初始情况筛选值
    var situation = "all";

    // 根据系列名称确定情况筛选条件
    if (seriesName !== '总数量' && seriesName !== '分支总数量') {
        situation = seriesName;
    }

    // 根据系列名称确定查询类型
    var queryType = "all"; // 默认值
    if (seriesName === '总数量') {
        queryType = "main"; // 仅查询主表
    } else if (seriesName === '分支总数量' || (seriesName !== '总数量' && seriesName !== '分支总数量')) {
        queryType = "branch"; // 查询主表关联分支表
    }

    // 构建文件类型字符串
    var fileType = modelName + ' - ' + seriesName;

    // 调用新的详情tab显示函数
    showChangeOrderDetailTab(modelId, startDate, endDate, fileType, situation, queryType);
}

/**
 * 显示技术状态更改单详细信息tab页
 * @param {String} modelId 模型ID
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 * @param {String} fileType 文件类型
 * @param {String} situation 情况筛选值
 * @param {String} queryType 查询类型
 */
function showChangeOrderDetailTab(modelId, startDate, endDate, fileType, situation, queryType) {
    var modelName = fileType.split(' - ')[0] || '技术状态更改单';
    var seriesName = fileType.split(' - ')[1] || '详情';

    // 存储当前fileType，用于后续筛选
    var curFileType = fileType;
    var currentStatus = 'all';
    var currentSituation = situation || 'all'; // 情况由点击的柱状图确定，不再变更
    var currentQueryType = queryType || 'all'; // 查询类型

    // 构建筛选按钮HTML - 只保留状态筛选和导出按钮
    var tableSelectHtml = '<div class="status-select-container layui-form" style="position:absolute;right:50px;top:10px;z-index:10;width:285px;">' +
        '<div class="layui-form-item" style="margin-bottom:0;">' +
        '<div class="layui-inline" style="margin-right:0;margin-bottom:0;">' +
        '<span style="color:#fff;font-weight:500;float:left;line-height:30px;margin-right:10px;">状态筛选：</span>' +
        '<div class="model-select" style="width:100px;float:left;">' +
        '<select id="table-status-select" name="table-status-select" lay-filter="status-select">' +
        '<option value="all" selected>全部</option>' +
        '<option value="finished">已完成</option>' +
        '<option value="unfinished">未完成</option>' +
        '</select>' +
        '</div>' +
        '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="export-table-btn" style="margin-left:10px;margin-top:0px;float:left;">' +
        '<i class="layui-icon layui-icon-export"></i> 导出数据' +
        '</button>' +
        '</div>' +
        '</div>' +
        '</div>';

    layer.tab({
        type: 1,
        tab: [{
            title: '详细表格',
            content: '<div id="tableContent" style="height: 620px;padding:15px 50px 50px 50px;position:relative;">' + tableSelectHtml + '<div style="margin-top:45px;"></div><table id="list-table"></table></div>'
        }],
        anim: false,
        openDuration: 200,
        skin: 'layui-layer-tab my-layer',
        isOutAnim: false,
        closeDuration: 200,
        closeBtn: 2,
        shadeClose: false,
        maxmin: false,
        resize: false,
        area: ['1500px', '770px'],
        success: function () {
            // 初始化表单组件
            form.render();

            // 根据查询类型定义表格列配置
            var cols = [];
            if (currentQueryType === "main") {
                // 仅查询主表时的列配置（隐藏分支相关列）
                cols = [[
                    { title: '序号', type: 'numbers', width: 70 },
                    { field: 'XH', title: '型号', width: 120 },
                    { field: 'YZJD', title: '研制阶段', width: 100 },
                    { field: 'GGDH', title: '更改单号' },
                    { field: 'ZDR', title: '制单人', width: 120 },
                    { field: 'ZDRQ', title: '制单日期', width: 120 },
                    {
                        field: 'STATUS', title: '状态', width: 100, templet: function (d) {
                            return d.STATUS === '已完成' ?
                                '<span class="layui-badge layui-bg-green">已完成</span>' :
                                '<span class="layui-badge layui-bg-orange">未完成</span>';
                        }
                    },
                    { field: 'CREATE_DATE', title: '创建日期', width: 120 },
                    {
                        title: '操作', width: 120, templet: function () {
                            return '<a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>';
                        }
                    }
                ]];
            } else {
                // 查询分支表时的完整列配置
                cols = [[
                    { title: '序号', type: 'numbers', width: 70 },
                    { field: 'XH', title: '型号', width: 120 },
                    { field: 'YZJD', title: '研制阶段', width: 100 },
                    { field: 'GGDH', title: '更改单号', width: 120 },
                    // { field: 'GYRY', title: '工艺人员', width: 120 },
                    // { field: 'GYS', title: '工艺师', width: 120 },
                    { field: 'FZ', title: '分支', width: 120 },
                    { field: 'JTQK', title: '具体情况' },
                    {
                        field: 'STATUS', title: '状态', width: 100, templet: function (d) {
                            return d.STATUS === '已完成' ?
                                '<span class="layui-badge layui-bg-green">已完成</span>' :
                                '<span class="layui-badge layui-bg-orange">未完成</span>';
                        }
                    },
                    { field: 'CREATE_DATE', title: '创建日期', width: 120 },
                    {
                        title: '操作', width: 120, templet: function () {
                            return '<a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>';
                        }
                    }
                ]];
            }

            // 初始化表格
            table.render({
                id: "list-table",
                elem: '#list-table',
                skin: "nob",
                url: getUrl(thing, 'QueryChangeOrderList', ""),
                where: {
                    treeId: modelId,
                    username: username,
                    startDate: startDate,
                    endDate: endDate,
                    status: currentStatus,
                    situation: currentSituation,
                    queryType: currentQueryType
                },
                page: {
                    layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                    groups: 1,
                    first: false,
                    last: false
                },
                cols: cols,
                even: true
            });

            // 监听表格工具条事件
            table.on('tool(list-table)', function (obj) {
                var data = obj.data;
                if (obj.event === 'detail') {
                    showChangeOrderDetail(data, currentQueryType);
                }
            });

            // 添加状态筛选下拉框选择事件
            form.on('select(status-select)', function (data) {
                currentStatus = data.value;
                // 重新加载表格数据
                table.reload('list-table', {
                    page: {
                        layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
                        groups: 1,
                        first: false,
                        last: false,
                        curr: 1
                    },
                    where: {
                        treeId: modelId,
                        username: username,
                        startDate: startDate,
                        endDate: endDate,
                        status: currentStatus,
                        situation: currentSituation,
                        queryType: currentQueryType
                    }
                });
            });

            // 添加导出按钮点击事件
            $('#export-table-btn').on('click', function () {
                // 显示loading
                var loading = layer.msg('正在导出数据，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 调用导出接口
                $.fileDownload(fileHandlerUrl + '/aitScreen/exportChangeOrderExcel', {
                    httpMethod: 'POST',
                    data: {
                        treeId: modelId,
                        username: username,
                        startDate: startDate,
                        endDate: endDate,
                        status: currentStatus,
                        situation: currentSituation,
                        queryType: currentQueryType
                    },
                    successCallback: function () {
                        layer.close(loading);
                        layer.msg('导出成功', { icon: 1 });
                    },
                    failCallback: function (responseHtml) {
                        layer.close(loading);
                        layer.msg('导出失败', { icon: 2 });
                    }
                });
            });
        }
    });
}

/**
 * 辅助函数：安全获取字段值
 * @param {*} value 字段值
 * @returns {String} 安全的字符串值
 */
function getSafeValue(value) {
    return value || '';
}

/**
 * 显示技术状态更改单详情
 * @param {Object} data 技术状态更改单数据
 * @param {String} queryType 查询类型
 */
function showChangeOrderDetail(data, queryType) {

    // 辅助函数：格式化日期
    function formatDate(dateStr) {
        if (!dateStr) return '';
        try {
            if (dateStr instanceof Date) {
                return dateFormat(dateStr, "yyyy-MM-dd");
            } else if (typeof dateStr === 'string') {
                return dateStr.indexOf('-') > -1 ? dateStr : '';
            }
            return dateStr;
        } catch (e) {
            return dateStr || '';
        }
    }

    // 定义CSS样式 - 基于jsztggd.html结构
    var formStyles = `
        <style>
            .aws-form-ux-container {
                font-family: "微软雅黑", Arial, sans-serif;
                background: #fff;
                padding: 10px;
            }
            .aws-form-ux-maintable {
                width: 100%;
                table-layout: fixed;
                border-collapse: collapse;
                margin: 0 auto;
            }
            .aws-form-ux-titlebg {
                background: #f5f5f5;
            }
            .aws-form-ux-header {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border: 1px solid #000;
                border-bottom: none;
                position: relative;
                margin-left: -1px;
            }
            .awsui-ux.table-striped {
                width: 100%;
                table-layout: fixed;
                border-collapse: collapse;
                padding: 0px;
            }
            .awsui-ux-title {
                background: #f9f9f9;
                font-weight: bold;
                text-align: center;
                padding: 8px;
                border: 1px solid #000;
                font-family: "微软雅黑";
                white-space: nowrap;
                vertical-align: middle;
            }
            .aws-form-ux-content {
                padding: 8px;
                border: 1px solid #000;
                vertical-align: middle;
                min-height: 20px;
            }
            .aws-form-ux-content.colspan_3 {
                padding: 12px 8px;
                min-height: 40px;
            }
            .sqdh {
                font-size: 14px;
                font-weight: normal;
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
            }
            /* 内嵌表格样式 - 无边框 */
            .inner-table {
                width: 100%;
                border-collapse: collapse;
                table-layout: fixed;
            }
            .inner-table td {
                border: none !important;
                padding: 4px;
                vertical-align: middle;
            }
            .inner-table .inner-title {
                text-align: center;
                font-size: 14px;
                font-weight: bold;
                padding: 4px;
                background: #f9f9f9;
                width: 16.66%;
            }
            .inner-table .inner-content {
                width: 16.67%;
            }
            /* 分支表格样式 */
            .branch-data-table {
                width: calc(100% + 1px);
                border-collapse: collapse;
                font-family: "微软雅黑", Arial, sans-serif;
            }
            .branch-data-table th {
                background: #f9f9f9;
                font-weight: bold;
                text-align: center;
                padding: 8px;
                border: 1px solid #000;
                font-size: 12px;
                white-space: nowrap;
            }
            .branch-data-table td {
                padding: 6px;
                border: 1px solid #000;
                text-align: center;
                font-size: 12px;
                max-width: 120px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .branch-data-table .btn-detail {
                background: #1890ff;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 11px;
            }
            .branch-data-table .btn-detail:hover {
                background: #40a9ff;
            }
            .branch-section-title {
                width: calc(100% - 20px);
                font-size: 16px;
                font-weight: bold;
                margin: 10px 0 0px 0;
                padding: 10px;
                background: #f5f5f5;
                border-width: 1px 1px 0 1px;
                text-align: center;
                border-style: solid;
                border-color: #000;
            }
            .no-branch-data {
                text-align: center;
                color: #999;
                padding: 20px;
                font-style: italic;
            }
            /* 强制列宽设置 - 4列布局（主表） */
            .awsui-ux.table-striped.main-table col:nth-child(1) {
                width: 12% !important;
            }
            .awsui-ux.table-striped.main-table col:nth-child(2) {
                width: 38% !important;
            }
            .awsui-ux.table-striped.main-table col:nth-child(3) {
                width: 12% !important;
            }
            .awsui-ux.table-striped.main-table col:nth-child(4) {
                width: 38% !important;
            }
            /* 强制列宽设置 - 6列布局（分支表） */
            .awsui-ux.table-striped.branch-table col:nth-child(1) {
                width: 10% !important;
            }
            .awsui-ux.table-striped.branch-table col:nth-child(2) {
                width: 23% !important;
            }
            .awsui-ux.table-striped.branch-table col:nth-child(3) {
                width: 10% !important;
            }
            .awsui-ux.table-striped.branch-table col:nth-child(4) {
                width: 23% !important;
            }
            .awsui-ux.table-striped.branch-table col:nth-child(5) {
                width: 10% !important;
            }
            .awsui-ux.table-striped.branch-table col:nth-child(6) {
                width: 24% !important;
            }
        </style>
    `;

    var detailContent = '';

    // 根据queryType构建不同的表单内容
    if (queryType === "main") {
        // 主表详情表单（已有实现）
        detailContent = formStyles +
            '<div class="aws-form-ux-container">' +
            '<table class="aws-form-ux-maintable">' +
            '<tbody>' +
            // 标题行
            '<tr class="aws-form-ux-titlebg">' +
            '<td class="aws-form-ux-header">' +
            '技术状态更改落实情况检查表' +
            '<div class="sqdh">单据编号：' + getSafeValue(data.BILLNO) + '</div>' +
            '</td>' +
            '</tr>' +
            // 表单内容区
            '<tr class="aws-form-ux-formcontent">' +
            '<td>' +
            '<table class="awsui-ux table-striped main-table" style="padding: 0px; table-layout: fixed; margin-left: -1px;" width="100%" cellspacing="3" cellpadding="0">' +
            '<colgroup>' +
            '<col class="awsui-ux-title" style="width: 12%;" />' +
            '<col style="width: 38%;" />' +
            '<col class="awsui-ux-title" style="width: 12%;" />' +
            '<col style="width: 38%;" />' +
            '</colgroup>' +
            '<tbody>' +
            // 第一行：单据编号、PDM文件链接
            '<tr>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="BILLNO">单据编号</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.BILLNO) + '</td>' +
            '<td class="awsui-ux-title"><label for="PDMFILELINK" class="aws-form-ux-label">PDM文件链接</label></td>' +
            '<td class="aws-form-ux-content">' + (data.PDMFILELINK ? '<a style="color: #1890ff; text-decoration: underline;" href="' + data.PDMFILELINK + '" target="_blank">点击查看</a>' : '') + '</td>' +
            '</tr>' +
            // 其他主表字段...（保持原有实现）
            '<tr>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="ZDRQ">制单日期</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.ZDRQ) + '</td>' +
            '<td class="awsui-ux-title"><label for="ZDR" class="aws-form-ux-label">制单人</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.ZDR) + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label for="XH" class="aws-form-ux-label">型号</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.XH) + '</td>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="YZJD">研制阶段</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.YZJD) + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label for="CPDH" class="aws-form-ux-label">产品代号</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.CPDH) + '</td>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="PCH_SL">批次号及数量</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.PCH_SL) + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label for="GGDH" class="aws-form-ux-label">更改单号</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.GGDH) + '</td>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="ZLGZQRRY">质量跟踪确认人员</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.ZLGZQRRY) + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label for="GGDFFBBH" class="aws-form-ux-label">更改单发放包编号</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.GGDFFBBH) + '</td>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="GGDFFBMC">更改单发放包名称</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.GGDFFBMC) + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label for="LZDW" class="aws-form-ux-label">来自单位</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.LZDW) + '</td>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="SJSMC">设计师</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.SJSMC) + '</td>' +
            '</tr>' +
            // 输入文件区域
            '<tr>' +
            '<td class="awsui-ux-title" rowspan="2">输入文件</td>' +
            '<td class="aws-form-ux-content" colspan="3">' +
            '<table class="inner-table">' +
            '<colgroup>' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '</colgroup>' +
            '<tbody>' +
            '<tr>' +
            '<td class="inner-title">更改前编号：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SRWJ_GGQ) + '</td>' +
            '<td class="inner-title">更改前名称：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SRWJ_GGQ_MC) + '</td>' +
            '<td class="inner-title">更改前版本：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SRWJ_GGQ_BB) + '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="aws-form-ux-content" colspan="3">' +
            '<table class="inner-table">' +
            '<colgroup>' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '</colgroup>' +
            '<tbody>' +
            '<tr>' +
            '<td class="inner-title">更改后编号：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SRWJ_GGH) + '</td>' +
            '<td class="inner-title">更改后名称：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SRWJ_GGH_MC) + '</td>' +
            '<td class="inner-title">更改后版本：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SRWJ_GGH_BB) + '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label for="GGXM" class="aws-form-ux-label">更改项目</label></td>' +
            '<td class="aws-form-ux-content" colspan="3">' + getSafeValue(data.GGXM) + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label class="aws-form-ux-label" for="QSR">签署人</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.QSR) + '</td>' +
            '<td class="awsui-ux-title"><label for="QSRQ" class="aws-form-ux-label">签署日期</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.QSRQ) + '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            // 分支信息区域
            '<div class="branch-section-title">关联分支信息</div>' +
            '<div id="branch-data-container">' +
            '<div class="no-branch-data">正在加载分支信息...</div>' +
            '</div>' +
            '</div>';
    } else {
        // 分支表详情表单
        detailContent = formStyles +
            '<div class="aws-form-ux-container">' +
            '<table class="aws-form-ux-maintable">' +
            '<tbody>' +
            // 标题行
            '<tr class="aws-form-ux-titlebg">' +
            '<td class="aws-form-ux-header">' +
            '技术状态更改单分支详情' +
            '<div class="sqdh">分支编号：' + getSafeValue(data.FZ) + '</div>' +
            '</td>' +
            '</tr>' +
            // 表单内容区
            '<tr class="aws-form-ux-formcontent">' +
            '<td>' +
            '<table class="awsui-ux table-striped branch-table" style="padding: 0px; table-layout: fixed;margin-left: -1px;" width="100%" cellspacing="3" cellpadding="0">' +
            '<colgroup>' +
            '<col class="awsui-ux-title" style="width: 10%;" />' +
            '<col style="width: 23%;" />' +
            '<col class="awsui-ux-title" style="width: 10%;" />' +
            '<col style="width: 23%;" />' +
            '<col class="awsui-ux-title" style="width: 10%;" />' +
            '<col style="width: 24%;" />' +
            '</colgroup>' +
            '<tbody>' +
            // 基本信息区域
            '<tr>' +
            '<td class="awsui-ux-title"><label for="FZ" class="aws-form-ux-label">分支编号</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.FZ) + '</td>' +
            '<td class="awsui-ux-title"><label for="YWBM" class="aws-form-ux-label">业务部门</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.YWBM) + '</td>' +
            '<td class="awsui-ux-title"><label for="JTQK" class="aws-form-ux-label">具体情况</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.JTQK) + '</td>' +
            '</tr>' +
            // 人员信息区域
            '<tr>' +
            '<td class="awsui-ux-title"><label for="GYRY" class="aws-form-ux-label">工艺人员</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.GYRY) + '</td>' +
            '<td class="awsui-ux-title"><label for="GYS" class="aws-form-ux-label">工艺师</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.GYS) + '</td>' +
            '<td class="awsui-ux-title"><label for="JYY" class="aws-form-ux-label">检验人员</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.JYY) + '</td>' +
            '</tr>' +
            // 生产文件区域 - 更改前
            '<tr>' +
            '<td class="awsui-ux-title" rowspan="2">生产文件</td>' +
            '<td class="aws-form-ux-content" colspan="5">' +
            '<table class="inner-table">' +
            '<colgroup>' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '</colgroup>' +
            '<tbody>' +
            '<tr>' +
            '<td class="inner-title">更改前编号：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SCWJ_GGQ) + '</td>' +
            '<td class="inner-title">更改前名称：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SCWJ_GGQ_MC) + '</td>' +
            '<td class="inner-title">更改前版本：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SCWJ_GGQ_BB) + '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</td>' +
            '</tr>' +
            // 生产文件区域 - 更改后
            '<tr>' +
            '<td class="aws-form-ux-content" colspan="5">' +
            '<table class="inner-table">' +
            '<colgroup>' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '<col style="width: 16.66%;" />' +
            '<col style="width: 16.67%;" />' +
            '</colgroup>' +
            '<tbody>' +
            '<tr>' +
            '<td class="inner-title">更改后编号：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SCWJ_GGH) + '</td>' +
            '<td class="inner-title">更改后名称：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SCWJ_GGH_MC) + '</td>' +
            '<td class="inner-title">更改后版本：</td>' +
            '<td class="inner-content">' + getSafeValue(data.SCWJ_GGH_BB) + '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</td>' +
            '</tr>' +
            // 其它说明1
            '<tr>' +
            '<td class="awsui-ux-title"><label for="QTSM1" class="aws-form-ux-label">其它说明1</label></td>' +
            '<td class="aws-form-ux-content" colspan="5">' + getSafeValue(data.QTSM1) + '</td>' +
            '</tr>' +
            // 主产品更改区域 - 在线
            '<tr>' +
            '<td class="awsui-ux-title"><label for="ZFCP_ZX_GGSL" class="aws-form-ux-label">主产品在线更改数量</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.ZFCP_ZX_GGSL) + '</td>' +
            '<td class="awsui-ux-title"><label for="ZFCP_ZX_GGJL" class="aws-form-ux-label">主产品在线更改结论</label></td>' +
            '<td class="aws-form-ux-content" colspan="3">' + getSafeValue(data.ZFCP_ZX_GGJL) + '</td>' +
            '</tr>' +
            // 主产品更改区域 - 库房
            '<tr>' +
            '<td class="awsui-ux-title"><label for="ZFCP_KF_GGSL" class="aws-form-ux-label">主产品库房更改数量</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.ZFCP_KF_GGSL) + '</td>' +
            '<td class="awsui-ux-title"><label for="ZFCP_KF_GGJL" class="aws-form-ux-label">主产品库房更改结论</label></td>' +
            '<td class="aws-form-ux-content" colspan="3">' + getSafeValue(data.ZFCP_KF_GGJL) + '</td>' +
            '</tr>' +
            // 主产品更改区域 - 已交
            '<tr>' +
            '<td class="awsui-ux-title"><label for="ZFCP_YJ_GGSL" class="aws-form-ux-label">主产品已交更改数量</label></td>' +
            '<td class="aws-form-ux-content">' + getSafeValue(data.ZFCP_YJ_GGSL) + '</td>' +
            '<td class="awsui-ux-title"><label for="ZFCP_YJ_GGJL" class="aws-form-ux-label">主产品已交更改结论</label></td>' +
            '<td class="aws-form-ux-content" colspan="3">' + getSafeValue(data.ZFCP_YJ_GGJL) + '</td>' +
            '</tr>' +
            // 元器件更改区域
            '<tr>' +
            '<td class="awsui-ux-title"><label for="YQJ_GGQ" class="aws-form-ux-label">元器件（更改前）</label></td>' +
            '<td class="aws-form-ux-content" colspan="2">' + getSafeValue(data.YQJ_GGQ) + '</td>' +
            '<td class="awsui-ux-title"><label for="YQJ_GGH" class="aws-form-ux-label">元器件（更改后）</label></td>' +
            '<td class="aws-form-ux-content" colspan="2">' + getSafeValue(data.YQJ_GGH) + '</td>' +
            '</tr>' +
            // 其它说明2
            '<tr>' +
            '<td class="awsui-ux-title"><label for="QTSM2" class="aws-form-ux-label">其它说明2</label></td>' +
            '<td class="aws-form-ux-content" colspan="5">' + getSafeValue(data.QTSM2) + '</td>' +
            '</tr>' +
            // 确认签字区域
            '<tr>' +
            '<td class="awsui-ux-title"><label for="YYBMQR" class="aws-form-ux-label">业务部门确认</label></td>' +
            '<td class="aws-form-ux-content" colspan="5">' + getSafeValue(data.YYBMQR) + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td class="awsui-ux-title"><label for="ZLSQR" class="aws-form-ux-label">质量师确认</label></td>' +
            '<td class="aws-form-ux-content" colspan="5">' + getSafeValue(data.ZLSQR) + '</td>' +
            '</tr>' +
            // 移交人员区域
            '<tr>' +
            '<td class="awsui-ux-title"><label for="YJRY_GY" class="aws-form-ux-label">工艺移交人员</label></td>' +
            '<td class="aws-form-ux-content" colspan="2">' + getSafeValue(data.YJRY_GY) + '</td>' +
            '<td class="awsui-ux-title"><label for="YJRY_JY" class="aws-form-ux-label">检验移交人员</label></td>' +
            '<td class="aws-form-ux-content" colspan="2">' + getSafeValue(data.YJRY_JY) + '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</div>';
    }

    // 显示弹窗
    var layerIndex = layer.open({
        type: 1,
        title: queryType === "main" ? '技术状态更改落实情况检查表详情' : '技术状态更改单分支详情',
        area: ['1400px', '900px'],
        content: detailContent,
        maxmin: true,
        scrollbar: true,
        success: function () {
            // 如果是主表详情，异步加载分支数据
            if (queryType === "main") {
                loadBranchData(data.BINDID);
            }
        }
    });
}

/**
 * 加载分支数据并构建表格
 * @param {String} bindId 绑定ID
 */
function loadBranchData(bindId) {
    if (!bindId) {
        document.getElementById('branch-data-container').innerHTML =
            '<div class="no-branch-data">无法获取分支信息：缺少绑定ID</div>';
        return;
    }

    // 查询分支数据
    twxAjax(thing, 'QueryChangeOrderList', {
        username: username,
        treeId: -1,
        startDate: '',
        endDate: '',
        status: 'all',
        situation: 'all',
        queryType: 'branch',
        isAllData: true
    }, true, function (res) {
        if (res.code === 0) {
            // 过滤出当前BINDID的分支数据
            var branches = res.data.filter(function (item) {
                return item.BINDID === bindId;
            });

            buildBranchTable(branches);
        } else {
            document.getElementById('branch-data-container').innerHTML =
                '<div class="no-branch-data">查询分支信息失败：' + (res.msg || '未知错误') + '</div>';
        }
    }, function (error) {
        document.getElementById('branch-data-container').innerHTML =
            '<div class="no-branch-data">查询分支信息出错，请稍后重试</div>';
    });
}

/**
 * 构建分支数据表格
 * @param {Array} branches 分支数据数组
 */
function buildBranchTable(branches) {
    var container = document.getElementById('branch-data-container');

    if (!branches || branches.length === 0) {
        container.innerHTML = '<div class="no-branch-data">暂无分支信息</div>';
        return;
    }

    // 构建表格HTML
    var tableHtml = '<table class="branch-data-table">' +
        '<thead>' +
        '<tr>' +
        '<th style="width: 5%;">序号</th>' +
        '<th style="width: 8%;">分支编号</th>' +
        '<th style="width: 10%;">业务部门</th>' +
        '<th style="width: 8%;">工艺人员</th>' +
        '<th style="width: 12%;">具体情况</th>' +
        '<th style="width: 10%;">更改前编号</th>' +
        '<th style="width: 10%;">更改前名称</th>' +
        '<th style="width: 8%;">更改前版本</th>' +
        '<th style="width: 10%;">更改后编号</th>' +
        '<th style="width: 10%;">更改后名称</th>' +
        '<th style="width: 8%;">更改后版本</th>' +
        '<th style="width: 12%;">其他说明</th>' +
        '<th style="width: 8%;">工艺师</th>' +
        '<th style="width: 8%;">操作</th>' +
        '</tr>' +
        '</thead>' +
        '<tbody>';

    // 构建数据行
    for (var i = 0; i < branches.length; i++) {
        var branch = branches[i];
        var rowIndex = i + 1;

        tableHtml += '<tr>' +
            '<td>' + rowIndex + '</td>' +
            '<td title="' + getSafeValue(branch.FZ) + '">' + getSafeValue(branch.FZ) + '</td>' +
            '<td title="' + getSafeValue(branch.YWBM) + '">' + getSafeValue(branch.YWBM) + '</td>' +
            '<td title="' + getSafeValue(branch.GYRY) + '">' + getSafeValue(branch.GYRY) + '</td>' +
            '<td title="' + getSafeValue(branch.JTQK) + '">' + getSafeValue(branch.JTQK) + '</td>' +
            '<td title="' + getSafeValue(branch.SCWJ_GGQ) + '">' + getSafeValue(branch.SCWJ_GGQ) + '</td>' +
            '<td title="' + getSafeValue(branch.SCWJ_GGQ_MC) + '">' + getSafeValue(branch.SCWJ_GGQ_MC) + '</td>' +
            '<td title="' + getSafeValue(branch.SCWJ_GGQ_BB) + '">' + getSafeValue(branch.SCWJ_GGQ_BB) + '</td>' +
            '<td title="' + getSafeValue(branch.SCWJ_GGH) + '">' + getSafeValue(branch.SCWJ_GGH) + '</td>' +
            '<td title="' + getSafeValue(branch.SCWJ_GGH_MC) + '">' + getSafeValue(branch.SCWJ_GGH_MC) + '</td>' +
            '<td title="' + getSafeValue(branch.SCWJ_GGH_BB) + '">' + getSafeValue(branch.SCWJ_GGH_BB) + '</td>' +
            '<td title="' + getSafeValue(branch.QTSM1) + '">' + getSafeValue(branch.QTSM1) + '</td>' +
            '<td title="' + getSafeValue(branch.GYS) + '">' + getSafeValue(branch.GYS) + '</td>' +
            '<td><button class="btn-detail" onclick="showBranchDetail(' + i + ')">查看详情</button></td>' +
            '</tr>';
    }

    tableHtml += '</tbody></table>';

    // 更新容器内容
    container.innerHTML = tableHtml;

    // 保存分支数据到全局变量，供详情查看使用
    window.currentBranchData = branches;
}

/**
 * 显示分支详情
 * @param {Number} index 分支数据索引
 */
function showBranchDetail(index) {
    if (window.currentBranchData && window.currentBranchData[index]) {
        var branchData = window.currentBranchData[index];
        showChangeOrderDetail(branchData, "branch");
    }
}

/**
 * 显示单据的所有分支详情
 * @param {String} billNo 单据编号
 * @param {Number} parentLayer 父级弹窗索引
 */
function showAllBranchesDetail(billNo, parentLayer) {
    // 查询该单据的所有分支信息
    twxAjax(thing, 'QueryChangeOrderList', {
        username: username,
        treeId: -1,
        startDate: '',
        endDate: '',
        status: 'all',
        situation: 'all',
        queryType: 'branch',
        isAllData: true
    }, true, function (res) {
        if (res.code === 0) {
            // 过滤出当前单据的分支数据
            var branches = res.data.filter(function (item) {
                return item.BILLNO === billNo;
            });

            var branchContent = '<div style="padding: 20px;">' +
                '<h3 style="margin-bottom: 15px;">单据【' + billNo + '】的所有分支详情</h3>';

            if (branches.length > 0) {
                branchContent += '<div style="max-height: 400px; overflow-y: auto;">';
                for (var i = 0; i < branches.length; i++) {
                    var branch = branches[i];
                    branchContent += '<div style="margin-bottom: 15px; border: 1px solid #e6e6e6; padding: 10px; border-radius: 4px;">' +
                        '<h4 style="margin: 0 0 10px 0; color: #333;">分支 ' + (i + 1) + '</h4>' +
                        '<table class="layui-table">' +
                        '<tr><td style="width:120px;">分支编号：</td><td>' + (branch.FZ || '无') + '</td></tr>' +
                        '<tr><td>具体情况：</td><td>' + (branch.JTQK || '无') + '</td></tr>' +
                        '<tr><td>状态：</td><td>' + branch.STATUS + '</td></tr>' +
                        '</table>' +
                        '</div>';
                }
                branchContent += '</div>';
            } else {
                branchContent += '<p style="text-align: center; color: #999; padding: 20px;">该单据暂无分支信息</p>';
            }

            branchContent += '</div>';

            layer.open({
                type: 1,
                title: '所有分支详情',
                area: ['700px', '600px'],
                content: branchContent
            });
        } else {
            layer.msg('查询分支信息失败', { icon: 2 });
        }
    }, function (error) {
        layer.msg('查询分支信息出错', { icon: 2 });
    });
}

/**
 * 加载技术状态更改单柱状图
 * @param {String} modelId 模型ID
 * @param {String} startDate 开始日期
 * @param {String} endDate 结束日期
 */
function loadChangeOrderChart(modelId, startDate, endDate) {
    var chartDom = document.getElementById('jszt-chart');
    if (!chartDom) {
        return;
    }

    // 确保图表容器有足够的高度
    if (chartDom.offsetHeight < 50) {
        chartDom.style.height = '300px';
    }

    var myChart = echarts.init(chartDom);
    myChart.showLoading("default", loadingOption);

    twxAjax(thing, 'QueryChangeOrderCount', {
        username: username,
        treeId: modelId,
        startDate: startDate,
        endDate: endDate
    }, true, function (res) {
        if (res.success) {
            myChart.hideLoading();
            var option = res.data.option;

            // 保存状态数据到全局变量，供提示框使用
            window.changeOrderStatusData = res.data.statusData;

            // 设置图例formatter，显示总数
            option.legend.formatter = function (name) {
                var count = res.data.total[name] || 0;
                return name + ' (' + count + ')';
            };

            // 设置图例项间距，缩小换行后的间距
            option.legend.itemGap = 6;

            // 设置提示框格式化函数
            option.tooltip.formatter = changeOrderTooltipFormatter;

            // 设置X轴标签格式化函数
            option.xAxis.axisLabel.formatter = xAxisLabelFormatter;

            // 确保图表配置完整
            if (!option.series || option.series.length === 0) {
                return;
            }

            if (!option.dataset || !option.dataset.source || option.dataset.source.length === 0) {
                return;
            }

            // 渲染图表
            try {
                myChart.setOption(option, true); // 添加第二个参数true，强制清除之前的图表
            } catch (error) {
            }

            // 绑定点击事件
            myChart.off('click');
            myChart.on('click', function (params) {
                changeOrderChartClick(params, startDate, endDate, params.seriesName);
            });
        } else {
            myChart.hideLoading();
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (error) {
        myChart.hideLoading();
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}
