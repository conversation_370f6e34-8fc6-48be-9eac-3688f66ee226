var sysdicThingName = "Thing.Fn.SystemDic";
var dictionaryData = [];
var dictionaryDataData = [];
$(document).ready(function() {
	layui.use(['layer', 'form', 'jquery'], function() {
		initTableComp(layui);
		//初始化加载数据
		loadDictionaryTable();
		//初始化数据字典 新增 按钮
		initAddDictionary(layui);
		initEditDictionary(layui);
		initDicDataAdd(layui);
		initDicDataEdit(layui);

		initDicDelete(layui);
		initDicDataDelete(layui);
		formVerify(layui);
	});
});

//初始化表格
var initTableComp = function(layui) {
	//初始化字典分类表格
	renderDictionaryTable([]);
	//初始化字典数据表格
	renderDataTable([]);
};

var formVerify = function(layui) {
	layui.form.verify({
		dictionaryNameRepeat: function(value, item) {
			var oldName = $(item).attr('oldname');
			var flag = true;
			for (var i = 0; i < dictionaryData.length; i++) {
				var d = dictionaryData[i];
				if (value == d.NAME) {
					flag = false;
				}
			}
			if (value == oldName) {
				flag = true;
			}
			if (!flag) {
				return '该字典已存在！';
			}
		},
		dictionaryDataNameRepeat: function(value, item) {
			var oldName = $(item).attr('oldname');
			var flag = true;
			for (var i = 0; i < dictionaryDataData.length; i++) {
				var d = dictionaryDataData[i];
				if (value == d.NAME) {
					flag = false;
				}
			}
			if (value == oldName) {
				flag = true;
			}
			if (!flag) {
				return '该名称已存在！';
			}
		}
	});
}
//新增字典按钮
var initAddDictionary = function(layui) {
	$('#dic_add').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		//监听提交
		form.on('submit(addDictionarySubmit)', function(data) {
			var param = data.field;
			var name = param.NAME;
			var cb_success = function(data) {
				if (data.success === false) {
					logRecord('新增', '数据字典-新增字典(名称：' + name + '、描述：'+param.REMARK+')', 0);
					layer.msg('新增失败!');
					return;
				}
				loadDictionaryTable();
				logRecord('新增', '数据字典-新增字典(名称：' + name + '、描述：'+param.REMARK+')', 1);
				layer.msg('新增成功');
				layer.closeAll();
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {};
			//同步新增
			twxAjax(sysdicThingName, "addDictionary", param, true, cb_success, cb_error);

			return false;
		});

		layer.open({
			title: '新增字典',
			type: 1,
			shadeClose: false,
			maxmin: false,
			resize: false, //不允许拉伸
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			area: ['360px', '280px'],
			content: '<div id="addDictionaryContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['新增', '重置', '关闭'],
			yes: function() {
				$('#btn_dic_submit').click();
			},
			btn2: function() {
				$('#btn_dic_reset').click();
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				var addTpl = $("#addDictionaryHtml")[0].innerHTML;
				$("#addDictionaryContent").append(addTpl);

				form.render(null, 'addDictionaryForm');
			}
		});

	});
};

var getDicSelectedData = function() {
	var sels = $('#dictionaryTable').datagrid('getSelections');
	return sels;
};

//编辑数据字典按钮
var initEditDictionary = function(layui) {

	$('#dic_edit').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		//获取选中的数据
		var datas = getDicSelectedData();
		if (datas.length == 0) {
			layer.msg('请选择待编辑数据!');
			return;
		} else {
			if (datas[0].NAME == '系统配置') {
				layer.msg('系统配置不可编辑！')
				return;
			}
		}

		layer.closeAll();

		//监听提交
		form.on('submit(editDictionarySubmit)', function(data) {
			var param = data.field;
			var name = param.NAME;
			var remark = param.REMARK;
			var oldName = $("#dictionaryName").attr('oldname');
			var oldRemark = $("#dictionaryName").attr('oldremark');
			param.ID = datas[0].ID;
			var cb_success = function(data) {

				if (data.success === false) {
					logRecord('编辑', '数据字典-(ID：' + datas[0].ID + '、名称：' + oldName + '、备注：' + (oldRemark==undefined ? '':oldRemark) + ')更新为(名称：' + name + '、备注：' +
						remark + ')', 0);
					layer.msg(data.message);
					return;
				}

				layer.closeAll();
				loadDictionaryTable();
				logRecord('编辑', '数据字典-(ID：' + datas[0].ID + '、名称：' + oldName + '、备注：' + (oldRemark==undefined ? '':oldRemark) + ')更新为(名称：' + name + '、备注：' +
					remark + ')', 1);
				layer.msg('编辑成功');
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {};
			//同步新增
			twxAjax(sysdicThingName, "editDictionary", param, true, cb_success, cb_error);

			return false;
		});

		layer.open({
			title: '编辑字典',
			type: 1,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			area: ['360px', '280px'],
			content: '<div id="editDictionaryContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['保存', '重置', '关闭'],
			yes: function() {
				$('#btn_dic_update').click();
			},
			btn2: function() {
				$('#btn_dic_reset').click();
				form.val("addDictionaryForm", datas[0]);
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				var editTpl = $("#addDictionaryHtml")[0].innerHTML;
				$("#editDictionaryContent").append(editTpl);

				form.render(null, 'addDictionaryForm');
				form.val("addDictionaryForm", datas[0]);
				$("#dictionaryName").attr("oldname", datas[0].NAME);
				$("#dictionaryName").attr("oldremark", datas[0].REMARK);
			}
		});
	});
};

//新增字典数据按钮
var initDicDataAdd = function(layui) {
	$('#data_add').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		//监听提交
		form.on('submit(addDataSubmit)', function(data) {
			var param = data.field;
			param.PID = selectDictionaryRow.ID;
			var name = param.NAME;
			var cb_success = function(data) {
				if (data.success === false) {
					logRecord('新增', '数据字典-字典(ID：' + selectDictionaryRow.ID + '、名称：' + selectDictionaryRow.NAME + ')下新增数据(名称：' + name +
						'、值：' + param.KEY + '、描述：' + param.REMARK + ')', 0);
					layer.msg(data.message);
					return;
				}
				layer.closeAll();
				loadDataTable();
				logRecord('新增', '数据字典-字典(ID：' + selectDictionaryRow.ID + '、名称：' + selectDictionaryRow.NAME + ')下新增数据(名称：' + name +
					'、值：' + param.KEY + '、描述：' + param.REMARK + ')', 1);
				layer.msg('新增成功');
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {};
			//同步新增
			twxAjax(sysdicThingName, "addDictionaryData", param, true, cb_success, cb_error);

			return false;
		});

		layer.open({
			title: '新增数据',
			type: 1,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			area: ['360px', '335px'],
			content: '<div id="addDataContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['新增', '重置', '关闭'],
			yes: function() {
				$('#btn_dicdata_submit').click();
			},
			btn2: function() {
				$('#btn_dicdata_reset').click();
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				var addTpl = $("#addDataHtml")[0].innerHTML;
				$("#addDataContent").append(addTpl);
				form.render(null, 'addDataForm');
			}
		});

	});
};

var getDicDataSelectedData = function() {
	var sels = $('#dataTable').datagrid('getSelections');
	return sels;
};

//编辑字典数据按钮
var initDicDataEdit = function(layui) {
	$('#data_edit').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;

		//获取选中的数据
		var datas = getDicDataSelectedData();
		if (datas.length == 0) {
			layer.msg('请选择待编辑数据!');
			return;
		}

		layer.open({
			title: '编辑数据',
			type: 1,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			area: ['360px', '335px'],
			content: '<div id="editDataContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['保存', '重置', '关闭'],
			yes: function() {
				$('#btn_dicdata_update').click();
			},
			btn2: function() {
				$('#btn_dicdata_reset').click();
				form.val("addDataForm", datas[0]);
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				var editTpl = $("#addDataHtml")[0].innerHTML;
				$("#editDataContent").append(editTpl);

				form.render(null, 'addDataForm');
				form.val("addDataForm", datas[0]);
				$("#dataName").attr("oldname", datas[0].NAME);
				if (selectDictionaryRow.NAME == '系统配置') {
					$("#dataName").attr("readOnly", "readOnly");
				}
			}
		});

		//监听提交
		form.on('submit(editDataSubmit)', function(data) {
			var param = data.field;
			var name = param.NAME;
			param.ID = datas[0].ID;
			var cb_success = function(data) {
				if (data.success === false) {
					logRecord('编辑', '数据字典-字典(ID：' + selectDictionaryRow.ID + '、名称：' + selectDictionaryRow.NAME + ')下的数据(ID：' + datas[
						0].ID + '、名称：' + datas[0].NAME + '、值：' + (datas[0].KEY == undefined ? '' : datas[0].KEY) + '、备注：' + (datas[0]
					.REMARK == undefined ? '' : datas[0].REMARK) +
					')更新为(名称：' + name + '、值：' + param.KEY + '、备注：' +
					param.REMARK + ')', 0);
					layer.msg(data.message);
					return;
				}
				layer.closeAll();
				loadDataTable();
				logRecord('编辑', '数据字典-字典(ID：' + selectDictionaryRow.ID + '、名称：' + selectDictionaryRow.NAME + ')下的数据(ID：' + datas[
						0].ID + '、名称：' + datas[0].NAME + '、值：' + (datas[0].KEY == undefined ? '' : datas[0].KEY) + '、备注：' + (datas[0]
					.REMARK == undefined ? '' : datas[0].REMARK) +
					')更新为(名称：' + name + '、值：' + param.KEY + '、备注：' +
					param.REMARK + ')', 1);
				layer.msg('编辑成功');
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {};
			//同步新增
			twxAjax(sysdicThingName, "editDictionaryData", param, true, cb_success, cb_error);
			return false;
		});
	});
};

var selectDictionaryRow = {};
//初始化字典表
function renderDictionaryTable(data) {
	$('#dictionaryTable').datagrid({
		data: data,
		fitColumns: true,
		singleSelect: true,
		fit: true,
		toolbar: '#dictionaryTable_tb',
		columns: [
			[{
					field: 'ID',
					hidden: true
				}, {
					field: 'NAME',
					title: '名称',
					width: 100,
					align: 'center'
				},
				{
					field: 'REMARK',
					title: '描述',
					width: 200,
					align: 'align'
				}
			]
		],
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>数据加载中...</font></div>',
		loadingMessage: '正在加载数据...',
		rownumbers: true,
		striped: true,
		onClickRow: function(index, row) {
			selectDictionaryRow = row;
			loadDataTable();
		}
	});
}

//初始化字典数据表
function renderDataTable(data) {
	$('#dataTable').datagrid({
		data: data,
		fitColumns: true,
		singleSelect: true,
		fit: true,
		toolbar: '#dataTable_tb',
		columns: [
			[{
					field: 'ID',
					hidden: true
				}, {
					field: 'PID',
					hidden: true
				}, {
					field: 'NAME',
					title: '名称',
					width: 100,
					align: 'center'
				}, {
					field: 'KEY',
					title: '值',
					width: 100,
					align: 'align'
				},
				{
					field: 'REMARK',
					title: '描述',
					width: 200,
					align: 'center'
				}
			]
		],
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>请切换数据分类...</font></div>',
		loadingMessage: '正在加载数据...',
		rownumbers: true,
		striped: true
	});
}

//加载字典表格
function loadDictionaryTable() {
	$('#dictionaryTable').datagrid('loading');
	twxAjax(sysdicThingName, 'getDictionary', '', true, function(data) {
		//renderDictionaryTable(data.rows);
		dictionaryData = data.rows;
		$('#dictionaryTable').datagrid('loadData', data.rows);
		$('#dictionaryTable').datagrid('loaded');
	});
}

//加载字典数据表格
function loadDataTable() {
	$('#dataTable').datagrid('loading');
	twxAjax(sysdicThingName, 'getDictionaryData', {
		PID: selectDictionaryRow.ID
	}, true, function(data) {
		//renderDataTable(data.rows);
		dictionaryDataData = data.rows;
		$('#dataTable').datagrid('loadData', data.rows);
		$('#dataTable').datagrid('loaded');
	});
}


//删除字典
function initDicDelete(layui) {
	$('#dic_del').bind('click', function() {
		var layer = layui.layer;
		//获取选中的数据
		var datas = getDicSelectedData();
		if (datas.length == 0) {
			layer.msg('请选择一条数据!');
			return;
		} else {
			if (datas[0].NAME == '系统配置') {
				layer.msg('系统配置不可删除！')
				return;
			} else {
				if (dictionaryDataData.length > 0) {
					layer.msg('该字典下有数据，不可删除！');
					return;
				}
			}
		}
		var name = datas[0].NAME;
		layer.confirm("确认删除字典" + name + "吗？", {
			icon: 3,
			title: '提示'
		}, function(index) {
			var cb_success = function(data) {
				if (data.success === false) {
					logRecord('删除', '数据字典-删除字典(ID：' + datas[0].ID + '、名称：' + name + ')', 0);
					layer.msg(data.message);
					return;
				}
				layer.closeAll();
				loadDictionaryTable();
				logRecord('删除', '数据字典-删除字典(ID：' + datas[0].ID + '、名称：' + name + ')', 1);
				layer.msg('删除成功');
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {};
			//向服务端发送删除指令
			twxAjax(sysdicThingName, "deleteDictionary", {
				ID: datas[0].ID
			}, true, cb_success, cb_error);
		});
	});
}
//删除字典数据
function initDicDataDelete() {
	$('#data_del').bind('click', function() {
		var layer = layui.layer;
		//获取选中的数据
		var datas = $('#dataTable').datagrid('getSelections');
		if (datas.length == 0) {
			layer.msg('请选择一条数据!');
			return;
		} else {
			if (selectDictionaryRow.NAME == '系统配置') {
				layer.msg('系统配置项不可删除！');
				return;
			}
		}
		var name = datas[0].NAME;
		layer.confirm("确认删除数据" + name + "吗？", {
			icon: 3,
			title: '提示'
		}, function(index) {
			var cb_success = function(data) {
				if (data.success === false) {
					logRecord('删除', '数据字典-删除字典(ID：' + selectDictionaryRow.ID + '、名称：' + selectDictionaryRow.NAME + ')下的数据(ID：' + datas[
						0].ID + '、名称：' + datas[0].NAME + ')', 0);
					layer.msg(data.message);
					return;
				}
				layer.closeAll();
				loadDataTable();
				logRecord('删除', '数据字典-删除字典(ID：' + selectDictionaryRow.ID + '、名称：' + selectDictionaryRow.NAME + ')下的数据(ID：' + datas[
					0].ID + '、名称：' + datas[0].NAME + ')', 1);
				layer.msg('删除成功');
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {};
			//向服务端发送删除指令
			twxAjax(sysdicThingName, "deleteDictionaryData", {
				ID: datas[0].ID
			}, true, cb_success, cb_error);
		});
	});
}
