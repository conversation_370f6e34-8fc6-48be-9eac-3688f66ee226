/**
 * PDF导出弹窗模块
 * 提供PDF导出设置弹窗功能，包括纸张大小和页面方向选择
 */
var PdfExportDialog = (function () {
    // 默认配置选项
    var defaultOptions = {
        pageSize: 'A4',
        pageOrientation: 'landscape'
    };

    // 可用纸张大小
    var availablePageSizes = ['A4', 'A3', 'A2', 'A1'];

    /**
     * 显示PDF导出设置弹窗
     * @param {Object} [customOptions] - 自定义配置选项
     * @param {Function} callback - 用户确认后的回调函数，参数为用户选择的设置
     * @param {Boolean} [isDirectoryUpdate=false] - 是否为目录更新功能
     * @param {String} [dialogTitle] - 自定义对话框标题，为空时根据isDirectoryUpdate判断
     * @param {String} [confirmButtonText] - 自定义确认按钮文字，为空时根据isDirectoryUpdate判断
     */
    function showPdfExportDialog(customOptions, callback, isDirectoryUpdate, dialogTitle, confirmButtonText) {
        // 处理参数
        if (typeof customOptions === 'function') {
            isDirectoryUpdate = callback;
            callback = customOptions;
            customOptions = {};
        }

        // 默认为PDF导出功能
        isDirectoryUpdate = !!isDirectoryUpdate;

        // 合并默认选项和自定义选项
        var options = $.extend({}, defaultOptions, customOptions);

        // 判断是否已加载layui
        if (!layui || !layui.layer || !layui.form) {
            console.error('PdfExportDialog: layui模块未加载');
            return;
        }

        var layer = layui.layer;
        var form = layui.form;

        // 构建纸张大小选项
        var pageSizeOptions = '';
        $.each(availablePageSizes, function(index, size) {
            var selected = size === options.pageSize ? 'selected' : '';
            pageSizeOptions += '<option value="' + size + '" ' + selected + '>' + size + '</option>';
        });

        // 构建弹窗内容
        var content = '<div style="padding: 20px;">' +
            '<form class="layui-form" action="" lay-filter="pdfExportForm">' +
                '<div class="layui-form-item">' +
                    '<label class="layui-form-label">纸张大小</label>' +
                    '<div class="layui-input-block">' +
                        '<select name="pageSize" lay-verify="required">' +
                            pageSizeOptions +
                        '</select>' +
                    '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                    '<label class="layui-form-label">页面方向</label>' +
                    '<div class="layui-input-block">' +
                        '<input type="radio" name="pageOrientation" value="landscape" title="横向" ' + (options.pageOrientation === 'landscape' ? 'checked' : '') + '>' +
                        '<input type="radio" name="pageOrientation" value="portrait" title="纵向" ' + (options.pageOrientation === 'portrait' ? 'checked' : '') + '>' +
                    '</div>' +
                '</div>' +
                '<div class="layui-form-item" style="display:none;">' +
                    '<button type="submit" class="layui-btn" lay-submit lay-filter="pdfExportSubmit">导出</button>' +
                '</div>' +
            '</form>' +
        '</div>';

        // 根据功能设置不同的标题和按钮文字
        if (!dialogTitle) {
            dialogTitle = isDirectoryUpdate ? '更新目录页码' : 'PDF导出设置';
        }
        if (!confirmButtonText) {
            confirmButtonText = isDirectoryUpdate ? '开始更新' : '导出';
        }

        // 打开弹窗
        layer.open({
            type: 1,
            title: dialogTitle,
            area: ['400px', '300px'],
            content: content,
            btn: [confirmButtonText, '取消'],
            yes: function(index) {
                // 触发表单提交
                $('button[lay-filter="pdfExportSubmit"]').click();
            },
            success: function() {
                // 重新渲染表单
                form.render(null, 'pdfExportForm');

                // 监听表单提交
                form.on('submit(pdfExportSubmit)', function(data) {
                    // 关闭弹窗
                    layer.closeAll();

                    // 调用回调函数，传递选项
                    if (typeof callback === 'function') {
                        callback(data.field);
                    }

                    return false;
                });
            }
        });
    }

    // 暴露公共API
    return {
        showDialog: showPdfExportDialog,
        // 提供专门用于目录更新的对话框方法
        showDirectoryUpdateDialog: function(customOptions, callback) {
            // 调用showDialog方法，并明确指定标题和按钮文字
            showPdfExportDialog(customOptions, callback, true, '更新目录页码', '开始更新');
        },
        // 提供获取默认配置的方法
        getDefaultOptions: function() {
            return $.extend({}, defaultOptions);
        },
        // 提供设置默认配置的方法
        setDefaultOptions: function(options) {
            defaultOptions = $.extend({}, defaultOptions, options);
        },
        // 提供添加新纸张大小的方法
        addPageSize: function(size) {
            if (size && !availablePageSizes.includes(size)) {
                availablePageSizes.push(size);
            }
        }
    };
})();