//产品结构树的对象
var BomTree = function() {
	var othis = this;
	this.ztreeObj = {};
	this.treeId = 'bomTree';
	this.treeSetting = bomTreeUtil.treeSetting;
	//加载树结构
	this.loadTree = function() {
		var cb_success = function(data) {
			var datas = data.rows;
			if (datas.length > 0) {
				datas = bomTreeUtil.dealDataIcons(datas);
				othis.treeSetting.callback.onClick = function(event, treeId, treeNode) {
					if (treeNode.TYPE == 'Product') {
						productId = treeNode.CODE;
						productName = treeNode.NAME;
						// searchTable();
					} else {
						productId = '-1';
						productName = '';
					}
				};
				othis.treeSetting.callback.onExpand = function(event, treeId, treeNode) {

				};

				othis.ztreeObj = $.fn.zTree.init($("#" + othis.treeId), othis.treeSetting, datas);
				var nodes = othis.ztreeObj.getNodes();
				for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
					othis.ztreeObj.expandNode(nodes[i], true, false, true);
				}
			}
		};
		//使用ajax进行异步加载Tree
		twxAjax('Thing.Fn.BOM', 'QueryBomTreeRoot', '', true, cb_success);
	};
	this.loadTree();
};

//质量数据表
var Table = function() {
	var othis = this;
	this.tableId = 'table';
	this.columns = [
		[{
				field: 'ck',
				checkbox: true
			}, {
				field: 'NAME',
				title: '名称',
				width: 150,
				align: 'center'
			},
			{
				field: 'NUMBER',
				title: '编号',
				width: 100,
				align: 'center'
			},
			{
				field: 'MODEL',
				title: '所属型号',
				width: 100,
				align: 'center'
			},
			{
				field: 'MODEL',
				title: '编制者',
				width: 100,
				align: 'center'
			}
		]
	];
	this.renderTable = function() {
		$('#' + othis.tableId).datagrid({
			data: [],
			toolbar: "#tb",
			fit: true,
			columns: othis.columns,
			emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>',
			// pagination: true,
			loadMsg: '正在加载数据...',
			rownumbers: true,
			striped: true
		});
	};
	this.initPagination = function(data) {

	};

	this.initBtn = function() {
		$("#push").click(function() {
			alert('push');
		});
		$("#confirm").click(function() {
			alert('confirm');
		});
		$("#generateReport").click(function() {
			alert('generateReport');
		});
		$("#viewReport").click(function() {
			alert('viewReport');
		});
	};
	this.initBtn();
	this.renderTable();
}
$(function() {
	var bomTree = new BomTree();
	var table = new Table();
});
