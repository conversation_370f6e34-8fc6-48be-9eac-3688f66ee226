/**
 * HandsonTableUtil.js - Handsontable工具类
 *
 * 版本升级记录：
 * - 2025-07-11: 升级适配Handsontable 16.0.1版本
 *   * 增强错误处理机制，提高稳定性
 *   * 优化Handsontable配置，利用16.0.1性能改进
 *   * 改进乐观锁检查和数据保存的错误处理
 *   * 增加参数验证和异常捕获
 *   * 保持ES5语法兼容性
 *
 * <AUTHOR>
 * @version 16.0.1-compatible
 */
var HotUtil = {
	/**
	 * 当前节点的工作类型
	 */
	workType: '',
	workTypes: [],
	postTypes: [],
	currentTreeNodeId: '',
	/**
	 * 当前表格的保存时间，用于乐观锁检查
	 */
	currentSaveTime: '',
	initWorkTypes: function () {
		twxAjax("Thing.Fn.SystemDic", 'getDictionaryDataByName', {
			name: '工时工作类型'
		}, true, function (res) {
			HotUtil.workTypes = res.rows;
		});
		twxAjax("Thing.Fn.SystemDic", 'getDictionaryDataByName', {
			name: '工时岗位'
		}, true, function (res) {
			HotUtil.postTypes = res.rows;
		});
	},
	injectSecurity: function () {
		twxAjax("Thing.Fn.SystemDic", 'getDictionaryDataByName', {
			name: '密级'
		}, true, function (res) {
			window.securitys = res.rows;
		}, function (xhr, textStatus, errorThrown) {
			// layer.alert('请求密级出错！', {
			// 	icon: 2
			// });
		});
	},
	getSecurityName: function (securityKey) {
		var securityName = "";
		for (var i = 0; i < securitys.length; i++) {
			if (securityKey == securitys[i].KEY) {
				securityName = securitys[i].NAME;
				break;
			}
		}
		return securityName;
	},
	lockRow: function (tr, treeNode) {
		var contextMenuTd = void 0;
		tr.find("td:first").contextMenu({
			width: 110,
			menu: [{
				text: "锁定该行",
				icon: '../dataTree/images/lock.png',
				callback: function () {
					HotUtil.openEdit(treeNode, 0, function () {
						HotUtil.updateLockRow(contextMenuTd, treeNode, 'lock');
					});
				}
			}],
			target: function (ele) {
				contextMenuTd = ele;
			}
		});
	},
	/**
	 * 更新锁定单元格的信息
	 * @param {Object} tds 选中的单元格信息
	 * @param {Object} id 确认表的id
	 * @param {Object} type 是锁定行还是解锁行 lock or unlock
	 */
	uploadLockTds: function (tds, treeNode, type, successFn) {
		var log = {};
		if (type == "lock") {
			log.operation = "锁定";
		} else {
			log.operation = "解锁";
		}
		log.operation = log.operation + "行";
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;
		var row = tds[0].row + 1;
		log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中" + log.operation + "了第" + row + "行";
		var cb_success = function (res) {
			if (res.success) {
				log.reqResult = 1;
				successFn(res.msg);
			} else {
				log.reqResult = 0;
				layer.alert(res.msg, {
					icon: 2
				});
			}
			addConfirmLog(log);
		}
		var cb_error = function (err) {
			layer.alert('请求失败', {
				icon: 2
			});
		}
		twxAjax(THING, "UpdateLockRow", {
			id: treeNode.ID,
			lockRow: JSON.stringify(tds),
			userName: sessionStorage.getItem('username'),
			type: type
		}, true, cb_success, cb_error);
	},
	/**
	 * 更新锁定行的信息
	 * @param {Object} contextMenuTd 右键的单元格
	 * @param {Object} treeNode
	 * @param {Object} type 是锁定行还是解锁行 lock or unlock
	 */
	updateLockRow: function (contextMenuTd, treeNode, type) {
		// 在锁定行操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, type == "lock" ? "锁定行" : "解锁行", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '设置失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			var msg = "";
			if (type == 'lock') {
				msg = "锁定之后不可编辑，是否继续？";
			} else {
				msg = "解除锁定之后会清除签名图片，并且不可恢复，是否继续？";
			}
			layer.confirm(msg, {
				icon: 3,
				title: '提示'
			}, function (index) {
				var tds = [];
				contextMenuTd.parent().find("td").each(function (i, n) {
					tds.push({
						row: parseInt($(n).attr("row")),
						col: parseInt($(n).attr("col"))
					});
				});

				HotUtil.uploadLockTds(tds, treeNode, type, function (msg) {
					layer.msg(msg);
					reloadTable(treeNode);
				});
			});
		});
	},
	/**
	 * 获取表头的开始值和结束值
	 */
	getTableHeader: function (tableHeader) {
		var min = 0;
		var max = 0;
		if (tableHeader.indexOf("-") > -1) {
			min = Number(tableHeader.split("-")[0]);
			max = Number(tableHeader.split("-")[1]);
		} else {
			max = Number(tableHeader);
		}
		return {
			min: min,
			max: max
		};
	},
	/**
	 * 判断当前节点是否是pdf节点
	 */
	isPdf: function (treeNode) {
		var isPdf = false;
		if ((treeNode.TYPE == 'b' || treeNode.TYPE.indexOf('table') > -1) && (treeNode.FILE_FORMAT == 'pdf' ||
			treeNode.FILE_FORMAT == 'PDF')) {
			isPdf = true;
		}
		return isPdf;
	},
	loadHtmlTable: function (res, treeNode, tableSel, otherHeight, pageType, scrollObj) {
		if (res.success) {
			tableSel.empty();
			var html = res.data.HTML_DATA || "";
			// if (res.data.SIGN_HTML && res.data.TABLE_STATUS == 'sign') {
			// 	treeNode.SIGN_HTML = res.data.SIGN_HTML;
			// 	html = res.data.SIGN_HTML;
			// }
			//更新node节点的数据
			treeNode.HTML_DATA = html;
			treeNode.SAVE_DATA = res.data.SAVE_DATA || "";
			treeNode.TABLE_STATUS = res.data.TABLE_STATUS || "";
			treeNode.SAVE_TIME = res.data.SAVE_TIME || "";
			treeNode.SECURITY = res.data.SECURITY;
			treeNode.FILE_PATH = res.data.FILE_PATH;
			treeNode.FILE_FORMAT = res.data.FILE_FORMAT;
			treeNode.ATTACHMENT = res.data.ATTACHMENT || "";
			// 记录当前表格的保存时间，用于乐观锁检查
			HotUtil.currentSaveTime = treeNode.SAVE_TIME;
			HotUtil.currentTreeNodeId = treeNode.ID;
			ztreeObj.updateNode(treeNode);
			initTbr(treeNode);
			HotUtil.workType = '';
			if (treeNode['SAVE_DATA']) {
				HotUtil.workType = JSON.parse(treeNode['SAVE_DATA']).workType || '';
			}
			$(".table-security").text(HotUtil.getSecurityName(treeNode.SECURITY));
			if (HotUtil.isPdf(treeNode)) {
				tableSel.css({
					"padding": "0px 0px",
				});
				tableSel.append('<iframe id="iframe"></iframe>');
				var ifHeight = tableSel.parent().height() - $("#tbr").height() - 5;
				if (treeNode.TYPE.indexOf('table') > -1) {
					ifHeight = ifHeight - 40;
				}
				$("#iframe").css({
					"height": ifHeight + "px",
					"border": "none",
					"width": "100%"
				});
				$("#iframe").attr("src", "/File" + treeNode.FILE_PATH);
			} else {
				if (html != "") {
					//添加表格
					var $table = $(html.replaceAll("\n", "<br>"));
					HotUtil.delTableImg($table, res.data.IS_ELECTRIC_TEST === 1);
					//设置表格的class 区别显示的表格 表示为存储数据的表格 因为他们的class都有 layui-table
					$table.addClass('data-table').show();
					tableSel.css({
						"height": "auto",
						"padding": "0px 15px",
						"overflow": "visible"
					});

					if (res.data.TABLE_HEADER != "0" && res.data.TABLE_HEADER != undefined && res.data
						.TABLE_HEADER != "" && res.data.TABLE_HEADER != "0-0") {
						//如果存在表头的话 固定表头显示
						var $thead = $('<thead class="sticky-thead"></thead>');
						var top = "-1px";
						if (treeNode.TYPE == 'report' || treeNode.TYPE.indexOf('table') > -1) {
							// top = "-11px";
						}
						$thead.css({
							"top": top,
						});

						var tableHeader = HotUtil.getTableHeader(res.data.TABLE_HEADER);
						//处理表头的显示
						$table.find("tr").each(function (i, n) {
							if (i <= (tableHeader.max - 1) && i >= (tableHeader.min - 1)) {
								// $(n).css({
								// 	"font-weight": "bold",
								// 	"background-color": "#e6e6e6"
								// });
								$thead.append($(n).clone(true));
								$(n).remove();
							}
						});
						$table.prepend($thead);
					}
					var colWidths = JSON.parse(treeNode.SAVE_DATA).colWidths || [];
					if (colWidths.length > 0) {
						var $colgroup = $('<colgroup></colgroup>');
						for (var i = 0; i < colWidths.length; i++) {
							$colgroup.append('<col width="' + colWidths[i] + '">');
						}
						$table.prepend($colgroup);
					}
					tableSel.append($table);

					// 在表格锁定状态下隐藏签署框样式
					if (treeNode.TABLE_STATUS === 'sign') {
						$table.find('td.sign-box').removeClass('sign-box');
					}

					tableSel.find(".layui-table tbody tr:hover").css("background-color", "");
					$table.find("tr").each(function (i, n) {
						HotUtil.trContextMenu($(n), treeNode, tableSel, pageType);
					});

					if (treeNode.TABLE_STATUS == 'sign') {
						$(".layui-table td").each(function (i, n) {
							HotUtil.tdContextSignMenu(n, treeNode, tableSel, false, pageType);
						});
					}
					$table.find("td").each(function (i, n) {
						var comment = $(n).attr("comment");
						if (comment) {
							$(n).mouseover(function () {
								layer.tips(comment, this);
							});

							$(n).mouseout(function () {
								layer.closeAll();
							});
						}
					});
					HotUtil.tableAddLink(treeNode, $table);
				} else {
					tableSel.append('<span style="color:red;"> 请先编辑表格！</span>');
				}
			}
		} else {
			tableSel.hide();
			$("#msg").text(res.msg).show();
		}
	},
	/**
	 * 处理表格中图片的显示
	 *
	 * @param isShowThumbnail 是否显示缩略图
	 */
	delTableImg: function ($table, isShowThumbnail) {
		$table.find("td").each(function (i, n) {
			var $td = $(n);
			if ($td.find("img[type='photo']").length > 0) {
				var imgs = [];
				var photoShowNums = [];
				$td.find("img[type='photo']").each(function (j, m) {
					var $img = $(m);
					var photoPath = $img.attr("src");
					var photoName = $img.attr("photoName");
					var photoShowNum = $img.attr("photoShowNum");
					var photoId = $img.attr("id");
					var photoformat = $img.attr("photoformat");
					var imgData = {
						photoPath: photoPath,
						photoName: photoName,
						photoShowNum: photoShowNum,
						photoId: photoId,
						photoformat: photoformat
					};
					imgs.push(imgData);
					photoShowNums.push(photoShowNum);

					//删除元素
					var $br1 = $img.prev();
					var $br2 = $img.next();
					var $span = $br2.next();
					if (!isShowThumbnail) {
						$br1.remove();
						$br2.remove();
						$span.remove();
						$img.remove();
					} else {
						$br2.remove();
						$span.remove();
					}
				});
				$td.data("imgs", imgs);
				$td.data("hasImg", true);
				if (!isShowThumbnail) {
					var numText = HotUtil.dealImgNumShow(photoShowNums);
					$td.append(numText);
				}
			} else {
				$td.data("hasImg", false);
			}
		});
	},
	/**
	 * 处理图片编号的显示
	 * @param {Object} photoShowNums
	 */
	dealImgNumShow: function (photoShowNums) {
		//表a1-12
		var tableNum = photoShowNums[0].substr(0, photoShowNums[0].lastIndexOf('-'));
		if (tableNum) {
			tableNum = tableNum + "-";
		}

		function getNum(photoShowNum) {
			//表a1-12-图8
			var tempArr = photoShowNum.split("-");
			var num = parseInt(tempArr[tempArr.length - 1].substr(1));
			return num;
		}

		var arr = [];

		for (var i = 0; i < photoShowNums.length; i++) {
			var photoShowNum = photoShowNums[i];
			var num = getNum(photoShowNum);
			arr.push(num);
		}


		// 定义一个新数组用于存储结果
		var newArr = [];

		// 使用for循环遍历原始数组
		for (var i = 0; i < arr.length; i++) {
			// 定义一个临时数组用于存储连续数字
			var tempArr = [arr[i]];

			// 使用while循环查找连续数字
			while (arr[i + 1] - arr[i] === 1) {
				tempArr.push(arr[i + 1]);
				i++;
			}
			// 将结果推入新数组
			newArr.push(tempArr);
		}

		var textArr = [""];

		for (var i = 0; i < newArr.length; i++) {
			var arr = newArr[i];
			if (arr.length == 1) {
				textArr.push(tableNum + "图" + arr[0]);
			} else {
				textArr.push(tableNum + "图" + arr[0] + "~" + "图" + arr[arr.length - 1]);
			}
		}
		var span = '<span style="color:red;">' + textArr.join("<br>") + '</span>'
		return span;
	},
	//表格增加跳转连接
	tableAddLink: function (treeNode, $table) {
		//如果表为A表 则需要在表中显示B表的超链接 根据B表的表序号来识别
		if (treeNode.TYPE == 'a' || treeNode.TYPE == 'report') {
			//首先查询型号下的所有表的信息
			var cb_success = function (res) {
				if (res.success) {
					var bNodes = res.data;
					for (var i = 0; i < bNodes.length; i++) {
						var bNode = bNodes[i];
						$table.find("td").each(function (j, td) {
							if ($(td).text().indexOf(bNode.TABLE_NUM) > -1) {
								var locationArr = [];
								$(td).find(".location-text").each(function (x, span) {
									locationArr.push({
										id: $(span).attr("refid"),
										num: $(span).text()
									});
									$(span).text('');
								});
								var spanHtml =
									'<span class="location-text" style="color:blue;cursor:pointer;text-decoration:underline"  refid="' +
									bNode.ID + '">' + bNode.TABLE_NUM + '</span>';
								$(td).html($(td).html().replaceAll(bNode.TABLE_NUM, spanHtml));
								for (var x = 0; x < locationArr.length; x++) {
									$(td).find('span[refid=' + locationArr[x].id + ']').text(
										locationArr[x].num);
								}
							}
						});
					}
					$(".location-text").click(function (e) {
						HotUtil.locationTreeNode($(e.target).attr("refid"));
					});
				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}
			//请求失败的回调
			var cb_error = function (xhr, textStatus, errorThrown) {

			};
			var service = "QueryModelNodesById";
			if (treeNode.TYPE == 'report') {
				service = 'QueryProcessNodesById';
			}
			twxAjax(THING, service, {
				id: treeNode.ID
			}, true, cb_success, cb_error);
		}
	},
	/**
	 * 定位在线确认树节点
	 * @param {*} nodeId
	 */
	locationTreeNode: function (nodeId) {
		var cb_success = function (res) {
			if (res.success) {
				locationTreeNodeUtil(res.data, ztreeObj, 'ID', function (thisNode) {
					loadTreeMenu();
					reloadTable(thisNode);
				});
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}
		//请求失败的回调
		var cb_error = function (xhr, textStatus, errorThrown) {

		};
		twxAjax(THING, "QueryAllPId", {
			id: nodeId
		}, true, cb_success, cb_error);
	},
	scrollEle: function (scrollObj, parentSelector) {
		if (scrollObj && scrollObj.isScroll) {
			var $scrollEle = parentSelector.find(' td[row="' + scrollObj.row + '"][col="' + scrollObj.col +
				'"]');
			parentSelector.animate({
				scrollTop: $scrollEle.offset().top - 400
			}, 500);
		}
	},
	//表格行的右键菜单
	trContextMenu: function ($newTr, treeNode, tableSel, pageType) {
		var trIsHasLockTd = false;
		//处理锁定单元格的显示
		$newTr.find("td").each(function (j, m) {
			if ($(m).attr("lock") == "true") {
				trIsHasLockTd = true;
				$(m).addClass("htDimmed");
				if (j == 0) {
					HotUtil.tdContextSignMenu(m, treeNode, tableSel, true, pageType);
				} else {
					HotUtil.tdContextSignMenu(m, treeNode, tableSel, false, pageType);
				}
			} else {
				HotUtil.tdContextEditMenu(m, treeNode, tableSel, false, pageType);
			}
		})
		if (!trIsHasLockTd) {
			//没有锁定的话
			$newTr.find("td").each(function (j, m) {
				if (j == 0) {
					HotUtil.tdContextEditMenu(m, treeNode, tableSel, true, pageType);
				} else {
					HotUtil.tdContextEditMenu(m, treeNode, tableSel, false, pageType);
				}
			})
		}
	},
	/**
	 * 检查此客户端是否允许拍照
	 */
	isAllowTakePhoto: function () {
		var flag = false;
		try {
			if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator.webkitGetUserMedia ||
				navigator
					.mozGetUserMedia) {
				flag = true;
			}
		} catch (e) {

		}
		return flag;
	},
	/**
	 * 锁定之前的右键菜单
	 * @param {Object} n td元素
	 * @param {Object} treeNode 树节点
	 * @param {Object} tableSel 显示表格的页面元素选择器
	 * @param {Object} islockRow 是否锁定此行
	 * @param {Object} pageType 页面类型 （发射场： launch） （产品 ：confirm） （ait质量 ：report）
	 */
	tdContextEditMenu: function (n, treeNode, tableSel, islockRow, pageType) {
		var contextEle = void 0;
		var menus = [];
		if (islockRow) {
			menus.push({
				text: "锁定该行",
				icon: '../dataTree/images/lock.png',
				callback: function () {
					HotUtil.openEdit(treeNode, 0, function () {
						HotUtil.updateLockRow(contextEle, treeNode, 'lock');
					});
				}
			});
		}
		menus.push({
			text: "上传图片",
			icon: '../dataTree/images/upload-image.png',
			callback: function () {
				HotUtil.openEdit(treeNode, 0, function () {
					HotUtil.insertImage(treeNode, contextEle, tableSel);
				});
			}
		});
		if (HotUtil.isAllowTakePhoto()) {
			menus.push({
				text: "拍照",
				icon: '../dataTree/images/拍照.png',
				callback: function () {
					HotUtil.takePhoto(treeNode, contextEle, tableSel);
				}
			});
		}
		if ($(n).data("hasImg")) {
			menus.push({
				text: "查看图片",
				icon: '../dataTree/images/view.png',
				callback: function () {
					HotUtil.viewImage(treeNode, contextEle, tableSel, true);
				}
			});
		}
		$(n).contextMenu({
			width: 110,
			menu: menus,
			target: function (ele) {
				contextEle = ele;
			}
		});
	},

	/**
	 * 锁定之后的右键菜单
	 * @param {Object} n td元素
	 * @param {Object} treeNode 树节点
	 * @param {Object} tableSel 显示表格的页面元素选择器
	 * @param {Object} isUnlockRow 是否解锁此行
	 * @param {Object} pageType 页面类型 （发射场： launch） （产品 ：confirm） （ait质量 ：report）
	 */
	tdContextSignMenu: function (n, treeNode, tableSel, isUnlockRow, pageType) {
		var contextEle = void 0;
		var menus = [];
		if (isUnlockRow && contains(sessionStorage.getItem('funcids').split(','), 'func-' + pageType +
			'-unlock-row')) {
			menus.push({
				text: "解锁此行",
				icon: '../dataTree/images/unlock.png',
				callback: function () {
					HotUtil.updateLockRow(contextEle, treeNode, 'unlock');
				}
			});
		}
		menus = menus.concat([{
			text: "鼠标签名",
			icon: '../dataTree/images/sign.png',
			callback: function () {
				HotUtil.sign(treeNode, contextEle, tableSel);
			}
		}, {
			text: "手写板签名",
			icon: '../dataTree/images/sign.png',
			callback: function () {
				HotUtil.wacomSign(treeNode, contextEle, tableSel);
			}
		}, {
			text: "上传签章",
			icon: '../dataTree/images/sign1.png',
			callback: function () {
				HotUtil.uploadSign(treeNode, contextEle, tableSel);
			}
		}]);
		if ($(n).data("hasImg")) {
			menus.push({
				text: "查看图片",
				icon: '../dataTree/images/view.png',
				callback: function () {
					HotUtil.viewImage(treeNode, contextEle, tableSel, false);
				}
			});
		}
		$(n).contextMenu({
			width: 110,
			menu: menus,
			target: function (ele) {
				contextEle = ele;
			}
		});
	},
	base64Upload: function (dataURL, uploadURL, successFn) {
		var loadIndex = layer.load(); //上传loading
		// 解码Base64字符串
		var base64Data = dataURL.split(',')[1];
		var binaryData = atob(base64Data);

		// 将二进制数据创建为Blob对象
		var blobData = new Blob([new Uint8Array(binaryData.length).map(function (_, i) {
			return binaryData.charCodeAt(i);
		})], {
			type: 'image/bmp'
		});

		// 创建一个FormData对象并将Blob对象附加到其中
		var formData = new FormData();
		formData.append('file', blobData, 'image.bmp'); // 'file'是服务器接受文件的字段名， 'image.bmp'是上传的文件名

		// 发送AJAX请求
		var xhr = new XMLHttpRequest();
		xhr.open('POST', uploadURL, true); // 上传到服务器的URL
		xhr.onreadystatechange = function () {

			if (xhr.readyState === 4) {
				if (xhr.status === 200) {
					layer.close(loadIndex);
					successFn(JSON.parse(xhr.responseText).data.filePath);
				} else {
					// 处理上传失败的回调
					layer.alert("图片上传失败", {
						icon: 2
					});
				}
			}
		};

		xhr.send(formData);
	},
	takePhoto: function (treeNode, ele, tableSel) {
		ele = HotUtil.findDataTableEle(ele);
		var mediaStreamTrack = null;
		var isTake = false;
		var photoWidth = 960;
		var photoHeight = 600;
		layer.open({
			title: false,
			closeBtn: 0,
			type: 1,
			area: [photoWidth + 'px', (photoHeight + 68) + 'px'],
			content: '<div id="take-photo-content"></div>',
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			btn: ['拍照', '重拍', '上传', '取消'],
			yes: function () {
				var canvas = document.getElementById('canvas');
				$("#video").hide();
				$("#canvas").show();
				var context = canvas.getContext('2d');
				context.drawImage(video, 0, 0, photoWidth, photoHeight);
				isTake = true;
			},
			btn2: function () {
				$("#video").show();
				$("#canvas").hide();
				isTake = false;
				return false;
			},
			btn3: function () {
				var dataUrl = document.getElementById('canvas').toDataURL();
				if (isTake) {
					HotUtil.base64Upload(dataUrl, fileHandlerUrl + '/file/upload', function (filePath) {
						HotUtil.updateTableSign(filePath, ele, tableSel, treeNode, "photo")
						mediaStreamTrack.stop();
						$("#video").remove();
					});
				} else {
					layer.alert('请先拍照！', {
						icon: 2
					});
				}
				return false;
			},
			btn4: function () {
				try {
					mediaStreamTrack.stop();
				} catch (e) { }

				$("#video").remove();
				return true;
			},
			success: function () {
				var tpl = '<video id="video" width="' + photoWidth + '" height="' + photoHeight + '"  autoplay="autoplay">\
							</video>\
							<canvas id="canvas" width="' + photoWidth + '" height="' + photoHeight + '" style="display:none" ></canvas>';
				$("#take-photo-content").append(tpl);
				var video = document.getElementById('video');
				if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator
					.webkitGetUserMedia || navigator
						.mozGetUserMedia) {

					//访问用户媒体设备的兼容方法
					function getUserMedia(constraints, success, error) {
						try {
							if (navigator.mediaDevices.getUserMedia) {
								//最新的标准API
								navigator.mediaDevices.getUserMedia(constraints).then(success)
									.catch(error);
							} else if (navigator.webkitGetUserMedia) {
								//webkit核心浏览器
								navigator.webkitGetUserMedia(constraints, success, error)
							} else if (navigator.mozGetUserMedia) {
								//firfox浏览器
								navigator.mozGetUserMedia(constraints, success, error);
							} else if (navigator.getUserMedia) {
								//旧版API
								navigator.getUserMedia(constraints, success, error);
							}
						} catch (e) {
							//TODO handle the exception
						}
					}

					function renderMedia(type, errorFn) {
						//默认后置摄像头
						var facingMode = {
							exact: "environment"
						};
						//前置摄像头
						if (type == "user") {
							facingMode = "user";
						}
						getUserMedia({
							video: {
								width: photoWidth,
								height: photoHeight,
								facingMode: facingMode
							}
						}, function (stream) {
							//兼容webkit核心浏览器
							var CompatibleURL = window.URL || window.webkitURL;
							//将视频流设置为video元素的源
							console.log(stream);
							mediaStreamTrack = stream.getTracks()[0];
							//video.src = CompatibleURL.createObjectURL(stream);
							video.srcObject = stream;
							video.play();
						}, function (error) {
							errorFn(error);
						});
					}

					//调用用户媒体设备, 访问摄像头
					renderMedia("environment", function (error) {
						renderMedia("user", function (error) {
							$("#video").remove();
							$("#take-photo-content").append(
								'<div style="color:red;padding:20px;font-size:20px">访问用户媒体设备失败,请检查是否连接摄像头设备！</div>'
							);
							console.log(
								`访问用户媒体设备失败${error.name}, ${error.message}`);
						});
					});
				} else {
					alert('不支持访问用户媒体');
				}
			}
		});
	},
	/**
	 * 弹出上传签章的窗口
	 * @param {Object} uploadSuccessFn 上传成功之后的回调
	 */
	uploadSignLayer: function (uploadSuccessFn) {
		if (device.ie && device.ie < 10) {
			layer.alert("请使用chrome浏览器！", {
				icon: 2
			});
		} else {
			var isAdm = false;
			var layerHeight = "460px";
			if (sessionStorage.getItem('username') == 'adm' || sessionStorage.getItem('username') == 'admin' || sessionStorage.getItem('username') == 'huaxiaodong') {
				isAdm = true;
				layerHeight = "510px";
			}

			var layerIndex = layer.open({
				title: "上传签章",
				type: 1,
				area: ['400px', layerHeight],
				content: '<div id="uploadSignContent" style="padding: 15px 0px 0px 0px;"></div>',
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				resize: false,
				btn: ['确定', '取消'],
				yes: function () {
					var signSrc = $("#previewImg").attr("src");
					if (signSrc) {
						$("#uploadStart").click();
					} else {
						layer.alert("请选择签章图片！", {
							icon: 2
						});
					}
				},
				btn2: function () {
					return true;
				},
				success: function () {
					var signDateDiv = "";
					if (isAdm) {
						signDateDiv = '<div class="layui-form-item">\
									<label class="fieldlabel layui-form-label">签署日期:</label>\
									<div class="layui-input-block">\
										<input type="text" class="layui-input" id="sign-date" style="width:274px;">\
									</div>\
								</div>';
					}
					var tpl = '<form class="layui-form" lay-filter="uploadSignForm">' +
						signDateDiv + '<div class="layui-form-item">\
									<label class="fieldlabel layui-form-label">文件内容:</label>\
									<div class="layui-input-block">\
										<div class="layui-upload">\
                                             <div id="uploadChoice">选择文件</div>											\
                                             <button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
										</div>\
									</div>\
								</div>\
								<div class="layui-form-item selectedFile" style="display: none;">\
									<label class="fieldlabel layui-form-label">已选文件:</label>\
									<div class="layui-input-block">\
										<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
									</div>\
								</div>\
								<div class="layui-form-item selectedFile" style="display: none;">\
									<label class="fieldlabel layui-form-label">签名用户:</label>\
									<div class="layui-input-block">\
									    <input type="text" class="layui-input" id="sign-name" style="width:274px;">\
									</div>\
								</div>\
								<div class="layui-form-item" id="previewFile" style="display: none;">\
									<label class="fieldlabel layui-form-label">签章预览:</label>\
									<div class="layui-input-block">\
										<img style="max-width: 285px;max-height: 150px;" id="previewImg" />\
									</div>\
								</div>\
								<div class="layui-form-item" style="display:none;">\
									<center>\
										<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
										<button id="btn_cancel" class="layui-btn">取消</button>\
									</center>\
								</div>\
							</form>';
					$("#uploadSignContent").append(tpl);
					form.render(null, 'uploadSignForm');
					laydate.render({
						elem: '#sign-date' //指定元素
					});

					var reqIdent = new Date().getTime();
					var uploader = WebUploader.create({
						// 选完文件后，是否自动上传。
						auto: false,
						// 文件接收服务端。
						server: fileHandlerUrl + '/table/web/upload/photo',
						// 选择文件的按钮。可选。
						pick: {
							id: '#uploadChoice',
							multiple: false // 设置multiple为false
						},
						timeout: 60 * 60 * 1000,
						// 配置分片上传
						chunked: true,
						chunkSize: 10 * 1024 * 1024,
						fileNumLimit: 1,
						accept: {
							title: 'Images',
							extensions: 'gif,jpg,jpeg,bmp,png',
							mimeTypes: 'image/*'
						},
						thumb: {
							width: 285,
							height: 150,
							// 图片质量，只有type为`image/jpeg`的时候才有效。
							quality: 100,
							// 是否允许放大，如果想要生成小图的时候不失真，此选项应该设置为false.
							allowMagnify: false,
							// 是否允许裁剪。
							crop: false
						},
						formData: {
							reqIdent: reqIdent,
							extraData: JSON.stringify({
								username: sessionStorage.getItem("username")
							})
						}
					});
					uploader.on('uploadBeforeSend', function (object, data, headers) {

					});

					// 当有文件被添加进队列之前触发
					uploader.on('beforeFileQueued', function (file) {
						// 检查队列中是否已经有文件
						if (uploader.getFiles().length > 0) {
							// 如果有文件，先移除旧的文件
							uploader.removeFile(uploader.getFiles()[0], true);
						}
					});

					// 当有文件被添加进队列的时候
					uploader.on('fileQueued', function (file) {
						$(".selectedFile").show();
						$("#selectedFileName").text(file.name);
						$("#previewFile").show();
						$("#sign-name").val(sessionStorage.getItem('fullname'));
						uploader.makeThumb(file, function (error, ret) {
							if (error) {

							} else {
								$("#previewImg").attr("src", ret);
							}
						});
					});

					uploader.on('uploadSuccess', function (file, res) {
						if (res.success) {
							var src = res.data.path;
							var date = $("#sign-date").val() || layui.util.toDateString(
								new Date(), 'yyyy-MM-dd');
							var signName = $("#sign-name").val();
							uploadSuccessFn(src, date, signName);
							layer.close(layerIndex);
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					});

					// 文件上传失败，显示上传出错。
					uploader.on('uploadError', function (file) {

					});

					// 完成上传完毕，成功或者失败，先删除进度条。
					uploader.on('uploadComplete', function (file) {

					});

					// 当所有文件上传结束时触发
					uploader.on('uploadFinished', function () {

					});

					$("#uploadStart").on('click', function () {
						uploader.upload();
					});
				}
			});
		}
	},
	uploadSign: function (treeNode, ele, tableSel) {
		// 在上传签章操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "上传签章", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '签名失败'
				});
				return; // 直接返回，不打开上传界面
			}

			// 乐观锁检查通过，继续执行上传签章逻辑
			ele = HotUtil.findDataTableEle(ele);
			HotUtil.uploadSignLayer(function (src, date, signName) {
				HotUtil.updateTableSign(src, ele, tableSel, treeNode, "upload", date, signName);
			});
		});
	},
	wacomSign: function (treeNode, ele, tableSel) {
		// 在手写板签名操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "手写板签名", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '签名失败'
				});
				return; // 直接返回，不开始签名流程
			}

			// 乐观锁检查通过，继续执行手写板签名逻辑
			HotUtil._executeWacomSign(treeNode, ele, tableSel);
		});
	},

	/**
	 * 执行手写板签名的具体逻辑（从原 wacomSign 函数中提取）
	 */
	_executeWacomSign: function (treeNode, ele, tableSel) {
		ele = HotUtil.findDataTableEle(ele);
		if (device.ie && device.ie < 10) {
			layer.alert('请在Chrome浏览器中使用！', {
				icon: 2
			});
		} else {
			function saveSignToBase64() {
				if (!!signPlugin) {
					//Get the signPlugin's signature image data.
					signPlugin.saveSignToBase64( /*615, 272*/ 0, 0, function (state, args) {
						if (state) {
							var img_base64_data = args[0];
							//Show the signature image.
							var img_base64 = "data:image/png;base64," + img_base64_data;
							if (img_base64 == blankSign) {
								layer.alert('获取签名图形失败！', {
									icon: 2
								});
							} else {
								document.getElementById("img_sign_result").src = img_base64;

								var dataUrl = $('#img_sign_result').attr("src");
								if (dataUrl) {
									HotUtil.updateTableSign(dataUrl, ele, tableSel, treeNode, "wacom");
								} else {
									layer.alert('请先签名！', {
										icon: 2
									});
								}

							}
							debugPrint("saveSignToBase64 OK");
							//Submit the signature base64 string to the server
							//...
						} else {
							debugPrint("saveSignToBase64 error,description:" + args[0]);
						}
					});
				}
			}

			/*confirm event*/
			signPlugin.onConfirm = function () {
				saveSignToBase64();
				endSign();
			};

			//在页面中构建一个隐藏的img标签用来存储签名图片
			var $signImg = $('<img style="display:none;" src="" id="img_sign_result"/>');
			$signImg.appendTo($('body'));
			beginSign();
		}
	},
	sign: function (treeNode, ele, tableSel) {
		// 在鼠标签名操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "鼠标签名", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '签名失败'
				});
				return; // 直接返回，不打开签名界面
			}

			// 乐观锁检查通过，继续执行鼠标签名逻辑
			HotUtil._executeSign(treeNode, ele, tableSel);
		});
	},

	/**
	 * 执行鼠标签名的具体逻辑（从原 sign 函数中提取）
	 */
	_executeSign: function (treeNode, ele, tableSel) {
		ele = HotUtil.findDataTableEle(ele);
		if (device.ie && device.ie < 10) {
			layer.alert('请在Chrome浏览器中使用！', {
				icon: 2
			});
		} else {
			layer.open({
				title: "签名",
				type: 1,
				area: ['1000px', '570px'],
				content: '<div id="report-signContent"></div>',
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				resize: false,
				btn: ['确定', '重签', '取消'],
				yes: function () {
					var dataUrl = $('.js-signature').eq(0).jqSignature('getDataURL');
					if (dataUrl) {
						HotUtil.updateTableSign(dataUrl, ele, tableSel, treeNode, "mouse");
					} else {
						layer.alert('请先签名！', {
							icon: 2
						});
					}
				},
				btn2: function () {
					$('.js-signature').eq(0).jqSignature('clearCanvas');
					return false;
				},
				btn3: function () {
					return true;
				},
				success: function () {
					var tpl = '<div class="js-signature" data-width="1000" data-height="460" data-border="1px solid #333"\
									data-line-color="#000" data-line-width="5" data-auto-fit="true">\
									</div>';
					$("#report-signContent").append(tpl);
					$('.js-signature').jqSignature();
				}
			});
		}
	},
	updateTableSign: function (signSrc, ele, tableSel, treeNode, type, signDate
		, signName, workno) {
		// 处理默认参数（ES5兼容方式）
		if (typeof signName === 'undefined') {
			signName = sessionStorage.getItem('fullname');
		}
		if (typeof workno === 'undefined') {
			workno = sessionStorage.getItem('workno');
		}

		// 在签名操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "签名", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '签名失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			// var nowDate = layui.util.toDateString(new Date(), 'yyyy-MM-dd');
			// var $img = $('<img>').attr("type", "sign").attr('src', signSrc).attr('class', 'sign-img').attr('date', nowDate);
			// var date = '<br><span>' + nowDate + '</span>'
			// if ($(ele).text().trim() != '' || $(ele).find("img").length > 0) {
			// 	$(ele).append("<br>");
			// }
			// $(ele).append($img).append(date);
			var log = {};
			if (type == "photo") {
				log.operation = "拍照";
			} else if (type == "mouse") {
				log.operation = "鼠标签名";
			} else if (type == "wacom") {
				log.operation = "手写板签名";
			} else if (type == "upload") {
				log.operation = "上传签章";
			}
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中第" +
				(parseInt($(ele).attr("row")) + 1) + "行第" + (parseInt($(ele).attr("col")) + 1) + "列" + log
					.operation;

			if (signDate) {

			} else {
				signDate = layui.util.toDateString(new Date(), 'yyyy-MM-dd');
			}
			// var htmlData = tableSel.find(".data-table")[0].outerHTML;
			var loadIndex = layer.load(); //上传loading
			var scrollObj = {
				isScroll: true,
				row: $(ele).attr("row"),
				col: $(ele).attr("col"),
			};
			var cb_success = function (res) {
				if (res.success) {
					log.content = log.content + "，照片路径为：" + res.data;
					log.reqResult = 1;
					layer.close(loadIndex);
					reloadTable(treeNode, scrollObj);
					layer.closeAll();
					if (res.msg) {
						layer.msg(res.msg);
					}
				} else {
					log.reqResult = 0;
					layer.close(loadIndex);
					layer.alert(res.msg);
				}
				addConfirmLog(log);
			}
			//请求失败的回调
			var cb_error = function (xhr, textStatus, errorThrown) {
				layer.alert("签名出错！", {
					icon: 2
				});
			};

			//同步新增
			twxAjax(THING, "AddSign", {
				id: treeNode.ID,
				img: signSrc,
				row: $(ele).attr("row"),
				col: $(ele).attr("col"),
				type: type,
				date: signDate,
				signName: signName,
				creator: sessionStorage.getItem('username'),
				workno: workno
			}, true, cb_success, cb_error);
		});
	},
	//找到数据存放表格对应的ele
	findDataTableEle: function (ele) {
		var tdCol = $(ele).attr("col");
		var tdRow = $(ele).attr("row");
		var dataTableEle = $(".data-table").find('td[col=' + tdCol + '][row=' + tdRow + ']');
		return dataTableEle[0];
	},
	insertImageLayer: function (treeNode, uploadAllDoneSuccess) {
		// 在上传图片操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "上传图片", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '上传失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			// 乐观锁检查通过，继续执行上传图片逻辑
			HotUtil._executeInsertImageLayer(treeNode, uploadAllDoneSuccess);
		});
	},

	/**
	 * 执行上传图片的具体逻辑（从原 insertImageLayer 函数中提取）
	 */
	_executeInsertImageLayer: function (treeNode, uploadAllDoneSuccess) {
		var tpl = '<div style="padding:12px;">\
							<div class="layui-upload" style="margin-bottom:2px;">\
								<button type="button" class="layui-btn layui-btn-normal" id="chooseFile">选择图片</button>\
								<button type="button" class="layui-btn" id="startUpload" style="display:none;">开始上传</button>\
							</div>\
							<div id="photo-table" ></div>\
						</div>';
		if (device.ie && device.ie < 10) {
			layer.alert("请使用chrome浏览器！", {
				icon: 2
			});
		} else {

			var cb_success = function (indexRes) {
				var photoIndex = indexRes.rows[0].result;

				var loadIndex;
				var layerIndex = layer.open({
					title: '上传图片',
					type: 1,
					area: ['1100px', '660px'],
					content: tpl,
					anim: false,
					openDuration: 200,
					isOutAnim: false,
					closeDuration: 200,
					resize: false,
					btn: ['上传', '取消'],
					yes: function () {
						var photoDatas = $('#photo-table').datagrid("getData").rows;
						if (photoDatas.length == 0) {
							layer.msg('请选择图片!');
							return false;
						}
						$('#startUpload').click();
					},
					btn2: function () {
						return true;
					},
					success: function () {
						window.delRow = function (fileIndex) {
							var rowInex = $('#photo-table').datagrid("getRowIndex",
								fileIndex);
							$('#photo-table').datagrid("deleteRow", rowInex);
							window.deletedFile.push(fileIndex);
						}
						//点击了删除按钮的文件列表
						window.deletedFile = [];
						$('#photo-table').datagrid({
							data: [],
							fitColumns: true,
							idField: 'index',
							// fit: true,
							columns: [
								[{
									field: 'photoName',
									title: '图片名称',
									width: 350
								}, {
									field: 'photoSize',
									title: '图片大小',
									width: 100,
									align: 'right',
								}, {
									field: 'photoShowNum',
									title: '图片编号',
									width: 350
								}, {
									field: 'photoNumber',
									hidden: true
								}, {
									field: 'index',
									hidden: true
								}, {
									field: 'photoFormat',
									title: '图片格式',
									width: 100
								}, {
									field: 'result',
									title: '预览',
									align: 'center',
									width: 100,
									formatter: function (value, row, index) {
										if (value) {
											return '<img style="max-width: 90px;height: 90px;" src="' +
												value + '" />';
										}
										return "";
									}
								}, {
									field: 'operation',
									title: '操作',
									align: 'center',
									formatter: function (value, row, index) {
										var fileIndex = row.index;
										return '<a fileIndex="' +
											fileIndex +
											'" onclick="delRow(\'' +
											fileIndex +
											'\')" class="layui-btn layui-btn-danger layui-btn-xs">删除</a>';
									},
									width: 100
								}]
							],
							emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>请上传图片</font></div>',
							loadMsg: '正在加载数据...',
							height: 498,
							singleSelect: true,
							rownumbers: true,
							striped: true
						});

						//上传组件
						var photoUploadIns = upload.render({
							elem: '#chooseFile',
							url: fileHandlerUrl + '/table/upload/photo',
							auto: false,
							multiple: true,
							accept: 'images',
							acceptMime: 'image/*', // 限制文件浏览器只显示图片文件
							field: 'photo',
							bindAction: '#startUpload',
							dataType: "json",
							before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
								loadIndex = layer.load(); //上传loading
								//删除文件
								var files = obj.pushFile();
								for (var i = 0; i < window.deletedFile
									.length; i++) {
									delete files[window.deletedFile[i]];
								}
							},
							choose: function (obj) {
								var files = obj.pushFile();

								function isInTable(key, tableData) {
									for (var i = 0; i < tableData.length; i++) {
										if (key == tableData[i].index) {
											return true;
										}
									}
									return false;
								}

								var datas = $('#photo-table').datagrid("getData")
									.rows;
								for (var key in files) {
									if (!isInTable(key, datas) && window.deletedFile
										.indexOf(key) == -1) {
										var file = files[key];
										var fileName = file.name;
										var photoObj = {};
										photoObj.photoName = fileName.substr(0,
											fileName.lastIndexOf('.'));
										photoObj.photoFormat = fileName.substr(
											fileName.lastIndexOf('.') + 1);
										photoObj.photoSize = (file.size / 1024)
											.toFixed(1) + 'kb';
										photoObj.index = key;
										photoObj.file = file;
										datas.push(photoObj);
									}
								}
								datas.sort(function (x, y) {
									return x['photoName'].localeCompare(y[
										'photoName'], "zh");
								});
								for (var i = 0, initIndex = photoIndex; i < datas
									.length; i++, initIndex++) {
									var photoObj = datas[i];
									var tempStr = treeNode.NAME;
									if (treeNode.TABLE_NUM) {
										tempStr = treeNode.TABLE_NUM;
									}
									photoObj.photoShowNum = tempStr + "-图" +
										initIndex;
									photoObj.photoNumber = initIndex;
								}
								$('#photo-table').datagrid("loadData", datas);

								//预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
								obj.preview(function (index, file, result) {
									var rowInex = $('#photo-table')
										.datagrid("getRowIndex", index);
									var rowData = $('#photo-table')
										.datagrid("getData").rows[rowInex];
									rowData.result = result;
									$('#photo-table').datagrid(
										"updateRow", {
										index: rowInex,
										row: rowData
									});
									$('#photo-table').datagrid("refreshRow",
										rowInex);
								});
							},
							done: function (res, fileIndex) {
								if (res.success) {
									var rowIndex = $('#photo-table').datagrid(
										"getRowIndex", fileIndex);
									var rowData = $('#photo-table').datagrid(
										"getData").rows[rowIndex];
									rowData.photoPath = res.data[0].photoPath;
									rowData.success = true;
									$('#photo-table').datagrid("updateRow", {
										index: rowIndex,
										row: rowData
									});
									$('#photo-table').datagrid("refreshRow",
										rowIndex);
								} else {
									layer.alert(res.msg, {
										icon: 2
									});
								}
							},
							allDone: function (obj) {
								layer.close(loadIndex);
								if (obj.aborted > 0) {
									layer.msg('上传失败！');
								} else {
									var datas = $('#photo-table').datagrid(
										"getData").rows;
									uploadAllDoneSuccess(datas, layerIndex,
										loadIndex);
								}
							}
						});
					}
				});
			}
			//请求失败的回调
			var cb_error = function (xhr, textStatus, errorThrown) {
				layer.alert("请求照片序号出错！", {
					icon: 2
				});
			};
			twxAjax(THING, "GetNewPhotoNum", {
				id: treeNode.ID
			}, true, cb_success, cb_error);
		}
	},
	/**
	 * @param {Object} treeNode
	 * @param {Object} ele
	 * @param {Object} photoIndex
	 * @param {Object} tableSel
	 */
	insertImage: function (treeNode, ele, tableSel) {
		ele = HotUtil.findDataTableEle(ele);
		var eleCol = parseInt($(ele).attr("col"));
		var eleRow = parseInt($(ele).attr("row"));
		HotUtil.insertImageLayer(treeNode, function (datas, layerIndex) {
			var photos = [];
			var photoText = "[";
			var photoCount = 0;
			for (var i = 0; i < datas.length; i++) {
				var rowData = datas[i];
				if (rowData.success) {
					var params = {};
					params.id = treeNode.ID;
					params.photoPath = rowData.photoPath;
					params.photoFormat = rowData.photoFormat;
					params.photoName = rowData.photoName;
					params.photoNumber = rowData.photoNumber;
					params.photoShowNum = rowData.photoShowNum;
					params.photoSize = rowData.photoSize;
					params.index = rowData.index;
					params.creator = sessionStorage.getItem('username');
					params.col = eleCol;
					params.row = eleRow;
					photos.push(params);
					photoText += "{照片名称：" + (params.photoName + "." + params.photoFormat) + "，路径：" +
						params.photoPath + "，大小：" + params.photoSize + "，编号：" + params.photoShowNum +
						"},";
					photoCount++;
				}
				photoText = photoText.substring(0, photoText.length - 1);
				photoText += "]";
			}
			var log = {};
			log.operation = "上传图片";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			var logContent = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】的表中的第" + (eleRow + 1) +
				"行第" + (eleCol + 1) +
				"列中上传了" + photoCount + "张图片，分别是" + photoText;
			log.content = logContent;
			var cb_success = function (res) {
				if (res.success) {
					log.reqResult = 1;
					reloadTable(treeNode);
					layer.close(layerIndex);
					layer.msg(res.msg);
				} else {
					log.reqResult = 0;
					layer.alert(res.msg, {
						icon: 2
					});
				}
				addConfirmLog(log);
			}

			//请求失败的回调
			var cb_error = function (xhr, textStatus, errorThrown) {
				layer.alert("上传图片出错！", {
					icon: 2
				});
			};
			//同步新增
			twxAjax(THING, "AddPhotos", {
				datas: photos
			}, false, cb_success, cb_error);
		});
	},
	getPreviewEle: function (imgs, isAllowDel) {
		var $ul = $('<ul class="picView-magnify-list" style="padding: 30px"></ul>');
		for (var i = 0; i < imgs.length; i++) {
			var photoPath = imgs[i].photoPath;
			if (photoPath.indexOf("File") == -1) {
				photoPath = "/File" + photoPath;
			}
			var photoName = imgs[i].photoName + "." + imgs[i].photoformat;
			var photoShowNum = imgs[i].photoShowNum;
			var photoId = imgs[i].photoId || imgs[i].id;
			var deleteDiv = '<div class="img-icon">\
													<i class="layui-icon" photo-id="' + photoId + '" photo-name="' + photoName + '" photo-num="' +
				photoShowNum + '" photo-path="' + photoPath + '">&#xe640;</i>\
												</div>';
			var li = '<li>\
													<a href="javascript:void(0)" data-magnify="gallery" data-group="g1" data-src="' + photoPath +
				'" data-caption="' + photoShowNum + '">\
														<img src="' + photoPath + '">\
													</a>\
													<div class="img-title">' + photoShowNum + '</div>\
												</li>';
			var $li = $(li);
			$ul.append($li);

			if (isAllowDel) {
				$li.prepend(deleteDiv);
			}
		}
		return $ul;
	},
	viewImageLayer: function (imgs, isAllowDel, cancelFn, delClickFn) {
		layer.open({
			title: '查看图片',
			type: 1,
			area: ['1130px', '560px'],
			zIndex: layer.zIndex + 1,
			content: '<div id="previewContent"></div>',
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			cancel: function (index, layero) {
				cancelFn();
			},
			success: function () {
				var $ul = HotUtil.getPreviewEle(imgs, isAllowDel);
				$('#previewContent').append($ul);
				if (isAllowDel) {
					//删除事件
					$ul.find('i').click(function () {
						var $i = $(this);
						delClickFn($i, $ul);
					});
				}
				$('[data-magnify]').magnify({
					resizable: false,
					initMaximized: true,
					zIndex: layer.zIndex + 1,
					headerToolbar: [
						'close'
					],
					i18n: {
						minimize: '最小化',
						maximize: '最大化',
						close: '关闭',
						zoomIn: '缩小',
						zoomOut: '放大',
						prev: '上一张',
						next: '下一张',
						fullscreen: '全屏',
						actualSize: '实际大小',
						rotateLeft: '向左旋转',
						rotateRight: '向右旋转',
					}
				});

			}
		});
	},
	viewImage: function (treeNode, ele, tableSel, isAllowDel) {
		ele = HotUtil.findDataTableEle(ele);
		if (!$(ele).data("hasImg")) {
			layer.msg("暂无图片");
			return false;
		}
		var imgs = $(ele).data("imgs");
		HotUtil.viewImageLayer(imgs, isAllowDel, function () {
			if ($(".picView-magnify-list").data("delete")) {
				//如果删除过照片则需要在关闭预览窗口的时候刷新整个表格
				var scrollObj = {
					isScroll: true,
					row: $(ele).attr("row"),
					col: $(ele).attr("col"),
				};
				reloadTable(treeNode, scrollObj);
			}
		}, function ($i, $ul) {
			// 在删除图片操作前进行异步乐观锁检查
			HotUtil.checkOptimisticLockBeforeAction(treeNode, "删除图片", function(lockCheckResult) {
				if (!lockCheckResult.success) {
					// 乐观锁检查失败，直接显示错误信息并返回
					layer.alert(lockCheckResult.errorMsg, {
						icon: 2,
						title: '删除失败'
					});
					return; // 直接返回，不执行后续逻辑
				}

				var src = $i.attr("photo-path");
				var photoShowNum = $i.attr("photo-num");
				var photoName = $i.attr("photo-name");
				var log = {};
				log.operation = "删除照片";
				log.tablePid = treeNode.PID;
				log.tableId = treeNode.ID;
				log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID +
					"）】的表中的第" +
					(parseInt($(ele).attr("row")) + 1) + "行第" + (parseInt($(ele)
						.attr("col")) + 1) +
					"列删除了照片（名称：" + photoName + "，编号：" + photoShowNum + "，路径：" +
					src + "）";

				twxAjax(THING, "DeletePhoto", {
					id: treeNode.ID,
					src: src,
					col: $(ele).attr("col"),
					row: $(ele).attr("row")
				}, true, function (res) {
					if (res.success) {
						log.reqResult = 1;
						$i.parent().parent().remove();
						$ul.data("delete", true);
						layer.msg(res.msg);
					} else {
						log.reqResult = 0;
						layer.alert(res.msg, {
							icon: 2
						});
					}
					addConfirmLog(log);
				}, function () {
					layer.alert("删除失败");
				});
			});
		});
	},
	clearLock: function (treeNode) {
		// 在清除签名操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "清除签名", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '清除签名失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			if (treeNode.TABLE_STATUS != 'sign') {
				layer.alert("表格还未确认，无需清除签名！");
				return false;
			}
			layer.confirm("清除签名之后不可恢复，您确定吗？", {
				icon: 3,
				title: '提示'
			}, function (index) {
				var cb_success = function (res) {
					var log = {};
					log.operation = "清除签名";
					log.tablePid = treeNode.PID;
					log.tableId = treeNode.ID;
					log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上清除签名";

					if (res.success) {
						log.reqResult = 1;
						//更新node节点的数据
						treeNode.TABLE_STATUS = 'edit';
						ztreeObj.updateNode(treeNode);
						reloadTable(treeNode);
						layer.closeAll();
						layer.msg(res.msg);
					} else {
						log.reqResult = 0;
						layer.alert(res.msg, {
							icon: 2
						});
					}
					addConfirmLog(log);
				};
				var cb_error = function (xhr) {
					layer.alert('清除签名失败!', {
						icon: 2
					});
				};
				twxAjax(THING, "UpdateTableStatus", {
					id: treeNode.ID,
					status: 'edit'
				}, false, cb_success, cb_error);
			});
		});
	},
	/**
	 * 锁定整个表格 去除单行的锁定信息
	 * @param {Object} treeNode
	 */
	lockTable: function (treeNode) {
		// 在锁定表格操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "锁定表格", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '锁定失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			if (treeNode.HTML_DATA) {
				if (treeNode.TABLE_STATUS == 'sign') {
					layer.alert("表格已经锁定！");
					return false;
				}
				layer.confirm("请确认表格数据无误，锁定之后不可再次编辑表格！", {
					icon: 3,
					title: '提示'
				}, function (index) {
					var cb_success = function (res) {
						var log = {};
						log.operation = "锁定整表";
						log.tablePid = treeNode.PID;
						log.tableId = treeNode.ID;
						log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上锁定整表";

						if (res.success) {
							log.reqResult = 1;
							//更新node节点的数据
							treeNode.TABLE_STATUS = 'sign';
							ztreeObj.updateNode(treeNode);
							reloadTable(treeNode);
							layer.closeAll();
							layer.msg(res.msg);
						} else {
							log.reqResult = 0;
							layer.alert(res.msg, {
								icon: 2
							});
						}
						addConfirmLog(log);
					};
					var cb_error = function (xhr) {
						layer.alert('锁定失败!', {
							icon: 2
						});
					};
					twxAjax(THING, "UpdateTableStatus", {
						id: treeNode.ID,
						status: 'sign',
						saveUser: sessionStorage.getItem("username")
					}, false, cb_success, cb_error);
				});
			} else {
				layer.alert("请先编辑表格");
			}
		});
	},
	importPdf: function (treeNode, url) {
		layer.confirm("导入Pdf会覆盖现有表格，是否继续？", {
			icon: 3,
			title: '提示'
		}, function (index) {
			layer.close(index);
			var fileFlag = false;
			layer.open({
				title: "导入Pdf",
				type: 1,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				shadeClose: false,
				// fixed: false,
				maxmin: false,
				resize: false, //不允许拉伸
				area: ['350px', '220px'],
				content: '<div id="importPdfContent" style="padding-top: 15px;padding-right: 15px;"></div>',
				btn: ['确认', '取消'],
				yes: function () {
					if (!fileFlag) {
						layer.alert('请选择需要导入的pdf文件!', {
							icon: 2
						});
						return false;
					}
					$('#uploadStart').click();
				},
				btn2: function () {
					layer.closeAll();
				},
				success: function () {
					var addTpl = $("#uploadHtml")[0].innerHTML;
					$("#importPdfContent").append(addTpl);
					form.render(null, 'uploadForm');
					var log = {};
					log.operation = "导入Pdf";
					log.tablePid = treeNode.PID;
					log.tableId = treeNode.ID;

					var uploadInst = upload.render({
						elem: '#uploadChoice',
						url: url + '?thing=' + THING + '&id=' + treeNode.ID +
							'&saveUser=' + sessionStorage.getItem("username"),
						auto: false,
						accept: 'file',
						field: 'uploadFile',
						exts: 'pdf|PDF',
						bindAction: '#uploadStart',
						dataType: "json",
						choose: function (obj) {
							fileFlag = true;
							var o = obj.pushFile();
							var filename = '';
							for (var k in o) {
								var file = o[k];
								filename = file.name;
							}
							$("#selectedFile").show();
							$("#selectedFileName").text(filename);
							log.content = "在节点【" + treeNode.NAME + "（" +
								treeNode.ID + "）】上导入了Pdf文件(" + filename + ")";
						},
						before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
							layer.load(); //上传loading
						},
						done: function (res, index, upload) {
							layer.closeAll();
							if (res.success) {
								layer.msg("导入成功");
								reloadTable(treeNode);
								log.reqResult = 1;
							} else {
								log.reqResult = 0;
								layer.alert(res.msg, {
									icon: 2
								});
							}
							addConfirmLog(log);
						}
					});
				}
			});
		});
	},
	importBigZip: function (treeNode, url) {
		layer.confirm("导入数据包会覆盖点该节点的原有数据，是否继续？", {
			icon: 3,
			title: '提示'
		}, function (index) {
			layer.close(index);
			var log = {};
			log.operation = "导入数据包";
			log.tablePid = treeNode['PID'];
			log.tableId = treeNode['ID'];
			log.reqResult = 0;
			oneBigFileUpload({
				url: url,
				layerTitle: "导入数据包",
				emptyMsg: "请选择需要导入的数据文件!",
				extraData: {
					username: sessionStorage.getItem("username"),
					tableId: treeNode['ID'],
					tablePId: treeNode['PID'],
					srcType: treeNode['TYPE'],
					treeId: treeNode['TREEID'],
					thing: THING
				},
				fileQueued: function (fileName) {
					log.content = "在节点【" + treeNode['NAME'] + "（" + treeNode['ID'] +
						"）】上导入数据包(" +
						fileName + ")";
				},
				uploadSuccess: function (res) {
					log.reqResult = 1;
					layer.msg("导入成功！");
					reloadTree(treeNode['PID'], res['rootId']);
				},
				uploadComplete: function () {
					addConfirmLog(log);
				}
			})

		});
	},
	importExcel: function (treeNode, url) {
		// 在导入Excel操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "导入Excel", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '导入失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			// 乐观锁检查通过，继续执行导入Excel逻辑
			HotUtil._executeImportExcel(treeNode, url);
		});
	},

	/**
	 * 执行导入Excel的具体逻辑（从原 importExcel 函数中提取）
	 */
	_executeImportExcel: function (treeNode, url) {
		function importExcelLayer(index) {
			if (index) {
				layer.close(index);
			}
			var fileFlag = false;
			layer.open({
				title: "导入Excel",
				type: 1,
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				shadeClose: false,
				// fixed: false,
				maxmin: false,
				resize: false, //不允许拉伸
				area: ['350px', '220px'],
				content: '<div id="importExcelContent" style="padding-top: 15px;padding-right: 15px;"></div>',
				btn: ['确认', '取消'],
				yes: function () {
					if (!fileFlag) {
						layer.alert('请选择需要导入的excel文件!', {
							icon: 2
						});
						return false;
					}
					$('#uploadStart').click();
				},
				btn2: function () {
					layer.closeAll();
				},
				success: function () {
					var addTpl = $("#uploadHtml")[0].innerHTML;
					$("#importExcelContent").append(addTpl);
				}
			});
			form.render(null, 'uploadForm');

			var log = {};
			log.operation = "导入Excel";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;

			var uploadInst = upload.render({
				elem: '#uploadChoice',
				url: url + '?thing=' + THING + '&id=' + treeNode.ID + '&saveUser=' + sessionStorage
					.getItem("username"),
				auto: false,
				accept: 'excel', // 指定只接受excel类型文件
				acceptMime: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // 限制文件浏览器只显示excel文件
				field: 'uploadFile',
				exts: 'xls|xlsx',
				bindAction: '#uploadStart',
				dataType: "json",
				choose: function (obj) {
					fileFlag = true;
					var o = obj.pushFile();
					var filename = '';
					for (var k in o) {
						var file = o[k];
						filename = file.name;
					}
					$("#selectedFile").show();
					$("#selectedFileName").text(filename);
					log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导入了Excel文件(" +
						filename + ")";
				},
				before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
					layer.load(1, { content: '正在上传文件...' }); //上传loading
				},
				done: function (res, index, upload) {
					layer.closeAll();
					if (res.success) {
						layer.msg("导入成功");
						reloadTable(treeNode);
						log.reqResult = 1;
					} else {
						log.reqResult = 0;
						layer.alert(res.msg, {
							icon: 2
						});
					}
					addConfirmLog(log);
				}
			});
			if (device.ie && device.ie < 10) {
				$("input[name='uploadFile']").change(function () {
					var filename = $(this).val();
					filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
					$("#selectedFile").show();
					$("#selectedFileName").text(filename);
				});
			}

		}

		if (treeNode.HTML_DATA) {
			if (treeNode.HTML_DATA.indexOf('lock="true"') > -1) {
				layer.alert('表格已有部分内容锁定，不可导入！', {
					icon: 2
				});
			} else {
				layer.confirm("导入Excel会覆盖现有表格，并且已经上传的图片会被删除，是否继续？", {
					icon: 3,
					title: '提示'
				}, function (index) {
					importExcelLayer(index);
				});
			}
		} else {
			importExcelLayer();
		}
	},
	exportPdf: function (treeNode, url) {
		//日志记录
		var log = {};
		log.operation = "导出PDF";
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;
		log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出了PDF文件";
		var loading;
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"id": treeNode.ID,
				"thing": THING
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				log.reqResult = 0;
				addConfirmLog(log);
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				log.reqResult = 1;
				addConfirmLog(log);
				layer.close(loading);
			},
			failCallback: function (html, url) {
				var msg = '导出失败！表格中可能存在多余的合并单元格，请处理之后重试。';
				log.reqResult = 0;
				log.content = log.content + ",报错：" + msg;
				addConfirmLog(log);
				layer.close(loading);
				layer.alert(msg, {
					icon: 2
				});
			}
		});
	},
	exportExcel: function (treeNode, url) {
		//日志记录
		var log = {};
		log.operation = "导出Excel";
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;
		log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上导出了Excel文件";

		var loading;
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"id": treeNode.ID,
				"thing": THING
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				log.reqResult = 1;
				addConfirmLog(log);
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				log.reqResult = 1;
				addConfirmLog(log);
				layer.close(loading);
			},
			failCallback: function (html, url) {
				log.reqResult = 0;
				addConfirmLog(log);
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});
	},
	exportImg: function (treeNode, url) {
		//日志记录
		var log = {};
		log.operation = "下载所有照片";
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;
		log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上下载所有照片";

		var loading;
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"id": treeNode.ID,
				"thing": THING
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在导出...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				log.reqResult = 0;
				addConfirmLog(log);
				layer.close(loading);
				layer.msg("导出异常！！");
			},
			successCallback: function (url) {
				log.reqResult = 1;
				addConfirmLog(log);
				layer.close(loading);
			},
			failCallback: function (html, url) {
				log.reqResult = 0;
				addConfirmLog(log);
				layer.close(loading);
				layer.msg("导出失败！！");
			}
		});
	},
	updateHeaderRow: function (treeNode) {
		// 在设置表头行操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "设置表头行", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '设置失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			// 乐观锁检查通过，继续执行设置表头行逻辑
			HotUtil._executeUpdateHeaderRow(treeNode);
		});
	},

	/**
	 * 执行设置表头行的具体逻辑（从原 updateHeaderRow 函数中提取）
	 */
	_executeUpdateHeaderRow: function (treeNode) {
		var tpl = '<form class="layui-form" action="" lay-filter="header-form">\
						<div class="layui-form-item">\
							<div class="layui-inline">\
								<label class="layui-form-label">行数</label>\
									<div class="layui-input-inline" style="width: 100px;">\
										<input type="number" name="header_min" lay-verify="required|number" autocomplete="off" class="layui-input">\
									</div>\
									<div class="layui-form-mid">-</div>\
									<div class="layui-input-inline" style="width: 100px;">\
										<input type="number" name="header_max" lay-verify="required|number" autocomplete="off" class="layui-input">\
									</div>\
							</div>\
						</div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="submit-header" lay-submit="" lay-filter="submit-header">确认</button>\
								</div>\
							</div>\
						</div>\
					</form>';
		layer.open({
			title: '设置表格表头行',
			type: 1,
			fixed: false,
			maxmin: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			resize: false, //不允许拉伸
			area: ['380px', '180px'],
			content: '<div id="headerContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['确认', '取消'],
			yes: function () {
				$('#submit-header').click();
			},
			btn2: function () {
				return true;
			},
			success: function () {
				$("#headerContent").append(tpl);
			}
		});
		form.render(null, 'header-form');
		form.on('submit(submit-header)', function (data) {
			if (/^\+?[0-9][0-9]*$/.test(data.field.header_min) && /^\+?[0-9][0-9]*$/.test(data.field
				.header_max)) {
				if (Number(data.field.header_min) <= Number(data.field.header_max)) {
					//日志记录
					var log = {};
					log.operation = "设置表头行";
					log.tablePid = treeNode.PID;
					log.tableId = treeNode.ID;
					var cb_success = function (res) {
						if (res.success) {
							log.reqResult = 1;
							layer.closeAll();
							layer.msg(res.msg);
							reloadTable(treeNode);
						} else {
							log.reqResult = 0;
							layer.alert(res.msg, {
								icon: 2
							});
						}
						addConfirmLog(log);
					};
					var cb_error = function () {
						layer.closeAll();
						layer.msg('设置失败！');
					};
					var headerRow = data.field.header_min + "-" + data.field.header_max;
					log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" +
						"设置表头行为（" + headerRow + "）";
					twxAjax(THING, 'UpdateTableHeader', {
						id: treeNode.ID,
						header: headerRow
					}, true, cb_success, cb_error);
				} else {
					layer.msg('起始值比结束值小！')
				}
			} else {
				layer.msg('请输入正整数！')
			}
			return false;
		});
	},
	/** 使表格进入编辑状态
	 * @param {Object} treeNode
	 */
	openEdit: function (treeNode, isUpdate, successFn) {
		twxAjax(THING, 'OpenEdit', {
			id: treeNode.ID,
			isUpdate: isUpdate,
			user: sessionStorage.getItem("fullname") + '[' + sessionStorage.getItem('username') + ']',
		}, true, function (res) {
			if (res.success) {
				successFn();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function (err) {
			layer.alert('进入编辑失败！', {
				icon: 2
			});
		});
	},
	/** 使表格关闭编辑状态
	 * @param {Object} treeNode
	 */
	closeEdit: function (treeNode, successFn) {
		twxAjax(THING, 'CloseEdit', {
			id: treeNode.ID
		}, true, function (res) {
			if (res.success) {
				successFn();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function (err) {
			layer.alert('退出编辑失败！', {
				icon: 2
			});
		});
	},
	editTable: function (treeNode) {
		// 在编辑表格操作开始时进行异步乐观锁检查
		HotUtil.checkOptimisticLockBeforeAction(treeNode, "编辑表格", function(lockCheckResult) {
			if (!lockCheckResult.success) {
				// 乐观锁检查失败，直接显示错误信息并返回
				layer.alert(lockCheckResult.errorMsg, {
					icon: 2,
					title: '编辑失败'
				});
				return; // 直接返回，不执行后续逻辑
			}

			// 乐观锁检查通过，设置编辑状态后继续执行编辑表格逻辑
			HotUtil.openEdit(treeNode, 1, function () {
				HotUtil._executeEditTable(treeNode);
			});
		});
	},

	/**
	 * 执行编辑表格的具体逻辑（从原 editTable 函数中提取）
	 */
	_executeEditTable: function (treeNode) {
		var log = {};
		log.operation = '编辑表格';
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;
		log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上编辑表格";
		layer.open({
			title: '编辑：' + treeNode.NAME,
			type: 1,
			fixed: false,
			maxmin: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			zIndex: 1000,
			shadeClose: false,
			resize: false, //不允许拉伸
			area: [$('body').css('width'), $('body').css('height')],
			content: '<div id="handsontable" style="width:100%;height:100%;"></div>',
			cancel: function (index, layero) {
				HotUtil.closeEdit(treeNode, function () {
					if (window.hot) {
						window.hot.destroy();
					}
					reloadTable(treeNode);
				});
			},
			btn: ['保存', '取消'],
			yes: function () {
				// 防止重复点击手动保存按钮
				if (HotUtil._manualSaveInProgress) {
					layer.msg('正在保存中，请勿重复操作', { icon: 0 });
					return false;
				}

				HotUtil.closeEdit(treeNode, function () {
					// 手动保存：显示保存状态提示，允许与自动保存并发
					HotUtil.saveTableData(treeNode, true, true, true);
					if (window.hot) {
						window.hot.destroy();
					}
				});
			},
			btn2: function () {
				HotUtil.closeEdit(treeNode, function () {
					if (window.hot) {
						window.hot.destroy();
					}
					reloadTable(treeNode);
					return true;
				});
			},
			success: function (layero, index) {
				var saveData = treeNode.SAVE_DATA;
				//重新请求表格数据 避免同时编辑的时候产生冲突

				var afterChange = function (change, source) {
					// 增强错误处理：避免在初始化过程中触发保存
					if (source != 'loadData' && source != 'populateFromArray' && source != 'auto') {
						// 检查Handsontable是否完全初始化
						if (window.hot && window.hot.isDestroyed !== true) {
							// 自动保存：静默保存，不显示保存状态提示
							HotUtil.saveTableData(treeNode, false, false, false);
						} else {
							console.log('afterChange: Handsontable未完全初始化，跳过保存操作');
						}
					}
				}
				var cb_success = function (res) {
					if (res.success) {
						saveData = res.data.SAVE_DATA || "";
						HotUtil.renderHot(saveData, treeNode, res.data.TABLE_HEADER,
							afterChange);
						log.reqResult = 1;
					} else {
						log.reqResult = 0;
					}
					addConfirmLog(log);
				};
				var cb_error = function (xhr) {
					layer.alert('加载表格失败!', {
						icon: 2
					});
				};
				twxAjax(THING, "QueryNodeById", {
					id: treeNode.ID
				}, true, cb_success, cb_error);
			}
		});
	},
	/**
	 * 获取表格列的宽度
	 */
	getColWidths: function () {
		if (!window.hot) return [];
		var countCols = window.hot.countCols();
		var colWidths = [];
		for (var i = 0; i < countCols; i++) {
			colWidths.push(window.hot.getColWidth(i));
		}
		return colWidths;
	},
	/**
	 * 获取表格行的高度
	 */
	getRowHieghts: function () {
		if (!window.hot) return [];
		var countRows = window.hot.countRows();
		var rowHieghts = [];
		for (var i = 0; i < countRows; i++) {
			rowHieghts.push(window.hot.getRowHeight(i));
		}
		return rowHieghts;
	},
	/**
	 * 保存状态管理
	 */
	_autoSaveInProgress: false,  // 自动保存状态
	_manualSaveInProgress: false, // 手动保存状态

	/**
	 * 保存表格数据
	 * @param {Object} treeNode 树节点对象
	 * @param {Boolean} isReloadTable 是否重新加载表格
	 * @param {Boolean} isValidEmpty 是否验证空表
	 * @param {Boolean} showSaveMsg 是否显示保存状态提示，默认true（手动保存），false为静默保存（自动保存）
	 */
	saveTableData: function (treeNode, isReloadTable, isValidEmpty, showSaveMsg) {
		// 处理默认参数（ES5兼容方式）
		if (typeof showSaveMsg === 'undefined') {
			showSaveMsg = true; // 默认显示保存提示（手动保存）
		}

		// 区分自动保存和手动保存的防重复检查
		var isManualSave = showSaveMsg;
		var isAutoSave = !showSaveMsg;

		// 防重复提交检查
		if (isManualSave && HotUtil._manualSaveInProgress) {
			layer.msg('正在保存中，请勿重复操作', { icon: 0 });
			return false;
		}

		if (isAutoSave && HotUtil._autoSaveInProgress) {
			// 自动保存正在进行中，静默跳过
			return false;
		}

		// 增强参数验证
		if (!treeNode || !treeNode.ID) {
			console.error('保存表格数据：无效的节点参数');
			if (showSaveMsg) {
				layer.alert('节点信息无效，无法保存数据', {
					icon: 2,
					title: '保存失败'
				});
			}
			return false;
		}

		// 增强错误处理：检查Handsontable实例（允许初始化过程中的调用）
		if (!window.hot) {
			console.warn('保存表格数据：Handsontable实例不存在，可能正在初始化中');
			// 如果是在初始化过程中，不显示错误提示，直接返回
			return false;
		}

		// 设置对应的保存状态
		if (isManualSave) {
			HotUtil._manualSaveInProgress = true;
		} else {
			HotUtil._autoSaveInProgress = true;
		}

		// 根据参数决定是否显示保存状态提示
		var savingMsg = null;
		if (showSaveMsg) {
			savingMsg = layer.msg('正在保存数据...', {
				icon: 16,
				shade: 0.3,
				time: 0 // 不自动关闭
			});
		}

		// 定义保存完成后的清理函数
		var finalizeSave = function() {
			if (isManualSave) {
				HotUtil._manualSaveInProgress = false;
			} else {
				HotUtil._autoSaveInProgress = false;
			}
			if (savingMsg) {
				layer.close(savingMsg);
			}
		};

		try {
			var tableData = window.hot.getData();
			var meta = HotUtil.myGetCellsMeta();

			if (isValidEmpty) {
				if (!tableData || tableData.length == 0) {
					finalizeSave();
					layer.alert("空表不可提交");
					return false;
				}
			}

			// 增强错误处理：获取合并单元格信息
			var merged = [];
			try {
				var mergePlugin = window.hot.getPlugin('MergeCells');
				if (mergePlugin && mergePlugin.mergedCellsCollection) {
					merged = mergePlugin.mergedCellsCollection.mergedCells || [];
				}
			} catch (mergeError) {
				console.warn('获取合并单元格信息失败:', mergeError);
				merged = [];
			}

			var data = {
				tableData: tableData,
				merged: merged,
				meta: meta,
				colWidths: HotUtil.getColWidths()
			};

			// 增强错误处理：JSON序列化
			var serializedData;
			try {
				serializedData = JSON.stringify(data);
				//替换英文引号为中文引号
				serializedData = serializedData.replace(/'(.[^']*)'/g, "' $1 '");
			} catch (jsonError) {
				console.error('数据序列化失败:', jsonError);
				finalizeSave();
				layer.alert('数据格式错误，无法保存', {
					icon: 2,
					title: '保存失败'
				});
				return false;
			}

			var param = {};
			param.id = treeNode.ID;
			param.tableData = serializedData;
			param.saveUser = sessionStorage.getItem("username");

			var log = {};
			log.operation = "保存数据";
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;
			log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上保存数据";

			var cb_success = function (res) {
				finalizeSave();
				try {
					if (res && res.success) {
						log.reqResult = 1;
						if (isReloadTable) {
							reloadTable(treeNode);
							layer.closeAll();
							layer.msg(res.msg || '保存成功');
						}
					} else {
						log.reqResult = 0;
						layer.alert(res.msg || '保存失败', {
							icon: 2
						});
					}
				} catch (successError) {
					console.error('处理保存成功回调时出错:', successError);
					log.reqResult = 0;
				} finally {
					addConfirmLog(log);
				}
			};

			var cb_error = function (xhr, textStatus, errorThrown) {
				finalizeSave();
				console.error('保存表格数据请求失败:', textStatus, errorThrown);
				log.reqResult = 0;
				layer.alert("保存失败：" + (textStatus || "网络错误"), {
					icon: 2,
					title: '保存失败'
				});
				addConfirmLog(log);
			};

			//使用ajax进行异步加载Tree
			twxAjax(THING, 'SaveTableData', param, true, cb_success, cb_error);

		} catch (error) {
			finalizeSave();
			console.error('保存表格数据时发生异常:', error);
			layer.alert('保存过程中发生错误: ' + error.message, {
				icon: 2,
				title: '保存失败'
			});
			return false;
		}
	},
	/**
	 * 在执行操作前检查乐观锁状态和编辑状态（异步版本）
	 * @param {Object} treeNode 当前节点
	 * @param {String} actionName 操作名称（用于错误提示）
	 * @param {Function} callback 回调函数 callback(result) result: {success: boolean, errorMsg: string}
	 */
	checkOptimisticLockBeforeAction: function (treeNode, actionName, callback) {
		// 增强参数验证
		if (!treeNode || !treeNode.ID) {
			console.error('乐观锁检查：无效的节点参数');
			callback({
				success: false,
				errorMsg: "节点信息无效，无法执行" + (actionName || "操作")
			});
			return;
		}

		// 验证回调函数
		if (typeof callback !== 'function') {
			console.error('乐观锁检查：缺少回调函数');
			return;
		}

		// 如果没有currentSaveTime，说明可能是新数据或页面刚加载，允许继续
		if (!HotUtil.currentSaveTime) {
			callback({ success: true, errorMsg: "" });
			return;
		}

		// 显示检查状态提示
		var checkingMsg = layer.msg('正在检查数据状态...', {
			icon: 16,
			shade: 0.3,
			time: 0 // 不自动关闭
		});

		// 使用异步方式调用增强的乐观锁检查（同时检查编辑状态）
		twxAjax(THING, 'CheckOptimisticLockAndEditStatus', {
			id: treeNode.ID,
			expectedSaveTime: HotUtil.currentSaveTime,
			currentUser: sessionStorage.getItem("fullname") + '[' + sessionStorage.getItem('username') + ']'
		}, true, function (res) {
			// 关闭检查状态提示
			layer.close(checkingMsg);

			try {
				if (res && res.success) {
					callback({ success: true, errorMsg: "" });
				} else {
					var errorMsg = "";
					if (res && res.editConflict) {
						errorMsg = "表格正在被用户 " + res.currentEditor + " 编辑中，无法" + actionName + "。请稍后再试。";
					} else if (res && res.conflict) {
						errorMsg = "数据已被其他用户修改，无法" + actionName + "。请刷新页面获取最新数据后重试。";
					} else {
						errorMsg = (res && res.msg) || "数据状态检查失败";
					}
					callback({ success: false, errorMsg: errorMsg });
				}
			} catch (e) {
				console.error('处理乐观锁检查结果时出错:', e);
				callback({
					success: false,
					errorMsg: "数据状态检查出错: " + e.message
				});
			}
		}, function (xhr, textStatus, errorThrown) {
			// 关闭检查状态提示
			layer.close(checkingMsg);

			// 如果新服务不存在，回退到原来的检查方式
			if (textStatus === 'error' && xhr.status === 404) {
				console.warn('CheckOptimisticLockAndEditStatus 服务不存在，回退到原来的乐观锁检查');
				HotUtil._fallbackOptimisticLockCheck(treeNode, actionName, callback);
			} else {
				console.error('乐观锁检查请求失败:', textStatus, errorThrown);
				callback({
					success: false,
					errorMsg: "无法连接服务器检查数据状态: " + (textStatus || "未知错误")
				});
			}
		});
	},

	/**
	 * 回退到原来的乐观锁检查方式（向后兼容）
	 * @param {Object} treeNode 当前节点
	 * @param {String} actionName 操作名称
	 * @param {Function} callback 回调函数
	 */
	_fallbackOptimisticLockCheck: function (treeNode, actionName, callback) {
		// 使用原来的乐观锁检查服务
		twxAjax(THING, 'CheckOptimisticLock', {
			id: treeNode.ID,
			expectedSaveTime: HotUtil.currentSaveTime
		}, true, function (res) {
			try {
				if (res && res.success) {
					callback({ success: true, errorMsg: "" });
				} else {
					var errorMsg = "";
					if (res && res.conflict) {
						errorMsg = "数据已被其他用户修改，无法" + actionName + "。请刷新页面获取最新数据后重试。";
					} else {
						errorMsg = (res && res.msg) || "数据状态检查失败";
					}
					callback({ success: false, errorMsg: errorMsg });
				}
			} catch (e) {
				console.error('处理回退乐观锁检查结果时出错:', e);
				callback({
					success: false,
					errorMsg: "数据状态检查出错: " + e.message
				});
			}
		}, function (xhr, textStatus, errorThrown) {
			console.error('回退乐观锁检查请求失败:', textStatus, errorThrown);
			callback({
				success: false,
				errorMsg: "无法连接服务器检查数据状态: " + (textStatus || "未知错误")
			});
		});
	},
	/**
	 * 调整上下文菜单位置，防止超出视口边界（16.0.1版本优化）
	 */
	adjustContextMenuPosition: function () {
		try {
			// 使用短延迟确保菜单完全渲染
			setTimeout(function () {
				var menuElement = document.querySelector('.htContextMenu');
				if (!menuElement || !menuElement.offsetParent) {
					return; // 菜单不存在或不可见
				}

				// 检查菜单是否已经调整过，避免重复处理
				var currentStyle = window.getComputedStyle(menuElement);
				if (currentStyle.position === 'fixed') {
					return;
				}

				var menuRect = menuElement.getBoundingClientRect();
				var viewportWidth = window.innerWidth || document.documentElement.clientWidth;
				var viewportHeight = window.innerHeight || document.documentElement.clientHeight;
				var margin = 10; // 统一边距

				var needsAdjustment = false;
				var newTop = menuRect.top;
				var newLeft = menuRect.left;

				// 检查右边界溢出
				if (menuRect.right > viewportWidth - margin) {
					newLeft = Math.max(margin, viewportWidth - menuRect.width - margin);
					needsAdjustment = true;
				}

				// 检查底部边界溢出
				if (menuRect.bottom > viewportHeight - margin) {
					newTop = Math.max(margin, viewportHeight - menuRect.height - margin);
					needsAdjustment = true;
				}

				// 确保不超出左边界
				if (newLeft < margin) {
					newLeft = margin;
					needsAdjustment = true;
				}

				// 确保不超出顶部边界
				if (newTop < margin) {
					newTop = margin;
					needsAdjustment = true;
				}

				// 只在真正需要时才调整位置
				if (needsAdjustment) {
					// 应用新位置
					menuElement.style.position = 'fixed';
					menuElement.style.top = newTop + 'px';
					menuElement.style.left = newLeft + 'px';
					menuElement.style.zIndex = '9999';
				}

			}, 10); // 减少延迟时间

		} catch (error) {
			console.warn('调整上下文菜单位置时出错:', error);
		}
	},

	/**
	 * 检查选中的单元格中是至少有一个单元格是锁定状态
	 * @param {Object} thisHot
	 */
	atLeastOneReadOnly: function (thisHot) {
		var atLeastOneReadOnly = false;
		HotUtil.eachSelectedRange(thisHot, function (r, c) {
			if (thisHot.getCellMeta(r, c).readOnly) {
				atLeastOneReadOnly = true;
				return true;
			} else {
				return false;
			}
		});
		return atLeastOneReadOnly;
	},
	/**
	 * 检查选中的单元格中全是锁定状态
	 * @param {Object} thisHot
	 */
	allReadOnly: function (thisHot) {
		var allReadOnly = true;
		HotUtil.eachSelectedRange(thisHot, function (r, c) {
			if (!thisHot.getCellMeta(r, c).readOnly) {
				allReadOnly = false;
				return true;
			} else {
				return false;
			}
		});
		return allReadOnly;
	},
	/**
	 * 遍历选中的每一个单元格
	 * @param {Object} thisHot
	 * @param {Object} eachFn
	 */
	eachSelectedRange: function (thisHot, eachFn) {
		var srs = HotUtil.getSelectedRange();
		HotUtil.eachArrays(srs, thisHot, eachFn);
	},
	getSelectedRange: function () {
		var selectedRange = window.hot.getSelectedRange();
		if (selectedRange) {
			for (var i = 0; i < selectedRange.length; i++) {
				var selectedRangeElement = selectedRange[i];
				var from = selectedRangeElement.from;
				if (from.row == -1) {
					from.row = 0;
				}
				if (from.col == -1) {
					from.col = 0;
				}

				var to = selectedRangeElement.to;
				if (to.row == -1) {
					to.row = 0;
				}
				if (to.col == -1) {
					to.col = 0;
				}
			}
			return selectedRange;
		} else {
			return [];
		}
	},
	eachArrays: function (arrays, thisHot, eachFn) {
		for (var i = 0; i < arrays.length; i++) {
			var sr = arrays[i];
			for (var r = sr.from.row; r <= sr.to.row; r++) {
				for (var c = sr.from.col; c <= sr.to.col; c++) {
					if (r > -1 && c > -1) {
						if (thisHot.getCellMeta(r, c).hidden) {
							continue;
						}
						var isBreak = eachFn(r, c);
						if (isBreak) {
							break;
						}
					}
				}
			}
		}
	},
	/**
	 * 禁用列相关的右键操作
	 * @param {Object} thisHot
	 */
	disableColContextMenu: function (thisHot) {
		//选中的单元格是否存在锁定的
		var hasLock = HotUtil.atLeastOneReadOnly(thisHot);
		if (!hasLock) {
			//检测选中单元格同列是否存在锁定的
			var selectedRange = HotUtil.getSelectedRange()[0];
			for (var row = 0; row < hot.countRows(); row++) {
				for (var col = selectedRange.from.col; col <= selectedRange.to.col; col++) {
					if (col > -1 && row > -1) {
						if (thisHot.getCellMeta(row, col).readOnly) {
							hasLock = true;
							break;
						}
					}
				}
			}
		}
		return hasLock;
	},
	/**
	 * 自动填充自动增加序列
	 * @param {Object} str
	 */
	incrementString: function (str, type) {
		if (str == "" || str == null || str == " ") {
			return "";
		}
		var regex = /(\d+)(?!.*\d)/;
		var match = regex.exec(str);
		if (match) {
			var num = parseInt(match[1]);
			var incremented = (num + 1).toString();
			if (type == 'subtract') {
				incremented = (num - 1).toString();
			}
			return str.slice(0, match.index) + incremented.padStart(match[1].length, '0') + str.slice(match
				.index + match[1].length);
		} else {
			return str;
		}
	},
	/**
	 * 编辑表格初始化表
	 * @param {Object} saveData 表格数据
	 * @param {Object} afterChange 更改后的处理事件
	 */
	renderHot: function (saveData, treeNode, tableHeader, afterChange) {
		var fixedRowsTop = HotUtil.getTableHeader(tableHeader).max;
		var initData = [];
		var merged = [];
		var metas = [];
		var colWidths = [];
		if (saveData) {
			saveData = JSON.parse(saveData);
			initData = saveData.tableData;
			merged = saveData.merged;
			colWidths = saveData.colWidths || void 0;
			metas = saveData.meta || [];
		} else {
			var initColNum = 12,
				initRowNum = 15;
			for (var r = 0; r < initRowNum; r++) {
				var rows = [];
				for (var c = 0; c < initColNum; c++) {
					rows.push(null);
				}
				initData.push(rows);
			}
		}

		function myCellRenderer(instance, td, row, col, prop, value, cellProperties) {
			Handsontable.renderers.TextRenderer.apply(this, arguments);
			const stringifiedValue = Handsontable.helper.stringify(value);

			var eles = cellProperties.eles;
			var isText = true;
			var signImgs = [];
			var photoNums = [];
			if (Array.isArray(eles)) {
				for (var i = 0; i < eles.length; i++) {
					isText = false;
					var ele = eles[i];
					var eleType = ele.type;
					if (eleType == "sign") {
						var src = "/File" + ele.src;
						var date = ele.date;
						var $img = $('<br><img type="sign" src="' + src + '" class="sign-img" date="' + date +
							'"><br><span>' + date + '</span>');
						signImgs.push($img);
					} else if (eleType == "photo") {
						photoNums.push(ele.photoShowNum);
					}
				}
			}

			// 处理签署人员信息显示
			var signUsersText = '';
			// 只有签署框类型的单元格才显示签署人员信息
			if (cellProperties.signUsers && Array.isArray(cellProperties.signUsers) &&
				cellProperties.signUsers.length > 0 &&
				cellProperties.className && cellProperties.className.indexOf('sign-box') !== -1) {
				// 过滤已签名的用户
				var unsignedUsers = [];
				for (var j = 0; j < cellProperties.signUsers.length; j++) {
					var user = cellProperties.signUsers[j];
					var isSigned = false;

					// 检查该用户是否已签名
					if (Array.isArray(eles)) {
						for (var k = 0; k < eles.length; k++) {
							var ele = eles[k];
							if (ele.type === "sign" &&
								ele.signName === user.user_fullname &&
								ele.workno === user.user_workno) {
								isSigned = true;
								break;
							}
						}
					}

					// 如果用户未签名，添加到显示列表
					if (!isSigned) {
						unsignedUsers.push(user.user_fullname);
					}
				}

				// 只有存在未签名用户时才显示
				if (unsignedUsers.length > 0) {
					signUsersText = unsignedUsers.join('，');
				}
			}

			if (!isText || signUsersText) {
				$(td).empty();

				// 显示原始数据
				if (stringifiedValue) {
					$(td).append(stringifiedValue);
				}

				// 显示签名图片
				for (var i = 0; i < signImgs.length; i++) {
					$(td).append(signImgs[i]);
				}

				// 显示照片数量
				if (photoNums.length > 0) {
					$(td).append(HotUtil.dealImgNumShow(photoNums));
				}

				// 显示签署人员信息
				if (signUsersText) {
					var $signUsersDiv = $('<div style="margin-top: 5px; padding: 2px 5px; background-color: #f0f8ff; border-left: 3px solid #1E9FFF; font-size: 12px; color: #666;">' + signUsersText + '</div>');
					$(td).append($signUsersDiv);
				}
			}
		}

		var container = document.getElementById('handsontable');

		// 增强错误处理：检查容器是否存在
		if (!container) {
			console.error('Handsontable容器元素未找到');
			layer.alert('表格容器初始化失败，请刷新页面重试', {
				icon: 2,
				title: '初始化错误'
			});
			return;
		}

		// 优化配置：利用16.0.1版本的性能改进
		var handsontableConfig = {
			data: initData,
			fixedRowsTop: fixedRowsTop,
			mergeCells: merged,
			rowHeaders: true,
			colHeaders: true,
			rowHeights: 40,
			dropdownMenu: false,
			customBorders: true,
			comments: true,
			colWidths: colWidths,
			fillHandle: true,
			renderer: myCellRenderer,
			language: 'zh-CN',
			licenseKey: 'non-commercial-and-evaluation',
			className: 'htMiddle htCenter',
			manualColumnResize: true,
			manualRowResize: true,
			afterChange: afterChange,
			contextMenu: {
				callback: function (key, selection, clickEvent) {
					// 增强错误处理
					try {
						console.log(key, selection, clickEvent);
					} catch (error) {
						console.error('上下文菜单回调错误:', error);
					}
				},
				items: HotUtil.getContextItems(treeNode)
			}
		};

		// 增强错误处理：Handsontable实例化
		try {
			window.hot = new Handsontable(container, handsontableConfig);

			// 16.0.1版本上下文菜单定位优化：使用afterContextMenuShow钩子
			window.hot.addHook('afterContextMenuShow', function () {
				HotUtil.adjustContextMenuPosition();
			});
		} catch (error) {
			console.error('Handsontable初始化失败:', error);
			layer.alert('表格初始化失败: ' + error.message, {
				icon: 2,
				title: '初始化错误'
			});
			return;
		}

		// 增强错误处理：afterAutofill hook
		window.hot.addHook('afterAutofill', function (fillData, sourceRange, targetRange, direction) {
			try {
				//只有选中一个单元格自动填充才会增加序列
				if ((sourceRange.from.row == sourceRange.to.row) && (sourceRange.from.col == sourceRange.to.col)) {
					var fromRow = targetRange.from.row;
					var fromCol = targetRange.from.col;
					var toRow = targetRange.to.row;
					var toCol = targetRange.to.col;

					if (direction == "down" || direction == "right") {
						for (var r = fromRow; r <= toRow; r++) {
							for (var c = fromCol; c <= toCol; c++) {
								try {
									if (window.hot.getCellMeta(r, c).readOnly != true) {
										var nextValue = "";
										if (direction == "down") {
											nextValue = window.hot.getDataAtCell(r - 1, c);
										} else if (direction == "right") {
											nextValue = window.hot.getDataAtCell(r, c - 1);
										}
										window.hot.setDataAtCell(r, c, HotUtil.incrementString(nextValue, "add"));
									}
								} catch (cellError) {
									console.warn('处理单元格(' + r + ',' + c + ')时出错:', cellError);
								}
							}
						}
					} else {
						for (var r = toRow; r >= fromRow; r--) {
							for (var c = toCol; c >= fromCol; c--) {
								try {
									if (window.hot.getCellMeta(r, c).readOnly != true) {
										var nextValue = "";
										if (direction == "up") {
											nextValue = window.hot.getDataAtCell(r + 1, c);
										} else if (direction == "left") {
											nextValue = window.hot.getDataAtCell(r, c + 1);
										}
										window.hot.setDataAtCell(r, c, HotUtil.incrementString(nextValue, 'subtract'));
									}
								} catch (cellError) {
									console.warn('处理单元格(' + r + ',' + c + ')时出错:', cellError);
								}
							}
						}
					}
				}
			} catch (error) {
				console.error('afterAutofill hook执行错误:', error);
			}
		});

		// 增强错误处理：批量设置单元格元数据
		// 16.0.1版本优化：使用批量操作提高性能
		try {
			// hot.suspendRender(); // 16.0.1版本中可选择性使用
			for (var m = 0; m < metas.length; m++) {
				var meta = metas[m];
				if (meta && typeof meta.row === 'number' && typeof meta.col === 'number') {
					try {
						//将表头添加背景颜色
						if (meta.row < fixedRowsTop) {
							if (meta.className) {
								meta.className = meta.className + " td-bg";
							} else {
								meta.className = "td-bg";
							}
						}

						// 安全地设置单元格元数据
						if (meta.className) {
							window.hot.setCellMeta(meta.row, meta.col, 'className', meta.className);
						}
						if (meta.readOnly) {
							window.hot.setCellMeta(meta.row, meta.col, 'readOnly', meta.readOnly);
						}
						if (meta.eles) {
							window.hot.setCellMeta(meta.row, meta.col, 'eles', meta.eles);
						}
						if (meta.comment) {
							window.hot.setCellMeta(meta.row, meta.col, 'comment', meta.comment);
						}
						if (meta.signUsers) {
							window.hot.setCellMeta(meta.row, meta.col, 'signUsers', meta.signUsers);
						}
					} catch (metaError) {
						console.warn('设置单元格(' + meta.row + ',' + meta.col + ')元数据时出错:', metaError);
					}
				}
			}
			// hot.resumeRender(); // 16.0.1版本中可选择性使用

			// 增强错误处理：渲染操作
			window.hot.render();
		} catch (error) {
			console.error('设置单元格元数据或渲染时出错:', error);
			layer.alert('表格渲染失败，请刷新页面重试', {
				icon: 2,
				title: '渲染错误'
			});
		}
	},
	/**
	 * 获取右键菜单
	 */
	getContextItems: function (treeNode) {
		var obj = {
			row_above: {
				name: '上方插入行'
			},
			row_below: {
				name: '下方插入行'
			},
			sp1: '---------',
			col_left: {
				name: '左方插入列',
				disabled() {
					return HotUtil.disableColContextMenu(this);
				}
			},
			col_right: {
				name: '右方插入列',
				disabled() {
					return HotUtil.disableColContextMenu(this);
				}
			},
			sp2: '---------',
			remove_row: {
				name: '移除该行',
				disabled() {
					//选中的单元格是否存在锁定的
					var hasLock = HotUtil.atLeastOneReadOnly(this);
					if (!hasLock) {
						//检测选中单元格同行是否存在锁定的
						var selectedRange = HotUtil.getSelectedRange()[0];
						for (var row = selectedRange.from.row; row <= selectedRange.to
							.row; row++) {
							for (var col = 0; col < hot.countCols(); col++) {
								if (col > -1 && row > -1) {
									if (this.getCellMeta(row, col).readOnly) {
										hasLock = true;
										break;
									}
								}
							}
						}
					}
					return hasLock;
				}
			},
			remove_col: {
				name: '移除该列',
				disabled() {
					return HotUtil.disableColContextMenu(this);
				}
			},
			sp3: '---------',
			alignment: {
				name: '对齐',
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			// borders: {
			// 	name: '边框'
			// },
			fontSize: {
				name() {
					return '字体大小';
				},
				submenu: {
					items: HotUtil.fontSizeItems()
				},
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			fontColor: {
				name() {
					return '字体颜色';
				},
				submenu: {
					items: HotUtil.fontColorItems()
				},
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			mergeCells: {
				name: function name() {
					var sel = this.getSelectedLast();
					if (sel) {
						var info = this.getPlugin('MergeCells').mergedCellsCollection.get(
							sel[0], sel[1]);

						if (info.row === sel[0] && info.col === sel[1] && info.row + info
							.rowspan - 1 === sel[2] && info.col + info.colspan - 1 === sel[
							3]) {
							return "取消合并";
						}
					}
					return "合并";
				},
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			bold: { // Own custom option
				name() { // `name` can be a string or a function
					return '加粗'; // Name can contain HTML
				},
				callback(key, selection, clickEvent) { // Callback for specific option
					HotUtil.dealClass("c-bold", "add");
				},
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			cancelBold: { // Own custom option
				name() { // `name` can be a string or a function
					return '取消加粗'; // Name can contain HTML
				},
				callback(key, selection, clickEvent) { // Callback for specific option
					HotUtil.dealClass("c-bold", "cancel");
				},
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			commentsAddEdit: {
				name() {
					// 16.0.1版本兼容性修复：使用getCommentAtCell替代checkSelectionCommentsConsistency
					var range = this.getSelectedRangeLast();
					if (range && range.highlight && range.highlight.isCell()) {
						var commentsPlugin = this.getPlugin("Comments");
						if (commentsPlugin && commentsPlugin.getCommentAtCell(range.highlight.row, range.highlight.col)) {
							return "编辑批注";
						}
					}

					return "添加批注";
				},
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			commentsRemove: {
				name() {
					return "删除批注";
				},
				disabled() {
					return HotUtil.atLeastOneReadOnly(this);
				}
			},
			make_read_only: {
				name() {
					var label = "锁定";
					if (HotUtil.atLeastOneReadOnly(this)) {
						label = "解除锁定";
					}
					return label;
				},
				callback(key, selection, clickEvent) {
					var _this = this;
					var atLeastOneReadOnly = HotUtil.atLeastOneReadOnly(_this);

					var label = "锁定";
					if (atLeastOneReadOnly) {
						label = "解除锁定";
					}
					var log = {};
					log.operation = label + "单元格";
					log.tablePid = treeNode.PID;
					log.tableId = treeNode.ID;
					var locations = "";
					var tdCount = 0;
					HotUtil.eachSelectedRange(_this, function (r, c) {
						locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
						tdCount++;
						_this.setCellMeta(r, c, 'readOnly', !atLeastOneReadOnly);
						if (atLeastOneReadOnly) {
							//解除锁定需要清除签名
							var meta = _this.getCellMeta(r, c);
							var eles = meta.eles || [];
							var newEles = [];
							for (var e = 0; e < eles.length; e++) {
								if (eles[e].type != 'sign') {
									newEles.push(eles[e]);
								}
							}
							_this.setCellMeta(r, c, 'eles', newEles);
						}
					});
					locations = locations.substring(0, locations.length - 1);
					log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中" + label +
						"了" + tdCount + "个单元格，分别是" + locations;
					log.reqResult = 1;
					addConfirmLog(log);
					_this.render();
				},
				disabled() {
					if (funcIdent == "tpl") {
						return true;
					} else {
						if (contains(sessionStorage.getItem('funcids').split(','), 'func-' +
							funcIdent + '-unlock-row')) {
							return false;
						} else {
							return HotUtil.atLeastOneReadOnly(this);
						}
					}

				}
			},
			setSignTd: {
				name() {
					return '上传签章';
				},
				callback(key, selection, clickEvent) {

					var log = {};
					log.operation = "上传签章";
					log.tablePid = treeNode.PID;
					log.tableId = treeNode.ID;
					var content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中的";
					var locations = "";
					var _this = this;
					var srs = HotUtil.getSelectedRange();
					HotUtil.uploadSignLayer(function (src, date, signName) {
						HotUtil.eachArrays(srs, _this, function (r, c) {
							locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
							var eles = _this.getCellMeta(r, c).eles || [];
							eles.push({
								type: "sign",
								date: date,
								signName: signName,
								workno: sessionStorage.getItem('workno'),
								src: src
							});
							_this.setCellMeta(r, c, "eles", eles);
							//添加到签名数据库中
							twxAjax(THING, "AddOneSign", {
								img: src,
								creator: sessionStorage.getItem(
									"username"),
								id: treeNode.ID
							});
						});
						locations = locations.substring(0, locations.length - 1);
						content = content + locations;
						content += "上传了签章，签章日期为：" + date + "，签章路径为：" + src;
						log.content = content;
						log.reqResult = 1;
						addConfirmLog(log);
						_this.render();
					});
				},
				disabled() {
					if (funcIdent == "tpl") {
						return true;
					} else {
						return !HotUtil.allReadOnly(this);
					}
				}
			},
			setImgTd: {
				name() {
					return '上传图片';
				},
				callback(key, selection, clickEvent) {
					var _this = this;
					var srs = HotUtil.getSelectedRange();
					var log = {};
					log.operation = "上传图片";
					log.tablePid = treeNode.PID;
					log.tableId = treeNode.ID;
					log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中的";
					var locations = "";
					HotUtil.insertImageLayer(treeNode, function (datas, layerIndex) {
						HotUtil.eachArrays(srs, _this, function (r, c) {
							locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
							var eles = _this.getCellMeta(r, c).eles || [];
							for (var i = 0; i < datas.length; i++) {
								var rowData = datas[i];
								if (rowData.success) {
									var params = {};
									params.id = rowData.index;
									params.photoPath = rowData.photoPath;
									params.photoFormat = rowData.photoFormat;
									params.photoName = rowData.photoName;
									params.photoShowNum = rowData.photoShowNum;
									params.type = "photo";
									params.src = "/File" + rowData.photoPath;
									params.class = "sign-img photo";
									params.date = layui.util.toDateString(
										new Date(), 'yyyy-MM-dd');
									eles.push(params);
								}
							}
							_this.setCellMeta(r, c, "eles", eles);
						});
						locations = locations.substring(0, locations.length - 1);
						var photoText = "[";
						var photoCount = 0;
						for (var i = 0; i < datas.length; i++) {
							var rowData = datas[i];
							if (rowData.success) {
								photoText += "{照片名称：" + (rowData.photoName + "." +
									rowData.photoFormat) +
									"，路径：" + rowData.photoPath + "，大小：" + rowData
										.photoSize +
									"，编号：" + rowData.photoShowNum + "},";
								//添加到图片数据库中
								twxAjax(THING, "AddOnePhoto", {
									data: {
										id: treeNode.ID,
										photoName: rowData.photoName,
										photoPath: rowData.photoPath,
										photoFormat: rowData.photoFormat,
										photoNumber: rowData.photoNumber,
										photoSize: rowData.photoSize,
										creator: sessionStorage.getItem(
											"username")
									}
								});
								photoCount++;
							}
						}
						photoText = photoText.substring(0, photoText.length - 1);
						photoText += "]";
						log.content = log.content + locations + "上传了" + photoCount +
							"张图片,分别是" + photoText;
						log.reqResult = 1;
						addConfirmLog(log);
						_this.render();
						layer.close(layerIndex);
					});
				},
				disabled() {
					if (funcIdent == "tpl") {
						return true;
					} else {
						return HotUtil.atLeastOneReadOnly(this);
					}
				}
			},
			viewImgTd: {
				name() {
					return '查看图片';
				},
				callback(key, selection, clickEvent) {
					var _this = this;
					var row = selection[0].start.row;
					var col = selection[0].start.col;
					var cellMeta = this.getCellMeta(row, col);
					var eles = cellMeta.eles;
					var readOnly = cellMeta.readOnly || false;
					var imgs = [];
					for (var i = 0; i < eles.length; i++) {
						if (eles[i]['type'] == 'photo') {
							imgs.push(eles[i]);
						}
					}

					var delImgs = [];
					HotUtil.viewImageLayer(imgs, !readOnly, function () {
						if ($(".picView-magnify-list").data("delete")) {
							var newEles = [];
							for (var i = 0; i < eles.length; i++) {
								if (eles[i].type == 'photo') {
									var isDel = false;
									for (var j = 0; j < delImgs.length; j++) {
										if (eles[i].id == delImgs[j]) {
											isDel = true;
											break;
										}
									}
									if (!isDel) {
										newEles.push(eles[i]);
									}
								} else {
									newEles.push(eles[i]);
								}
							}
							_this.setCellMeta(row, col, 'eles', newEles);
							_this.render();
						}
					}, function ($i, $ul) {
						var photoId = $i.attr("photo-id");
						$i.parent().parent().remove();
						$ul.data("delete", true);
						delImgs.push(photoId);
					});

				},
				disabled() {
					var disable = true;
					var selectedRange = HotUtil.getSelectedRange();
					if (selectedRange.length == 1) {
						var from = selectedRange[0].from;
						var to = selectedRange[0].to;
						var cellMeta = this.getCellMeta(from.row, from.col);
						var colspan = cellMeta['colspan'] || 1;
						var rowspan = cellMeta['rowspan'] || 1;
						var isSingleCell = false;
						if (from.row == to.row && from.col == to.col) {
							isSingleCell = true;
						} else {
							if ((from.row + rowspan - 1) == to.row && (from.col + colspan -
								1) == to.col) {
								isSingleCell = true;
							}
						}

						if (isSingleCell) {
							if (cellMeta.eles && cellMeta.eles.length > 0) {
								var eles = cellMeta.eles;
								for (var i = 0; i < eles.length; i++) {
									if (eles[i]['type'] == 'photo') {
										disable = false;
										break;
									}
								}
							}
						}
					}
					return disable;
				}
			},
		};
		obj['setSignBox'] = {
			name: function () {
				// 获取选中区域
				var selection = this.getSelected() || [];
				if (selection.length === 0) {
					return '设定为签署框';
				}

				// 检查选中区域是否包含已设置签署框的单元格
				for (var index = 0; index < selection.length; index += 1) {
					var [row1, column1, row2, column2] = selection[index];
					var startRow = Math.min(row1, row2);
					var endRow = Math.max(row1, row2);
					var startCol = Math.min(column1, column2);
					var endCol = Math.max(column1, column2);

					for (var row = startRow; row <= endRow; row++) {
						for (var col = startCol; col <= endCol; col++) {
							var currentClassName = this.getCellMeta(row, col).className || '';
							if (currentClassName.indexOf('sign-box') !== -1) {
								return '取消设定签署框';
							}
						}
					}
				}

				return '设定为签署框';
			},
			callback: function (key, selection) {
				HotUtil.setSignBox(this, selection);
			},
			disabled() {
				// 如果表格状态不是编辑状态，则禁用此选项
				// 不管选中的单元格是否锁定都可以操作
				if (treeNode && treeNode.TABLE_STATUS !== 'edit') {
					return true;
				}
				return false;
			}
		}
		obj['setSignUsers'] = {
			name: function () {
				return '设定签署人员';
			},
			callback: function (key, selection) {
				HotUtil.setSignUsers(this, selection);
			},
			disabled() {
				// 如果表格状态不是编辑状态，则禁用此选项
				if (treeNode && treeNode.TABLE_STATUS !== 'edit') {
					return true;
				}
				// 检查选中区域是否包含签署框类型的单元格
				var selection = this.getSelected() || [];
				if (selection.length === 0) {
					return true;
				}

				var hasSignBox = false;
				for (var index = 0; index < selection.length; index += 1) {
					var [row1, column1, row2, column2] = selection[index];
					var startRow = Math.min(row1, row2);
					var endRow = Math.max(row1, row2);
					var startCol = Math.min(column1, column2);
					var endCol = Math.max(column1, column2);

					for (var row = startRow; row <= endRow; row++) {
						for (var col = startCol; col <= endCol; col++) {
							var currentClassName = this.getCellMeta(row, col).className || '';
							if (currentClassName.indexOf('sign-box') !== -1) {
								hasSignBox = true;
								break;
							}
						}
						if (hasSignBox) break;
					}
					if (hasSignBox) break;
				}

				return !hasSignBox; // 只有当选中区域包含签署框时才启用
			}
		}
		if (HotUtil.workType !== '') {
			obj['postType'] = {
				name() {
					return '设置签名岗位';
				},
				submenu: {
					items: HotUtil.postTypeItems()
				},
				disabled() {
					if (HotUtil.getSelectedRange().length === 0) {
						return true;
					} else {
						return false;
					}
				}
			}
		}

		return obj;
	},

	/**
	 * 设定签署人员
	 * @param {Object} hot Handsontable实例
	 * @param {Array} selection 选择区域
	 */
	setSignUsers: function (hot, selection) {
		if (!selection || selection.length === 0) {
			return;
		}

		// 获取选中区域的起始和结束行列
		var startRow = selection[0].start.row;
		var startCol = selection[0].start.col;
		var endRow = selection[0].end.row;
		var endCol = selection[0].end.col;

		// 检查选中区域是否包含签署框类型的单元格
		var hasSignBox = false;
		for (var row = startRow; row <= endRow; row++) {
			for (var col = startCol; col <= endCol; col++) {
				var currentClassName = hot.getCellMeta(row, col).className || '';
				if (currentClassName.indexOf('sign-box') !== -1) {
					hasSignBox = true;
					break;
				}
			}
			if (hasSignBox) break;
		}

		if (!hasSignBox) {
			layer.alert('请先选择签署框类型的单元格！', {
				icon: 2
			});
			return;
		}

		// 打开人员选择弹窗
		HotUtil.openSignUsersDialog(hot, selection);
	},

	/**
	 * 设置或取消设置选中单元格为签署框
	 * @param {Object} hot Handsontable实例
	 * @param {Array} selection 选择区域
	 */
	setSignBox: function (hot, selection) {
		if (!selection || selection.length === 0) {
			return;
		}

		// 获取选中区域的起始和结束行列
		var startRow = selection[0].start.row;
		var startCol = selection[0].start.col;
		var endRow = selection[0].end.row;
		var endCol = selection[0].end.col;

		// 检查选中区域是否包含已设置签署框的单元格
		var hasSignBox = false;
		var operation = '设置';

		// 首先检查是否有单元格已设置为签署框
		for (var row = startRow; row <= endRow; row++) {
			for (var col = startCol; col <= endCol; col++) {
				var currentClassName = hot.getCellMeta(row, col).className || '';
				if (currentClassName.indexOf('sign-box') !== -1) {
					hasSignBox = true;
					break;
				}
			}
			if (hasSignBox) break;
		}

		// 根据检查结果决定是设置还是取消设置
		for (var row = startRow; row <= endRow; row++) {
			for (var col = startCol; col <= endCol; col++) {
				var currentClassName = hot.getCellMeta(row, col).className || '';

				if (hasSignBox) {
					// 如果有签署框，则取消设置（但不解除锁定状态）
					if (currentClassName.indexOf('sign-box') !== -1) {
						var newClassName = currentClassName.replace(/\s*sign-box\s*/g, ' ').trim();
						hot.setCellMeta(row, col, 'className', newClassName);
						operation = '取消设置';
						// 取消设定签署框后不解除锁定状态，保持原有的readOnly属性
					}
				} else {
					// 如果没有签署框，则设置并自动锁定
					if (currentClassName.indexOf('sign-box') === -1) {
						var newClassName = currentClassName + ' sign-box';
						hot.setCellMeta(row, col, 'className', newClassName.trim());
						// 设定为签署框时自动锁定单元格
						hot.setCellMeta(row, col, 'readOnly', true);
					}
				}
			}
		}

		// 重新渲染表格
		hot.render();

		// 显示提示
		layer.msg('已' + operation + '签署框，请保存表格');
	},

	/**
	 * 打开签署人员选择弹窗
	 * @param {Object} hot Handsontable实例
	 * @param {Array} selection 选择区域
	 */
	openSignUsersDialog: function (hot, selection) {
		// 将selectedUsers存储在全局变量中，以便在弹窗中访问
		window.currentSelectedUsers = []; // 存储选中的用户
		var currentCellMeta = null; // 当前单元格的meta信息

		// 获取当前选中单元格的签名信息（用于回显）
		if (selection && selection.length > 0) {
			var row = selection[0].start.row;
			var col = selection[0].start.col;
			currentCellMeta = hot.getCellMeta(row, col);
			if (currentCellMeta.signUsers) {
				window.currentSelectedUsers = JSON.parse(JSON.stringify(currentCellMeta.signUsers));
			}
		}

		const dialogContent = `
			<div style="padding: 15px; height: 100%;">
				<div style="display: flex; height: 100%;">
					<div style="width: 50%; padding-right: 10px;">
						<div style="margin-bottom: 15px; height: 65px;">
							<h4 style="margin: 0 0 10px 0;">全部用户列表</h4>
							<input type="text" id="userFullnameSearch" placeholder="请输入用户全名进行搜索" class="layui-input" style="width: 210px; float: left;">
							<button type="button" id="searchUserBtn" class="layui-btn" style="float: left; margin-left: 20px;">搜索</button>
						</div>
						<table id="allUserTableContainer"></table>
					</div>
					<div style="width: 50%; padding-left: 10px;">
						<div style="margin-bottom: 15px; height: 65px;">
							<h4 style="margin: 0 0 10px 0;">已选用户列表</h4>
						</div>
						<table id="selectedUserTableContainer"></table>
					</div>
				</div>
				<script type="text/html" id="addUserToolbar">
					<a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="add">➔</a>
				</script>
				<script type="text/html" id="removeUserToolbar">
					<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="remove">✖</a>
				</script>
			</div>
		`;

		layer.open({
			title: '设定签署人员',
			type: 1,
			area: ['1000px', '680px'],
			content: dialogContent,
			btn: ['确定', '取消'],
			yes: function (index) {
				// 确定按钮回调
				HotUtil.saveSignUsers(hot, selection, window.currentSelectedUsers);
				layer.close(index);
			},
			btn2: function () {
				// 取消按钮回调
				return true;
			},
			success: function () {
				// 弹窗打开成功后的回调
				HotUtil.initSignUsersDialog(window.currentSelectedUsers);
			}
		});
	},

	/**
	 * 初始化签署人员选择弹窗
	 * @param {Array} selectedUsers 已选择的用户列表
	 */
	initSignUsersDialog: function (selectedUsers) {
		// 初始化左侧全部用户表格
		HotUtil.renderAllUserTable();

		// 初始化右侧已选用户表格
		HotUtil.renderSelectedUserTable();

		// 绑定搜索事件
		$('#searchUserBtn').on('click', function () {
			var fullname = $('#userFullnameSearch').val();
			table.reload('allUserTable', {
				where: {
					fullname: fullname
				}
			});
		});

		// 绑定回车搜索事件
		$('#userFullnameSearch').on('keypress', function (e) {
			if (e.which === 13) {
				$('#searchUserBtn').click();
			}
		});

		// 监听左侧表格工具栏事件（添加用户）
		table.on('tool(allUserTable)', function (obj) {
			if (obj.event === 'add') {
				HotUtil.addUserToSelected(obj.data);
			}
		});

		// 监听右侧表格工具栏事件（删除用户）
		table.on('tool(selectedUserTable)', function (obj) {
			if (obj.event === 'remove') {
				HotUtil.removeUserFromSelected(obj.data.user_id);
			}
		});
	},



	/**
	 * 渲染全部用户表格（左侧）
	 */
	renderAllUserTable: function () {
		table.render({
			elem: '#allUserTableContainer',
			id: 'allUserTable',
			url: getUrl('Thing.Fn.SystemManagement', 'QuerySignUserPage'),
			height: 472,
			page: {
				layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'],
				groups: 1,
				first: false,
				last: false
			},
			cols: [[
				{ type: 'numbers', title: '序号', width: 60 },
				{ field: 'USER_ID', title: 'ID', hide: true },
				{ field: 'USER_NAME', title: '用户名' },
				{ field: 'USER_FULLNAME', title: '全名', width: 100 },
				{ field: 'USER_WORKNO', title: '工号', width: 108 },
				{ field: 'operation', align: 'center', title: '操作', width: 60, toolbar: '#addUserToolbar' }
			]]
		});
	},

	/**
	 * 渲染已选用户表格（右侧）
	 */
	renderSelectedUserTable: function () {
		table.render({
			elem: '#selectedUserTableContainer',
			id: 'selectedUserTable',
			data: window.currentSelectedUsers || [],
			height: 472,
			page: false,
			cols: [[
				{ type: 'numbers', title: '序号', width: 60 },
				{
					field: 'user_info', title: '用户信息', templet: function (d) {
						return d.user_fullname + '(' + d.user_workno + ')';
					}
				},
				{ field: 'operation', title: '操作', width: 60, toolbar: '#removeUserToolbar' }
			]]
		});
	},

	/**
	 * 添加用户到已选列表
	 * @param {Object} userData 用户数据
	 */
	addUserToSelected: function (userData) {
		var selectedUsers = window.currentSelectedUsers || [];

		// 检查工号是否已存在（去重）
		for (var i = 0; i < selectedUsers.length; i++) {
			if (selectedUsers[i].user_workno === userData.USER_WORKNO) {
				layer.msg('该用户已在选择列表中', { icon: 2 });
				return;
			}
		}

		// 添加用户到已选列表
		var user = {
			user_id: userData.USER_ID,
			user_fullname: userData.USER_FULLNAME,
			user_workno: userData.USER_WORKNO,
			user_name: userData.USER_NAME
		};
		selectedUsers.push(user);
		window.currentSelectedUsers = selectedUsers;

		// 刷新右侧表格
		table.reload('selectedUserTable', {
			data: selectedUsers
		});
	},

	/**
	 * 从已选列表移除用户
	 * @param {Number} userId 用户ID
	 */
	removeUserFromSelected: function (userId) {
		var selectedUsers = window.currentSelectedUsers || [];

		// 从数组中移除用户
		for (var i = selectedUsers.length - 1; i >= 0; i--) {
			if (selectedUsers[i].user_id === userId) {
				selectedUsers.splice(i, 1);
				break;
			}
		}
		window.currentSelectedUsers = selectedUsers;

		// 刷新右侧表格
		table.reload('selectedUserTable', {
			data: selectedUsers
		});
	},



	/**
	 * 保存签署人员信息
	 * @param {Object} hot Handsontable实例
	 * @param {Array} selection 选择区域
	 * @param {Array} selectedUsers 选中的用户列表
	 */
	saveSignUsers: function (hot, selection, selectedUsers) {
		if (!selection || selection.length === 0) {
			return;
		}

		// 获取选中区域的起始和结束行列
		var startRow = selection[0].start.row;
		var startCol = selection[0].start.col;
		var endRow = selection[0].end.row;
		var endCol = selection[0].end.col;

		// 为选中区域的每个单元格设置签名信息
		for (var row = startRow; row <= endRow; row++) {
			for (var col = startCol; col <= endCol; col++) {
				var currentClassName = hot.getCellMeta(row, col).className || '';
				if (currentClassName.indexOf('sign-box') !== -1) {
					// 存储完整的签名信息到meta对象
					hot.setCellMeta(row, col, 'signUsers', selectedUsers);
				}
			}
		}

		// 重新渲染表格
		hot.render();

		// 显示提示
		layer.msg('已设定签署人员，请保存表格');
	},
	/**
	 * 设置更为类型
	 * @param name
	 * @param value
	 */
	setPostType: function (postType) {
		var selected = HotUtil.getSelecteds();
		for (var i = 0; i < selected.length; i++) {
			var obj = selected[i];
			hot.setCellMeta(obj.row, obj.col, 'postType', postType);
		}
		hot.render();
	},
	/**
	 * 获取全部的签名岗位信息
	 */
	postTypeItems: function () {
		var items = [];
		for (var i = 0; i < HotUtil.postTypes.length; i++) {
			var obj = {};
			obj.key = 'postType:' + HotUtil.postTypes[i]['NAME'];
			obj.name = HotUtil.postTypes[i]['NAME'];
			obj.callback = function (key, selection, clickEvent) {
				HotUtil.setPostType(key.split(":")[1]);
			}
			items.push(obj);
		}
		return items;
	},
	fontSizeItems: function () {
		var items = [];
		for (var i = 12; i <= 30; i++) {
			var obj = {};
			var name = i + "";
			obj.key = 'fontSize:' + name;
			obj.name = name;
			obj.callback = function (key, selection, clickEvent) {
				HotUtil.dealClass("font-size-" + key.split(":")[1], "add");
			}
			items.push(obj);
		}
		return items;
	},
	/**
	 * 获取全部的字体颜色
	 * @returns {*[]}
	 */
	fontColorItems: function () {
		var items = [];
		var colors = [{
			color: 'purple',
			name: '紫色'
		}, {
			color: 'green',
			name: '绿色'
		}, {
			color: 'orange',
			name: '橙色'
		}, {
			color: 'deeppink',
			name: '粉色'
		}, {
			color: 'black',
			name: '黑色'
		}];
		for (var i = 0; i < colors.length; i++) {
			var obj = {};
			obj.key = 'fontColor:' + colors[i].color;
			obj.name = '<span class="font-color-' + colors[i].color + '">' + colors[i].name + '</span>';
			obj.callback = function (key, selection, clickEvent) {
				HotUtil.dealClass("font-color-" + key.split(":")[1], "add");
			}
			items.push(obj);
		}
		return items;
	},
	myGetCellsMeta: function () {
		if (!window.hot) return [];
		var meta = [];
		var colCount = window.hot.countCols();
		var rowCount = window.hot.countRows();
		for (var i = 0; i < rowCount; i++) {
			for (var j = 0; j < colCount; j++) {
				var cellMeta = window.hot.getCellMeta(i, j);
				if (cellMeta.className) {
					cellMeta.className = cellMeta.className.replaceAll("td-bg", "").trim();
				}
				meta.push(cellMeta);
			}
		}
		return meta;
	},
	getSelecteds: function () {
		if (!window.hot) return [];
		var selected = window.hot.getSelected() || [];
		var arr = [];
		// hot.suspendRender();
		for (var index = 0; index < selected.length; index += 1) {
			var [row1, column1, row2, column2] = selected[index];
			var startRow = Math.max(Math.min(row1, row2), 0);
			var endRow = Math.max(row1, row2);
			var startCol = Math.max(Math.min(column1, column2), 0);
			var endCol = Math.max(column1, column2);

			for (var rowIndex = startRow; rowIndex <= endRow; rowIndex += 1) {
				for (var columnIndex = startCol; columnIndex <=
					endCol; columnIndex += 1) {
					var obj = {
						row: rowIndex,
						col: columnIndex
					};
					arr.push(obj);
				}
			}
		}
		return arr;
	},
	dealClass: function (className, type) {
		var selected = HotUtil.getSelecteds();
		for (var i = 0; i < selected.length; i++) {
			var obj = selected[i];
			var meta = hot.getCellMeta(obj.row, obj.col);
			var oldClass = "";
			if (meta.className) {
				oldClass = meta.className;
			}
			var classArr = oldClass.split(" ");
			var newClassArr = [];
			for (var j = 0; j < classArr.length; j++) {
				if (className.indexOf('font-size') > -1) {
					if (classArr[j] !== className && classArr[j] !== " " && classArr[j].indexOf('font-size') ===
						-1) {
						newClassArr.push(classArr[j]);
					}
				} else if (className.indexOf('font-color') > -1) {
					if (classArr[j] !== className && classArr[j] !== " " && classArr[j].indexOf(
						'font-color') === -1) {
						newClassArr.push(classArr[j]);
					}
				} else {
					if (classArr[j] !== className && classArr[j] !== " ") {
						newClassArr.push(classArr[j]);
					}
				}

			}
			if (type === 'add') {
				if (className !== 'font-color-black') {
					newClassArr.push(className);
				}
			}
			hot.setCellMeta(obj.row, obj.col, 'className', newClassArr.join(" "));
		}
		hot.render();
	},
	setSelectedStyle: function (style) {
		var selected = HotUtil.getSelecteds();
		for (var i = 0; i < selected.length; i++) {
			var obj = selected[i];
			var cellTd = hot.getCell(obj.row, obj.col);
			for (var key in style) {
				cellTd.style[key] = style[key];
			}
		}
		// hot.resumeRender();
	},
	createFontSizeCss: function () {
		var res = '';
		for (var i = 12; i <= 40; i++) {
			res += '.font-size-' + i + '{font-size:' + i + 'px !important;line-height:' + (i + 7) +
				'px !important}\n';
		}
	},
	checkNodeNameIsRepeat: function (parentId, nodeName, oldNodeName) {
		var flag = false;
		var cb_success = function (res) {
			if (res.success) {
				var ds = res.data;
				for (var i = 0; i < ds.length; i++) {
					var d = ds[i];
					if (nodeName == d.NAME) {
						flag = true;
						break;
					}
				}
				if (nodeName == oldNodeName) {
					flag = false;
				}
			} else {
				layer.alert(res.msg);
			}
		};
		twxAjax(THING, 'QueryChildrenByPid', {
			pid: parentId
		}, false, cb_success); //同步请求校验
		return flag;
	},
	//校验同一节点下子节点表序号是否重复
	checkTableNumIsRepeat: function (parentId, tableNum, oldTableNum) {
		var flag = false;
		var cb_success = function (res) {
			if (res.success) {
				var ds = res.data;
				for (var i = 0; i < ds.length; i++) {
					var d = ds[i];
					if (tableNum == d.TABLE_NUM) {
						flag = true;
						break;
					}
				}
				if (tableNum == oldTableNum) {
					flag = false;
				}
			} else {
				layer.alert(res.msg);
			}
		};
		twxAjax(THING, 'QueryChildrenByPid', {
			pid: parentId
		}, false, cb_success); //同步请求校验
		return flag;
	},
	editTableNode: function (treeNode, pageType) {
		var log = {};
		var type = treeNode.TYPE;
		var oldSecurity = treeNode.SECURITY;
		var typeInfo = HotUtil.getTypeInfo(type);
		var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
			level = typeInfo.level,
			isTableNum = typeInfo.isTableNum;
		var nameTpl = '<div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
											<div class="layui-input-block">\
												<input type="text" name="newName" value="' + treeNode.NAME +
			'" lay-verify="required" required autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '名称" class="layui-input">\
											</div>\
										</div>';
		var tableNumTpl = '<div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '序号:</label>\
											<div class="layui-input-block">\
												<input type="text" name="newTableNum" value="' + treeNode.TABLE_NUM +
			'" lay-verify="required" required autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '序号" class="layui-input">\
											</div>\
										</div>';
		var securityOptions = "";
		for (var i = 0; i < securitys.length; i++) {
			if (securitys[i].KEY == oldSecurity) {
				securityOptions += '<option selected value="' + securitys[i].KEY + '">' + securitys[i].NAME +
					'</option>';
			} else {
				securityOptions += '<option value="' + securitys[i].KEY + '">' + securitys[i].NAME +
					'</option>';
			}
		}

		var securityTpl = '<div class="layui-form-item">\
											<label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '密级:</label>\
											<div class="layui-input-block">\
												<select name="security" id="security">' + securityOptions + '</select>\
											</div>\
										</div>';
		if (!isTableNum) {
			tableNumTpl = "";
			securityTpl = "";
		}

		//日志记录
		log.operation = '编辑' + nodeTypeNameLabel;
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;

		var updateTpl =
			'<form class="layui-form" action="" lay-filter="editNodeForm">\
				' + tableNumTpl + nameTpl + securityTpl + '\
				<div class="layui-form-item" style="display:none;">\
					<div class="layui-input-block">\
						<div class="layui-footer">\
							<button class="layui-btn" id="editNodeSubmit" lay-submit="" lay-filter="editNodeSubmit">确认</button>\
							<button type="reset" id="editNodeReset" class="layui-btn layui-btn-primary">重置</button>\
						</div>\
					</div>\
				</div>\
			</form>';
		layer.open({
			title: '编辑' + nodeTypeNameLabel,
			type: 1,
			fixed: false,
			maxmin: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			resize: false, //不允许拉伸
			area: ['600px', isTableNum ? '280px' : '170px'],
			content: '<div id="editNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['确认', '重置', '关闭'],
			yes: function () {
				$('#editNodeSubmit').click();
			},
			btn2: function () {
				$('#editNodeReset').click();
				return false;
			},
			btn3: function () {
				return true;
			},
			success: function (layero, userLayerIndex, that) {
				$(layero).find('.layui-layer-content').css("overflow", "visible");
				$("#editNodeContent").append(updateTpl);
			}
		});

		form.render(null, 'editNodeForm');
		form.on('submit(editNodeSubmit)', function (data) {

			var parentId = treeNode.PID;
			var param = {};
			if (HotUtil.checkNodeNameIsRepeat(parentId, data.field.newName, treeNode.NAME)) {
				layer.alert("该节点名称已经存在！");
				return false;
			}
			if (isTableNum) {
				if (HotUtil.checkTableNumIsRepeat(parentId, data.field.newTableNum, treeNode
					.TABLE_NUM)) {
					layer.alert("该节点表序号已经存在！");
					return false;
				}
				param.tableNum = data.field.newTableNum || "";
				param.security = data.field.security;
			}
			param.name = data.field.newName;

			param.id = treeNode.ID;
			//是否有锁定编辑的权限
			var allfuns = sessionStorage.getItem('funcids');
			var funcArr = allfuns.split(',');
			if (contains(funcArr, 'func-' + pageType + '-lock-edit')) {
				param.hasLockEdit = "true";
			} else {
				param.hasLockEdit = "false";
			}
			var content = "将节点【" + treeNode.NAME + "（" + treeNode.ID + "）】更改为";
			if (isTableNum) {
				content += "（" + nodeTypeNameLabel + "序号：" + param.tableNum + "，" + nodeTypeNameLabel +
					"名称：" + param.name + "，" + nodeTypeNameLabel + "密级：" + HotUtil.getSecurityName(param
						.security) + "）";
			} else {
				content += "（" + nodeTypeNameLabel + "名称：" + param.name + "）";
			}
			log.content = content;
			var cb_success = function (res) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					reloadTree(treeNode.PID, treeNode.ID);
					log.reqResult = 1;
				} else {
					log.reqResult = 0;
					layer.alert(res.msg, {
						icon: 2
					});
				}
				addConfirmLog(log);
			};
			var cb_error = function () {
				layer.closeAll();
				layer.msg('修改失败!');
			};
			twxAjax(THING, 'UpdateTableNode', param, true, cb_success, cb_error);
			return false;
		});
	},
	deleteTableNode: function (treeNode) {
		var log = {};
		//日志记录
		log.operation = "删除节点";
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;
		log.content = "删除节点【" + treeNode.NAME + "（" + treeNode.ID + "）】";
		var msg = "确认删除 节点 -- " + treeNode.NAME + " 吗？";
		if (treeNode.ISPARENT) {
			msg = "该节点下有子节点,确认删除吗?"
		}
		layer.confirm(msg, {
			icon: 3,
			title: '提示'
		}, function (index) {
			var cb_success = function (res) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					reloadTree(treeNode.PID);
					log.reqResult = 1;
				} else {
					log.reqResult = 0;
					layer.alert(res.msg, {
						icon: 2
					});
				}
				addConfirmLog(log);
			};
			var cb_error = function () {
				layer.msg('删除失败！');
			}
			twxAjax(THING, 'DeleteNode', {
				id: treeNode.ID
			}, true, cb_success, cb_error);
		});
	},
	importTableNode: function (treeNode, type) {
		if (device.ie && device.ie < 10) {
			layer.alert("请使用chrome浏览器！", {
				icon: 2
			});
		} else {
			var nodeTypeNameLabel, level;
			var typeInfo = HotUtil.getTypeInfo(type);
			var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
				level = typeInfo.level,
				isTableNum = typeInfo.isTableNum;
			//日志记录
			var log = {};
			log.operation = "导入" + nodeTypeNameLabel;
			log.tablePid = treeNode.PID;
			log.tableId = treeNode.ID;

			var fileFlag = false;
			var uploadInst;
			layer.open({
				title: "导入" + nodeTypeNameLabel,
				type: 1,
				area: ['460px', "330px"],
				content: '<div id="importTableContent" style="padding: 15px 0px 0px 0px;"></div>',
				anim: false,
				openDuration: 200,
				isOutAnim: false,
				closeDuration: 200,
				resize: false,
				btn: ['确定', '取消'],
				yes: function () {
					if (!fileFlag) {
						layer.alert('请选择需要导入的excel文件!', {
							icon: 2
						});
						return false;
					}
					var tableName = $("#b-tableName").val();

					if (HotUtil.checkNodeNameIsRepeat(treeNode.ID, tableName)) {
						layer.alert("该" + nodeTypeNameLabel + "名称已经存在！", {
							icon: 2
						});
						return false;
					}

					var tableNum = $("#b-tableNum").val();
					if (tableNum == "") {
						layer.alert('请输入' + nodeTypeNameLabel + '的序号!', {
							icon: 2
						});
						return false;
					}

					if (HotUtil.checkTableNumIsRepeat(treeNode.ID, tableNum)) {
						layer.alert("该" + nodeTypeNameLabel + "序号已经存在！", {
							icon: 2
						});
						return false;
					}
					uploadInst.config.url = fileHandlerUrl + '/online/import/table?thing=' +
						THING + '&pid=' + treeNode.ID + '&treeId=' + treeNode.TREE_ID + '&type=' +
						type + '&level=' + level + '&tableName=' + tableName + '&tableNum=' +
						tableNum + '&saveUser=' + sessionStorage.getItem("username");
					log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" +
						"导入了" + nodeTypeNameLabel + "，具体内容为（" + nodeTypeNameLabel +
						"序号：" + tableNum + "，" + nodeTypeNameLabel +
						"名称：" + tableName + "）";
					$('#uploadStart').click();
				},
				btn2: function () {
					return true;
				},
				success: function () {
					var tpl = '<form class="layui-form" lay-filter="importTableForm">\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">' + nodeTypeNameLabel +
						'序号:</label>\
										<div class="layui-input-block">\
											<input type="text" name="tableNum" id="b-tableNum" lay-verify="required" autocomplete="off" placeholder="请输入' +
						nodeTypeNameLabel + '序号" style="width:330px" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item layui-hide">\
										<label class="fieldlabel layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
										<div class="layui-input-block">\
											<input type="text" name="name" id="b-tableName" lay-verify="required" autocomplete="off" placeholder="请输入' +
						nodeTypeNameLabel + '名称" style="width:330px" class="layui-input">\
										</div>\
									</div>\
									<div class="layui-form-item">\
										<label class="fieldlabel layui-form-label">文件内容:</label>\
										<div class="layui-input-block">\
											<div class="layui-upload">\
												<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>\
												<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>\
											</div>\
										</div>\
									</div>\
									<div class="layui-form-item" id="selectedFile" style="display: none;">\
										<label class="fieldlabel layui-form-label">已选文件:</label>\
										<div class="layui-input-block">\
											<div class="layui-form-mid layui-word-aux" id="selectedFileName"></div>\
										</div>\
									</div>\
									<div class="layui-form-item" style="display:none;">\
										<center>\
											<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>\
											<button id="btn_cancel" class="layui-btn">取消</button>\
										</center>\
									</div>\
								</form>'
					$("#importTableContent").append(tpl);
					form.render(null, 'importTableForm');
					uploadInst = upload.render({
						elem: '#uploadChoice',
						url: fileHandlerUrl + '/online/import/table',
						auto: false,
						exts: 'xls|xlsx',
						field: 'uploadFile',
						bindAction: '#uploadStart',
						dataType: "json",
						choose: function (obj) {
							fileFlag = true;
							var files = obj.pushFile();
							obj.preview(function (index, file, result) {
								var filename = file.name;
								$("#selectedFile").show();
								$("#selectedFileName").text(filename);
								filename = filename.replace("：", ":");
								if (filename.indexOf(":") > -1) {
									filename = filename.split(":")[1];
								}

								if (filename.indexOf(".") > -1) {
									filename = filename.split(".")[0];
								}
								$("#b-tableName").val(filename);
								$("#b-tableName").parent().parent().removeClass(
									"layui-hide");
							});
						},
						before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
							layer.load(); //上传loading
						},
						done: function (res, index, upload) {
							layer.closeAll();
							if (res.success) {
								log.reqResult = 1;
								layer.msg("导入成功");
								reloadTree(treeNode.ID, res.data.id);
							} else {
								log.reqResult = 0;
								layer.alert(res.msg, {
									icon: 2
								});
							}
							addConfirmLog(log);
						}
					});
				}
			});
		}
	},
	getTypeInfo: function (type) {
		var nodeTypeNameLabel = '节点',
			level;
		var isTableNum = true;
		if (type == "a") {
			level = 3;
			nodeTypeNameLabel = 'A表';
		} else if (type == "b") {
			level = 4;
			nodeTypeNameLabel = 'B表';
		} else if (type == "model") {
			level = 1;
			nodeTypeNameLabel = "型号";
			isTableNum = false;
		} else if (type == "project") {
			level = 2;
			nodeTypeNameLabel = "项目";
			isTableNum = false;
		} else if (type == "folder") {
			level = 0;
			nodeTypeNameLabel = "分类";
			isTableNum = false;
		} else if (type == 'report') {
			level = 6;
		} else if (type == 'table') {
			level = 7;
		} else if (type == 'table_1') {
			level = 8;
		} else if (type == 'table_2') {
			level = 9;
		} else if (type == 'table_3') {
			level = 10;
		}
		return {
			isTableNum: isTableNum,
			level: level,
			nodeTypeNameLabel: nodeTypeNameLabel
		};
	},
	//添加结构树的确认表节点 isCopy  是否为复制节点
	addTableNode: function (treeNode, type, isCopy) {
		var typeInfo = HotUtil.getTypeInfo(type);
		var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
			level = typeInfo.level,
			isTableNum = typeInfo.isTableNum;
		var optText = isCopy ? '复制' : '添加';
		var optTitle = optText + nodeTypeNameLabel;
		//日志记录
		var log = {};
		log.operation = optTitle;
		log.tablePid = treeNode.PID;
		log.tableId = treeNode.ID;

		var securityOptions = "";
		for (var i = 0; i < securitys.length; i++) {
			securityOptions += '<option value="' + securitys[i].KEY + '">' + securitys[i].NAME + '</option>';
		}

		var addTpl =
			'<form class="layui-form" action="" lay-filter="add-node-form">\
				<div class="layui-form-item ' + (isTableNum ? '' : 'layui-hide') + '">\
					<label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '序号:</label>\
					<div class="layui-input-block">\
						<input type="text" name="tableNum" lay-verify="' + (isTableNum ? 'required' : '') +
			'" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '序号"  class="layui-input">\
					</div>\
				</div>\
				<div class="layui-form-item">\
					<label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '名称:</label>\
					<div class="layui-input-block">\
						<input type="text" name="name" lay-verify="required" autocomplete="off" placeholder="请输入' + nodeTypeNameLabel + '名称" class="layui-input">\
					</div>\
				</div>\
				<div class="layui-form-item ' + (isTableNum ? '' : 'layui-hide') + '">\
					<label class="fieldlabel1 layui-form-label">' + nodeTypeNameLabel + '密级:</label>\
					<div class="layui-input-block">\
						<select name="security" id="security">' + securityOptions + '</select>\
					</div>\
				</div>\
				<div class="layui-form-item" style="display:none;">\
					<div class="layui-input-block">\
						<div class="layui-footer">\
							<button class="layui-btn" id="addNodeSubmit" lay-submit="" lay-filter="submit-node">确认</button>\
							<button type="reset" id="addNodeReset" class="layui-btn layui-btn-primary">重置</button>\
						</div>\
					</div>\
				</div>\
			</form>';
		layer.open({
			title: optTitle,
			type: 1,
			fixed: false,
			maxmin: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			resize: false, //不允许拉伸
			area: ['500px', isTableNum ? '280px' : '170px'],
			content: '<div id="addTableNodeContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['确认', '重置', '取消'],
			yes: function () {
				$('#addNodeSubmit').click();
			},
			btn2: function () {
				$('#addNodeReset').click();
				return false;
			},
			btn3: function () {
				return true;
			},
			success: function (layero, userLayerIndex, that) {
				$(layero).find('.layui-layer-content').css("overflow", "visible");
				$("#addTableNodeContent").append(addTpl);
			}
		});
		form.render(null, 'add-node-form');

		form.on('submit(submit-node)', function (formData) {
			var param = {};
			var parentId = isCopy ? treeNode.PID : treeNode.ID;
			if (HotUtil.checkNodeNameIsRepeat(parentId, formData.field.name)) {
				layer.alert("该" + nodeTypeNameLabel + "名称已经存在！", {
					icon: 2
				});
				return false;
			}
			if (isTableNum) {
				if (HotUtil.checkTableNumIsRepeat(parentId, formData.field.tableNum)) {
					layer.alert("该" + nodeTypeNameLabel + "序号已经存在！", {
						icon: 2
					});
					return false;
				}
			}

			param.name = formData.field.name;
			param.tableNum = formData.field.tableNum || '';
			param.security = formData.field.security;
			param.creator = sessionStorage.getItem('username');
			if (isCopy) {
				param.id = treeNode.ID;
				param.pid = treeNode.PID;
				param.processTreeId = treeNode.TREE_ID;
			} else {
				param.pid = treeNode.ID;
				param.treeId = treeNode.TREE_ID;
				param.type = type;
				param.level = level;
			}
			var content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上" + optText + "了" +
				nodeTypeNameLabel;
			if (isTableNum) {
				content += "，具体内容为（" + nodeTypeNameLabel + "序号：" + param.tableNum +
					"，" + nodeTypeNameLabel + "名称：" + param.name +
					"，" + nodeTypeNameLabel + "密级：" + HotUtil.getSecurityName(param.security) + "）";
			} else {
				content += "，具体内容为（" + nodeTypeNameLabel + "名称：" + param.name + "）";
			}
			log.content = content;
			layer.load();
			var cb_success = function (res) {
				if (res.success) {
					layer.closeAll();
					layer.msg(res.msg);
					reloadTree(parentId, res.data.id);
					log.reqResult = 1;
				} else {
					log.reqResult = 0;
					layer.alert(res.msg, {
						icon: 2
					});
				}
				addConfirmLog(log);
			};
			var cb_error = function (data) {
				layer.alert('添加失败，请联系管理员！', {
					icon: 2
				});
			};
			twxAjax(THING, isCopy ? 'CopyNode' : 'AddTableNode', param, true, cb_success, cb_error);
			return false;
		});
	},
	ejectDownloadTable: function (o) {
		layer.open({
			title: "文件下载列表",
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			maxmin: true,
			resize: false, //不允许拉伸
			area: ['' + o.tableWidth + 'px', '700px'],
			scrollbar: false,
			content: o.seachFormHtml + '<div id="download-table"></div>',
			success: function () {
				renderSearchForm();
				HotUtil.renderDownloadTable(o.cols, o.tableHeight);
			}
		});
	},
	renderDownloadTable: function (cols, tableHeight) {
		// 创建渲染实例
		table.render({
			elem: '#download-table',
			id: 'download-table',
			url: getUrl(THING, 'QueryDownloadTable'),
			where: {
				creator: sessionStorage.getItem("username")
			},
			height: tableHeight, // 最大高度减去其他容器已占有的高度差
			cellMinWidth: 80,
			page: {
				layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
			},
			cols: [
				[{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'DWONLOAD_ID',
					hide: true
				},
				{
					field: 'FOLDER',
					width: 102,
					title: '分类'
				},
				{
					field: 'MODEL',
					title: '型号',
					width: 85
				}
				].concat(cols).concat([{
					field: 'A_TABLE',
					width: 300,
					title: 'A表',
					templet: function (d) {
						return d.A_TABLE == '：' ? "" : d.A_TABLE;
					},
				},
				{
					field: 'EXPORT_TYPE',
					title: '文件类型',
					width: 85,
					templet: function (d) {
						var html = "";
						if (d.EXPORT_TYPE == 1) {
							html = '<span class="layui-badge layui-bg-blue">Pdf</span>';
						} else if (d.EXPORT_TYPE == 2) {
							html =
								'<span class="layui-badge layui-bg-green">Pdf压缩包</span>';
						} else if (d.EXPORT_TYPE == 3) {
							html =
								'<span class="layui-badge layui-bg-red">Excel压缩包</span>';
						} else if (d.EXPORT_TYPE == 4) {
							html = '<span class="layui-badge layui-bg-red">数据包</span>';
						}
						return html;
					},
					align: 'center'
				},
				{
					field: 'START_TIME',
					width: 152,
					title: '提交时间',
					align: 'center'
				},
				{
					field: 'FILE_SIZE',
					width: 100,
					title: '文件大小',
					align: 'center',
					templet: function (d) {
						var html = "";
						if (d.FILE_SIZE) {
							if (d.FILE_SIZE != 'undefined' && d.FILE_SIZE != '') {
								html = d.FILE_SIZE;
							}
						}
						return html;
					}
				},
				{
					field: 'IS_COMPLETE',
					title: '是否完成',
					width: 90,
					minWidth: 90,
					templet: function (d) {
						var html = "";
						if (d.IS_COMPLETE == 0) {
							html = '<span class="layui-badge layui-bg-blue">进行中</span>';
						} else if (d.IS_COMPLETE == 1) {
							html =
								'<span class="layui-badge layui-bg-green">已完成</span>';
						} else if (d.IS_COMPLETE == 2) {
							html =
								'<span class="layui-badge layui-bg-red show-msg" title="点击查看原因" msg="' +
								d.MSG + '">生成失败</span>';
						}
						return html;
					},
					align: 'center'
				},
				{
					field: 'END_TIME',
					title: '完成时间',
					width: 152,
					align: 'center'
				},
				{
					field: 'IS_DOWNLOAD',
					title: '是否下载',
					width: 85,
					templet: function (d) {
						var html = "";
						if (d.IS_DOWNLOAD == 0) {
							html = '<span class="layui-badge layui-bg-blue">未下载</span>';
						} else if (d.IS_DOWNLOAD == 1) {
							html =
								'<span class="layui-badge layui-bg-green">已下载</span>';
						}
						return html;
					},
					align: 'center'
				},
				{
					fixed: 'right',
					title: '操作',
					width: 68,
					minWidth: 68,
					toolbar: `<div class="layui-clear-space">
									<a class="layui-btn layui-btn-xs" lay-event="download">下载</a>
								</div>`,
					align: 'left'
				}
				])
			],
			done: function () {
				$(".show-msg").off('click').on('click', function () {
					layer.alert($(this).attr("msg"));
				});
			},
			error: function (res, msg) {
				console.log(res, msg)
			}
		});

		// 工具栏事件
		table.on('tool(download-table)', function (obj) {
			var data = obj.data; // 获得当前行数据
			if (obj.event === 'download') {
				if (data.FILE_PATH) {
					var filePath = "//" + data.FILE_PATH;
					filePath = filePath.replace(/\\/g, "/");
					var fileName = data.FILE_NAME;
					var export_type = data.EXPORT_TYPE;
					if (export_type == 4) {
						fileName = new Date().getTime() + ".dat";
					}

					twxAjax('Thing.Util.HandsonTable', 'RecordDownloadFile', {
						downloadId: data.ID
					}, true, function (res) {
						downloadFile(filePath, fileName);
						table.reload('download-table');
					}, function () { });
				} else {
					layer.alert("文件还未生成，请稍后再试！", {
						icon: 2
					});
				}
			} else if (obj.event === 'delete') {
				if (data.IS_COMPLETE == 0) {

				}
			}
		});
	},
	moveNode: function (thing, treeNode) {
		var parentNode = treeNode.getParentNode();
		var moveZtreeObj;
		layer.open({
			title: "移动节点",
			type: 1,
			fixed: false,
			maxmin: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			resize: false,
			area: ['500px', '600px'],
			content: '<div id="moveNodeContent" style="padding-top: 15px;padding-right: 15px;"><ul id="moveTree" class="ztree"></ul></div>',
			btn: ['确定', '取消'],
			yes: function () {
				var checkedNodes = moveZtreeObj.getCheckedNodes(true);
				if (checkedNodes.length == 0) {
					layer.alert("请选择目标节点！", {
						icon: 2
					});
				} else {
					var targetNode = checkedNodes[0];
					var targetId = targetNode['ID'];
					var targetTreeId = targetNode['TREE_ID'];
					var log = {};
					log.operation = '移动节点';
					log.tablePid = treeNode['PID'];
					log.tableId = treeNode['ID'];
					log.content = "将节点【" + treeNode['NAME'] + "（" + treeNode['ID'] +
						"）】移动到节点【" + targetNode['NAME'] + "（" + targetId + "）】下";
					twxAjax(THING, 'MoveNode', {
						id: treeNode['ID'],
						targetId: targetId,
						targetTreeId: targetTreeId
					}, true, function (res) {
						if (res.success) {
							log.reqResult = 1;
							layer.closeAll();
							ztreeObj.reAsyncChildNodes(parentNode.getParentNode(),
								'refresh', false,
								function () {
									var nodeByParam = ztreeObj.getNodeByParam("ID",
										targetNode.getParentNode()['ID'], null);
									if (nodeByParam !== null) {
										ztreeObj.reAsyncChildNodes(nodeByParam,
											'refresh', false,
											function () {
												loadTreeMenu();
											});
									}
								});
						} else {
							log.reqResult = 0;
						}
						addConfirmLog(log);
					});
				}
			},
			btn2: function () {
				return true;
			},
			success: function (layero, index, that) {
				var cb_success = function (res) {
					if (res.success) {
						var datas = res.data;
						if (datas.length > 0) {
							datas = dealMoveTreeData(datas, parentNode);
							treeSetting.check = {
								enable: true,
								chkStyle: "radio",
								radioType: "all"
							};
							treeSetting.async.dataFilter = function (treeId, parentNode1,
								responseData) {
								if (responseData.success) {
									var datas = responseData.data;
									if (datas.length > 0) {
										datas = dealMoveTreeData(datas, parentNode);
									}
									return datas;
								} else {
									layer.alert(responseData.msg, {
										icon: 2
									});
								}
							};
							treeSetting.edit.enable = false;
							treeSetting.callback.onExpand = function (event, treeId,
								treeNode) { };

							treeSetting.callback.onClick = function (event, treeId,
								treeNode) { };
							moveZtreeObj = $.fn.zTree.init($("#moveTree"), treeSetting,
								datas);
							var node = moveZtreeObj.getNodeByParam("LEVEL_NUM", 0, null);
							moveZtreeObj.expandNode(node, true, false, true);
						}
					} else {
						layer.alert(res.msg);
					}
				};
				//使用ajax进行异步加载Tree
				twxAjax(thing, 'QueryTreeRoot', {
					username: sessionStorage.getItem('username')
				}, true, cb_success);
			}
		});
	},
	batchImportExcel: function (treeNode, type) {
		var typeInfo = HotUtil.getTypeInfo(type);
		var nodeTypeNameLabel = typeInfo.nodeTypeNameLabel,
			level = typeInfo.level;
		var domHtml = `<div style="padding:12px;">
						<div class="layui-form" style="margin-bottom: 10px;">
							<div class="layui-form-item" style="margin-bottom:0px;">
								<div class="layui-inline" style="margin-bottom:0px;">
									<div class="layui-upload" style="margin-bottom:2px;">
										<div id="chooseFile">选择文件</div>
										<button type="button" class="layui-btn" id="manyUploadStart" style="display: none;">开始上传</button>
									</div>
								</div>
							</div>
						</div>
						<table id="file-table" lay-filter="file-table"></table>
					</div>`;
		layer.open({
			title: '批量导入' + nodeTypeNameLabel,
			type: 1,
			area: ['1100px', '570px'],
			content: domHtml,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			btn: ['上传', '关闭'],
			yes: function () {
				$("#manyUploadStart").click();
			},
			btn2: function () {
				table.cache["file-table"] = [];
				return true;
			},
			success: function () {

				var fileTable = table.render({
					elem: '#file-table',
					data: [],
					height: 375,
					limit: 999,
					cols: [
						[{
							field: '',
							type: 'numbers',
							width: 80
						}, {
							field: 'tableNum',
							title: nodeTypeNameLabel + '序号',
							templet: '<div>{{d.tableNum}}</div>',
							edit: 'text',
							width: 200
						}, {
							field: 'tableName',
							title: nodeTypeNameLabel + '名称',
							templet: '<div>{{d.tableName}}</div>',
							edit: 'text'
						}, {
							field: 'fileSize',
							title: '文件大小',
							templet: '<div>{{d.fileSize}}</div>',
							width: 100
						}, {
							field: 'operate',
							title: '操作(双击)',
							width: 100,
							templet: `<div class="operate">
	                                            <a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="del">删除</a>
	                                          </div>`
						}]
					],
					done: function (res, curr, count) {

					}
				});
				table.on('toolDouble(file-table)', function (obj) {
					var data = obj.data; // 得到当前行数据
					var layEvent = obj.event; // 获得元素对应的 lay-event 属性值
					if (layEvent === 'del') { //删除
						layer.confirm('确定删除吗？', function (index) {
							obj.del();
							uploader.removeFile(data.file);
							layer.close(index);
						});
					}
				});

				var doneDatas = [];
				var uploader = WebUploader.create({
					// 选完文件后，是否自动上传。
					auto: false,
					// 文件接收服务端。
					server: fileHandlerUrl + '/online/batch/import/table',
					// 选择文件的按钮。可选。
					pick: {
						id: '#chooseFile',
						multiple: true
					},
					accept: {
						title: 'Excel文件',
						extensions: 'xlsx',
						mimeTypes: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
					},
					timeout: 10 * 60 * 1000,
					threads: 1,
					// 配置分片上传
					formData: {
						extraData: JSON.stringify({})
					}
				});
				uploader.on('uploadBeforeSend', function (object, data, headers) {
					var uid = object.blob.uid;
					var tableData = table.cache["file-table"];
					var tableNum = "",
						tableName = "";
					for (var i = 0; i < tableData.length; i++) {
						if (tableData[i].uid == uid) {
							tableNum = tableData[i].tableNum;
							tableName = tableData[i].tableName;
							break;
						}
					}
					data.extraData = JSON.stringify({
						pid: treeNode['ID'],
						treeId: treeNode['TREE_ID'],
						thing: THING,
						type: type,
						level: level,
						tableNum: tableNum,
						tableName: tableName,
						saveUser: sessionStorage.getItem("username")
					});
				});


				// 当有文件被添加进队列之前触发
				uploader.on('beforeFileQueued', function (file) {

				});
				// 当有文件被添加进队列的时候
				uploader.on('fileQueued', function (file) {
					var fileName = file.name;
					var o = {};
					fileName = fileName.replace("：", ":");
					var tableNum = "";
					if (fileName.indexOf(":") > -1) {
						tableNum = fileName.split(":")[0];
						fileName = fileName.split(":")[1];
					}
					if (fileName.indexOf(".") > -1) {
						fileName = fileName.split(".")[0];
					}
					o.tableNum = tableNum;
					o.tableName = fileName;
					o.fileSize = WebUploader.formatSize(file.size);
					o.uid = file.source.uid;
					o.file = file;
					table.cache["file-table"].push(o);
					table.renderData('file-table');
				});
				uploader.on('filesQueued', function (files) {

				});
				uploader.on('uploadProgress', function (file, percentage) {

				})


				uploader.on('uploadSuccess', function (file, res) {
					console.log(res);
				});

				// 文件上传失败，显示上传出错。
				uploader.on('uploadError', function (file) {

				});

				// 完成上传完毕，成功或者失败，先删除进度条。
				uploader.on('uploadComplete', function (file) {

				});

				// 当所有文件上传结束时触发
				uploader.on('uploadFinished', function () {
					reloadTree(treeNode.ID, treeNode.ID);
					//提示完成后，点击确定再刷新界面
					layer.closeAll();
					table.cache["file-table"] = [];
					layer.msg('导入成功');
				});

				$("#manyUploadStart").on('click', function () {
					var tableData = table.cache["file-table"];
					if (tableData.length > 0) {
						var hasEmpty = false;
						for (var i = 0; i < tableData.length; i++) {
							if (tableData[i].tableNum == '' || tableData[i].tableName ==
								'') {
								hasEmpty = true;
								break;
							}
						}
						if (hasEmpty) {
							layer.alert('表序号和表名称都不能为空！', {
								icon: 2
							});
						} else {
							layer.load();
							uploader.upload(); // 手动触发上传操作
						}
					} else {
						layer.alert('请选择需要上传的Excel文件！', {
							icon: 2
						});
					}
				});
			}
		});
	}

};

HotUtil.injectSecurity();
HotUtil.initWorkTypes();