var menuThingName = "Thing.Fn.SystemManagement";
var avaliableSelectMenuHtml = '';

var initAssignMenuBtn = function(layui) {
	$('#btnassignmenu').bind('click', function() {
		var layer = layui.layer;
		//获取选择的角色
		var sels = getSelectedData();
		if (sels.length == 0) {
			layer.msg('请先选择角色...', {
				icon: 2,
				anim: 6
			});
			return;
		}

		var selRowIndex = $('#roleTable').datagrid('getRowIndex', sels[0]);

		layer.open({
			title: '分配菜单',
			type: 1,
			area: ['300px', '580px'],
			content: '<ul id="avaliableSelectTree" style="width:100%;height:100%;padding:5px;"></ul>',
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			btn: ['分配', '重置', '关闭'],
			yes: function() {
				//获取选中的树节点数据
				var cks = $('#avaliableSelectTree').tree('getChecked', ['checked', 'indeterminate']);
				var menuids = '';
				var logs = '';
				for (var i = 0; i < cks.length; i++) {
					menuids += ',' + cks[i].MENU_ID;
					logs += ',' + '(ID：' + cks[i].MENU_ID + '、菜单名称：' + cks[i].MENU_NAME + ')';
				}
				logs = logs.substring(1);
				menuids = menuids.substring(1);

				var param = {
					menuids: menuids
				};
				param.roleid = sels[0].ROLE_ID;

				var cb_success = function(data) {
					if (data.success === false) {
						layer.msg(data.message, {
							icon: 2
						});
						logRecord('编辑', '角色管理-给角色(ID：' + sels[0].ROLE_ID + '、名称：' + sels[0].ROLE_NAME + ')分配了' + cks.length +
							'个菜单【' + logs + '】', 0);
						return;
					}
					initRoleData(selRowIndex);

					//$('#roleTable').datagrid('selectRow',selRowIndex);
					logRecord('编辑', '角色管理-给角色(ID：' + sels[0].ROLE_ID + '、名称：' + sels[0].ROLE_NAME + ')分配了' + cks.length +
						'个菜单【' + logs + '】', 1);
					layer.closeAll();
				};
				var cb_error = function() {};
				twxAjax(menuThingName, 'AssignMenuToRole', param, true, cb_success, cb_error);

				return false;
			},
			btn2: function() {
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function() {
				$.parser.parse();
				$('#avaliableSelectTree').tree({
					checkbox: true
				});
				initAvaliableTreeData();
			}
		});
	});
};

var initHadSelectedMenu = function() {
	var sels = getSelectedData();
	var menuids = sels[0].MENUIDS;
	if (menuids != undefined) {
		var menuidArr = menuids.split(',');
		for (var i = 0; i < menuidArr.length; i++) {
			var node = $('#avaliableSelectTree').tree('find', menuidArr[i]);
			if (node != null) {
				var isLeaf = $('#avaliableSelectTree').tree('isLeaf', node.target);
				if (isLeaf === true) {
					$('#avaliableSelectTree').tree('check', node.target);
				}
			}
		}
	}
};

var initAssignMenu = function() {
	var param = {};
	param.menuids = getSelectedData()[0].MENUIDS;
	var cb_success = function(data) {
		$('#selectedMenuTree').tree({
			checkbox: false,
			data: [data]
		});
	};
	var cb_error = function() {};
	twxAjax(menuThingName, 'QueryAssignMenu', param, true, cb_success, cb_error);

};

var initAvaliableTreeData = function() {
	var cb_success = function(data) {
		$('#avaliableSelectTree').tree('loadData', [data]);

		initHadSelectedMenu();
	};
	var cb_error = function() {};
	twxAjax(menuThingName, "getAllMenus", {
		isAssign: 1
	}, true, cb_success, cb_error);
};


var initFuncs = function(layui) {
	var laytpl = layui.laytpl,
		form = layui.form;
	var param = {};
	param.menuids = getSelectedData()[0].MENUIDS;
	var cb_success = function(data) {
		var func = document.getElementById('func');
		var getTpl = func.innerHTML,
			view = document.getElementById('view');
		laytpl(getTpl).render(data, function(html) {
			view.innerHTML = html;
		});
		form.render();

		initHadDistriFunc(layui);
	};
	var cb_error = function() {};
	twxAjax(menuThingName, 'QueryAllAssignMenuFuncs', param, true, cb_success, cb_error);
};

//初始化保存按钮
var initBtnSaveFunc = function(layui) {
	$('#btnfuncsave').bind('click', function() {
		var layer = layui.layer;
		var sels = getSelectedData();
		if (sels.length == 0) {
			layer.msg('请先选择角色...', {
				icon: 2,
				anim: 6
			});
			return;
		}
		var selRowIndex = $('#roleTable').datagrid('getRowIndex', sels[0]);
		var cks = $('input[name=func]:checked');
		var param = {};
		param.funcids = '';
		param.roleid = sels[0].ROLE_ID;
		for (var i = 0; i < cks.length; i++) {
			param.funcids += "," + cks[i].id;
		}
		param.funcids = param.funcids.substring(1);

		var cb_success = function(data) {
			if (data.success === false) {
				layer.msg(data.message, {
					icon: 2,
					anim: 6
				});
				return;
			}
			layer.msg('保存成功');
			initRoleData(selRowIndex);
			//layer.closeAll();
		};
		var cb_error = function() {};
		twxAjax(menuThingName, 'AssignFuncToRole', param, true, cb_success, cb_error);
	});
};

//初始化全选按钮
var initBtnCkAll = function(layui) {
	$('#ckall').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		if ($('input:checkbox[name=func]').length == 0) {
			layer.msg('未找到可选中的数据...', {
				icon: 2,
				anim: 6
			});
			return;
		}
		$('input:checkbox[name=func]').each(function() {
			$(this).attr('checked', true);
		});

		form.render('checkbox', 'func');
	});
};
//初始化全不选按钮
var initBtnUnCKAll = function(layui) {
	$('#unckall').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		if ($('input:checkbox[name=func]').length == 0) {
			layer.msg('未找到可取消的数据...', {
				icon: 2,
				anim: 6
			});
			return;
		}
		$('input:checkbox[name=func]').each(function() {
			$(this).attr('checked', false);
		});

		form.render('checkbox', 'func');
	});
};

//初始化已分配的功能
var initHadDistriFunc = function(layui) {
	var form = layui.form;
	var sels = getSelectedData();
	var funcids = sels[0].FUNCIDS;
	$('input:checkbox[name=func]').each(function() {
		if (("," + funcids + ",").indexOf("," + $(this).attr('id') + ",") > -1) {
			$(this).attr('checked', true);
		}
	});
	form.render('checkbox', 'func');
};

$(document).ready(function() {
	layui.use(['layer', 'form', 'laytpl'], function() {
		var laytpl = layui.laytpl,
			form = layui.form;
		initAssignMenuBtn(layui);

		var getTpl = func.innerHTML,
			view = document.getElementById('view');
		laytpl(getTpl).render({}, function(html) {
			view.innerHTML = html;
		});

		form.render();

		initBtnSaveFunc(layui);
		initBtnCkAll(layui);
		initBtnUnCKAll(layui);
	});
});
