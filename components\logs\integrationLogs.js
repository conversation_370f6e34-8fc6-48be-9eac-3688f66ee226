
//分页信息
var pageOptions = {
    pageSize: 30,
    pageNumber : 1
};

var loadFlag = 'init';

//初始化表格
function initComp(){
    $('#logtable').datagrid({
        data:[],
        singleSelect:true,
        fitColumns:true,
        striped:true,
        // toolbar:[],
        fit: true,
        rownumbers: true,
        columns:[[
            {field:'LOG_ID', title:'日志ID', width:100,hidden:true}
            ,{field:'LOG_DIRECT', title:'请求方向', width:100}
            ,{field:'LOG_REQ_PARAM', title: '请求参数',width: 300}
            ,{field:'LOG_REQ_TIME', title: '请求时间',width:100}
            ,{field:'LOG_RES_CONTENT', title: '响应内容',width: 300,formatter:function(value){
				return "<xmp>"+value+"</xmp>";
			}}
            ,{field:'LOG_RES_TIME',title:'响应时间',width:100}
            ,{field:'LOG_DURATION',title:'耗时',width:80,hidden:true}
            ,{field:'LOG_RESULT',title:'结果',width:80,formatter:function(value){
                if(value == '1'){
                    return '<span class="layui-badge layui-bg-green">成功</span>';
                }else{
                    return '<span class="layui-badge layui-bg-red">失败</span>';
                }
            }}
        ]],
        loadMsg:'正在加载数据...',
        emptyMsg:'<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有日志数据...</font></div>',
        pagination:true,
        onLoadSuccess:function(){
            //$(this).datagrid("fixRownumber");
        }
    });
    //初始化分页组件
    initPagination('logtable',{total:0});
    //显示第一页的数据
    queryDataByPage(pageOptions.pageSize,pageOptions.pageNumber);
    
}

//获取筛选条件的值
var getFieldValue = function(){
    var params = {};
    params.log_result = loadFlag === 'init' ? '' : $('#log_direct').combobox('getValue');
    params.s_time = loadFlag === 'init' ? '' : $('#s_time').textbox('getValue');
    params.e_time = loadFlag === 'init' ? '' : $('#e_time').textbox('getValue');
    params.log_result = loadFlag === 'init' ? '' : $('#log_result').combobox('getValue');
    return params;
};

//初始化行号
var initLineNumbers = function(){
    var rowNumbers = $('.datagrid-cell-rownumber');
    var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
    $(rowNumbers).each(function(index){
        var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
        $(rowNumbers[index]).html("");
        $(rowNumbers[index]).html(row);
    });
};

//初始化全部的记录条数
var initTotalRecords = function(){
    //查询所有的记录条数
    //初始化分页框架
    var cb_success = function(data){
        //initPagination('logtable',{total:data.rows[0].COUNT});
        $('#logtable').datagrid('getPager').pagination('refresh',{
            total:data.rows[0].COUNT,
            pageSize:pageOptions.pageSize,
            pageNumber:pageOptions.pageNumber
        });
        //重新初始化行号
        initLineNumbers();
    };
    var cb_error = function(){};

    //传递参数
    var param = getFieldValue();
    twxAjax('Thing.Integration.LogUtil','QueryTotalNumbers',param,true,cb_success,cb_error);
};

//分页查询数据
var queryDataByPage = function(pageSize,pageNumber){
    $('#logtable').datagrid('loading');
    var cb_success = function(data){
        //调用成功后，渲染数据
        $('#logtable').datagrid('loadData',data.rows);
        initTotalRecords();
        $('#logtable').datagrid('loaded');
    };
    var cb_error = function(){
        $('#logtable').datagrid('loaded');
        layui.use(['layer'],function(){
            var layer = layui.layer;
            layer.alert('加载出错...',{icon:2});
        });
    };
    //传递的参数
    var param = getFieldValue();
    param.pageSize = pageSize;
    param.pageNumber = pageNumber;
    //初始化表格后调用Ajax进行数据的加载显示
    twxAjax('Thing.Integration.LogUtil','QueryPageDataLogs',param,true,cb_success,cb_error);
};

//初始化分页组件
var initPagination = function(tableName,data){
    $('#' + tableName).datagrid('getPager').pagination({
        total:data.total,
        pageSize:pageOptions.pageSize,
        pageNumber:1,
        buttons:[{
			iconCls:'icon-refresh',
			handler:function(){ 
                queryDataByPage(pageOptions.pageSize,pageOptions.pageNumber);
            }
		}],
        pageList:[10,15,20,25,30,35,40,45,50],
        showPageList:true,
        showRefresh:false,
        onSelectPage : function (pageNumber,pageSize) {
            //当页码发生改变的时候进行调用
            pageOptions.pageNumber = pageNumber;
            queryDataByPage(pageSize,pageNumber);
        },
        onBeforeRefresh:function (pageNumber,pageSize) {
            //返回false可以在取消刷新操作
            //alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
        },
        onRefresh:function (pageNumber,pageSize) {
            //alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
            queryDataByPage(pageSize,pageOptions.pageNumber);
        },
        onChangePageSize:function (pageSize) {
            //改变pageSize时触发
            pageOptions.pageSize = pageSize;
            queryDataByPage(pageSize,pageOptions.pageNumber);
        }
    })
};


//初始化操作下拉框
var initOperations = function(){
    var cb_success = function(data){
        $('#log_direct').combobox('loadData',data.rows);
    };
    var cb_error = function(){};
    twxAjax('Thing.Integration.LogUtil','QueryAllLogDirect',{},true,cb_success,cb_error);
};

var initData = function(){
    //init operations
    initOperations();
};

//初始化组件
initComp();
//初始化控件的数据
initData();

//搜索按钮开始搜索
var searchLogs = function(){
    loadFlag = 'search';
    pageOptions.pageNumber = 1;

    queryDataByPage(pageOptions.pageSize,pageOptions.pageNumber);
};

//清除字段
var clearFields = function(){
    $('#log_direct').combobox('setValue','');
    $('#s_time').textbox('setValue','');
    $('#e_time').textbox('setValue','');
    $('#log_result').combobox('setValue','');
};