var curInfo = {
	tableId: 'dataTable',
	tableType: '物料信息查看',
	materielCode: ''
};


$(document).ready(function() {
	layui.use('layer', function() {
		curInfo.materielCode = parent.window.materielCode;
		//初始化easyui的表格
		initTableComp();

		//加载数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	});
});

//初始化表格 - 
var initTableComp = function(layui) {
	$('#' + curInfo.tableId).datagrid({
		data: [],
		singleSelect: true,
		// fitColumns: true,
		striped: true,
		fit: true,
		rownumbers: true,
		pagination: true,
		columns: [
			[{
				field: 'ID',
				hidden: true
			}, {
				field: 'TECHFILENAME',
				title: '依据工艺文件',
				align: 'center',
				width: 100
			}, {
				field: 'WORKPOSITION',
				title: '需求工位/代号',
				align: 'center',
				width: 100
			}, {
				field: 'REQUIREDATE',
				title: '需求日期',
				align: 'center',
				width: 100
			}, {
				field: 'REQUIREPERSON',
				title: '申请人',
				align: 'center',
				width: 100
			}, {
				field: 'MATERIALNAME',
				title: '物料名称',
				align: 'center',
				width: 100
			}, {
				field: 'SPEC',
				title: '规格',
				align: 'center',
				width: 100
			}, {
				field: 'MODEL',
				title: '物料型号',
				align: 'center',
				width: 100
			}, {
				field: 'UNIT',
				title: '计量单位',
				align: 'center',
				width: 100
			}, {
				field: 'REQUIREQUANTITY',
				title: '需求量',
				align: 'center',
				width: 100
			}, {
				field: 'RECEIVEQUANTITY',
				title: '领取量',
				align: 'center',
				width: 100
			}, {
				field: 'RECEIVEPERSON',
				title: '接收人',
				align: 'center',
				width: 100
			}]
		],
		loadMsg: '正在加载数据...',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>'
	});

	//初始化分页控件
	initPagination(curInfo.tableId, []);
};


//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var getFieldValue = function() {
	var param = {};
    param.materielCode = curInfo.materielCode;
	return param;
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(data) {
		//initPagination('logtable',{total:data.rows[0].COUNT});
		$('#' + curInfo.tableId).datagrid('getPager').pagination('refresh', {
			total: data.rows[0].COUNT,
			pageSize: pageOptions.pageSize,
			pageNumber: pageOptions.pageNumber
		});
		//重新初始化行号
		initLineNumbers();
	};
	var cb_error = function() {};

	//传递参数
	var param = getFieldValue();
	twxAjax('Thing.Fn.DataSearch', 'QueryMaterielCount', param, true, cb_success, cb_error);
};

//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	$('#' + curInfo.tableId).datagrid('loading');
	var cb_success = function(data) {
		//调用成功后，渲染数据
		$('#' + curInfo.tableId).datagrid('loadData', data.rows);
		initTotalRecords();
		$('#' + curInfo.tableId).datagrid('loaded');
	};
	var cb_error = function() {
		$('#' + curInfo.tableId).datagrid('loaded');
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	//传递的参数
	var param = getFieldValue();
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.DataSearch', 'QueryMateriel', param, true, cb_success, cb_error);
};

//初始化分页组件
var initPagination = function(tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {

		},
		onRefresh: function(pageNumber, pageSize) {
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	});
};
