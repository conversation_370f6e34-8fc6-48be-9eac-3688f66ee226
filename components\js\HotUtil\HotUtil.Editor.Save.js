/**
 * HotUtil.Editor.Save.js - Handsontable 编辑器工具库 (数据保存模块)
 *
 * 负责将编辑器中的表格数据保存到后端。
 */

/**
 * 保存表格数据
 * @param {Object} treeNode
 * @param {boolean} isReloadTable
 * @param {boolean} isValidEmpty
 * @param {boolean} [showSaveMsg]
 */
HotUtil.saveTableData = function (treeNode, isReloadTable, isValidEmpty, showSaveMsg) {
    // 处理默认参数（ES5兼容方式）
    if (typeof showSaveMsg === 'undefined') {
        showSaveMsg = true; // 默认显示保存提示（手动保存）
    }

    // 区分自动保存和手动保存的防重复检查
    var isManualSave = showSaveMsg;
    var isAutoSave = !showSaveMsg;

    // 防重复提交检查
    if (isManualSave && HotUtil._manualSaveInProgress) {
        layer.msg('正在保存中，请勿重复操作', { icon: 0 });
        return false;
    }

    if (isAutoSave && HotUtil._autoSaveInProgress) {
        // 自动保存正在进行中，静默跳过
        return false;
    }

    // 增强参数验证
    if (!treeNode || !treeNode.ID) {
        console.error('保存表格数据：无效的节点参数');
        if (showSaveMsg) {
            layer.alert('节点信息无效，无法保存数据', {
                icon: 2,
                title: '保存失败'
            });
        }
        return false;
    }

    // 增强错误处理：检查Handsontable实例（允许初始化过程中的调用）
    if (!window.hot) {
        console.warn('保存表格数据：Handsontable实例不存在，可能正在初始化中');
        // 如果是在初始化过程中，不显示错误提示，直接返回
        return false;
    }

    // 设置对应的保存状态
    if (isManualSave) {
        HotUtil._manualSaveInProgress = true;
    } else {
        HotUtil._autoSaveInProgress = true;
    }

    // 根据参数决定是否显示保存状态提示
    var savingMsg = null;
    if (showSaveMsg) {
        savingMsg = layer.msg('正在保存数据...', {
            icon: 16,
            shade: 0.3,
            time: 0 // 不自动关闭
        });
    }

    // 定义保存完成后的清理函数
    var finalizeSave = function () {
        if (isManualSave) {
            HotUtil._manualSaveInProgress = false;
        } else {
            HotUtil._autoSaveInProgress = false;
        }
        if (savingMsg) {
            layer.close(savingMsg);
        }
    };

    try {
        var tableData = window.hot.getData();
        var meta = HotUtil.myGetCellsMeta();

        if (isValidEmpty) {
            if (!tableData || tableData.length == 0) {
                finalizeSave();
                layer.alert("空表不可提交");
                return false;
            }
        }

        // 增强错误处理：获取合并单元格信息
        var merged = [];
        try {
            var mergePlugin = window.hot.getPlugin('MergeCells');
            if (mergePlugin && mergePlugin.mergedCellsCollection) {
                merged = mergePlugin.mergedCellsCollection.mergedCells || [];
            }
        } catch (mergeError) {
            console.warn('获取合并单元格信息失败:', mergeError);
            merged = [];
        }

        var data = {
            tableData: tableData,
            merged: merged,
            meta: meta,
            colWidths: HotUtil.getColWidths()
        };

        // 增强错误处理：JSON序列化
        var serializedData;
        try {
            serializedData = JSON.stringify(data);
            //替换英文引号为中文引号
            serializedData = serializedData.replace(/'(.[^']*)'/g, "' $1 '");
        } catch (jsonError) {
            console.error('数据序列化失败:', jsonError);
            finalizeSave();
            layer.alert('数据格式错误，无法保存', {
                icon: 2,
                title: '保存失败'
            });
            return false;
        }

        var param = {};
        param.id = treeNode.ID;
        param.tableData = serializedData;
        param.saveUser = sessionStorage.getItem("username");

        var log = {};
        log.operation = "保存数据";
        log.tablePid = treeNode.PID;
        log.tableId = treeNode.ID;
        log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】上保存数据";

        var cb_success = function (res) {
            finalizeSave();
            try {
                if (res && res.success) {
                    log.reqResult = 1;
                    if (isReloadTable) {
                        reloadTable(treeNode);
                        layer.closeAll();
                        layer.msg(res.msg || '保存成功');
                    }
                } else {
                    log.reqResult = 0;
                    layer.alert(res.msg || '保存失败', {
                        icon: 2
                    });
                }
            } catch (successError) {
                console.error('处理保存成功回调时出错:', successError);
                log.reqResult = 0;
            } finally {
                addConfirmLog(log);
            }
        };

        var cb_error = function (xhr, textStatus, errorThrown) {
            finalizeSave();
            console.error('保存表格数据请求失败:', textStatus, errorThrown);
            log.reqResult = 0;
            layer.alert("保存失败：" + (textStatus || "网络错误"), {
                icon: 2,
                title: '保存失败'
            });
            addConfirmLog(log);
        };

        //使用ajax进行异步加载Tree
        twxAjax(THING, 'SaveTableData', param, true, cb_success, cb_error);

    } catch (error) {
        finalizeSave();
        console.error('保存表格数据时发生异常:', error);
        layer.alert('保存过程中发生错误: ' + error.message, {
            icon: 2,
            title: '保存失败'
        });
        return false;
    }
};