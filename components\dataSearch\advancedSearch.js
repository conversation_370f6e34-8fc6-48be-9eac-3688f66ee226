$(function() {
	loadProductCombobox();
	loadPhaseCombobox(0);
	loadMajorCombobox(0);
	loadProcessCombobox(0);
	loadSecLevelCombobox();
	loadTable();
	resizeInputWidth();
	$("#root_layout").panel({
		onResize: function(width, height) {
			//2是边框宽度
			var gridWidth = width - 2;
			try {
				$("#" + tableName).datagrid('resize', {
					width: gridWidth
				});
				changeWidth(tableName);
			} catch (e) {

			}
		}
	});
})
var layer;
layui.use(['layer'], function() {
	layer = layui.layer;
});
//表格名称
var tableName = 'searchResultTable';
//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};
var emptyOption = {
	TREEID: 0,
	NODENAME: '--请选择--'
};
var tableLoadFlag = false;
//renderTable
var renderTable = function() {
	if (!tableLoadFlag) {
		var columns = listTableUtil.getColumns("all");
		$('#' + tableName).datagrid({
			data: [],
			fitColumns: true,
			height: windowH,
			toolbar: '#tb',
			columns: columns,
			emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>数据加载中...</font></div>',
			pagination: true,
			loadMsg: '正在加载数据...',
			rownumbers: true,
			singleSelect: true,
			striped: true,
			onLoadSuccess: function(data) {
				changeWidth(tableName);
			}
		});
		tableLoadFlag = true;
	}
}

//初始化分页组件
var initPagination = function(data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function(pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};

function getTreeId() {
	var treeId = 1;
	if ($("#product").combobox('getValue') != '') {
		if ($("#phase").combobox('getValue') != '') {
			if ($("#major").combobox('getValue') != '') {
				if ($("#process").combobox('getValue') != '') {
					treeId = $("#process").combobox('getValue');
				} else {
					treeId = $("#major").combobox('getValue');
				}
			} else {
				treeId = $("#phase").combobox('getValue');
			}
		} else {
			treeId = $("#product").combobox('getValue');
		}
	}
	return treeId;
}

function getTreeLog() {
	var log = '';
	var product = $("#product").combobox('getText');
	var phase = $("#phase").combobox('getText');
	var major = $("#major").combobox('getText');
	var process = $("#process").combobox('getText');
	if (product != '--请选择--') {
		if (phase != '--请选择--') {
			if (major != '--请选择--') {
				if (process != '--请选择--') {
					log = '型号：' + product + '、阶段：' + phase + '、专业：' + major + '、过程：' + process;
				} else {
					log = '型号：' + product + '、阶段：' + phase + '、专业：' + major;
				}
			} else {
				log = '型号：' + product + '、阶段：' + phase;
			}
		} else {
			log = '型号：' + product;
		}
	}
	return log;
}

//获取查询条件参数
function getSearchParams() {
	var secLevel = ''
	if ($("#secLevel").combobox('getValue') == '') {
		secLevel = '<' + userSecLevel;
	} else {
		secLevel = '=' + $("#secLevel").combobox('getValue');
	}
	return {
		processTreeId: getTreeId(),
		queryType: $("#searchType").combobox('getValue'),
		fileName: $("#searchName").val(),
		fileType: $("#searchFileType").val(),
		collectionMode: $("#searchModel").combobox('getValue'),
		creator: $("#searchCreator").val(),
		secLevel: secLevel,
		queryUser: sessionStorage.getItem('username')
	}
}
//获取查询日志信息
function getSearchLog() {
	var log = '';
	var treeLog = getTreeLog();
	var type = $("#searchType").combobox('getText'); //类别
	var name = $("#searchName").val(); //名称
	var fileType = $("#searchFileType").val(); //文件类别
	var model = $("#searchModel").combobox('getValue'); //采集方式
	var creator = $("#searchCreator").val(); //创建者
	if (treeLog != '') {
		log += '、' + treeLog;
	}
	if (type != '') {
		log += '、' + '类别：' + type;
	}
	if (fileType != '') {
		log += '、' + '文件类别：' + fileType;
	}
	if (name != '') {
		log += '、' + '名称：' + name;
	}
	if (model != '') {
		log += '、' + '采集方式：' + model;
	}
	if (creator != '') {
		log += '、' + '创建者：' + creator;
	}
	log = log.substring(1);
	if (log == '') {
		log = '所有';
	}
	return log;
}
//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	$('#' + tableName).datagrid('loading');
	initTotalRecords();
	var cb_success = function(res) {
		if (res.success) {
			dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + tableName).datagrid('loadData', res.data);
			if (pageLoadFlag) {
				paginationShow();
			}
			$('#' + tableName).datagrid('loaded');
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	};
	var cb_error = function() {
		layer.alert('加载出错...', {
			icon: 2
		});
	};
	var params = {};
	params.query = getSearchParams();
	params.pageSize = pageOptions.pageSize;
	params.pageNumber = pageOptions.pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.ListData', 'QueryListDataPage', params, true, cb_success, cb_error);
};

var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function() {
	$('#' + tableName).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
	var log = getSearchLog();
	logRecord('查询', '高级查询-查询条件(' + log + ')，共查询出' + totalRecords + '条数据', 1);
}

//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(res) {
		if (res.success) {
			pageLoadFlag = true;
			totalRecords = res.data;
			if (dataLoadFlag) {
				paginationShow();
			}
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	};
	var cb_error = function() {};
	var params = {};
	params.query = getSearchParams();
	twxAjax('Thing.Fn.ListData', 'QueryListDataCount', params, true, cb_success, cb_error);
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//加载表格数据
function loadTable() {
	//渲染表格
	renderTable();

	//初始化分页组件
	initPagination({
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}

//搜索表格数据
function searchTable() {
	//渲染表格
	// renderTable();

	//初始化分页组件
	initPagination({
		total: 0
	});
	pageOptions.pageNumber = 1;
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}

//加载密级列表
function loadSecLevelCombobox() {
	twxAjax('Thing.Fn.SystemDic', 'GetSecDataBySecLevel', {
		secLevel: userSecLevel
	}, true, function(data) {
		data.rows.unshift({
			NAME: '所有',
			KEY: ''
		});
		$("#secLevel").combobox({
			valueField: 'KEY',
			textField: 'NAME',
			panelHeight: 'auto',
			data: data.rows,
			editable: false
		});
	});
}

//加载型号列表
function loadProductCombobox() {
	twxAjax('Thing.Fn.DataDownload', 'QueryProduct', {
		username: sessionStorage.getItem('username')
	}, true, function(data) {
		data.rows.unshift(emptyOption);
		$("#product").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			panelHeight: '400px',
			data: data.rows,
			editable: true,
			onSelect: function(record) {
				loadPhaseCombobox(record.TREEID);
				$("#major").combobox('loadData', [emptyOption]);
				$("#major").combobox('setValue', '');
				$("#process").combobox('loadData', [emptyOption]);
				$("#process").combobox('setValue', '');
			},
			onLoadSuccess: function() {
				resizeComboboxWidth(this);
			}
		});
	});
}
//加载阶段列表
function loadPhaseCombobox(parentId) {
	twxAjax('Thing.Fn.DataDownload', 'QueryPhase', {
		parentId: parentId
	}, true, function(data) {
		data.rows.unshift(emptyOption);
		$("#phase").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			data: data.rows,
			panelHeight: 'auto',
			editable: false,
			onSelect: function(record) {
				loadMajorCombobox(record.TREEID);
				$("#process").combobox('loadData', [emptyOption]);
				$("#process").combobox('setValue', '');
			},
			onLoadSuccess: function() {
				resizeComboboxWidth(this);
			}
		});
	});
}
//加载专业列表
function loadMajorCombobox(parentId) {
	twxAjax('Thing.Fn.DataDownload', 'QueryMajor', {
		parentId: parentId
	}, true, function(data) {
		data.rows.unshift(emptyOption);
		$("#major").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			data: data.rows,
			panelHeight: 'auto',
			editable: false,
			onSelect: function(record) {
				loadProcessCombobox(record.TREEID);
			},
			onLoadSuccess: function() {
				resizeComboboxWidth(this);
			}
		});
	});
}
//加载过程列表
function loadProcessCombobox(parentId) {
	twxAjax('Thing.Fn.DataDownload', 'QueryProcess', {
		parentId: parentId
	}, true, function(data) {
		data.rows.unshift(emptyOption);
		$("#process").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			data: data.rows,
			panelHeight: 300,
			editable: false,
			onLoadSuccess: function() {
				resizeComboboxWidth(this);
			}
		});
	});
}
//置空查询条件
function resetForm() {
	$("#product").combobox('setValue', '');
	$("#phase").combobox('setValue', '');
	$("#major").combobox('setValue', '');
	$("#process").combobox('setValue', '');
	$("#phase").combobox('loadData', [emptyOption]);
	$("#major").combobox('loadData', [emptyOption]);
	$("#process").combobox('loadData', [emptyOption]);
	$("#searchName").textbox('setValue', '');
	$("#searchFileType").textbox('setValue', '');
	$("#searchCreator").textbox('setValue', '');
	$("#searchType").combobox('setValue', '');
	$("#secLevel").combobox('setValue', '');
	$("#searchModel").combobox('setValue', '');
}

function exportExcel() {
	var query = getSearchParams();
	var loading = layer.msg("导出中...", {
		icon: 16,
		shade: 0.3,
		time: 0
	});
	$.fileDownload(fileHandlerUrl + "/list/export/excel", {
		httpMethod: 'POST',
		data: {
			"query": JSON.stringify(query)
		},
		prepareCallback: function(url) {},
		abortCallback: function(url) {
			layer.close(loading);
			layer.msg("导出异常！！");
		},
		successCallback: function(url) {
			layer.close(loading);
		},
		failCallback: function(html, url) {
			layer.close(loading);
			layer.msg("导出失败！！");
		}
	});
}