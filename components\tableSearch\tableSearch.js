layui.use(['layer', 'form', 'upload'], function () {
	layer = layui.layer;
	form = layui.form;
	upload = layui.upload;
});

var secondTable;
var tableConfigId;
var tableConfigName;
$(function () {
	loadDataSearchTypeCombobox();
})

//加载数据类型列表
function loadDataSearchTypeCombobox() {
	twxAjax('Thing.Fn.TableSearch', 'GetTableSearchType', '', true, function (res) {
		if (res.success) {
			var deaflutValue = res.data[0].name;
			$("#dataType").combobox({
				valueField: 'name',
				textField: 'name',
				panelHeight: 'auto',
				data: res.data,
				editable: false,
				onLoadSuccess: function () {
					$("#dataType").combobox('setValue', deaflutValue);
				},
				onSelect: function (record) {
					// selTableType = record.ID;
					// selTableName = record.TREE_NAME;
					// loadTable();
					tableConfigId = record.id;
					tableConfigName = record.name;
					loadQueryParams(record.id);
					secondTable = new SecondTable(record.id);
					if (record.allowCustom) {
						$("#customExcel").show();
					} else {
						$("#customExcel").hide();
					}
				}
			});
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}

	});
}
//加载查询条件框
function loadQueryParams(tableConfigId) {
	//清除之前的查询条件
	$(".query-param").remove();
	twxAjax('Thing.Fn.TableSearch', 'GetQueryParams', {
		tableConfigId: tableConfigId
	}, true, function (res) {
		var params = res.data;
		if (res.success) {
			for (var i = 0; i < params.length; i++) {
				var paramId = params[i].ID;
				var paramName = params[i].PARAM_NAME;
				var td = '<td class="query-param" style="padding-left:15px;">' + paramName +
					'：<input colName = "' + params[i].COL_NAME + '" class="param-input" id="param-' + paramId +
					'" style="width:120px"/></td>';
				$("#paramTr").append(td);
				$("#param-" + paramId).textbox();
			}
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});
}

//置空查询条件
function resetForm() {
	$(".param-input").each(function (i, n) {
		$(n).textbox('setValue', '');
	});
	searchTable();
}

function exportCustomExcel() {
	var fileFlag = false;
	layer.open({
		title: '自定义导出',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['450px', '300px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要导入的excel文件!', {
					icon: 2
				});
				return false;
			}
			uploadInst.config.url = fileHandlerUrl + "/table/custom/export?tableId=" + tableConfigId +
				"&tableName=" + tableConfigName +
				"&isCurve=" + $("#isCurve")[0].checked +
				"&isRelatedData=" + getIsRelatedDataValue() +
				"&username=" + sessionStorage.getItem("username");
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function (layero, index, that) {
			var addTpl = '';
			addTpl = $("#uploadHtml")[0].innerHTML;
			$("#uploadContent").append(addTpl);
			if (tableConfigName == '热敏电阻加工') {
				$("#curveDiv").show();
				// 初始化联动逻辑
				initCurveRelatedDataLogic();
				layer.style(index, {
					height: '400px'
				});
			} else {
				$("#curveDiv").hide();
				$("#relatedDataDiv").hide();
				layer.style(index, {
					height: '300px'
				});
			}
		}
	});

	form.render(null, 'uploadForm');

	$("#downloadTpl").unbind('click').bind('click', function () {
		var loading;
		var url = fileHandlerUrl + "/table/export/custom/tpl";
		$.fileDownload(url, {
			httpMethod: 'POST',
			data: {
				"tableId": tableConfigId,
				"tableName": tableConfigName
			},
			prepareCallback: function (url) {
				loading = layer.msg("正在下载...", {
					icon: 16,
					shade: 0.3,
					time: 0
				});
			},
			abortCallback: function (url) {
				layer.close(loading);
				layer.msg("下载异常！！");
			},
			successCallback: function (url) {
				layer.close(loading);
			},
			failCallback: function (html, url) {
				layer.close(loading);
				layer.msg("下载失败！！");
			}
		});
	});
	var loadIndex;
	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + "/table/custom/export?tableId=" + tableConfigId + "&tableName=" + tableConfigName +
			"&isCurve=" + $("#isCurve")[0].checked +
			"&isRelatedData=" + getIsRelatedDataValue() +
			"&username=" + sessionStorage.getItem("username"),
		auto: false,
		accept: 'file',
		field: 'uploadFile',
		exts: 'xlsx',
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;
			var files = obj.pushFile();
			var thisFile = obj.getChooseFiles();
			var thisFileIndex;
			var filename = '';
			for (var k in thisFile) {
				thisFileIndex = k;
				filename = thisFile[k].name;
			}

			for (var k in files) {
				if (thisFileIndex != k) {
					delete files[k];
				}
			}
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			loadIndex = layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			layer.close(loadIndex);
			if (res.success) {
				layer.closeAll();
				window.open("/File/" + res.file);
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}
	});
}

//导出查询的数据
function exportExcel() {
	var loading;
	var url = fileHandlerUrl + "/table/second/export";
	$.fileDownload(url, {
		httpMethod: 'POST',
		data: {
			"query": JSON.stringify(getQueryParam()),
			"tableId": tableConfigId,
			"tableName": tableConfigName,
			"dlwIsAll": 2
		},
		prepareCallback: function (url) {
			loading = layer.msg("正在导出...", {
				icon: 16,
				shade: 0.3,
				time: 0
			});
		},
		abortCallback: function (url) {
			layer.close(loading);
			layer.msg("导出异常！！");
		},
		successCallback: function (url) {
			layer.close(loading);
		},
		failCallback: function (html, url) {
			layer.close(loading);
			layer.msg("导出失败！！");
		}
	});
}

function searchTable() {
	secondTable.queryDataByPage(secondTable.pageOptions.pageSize, 1);
}

function getQueryParam() {
	var params = [];
	$(".param-input").each(function (i, n) {
		var value = $(n).textbox('getValue');
		var name = $(n).attr("colName");
		params.push({
			name: name,
			value: value
		});
	});
	return {
		params: params,
		queryUser: sessionStorage.getItem('username')
	};
}
//二级表对象
var SecondTable = function (tableConfigId) {
	var othis = this;
	this.tableId = "searchDataTable";
	this.cmenu = undefined;
	this.pageOptions = {
		pageSize: 30,
		pageNumber: 1
	};
	this.renderTable = function () {
		var gridHeight = windowH - 90;
		var cb_success = function (data) {
			if (data.success) {
				var dealCol = dealColumns(JSON.parse(data.result), false, true);
				$('#' + othis.tableId).datagrid({
					data: [],
					columns: dealCol.col,
					height: gridHeight,
					singleSelect: false,
					remoteSort: false,
					pagination: true,
					emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
					loadMsg: '正在加载数据...',
					striped: false,
					onHeaderContextMenu: function (e, field) {
						e.preventDefault();
						if (!othis.cmenu) {
							othis.cmenu = gridUtil.createColumnMenu(othis.tableId);
						}
						othis.cmenu.menu('show', {
							left: e.pageX,
							top: e.pageY
						});
					},
					onLoadSuccess: function (data) {
						var rows = data.rows;
						var $datagrid = $('#' + othis.tableId);
						if (rows.length > 0) {
							for (var i = 0; i < rows.length; i++) {
								var row = rows[i];
								var mergedInfo = row.mergedInfo;
								if (mergedInfo != "" && mergedInfo != undefined) {
									var mergeds = mergedInfo.split(",");
									for (var j = 0; j < mergeds.length; j++) {
										var merged = mergeds[j];
										var columnName = merged.split(":")[0];
										var rowspan = merged.split(":")[1];
										$datagrid.datagrid('mergeCells', {
											index: i,
											field: columnName,
											rowspan: rowspan
										});
									}
								}
							}
						}
						changeWidth(othis.tableId);
						$("#root_layout .datagrid-body").css("overflow-x", "auto");
						$('#' + othis.tableId).datagrid('loaded');
					}
				});
			} else {
				layer.alert("表头获取失败！", {
					icon: 2
				});
			}
		}

		var cb_error = function () {
			layer.alert("表头获取失败！", {
				icon: 2
			});
		};

		var parmas = {
			id: tableConfigId
		};
		twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', parmas, false, cb_success, cb_error);
	};
	//初始化分页组件
	this.initPagination = function (data) {
		$('#' + othis.tableId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function () {
					othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions
						.pageNumber);
				}
			}],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function (pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function (pageNumber, pageSize) {
				//返回false可以在取消刷新操作
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			},
			onRefresh: function (pageNumber, pageSize) {
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function (pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};
	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function () {
		$('#' + othis.tableId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
	};
	this.getParams = function () {
		return {
			table_config_id: tableConfigId,
			table_config_name: tableConfigName,
			query: getQueryParam()
		}
	};
	//初始化全部的记录条数
	this.initTotalRecords = function () {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function (data) {
			othis.pageLoadFlag = true;
			othis.totalRecords = data.rows[0].result;
			if (othis.dataLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () { };

		var parmas = othis.getParams();
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataCount', parmas, true, cb_success, cb_error);
	};

	//分页查询数据
	this.queryDataByPage = function (pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.tableId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function (data) {
			othis.dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + othis.tableId).datagrid('loadData', data.array);
			if (othis.pageLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function () {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = othis.getParams();
		parmas.pageSize = pageSize;
		parmas.pageNumber = pageNumber;
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataPage', parmas, true, cb_success, cb_error);
	};

	this.renderTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
}

// 初始化"是否导出曲线"和"是否关联数据"的联动逻辑
function initCurveRelatedDataLogic() {
	// 初始化显示状态
	updateRelatedDataVisibility();

	// 监听"是否导出曲线"开关的变化
	form.on('switch(isCurve)', function (data) {
		updateRelatedDataVisibility();
	});
}

// 更新"是否关联数据"的显示状态
function updateRelatedDataVisibility() {
	var isCurveChecked = $("#isCurve")[0].checked;
	if (isCurveChecked) {
		// 显示"是否关联数据"开关，默认为false
		$("#relatedDataDiv").show();
		$("#isRelatedData")[0].checked = false;
	} else {
		// 隐藏"是否关联数据"开关
		$("#relatedDataDiv").hide();
	}
	// 重新渲染表单以更新开关状态
	form.render('checkbox', 'uploadForm');
}

// 根据联动规则获取isRelatedData的值
function getIsRelatedDataValue() {
	var isCurveChecked = $("#isCurve")[0].checked;
	if (isCurveChecked) {
		// 当"是否导出曲线"为true时，返回用户设置的值
		return $("#isRelatedData")[0].checked;
	} else {
		// 当"是否导出曲线"为false时，强制返回true
		return true;
	}
}