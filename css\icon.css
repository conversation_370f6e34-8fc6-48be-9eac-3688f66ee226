.file {
	background-image: url(../img/file.png) !important;
	background-size: 16px 16px;
}

.report {
	background-image: url(../img/report.png) !important;
	background-size: 16px 16px;
}

.analysis {
	background-image: url(../img/analysis.png) !important;
	background-size: 16px 16px;
}

.photo-icon {
	background-image: url(../img/photo.png) !important;
	background-size: 16px 16px;
}

.product_list {
	background-image: url(../img/product.png) !important;
	background-size: 16px 16px;
}

.design_list {
	background-image: url(../img/design.png) !important;
	background-size: 16px 16px;
}

.gongyi_list {
	background-image: url(../img/gongyi.png) !important;
	background-size: 16px 16px;
}

.guocheng_list {
	background-image: url(../img/guocheng.png) !important;
	background-size: 16px 16px;
}

.quanlity_list {
	background-image: url(../img/quanlity.png) !important;
	background-size: 16px 16px;
}

.icon-cp-save {
	background-image: url(../img/save.png) !important;
}

.icon-cp-pause {
	background-image: url(../img/pause.png) !important;
}

.icon-cp-start {
	background-image: url(../img/start.png) !important;
}

.icon-myupload {
	background: url('../img/upload.png') no-repeat center center;
	width: 16px;
	height: 16px;
}

.icon-myexcel {
	background: url('../img/excel.png') no-repeat center center;
	width: 16px;
	height: 16px;
}

.icon-manualSyn {
	background: url('../img/manualSyn.png') no-repeat center center;
	width: 16px;
	height: 16px;
}

.icon-confirm {
	background: url('../img/confirm.png') no-repeat center center;
	width: 16px;
	height: 16px;
}

.icon-relation {
	background: url('../img/relation.png') no-repeat center center;
	width: 16px;
	height: 16px;
}

.icon-refresh {
	background: url('../img/refresh.png') no-repeat center center !important;
}


.icon-myadd {
	background: url('../img/myadd.png') no-repeat center center;
	width: 16px;
	height: 16px;
}

.icon-myedit {
	background: url('../img/myedit.png') no-repeat center center;
}

.icon-myremove {
	background: url('../img/mydelete.png') no-repeat center center;
}

.icon-myview {
	background: url('../img/myview.png') no-repeat center center;
}

.icon-myconfig {
	background: url('../img/myConfig.png') no-repeat center center;
}

.icon-myprintqrcode {
	background: url('../img/my_print_qrcode.png') no-repeat center center;
}

.icon-mysearch {
	background: url('../img/search1.png') no-repeat center center;
}

.icon-mydownload {
	background: url('../img/download.png') no-repeat center center;
}

.icon-clear {
	background: url('../img/clear.png') no-repeat center center;
}

.icon-ok {
	background: url('../img/ok.png') no-repeat center center;
}
