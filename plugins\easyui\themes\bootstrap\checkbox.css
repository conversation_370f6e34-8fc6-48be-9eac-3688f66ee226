.checkbox {
  position: relative;
  border: 2px solid #0070a9;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.checkbox-checked {
  border: 0;
  background: #0070a9;
}
.checkbox-inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.checkbox path {
  stroke-width: 2px;
}
.checkbox-disabled {
  opacity: 0.6;
}
.checkbox-value {
  position: absolute;
  overflow: hidden;
  width: 1px;
  height: 1px;
  left: -999px;
}
