<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>确认表签署统计</title>
    <link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css">
    <style>
        body { font-family: "Microsoft YaHei", sans-serif; background-color: #f5f5f5; margin: 0; padding: 0; }
        .dashboard-container { max-width: 1600px; margin: 0 auto; padding: 10px; background-color: #fff; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 4px; }
        .dashboard-header { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee; margin-bottom: 10px; }
        .dashboard-title { margin: 0; color: #333; font-size: 22px; font-weight: 600; }
        .dashboard-subtitle { color: #666; font-size: 14px; margin-top: 5px; }
        .dashboard-toolbar { display: flex; gap: 10px; }
        .dashboard-content { margin-top: 10px; }
        .info-box { background-color: #f8f8f8; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #FF5722; }
        .info-text { margin: 0; color: #666; }
        .layui-btn i { margin-right: 5px; font-size: 14px; }
        .done { background-color: rgb(255, 245, 217); }
        .unsigned-position { display: inline-flex; align-items: center; justify-content: center; margin: 2px; padding: 1px 2px; background-color: #fff1f0; border: 1px solid #ffccc7; border-radius: 2px; color: #ff4d4f; font-size: 12px; line-height: 1.2; white-space: nowrap; flex-grow: 0; flex-shrink: 0; flex-basis: calc(100%/3 - 4px); box-sizing: border-box; }
        .unsigned-positions-wrapper { display: flex; flex-wrap: wrap; overflow-x: hidden; padding: 2px; align-content: flex-start; max-height: none; overflow-y: visible; }
        /* 自适应行高的表格样式 */
        .layui-table-cell { height: auto; white-space: normal; }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div>
                <h2 class="dashboard-title">确认表签署统计</h2>
                <p class="dashboard-subtitle">显示所有应签未签的确认表信息，您可以进行定位或导出为Excel</p>
            </div>
            <div class="dashboard-toolbar">
                <button class="layui-btn" onclick="exportExcel()"><i class="layui-icon layui-icon-export"></i>导出Excel</button>
            </div>
        </div>
        <div class="info-box">
            <p class="info-text">共找到 <span id="total-count">0</span> 个应签未签的确认表。点击"定位"按钮可直接跳转至对应表格。</p>
        </div>
        <div class="dashboard-content">
            <table id="statisticsTable" lay-filter="statisticsTable"></table>
        </div>
    </div>
    
    <script type="text/html" id="barTpl">
        <button class="layui-btn layui-btn-sm" lay-event="locate"><i class="layui-icon layui-icon-location"></i>定位</button>
    </script>
    
    <script type="text/html" id="positionsTpl">
        <div class="unsigned-positions-wrapper">
            {{# 
              var positions = []; 
              if(d.unsignedCells && d.unsignedCells.length > 0) { 
                positions = d.unsignedCells; 
              } else if(d.UNSIGNED_POSITIONS) { 
                try { 
                  positions = JSON.parse(d.UNSIGNED_POSITIONS); 
                } catch(e) { positions = []; } 
              } 
            }}
            {{# if(positions && positions.length > 0){ }}
              {{# layui.each(positions, function(index, item){ }}
                <span class="unsigned-position">行{{ item.row }} 列{{ item.col }}</span>
              {{# }); }}
            {{# } else { }}
              <span>无详细信息</span>
            {{# } }}
        </div>
    </script>
    
    <script src="../../plugins/layui-lasted/layui.js"></script>
    <script>
        // 获取URL参数
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return false;
        }
        
        // 窗口ID
        var windowId = getQueryVariable('id') || window.name;
        window.name = windowId;
        
        // 从sessionStorage加载数据
        var statData = JSON.parse(sessionStorage.getItem(windowId));
        var totalCount = document.getElementById("total-count");
        
        // 使用layui模块
        layui.use(["table"], function() {
            var $ = layui.jquery;
            var table = layui.table;
            
            if (statData && statData.data) {
                totalCount.textContent = statData.data.length;
                
                // 初始化表格
                table.render({
                    elem: "#statisticsTable",
                    height: 580,
                    data: statData.data,
                    cols: [[
                        {type: "numbers", title: "序号", width: 60},
                        {field: "FOLDER_NAME", title: "型号分类", width: 90},
                        {field: "MODEL_NAME", title: "型号", width: 90},
                        {field: "PHASE_NAME", title: "阶段", width: 90},
                        {field: "DIR_NAME", title: "专业", width: 140},
                        {field: "LEAF_NAME", title: "过程", width: 140},
                        {field: "REPORT_NAME", title: "A表", width: 200},
                        {field: "TYPE", title: "类型", width: 60, templet: function(d) {
                            if(d.TYPE === "report") return "A表";
                            if(d.TYPE.indexOf("table")>-1) return "B表";
                            return d.TYPE;
                        }},
                        {field: "NAME", title: "表名称"},
                        {title: "应签未签位置", templet: "#positionsTpl", width: 200},
                        {title: "操作", align: "center", toolbar: "#barTpl", width: 100, fixed: "right"}
                    ]],
                    page: false,
                    cellMinWidth: 80,
                    lineStyle: 'height: auto;',
                    done: function(res, curr, count) {
                        $(".layui-table-body tr").on("click", function() {
                            $(this).addClass("done");
                        });
                        
                        // 让表格内容自适应高度
                        $(".layui-table-cell").css("height", "auto");
                        
                        // 同步fixed列高度
                        setTimeout(function() {
                            var mainRows = document.querySelectorAll('.layui-table-body tr');
                            var fixedRows = document.querySelectorAll('.layui-table-fixed-r .layui-table-body tr');
                            for (var i = 0; i < mainRows.length; i++) {
                                if (fixedRows[i]) {
                                    var mainHeight = mainRows[i].offsetHeight;
                                    fixedRows[i].style.height = mainHeight + 'px';
                                }
                            }
                        }, 0);
                    }
                });
                
                // 监听工具条事件
                table.on("tool(statisticsTable)", function(obj) {
                    if (obj.event === "locate") {
                        locateToNode(obj.data.ID);
                    }
                });
            }
        });
        
        // 定位节点函数
        function locateToNode(nodeId) {
            var opener = window.opener;
            if(opener && !opener.closed) {
                opener.postMessage({
                    action: "locateNode",
                    nodeId: nodeId,
                    from: windowId
                }, "*");
            } else {
                alert("主窗口已关闭，无法定位！");
            }
        }
        
        // 导出Excel函数
        function exportExcel() {
            var opener = window.opener;
            if(opener && !opener.closed) {
                opener.postMessage({
                    action: "exportSignExcel",
                    nodeId: statData.nodeId,
                    from: windowId
                }, "*");
            } else {
                alert("主窗口已关闭，无法导出！");
            }
        }
    </script>
</body>
</html> 