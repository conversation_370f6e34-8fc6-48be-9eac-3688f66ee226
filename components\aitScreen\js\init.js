
/**
 * 加载搜索按钮
 */
function renderSearchBtn() {
    $("#search-btn").bind('click', function () {
        //获取型号
        var modelId = $("#model-select").val();
        //获取开始时间和结束时间
        var startDate = $('#start-date').val();
        var endDate = $('#end-date').val();

        loadAll(modelId, startDate, endDate);
    });

    $("#refresh-btn").bind('click', function () {
        layer.confirm('重新刷新会非常耗时，确认需要刷新吗？', function (index) {
            layer.close(index);
            layer.alert('统计正在进行中，请稍后刷新页面查看！', {
                icon: 1
            });
            twxAjax(thing, 'UpdateAllData', {}, true, function (res) { });
        });
    });

    $("#refresh-temp-btn").bind('click', function () {
        var loadIndex = layer.load();
        twxAjax("Thing.Fn.ElectricTest", 'InitTemporaryTable', {}, true, function (res) {
            layer.close(loadIndex);
            if (res.success) {
                //获取型号
                var modelId = $("#model-select").val();
                //获取开始时间和结束时间
                var startDate = $('#start-date').val();
                var endDate = $('#end-date').val();

                loadHandleChart(modelId, startDate, endDate, handlerList[1].fileType, handlerList[1].chartId, handlerList[1].service);
            } else {
                layer.alert(res.msg, {
                    icon: 2
                });
            }
        });
    });

    $("#export-model-process").bind('click', function () {
        var loading;
        $.fileDownload(fileHandlerUrl + "/file/export/model/process", {
            httpMethod: 'POST',
            prepareCallback: function (url) {
                loading = layer.msg("正在导出...", {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });
            },
            abortCallback: function (url) {
                layer.close(loading);
                layer.msg("导出异常！！");
            },
            successCallback: function (url) {
                layer.close(loading);
            },
            failCallback: function (html, url) {
                layer.close(loading);
                layer.msg("导出失败！！");
            }
        });
    });
}

/**
 * 加载更新时间
 */
function renderUpdateTime() {
    twxAjax(thing, 'QueryUpdateTime', {}, true, function (res) {
        if (res.success) {
            var updateTime = res.data;
            $("#update-time").text(updateTime);
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    });
}

/**
 * 加载型号的下拉框
 */
function loadModelSelect() {
    twxAjax(thing, 'QueryAllModel', {
        username: username
    }, true, function (res) {
        if (res.success) {
            var models = res.data;
            $("#model-select").empty().append('<option value="-1">所有型号</option>');
            for (var i = 0; i < models.length; i++) {
                $("#model-select").append('<option value="' + models[i].TREEID + '">' + models[i].NODENAME + '</option>');
            }
            form.render('select');
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    });
}