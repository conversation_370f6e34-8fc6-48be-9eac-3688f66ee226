@font-face {
    font-family: 'MiSans';
    src: url('font/MiSans-Medium.ttf') format('truetype');
    font-weight: 500;
}

@font-face {
    font-family: 'MiSans';
    src: url('font/MiSans-Bold.ttf') format('truetype');
    font-weight: 700;
}

@font-face {
    font-family: 'MiSans';
    src: url('font/MiSans-Regular.ttf') format('truetype');
    font-weight: 400;
}

@font-face {
    font-family: 'AlimamaShuHeiTi';
    src: url('font/AlimamaShuHeiTi-Bold.otf') format('opentype');
    font-weight: 700;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'MiSans', sans-serif;
}

body {
    width: 1920px;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
    color: #333;
    overflow: hidden;
    position: relative;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 153, 255, 0.03) 0%, rgba(0, 102, 204, 0.05) 100%);
    z-index: -1;
}

.container {
    width: 1920px;
    min-height: 100vh;
    padding: 20px 40px 40px;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    margin-bottom: 30px;
    position: relative;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 153, 255, 0.5), transparent);
}

.header-side {
    flex: 1;
}

.header-side:last-of-type {
    display: flex;
    justify-content: flex-end; 
}

.title {
    font-family: 'AlimamaShuHeiTi', sans-serif;
    font-size: 42px;
    font-weight: 700;
    color: #0066cc;
    text-align: center;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 102, 204, 0.1);
    opacity: 0;
    animation: fadeIn 1s ease forwards;
    flex-shrink: 0;
}

.datetime {
    font-size: 18px;
    color: #0066cc;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    opacity: 0;
    animation: fadeIn 1s ease 0.3s forwards;
}

.grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 30px;
    flex-grow: 1;
}

.card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 102, 204, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    opacity: 0;
    animation: fadeIn 0.8s ease forwards;
}

.card:nth-child(1) {
    animation-delay: 0.4s;
}

.card:nth-child(2) {
    animation-delay: 0.6s;
}

.card:nth-child(3) {
    animation-delay: 0.8s;
}

.card:nth-child(4) {
    animation-delay: 1s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(0, 102, 204, 0.15);
}

.card-header {
    background: linear-gradient(90deg, #0080ff 0%, #0066cc 100%);
    color: white;
    padding: 15px 20px;
    font-size: 25px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header i {
    margin-right: 10px;
    font-size: 22px;
}

/* 超链接图标样式 */
.header-link {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.header-link:hover {
    opacity: 1;
    color: white;
}

.header-link i {
    margin-right: 0;
    font-size: 18px;
}

.card-body {
    padding: 20px;
    flex-grow: 1;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
}

.data-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 102, 204, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.data-item:hover {
    transform: scale(1.03);
    box-shadow: 0 6px 16px rgba(0, 102, 204, 0.12);
}

.data-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #0080ff, #00b3ff);
}

.item-label {
    font-size: 22px;
    color: #555;
    margin-bottom: 8px;
    font-weight: 500;
    font
}

.item-value {
    font-size: 32px;
    font-weight: 700;
    color: #0066cc;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.warning .item-value {
    color: #ff6600;
}

.critical .item-value {
    color: #ff3300;
}

.normal .item-value {
    color: #00cc66;
}

.item-value i {
    margin-right: 5px;
    font-size: 24px;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 20px;
    height: 100%;
}

.decoration {
    position: absolute;
    opacity: 0.05;
    z-index: -1;
}

.decoration-1 {
    top: 10%;
    left: 5%;
    width: 300px;
    height: 300px;
    border: 2px solid #0066cc;
    border-radius: 50%;
    animation: rotate 60s linear infinite;
}

.decoration-2 {
    bottom: 15%;
    right: 8%;
    width: 200px;
    height: 200px;
    border: 2px solid #0080ff;
    border-radius: 50%;
    animation: rotate 40s linear infinite reverse;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 加载动画样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(240, 248, 255, 0.95);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid #0066cc;
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
    width: 60px;
    height: 60px;
    top: 10px;
    left: 10px;
    border-top-color: #0080ff;
    animation-duration: 1.2s;
    animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
    width: 40px;
    height: 40px;
    top: 20px;
    left: 20px;
    border-top-color: #00b3ff;
    animation-duration: 0.9s;
}

.loading-text {
    font-size: 18px;
    color: #0066cc;
    font-weight: 500;
    text-align: center;
    animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loadingPulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}