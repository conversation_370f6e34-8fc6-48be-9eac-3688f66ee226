<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<title>产品结构树查询</title>

		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">
		<link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">

		<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
		<link rel="stylesheet" href="../../css/icon.css">
		<script src="../../plugins/easyui/jquery.min.js"></script>
		<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
		<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>


		<script type="text/javascript" src="../../plugins/ztree/js/jquery.ztree.all.min.js"></script>
		<script type="text/javascript" src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
		<script src="../../plugins/layui/layui.js"></script>
		<script type="text/javascript" src="../js/config/twxconfig.js"></script>
		<script type="text/javascript" src="../js/intercept.js"></script>
		<script type="text/javascript" src="../js/util.js"></script>

		<script type="text/javascript" src="../js/logUtil.js"></script>

		<style>
			.table-row-a {
				color: blue;
				cursor: pointer;
			}

			#product_list_table .datagrid-header {
				border-color: #000000;
				border-width: 1px 0 1px 0;
			}

			#product_list_table .datagrid-header td,
			#product_list_table .datagrid-body td {
				border-color: #000000;
			}

			#product_list_table .datagrid-header td {
				font-weight: 600;
			}

			#product_list_table .datagrid-header,
			#product_list_table .datagrid-td-rownumber {
				background-color: transparent !important;
			}

			.tbr-btn {
				margin-left: 10px;
				margin-top: 0px;
				margin-bottom: 10px;
			}

			.fieldlabel {
				font-family: '微软雅黑';
				font-size: 14px;
				width: 100px;
			}
		</style>

	</head>
	<body>
		<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
			<div id="p" data-options="region:'west',split:true,tools:[{iconCls:'pagination-load',handler:function(){asyncBomTree();}}]" title="产品结构树" style="width:350px;padding:10px">
				<ul id="bomTree" class="ztree"></ul>
			</div>
			<div data-options="region:'center'">
				<div id="root_layout_tabs" class="easyui-tabs" data-options="fit:true,border:false" style="width:100%;height:100%;">
					<div title="质量数据" id="product_list_table" data-options="name:'质量数据',tabId:'quality_data',fit:true,iconCls:'product_list'" style="display:none;padding-top: 10px;">
						<div id="msg" style="float: left; width: 100%;color: red;padding-left: 15px;padding-top: 15px;">暂无数据!</div>
						<div style="height: 40px; float: left; width: 100%;">
							<div id="productTypeDiv" style="display: none;margin-left:15px;margin-bottom: 10px; float: left;">
								<input id="productType">
							</div>
							<div id="tbr" style="display: none;float: left;">
								<button type="button" class="layui-btn layui-btn-sm layui-btn-normal tbr-btn" func="" id="product-quality-export">
									<i class="layui-icon">&#xe601;</i> 导出数据
								</button>
								<!-- <button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm layui-btn-warm" func="" id="product-quality-reassociation">
									<i class="layui-icon">&#xe669;</i> 重新关联
								</button>
								<button type="button" style="margin-left: 10px;margin-top: 0px;margin-bottom: 10px;" class="layui-btn layui-btn-sm" func="" id="product-quality-productLink">
									<i class="layui-icon">&#xe669;</i> 过程结构树关联
								</button> -->
								<!-- <button type="button" id="push" class="layui-btn layui-btn-sm tbr-btn">
									<i class="layui-icon">&#xe609;</i> 数据推送
								</button>
								<button type="button" id="confirm" class="layui-btn layui-btn-sm layui-btn-normal tbr-btn">
									<i class="layui-icon">&#xe672;</i> 数据确认
								</button>
								<button type="button" id="generateReport" class="layui-btn layui-btn-sm layui-btn-warm tbr-btn">
									<i class="layui-icon">&#xe629;</i> 生成质量报告
								</button>
								<button type="button" id="viewReport" class="layui-btn layui-btn-sm layui-btn-danger tbr-btn">
									<i class="layui-icon">&#xe60a;</i> 查看质量报告
								</button> -->
							</div>
						</div>
						<div id="secondTableDiv" style="float: left; width: 100%;">
							<!-- 显示二级表表格 -->
							<div id="secondTable" style="float: left;" data-options="border:false"></div>
						</div>
					</div>
					<div title="影像记录" data-options="name:'影像记录',tabId:'photo_table',queryType:'photo',fit:true,iconCls:'photo-icon'" style="display:none;">
						<div id="photo_table" data-options="border:false"></div>
					</div>
					<div title="设计类要求" data-options="name:'设计类',tabId:'design_list_table',queryType:'design',fit:true,iconCls:'design_list'" style="display:none;">
						<div id="design_list_table" data-options="border:false"></div>
					</div>
					<div title="工艺类要求" data-options="name:'工艺类',tabId:'craft_list_table',queryType:'craft',fit:true,iconCls:'gongyi_list'" style="display:none;">
						<div id="craft_list_table" data-options="border:false"></div>
					</div>
					<div title="过程控制记录" data-options="name:'过程控制',tabId:'processcontrol_list_table',queryType:'process',fit:true,iconCls:'guocheng_list'" style="display:none;">
						<div id="processcontrol_list_table" data-options="border:false"></div>
					</div>
					<div title="质量综合信息" data-options="name:'质量综合',tabId:'qualitycontrol_list_table',queryType:'quality',fit:true,iconCls:'quanlity_list'" style="display:none;">
						<div id="qualitycontrol_list_table" data-options="border:false"></div>
					</div>
					<!-- <div title="报表汇总" data-options="name:'报表汇总',tabId:'report_table',fit:true,iconCls:'report'" style="display:none;">
						<div id="report_table" data-options="border:false"></div>
					</div>
					<div title="关键指标分析" data-options="name:'关键指标分析',tabId:'analysis_table',fit:true,iconCls:'analysis'" style="display:none;">
						<div id="analysis_table" data-options="border:false"></div>
					</div> -->
				</div>
			</div>
		</div>
	</body>
	<script src="../../plugins/index/jquery.fileDownload.js"></script>
	<script type="text/javascript" src="productQuality.js"></script>
	<script type="text/javascript" src="productDataSearch.js"></script>
</html>
<script type="text/html" id="pushHtml">
	<form class="layui-form" lay-filter="pushForm">
		<input type="hidden" name="table_config_id" class="layui-input" id="table_config_id">
		<input type="hidden" name="pusher" class="layui-input" id="pusher">
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">确认人员:</label>
			<div class="layui-input-block">
				<select name="receiver" multiple="multiple" id="receiver">
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="fieldlabel layui-form-label">备注:</label>
			<div class="layui-input-block">
				<textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<button id="push-form-submit" class="layui-btn" lay-submit lay-filter="push">推送</button>
		</div>
	</form>
</script>
