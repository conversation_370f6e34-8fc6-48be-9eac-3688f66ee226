var tree, table, upload, form, listId, optType, util, selectFolderId = '-1',
	saveData;
var THING = 'Thing.Fn.Interface';
layui.use(function() {
	tree = layui.tree;
	table = layui.table;
	upload = layui.upload;
	form = layui.form;
	util = layui.util;
	listId = getQueryString("listId");
	optType = getQueryString("type");
	if (optType == 'toDo') {
		$("#createPkg").removeClass('layui-hide');
		$("#createTplPkg").removeClass('layui-hide');
		$("#addListTpl").removeClass('layui-hide');
	}
	renderPkg();
	util.on({
		createPkg: function() {
			if (saveData.datapacketName) {
				layer.msg("已创建数据包！")
			} else {
				createPkg();
			}
		},
		createTplPkg: function() {
			if (saveData.datapacketName) {
				layer.msg("已创建数据包！")
			} else {
				createTplPkg();
			}
		},
		addListTpl: function() {
			if (saveData.datapacketName) {
				addListTpl();
			} else {
				layer.msg("请先创建数据包！")
			}
		}
	});
});

/**
 * 添加清单模板
 */
function addListTpl() {
	layer.open({
		type: 1,
		title: '添加清单模板',
		area: ['1600px', '600px'],
		content: `<div class="layui-row">
					<div class="layui-col-md3">
						<div class="layui-card">
							<div class="layui-card-header">表单</div>
							<div class="layui-card-body">
								<form class="layui-form" style="height:418px">
									<div class="layui-form-item">
										<label class="layui-form-label" style="width: 60px;">清单名称</label>
										<div class="layui-input-block" style="margin-left: 90px;">
											<input type="text" name="dataPacketListName" lay-verify="required" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label" style="width: 60px;">项目类型</label>
										<div class="layui-input-block" style="margin-left: 90px;">
											<select id="projectCategory" name="projectCategory"></select>
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label" style="width: 60px;">采集时机</label>
										<div class="layui-input-block" style="margin-left: 90px;">
											<input type="text" id="collectionTimingInput" name="collectionTiming" lay-verify="required" readonly="readonly" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label" style="width: 60px;">所选模板</label>
										<div class="layui-input-block" style="margin-left: 90px;">
											<input type="text" id="templateNameInput" name="templateName" lay-verify="required" readonly="readonly" autocomplete="off" class="layui-input">
										</div>
									</div>
									<input type="text" id="templateIdInput" name="templateId" class="layui-hide">
									<input type="text" id="templateType" name="templateType" class="layui-hide">
									<div class="layui-hide">
											<button class="layui-btn" lay-submit id="list-tpl-create" lay-filter="list-tpl-create">确定</button>
											<button type="reset" class="layui-btn layui-btn-primary">清除</button>
									</div>
								</form>
							</div>
						</div>
					</div>
					<div class="layui-col-md2">
						<div class="layui-card">
							<div class="layui-card-header">采集时机</div>
							<div class="layui-card-body">
								<form class="layui-form" style="padding-bottom:15px">
									<select id="collectionTiming" lay-filter="collectionTiming-select-filter"></select>
								</form>
								<table class="layui-hide" id="collect-node-table"></table>
							</div>
						</div>
					</div>
					<div class="layui-col-md7">
						<div class="layui-card">
							<div class="layui-card-header">清单模板</div>
							<div class="layui-card-body">
								<form class="layui-form">
									<div class="layui-form-item" style="margin-bottom:10px">
										<div class="layui-inline">
											<label class="layui-form-label" style="width: 100px;">清单模板名称</label>
											<div class="layui-input-inline">
												<input type="text" id="listName" name="listName" class="layui-input">
											</div>
										</div>
										<div class="layui-inline">
											<button class="layui-btn " lay-submit lay-filter="list-tpl-table-search">搜索</button>
											<button type="reset" class="layui-btn layui-btn-primary">清除</button>
										</div>
									</div>
								</form>
								<table class="layui-hide" id="list-tpl-table"></table>
							</div>
						</div>
					</div>
				</div>`,
		btn: ['确定', '取消'],
		btn1: function(index, layero) {
			$("#list-tpl-create").click();
			return false;
		},
		btn2: function(index, layero) {
			layer.close(index);
		},
		success: function(layero, index) {
			initProjectCategory();
			submitListTpl();
			initCollectionTiming();
			initListTplTable();
			form.on('submit(list-tpl-table-search)', function(data) {
				var field = data.field;
				table.reload('list-tpl-table', {
					page: {
						curr: 1
					},
					where: {
						listName: field.listName
					}
				});
				return false;
			});
		}
	});
}

function initProjectCategory() {
	twxAjax(THING, "QueryProjectCategory", {}, true, function(res) {
		if (res.success) {
			var data = res.data;
			for (var i = 0; i < data.length; i++) {
				var $option = $("<option value='" + data[i].TYPE_CODE + "'>" + data[i].TYPE_NAME + "</option>");
				$("#projectCategory").append($option);
			}
			form.render('select');
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});
}

function submitListTpl() {
	form.on('submit(list-tpl-create)', function(data) {
		var field = data.field;
		field.planningListId = listId;
		twxAjax(THING, "CreatePkgList", field, true, function(res) {
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				renderPkg();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		});
		return false;
	});
}

function initCollectionTiming() {
	twxAjax(THING, "QueryCollectionTiming", {}, true, function(res) {
		if (res.success) {
			var data = res.data;
			for (var i = 0; i < data.length; i++) {
				var $option = $("<option value='" + data[i].TYPE_GROUP_NAME + "'>" + data[i].TYPE_GROUP_NAME + "</option>");
				$option.data("children", data[i].children);
				$("#collectionTiming").append($option);
			}
			$("#collectionTimingInput").val(data[0].TYPE_GROUP_NAME);
			form.render('select');
			renderCollectNodes(data[0].children);
			form.on('select(collectionTiming-select-filter)', function(data) {
				$("#collectionTimingInput").val(data.value);
				var children = $("#collectionTiming option:selected").data("children");
				renderCollectNodes(children);
			});
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});
}


function renderCollectNodes(nodes) {
	table.render({
		elem: '#collect-node-table',
		data: nodes,
		height: 375,
		cols: [
			[{
				field: 'DISPLAY_ORDER',
				width: 60,
				align: "center",
				title: '排序'
			}, {
				field: 'TYPE_NAME',
				title: '节点名称',
				align: "center"
			}]
		]
	});
}

//加载清单模板列表
function initListTplTable() {
	table.render({
		elem: '#list-tpl-table',
		url: getUrl(THING, 'QueryListTpl'),
		height: 375,
		cols: [
			[{
				type: 'radio',
				width: 60,
				align: "center"
			}, {
				field: 'ROWNO',
				width: 60,
				align: "center",
				title: '序号'
			}, {
				field: 'TEMPLATE_NAME',
				title: '模板名称',
				align: "center"
			}, {
				field: 'TEMPLATE_CATEGORY',
				title: '模板类型',
				align: "center",
				width: 115,
				templet: function(d) {
					return d.TEMPLATE_CATEGORY === 'tableTemplate' ? '结构化表单' : '电子实体文件';
				}
			}, {
				field: 'GRADE',
				title: '所属级别',
				align: "center",
				width: 120
			}, {
				field: 'SCOPE_OF_APPLICATION',
				title: '适用范围',
				align: "center",
				width: 100
			}, {
				field: 'COLLECT_UNIT',
				title: '采集单位',
				align: "center",
				width: 100
			}, {
				field: 'VERSION',
				title: '版本',
				align: "center",
				width: 60
			}]
		],
		page: {
			layout: ['limit', 'count', 'prev', 'page', 'next', 'refresh', 'skip'],
			groups: 1,
			first: false,
			last: false
		}
	});

	table.on('radio(list-tpl-table)', function(obj) {
		$("#templateNameInput").val(obj.data['TEMPLATE_NAME']);
		$("#templateIdInput").val(obj.data['ID']);
		$("#templateType").val(obj.data['TEMPLATE_CATEGORY']);
	});
}

function createPkg() {
	layer.open({
		type: 1,
		title: '创建数据包',
		area: ['400px', '330px'],
		content: `<form class="layui-form" style="padding: 15px 15px 0 0;">
					<div class="layui-form-item">
						<label class="layui-form-label">数据包名称</label>
						<div class="layui-input-block">
							<input type="text" name="datapacketName" class="layui-input" lay-verify="required">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">数据包编号</label>
						<div class="layui-input-block">
							<input type="text" name="datapacketNumber" class="layui-input" lay-verify="required">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">数据包类型</label>
						<div class="layui-input-block">
							<select name="datapacketType">
								<option value="型号产品">型号产品</option>
								<option value="通用产品">通用产品</option>
							</select>
						</div>
					</div>
					
					<div class="layui-hide">
						<button class="layui-btn" id="create-pkg" lay-submit lay-filter="create-pkg-submit">搜索</button>
						<button type="reset" id="reset-create-pkg" class="layui-btn layui-btn-primary">清除</button>
					</div>
				</form>`,
		btn: ['确定', '重置', '取消'],
		btn1: function(index, layero) {
			$("#create-pkg").click();
		},
		btn2: function(index, layero) {
			$("#reset-create-pkg").click();
			return false;
		},
		btn3: function(index, layero) {
			layer.close(index);
		},
		success: function(layero, index) {
			form.render();
			$(layero).find('.layui-layer-content').css('overflow', 'visible');
			form.on('submit(create-pkg-submit)', function(data) {
				var field = data.field;
				field.planningListId = listId;
				twxAjax(THING, "CreatePkg", field, true, function(res) {
					if (res.success) {
						layer.msg(res.msg);
						layer.close(index);
						renderPkg();
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				});
				return false;
			});
		}
	});
}

function savePkgData(saveData) {

}

function renderPkg() {
	twxAjax(THING, "QueryPlanListPkg", {
		planningListId: listId
	}, true, function(res) {
		if (res.success) {
			saveData = res.data;
			renderPkgTree(saveData);
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	})
}

function renderPkgTree(saveData) {
	var treeData = getPkgTreeData(saveData);
	// 渲染
	tree.render({
		elem: '#tree',
		data: treeData,
		edit: optType == 'toDo' ? ['del'] : false,
		showLine: true,
		showCheckbox: false, // 是否显示复选框
		onlyIconControl: true, // 是否仅允许节点左侧图标控制展开收缩
		id: 'tree',
		isJump: false, // 是否允许点击节点时弹出新窗口跳转
		click: function(obj) {
			var data = obj.data; //获取当前点击的节点数据
			$('.layui-tree-entry').css('background-color', '');
			$(obj.elem[0]).children('.layui-tree-entry').css('background-color', 'aliceblue');
			clickTree(data, saveData);
		},
		operate: function(obj) {
			var type = obj.type; // 得到操作类型：add、edit、del
			var data = obj.data; // 得到当前节点的数据
			var elem = obj.elem; // 得到当前节点元素

			// Ajax 操作
			var id = data.id; // 得到节点索引
			if (type === 'update') { // 修改节点
				console.log(elem.find('.layui-tree-txt').html()); // 得到修改后的内容
			} else if (type === 'del') {
				if (obj.data.type == 'list') {
					twxAjax(THING, "DeletePkgList", {
						planningListId: listId,
						templateId: obj.data.templateId
					}, true, function(res) {
						if (res.success) {
							layer.msg(res.msg);
							renderPkg();
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					});
				} else if (obj.data.type == 'package') {
					twxAjax(THING, "DeletePkg", {
						planningListId: listId
					}, true, function(res) {
						if (res.success) {
							layer.msg(res.msg);
							renderPkg();
						} else {
							layer.alert(res.msg, {
								icon: 2
							});
						}
					});
				}

			};
		},
		customName: {
			id: 'id',
			title: 'title',
			children: 'dataPacketTplList'
		},
	});
}

function clickTree(node, saveData) {
	$("#list-body").empty();
	if (node.type == 'package') {
		$("#list-header").html("数据包信息");
		var forms = [{
			name: "数据包名称",
			value: node.datapacketName
		}, {
			name: "数据包编号",
			value: node.datapacketNumber
		}, {
			name: "数据包类型",
			value: node.datapacketType
		}, {
			name: "型号代号",
			value: node.modelCode
		}];
		$("#list-body").append(get$Form(forms));
	} else {
		$("#list-header").html("项目清单详细内容");
		var templateId = node.templateId;
		var templateType = node.templateType;
		var listHeaders = node.listHeaders;
		var forms = [{
			name: "清单项名称",
			value: node.dataPacketListName
		}, {
			name: "清单项模板",
			value: node.templateName
		}, {
			name: "项目类型",
			value: node.projectCategory
		}];
		$("#list-body").append(get$Form(forms));

		var $tab = $('<div class="layui-tab layui-tab-brief"></div>');
		var $ul = $('<ul class="layui-tab-title"></ul>');
		var $content = $('<div class="layui-tab-content" style="padding-top:15px;padding-bottom:0"></div>');

		var dataPacketList = saveData.dataPacketList;
		var collectNodes = [];
		for (var i = 0; i < dataPacketList.length; i++) {
			if (dataPacketList[i].templateId == templateId) {
				collectNodes.push(dataPacketList[i]);
			}
		}

		for (var i = 0; i < collectNodes.length; i++) {
			var ulClass = "",
				contentClass = "";
			if (i == 0) {
				ulClass = 'layui-this', contentClass = 'layui-show';
			}
			$ul.append('<li class="' + ulClass + '" style="font-weight: bold;">' + collectNodes[i].collectNodeName + '</li>');
			$content.append('<div class="layui-tab-item ' + contentClass + '"> <table class="layui-hide" lay-filter="' + templateId + "-" + collectNodes[i].collectNode + '" id="' + templateId + "-" + collectNodes[i].collectNode + '"></table></div>');
		}

		$tab.append($ul).append($content);
		$("#list-body").append($tab);

		//加载表格
		var colRes = getTableCols(listHeaders, templateType);;
		var btns = [];
		if (templateType == 'tableTemplate') {
			//表格
			btns = [{
				name: '新增',
				event: "addRow",
				icon: 'add-1'
			}, {
				name: '删除',
				event: "deleteRow",
				icon: 'delete'
			}, {
				name: '保存',
				event: "saveTable",
				icon: 'release'
			}, {
				name: '导出',
				event: "exportTable",
				icon: 'export'
			}, {
				name: '导入',
				event: "importTable",
				icon: 'upload-circle'
			}];
		} else if (templateType == 'eleEntityDoc') {
			//文件
			btns = [{
				name: '上传',
				event: "addFile",
				icon: 'add-1'
			}, {
				name: '删除',
				event: "deleteRow",
				icon: 'delete'
			}, {
				name: '保存',
				event: "saveTable",
				icon: 'release'
			}];
		}
		if (optType != 'toDo') {
			btns = [];
		}

		for (var i = 0; i < collectNodes.length; i++) {
			renderTable(templateId + "-" + collectNodes[i].collectNode, collectNodes[i].collectNodeName, collectNodes[i].collectionDataList, colRes, btns, collectNodes[i]);
		}
	}
}

function getTableCols(listHeaders, templateType) {
	var tableCols = [];
	tableCols.push({
		type: "checkbox",
		fixed: 'left'
	});
	tableCols.push({
		title: "#",
		type: "numbers",
		fixed: 'left'
	});
	var selects = [];
	if (templateType == 'tableTemplate') {
		tableCols.push({
			title: "操作",
			width: 60,
			type: "opt",
			toolbar: '<div class="layui-clear-space">\
						<a style="cursor: pointer;font-weight: bold;padding-left: 5px;" title="上传文件" lay-event="uploadRowFile">\
							<i class="layui-icon layui-icon-upload-circle" style="font-size: 19px;color: #16b777;"></i>\
						</a>\
					  </div>',
			fixed: 'left'
		});

		for (var i = 0; i < listHeaders.length; i++) {
			var minWidth = StringTolog(listHeaders[i].title) * 15 + 30;
			var col = {
				minWidth: minWidth,
				field: listHeaders[i].field,
				title: listHeaders[i].title,
				align: "center"
			};
			if (optType == 'toDo') {
				if (listHeaders[i].type == 'select') {
					col.templet = function(d) {
						var selectOptions = this.selectOptions;
						var field = this.field;
						var $select = $('<select name="' + field + '" lay-filter="select-' + field + '" lay-append-to="body"></select>');
						for (var j = 0; j < selectOptions.length; j++) {
							if (d[field] == selectOptions[j].name) {
								$select.append('<option value="' + selectOptions[j].name + '" selected>' + selectOptions[j].name + '</option>');
							} else {
								$select.append('<option value="' + selectOptions[j].name + '">' + selectOptions[j].name + '</option>');
							}
						}
						var selectHtml = $select[0].outerHTML;
						return selectHtml;
					};
					col.type = "select";
					col.selectOptions = listHeaders[i].selectOptions;
					selects.push(listHeaders[i].field);
				} else {
					col.edit = 'text';
				}
			} else {
				col.edit = false;
			}
			tableCols.push(col);
		}
	} else {
		for (var i = 0; i < listHeaders.length; i++) {
			var minWidth = StringTolog(listHeaders[i].title) * 15 + 30;
			var col = listHeaders[i];
			col.minWidth = minWidth;
			col.align = "center";
			tableCols.push(col);
		}
	}

	return {
		selects: selects,
		tableCols: tableCols
	};
}

function getPkgTreeData(saveData) {
	//组建tree数据
	var treeData = saveData;
	treeData.title = saveData.datapacketName;
	treeData.id = saveData.datapacketName;
	treeData.spread = true;
	treeData.type = 'package';
	var dataPacketTplList = saveData.dataPacketTplList;
	for (var i = 0; i < dataPacketTplList.length; i++) {
		dataPacketTplList[i].title = dataPacketTplList[i].dataPacketListName;
		dataPacketTplList[i].templateId = dataPacketTplList[i].templateId;
		dataPacketTplList[i].type = "list";
	}
	return [treeData];
}


function createTplPkg() {
	layer.open({
		type: 1,
		title: '选择数据包模板',
		area: ['1600px', '600px'],
		content: `<div class="layui-row">
	<div class="layui-col-md3">
		<div class="layui-card">
			<div class="layui-card-header">表单</div>
			<div class="layui-card-body">
				<form class="layui-form" style="height:418px;overflow:auto;">
					<div class="layui-form-item">
						<label class="layui-form-label">数据包名称</label>
						<div class="layui-input-block">
							<input type="text" name="datapacketName" class="layui-input" lay-verify="required">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">数据包编号</label>
						<div class="layui-input-block">
							<input type="text" name="datapacketNumber" class="layui-input" lay-verify="required">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">数据包类型</label>
						<div class="layui-input-block">
							<select name="datapacketType">
								<option value="型号产品">型号产品</option>
								<option value="通用产品">通用产品</option>
							</select>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">采集时机</label>
						<div class="layui-input-block">
							<input type="text" id="collectionTimingInput" name="collectionTiming" lay-verify="required" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">所选模板</label>
						<div class="layui-input-block">
							<input type="text" id="datapacketTplInput" name="datapacketTpl" readonly="readonly" class="layui-input" lay-verify="required">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">密级</label>
						<div class="layui-input-block">
							<input type="text" id="secretLevelInput" name="secretLevel" readonly="readonly" class="layui-input" lay-verify="required">
						</div>
					</div>
					<input type="text" id="datapacketTplIdInput" name="datapacketTplId" class="layui-hide">
					<div class="layui-hide">
						<button class="layui-btn" lay-submit id="pkg-tpl-create" lay-filter="pkg-tpl-create">确定</button>
						<button type="reset" class="layui-btn layui-btn-primary">清除</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	<div class="layui-col-md2">
		<div class="layui-card">
			<div class="layui-card-header">采集时机</div>
			<div class="layui-card-body">
				<form class="layui-form" style="padding-bottom:15px">
					<select id="collectionTiming" lay-filter="collectionTiming-select-filter"></select>
				</form>
				<table class="layui-hide" id="collect-node-table"></table>
			</div>
		</div>
	</div>
	<div class="layui-col-md2">
		<div class="layui-card">
			<div class="layui-card-header">文件夹</div>
			<div class="layui-card-body">
				<div id="folderTree" style="height:428px"></div>
			</div>
		</div>
	</div>
	<div class="layui-col-md5">
		<div class="layui-card">
			<div class="layui-card-header">数据包模板</div>
			<div class="layui-card-body">
				<form class="layui-form">
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label" style="width: 100px;">数据包模板名称</label>
							<div class="layui-input-inline">
								<input type="text" id="pkgName" name="pkgName" class="layui-input">
							</div>
						</div>
						<div class="layui-inline">
							<button class="layui-btn " lay-submit lay-filter="pkg-tpl-table-search">搜索</button>
							<button type="reset" class="layui-btn layui-btn-primary">清除</button>
						</div>
					</div>
				</form>
				<table class="layui-hide" id="pkg-tpl-table"></table>
			</div>
		</div>
	</div>
</div>`,
		btn: ['确定', '取消'],
		btn1: function(index, layero) {
			$("#pkg-tpl-create").click();
			return false;
		},
		btn2: function(index, layero) {
			layer.close(index);
		},
		success: function(layero, index) {
			initProjectCategory();
			submitPkgTpl();
			initCollectionTiming();
			initFolderTree();
			initPkgTplTable();
			form.on('submit(pkg-tpl-table-search)', function(data) {
				var field = data.field;
				table.reload('pkg-tpl-table', {
					page: {
						curr: 1
					},
					where: {
						folderId: selectFolderId,
						pkgName: field.pkgName
					}
				});
				return false;
			});
		}
	});
}

function submitPkgTpl() {
	form.on('submit(pkg-tpl-create)', function(data) {
		var field = data.field;
		field.planningListId = listId;
		twxAjax(THING, "CreatePkgTpl", field, true, function(res) {
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				renderPkg();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		});
		return false;
	});
}

function transformTree(data) {
	// 创建一个空对象，用于存储每个节点的信息
	var map = {};
	// 创建一个空数组，用于存储根节点
	var root = [];
	// 遍历数据
	data.forEach(function(item) {
		// 如果map中没有当前节点的id，则将当前节点添加到map中，并创建一个空数组，用于存储当前节点的子节点
		if (!map[item.ID]) {
			map[item.ID] = item;
			map[item.ID].children = [];
			// 如果当前节点的pid为-1，则将当前节点添加到根节点数组中
			if (item.PID === '-1') {
				map[item.ID].spread = true;
				root.push(map[item.ID]);
			} else {
				// 如果map中没有当前节点的pid，则将当前节点的pid添加到map中，并创建一个空数组，用于存储当前节点的父节点的子节点
				if (!map[item.PID]) {
					map[item.PID] = {};
					map[item.PID].children = [];
				}
				// 将当前节点添加到当前节点的父节点的子节点数组中
				map[item.PID].children.push(map[item.ID]);
			}
		}
	});
	// 返回根节点数组
	return root;
}

function initFolderTree() {
	twxAjax(THING, "QueryAllFolder", {}, true, function(res) {
		if (res.success) {
			var treeData = transformTree(res.data);
			tree.render({
				elem: '#folderTree',
				data: treeData,
				showLine: true,
				showCheckbox: false,
				onlyIconControl: true,
				id: 'folderTree',
				isJump: false,
				click: function(obj) {
					var data = obj.data; //获取当前点击的节点数据
					$('.layui-tree-entry').css('background-color', '');
					$(obj.elem[0]).children('.layui-tree-entry').css('background-color', 'aliceblue');
					selectFolderId = data.ID;
					table.reload('pkg-tpl-table', {
						page: {
							curr: 1
						},
						where: {
							folderId: selectFolderId,
							pkgName: $("#pkgName").val()
						}
					});
				},
				customName: {
					id: 'ID',
					title: 'NAME',
					children: 'children'
				},
			});
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	});
}

function initPkgTplTable() {
	table.render({
		elem: '#pkg-tpl-table',
		url: getUrl(THING, 'QueryPkgTpl'),
		height: 370,
		cols: [
			[{
				type: 'radio',
				width: 60,
				align: "center"
			}, {
				field: 'ROWNO',
				width: 60,
				align: "center",
				title: '序号'
			}, {
				field: 'TEMPLATE_NAME',
				title: '数据包模板名称',
				align: "center"
			}, {
				field: 'FIELD',
				title: '领域',
				align: "center",
				width: 100
			}, {
				field: 'SECURITY_LEVEL',
				title: '密级',
				align: "center",
				width: 100
			}, {
				field: 'TEMPLATE_VERSION',
				title: '版本',
				align: "center",
				width: 100
			}]
		],
		page: {
			layout: ['limit', 'count', 'prev', 'page', 'next', 'refresh', 'skip'],
			groups: 1,
			first: false,
			last: false
		}
	});

	table.on('radio(pkg-tpl-table)', function(obj) {
		$("#datapacketTplInput").val(obj.data['TEMPLATE_NAME']);
		$("#datapacketTplIdInput").val(obj.data['ID']);
		$("#secretLevelInput").val(obj.data['SECURITY_LEVEL']);
	});
}