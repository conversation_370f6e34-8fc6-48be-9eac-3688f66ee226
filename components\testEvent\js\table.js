function reloadTable(treeId) {
    table.reloadData('event-table', {
        where: {
            treeId: treeId
        }, scrollPos: 'fixed '
    });
}

function renderSelect($select, data) {
    $select.empty();
    $select.append($("<option value=''></option>"));
    for (var i = 0; i < data.length; i++) {
        var option = $("<option value='" + data[i] + "'>" + data[i] + "</option>");
        $select.append(option);
    }
}

function initSelect(init, modelName = '') {
    twxAjax(THING, 'GetTestEventSearchItem', {modelName: modelName}, true, function (res) {
        if (res.success) {
            var data = res.data;
            if (init) {
                renderSelect($("#model_name"), data.modelName);
                renderSelect($("#problem_category"), data.problemCategory);
                renderSelect($("#closure_status"), data.closureStatus);
                renderSelect($("#test_item"), data.testItem);
                form.render('select');
                form.on('select(model_name)', function (selectData) {
                    initSelect(false, selectData.value);
                });
            } else {
                renderSelect($("#test_item"), data.testItem);
                form.render('select');
            }

        } else {
            layer.alert(res.msg, {icon: 2});
        }
    }, function (xhr, textStatus, errorThrown) {
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}

function renderSearchForm() {
    initSelect(true);
    laydate.render({
        elem: '#range-date', range: ['#start-date', '#end-date']
    });


    form.on('submit(event-table-search)', function (data) {
        var field = data.field; // 获取表单全部字段值
        table.reloadData('event-table', {
            page: {
                curr: 1
            }, where: {
                query: JSON.stringify(field)
            }, scrollPos: 'fixed'
        });
        return false; // 阻止默认 form 跳转
    });

    $("#sync-data").on('click', function () {
        layer.confirm('是否同步数据？', {icon: 3}, function () {
            var loadIndex = layer.msg('正在同步数据...', {
                icon: 16, shade: 0.01, time: 0
            });
            twxAjax(THING, 'UpdateAllTestLog', {}, true, function (res) {
                if (res.success) {
                    layer.close(loadIndex);
                    layer.msg(res.msg);
                    renderSearchForm();
                    table.reloadData('event-table', {
                        where: {}, scrollPos: 'fixed'
                    });
                } else {
                    layer.alert(res.msg, {icon: 2});
                }
            }, function (xhr, textStatus, errorThrown) {
                layer.alert('请求出错！', {
                    icon: 2
                });
            });
        }, function () {

        });
        return false;
    });

    $("#export-data").on('click', function () {
        var loading;
        $.fileDownload(fileHandlerUrl + "/electric/test/export/event", {
            httpMethod: 'POST',
            data: {
                "query": JSON.stringify(form.val('search-form'))
            },
            prepareCallback: function (url) {
                loading = layer.msg("正在导出...", {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });
            },
            abortCallback: function (url) {
                layer.close(loading);
                layer.msg("导出异常！！");
            },
            successCallback: function (url) {
                layer.close(loading);
            },
            failCallback: function (html, url) {
                var msg = '导出失败！';
                layer.close(loading);
                layer.alert(msg, {
                    icon: 2
                });
            }
        });
        return false;
    });
}


/**
 * 加载表格
 */
function renderTable() {
    var tableAlign = 'center';
    var cols = [[{
        width: 60, align: tableAlign, title: '序号', type: 'numbers'
    }, {field: 'MODEL_NAME', title: '卫星名称', align: tableAlign, width: 135},
        {field: 'STAGE_NAME', title: '阶段名称', align: tableAlign, width: 135},
        {field: 'TEST_ITEM', title: '测试项目', align: 'left', width: 300},
        {
            field: 'CREATION_TIME',
            title: '创建时间',
            minWidth: 160,
            maxWidth: 160,
            align: tableAlign,
            width: 160,
            hide: true
        },
        {field: 'CODE', title: '编号', align: tableAlign, width: 135},
        {field: 'OCCURRENCE_TIME', title: '发生时刻', minWidth: 160, maxWidth: 160, align: tableAlign, width: 160},
        {field: 'EXCEPTION_PHENOMENON', title: '异常现象', align: tableAlign, width: 260},
        {field: 'EXCEPTION_LOCATION', title: '异常发生地点', align: tableAlign, width: 135},
        {field: 'EXCEPTION_PRODUCT_NAME', title: '异常产品名称', align: tableAlign, width: 135},
        {field: 'PRODUCT_CODE', title: '产品代号', align: tableAlign, width: 135},
        {field: 'BATCH_OR_VERSION', title: '批次号/版本号', align: tableAlign, width: 135},
        {field: 'RESPONSIBLE_UNIT', title: '责任单位', align: tableAlign, width: 154},
        {field: 'PHENOMENON_DESCRIPTION', title: '现象描述', align: tableAlign, width: 135},
        {field: 'PROBLEM_CATEGORY', title: '问题分类', align: tableAlign, width: 90},
        {field: 'INITIAL_POSITION', title: '初步定位', align: tableAlign, width: 200},
        {field: 'HANDLING_MEASURES', title: '处理措施', align: tableAlign, width: 200},
        {field: 'CONFIRMATION_509_18', title: '处置确认-509所十八室', align: tableAlign, width: 135},
        {field: 'CONFIRMATION_812_TEST', title: '处置确认-812所测试中心', align: tableAlign, width: 135},
        {field: 'CLOSURE_STATUS', title: '闭环情况', align: tableAlign, width: 88},
        {field: 'CLOSURE_DESCRIPTION', title: '闭环情况描述', align: tableAlign, width: 135},
        {field: 'CLOSURE_TIME', title: '闭环时间', align: tableAlign, width: 160, minWidth: 160, maxWidth: 160},
        {field: 'CLOSURE_509_QUALITY', title: '闭环情况-509所质量师', align: tableAlign, width: 135},
        {field: 'CLOSURE_812_QUALITY', title: '闭环情况-812所质量师', align: tableAlign, width: 135},
        {field: 'REMARKS', title: '备注', align: tableAlign}]];
    table.render({
        elem: '#event-table',
        url: getUrl(THING, 'QueryTestEvent'),
        defaultToolbar: [],
        cellMinWidth: 86,
        height: tableHeight,
        page: {
            layout: ['limit', 'count', 'prev', 'page', 'next', 'refresh', 'skip'],
            limit: 20,
            limits: [10, 20, 30, 40, 50, 100],
            groups: 1,
            first: false,
            last: false
        },
        cols: cols,
        done: function (res, curr, count, origin) {
            // beautifyTable();
        },
    });

    table.on('colResized(event-table)', function (obj) {
        // beautifyTable();
    });

    // 工具栏事件
    table.on('toolbar(event-table)', function (obj) {

    });
}

function measureTextWidth(text) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    ctx.font = `14px Helvetica Neue,Helvetica,PingFang SC,Tahoma,Arial,sans-serif`; // 设置字体样式

    const metrics = ctx.measureText(text);
    return metrics.width;
}

function beautifyTable() {
    console.log('1');
    var rowHeight = 28;
    var $tableDiv = $("#event-table").next();
    var $table = $tableDiv.find('.layui-table-box>.layui-table-body>table');
    var $fixedTable = $tableDiv.find('.layui-table-box>.layui-table-fixed>.layui-table-body>table');

    var $trs = $table.find('tr');
    for (var i = 0; i < $trs.length; i++) {
        var $tr = $($trs[i]);
        var trH = $tr.height();
        var trRowNum = 1;
        var $tds = $tr.find("td");
        for (var j = 0; j < $tds.length; j++) {
            var $td = $($tds[j]);
            var tdRowNum = 1;
            var $cellDiv = $td.find('div');
            var divW = $cellDiv.width();
            var cellValue = $cellDiv.text();
            var textWidth = measureTextWidth(cellValue);
            if (divW < textWidth) {
                tdRowNum = Math.ceil(textWidth / divW);
                if (tdRowNum > trRowNum) {
                    trRowNum = tdRowNum;
                }
            }
        }
        var trHeight = trRowNum * rowHeight + 12;
        $tr.css('height', trHeight + 'px');
        $fixedTable.find('tr:eq(' + i + ')').css('height', trHeight + 'px');
    }
}