var THING = 'Thing.Fn.TestEvaluation';
var tableHeight = 0;

function initPageStyle() {
	//获取body的高度
	var bodyHeight = $("body").height();
	$("#tree").parent().css({
		"height": bodyHeight + "px"
	});
	tableHeight = bodyHeight - 5;
}

var form, layer, table, upload, dropdown, insertSelect;

layui.config({
	base: "../../plugins/layui/define/",
}).extend({
	insertSelect: "InsertSelect"
}).use(['form', 'layer', 'table', 'upload', 'dropdown', 'insertSelect'], function() {
	form = layui.form;
	layer = layui.layer;
	table = layui.table;
	upload = layui.upload;
	dropdown = layui.dropdown;
	insertSelect = layui.insertSelect;
	var myTree = new ProductTree();

	initPageStyle();
	renderFileTable(1, false);
});