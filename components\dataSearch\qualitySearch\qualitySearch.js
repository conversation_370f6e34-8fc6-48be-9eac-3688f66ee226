$(function() {
	loadProductCombobox();
	window.dataTable = new DataTable();
	window.bomTree = new BomTree();

	$("#root_layout").layout("panel", "center").panel({
		onResize: function(width, height) {
			//2是边框宽度
			var gridWidth = width - 2;
			var gridId = "secondTable";
			try {
				$("#" + gridId).datagrid('resize', {
					width: gridWidth
				});
				changeWidth(gridId);
			} catch (e) {

			}

		}
	});
});

//选中的过程节点 不包含型号节点
var selTreeNode = undefined;
// 下拉框置空选项
var emptyOption = {
	TREEID: 0,
	NODENAME: '--请选择--'
};

//加载过程结构树中的型号列表
function loadProductCombobox() {
	twxAjax('Thing.Fn.DataDownload', 'QueryProduct', {
		username: sessionStorage.getItem('username')
	}, true, function(data) {
		data.rows.unshift(emptyOption);
		$("#product").combobox({
			valueField: 'TREEID',
			textField: 'NODENAME',
			panelHeight: '400px',
			data: data.rows,
			editable: true,
			onSelect: function(record) {
				bomTree.showBomMsg("请选择型号和阶段！");
				selTreeNode = undefined;
				loadPhaseCombobox(record.TREEID);
				if (record.TREEID != 0) {
					$("#product").data("code", record.CODE);
				}
			}
		});
	});
}

//加载阶段列表
function loadPhaseCombobox(parentId) {
	if (parentId != 0) {
		dataTable.showMsg("请选择阶段！");
		bomTree.showBomMsg("请选择阶段！");
		twxAjax('Thing.Fn.DataDownload', 'QueryPhase', {
			parentId: parentId
		}, true, function(data) {
			data.rows.unshift(emptyOption);
			$("#phase").combobox({
				valueField: 'TREEID',
				textField: 'NODENAME',
				data: data.rows,
				panelHeight: 'auto',
				editable: false,
				onSelect: function(record) {
					if (record.TREEID != 0) {
						var model = $("#product").data("code");
						var stage = record.CODE;
						bomTree.loadTree(model, stage);
						selTreeNode = record;
						$("#phase").data("node", record);
						dataTable.loadTable(record);
					} else {
						dataTable.showMsg("请选择阶段！");
						bomTree.showBomMsg("请选择阶段！");
					}
					loadMajorCombobox(record.TREEID);
				},
				onLoadSuccess: function() {
					resizeComboboxWidth(this);
				}
			});
		});
	} else {
		dataTable.showMsg("请选择型号！");
		bomTree.showBomMsg("请选择型号！");
		$("#phase").combobox('loadData', []);
		$("#phase").combobox('setValue', '');
		$("#major").combobox('loadData', []);
		$("#major").combobox('setValue', '');
		$("#process").combobox('loadData', []);
		$("#process").combobox('setValue', '');
		resetProcess();
	}
}

//加载专业列表
function loadMajorCombobox(parentId) {
	resetProcess();
	if (parentId != 0) {
		twxAjax('Thing.Fn.DataDownload', 'QueryMajor', {
			parentId: parentId
		}, true, function(data) {
			data.rows.unshift(emptyOption);
			$("#major").combobox({
				valueField: 'TREEID',
				textField: 'NODENAME',
				data: data.rows,
				panelHeight: 'auto',
				editable: false,
				onSelect: function(record) {
					if (record.TREEID != 0) {
						selTreeNode = record;
						dataTable.loadTable(record);
						$("#major").data('node', record);
						loadProcessCombobox(record.TREEID);
					} else {
						$("#processDiv").hide();
						selTreeNode = $("#phase").data('node');
						dataTable.loadTable(selTreeNode);
					}
				},
				onLoadSuccess: function() {
					resizeComboboxWidth(this);
				}
			});
		});
	} else {
		$("#processDiv").hide();
		$("#major").combobox('loadData', []);
		$("#major").combobox('setValue', '');
		$("#process").combobox('loadData', []);
		$("#process").combobox('setValue', '');
	}
}

function resetProcess() {
	$("#diagramContainer").css('height', '1px');
	$("#diagramContainer").empty();
}
//加载过程列表
function loadProcessCombobox(parentId) {
	resetProcess();
	twxAjax('Thing.Fn.DataDownload', 'QueryProcess', {
		parentId: parentId
	}, true, function(data) {
		if (data.rows.length > 0) {
			// $("#processDiv").show();
			// data.rows.unshift(emptyOption);
			// $("#process").combobox({
			// 	valueField: 'TREEID',
			// 	textField: 'NODENAME',
			// 	data: data.rows,
			// 	panelHeight: 300,
			// 	editable: false,
			// 	onSelect: function(record) {
			// 		if (record.TREEID != 0) {
			// 			selTreeNode = record;
			// 			dataTable.loadTable(selTreeNode);
			// 		} else {
			// 			selTreeNode = $("#major").data('node');
			// 			dataTable.loadTable(selTreeNode);
			// 		}
			// 	},
			// 	onLoadSuccess: function() {
			// 		resizeComboboxWidth(this);
			// 	}
			// });
			var items = data.rows;
			var colNum = 6;
			var rowNum = Math.ceil(items.length / colNum);
			$("#diagramContainer").css("height", (70 * rowNum) + "px");
			for (var i = 0, t = 1; i < rowNum; i++) {
				$tr = $('<div class="layui-row"></div>');
				for (var j = 0; j < colNum; j++) {
					var treeNode = items[(i * colNum) + j];
					var text = (!!treeNode) ? treeNode['NODENAME'] : "";
					if (text !== '') {
						text = dealPrefix(t) + "-" + text;
						text = $('<div class="v-mult" id="jsPlumb' + t + '"><span class="empty"></span><span class="text">' + text + '</span></div>');
						text.data("treeNode", treeNode);
					}
					var $colDiv = $('<div class="layui-col-md2"></div>');
					$colDiv.append(text);
					if (i % 2 == 1) {
						$tr.prepend($colDiv);
					} else {
						$tr.append($colDiv);
					}
					t++;
				}
				$("#diagramContainer").append($tr);
			}
			$("#diagramContainer .v-mult").unbind('click').bind('click', function() {
				$("#diagramContainer .v-mult").removeClass('v-mult-active');
				$(this).addClass('v-mult-active');
				var treeNode = $(this).data("treeNode");
				if (treeNode.TREEID != 0) {
					selTreeNode = treeNode;
					dataTable.loadTable(selTreeNode);
				} else {
					selTreeNode = $("#major").data('node');
					dataTable.loadTable(selTreeNode);
				}
			});
			if (jsPlumb.getContainer()) {

			} else {
				jsPlumb.setContainer('diagramContainer');
			}
			for (var i = 1; i < items.length; i++) {
				var anchor = ['LeftMiddle', 'RightMiddle'];
				if (i % colNum == 0) {
					anchor = ['TopCenter', 'BottomCenter'];
				}
				jsPlumb.connect({
					source: 'jsPlumb' + i,
					target: 'jsPlumb' + (i + 1),
					anchor: anchor,
					endpoint: 'Blank',
					connector: ['Flowchart'],
					paintStyle: {
						lineWidth: 2,
						strokeStyle: '#169BD5'
					},
					overlays: [
						['Arrow', {
							width: 12,
							length: 12,
							location: 1
						}]
					]
				});
			}
		} else {
			$("#processDiv").hide();
		}
	});
}