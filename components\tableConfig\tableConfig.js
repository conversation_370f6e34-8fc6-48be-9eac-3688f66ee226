$(function() {
	loadTree();
	renderTable();
	renderParam();
	renderBtn();
	loadTable('all');
});

var THING = 'Thing.Fn.SecondTable';
//是否显示的是所有table数据
var isAllTable = true;

//选中的树节点名称
var treeName = '';

//属性表id
var paramId = 'paramTable';
//表id
var tableId = 'tableTable';

layui.use(['layer', 'form', 'upload', 'dropdown'], function() {
	layer = layui.layer;
	upload = layui.upload;
	form = layui.form;
	device = layui.device();
	dropdown = layui.dropdown;

	dropdown.render({
		elem: '#upload_tpl_file',
		data: [{
				title: '二级表模板',
				id: 'table_upload_second'
			}, {
				title: '三级表模板',
				id: 'table_upload_three'
			}, {
				title: '策划模板',
				id: 'table_upload_plan'
			}, {
				title: '影像记录策划模板',
				id: 'table_upload_photo'
			}, {
				title: '合格证模板',
				id: 'table_upload_certificate'
			}
			// , {
			// 	title: '影像记录汇总模板',
			// 	id: 'table_upload_photo_summary'
			// }, {
			// 	title: '质量报告模板',
			// 	id: 'table_upload_report'
			// },
		],
		click: function(data, othis) {
			if (data.id == 'table_upload_second') {
				uploadExcel(2);
			} else if (data.id == 'table_upload_three') {
				uploadExcel(3);
			} else if (data.id == 'table_upload_plan') {
				uploadExcel(4);
			} else if (data.id == 'table_upload_photo') {
				uploadExcel(5);
			} else if (data.id == 'table_upload_certificate') {
				uploadExcel(7);
			}
		}
	});

	form.verify({
		treeNameRepeat: function(value, item) {
			var oldName = $(item).attr('oldname');
			var flag = true;
			var cb_success = function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					var d = data.rows[i];
					if (value == d.TREE_NAME) {
						flag = false;
					}
				}
				if (value == oldName) {
					flag = true;
				}
			}
			//请求失败的回调
			var cb_error = function(xhr, textStatus, errorThrown) {

			};
			twxAjax(THING, "QueryAllTable", {}, false, cb_success, cb_error);

			if (!flag) {
				return '该模板名称已存在！';
			}
		}
	});
});


var tableLayerH = "450px";
var tableLayerH1 = "450px";
var tableLayerH2 = "270px";


var paramLayerH = '620px';
var paramLayerH1 = '670px';
var paramLayerH2 = '570px';
var paramLayerH3 = '720px';


//控制两个表格列的显示
function controlColShow(type) {
	// if (type == 1) { //mes
	// 	$("#table_upload_second").show();
	// 	$("#table_upload_three").hide();
	// 	// $('#' + tableId).datagrid('hideColumn', 'THREE_FILEPATH');
	// 	// $('#' + tableId).datagrid('showColumn', 'MES_INTERFACE');
	// 	// $('#' + tableId).datagrid('showColumn', 'SECOND_DATA_ROWNUM');
	// 	// $('#' + tableId).datagrid('showColumn', 'SECOND_FILEPATH');
	// 	$("#paramTableMsg").hide();
	// 	$('#' + paramId).datagrid("getPanel").show();
	// 	$('#' + paramId).datagrid('showColumn', 'MES_NAME');
	// 	$('#' + paramId).datagrid('hideColumn', 'THREE_AREA');
	// } else 
	if (type == 2 || type == 1 || type == 5) { //三级表 或者 mes
		$("#table_upload_second").show();
		$("#table_upload_three").show();
		// $('#' + tableId).datagrid('showColumn', 'THREE_FILEPATH');
		// $('#' + tableId).datagrid('hideColumn', 'MES_INTERFACE');
		// $('#' + tableId).datagrid('showColumn', 'SECOND_DATA_ROWNUM');
		// $('#' + tableId).datagrid('showColumn', 'SECOND_FILEPATH');
		$("#paramTableMsg").hide();
		$('#' + paramId).datagrid("getPanel").show();
		$('#' + paramId).datagrid('hideColumn', 'MES_NAME');
		$('#' + paramId).datagrid('showColumn', 'THREE_AREA');
	} else if (type == 3 || type == 6) { //二级表和三级表完全相同
		$("#table_upload_second").hide();
		$("#table_upload_three").show();
		// $('#' + tableId).datagrid('hideColumn', 'SECOND_DATA_ROWNUM');
		// $('#' + tableId).datagrid('hideColumn', 'SECOND_FILEPATH');
		// $('#' + tableId).datagrid('hideColumn', 'MES_INTERFACE');
		$('#' + paramId).datagrid("getPanel").hide();
		$("#paramTableMsg").text('无需表格属性配置！').show();
		$('#' + paramId).datagrid('hideColumn', 'MES_NAME');
		$('#' + paramId).datagrid('showColumn', 'THREE_AREA');
	} else if (type == 4) { //二级表和三级表表头相同
		$("#table_upload_second").hide();
		$("#table_upload_three").show();
		// $('#' + tableId).datagrid('showColumn', 'SECOND_DATA_ROWNUM');
		// $('#' + tableId).datagrid('hideColumn', 'SECOND_FILEPATH');
		// $('#' + tableId).datagrid('hideColumn', 'MES_INTERFACE');
		$("#paramTableMsg").hide();
		$('#' + paramId).datagrid("getPanel").show();
		$('#' + paramId).datagrid('hideColumn', 'MES_NAME');
		$('#' + paramId).datagrid('hideColumn', 'THREE_AREA');
	}
}

//编辑后刷新表数据
function refreshTable() {
	if (isAllTable) {
		loadTable('all');
	} else {
		loadTable(treeName);
	}
}