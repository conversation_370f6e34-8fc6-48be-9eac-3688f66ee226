$(function () {
    initTree();
    var isResizing = false;
    $($("#c_layout").layout('panel', 'north')).panel({
        onResize: function (width, height) {
            var selectedNodes = ztreeObj.getSelectedNodes();
            if (selectedNodes.length > 0) {
                if (getColNum(width) !== lastColNum) {
                    if (!isResizing) {
                        isResizing = true;
                        loadProcess(selectedNodes[0].ID, function () {
                            isResizing = false;
                        });
                    }
                }
            }
        }
    });

    $($("#c_layout").layout('panel', 'center')).panel({
        onResize: function (width, height) {
            $("#qualityTableDiv").css('height', (height - 42) + 'px');
        }
    });
});
var THING = "Thing.Fn.PhotoQuery";
var qualityPhoto = void 0;
var ztreeObj;
/**
 * 记录最后一次加载的列数
 * @type {number}
 */
var lastColNum = 0;

/**
 * 初始化产品结构树
 */
function initTree() {
    var cb_success = function (res) {
        if (res.success) {
            var datas = res.data;
            if (datas.length > 0) {
                datas = bomTreeUtil.dealDataIcons(datas);
                var treeSetting = bomTreeUtil.treeSetting;
                treeSetting.callback = {
                    beforeClick: function (treeId, treeNode, clickFlag) {
                        if (treeNode.NODE_TYPE == 'folder') {
                            return false;
                        } else {
                            return true;
                        }
                    },
                    onClick: function (event, treeId, treeNode) {
                        loadProcess(treeNode.ID);
                    },
                    onExpand: function (event, treeId, treeNode) {
                        loadTreeMenu();
                    }
                }
                ztreeObj = $.fn.zTree.init($("#bomTree"), treeSetting, datas);
                var nodes = ztreeObj.getNodes();
                for (var i = 0; i < nodes.length; i++) { //设置节点展开ss
                    ztreeObj.expandNode(nodes[i], true, false, true);
                }
                loadTreeMenu();
            }
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    };
    //使用ajax进行异步加载Tree
    twxAjax('Thing.Fn.BOM', 'QueryProductTreeRoot', {
        username: sessionStorage.getItem('username')
    }, true, cb_success);
}

var loadTreeMenu = function () {
    $("#bomTree  a").each(function (i, n) {
        var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
        if (node['NODE_TYPE'] != 'folder') {
            $(n).contextMenu({
                width: 115,
                menu: [{
                    text: "更新数据",
                    icon: '../dataTree/images/update.png',
                    callback: function () {
                        var productTreeId = node.ID;
                        layer.confirm("更新数据会比较耗时，是否继续？", {
                            icon: 3,
                            title: '提示'
                        }, function (index) {
                            var startTimestamp = new Date();
                            var timeoutId;
                            var loadIndex = layer.msg('正在更新中', {
                                icon: 16,
                                shade: 0.01,
                                time: 0
                            });
                            var isAlert = false;
                            // 发送请求前的处理
                            timeoutId = setTimeout(function () {
                                // 设置一个定时器，在59秒后触发
                                if (new Date().getTime() - startTimestamp > 59000) {
                                    layer.close(loadIndex);
                                    isAlert = true;
                                    layer.alert("请求还在运行中请稍后查看！");
                                }
                            }, 59000);
                            twxAjax(THING, 'UpdatePhotoCount', {
                                productTreeId: productTreeId
                            }, true, function (res) {
                                layer.close(loadIndex);
                                clearTimeout(timeoutId); // 清除定时器
                                if (!isAlert) {
                                    if (res.success) {
                                        layer.msg(res.msg);
                                    } else {
                                        layer.alert(res.msg, {
                                            icon: 2
                                        });
                                    }
                                }

                            }, function (xhr, textStatus, errorThrown) {
                                layer.alert('请求出错！', {
                                    icon: 2
                                });
                            });
                        });
                    }
                }],
                target: function (ele) {
                    var node = ztreeObj.getNodeByTId($(ele).parent().attr("id"));
                    ztreeObj.selectNode(node, false, true);
                }
            });
        }
    });
};

function getColNum(width) {
    return Math.floor((width - 17 - 170) / 230) + 1;
}

/**
 * 加载过程节点
 * @param {Object} productTreeId 产品结构树id
 */
function loadProcess(productTreeId, successFn = function () {
}) {
    var colNum = getColNum($("#c_layout").layout('panel', 'north').width());
    lastColNum = colNum;
    hidePhoto();
    $("#diagramContainer").empty();
    var loadIndex = layer.msg('正在查询中', {
        icon: 16,
        shade: 0.01,
        time: 0
    });
    twxAjax(THING, 'QueryAITPhotoByProduct', {
        productTreeId: productTreeId
    }, true, function (res) {
        layer.close(loadIndex);
        if (res.success) {
            var data = res.data;
            if (data.length > 0) {
                var items = res.data;
                var rowNum = Math.ceil(items.length / colNum);
                $("#diagramContainer").css("height", (76 * rowNum) + "px");
                for (var i = 0, t = 1; i < rowNum; i++) {
                    var $tr = $('<div class="layui-row"></div>');
                    for (var j = 0; j < colNum; j++) {
                        var treeNode = items[(i * colNum) + j];
                        var text = (!!treeNode) ? treeNode['processName'] : "";
                        if (text !== '') {
                            var processCount = treeNode['processCount'];
                            if (processCount > 0) {
                                processCount = '<span style="color:#15ff15;">' + processCount + '</span>';
                            }
                            text = dealPrefix(t) + "-" + text;
                            text = text + "（" + processCount + "）";
                            text = $('<div class="v-mult" id="jsPlumb' + t + '"><span class="empty"></span><span class="text">' + text + '</span></div>');
                            text.data("treeNode", treeNode);
                        }
                        var $colDiv = $('<div class="my-layui-col-md2"></div>');
                        $colDiv.append(text);
                        if (i % 2 == 1) {
                            if (j == 0) {
                                $colDiv.addClass("row-last");
                            }
                            $tr.prepend($colDiv);
                        } else {
                            if (j == colNum - 1) {
                                $colDiv.addClass("row-last");
                            }
                            $tr.append($colDiv);
                        }
                        t++;
                    }
                    $("#diagramContainer").append($tr);
                }
                $("#diagramContainer .v-mult").unbind('click').bind('click', function () {
                    $("#diagramContainer .v-mult").removeClass('v-mult-active');
                    $(this).addClass('v-mult-active');
                    var treeNode = $(this).data("treeNode");
                    renderTableType(treeNode.nodeTables, productTreeId, treeNode.processId);
                });

                jsPlumbBrowserUI.ready(function () {

                    var jsPlumb = jsPlumbBrowserUI.newInstance({
                        container: document.getElementById("diagramContainer")
                    });

                    for (var i = 1; i < items.length; i++) {

                        var anchor = ['Right', 'Left'];
                        if (Math.ceil(i / colNum) % 2 == 0) {
                            anchor = ['Left', 'Right'];
                        }
                        if (i % colNum == 0) {
                            anchor = ['Bottom', 'Top'];
                        }
                        jsPlumb.connect({
                            source: document.getElementById('jsPlumb' + i),
                            target: document.getElementById('jsPlumb' + (i + 1)),
                            anchors: anchor,
                            endpoints: ["Blank", "Blank"],
                            endpointStyle: {
                                fill: '#169BD5'
                            },
                            overlays: [{
                                type: "Arrow",
                                options: {
                                    width: 12,
                                    length: 12,
                                    location: 1
                                }
                            }],
                            paintStyle: {
                                strokeWidth: 2,
                                stroke: '#169BD5'
                            }
                        });
                    }
                });
                successFn();
            } else {
                processMsg("未查询到过程节点");
            }
        } else {
            processMsg(res.msg);
        }
    }, function (xhr, textStatus, errorThrown) {
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}

function renderTableType(datas, productTreeId, processTreeId) {
    if (datas.length > 0) {
        $('#qualityType').combobox({
            data: datas,
            valueField: 'tableConfigId',
            textField: 'tableName',
            editable: false,
            width: 300,
            panelHeight: 400,
            onSelect: function (record) {
                postPhoto(record.tableConfigId, record.nodeIds, productTreeId, processTreeId);
            }
        });
        $('#qualityType').combobox("select", datas[0].tableConfigId);
    } else {
        hidePhoto();
    }
}

/**
 * 过程节点区域的信息提示
 */
function processMsg(msg) {
    $("#diagramContainer").empty().append('<span style="color:red;padding:15px;font-size:16px; display: block;">' + msg + '</span>');
}

/**
 * 隐藏照片显示区域
 */
function hidePhoto() {
    $("#qualityTypeDiv").hide();
    $("#qualityTableDiv").hide();
}

/**
 * 显示照片显示区域
 */
function showPhoto() {
    $("#qualityTypeDiv").show();
    $("#qualityTableDiv").show();
}

/**
 * 请求照片数据
 * @param {Object} tableConfigId
 * @param {Object} productTreeId
 * @param {Object} processTreeId
 */
function postPhoto(tableConfigId, nodeIds, productTreeId, processTreeId) {
    hidePhoto();
    var loadIndex = layer.msg('正在查询中', {
        icon: 16,
        shade: 0.01,
        time: 0
    });
    var service = "QueryProcessPhoto";
    var params = {
        tableConfigId: tableConfigId,
        productTreeId: productTreeId,
        processTreeId: processTreeId
    };
    if (processTreeId == '-9999') {
        service = "QueryFinalProcessPhoto";
        params = {
            tableConfigId: tableConfigId,
            productTreeId: productTreeId,
            nodeIds: nodeIds
        };
    }
    twxAjax(THING, service, params, true, function (res) {
        layer.close(loadIndex);
        if (res.success) {
            var data = res.data;
            loadAutoPhotoTable(data, tableConfigId, processTreeId);
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (xhr, textStatus, errorThrown) {
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}


var loadQualityPhoto = function (isInit) {
    if (qualityPhoto == undefined) {
        qualityPhoto = new QualityPhoto();
    }
    if (isInit) {
        qualityPhoto.init();
    }
}

//加载影像记录表
function loadAutoPhotoTable(data, tableConfigId, treeId) {
    loadQualityPhoto(false);
    $("#qualityTableDiv").css('height', ($("#c_layout").layout('panel', 'center').height() - 40) + 'px');
    var planTableAlign = "left";
    $("#table").empty();
    var indexStr = "序号";
    var statusStr = "状态";
    var confirmerStr = "确认人";
    var actualPhotoStr = "实际照片";
    var $table = $('<table class="my-table layui-table"></table>');
    var $thead = $('<thead class="sticky-thead"></thead>');
    var cols = data[0].cols;
    //添加表头
    var $headerTr = $('<tr></tr>');
    for (var j = 0; j < cols.length; j++) {
        $headerTr.append('<td class="table-head" align="' + planTableAlign + '">' + cols[j] + '</td>');
    }
    $thead.append($headerTr);
    $table.append($thead);
    for (var i = 0; i < data.length; i++) {
        var table = data[i];
        var datas = table.datas;
        var cols = table.cols;
        var onlyValues = table.onlyValues;
        var indexIndex = cols.indexOf(indexStr);
        var statusIndex = cols.indexOf(statusStr);
        var confirmerIndex = cols.indexOf(confirmerStr);
        var photoIndex = cols.indexOf(actualPhotoStr);
        var relationParam = table.relationParam;
        var trArr = [];
        //添加表数据
        for (var j = 0; j < datas.length; j++) {
            var $tr = $('<tr></tr>');
            var d = datas[j];
            var onlyValue = onlyValues[j];
            var isAppendTr = false;
            for (var x = 0; x < d.length; x++) {

                if (x == photoIndex) {
                    var menus = [];

                    var photos = [];
                    if (d[x] != '[]') {
                        isAppendTr = true;
                        photos = JSON.parse(d[x]);
                        menus.push({
                            text: "查看照片",
                            icon: '../dataTree/images/view.png',
                            callback: function () {
                                qualityPhoto.previewPhoto(tableConfigId, treeId, true);
                            }
                        });
                    }
                    var $td = $('<td align="' + planTableAlign + '">' + qualityPhoto.getPhotoStr(photos) + '</td>');
                    $td.data('photos', photos);
                    $td.data('tableName', table.name);
                    $td.data("confirmer", d[confirmerIndex]);
                    $td.data('paramId', relationParam["ID"]);
                    $td.data("onlyValue", onlyValue);
                    $tr.append($td);
                    if (menus.length > 0) {
                        $td.contextMenu({
                            width: 100,
                            menu: menus,
                            target: function (ele) {
                                qualityPhoto.contextMenuTarget = ele;
                            }
                        });
                    }
                } else {
                    $tr.append('<td align="' + planTableAlign + '">' + d[x] + '</td>');
                }
            }
            if (isAppendTr) {
                trArr.push($tr);
            }
        }

        if (trArr.length > 0) {
            //添加合并行
            $table.append('<tr ><td class="table-name" colspan=' + cols.length + '>' + table.name + '</td></tr>');
        }

        for (var x = 0; x < trArr.length; x++) {
            var $tr = trArr[x];
            $tr.find("td:eq(" + indexIndex + ")").text(x + 1);
            $table.append($tr);
        }

    }
    $("#table").append($table);
    showPhoto();
}