<html>
    <head>
        <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
        <link rel="stylesheet" href="../../../plugins/layui/css/layui.css">
       <!-- <link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
        <link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->
		<link rel="stylesheet" href="../../../css/icon.css">
		
        <script src="../../../plugins/layui/layui.js"></script>
        <link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
        <script src="../../../plugins/easyui/jquery.min.js"></script>
        <script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
        <script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
        <script src="../../js/config/twxconfig.js"></script>
        <script src="../../js/util.js"></script>
    
        <script src="../../js/intercept.js"></script>
        <script src="../../js/logUtil.js"></script>
        <title>角色管理</title>
        <style>
            .layui-form-label{
                width: 95px;
            }
            .layui-form-checkbox span{
                height:18px;
            }
        </style>
    </head>
    <body>
		<div id="templateTable_tb" style="padding: 5px;">
			 <button type="button"  class="layui-btn layui-btn-sm" id="tbar_add">
			   <i class="layui-icon">&#xe608;</i> 添加
			 </button>
			 <button type="button"  class="layui-btn layui-btn-sm  layui-btn-warm" id="tbar_edit">
			   <i class="layui-icon">&#xe642;</i> 编辑
			 </button>
			 <button type="button"  class="layui-btn layui-btn-sm layui-btn-danger" id="tbar_del">
			   <i class="layui-icon">&#xe640;</i> 删除
			 </button>
		</div>
        <div id="templateTable" style="width:100%;height:100%;"></div>
    </body>
</html>

<script src="templateMgr.js"></script>

<script type="text/html" id="templateForm">
    <form class="layui-form" lay-filter="addForm" style="padding:8px;">
		<input type="hidden" name="id" class="layui-input" id="ID">
	    <div class="layui-form-item">
            <div class="layui-inline">
                <label class="fieldlabel layui-form-label">节点名称:</label>
                <div class="layui-input-inline" >
                    <input type="text" name="refname"  lay-verify="required" required autocomplete="off" placeholder="请输入节点名称" class="layui-input">
                </div>
            </div>
	    </div>
	    <div class="layui-form-item">
            <div class="layui-inline">
                <label class="fieldlabel layui-form-label">文件类别:</label>
                <div class="layui-input-inline" >
                    <input type="text" name="name"  lay-verify="required" required autocomplete="off" placeholder="请输入文件类别" class="layui-input">
                </div>
            </div>
	    </div>
	    <div class="layui-form-item">
            <div class="layui-inline">
                <label class="fieldlabel layui-form-label">分类:</label>
                <div class="layui-input-inline" >
                    <input type="text" name="type"  lay-verify="required" required autocomplete="off" placeholder="请输入类别" class="layui-input">
                </div>
            </div>
	    </div>
	    <div class="layui-form-item" style="display:none;">
	        <center>
				<button id="btn_add" class="layui-btn" lay-submit lay-filter="addData">提交</button>
				<button id="btn_update" class="layui-btn" lay-submit lay-filter="updateData">更新</button>
	            <button id="btn_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
	        </center>
	    </div>
	</form>
</script>