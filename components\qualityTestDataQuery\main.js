// 文档就绪后执行
layui.use(['form', 'table', 'laydate', 'element'], function () {
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;
    var element = layui.element;

    // 计算表格高度，避免出现纵向滚动条
    function calculateTableHeight() {
        var winHeight = window.innerHeight;
        var otherHeight = 110; // 估算其他元素的总高度（标签页+查询表单）
        return winHeight - otherHeight;
    }

    // 通用的表格渲染完成回调函数 - 优化版本
    function tableDoneCallback() {
        // 创建一个防抖函数，避免短时间内多次执行
        var debounce = function(func, wait) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        };
        
        // 窗口大小变化处理函数 - 使用防抖优化
        var handleResize = debounce(function() {
            var tableInstance = null;
            var activeTabIndex = $('.layui-tab-title .layui-this').index();
            if (activeTabIndex === 0) tableInstance = massTable;
            else if (activeTabIndex === 1) tableInstance = momentTable;
            else if (activeTabIndex === 2) tableInstance = calculationTable;
            else if (activeTabIndex === 3) tableInstance = calibrationTable;
            else if (activeTabIndex === 4) tableInstance = momentCalibrationTable;
            
            if (tableInstance) {
                tableInstance.resize({
                    height: calculateTableHeight()
                });
                // 窗口大小变化时重新调整表头列宽，但使用延迟执行
                setTimeout(function() {
                    adjustHeaderWidth();
                }, 50);
            }
        }, 100);
        
        // 只绑定一次resize事件
        if (!window._resizeHandlerBound) {
            $(window).off('resize.tableResize').on('resize.tableResize', handleResize);
            window._resizeHandlerBound = true;
        }
        
        return function (res, curr, count, origin) {
            // 使用requestAnimationFrame优化DOM操作，确保在下一帧渲染
            requestAnimationFrame(function() {
                // 批量设置CSS样式，减少重绘次数
                $('.layui-table-cell').css({
                    'white-space': 'nowrap',
                    'text-overflow': 'inherit'
                });
                
                // 使用更高效的选择器和批量处理
                var invalidValues = ['undefined', 'null', 'NaN', 'Infinity', '-Infinity'];
                $('.layui-table-cell').each(function() {
                    var $cell = $(this);
                    var text = $cell.text().trim();
                    if (invalidValues.indexOf(text) !== -1) {
                        $cell.text('');
                    }
                });
                
                // 延迟执行非关键操作，让表格先显示出来
                setTimeout(function() {
                    // 为表头添加悬停提示
                    addHeaderTooltips();
                    
                    // 调整表头列宽以确保表头内容完整显示
                    adjustHeaderWidth();
                }, 10);
            });
            
            return res; // 返回原始结果以保持链式调用
        };
    }

    // 为表头添加悬停提示的函数 - 优化版本
    function addHeaderTooltips() {
        // 缓存字段描述映射，避免每次都重新构建
        if (!window._fieldDescriptionMap) {
            window._fieldDescriptionMap = {};
            
            // 只构建一次字段描述映射
            var allCols = [];
            var tableConfigs = [
                massTableConfig, momentTableConfig, calculationTableConfig, 
                calibrationTableConfig, momentCalibrationTableConfig
            ];
            
            // 从所有表格配置中收集列定义
            for (var i = 0; i < tableConfigs.length; i++) {
                var config = tableConfigs[i];
                if (config && config.cols && config.cols[0]) {
                    allCols = allCols.concat(config.cols[0]);
                }
            }
            
            // 构建字段到描述的映射
            for (var i = 0; i < allCols.length; i++) {
                var col = allCols[i];
                if (col.field && col.description) {
                    window._fieldDescriptionMap[col.field] = col.description;
                }
            }
        }
        
        // 确保tooltip容器只创建一次
        var $tooltip = $('#header-tooltip');
        if (!$tooltip.length) {
            $tooltip = $('<div id="header-tooltip" class="layui-tooltip"></div>')
                .css({
                    position: 'absolute',
                    zIndex: 999999,
                    padding: '5px 10px',
                    background: '#000',
                    color: '#fff',
                    borderRadius: '3px',
                    fontSize: '12px',
                    maxWidth: '300px',
                    boxShadow: '0 0 5px rgba(0,0,0,0.3)',
                    display: 'none'
                })
                .appendTo('body');
        }
        
        // 使用事件委托而不是为每个th单独绑定事件
        $('.layui-table-header').off('.tooltip').on({
            'mouseenter.tooltip': function(e) {
                if (e.target.tagName === 'TH' || $(e.target).closest('th').length) {
                    var $th = $(e.target).closest('th');
                    var field = $th.data('field');
                    var description = $th.data('description') || '';
                    
                    // 如果没有直接的描述，从缓存的映射中查找
                    if (!description && field && window._fieldDescriptionMap[field]) {
                        description = window._fieldDescriptionMap[field];
                    }
                    
                    if (description) {
                        // 移除原生title属性，避免重复显示提示
                        $th.removeAttr('title');
                        
                        // 更新并显示tooltip
                        $tooltip.html(description).css({
                            left: e.pageX + 10,
                            top: e.pageY + 10,
                            display: 'block'
                        });
                    }
                }
            },
            'mouseleave.tooltip': function(e) {
                if (e.target.tagName === 'TH' || $(e.target).closest('th').length) {
                    $tooltip.hide();
                }
            },
            'mousemove.tooltip': function(e) {
                if ($tooltip.is(':visible')) {
                    $tooltip.css({
                        left: e.pageX + 10,
                        top: e.pageY + 10
                    });
                }
            }
        }, 'th');
    }

    // 调整表头列宽的函数 - 优化版本
    function adjustHeaderWidth() {
        // 创建一个隐藏的测量容器，只创建一次
        var $measureContainer = $('#width-measure-container');
        if (!$measureContainer.length) {
            $measureContainer = $('<div id="width-measure-container" style="position:absolute;visibility:hidden;white-space:nowrap;"></div>');
            $('body').append($measureContainer);
        }
        
        // 缓存列配置，避免重复查找
        if (!window._columnConfigCache) {
            window._columnConfigCache = {};
            var allConfigs = [
                massTableConfig && massTableConfig.cols && massTableConfig.cols[0],
                momentTableConfig && momentTableConfig.cols && momentTableConfig.cols[0],
                calculationTableConfig && calculationTableConfig.cols && calculationTableConfig.cols[0],
                calibrationTableConfig && calibrationTableConfig.cols && calibrationTableConfig.cols[0],
                momentCalibrationTableConfig && momentCalibrationTableConfig.cols && momentCalibrationTableConfig.cols[0]
            ];
            
            for (var i = 0; i < allConfigs.length; i++) {
                var configs = allConfigs[i];
                if (!configs) continue;
                
                for (var j = 0; j < configs.length; j++) {
                    if (configs[j].field) {
                        window._columnConfigCache[configs[j].field] = configs[j];
                    }
                }
            }
        }
        
        // 收集所有需要调整的表头，一次性处理
        var headerAdjustments = [];
        $('.layui-table-header th').each(function () {
            var $th = $(this);
            var $cell = $th.find('.layui-table-cell');
            var titleText = $cell.text();
            
            // 如果是序号列，固定宽度并跳过调整
            if (titleText === '序号') {
                headerAdjustments.push({
                    $cell: $cell,
                    $th: $th,
                    width: 60
                });
                return; // 跳过序号列的其他处理
            }
            
            // 使用共享的测量容器来测量文本宽度
            $measureContainer.text(titleText);
            var textWidth = $measureContainer.width();
            
            // 额外的边距和内边距（根据实际情况调整）
            var padding = 40;
            var minWidth = Math.max(textWidth + padding, 100);
            
            // 获取当前列的field
            var field = $th.data('field');
            if (field) {
                // 从缓存中查找列配置
                var colConfig = window._columnConfigCache[field];
                if (colConfig) {
                    // 如果列配置中有最小宽度设置，则取两者的较大值
                    var configWidth = colConfig.minWidth || colConfig.width || 0;
                    minWidth = Math.max(minWidth, configWidth);
                    
                    // 更新列配置的宽度
                    if (colConfig.width) {
                        colConfig.width = minWidth;
                    } else {
                        colConfig.minWidth = minWidth;
                    }
                }
            }
            
            // 收集调整信息
            headerAdjustments.push({
                $cell: $cell,
                $th: $th,
                width: minWidth
            });
        });
        
        // 批量应用所有调整，减少重绘次数
        for (var i = 0; i < headerAdjustments.length; i++) {
            var adjustment = headerAdjustments[i];
            adjustment.$cell.css('width', adjustment.width + 'px');
            adjustment.$th.css('min-width', adjustment.width + 'px');
        }
        
        // 获取当前活动的表格实例，只重绘当前活动的表格
        var activeTabIndex = $('.layui-tab-title .layui-this').index();
        var activeTable = null;
        
        switch(activeTabIndex) {
            case 0: activeTable = massTable; break;
            case 1: activeTable = momentTable; break;
            case 2: activeTable = calculationTable; break;
            case 3: activeTable = calibrationTable; break;
            case 4: activeTable = momentCalibrationTable; break;
        }
        
        // 只重绘当前活动的表格
        if (activeTable) {
            activeTable.resize();
        }
    }

    // 查找列配置 - 优化版本，使用缓存避免重复查找
    function findColumnConfig(field) {
        // 使用已经在adjustHeaderWidth中创建的缓存
        if (window._columnConfigCache && window._columnConfigCache[field]) {
            return window._columnConfigCache[field];
        }
        
        // 如果缓存不存在，则创建缓存
        if (!window._columnConfigCache) {
            window._columnConfigCache = {};
            var allConfigs = [
                massTableConfig && massTableConfig.cols && massTableConfig.cols[0],
                momentTableConfig && momentTableConfig.cols && momentTableConfig.cols[0],
                calculationTableConfig && calculationTableConfig.cols && calculationTableConfig.cols[0],
                calibrationTableConfig && calibrationTableConfig.cols && calibrationTableConfig.cols[0],
                momentCalibrationTableConfig && momentCalibrationTableConfig.cols && momentCalibrationTableConfig.cols[0]
            ];

            for (var i = 0; i < allConfigs.length; i++) {
                var configs = allConfigs[i];
                if (!configs) continue;

                for (var j = 0; j < configs.length; j++) {
                    if (configs[j].field) {
                        window._columnConfigCache[configs[j].field] = configs[j];
                    }
                }
            }
            
            // 再次尝试从缓存中获取
            return window._columnConfigCache[field] || null;
        }
        
        return null;
    }

    // 获取通用的表格配置 - 优化版本
    function getTableConfig(elem, cols) {
        // 定义通用的templet函数处理undefined值 - 优化版本
        function formatValue(d) {
            var value = d[this.field];
            // 使用一次性检查，减少条件判断
            if (value === undefined || value === null || 
                value === "undefined" || value === "null" ||
                value === "NaN" || value === "Infinity" || value === "-Infinity" ||
                (typeof value === 'number' && (!isFinite(value) || isNaN(value)))) {
                return "";
            }
            return value;
        }

        // 缓存formatValue函数，避免为每个列创建新函数
        var cachedFormatValue = formatValue;
        
        // 预处理列配置，设置minWidth而不是width以允许自动扩展
        if (cols && cols.length > 0) {
            // 一次性处理所有列配置
            var colsToProcess = cols[0];
            for (var i = 0; i < colsToProcess.length; i++) {
                var col = colsToProcess[i];
                // 如果有width但没有minWidth，则设置minWidth为width并删除width
                if (col.width && !col.minWidth) {
                    col.minWidth = col.width;
                    delete col.width;
                }
                // 如果既没有width也没有minWidth，设置一个默认的minWidth
                if (!col.width && !col.minWidth) {
                    col.minWidth = 100;
                }
                // 如果列没有templet，添加通用的templet处理undefined值
                if (!col.templet && col.field) {
                    col.templet = cachedFormatValue;
                }
                col.unresize = true;
            }
        }

        return {
            elem: elem,
            // url将在实际查询时动态设置
            method: 'GET',
            page: true,
            // 定义解析服务器返回的数据格式
            parseData: function (res) {
                // 处理数据中的undefined和null值
                if (res.data && Array.isArray(res.data)) {
                    res.data.forEach(function (item) {
                        for (var key in item) {
                            if (item[key] === undefined || item[key] === null || item[key] === "undefined" || item[key] === "null") {
                                item[key] = "";
                            }
                        }
                    });
                }

                return {
                    "code": res.success ? 0 : -1, // 解析接口状态
                    "msg": res.msg || "", // 解析提示文本
                    "count": res.count || res.total || (res.data ? res.data.length : 0), // 解析数据长度
                    "data": res.data || [] // 解析数据列表
                };
            },
            // 设置空数据的显示
            text: {
                none: '暂无数据'
            },
            // 当单元格内容为null/undefined等值时，显示为空字符串
            nullText: '',
            // 启用水平滚动条
            scrollbar: {
                type: 'default'
            },
            // 设置单元格最小宽度，防止内容被裁剪
            cellMinWidth: 100,
            // 设置行高
            lineHeight: 30,
            // 设置表格整体大小，高度自适应
            height: calculateTableHeight(),
            // 自动调整列宽
            cellExpandedWidth: 'auto',
            // 显示列标题提示
            showTooltips: true,
            // 启用自动列宽
            autoColWidth: true,
            // 显示溢出文本的提示
            tips: {
                type: 3,
                offset: [10, 0]
            },
            // 分页设置
            limit: 20,
            limits: [10, 20, 50, 100, 200],
            // 页面布局紧凑
            size: 'sm',
            // 添加序号列
            cols: [
                [
                    { type: 'numbers', title: '序号', width: 60, fixed: true }
                ].concat(cols[0])
            ]
        };
    }

    // 日期格式化配置
    var dateConfig = {
        size: 'sm', // 使用小尺寸
        type: 'datetime', // 日期时间选择器
        format: 'yyyy-MM-dd HH:mm:ss', // 与数据库日期格式匹配
        trigger: 'click' // 点击触发
    };

    // 初始化各个日期选择器
    function initDatePickers() {
        // 质心测试数据日期选择器
        laydate.render($.extend({}, dateConfig, { elem: '#massStartDate' }));
        laydate.render($.extend({}, dateConfig, { elem: '#massEndDate' }));

        // 转动惯量测试数据日期选择器
        laydate.render($.extend({}, dateConfig, { elem: '#momentStartDate' }));
        laydate.render($.extend({}, dateConfig, { elem: '#momentEndDate' }));

        // 质心及转动惯量计算数据日期选择器
        laydate.render($.extend({}, dateConfig, { elem: '#calculationStartDate' }));
        laydate.render($.extend({}, dateConfig, { elem: '#calculationEndDate' }));

        // 标定参数日期选择器
        laydate.render($.extend({}, dateConfig, { elem: '#calibrationStartDate' }));
        laydate.render($.extend({}, dateConfig, { elem: '#calibrationEndDate' }));

        // 转动惯量标定表日期选择器
        laydate.render($.extend({}, dateConfig, { elem: '#momentCalibStartDate' }));
        laydate.render($.extend({}, dateConfig, { elem: '#momentCalibEndDate' }));
    }

    // 初始化日期选择器
    initDatePickers();

    // 初始化质心测试数据表格
    var massTableConfig = getTableConfig("#massDataTable", massTableCols);
    // 首次不设置URL，等待loadTabData调用
    var massTable = table.render($.extend({}, massTableConfig, {
        done: tableDoneCallback()
    }));

    // 初始化转动惯量测试数据表格
    var momentTableConfig = getTableConfig("#momentDataTable", momentTableCols);
    var momentTable = table.render($.extend({}, momentTableConfig, {
        done: tableDoneCallback()
    }));

    // 初始化质心及转动惯量计算数据表格
    var calculationTableConfig = getTableConfig("#calculationDataTable", calculationTableCols);
    var calculationTable = table.render($.extend({}, calculationTableConfig, {
        done: tableDoneCallback()
    }));

    // 初始化标定参数表格
    var calibrationTableConfig = getTableConfig("#calibrationDataTable", calibrationTableCols);
    var calibrationTable = table.render($.extend({}, calibrationTableConfig, {
        done: tableDoneCallback()
    }));

    // 添加转动惯量标定表的配置和表格初始化代码
    var momentCalibrationTableConfig = getTableConfig("#momentCalibrationDataTable", momentCalibrationTableCols);
    var momentCalibrationTable = table.render($.extend({}, momentCalibrationTableConfig, {
        done: tableDoneCallback()
    }));

    // 查询函数 - 处理表单提交和查询
    function handleQuery(formName, queryFn) {
        return function (data) {
            console.log(formName + "查询参数(表单提交):", data.field);

            // 获取表单值并设置默认值
            var params = {};
            for (var key in data.field) {
                params[key] = data.field[key] || '';
            }

            // 添加分页参数
            params.page = 1;
            params.limit = 20;

            // 执行查询
            queryFn(params);

            return false; // 阻止表单跳转
        };
    }

    // 查询表格数据的通用函数
    function queryTableData(serviceName, actionName, params, tableInstance) {
        console.log("发送到后端的查询参数:", params);

        // 从params中提取分页参数，防止URL中重复
        var page = params.page || 1;
        var limit = params.limit || 20;

        // 复制一个新的参数对象，排除分页参数
        var queryParams = {};
        for (var key in params) {
            if (key !== 'page' && key !== 'limit' && params[key] !== '') {
                queryParams[key] = params[key];
            }
        }

        // 构建URL查询字符串
        var queryString = "";
        for (var key in queryParams) {
            if (queryParams[key] !== '') {
                queryString += "&" + key + "=" + encodeURIComponent(queryParams[key]);
            }
        }

        // 使用getUrl函数构建完整URL
        var url = getUrl(serviceName, actionName, queryString);
        console.log("请求URL:", url);

        // 使用表格的reload方法，直接通过url进行请求
        tableInstance.reload({
            url: url,
            page: {
                curr: page,
                limit: limit
            },
            // 使用统一的回调函数确保一致性
            done: tableDoneCallback(),
            error: function (e) {
                console.error("请求错误:", e);
                layer.alert('请求出错！', {
                    icon: 2
                });
            }
        });
    }

    // 具体的查询函数
    function queryMassData(params) {
        queryTableData('Thing.Fn.QualityTestDataQuery', 'QueryMassTestData', params, massTable);
    }

    function queryMomentData(params) {
        queryTableData('Thing.Fn.QualityTestDataQuery', 'QueryMomentTestData', params, momentTable);
    }

    function queryCalculationData(params) {
        queryTableData('Thing.Fn.QualityTestDataQuery', 'QueryCalculationData', params, calculationTable);
    }

    function queryCalibrationData(params) {
        queryTableData('Thing.Fn.QualityTestDataQuery', 'QueryCalibrationParameters', params, calibrationTable);
    }

    function queryMomentCalibrationData(params) {
        queryTableData('Thing.Fn.QualityTestDataQuery', 'QueryMomentCalibrationData', params, momentCalibrationTable);
    }

    // 页面加载时初始化加载各个标签页的数据
    function loadTabData(tabIndex) {
        if (tabIndex === 0 || tabIndex === undefined) {
            // 加载质心测试数据
            queryMassData({
                page: 1,
                limit: 20
            });
        } else if (tabIndex === 1) {
            // 加载转动惯量测试数据
            queryMomentData({
                page: 1,
                limit: 20
            });
        } else if (tabIndex === 2) {
            // 加载质心及转动惯量计算数据
            queryCalculationData({
                page: 1,
                limit: 20
            });
        } else if (tabIndex === 3) {
            // 加载标定参数数据
            queryCalibrationData({
                page: 1,
                limit: 20
            });
        } else if (tabIndex === 4) {
            // 加载转动惯量标定表数据
            queryMomentCalibrationData({
                page: 1,
                limit: 20
            });
        }
    }

    // 监听表单提交事件
    form.on('submit(massQuerySubmit)', handleQuery("质心测试数据", queryMassData));
    form.on('submit(momentQuerySubmit)', handleQuery("转动惯量测试数据", queryMomentData));
    form.on('submit(calculationQuerySubmit)', handleQuery("质心及转动惯量计算数据", queryCalculationData));
    form.on('submit(calibrationQuerySubmit)', handleQuery("标定参数", queryCalibrationData));
    form.on('submit(momentCalibrationQuerySubmit)', handleQuery("转动惯量标定表", queryMomentCalibrationData));

    // 处理分页事件的通用函数
    function handlePagebar(formFilterName, queryFn) {
        return function (obj) {
            // 获取当前页码和每页显示条数
            var curr = obj.curr;
            var limit = obj.limit;

            // 获取当前查询条件
            var queryParams = form.val(formFilterName);
            console.log("分页查询参数:", queryParams, "页码:", curr, "每页条数:", limit);

            // 合并查询条件和分页参数
            var params = {};
            for (var key in queryParams) {
                params[key] = queryParams[key] || '';
            }
            params.page = curr;
            params.limit = limit;

            queryFn(params);
        };
    }

    // 监听表格分页事件
    table.on('pagebar(massDataTable)', handlePagebar('massQueryForm', queryMassData));
    table.on('pagebar(momentDataTable)', handlePagebar('momentQueryForm', queryMomentData));
    table.on('pagebar(calculationDataTable)', handlePagebar('calculationQueryForm', queryCalculationData));
    table.on('pagebar(calibrationDataTable)', handlePagebar('calibrationQueryForm', queryCalibrationData));
    table.on('pagebar(momentCalibrationDataTable)', handlePagebar('momentCalibrationQueryForm', queryMomentCalibrationData));

    // 监听Tab切换事件，重新计算表格高度 - 优化版本
    element.on('tab(dataQueryTab)', function (data) {
        // 使用requestAnimationFrame优化视觉更新
        requestAnimationFrame(function() {
            // 先调整表格高度，让用户看到表格框架
            var tables = [massTable, momentTable, calculationTable, calibrationTable, momentCalibrationTable];
            if (data.index >= 0 && data.index < tables.length) {
                tables[data.index].resize({
                    height: calculateTableHeight()
                });
            }
            
            // 然后加载数据
            loadTabData(data.index);
        });
    });

    // 初始加载第一个标签页数据 - 使用延迟加载优化初始渲染速度
    setTimeout(function() {
        loadTabData(0);
    }, 10);
});