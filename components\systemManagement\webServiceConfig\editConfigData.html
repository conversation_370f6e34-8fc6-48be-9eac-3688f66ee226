<head>
	<meta http-equiv="content-type" content="txt/html; charset=utf-8" />
	<link rel="stylesheet" href="../../../plugins/layui/css/layui.css" media="all">
	<link href="../../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

	<link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
	<link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">


	<script src="../../../plugins/InsdepUI/jquery.min.js"></script>
	<script src="../../../plugins/InsdepUI/jquery.easyui.min.js"></script>
	<script src="../../../plugins/InsdepUI/insdep.extend.min.js"></script>
	<script src="../../../plugins/layui/layui.js"></script>
	<script src="../../js/config/twxconfig.js"></script>
	<script src="../../js/util.js"></script>

	<script type="text/javascript" src="../../js/intercept.js"></script>
	<script type="text/javascript" src="../../js/logUtil.js"></script>
</head>


<body style="padding: 15px">
	<form class="layui-form layui-form-pane" action="">
		<input type="hidden" name="ID" class="layui-input" id="ID">
		<div class="layui-form-item">
			<label class="layui-form-label">来源系统</label>
			<div class="layui-input-block">
				<select name="SOURCE_SYSTEM" lay-verify="required" id="SOURCE_SYSTEM" disabled="disabled">
					<option value=""></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">类别</label>
			<div class="layui-input-block">
				<select name="TYPE_NAME" lay-verify="required" id="TYPE_NAME" disabled="disabled">
					<option value=""></option>
					<option value="设计">设计</option>
					<option value="工艺">工艺</option>
					<option value="过程控制">过程控制</option>
					<option value="质量综合">质量综合</option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">文件类别</label>
			<div class="layui-input-block">
				<input type="text" name="DOCTYPE" id="DOCTYPE" lay-verify="required" autocomplete="off" class="layui-input"
				 disabled="disabled">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">用途</label>
			<div class="layui-input-block">
				<input type="text" name="PURPOSE" lay-verify="required" autocomplete="off" class="layui-input" id="PURPOSE">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">服务路径</label>
			<div class="layui-input-block">
				<input type="text" name="URL" lay-verify="required" autocomplete="off" class="layui-input" id="URL">
			</div>
		</div>
		<div class="layui-form-item" style="display: none;">
			<center>
				<button id="btn_add" class="layui-btn" lay-submit lay-filter="addData">提交</button>
				<button id="btn_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
				<!--<button class="layui-btn" lay-submit lay-filter="editData">提交</button>-->
				<!--<button class="layui-btn layui-btn-primary" type="reset">重置</button>-->
			</center>
		</div>
	</form>



</body>

<script>
	//编辑时初始化数据
	var initData = function(form) {
		var editedData = parent.selectedData.data;
		var initComponent = {
			file_types: {
				type: '文件类别'
			},
			SOURCE_SYSTEM: {
				type: '来源系统'
			},
			dstates: {
				type: '交付状态'
			}
		};

		for (var x in initComponent) {
			//初始化下拉列表
			var type = initComponent[x].type;
			var compId = x;

			var prop = '';
			if (x === 'file_types') {
				prop = 'FILE_TYPE';
			} else if (x === 'SOURCE_SYSTEM') {
				prop = 'SOURCE_SYSTEM';
			} else if (x === 'dstates') {
				prop = 'DELIVERY_STATE';
			}

			var types = twxAjax("publishMissionThing", "getDataFromDataDictionary", {
				type: type
			}, false, function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					var key = data.rows[i].ITEM_KEY;
					var value = data.rows[i].ITEM_VALUE;
					var html = '<option value="' + key + '" ';
					if (editedData[prop] === value) {
						html += ' selected ';
					}
					html += '>' + value + '</option>';
					$("#" + compId).append(html);
				}

				form.render();
			});
		}

		//设定采集方式
		$('#SOURCE_SYSTEM').val(editedData.SOURCE_SYSTEM);
		$('#TYPE_NAME').val(editedData.TYPE_NAME);
		$('#DOCTYPE').val(editedData.DOCTYPE);
		$('#PURPOSE').val(editedData.PURPOSE);
		//编辑时需要将ID带入
		$('#URL').val(editedData.URL);
		$('#ID').val(editedData.ID);
	};

	layui.config({
		base: '/DataPackageManagement/build/js/' //假设这是你存放拓展模块的根目录
	}).use(['form', 'laydate', 'table', 'utils', 'layer'], function() {
		var form = layui.form;
		var laydate = layui.laydate;
		var table = layui.table;
		var utils = layui.utils;
		var layer = layui.layer;


		//监听提交
		form.on('submit(addData)', function(data) {
			var param = data.field;

			// var check_data;
			// var check_success = function(data){
			//     check_data = data.rows;
			// };
			// //添加失败的弹窗
			// var check_error = function(xhr){
			//     layer.alert('校验错误，请联系管理员!',{icon:2},function(){
			//
			//     });
			// };
			// twxAjax("publishMissionThing","getWSConfigDataByConditions",param,false,check_success,check_error);
			// if (check_data.length>0){
			//     layer.alert('已存在同用途的接口，请检查后重试！',{icon:2});
			//     return false;
			// }
			var editedData = parent.selectedData.data;

			//获取父页面的参数
			param.type = "WSConfigTable";

			//添加成功的弹窗
			var cb_success = function(data) {
				//新增完成后需要刷新界面
				layer.alert("编辑成功", {
					icon: 1
				}, function(index) {
					//提示完成后，点击确定再刷新界面
					parent.reloadTable("WSConfigTable");

					logRecord('编辑', '接口配置-接口配置(ID：' + editedData.ID + '、来源系统：' + editedData.SOURCE_SYSTEM + '、类别：' + editedData
						.TYPE_NAME + '、文件类别：' + editedData.DOCTYPE +
						'、用途：' + editedData.PURPOSE + '、服务路径：' + editedData.URL + ')更新为(来源系统：' + param.SOURCE_SYSTEM + '、类别：' +
						param.TYPE_NAME +
						'、文件类别：' + param.DOCTYPE +
						'、用途：' + param.PURPOSE + '、服务路径：' + param.URL + ')', 1);

					var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
					parent.layer.close(index); //再执行关闭
				});
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {
				logRecord('编辑', '接口配置-接口配置(ID：' + editedData.ID + '、来源系统：' + editedData.SOURCE_SYSTEM + '、类别：' + editedData.TYPE_NAME +
					'、文件类别：' + editedData.DOCTYPE +
					'、用途：' + editedData.PURPOSE + '、服务路径：' + editedData.URL + ')更新为(来源系统：' + param.SOURCE_SYSTEM + '、类别：' +
					param.TYPE_NAME +
					'、文件类别：' + param.DOCTYPE +
					'、用途：' + param.PURPOSE + '、服务路径：' + param.URL + ')', 0);
				layer.alert('编辑失败!', {
					icon: 2
				});
			};

			//应该使用update进行参数的传递
			twxAjax("publishMissionThing", "updateDataToMSConfigTable", param, false, cb_success, cb_error);
			return false;
		});
		//初始化待编辑的数据
		initData(form);
		form.render();

	});
</script>
