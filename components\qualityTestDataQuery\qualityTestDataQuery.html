<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>质量测试数据查询</title>
        <meta name="renderer" content="webkit" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" />
        <style>
            /* 紧凑页面布局 */
            body {
                padding: 5px;
                overflow-y: hidden;
            }
            .layui-form-item {
                margin-bottom: 5px;
            }
            .layui-input, .layui-select, .layui-textarea {
                height: 30px;
                line-height: 30px;
            }
            .layui-form-label {
                padding: 5px 10px;
                height: 20px;
                line-height: 20px;
            }
            .layui-input-block {
                min-height: 30px;
                line-height: 30px;
            }
            .layui-btn {
                height: 30px;
                line-height: 30px;
                padding: 0 15px;
            }
            .layui-card-body {
                padding: 5px;
            }
            /* 减小标签页高度 */
            .layui-tab-title {
                height: 35px;
            }
            .layui-tab-title li {
                line-height: 35px;
            }
            .layui-tab-content {
                padding: 5px 0;
            }
            /* 优化表格区域 */
            .layui-table-view {
                margin: 0;
            }
            /* 行间距更紧凑 */
            .layui-table td, .layui-table th {
                padding: 5px 15px;
            }
            /* 日期范围选择器样式 */
            .date-range {
                display: flex;
                align-items: center;
            }
            .date-range .layui-input {
                width: 85%;
            }
            .date-range .date-separator {
                margin: 0 5px;
            }
            
            /* 性能优化：预设表格单元格样式，避免JS中设置 */
            .layui-table-cell {
                white-space: nowrap !important;
                text-overflow: inherit !important;
            }
            
            /* 性能优化：预设tooltip样式 */
            #header-tooltip {
                position: absolute;
                z-index: 999999;
                padding: 5px 10px;
                background: #000;
                color: #fff;
                border-radius: 3px;
                font-size: 12px;
                max-width: 300px;
                box-shadow: 0 0 5px rgba(0,0,0,0.3);
                display: none;
            }
            
            /* 性能优化：预设宽度测量容器样式 */
            #width-measure-container {
                position: absolute;
                visibility: hidden;
                white-space: nowrap;
            }
        </style>
    </head>
    <body>
        <!-- 标签页组件 -->
        <div class="layui-tab" lay-filter="dataQueryTab">
            <ul class="layui-tab-title">
                <li class="layui-this">质心测试数据</li>
                <li>转动惯量测试数据</li>
                <li>质心及转动惯量计算数据</li>
                <li>标定参数</li>
                <li>转动惯量标定表</li>
            </ul>
            <div class="layui-tab-content">
                <!-- 质心测试数据 -->
                <div class="layui-tab-item layui-show">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <form class="layui-form" id="massQueryForm" lay-filter="massQueryForm">
                                <div class="layui-row layui-col-space5">
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">测试编号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="testId" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">产品型号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="productModel" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">批次</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="batch" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">产品名称</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="productName" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">测试日期</label>
                                            <div class="layui-input-block date-range">
                                                <input type="text" name="startDate" placeholder="开始日期" autocomplete="off" class="layui-input" id="massStartDate" />
                                                <span class="date-separator">至</span>
                                                <input type="text" name="endDate" placeholder="结束日期" autocomplete="off" class="layui-input" id="massEndDate" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md1">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block" style="margin-left: 10px;">
                                                <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="massQuerySubmit" id="massQueryBtn">查询</button>
                                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <table id="massDataTable" lay-filter="massDataTable"></table>
                </div>

                <!-- 转动惯量测试数据 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <form class="layui-form" id="momentQueryForm" lay-filter="momentQueryForm">
                                <div class="layui-row layui-col-space5">
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">测试编号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="testId" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">产品型号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="productModel" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">产品批次</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="productBatch" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">产品名称</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="productName" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">测试日期</label>
                                            <div class="layui-input-block date-range">
                                                <input type="text" name="startDate" placeholder="开始日期" autocomplete="off" class="layui-input" id="momentStartDate" />
                                                <span class="date-separator">至</span>
                                                <input type="text" name="endDate" placeholder="结束日期" autocomplete="off" class="layui-input" id="momentEndDate" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md1">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block" style="margin-left: 10px;">
                                                <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="momentQuerySubmit" id="momentQueryBtn">查询</button>
                                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <table id="momentDataTable" lay-filter="momentDataTable"></table>
                </div>

                <!-- 质心及转动惯量计算数据 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <form class="layui-form" id="calculationQueryForm" lay-filter="calculationQueryForm">
                                <div class="layui-row layui-col-space5">
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">测试编号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="testId" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">产品型号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="productModel" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">测试日期</label>
                                            <div class="layui-input-block date-range">
                                                <input type="text" name="startDate" placeholder="开始日期" autocomplete="off" class="layui-input" id="calculationStartDate" />
                                                <span class="date-separator">至</span>
                                                <input type="text" name="endDate" placeholder="结束日期" autocomplete="off" class="layui-input" id="calculationEndDate" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md1">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block" style="margin-left: 10px;">
                                                <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="calculationQuerySubmit" id="calculationQueryBtn">查询</button>
                                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <table id="calculationDataTable" lay-filter="calculationDataTable"></table>
                </div>

                <!-- 标定参数 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <form class="layui-form" id="calibrationQueryForm" lay-filter="calibrationQueryForm">
                                <div class="layui-row layui-col-space5">
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">编号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="id" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">标定日期</label>
                                            <div class="layui-input-block date-range">
                                                <input type="text" name="startDate" placeholder="开始日期" autocomplete="off" class="layui-input" id="calibrationStartDate" />
                                                <span class="date-separator">至</span>
                                                <input type="text" name="endDate" placeholder="结束日期" autocomplete="off" class="layui-input" id="calibrationEndDate" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md1">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block" style="margin-left: 10px;">
                                                <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="calibrationQuerySubmit" id="calibrationQueryBtn">查询</button>
                                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <table id="calibrationDataTable" lay-filter="calibrationDataTable"></table>
                </div>

                <!-- 转动惯量标定表 -->
                <div class="layui-tab-item">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <form class="layui-form" id="momentCalibrationQueryForm" lay-filter="momentCalibrationQueryForm">
                                <div class="layui-row layui-col-space5">
                                    <div class="layui-col-md2">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">编号</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="id" autocomplete="off" class="layui-input" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">标定日期</label>
                                            <div class="layui-input-block date-range">
                                                <input type="text" name="startDate" placeholder="开始日期" autocomplete="off" class="layui-input" id="momentCalibStartDate" />
                                                <span class="date-separator">至</span>
                                                <input type="text" name="endDate" placeholder="结束日期" autocomplete="off" class="layui-input" id="momentCalibEndDate" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md1">
                                        <div class="layui-form-item">
                                            <div class="layui-input-block" style="margin-left: 10px;">
                                                <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="momentCalibrationQuerySubmit" id="momentCalibrationQueryBtn">查询</button>
                                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <table id="momentCalibrationDataTable" lay-filter="momentCalibrationDataTable"></table>
                </div>
            </div>
        </div>

        <script src="../../plugins/layui-lasted/layui.js"></script>
        <script>
            var $ = layui.$;
        </script>
        <script src="../js/config/twxconfig.js"></script>
        <script src="../js/util.js"></script>
        <script src="table-config.js"></script>
        <script src="main.js"></script>
    </body>
</html>
