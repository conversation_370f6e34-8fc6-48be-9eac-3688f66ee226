function getInputHtml(input) {
	var inputId = input.id || '';
	return '<div class="layui-inline">\
	  <label class="layui-form-label" style="width: 90px;">' + input.name + '</label>\
	  <div class="layui-input-inline" style="width: 220px;">\
		<input type="text" value="' + input.value + '" id="' + inputId + '" class="layui-input" disabled="disabled" readonly="readonly">\
	  </div>\
	</div>';
}

function get$Form(forms, elementsPerRow = 3) {
	var $form = $('<form class="layui-form"></form>');
	for (var i = 0; i < forms.length; i += elementsPerRow) {
		var $item = $('<div class="layui-form-item"></div>');
		for (var j = 0; j < elementsPerRow && (i + j) < forms.length; j++) {
			$item.append(getInputHtml(forms[i + j]));
		}
		$form.append($item);
	}
	return $form;
}

function getCols(titles, editType) {
	var tableCols = [];
	tableCols.push({
		type: "checkbox",
		fixed: 'left'
	});
	tableCols.push({
		title: "#",
		type: "numbers",
		fixed: 'left'
	});
	if (editType == 'text') {
		tableCols.push({
			title: "操作",
			width: 60,
			toolbar: '<div class="layui-clear-space">\
						<a style="cursor: pointer;font-weight: bold;padding-left: 5px;" title="上传文件" lay-event="uploadRowFile">\
							<i class="layui-icon layui-icon-upload-circle" style="font-size: 19px;color: #16b777;"></i>\
						</a>\
					  </div>',
			fixed: 'left'
		});
	}

	for (var i = 0; i < titles.length; i++) {
		var minWidth = StringTolog(titles[i].displayName) * 15 + 30;
		if (titles[i].internalName == 'IMAGE') {
			tableCols.push({
				field: titles[i].internalName,
				title: titles[i].displayName,
				minWidth: minWidth,
				type: 'normal',
				edit: editType,
				toolbar: ' <div class="layui-clear-space">\
							<a class="layui-btn layui-btn-xs" lay-event="viewFile">预览</a>\
						  </div>'
			});
		} else {
			tableCols.push({
				minWidth: minWidth,
				field: titles[i].internalName,
				title: titles[i].displayName,
				type: 'normal',
				edit: editType
			});
		}

	}
	return tableCols;
}

function getBtnHtml(btns) {
	var $container = $('<div class="layui-btn-container"></div>');
	for (var i = 0; i < btns.length; i++) {
		$container.append('<button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="' + btns[i].event + '">\
								<i class="layui-icon layui-icon-' + btns[i].icon + '"></i> ' + btns[i].name + '\
							</button>');
	}
	return $container[0].outerHTML;
}


function openDownloadDialog(url, saveName) {
	if (typeof url == 'object' && url instanceof Blob) {
		url = URL.createObjectURL(url); // 创建blob地址
	}
	var aLink = document.createElement('a');
	aLink.href = url;
	aLink.download = saveName || ''; // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
	var event;
	if (window.MouseEvent) event = new MouseEvent('click');
	else {
		event = document.createEvent('MouseEvents');
		event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0,
			null);
	}
	aLink.dispatchEvent(event);
}

// 将一个sheet转成最终的excel文件的blob对象，然后利用URL.createObjectURL下载
function sheet2blob(sheet, sheetName) {
	sheetName = sheetName || 'sheet1';
	var workbook = {
		SheetNames: [sheetName],
		Sheets: {}
	};
	workbook.Sheets[sheetName] = sheet;
	// 生成excel的配置项
	var wopts = {
		bookType: 'xlsx', // 要生成的文件类型
		bookSST: false, // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
		type: 'binary'
	};
	var wbout = XLSX.write(workbook, wopts);
	var blob = new Blob([s2ab(wbout)], {
		type: "application/octet-stream"
	});
	// 字符串转ArrayBuffer
	function s2ab(s) {
		var buf = new ArrayBuffer(s.length);
		var view = new Uint8Array(buf);
		for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
		return buf;
	}
	return blob;
}