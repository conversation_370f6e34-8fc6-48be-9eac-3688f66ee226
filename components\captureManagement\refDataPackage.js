var dpThingName = 'Thing.Fn.DataPackage';
/**
 * 初始化关联的数据包表格
 */

var initDataPackageRefTable = function(layui){
    $('#datapkgref').datagrid({
        data: [],
		singleSelect: true,
		striped: true,
		rownumbers:true,
		toolbar: [
			{
				iconCls: 'icon-myadd',
				text: '新增'
			}, {
				iconCls: 'icon-myedit',
				text: '编辑'
			}, {
				iconCls: 'icon-myremove',
				text: '删除'
			}
		],
		fit: true,
		columns: [[
            {field:'ID',title:'ID'},
            {field:'NAME',title:'数据包名称'},
            {field:'CODE',title:'数据包编号'},
            {field:'REMARK',title:'描述'},
            {field:'REFTREEID',title:'过程节点ID'},
            {field:'CERATOR',title:'创建者'},
            {field:'CREATETIME',title:'创建事件'}
		]],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
        loadMsg: '正在加载数据...',
        onSelect:function(){
            //当数据选中后重新加载各类别的数据
            var selectedTab = $('#root_layout_tabs').tabs('getSelected');
			var tabId = selectedTab.panel('options').name;
			reloadTable(tabId);
        }
    });
};

//重新加载表格数据
var reloadRefDPTable = function(treeId){

    //获取选中的数据节点
    // var treeId = '';
    // var selNodes = ztreeObj.getSelectedNodes();
    // if (selNodes.length > 0) {
    //     treeId = selNodes[0].TREEID;
    // }

    $('#datapkgref').datagrid('loading');
    var cb_success = function(data){
        $('#datapkgref').datagrid('loadData',data.rows);
        $('#datapkgref').datagrid('loaded');

        //如果数据存在则默认选中第一条
        if(data.rows.length > 0 ){
            $('#datapkgref').datagrid('selectRow',0);
        }else{
            var selectedTab = $('#root_layout_tabs').tabs('getSelected');
			var tabId = selectedTab.panel('options').name;
            reloadTable(tabId);
        }
    };
    var cb_error = function(){};
    twxAjax(dpThingName,'QueryDataPkg',{refTreeId:treeId},true,cb_success,cb_error);
}

$(document).ready(function(){
    layui.use(['layer'],function(){
        //初始化数据
        initDataPackageRefTable(layui);
    });
});