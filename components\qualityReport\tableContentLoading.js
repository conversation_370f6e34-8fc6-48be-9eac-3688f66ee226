/**
 * <AUTHOR>
 * @datetime 2025年4月2日10:56:04
 * @function	tableContentLoading
 * @description	表格内容加载
 */



//加载自动汇总表
function loadAutoSummaryTable(res, tableConfigId) {
    $('#table').css("padding", "0px 10px");
    $('#table').append('<div id="autoSummaryTable" style="float: left;" ></div>');
    var cb_success = function (data) {
        if (data.success) {
            var dealCol = dealColumns(JSON.parse(data.result));
            var cols = dealCol.col;
            //清除多选列和状态列
            cols[0].splice(0, 2);
            $('#autoSummaryTable').datagrid({
                data: res.data,
                columns: cols,
                height: windowH - 90,
                singleSelect: true,
                remoteSort: false,
                emptyMsg: '<div style="color:red; padding-left:10px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
                loadMsg: '正在加载数据...',
                striped: false,
                onLoadSuccess: function (data) {
                    var rows = data.rows;
                    var $datagrid = $('#autoSummaryTable');
                    if (rows.length > 0) {
                        for (var i = 0; i < rows.length; i++) {
                            var row = rows[i];
                            var mergedInfo = row.mergedInfo;
                            if (mergedInfo != "" && mergedInfo != undefined) {
                                var mergeds = mergedInfo.split(",");
                                for (var j = 0; j < mergeds.length; j++) {
                                    var merged = mergeds[j];
                                    var columnName = merged.split(":")[0];
                                    var rowspan = merged.split(":")[1];
                                    $datagrid.datagrid('mergeCells', {
                                        index: i,
                                        field: columnName,
                                        rowspan: rowspan
                                    });
                                }
                            }
                        }
                    }
                    changeWidth('autoSummaryTable');
                    $("#table .datagrid-body").css("overflow-x", "auto");
                    $('#autoSummaryTable').datagrid('loaded');
                }
            });
        } else {
            layer.alert("表头获取失败！", {
                icon: 2
            });
        }
    };
    var cb_error = function () {
        layer.alert("表头获取失败！", {
            icon: 2
        });
    };
    twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', {
        id: tableConfigId
    }, true, cb_success, cb_error);
}

//加载影像记录表
function loadAutoPhotoTable(res, tableConfigId, treeId) {
    loadQualityPhoto(false);
    var data = res.data;
    var signs = res.signs;
    $("#table").css('height', (windowH - 90) + 'px').css("padding", "0px 10px");
    var statusStr = "状态";
    var confirmerStr = "确认人";
    var actualPhotoStr = "实际照片";
    var $table = $('<table class="my-table layui-table"></table>');
    var $thead = $('<thead class="sticky-thead"></thead>');
    var cols = data[0].cols;
    //添加表头
    var $headerTr = $('<tr></tr>');
    for (var j = 0; j < cols.length; j++) {
        $headerTr.append('<td class="table-head" align="' + planTableAlign + '">' + cols[j] + '</td>');
    }
    $thead.append($headerTr);
    $table.append($thead);
    for (var i = 0; i < data.length; i++) {
        var table = data[i];
        var datas = table.datas;
        var cols = table.cols;
        var onlyValues = table.onlyValues;
        var statusIndex = cols.indexOf(statusStr);
        var confirmerIndex = cols.indexOf(confirmerStr);
        var photoIndex = cols.indexOf(actualPhotoStr);
        var relationParam = table.relationParam;
        //添加合并行
        $table.append('<tr ><td class="table-name" colspan=' + cols.length + '>' + table.name + '</td></tr>');
        //添加表数据
        for (var j = 0; j < datas.length; j++) {
            var $tr = $('<tr></tr>');
            var d = datas[j];
            var onlyValue = onlyValues[j];
            for (var x = 0; x < d.length; x++) {

                if (x == photoIndex) {
                    var menus = [];

                    var photos = [];
                    if (d[x] != '[]') {
                        photos = JSON.parse(d[x]);
                        menus.push({
                            text: "查看照片",
                            icon: '../dataTree/images/view.png',
                            callback: function () {
                                qualityPhoto.previewPhoto(tableConfigId, treeId, true);
                            }
                        });
                    }
                    var $td = $('<td align="' + planTableAlign + '">' + qualityPhoto.getPhotoStr(photos) + '</td>');
                    $td.data('photos', photos);
                    $td.data('tableName', table.name);
                    $td.data("confirmer", d[confirmerIndex]);
                    $td.data('paramId', relationParam["ID"]);
                    $td.data("onlyValue", onlyValue);
                    $tr.append($td);
                    if (menus.length > 0) {
                        $td.contextMenu({
                            width: 100,
                            menu: menus,
                            target: function (ele) {
                                qualityPhoto.contextMenuTarget = ele;
                            }
                        });
                    }
                } else {
                    var html = String(d[x]);
                    if (html.indexOf(":noKey") > -1) {
                        html = '<span style="color:red;">' + html.replace(":noKey", "") + "</span>";
                    }
                    $tr.append('<td align="' + planTableAlign + '">' + html + '</td>');
                }
            }
            $table.append($tr);
        }
    }
    $("#table").append($table);

    var $signDiv = $('<div class="layui-row"></div>');
    var $innerDiv = $('<div class="layui-col-md6"></div>');
    var $outerDiv = $('<div class="layui-col-md6"></div>');
    var $innerBtn = $('<div style="font-size:18px;font-weight:bold;">所内人员签字:</div>');
    var $outerBtn = $('<div style="font-size:18px;font-weight:bold;">外所人员签字:</div>');
    $innerDiv.append($innerBtn);
    $outerDiv.append($outerBtn);
    $signDiv.append($innerDiv).append($outerDiv);


    var $signImgDiv = $('<div class="layui-row"></div>');
    var $innerImgDiv = $('<div class="layui-col-md6" style = "height:1px;" id="data-innerImgDiv"></div>');
    var $outerImgDiv = $('<div class="layui-col-md6" id="data-outerImgDiv"></div>');
    for (var i = 0; i < signs.length; i++) {
        var sign = signs[i];
        var type = sign['TYPE'];
        var img = sign['IMG'];
        var $img = $('<img>').attr('src', img).attr('class', 'sign-img');
        if (type == 1) {
            $innerImgDiv.append($img);
        } else {
            $outerImgDiv.append($img);
        }
    }
    $signImgDiv.append($innerImgDiv).append($outerImgDiv);
    $("#table").append($signDiv).append($signImgDiv);
}

//加载自动确认表
function loadAutoConfirmTable(res) {
    var data = res.data;
    var signs = res.signs;
    $("#table").css('height', (windowH - 90) + 'px').css("padding", "0px 10px");
    var statusStr = "状态";
    var confirmerStr = "确认人";
    var $table = $('<table class="my-table layui-table"></table>');
    for (var i = 0; i < data.length; i++) {
        var table = data[i];
        var isBase = table.isBase;
        var datas = table.datas;
        var onlyValues = table.onlyValues;
        var cols = table.cols;
        var statusIndex = cols.indexOf(statusStr);
        var confirmerIndex = cols.indexOf(confirmerStr);
        var relationParamName = table.relationParamName;
        var relationParamId = table.relationParamId;
        //添加合并行
        $table.append('<tr><td class="table-name" colspan=' + cols.length + '>' + table.name + '</td></tr>');
        //添加表头
        var $headerTr = $('<tr></tr>');
        for (var j = 0; j < cols.length; j++) {
            $headerTr.append('<td class="table-head" align="' + planTableAlign + '">' + cols[j] + '</td>');
        }
        $table.append($headerTr);
        //添加表数据
        for (var j = 0; j < datas.length; j++) {
            var $tr = $('<tr></tr>');
            var d = datas[j];
            var onlyValue = onlyValues[j];
            for (var x = 0; x < d.length; x++) {
                $tr.append('<td align="' + planTableAlign + '">' + d[x] + '</td>');
            }
            $table.append($tr);
        }
    }
    $("#table").append($table);

    var $signDiv = $('<div class="layui-row"></div>');
    var $innerDiv = $('<div class="layui-col-md6"></div>');
    var $outerDiv = $('<div class="layui-col-md6"></div>');
    var $innerBtn = $('<div style="font-size:18px;font-weight:bold;">所内人员签字:</div>');
    var $outerBtn = $('<div style="font-size:18px;font-weight:bold;">外所人员签字:</div>');
    $innerDiv.append($innerBtn);
    $outerDiv.append($outerBtn);
    $signDiv.append($innerDiv).append($outerDiv);


    var $signImgDiv = $('<div class="layui-row"></div>');
    var $innerImgDiv = $('<div class="layui-col-md6" style = "height:1px;" id="data-innerImgDiv"></div>');
    var $outerImgDiv = $('<div class="layui-col-md6" id="data-outerImgDiv"></div>');
    for (var i = 0; i < signs.length; i++) {
        var sign = signs[i];
        var type = sign['TYPE'];
        var img = sign['IMG'];
        var $img = $('<img>').attr('src', img).attr('class', 'sign-img');
        if (type == 1) {
            $innerImgDiv.append($img);
        } else {
            $outerImgDiv.append($img);
        }
    }
    $signImgDiv.append($innerImgDiv).append($outerImgDiv);
    $("#table").append($signDiv).append($signImgDiv);
}

//加载手动编辑和文件清单类型的表格
function loadHtmlTable(res, treeNode) {
    if (res.success) {
        $("#table").empty();
        var html = res.data.HTML_DATA || "";
        if (res.data.SIGN_HTML) {
            treeNode.SIGN_HTML = res.data.SIGN_HTML;
            html = res.data.SIGN_HTML;
        }
        //更新node节点的数据
        treeNode.HTML_DATA = html;
        treeNode.SAVE_DATA = res.data.SAVE_DATA || "";
        treeNode.TABLE_STATUS = res.data.TABLE_STATUS || "";
        ztreeObj.updateNode(treeNode);
        initTbr(treeNode);
        if (html != "") {
            //添加表格
            var $table = $(html.replaceAll("\n", "<br>"));
            if (res.data.TABLE_HEADER) {
                var tableHeader = res.data.TABLE_HEADER;
                var min = 0;
                var max = 0;
                if (tableHeader.indexOf("-") > -1) {
                    min = Number(tableHeader.split("-")[0]);
                    max = Number(tableHeader.split("-")[1]);
                } else {
                    max = Number(tableHeader);
                }
                $table.find("tr").each(function (i, n) {
                    if (i <= (max - 1) && i >= (min - 1)) {
                        $(n).css("font-weight", "bold").css("background-color", "#e6e6e6");
                    }
                });
            }
            $("#table").append($table);
            $("#quality_report_tab #table .layui-table tbody tr:hover").css("background-color", "");
            if (treeNode.TABLE_STATUS == 'sign') {
                $(".layui-table td").each(function (i, n) {
                    $(n).contextMenu({
                        width: 130,
                        menu: [{
                            text: "签名",
                            icon: '../dataTree/images/sign.png',
                            callback: function () {
                                sign(treeNode, contextEle);
                            }
                        }, {
                            text: "上传图片",
                            icon: '../dataTree/images/upload-image.png',
                            callback: function () {
                                var cb_success = function (res) {
                                    insertImage(treeNode, contextEle, res.rows[0]
                                        .result);
                                }
                                //请求失败的回调
                                var cb_error = function (xhr, textStatus, errorThrown) {
                                    layer.alert("上传图片出错！", {
                                        icon: 2
                                    });
                                };
                                twxAjax(THING, "GetNewPhotoNum", {
                                    reportId: treeNode.ID
                                }, true, cb_success, cb_error);
                            }
                        }, {
                            text: "查看图片",
                            icon: '../dataTree/images/view.png',
                            callback: function () {
                                viewImage(treeNode, contextEle);
                            }
                        }],
                        target: function (ele) {
                            contextEle = ele;
                        }
                    });
                });
            }
        } else {
            $("#table").append('<span style="color:red;"> 请先编辑表格！</span>');
        }
    } else {
        $("#table").hide();
        $("#msg").text(res.msg).show();
    }
}