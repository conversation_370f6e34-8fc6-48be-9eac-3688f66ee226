function renderResultTable() {
	var tableHeight = $(".layui-col-md10 .layui-card").height() - 63;
	table.render({
		elem: '#result-table',
		id: 'result-table',
		url: getUrl(THING, 'QueryCalcResult'),
		where: {
			pId: currentTreeId
		},
		toolbar: '#result-toolbar',
		defaultToolbar: ['filter', {
			title: '刷新',
			layEvent: 'result_reload',
			icon: 'layui-icon-refresh'
		}],
		height: tableHeight,
		cellMinWidth: 80,
		cols: [
			[{
					type: 'checkbox',
					fixed: 'left'
				},
				{
					title: '序号',
					type: "numbers",
					width: 60
				},
				{
					field: 'NAME',
					width: 120,
					title: '名称'
				},
				{
					field: 'CODE',
					title: '代号',
					width: 80
				},
				{
					field: 'FORMULA',
					title: '公式',
					width: 120
				},
				{
					field: 'EXPENSE_TYPE',
					title: '费用类型',
					width: 120
				},
				{
					field: 'REMARK',
					title: '备注',
					width: 120
				},
				{
					fixed: 'right',
					title: '操作',
					width: 230,
					minWidth: 230,
					toolbar: '#result-rowbar',
					align: 'center'
				}
			]
		],
		done: function(res, curr, count, origin) {
			if (res.data.length > 0) {
				showVerify();
				initVerifyForm();
				hideFormula("请点击输出参数的编辑公式按钮进行公式编辑！");
			} else {
				var msg = "请添加输出参数！";
				hideVerify(msg);
			}
		},
		error: function(res, msg) {
			console.log(res, msg)
		}
	});

	// 工具栏事件
	table.on('toolbar(result-table)', function(obj) {
		switch (obj.event) {
			case 'add-btn':
				addResult();
				break;
			case 'delete-btn':
				deleteMoreResult();
				break;
			case 'result_reload':
				renderResultTable();
				break;
		};
	});

	table.on('tool(result-table)', function(obj) {
		var data = obj.data; // 获得当前行数据
		if (obj.event === 'quote') {
			var code = getCurrentResultCode();
			if (code == data['CODE']) {
				layer.alert('无法引用自身！', {
					icon: 2
				});
			} else {
				formulaAdd('`' + data['CODE'] + '`');
			}

		} else if (obj.event === 'formula') {
			//编辑公式
			initFormula(data);
		} else if (obj.event === 'edit') {
			editResult(data);
		} else if (obj.event === 'delete') {
			deleteResult(data.ID);
		}
	});
}

/**
 * 获取当前结果表格的所有数据
 */
function getCurrentResultData() {
	return table.getData('result-table');
}

/**
 * 隐藏输出参数模块 并给出信息提示
 * @param {Object} msg
 */
function hideResult(msg) {
	$('#result-div').hide();
	$("#result-msg").text(msg).show();
}

/**
 * 显示输出参数模块 并隐藏信息提示
 */
function showResult() {
	$('#result-div').show();
	$("#result-msg").hide();
}

function deleteResult(ids) {
	var msg = "确认删除参数吗？";
	layer.confirm(msg, {
		icon: 3,
		title: '提示'
	}, function(index) {
		twxAjax(THING, 'DeleteCalcResult', {
			ids: ids
		}, true, function(res) {
			layer.close(index)
			if (res.success) {
				renderResultTable();
				layer.msg(res.msg)
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	});
}

/**
 * 删除多条参数
 */
function deleteMoreResult() {
	var checks = table.checkStatus('result-table').data;
	if (checks.length == 0) {
		layer.alert("至少选择一条数据！", {
			icon: 2
		});
	} else {
		var tempArr = [];
		for (var i = 0; i < checks.length; i++) {
			var id = checks[i]["ID"];
			tempArr.push(id);
		}
		var ids = tempArr.join(",");
		deleteResult(ids);
	}
}

/**
 * 修改参数 并修改引用值
 * @param {Object} data
 */
function editResult(data) {
	var remark = data['REMARK'] || '';
	var editTpl =
		'<form class="layui-form" action="" lay-filter="edit-result-form">\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数名称:</label>\
				<div class="layui-input-block">\
					<input type="text" name="name" lay-verify="required" value="' + data['NAME'] + '" autocomplete="off" placeholder="请输入参数名称" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数代号:</label>\
				<div class="layui-input-block">\
					<input type="text" name="code" lay-verify="required|calcResultCode" value="' + data['CODE'] + '" autocomplete="off" placeholder="请输入参数代号" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">费用类型:</label>\
				<div class="layui-input-block">\
					<select name="expense_type" id="expense_type"></select>\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">备注:</label>\
				<div class="layui-input-block">\
					<textarea name="remark" autocomplete="off" placeholder="请输入备注" class="layui-textarea">' + remark + '</textarea>\
				</div>\
			</div>\
			<div class="layui-form-item" style="display:none;">\
				<div class="layui-input-block">\
					<div class="layui-footer">\
						<button class="layui-btn" id="editResultSubmit" lay-submit="" lay-filter="submit-edit-result">确认</button>\
						<button type="reset" id="editResultReset" class="layui-btn layui-btn-primary">重置</button>\
					</div>\
				</div>\
			</div>\
		</form>';
	layer.open({
		title: '修改参数',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: ['500px', '390px'],
		content: '<div id="editTableResultContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '重置', '取消'],
		yes: function() {
			$('#editResultSubmit').click();
		},
		btn2: function() {
			$('#editResultReset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function(layero, userLayerIndex, that) {
			$(layero).find('.layui-layer-content').css("overflow", "visible");
			$("#editTableResultContent").append(editTpl);
			renderExpenseTypeSelect(data['EXPENSE_TYPE']);
		}
	});

	form.on('submit(submit-edit-result)', function(formData) {
		var param = {};
		param.id = data.ID;
		param.name = formData.field.name;
		param.code = formData.field.code;
		param.remark = formData.field.remark;
		param.expenseType = formData.field.expense_type;
		param.updateUser = sessionStorage.getItem('username');
		var loadIndex = layer.load();
		var cb_success = function(res) {
			layer.close(loadIndex);
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				renderResultTable();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function() {
			layer.alert('添加失败，请联系管理员！', {
				icon: 2
			});
		};
		twxAjax(THING, 'UpdateCalcResult', param, true, cb_success, cb_error);
		return false;
	});
}

/**
 * 加载成本费用类型选择框
 * @param {Object} selectValue 选中值
 */
function renderExpenseTypeSelect(selectValue) {
	$("#expense_type").empty().append('<option value=""></option>');
	twxAjax(THING, 'QueryExpenseType', {}, true, function(res) {
		if (res.success) {
			var datas = res.data;
			for (var i = 0; i < datas.length; i++) {
				var d = datas[i];
				var name = d.NAME,
					value = d.KEY;
				if (selectValue == value) {
					$("#expense_type").append('<option selected value="' + value + '">' + name + '</option>');
				} else {
					$("#expense_type").append('<option value="' + value + '">' + name + '</option>');
				}
			}
			form.render("select");
		} else {
			layer.alert(res.msg, {
				icon: 2
			});
		}
	}, function(xhr, textStatus, errorThrown) {
		layer.alert('请求出错！', {
			icon: 2
		});
	});
}

/**
 * 添加参数
 */
function addResult() {
	var addTpl =
		'<form class="layui-form" action="" lay-filter="add-result-form">\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数名称:</label>\
				<div class="layui-input-block">\
					<input type="text" name="name" lay-verify="required" autocomplete="off" placeholder="请输入参数名称" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">参数代号:</label>\
				<div class="layui-input-block">\
					<input type="text" name="code" lay-verify="required|calcResultCode" autocomplete="off" placeholder="请输入参数代号" class="layui-input">\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">费用类型:</label>\
				<div class="layui-input-block">\
					<select name="expense_type" id="expense_type"></select>\
				</div>\
			</div>\
			<div class="layui-form-item">\
				<label class="fieldlabel1 layui-form-label">备注:</label>\
				<div class="layui-input-block">\
					<textarea name="remark" autocomplete="off" placeholder="请输入备注" class="layui-textarea"></textarea>\
				</div>\
			</div>\
			<div class="layui-form-item" style="display:none;">\
				<div class="layui-input-block">\
					<div class="layui-footer">\
						<button class="layui-btn" id="addResultSubmit" lay-submit="" lay-filter="submit-add-result">确认</button>\
						<button type="reset" id="addResultReset" class="layui-btn layui-btn-primary">重置</button>\
					</div>\
				</div>\
			</div>\
		</form>';
	layer.open({
		title: '添加参数',
		type: 1,
		fixed: false,
		maxmin: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		resize: false, //不允许拉伸
		area: ['500px', '390px'],
		content: '<div id="addTableResultContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '重置', '取消'],
		yes: function() {
			$('#addResultSubmit').click();
		},
		btn2: function() {
			$('#addResultReset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function(layero, userLayerIndex, that) {
			$(layero).find('.layui-layer-content').css("overflow", "visible");
			$("#addTableResultContent").append(addTpl);
			renderExpenseTypeSelect("");
		}
	});

	form.on('submit(submit-add-result)', function(formData) {
		var param = {};
		param.pId = currentTreeId;
		param.name = formData.field.name;
		param.code = formData.field.code;
		param.remark = formData.field.remark;
		param.remark = formData.field.remark;
		param.expenseType = formData.field.expense_type;
		var loadIndex = layer.load();
		var cb_success = function(res) {
			layer.close(loadIndex);
			if (res.success) {
				layer.closeAll();
				layer.msg(res.msg);
				renderResultTable();
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function(data) {
			layer.alert('添加失败，请联系管理员！', {
				icon: 2
			});
		};
		twxAjax(THING, 'AddCalcResult', param, true, cb_success, cb_error);
		return false;
	});
}