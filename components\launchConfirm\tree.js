var treeId = 'dpTree';
var ztreeObj;
var curDragNodes, autoExpandNode;
//tree setting
var treeSetting = {
	view: {
		dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
		showLine: true, //是否显示节点之间的连线
		fontCss: {
			'color': 'black'
		}, //字体样式函数
		selectedMulti: false, //设置是否允许同时选中多个节点,
		txtSelectedEnable: true,
		showTitle: true
	},
	async: {
		enable: true,
		url: getTreeUrl(THING, "QueryTreeNodeByPid", ""),
		type: "post",
		autoParam: ["ID"],
		contentType: "application/json;charset=utf-8",
		dataType: 'json',
		dataFilter: function(treeId, parentNode, responseData) {
			if (responseData.success) {
				var datas = responseData.data;
				if (datas.length > 0) {
					datas = dealDataIcons(datas);
					datas = dealDataNodeName(datas);
				}
				return datas;
			} else {
				layer.alert(responseData.msg, {
					icon: 2
				});
			}
		}
	},
	check: {
		enable: false
	},
	edit: {
		enable: true,
		editNameSelectAll: false,
		showRemoveBtn: false,
		showRenameBtn: false,
		removeTitle: "删除",
		renameTitle: "重命名",
		drag: {
			autoExpandTrigger: true,
			prev: dropPrev,
			inner: dropInner,
			next: dropNext
		}
	},
	data: {
		simpleData: { //简单数据模式
			enable: true,
			idKey: "ID",
			pIdKey: "PID",
			rootPId: -1
		},
		key: {
			name: 'TEXT',
			title: '',
			isParent: "ISPARENT"
		}
	},
	callback: {
		// beforeRename: function zTreeBeforeRename(treeId, treeNode, newName, isCancel) {
		// 	return false;
		// },
		beforeDrag: beforeDrag,
		beforeDrop: beforeDrop,
		beforeDragOpen: beforeDragOpen,
		onDrag: onDrag,
		onDrop: onDrop,
		beforeEditName: function(treeId, treeNode) {
			treeNode.NODENAME = getNodeName(treeNode.NODENAME);
			return true;
		},
		// beforeRename:function(){
		// 	return false;
		// },
		// onRename: function(event, treeId, treeNode, isCancel) {
		// 	var parentId = treeNode.PARENTID;
		// 	var nodeName = treeNode.NODENAME;
		// 	twxAjax('Thing.Fn.DataPackage', 'updateNodeName', {
		// 		treeId: treeNode.TREEID,
		// 		newName: nodeName
		// 	}, true, function(data) {
		// 		loadTree();
		// 	});

		// },
		onRemove: function(event, treeId, treeNode) {}
	}
};

function dropPrev(treeId, nodes, targetNode) {
	var pNode = targetNode.getParentNode();
	if (pNode && pNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			var curPNode = curDragNodes[i].getParentNode();
			if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function dropInner(treeId, nodes, targetNode) {
	if (targetNode && targetNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			if (!targetNode && curDragNodes[i].dropRoot === false) {
				return false;
			} else if (curDragNodes[i].parentTId && curDragNodes[i].getParentNode() !== targetNode && curDragNodes[i].getParentNode()
				.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function dropNext(treeId, nodes, targetNode) {
	var pNode = targetNode.getParentNode();
	if (pNode && pNode.dropInner === false) {
		return false;
	} else {
		for (var i = 0, l = curDragNodes.length; i < l; i++) {
			var curPNode = curDragNodes[i].getParentNode();
			if (curPNode && curPNode !== targetNode.getParentNode() && curPNode.childOuter === false) {
				return false;
			}
		}
	}
	return true;
}

function beforeDrag(treeId, treeNodes) {
	for (var i = 0, l = treeNodes.length; i < l; i++) {
		if (treeNodes[i].drag === false) {
			curDragNodes = null;
			return false;
		} else if (treeNodes[i].parentTId && treeNodes[i].getParentNode().childDrag === false) {
			curDragNodes = null;
			return false;
		}
	}
	curDragNodes = treeNodes;
	return true;
}

function beforeDragOpen(treeId, treeNode) {
	autoExpandNode = treeNode;
	return true;
}

function beforeDrop(treeId, treeNodes, targetNode, moveType, isCopy) {
	return true;
}

function onDrag(event, treeId, treeNodes) {}


function onDrop(event, treeId, treeNodes, targetNode, moveType, isCopy) {
	if (targetNode != null) {
		var sourceNodeSort = treeNodes[0].SORT;
		var sourceNodePId = treeNodes[0].PID;
		var sourceNodeId = treeNodes[0].ID;
		var sourceNodeName = treeNodes[0].NAME;

		var targetNodeSort = targetNode.SORT;
		var targetNodeId = targetNode.ID;
		var targetNodeName = targetNode.NAME;
		var type = "上面";
		if (sourceNodeSort < targetNodeSort) {
			type = '下面';
		}
		var parentNode = treeNodes[0].getParentNode();
		var allNode = parentNode.children;
		var arr = [];
		for (var i = 1; i <= allNode.length; i++) {
			arr.push(allNode[i - 1].ID + ":" + i);
		}
		var str = arr.join(",");
		twxAjax(THING, 'UpdateNodeSort', {
			str: str,
			type: postType
		}, true, function(res) {
			if (res.success) {
				reloadTree(sourceNodePId, sourceNodeId);
			} else {
				layer.alert(res.msg);
			}
		}, function(data) {
			layer.alert("拖动失败！");
		});
	}
}


/**
 * 添加节点
 * @param obj
 */
function addZTreeNode(obj) {
	var treeObj = $.fn.zTree.getZTreeObj(treeId);
	var parentZNode = treeObj.getSelectedNodes(); //获取父节点
	var newNode = obj;
	newNode.nodeFlg = 1; // 可以自定义节点标识
	newNode = treeObj.addNodes(parentZNode[0], newNode, true);
}
/**
 * 修改子节点
 * @param obj
 */
function editZTreeNode(obj) {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getSelectedNodes();
	for (var i = 0; i < nodes.length; i++) {
		nodes[i].name = obj;
		zTree.updateNode(nodes[i]);
	}
}

/**
 *  删除子节点 --选中节点
 * @param obj
 */
function removeZTreeNodeBySelect() {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getSelectedNodes(); //获取选中节点
	for (var i = 0; i < nodes.length; i++) {
		zTree.removeNode(nodes[i]);
	}
}

/**
 *  删除子节点 --勾选节点
 * @param obj
 */
function removeZTreeNodeByChecked() {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getCheckedNodes(true); //获取勾选节点
	for (var i = 0; i < nodes.length; i++) {
		zTree.removeNode(nodes[i]);
	}
}

/**
 *  根据节点id 批量删除子节点
 * @param obj
 */
function removeZTreeNodebPi(obj) {
	var idnodes = obj.split(",");
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var nodes = zTree.getSelectedNodes();
	for (var i = 0; i < nodes.length; i++) {
		var nodes = zTree.getNodeByParam("id", nodes[i]);
		zTree.removeNode(nodes);
	}
}
/**
 * 选择节点
 * @param obj
 */
function selectzTreeNode(obj) {
	var zTree = $.fn.zTree.getZTreeObj(treeId);
	var node = zTree.getNodeByParam("id", obj);
	if (node != null) {
		zTree.selectNode(node, true); //指定选中ID的节点
	}
}

/**
 * 对数据进行处理
 * @param {} datas 
 */
function dealDataIcons(datas) {
	var imagePrefix = '../dataTree/';
	for (var i = 0; i < datas.length; i++) {
		var dtype = datas[i].TYPE;
		if (dtype === 'root') {
			datas[i].icon = imagePrefix + "images/root.png";
			datas[i].drag = false;
		} else if (dtype === 'folder') {
			datas[i].icon = imagePrefix + "images/folder.png";
			datas[i].drag = false;
		} else if (dtype === 'model') {
			datas[i].icon = imagePrefix + "images/卫星.png";
			datas[i].drag = false;
			datas[i].childOuter = false;
		} else if (dtype === 'project') {
			datas[i].icon = imagePrefix + "images/dir.png";
			datas[i].childOuter = false;
		} else if (dtype === 'a') {
			datas[i].icon = imagePrefix + "images/A.png";
			datas[i].childOuter = false;
		} else if (dtype === 'b') {
			datas[i].icon = imagePrefix + "images/B.png";
			datas[i].dropInner = false;
		}
	}
	return datas;
}

//处理节点数据名称
function dealDataNodeName(datas) {
	for (var i = 0; i < datas.length; i++) {
		var dtype = datas[i].TYPE;
		datas[i].TEXT = datas[i].NAME;
		var tableNum = datas[i].TABLE_NUM || "";
		if (dtype === 'a' || dtype === 'b') {
			datas[i].TEXT = tableNum + "：" + datas[i].NAME;
		} else if (dtype == 'project') {
			datas[i].TEXT = datas[i].SORT + "-" + datas[i].NAME;
		}
	}
	return datas;
}

//去除显示节点前的序号
function getNodeName(name) {
	if (name.indexOf("-") > -1) {
		var arr = name.split("-");
		var arr2 = [];
		for (var i = 1; i < arr.length; i++) {
			arr2.push(arr[i]);
		}
		return arr2.join("-")
	}
	return name;
}

//获取显示节点前的序号
function getNodeNum(name) {
	if (name.indexOf("-") > -1) {
		var arr = name.split("-");
		return arr[0] + "-";
	}
	return "";
}
