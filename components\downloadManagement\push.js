// 渲染状态表格
function renderStatusTable(elemId, tableHeight) {
    return table.render({
        elem: '#' + elemId,
        height: tableHeight, // 填满layer高度
        cols: [[
            { type: 'numbers', title: '序号', width: 60 }, // 增加序号列
            {
                field: 'PACKAGE_TYPE', title: '数据包类型', width: 100, templet: function (d) {
                    var typeMap = {
                        'YSJL': '原始记录',
                        'QRB': '确认表',
                        'ZMCL': '证明材料',
                        'PHOTO': '照片'
                    };
                    return typeMap[d.PACKAGE_TYPE] || d.PACKAGE_TYPE;
                }
            },
            { field: 'PACKAGE_NAME', title: '数据包名称', width: 350 },
            { field: 'MODEL_NAME', title: '型号', width: 100 },
            { field: 'PHASE_NAME', title: '阶段', width: 90 },
            {
                field: 'STATUS_TEXT',
                title: '状态',
                width: 90,
                templet: function (d) {
                    if (d.COLLECT_STATUS === 'FAILED') {
                        return '<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="showCollectError">' + d.STATUS_TEXT + '</a>';
                    } else if (d.PUSH_STATUS === 'FAILED') {
                        return '<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="showPushError">' + d.STATUS_TEXT + '</a>';
                    }
                    return d.STATUS_TEXT;
                }
            },
            { field: 'ARCHIVE_COUNT', title: '档案数量', width: 90 },
            { field: 'CREATOR', title: '创建人', width: 90 },
            { field: 'CREATE_TIME', title: '创建时间', width: 160 },
            { field: 'COLLECT_END_TIME', title: '收集完成时间', width: 160 },
            { field: 'PUSH_END_TIME', title: '推送完成时间', width: 160 },
            {
                title: '操作',
                width: 90,
                align: 'center',
                templet: function (d) {
                    if (d.COLLECT_STATUS === 'SUCCESS' && d.PUSH_STATUS === 'SUCCESS') {
                        return '<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>';
                    }
                    return '';
                }
            }
        ]],
        data: []
    });
}

// 绑定表格工具栏事件
function bindTableToolEvents(tableFilter, refreshCallback) {
    table.on('tool(' + tableFilter + ')', function (obj) {
        if (obj.event === 'showCollectError') {
            layer.alert(obj.data.COLLECT_MESSAGE || '未知错误');
        } else if (obj.event === 'showPushError') {
            layer.alert(obj.data.PUSH_MESSAGE || '未知错误');
        } else if (obj.event === 'delete') {
            layer.confirm('确定要删除这条记录吗？', { icon: 3, title: '提示' }, function (index) {
                $.ajax({
                    url: fileHandlerUrl + '/archive/deleteByLogId',
                    type: 'POST',
                    data: {
                        logId: obj.data.ID
                    },
                    success: function (res) {
                        if (res.success) {
                            layer.msg('删除成功');
                            // 重新加载表格数据
                            refreshCallback();
                        } else {
                            layer.msg(res.msg || '删除失败');
                        }
                    },
                    error: function () {
                        layer.msg('删除失败，请稍后重试');
                    }
                });
                layer.close(index);
            });
        }
    });
}

// 获取状态数据
function getStatusData(treeId, callback) {
    $.ajax({
        url: fileHandlerUrl + '/archive/status',
        type: 'POST',
        data: {
            phaseTreeId: treeId
        },
        success: function (res) {
            callback(null, res);
        },
        error: function (err) {
            callback(err, null);
        }
    });
}

// 更新表格数据
function updateTableData(tableInstance, data) {
    // 获取当前滚动条位置
    var scrollTop = $('.layui-table-body').scrollTop();

    // 刷新表格数据
    tableInstance.reload({
        data: data || []
    });

    // 恢复滚动条位置
    $('.layui-table-body').scrollTop(scrollTop);
}

// 更新标题状态
function updateTitleStatus(layerIndex, data) {
    if (data && data.length > 0) {
        // 检查是否所有任务都完成了
        var allCompleted = data.every(function (item) {
            return (item.COLLECT_STATUS === 'SUCCESS' || item.COLLECT_STATUS === 'FAILED') &&
                (item.PUSH_STATUS === 'SUCCESS' || item.PUSH_STATUS === 'FAILED' || item.PUSH_STATUS == undefined);
        });

        if (allCompleted) {
            // 所有任务完成，更新标题
            var successCount = data.filter(function (item) {
                return item.COLLECT_STATUS === 'SUCCESS' && item.PUSH_STATUS === 'SUCCESS';
            }).length;

            var titleColor = successCount === data.length ? '#5FB878' : '#FF5722';
            var titleText = '数据包状态 <span style="color: ' + titleColor + '">[已完成 ' +
                successCount + '/' + data.length + ']</span>';

            layer.title(titleText, layerIndex);
            
            return {
                allCompleted: true,
                successCount: successCount,
                totalCount: data.length
            };
        } else {
            // 更新为处理中状态
            layer.title('数据包状态 <span style="color: #FFB800;">[处理中...]</span>', layerIndex);
            return {
                allCompleted: false
            };
        }
    } else {
        // 没有数据
        layer.title('数据包状态 <span style="color: #FF5722">[无数据]</span>', layerIndex);
        return {
            noData: true
        };
    }
}

// 推送数据包
function pushDataPackage(treeId) {
    $.ajax({
        type: "POST",
        url: fileHandlerUrl + "/archive/collect",
        data: {
            phaseTreeId: treeId,
            pushUserName: sessionStorage.getItem('username'),
            pushUserFullname: sessionStorage.getItem('fullname')
        },
        success: function (res) {
            // if (!res.success) {
            // 	layer.msg(res.msg);
            // 	return;
            // }
        },
        error: function () {
            layer.msg("推送失败");
        }
    });
    
    // 显示等待提示框
    var loadingIndex = layer.msg('正在启动推送任务...', {
        icon: 16,
        shade: 0.3,
        time: 1500
    }, function () {
        // 打开状态表格弹窗
        var statusTableIndex = layer.open({
            type: 1,
            title: '数据包状态 <span style="color: #FFB800;">[处理中...]</span>',
            area: ['1600px', '600px'],
            content: '<table id="archive-status-table" lay-filter="archive-status-table"></table>',
            end: function () {
                // 弹框关闭时停止轮询
                if (window.statusTableTimer) {
                    clearTimeout(window.statusTableTimer);
                    window.statusTableTimer = null;
                }
            },
            success: function () {
                // 渲染表格
                var tableIns = renderStatusTable('archive-status-table', 545);
                
                // 绑定工具条事件
                bindTableToolEvents('archive-status-table', refreshStatusTable);
                
                // 定义数据状态变量
                var hasData = false;
                var allCompletedMessageShown = false;
                
                // 定义刷新函数
                function refreshStatusTable() {
                    getStatusData(treeId, function(err, res) {
                        if (err) {
                            // 发生错误也继续刷新
                            window.statusTableTimer = setTimeout(refreshStatusTable, 2000);
                            return;
                        }
                        
                        if (res.code === 0) {
                            // 如果有数据，设置hasData标志
                            if (res.data && res.data.length > 0) {
                                hasData = true;
                            }
                            
                            // 更新表格数据
                            updateTableData(tableIns, res.data);
                            
                            // 更新标题状态
                            var status = updateTitleStatus(statusTableIndex, res.data);
                            
                            // 显示完成消息
                            if (hasData && status.allCompleted && !allCompletedMessageShown) {
                                layer.msg(status.successCount === status.totalCount ? '全部处理成功' : '部分处理失败，请查看详情');
                                allCompletedMessageShown = true;
                            }
                        }
                        
                        // 无论结果如何，始终每2秒刷新一次
                        window.statusTableTimer = setTimeout(refreshStatusTable, 2000);
                    });
                }

                // 开始定时刷新
                refreshStatusTable();
            }
        });
    });
}

// 查看推送状态
function viewPushStatus(treeId) {
    // 打开状态表格弹窗
    var statusTableIndex = layer.open({
        type: 1,
        title: '数据包状态',
        area: ['1600px', '600px'],
        content: '<div class="layui-row" style="padding: 5px 0;"><div class="layui-col-md12" style="text-align: left; padding-left: 20px;"><button class="layui-btn layui-btn-sm layui-btn-normal" id="refresh-status-btn">刷新</button></div></div><table id="archive-status-table" lay-filter="archive-status-table"></table>',
        success: function () {
            // 渲染表格
            var tableIns = renderStatusTable('archive-status-table', 500);
            
            // 绑定工具条事件
            bindTableToolEvents('archive-status-table', refreshTable);
            
            // 定义刷新函数
            function refreshTable() {
                var loadingIndex = layer.load(1, {
                    shade: [0.1, '#fff']
                });
                
                getStatusData(treeId, function(err, res) {
                    layer.close(loadingIndex);
                    
                    if (err) {
                        layer.msg('获取数据失败，请稍后重试');
                        return;
                    }
                    
                    if (res.code === 0) {
                        // 更新表格数据
                        updateTableData(tableIns, res.data);
                        
                        // 更新标题状态
                        updateTitleStatus(statusTableIndex, res.data);
                    } else {
                        layer.msg('获取数据失败');
                    }
                });
            }
            
            // 绑定刷新按钮点击事件
            $('#refresh-status-btn').on('click', function() {
                refreshTable();
            });
            
            // 首次加载数据
            refreshTable();
        }
    });
}
