//登录拦截
var _dcTime = (new Date().getTime());
if (sessionStorage.username) {
	//如果已经存在了则直接跳转到首页面
	if (location.href.indexOf('login.html') > -1) {
		location.href = '/DataPackageManagement/index.html?_dc=' + _dcTime;
	} else {
		var pathname = location.pathname;
		if(pathname!='/DataPackageManagement/index.html'&&(window.top==window.self)){
			pathname = pathname.split('/DataPackageManagement/')[1];
			twxAjax("Thing.Fn.SystemManagement", "getMenuIdByPath", {
				path: pathname
			}, false, function(data) {
				var menuid = data.rows[0].MENU_ID;
				var menuarr = sessionStorage.getItem('menuids').split(',');
				if(!contains(menuarr,menuid)){
					sessionStorage.clear();
					location.href = '/DataPackageManagement/login.html?_dc=' + _dcTime;
				}else{
					location.href = '/DataPackageManagement/index.html?_dc=' + _dcTime;
				}
			});
		}
	}
} else {
	if (location.href.indexOf('login.html') == -1) {
		location.href = '/DataPackageManagement/login.html?_dc=' + _dcTime;
		//如果需要跳转到登录页面则把sessionStorage中的变量清空
		sessionStorage.clear();
	}
}