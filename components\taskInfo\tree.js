function getTreeData(taskInfo) {
	//组建tree数据
	var treeData = taskInfo.collectionDataInfo;
	treeData.title = treeData.DATA_PACKET_NAME;
	treeData.id = treeData.DATA_PACKET_ID;
	treeData.spread = true;
	treeData.type = 'package';
	var dataPacketListInfo = treeData.dataPacketListInfo;
	for (var i = 0; i < dataPacketListInfo.length; i++) {
		var listInfo = dataPacketListInfo[i];
		listInfo.title = '[' + listInfo.LIST_SUBMIT_OR_DELIVER + ']' + listInfo.LIST_NAME + '(' + listInfo.LIST_PROJECT_CATEGORY + ')';
		listInfo.id = listInfo.LIST_ID;
		listInfo.type = 'list';
		listInfo.NODE_CREATE_USER = treeData.NODE_CREATE_USER;
	}
	return [treeData];
}

function renderTree(taskInfo) {
	var treeData = getTreeData(taskInfo);
	// 渲染
	tree.render({
		elem: '#tree',
		data: treeData,
		showLine: true,
		showCheckbox: false, // 是否显示复选框
		onlyIconControl: true, // 是否仅允许节点左侧图标控制展开收缩
		id: 'tree',
		isJump: false, // 是否允许点击节点时弹出新窗口跳转
		click: function(obj) {
			var data = obj.data; //获取当前点击的节点数据
			$('.layui-tree-entry').css('background-color', '');
			$(obj.elem[0]).children('.layui-tree-entry').css('background-color', 'aliceblue');
			clickTree(data);
		},
		customName: {
			id: 'id',
			title: 'title',
			children: 'dataPacketListInfo'
		},
	});
}

function clickTree(node) {
	$("#list-body").empty();
	if (node.type == 'package') {
		$("#list-header").html("型号节点信息");
		var forms = [{
			name: "型号类型",
			value: node.MODEL_TYPE
		}, {
			name: "型号领域",
			value: node.MODEL_FIELD
		}, {
			name: "所属系列",
			value: node.BELONG_SERIES
		}, {
			name: "型号名称",
			value: node.MODEL_NAME
		}, {
			name: "型号代号",
			value: node.MODEL_CODE
		}, {
			name: "抓总单位",
			value: node.GRASPING_UNIT
		}, {
			name: "产品状态",
			value: node.PRODUCT_STATUS
		}, {
			name: "所属系统",
			value: node.XI_TONG
		}, {
			name: "所属分系统",
			value: node.FEN_XI_TONG
		}, {
			name: "所属单机",
			value: node.DAN_JI
		}, {
			name: "节点创建人",
			value: node.NODE_CREATE_USER
		}, {
			name: "节点负责人",
			value: node.NODE_OWNER
		}, {
			name: "数据包名称",
			value: node.DATA_PACKET_NAME
		}, {
			name: "数据包状态",
			value: node.PACKET_STATUS
		}];
		$("#list-body").append(get$Form(forms));
	} else {
		$("#list-header").html("项目清单详细内容");
		var listId = node.id;
		// renderList(node);
		var loadIndex = layer.load(0);
		twxAjax(THING, "QueryListInfo", {
			taskId: taskId,
			listId: listId
		}, true, function(res) {
			if (res.success) {
				layer.close(loadIndex);
				res.data.NODE_CREATE_USER = node.NODE_CREATE_USER;
				renderList(res.data);
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		});
	}
}

function renderList(node) {
	var forms = [{
		name: "项目清单名称",
		value: node.LIST_NAME
	}, {
		name: "项目清单类型",
		value: node.LIST_PROJECT_CATEGORY
	}, {
		name: "要求交付时间",
		value: node.REQUIRED_DELIVER_TIME
	}, {
		name: "发起人",
		value: node.NODE_CREATE_USER
	}, {
		name: "下发时间",
		value: node.DISTRIBUTE_TASK_TIME
	}, {
		name: "提交时间",
		id: "submitTime",
		value: node.TABLE_SUBMIT_TIME
	}, {
		name: "项目清单状态",
		value: node.LIST_STATUS
	}];
	$("#list-body").append(get$Form(forms));
	var nodeNames = node.LIST_COLLECT_NODE_NAME.split(",");
	var nodeCodes = node.LIST_COLLECT_NODE.split(",");
	var $tab = $('<div class="layui-tab layui-tab-brief"></div>');
	var $ul = $('<ul class="layui-tab-title"></ul>');
	var $content = $('<div class="layui-tab-content"></div>');

	for (var i = 0; i < nodeNames.length; i++) {
		var ulClass = "",
			contentClass = "";
		if (i == 0) {
			ulClass = 'layui-this', contentClass = 'layui-show';
		}
		$ul.append('<li class="' + ulClass + '" style="font-weight: bold;">' + nodeNames[i] + '</li>');
		$content.append('<div class="layui-tab-item ' + contentClass + '"> <table class="layui-hide" lay-filter="' + nodeCodes[i] + '" id="' + nodeCodes[i] + '"></table></div>');
	}
	$tab.append($ul).append($content);
	$("#list-body").append($tab);

	var nodeType = node.LIST_TEMPLATE_TYPE;
	//加载表格
	var tableCols;
	var btns = [];
	if (nodeType == 'tableTemplate') {
		//表格
		var titles = JSON.parse(node.TABLE_TITLE_INFO);
		tableCols = getCols(titles, "text");
		btns = [{
			name: '新增',
			event: "addRow",
			icon: 'add-1'
		}, {
			name: '删除',
			event: "deleteRow",
			icon: 'delete'
		}, {
			name: '保存',
			event: "saveTable",
			icon: 'release'
		}, {
			name: '导出',
			event: "exportTable",
			icon: 'export'
		}, {
			name: '导入',
			event: "importTable",
			icon: 'upload-circle'
		}];
	} else if (nodeType == 'eleEntityDoc') {
		//文件
		var titles = JSON.parse(node.ELE_ENTITY_DOC_INFO);
		tableCols = getCols(titles, false);
		btns = [{
			name: '上传',
			event: "addFile",
			icon: 'add-1'
		}, {
			name: '删除',
			event: "deleteRow",
			icon: 'delete'
		}, {
			name: '保存',
			event: "saveTable",
			icon: 'release'
		}];
	}
	var tableData = JSON.parse(node.TABLE_DATA || '{}');
	for (var i = 0; i < nodeCodes.length; i++) {
		var thisTableData = JSON.parse(tableData[nodeCodes[i]] || '[]');
		renderTable(nodeCodes[i], nodeNames[i], thisTableData, tableCols, btns, node);
	}
}