<head>
    <meta http-equiv="content-type" content="txt/html; charset=utf-8" />
    <link rel="stylesheet" href="../../plugins/layui/css/layui.css" media="all">
    <link href="../../plugins/InsdepUI/insdep.easyui.min.css" rel="stylesheet" type="text/css">

    <link href="../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
    <link href="../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css">


    <script src="../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../plugins/InsdepUI/jquery.easyui.min.js"></script>
    <script src="../../plugins/InsdepUI/insdep.extend.min.js"></script>
    <script src="../../plugins/layui/layui.js"></script>
    <script src="../js/config/twxconfig.js"></script>
    <script src="../js/util.js"></script>

    <script type="text/javascript" src="../js/logUtil.js"></script>
</head>

<style>
    .layui-form-label{
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
        min-height: 36px
    }
</style>
<body style="padding: 15px">
<form class="layui-form layui-form-pane" action="">

    <div class="layui-form-item">
        <label class="layui-form-label">型号产品名称</label>
        <div class="layui-input-block" >
            <select name="model" lay-filter="model" lay-verify="required" id="model" >
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">数据包名称</label>
        <div class="layui-input-block">
            <select name="phase" lay-filter="phase" lay-verify="required" id="phase" >
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">清单类别</label>
        <div class="layui-input-block">
            <select name="subject" lay-filter="subject" lay-verify="required" id="subject" >
                <option value=""></option>
                <option value="DESIGN_DATA_RESULT">设计</option>
                <option value="CRAFT_DATA_RESULT">工艺</option>
                <option value="PROCESS_CONTROL_RESULT">过程控制</option>
                <option value="QUALITY_CONTROL_RESULT">质量控制</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">文件类型</label>
        <div class="layui-input-block">
            <select name="process" lay-filter="process" lay-verify="required" id="process">
                <option value=""></option>
            </select>
        </div>
    </div>


    <div class="layui-form-item" style="display: none;">
        <center>
            <!--<button class="layui-btn" lay-submit lay-filter="addData">提交</button>-->
            <!--<button class="layui-btn layui-btn-primary" type="reset">重置</button>-->
            <button id="btn_add" class="layui-btn" lay-submit lay-filter="addData" >提交</button>
            <button id="btn_reset" class="layui-btn layui-btn-primary" type="reset" >重置</button>
        </center>
    </div>
</form>



</body>

<script>
    layui.config({
        base: '/DataPackageManagement/build/js/' //假设这是你存放拓展模块的根目录
    }).use(['form','laydate','table','utils','layer'], function () {
        var form = layui.form;
        var laydate = layui.laydate;
        var table = layui.table;
        var utils = layui.utils;
        var layer = layui.layer;
        laydate.render({
            elem: '#updateTime', //或 elem: document.getElementById('test')、elem: lay('#test') 等
            position:'abolute',
            showBottom:false,
            trigger:'click'
        });

        //监听提交
        form.on('submit(addData)', function (data) {
            //layer.msg(JSON.stringify(data.field));
            layer.msg(JSON.stringify(data.field));
            var param = data.field;
            var tableName = parent.selectedData.tabName;
            var type = "";
            if(tableName === 'design_list_table'){
                type = 'DESIGN_DATA_RESULT';
            }else if(tableName === 'craft_list_table'){
                type = 'CRAFT_DATA_RESULT';
            }else if(tableName === 'processcontrol_list_table'){
                type = 'PROCESS_CONTROL_RESULT';
            }else if(tableName === 'quanlitycontrol_list_table'){
                type = 'QUALITY_CONTROL_RESULT';
            }
            param.source_type = type;
            param.type = document.getElementById("subject").value;
            param.ID = parent.selectedData.data.ID;
            param.NODECODE = document.getElementById("phase").value;
            param.FILE_TYPE = document.getElementById("process").value;

            var cb_success = function(data){
                layer.alert("关联成功",{icon:1},function(index){
                    //提示完成后，点击确定再刷新界面
                    parent.reloadTable(tableName);

                    logRecord('关联','关联数据包');

                    var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                    parent.layer.close(index); //再执行关闭
                });
            };

            var cb_error = function(xhr){
                layer.alert('关联失败!',{icon:2});
            };
            //同步新增
            twxAjax("publishMissionThing","setNodeToResultTable",param,false,cb_success,cb_error);
            return false;
        });

    });


    layui.use(['form'], function() {


        var form=layui.form;

        form.render('select');

        form.on('select(model)', function(d){
            twxAjax("publishMissionThing","getDataPackageByProductModel",{ model:document.getElementById("model").value},false,function (data) {
                $("#phase").empty();
                $("#phase").append('<option value="">请选择</option>');
                for (var i = 0;i<data.rows.length;i++){
                    $("#phase").append('<option value="'+data.rows[i].ID+'">'+data.rows[i].CODE+'</option>');
                    form.render();
                }
            });
        });

        // form.on('select(subject)', function(d){
        //     twxAjax("publishMissionThing","getTreeNodeByTypeAndParentID",{type:"dir",parentID:document.getElementById("phase").value},false,function (data) {
        //         for (var i = 0;i<data.rows.length;i++){
        //             $("#subject").append('<option value="'+data.rows[i].TREEID+'">'+data.rows[i].NODENAME+'</option>');
        //             form.render();
        //         }
        //     });
        // });
        //
        form.on('select(phase)', function(d){
            twxAjax("publishMissionThing","getDPFileType",{dpID:document.getElementById("phase").value},false,function (data) {
                $("#process").empty();
                $("#process").append('<option value="">请选择</option>');
                for (var i = 0;i<data.rows.length;i++){
                    $("#process").append('<option value="'+data.rows[i].FTYPE+'">'+data.rows[i].FTYPE+'</option>');
                    form.render();
                }
            });
        });


    });


    $(function () {

        twxAjax("publishMissionThing","getTreeNodeByTypeAndParentID",{type:"product"},false,function (data) {
            for (var i = 0;i<data.rows.length;i++){
                $("#model").append('<option value="'+data.rows[i].TREEID+'">'+data.rows[i].NODENAME+'</option>');
            }
        });



    });


</script>