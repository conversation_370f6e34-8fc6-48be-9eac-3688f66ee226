<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="Shortcut Icon" href="../../img/favicon.ico">
	<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" media="all">
	<link rel="stylesheet" href="../../css/icon.css">

	<script src="../../plugins/layui-lasted/layui.js"></script>

	<link rel="stylesheet" href="../../plugins/easyui/themes/gray/easyui.css">
	<link rel="stylesheet" href="../../css/second-table.css">
	<script src="../../plugins/easyui/jquery.min.js"></script>
	<script src="../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>

	<script src="../js/config/twxconfig.js"></script>
	<script src="../js/util.js"></script>

	<!-- <script type="text/javascript" src="../js/intercept.js"></script> -->
	<script type="text/javascript" src="../js/logUtil.js"></script>
	<style>
		.fieldlabel {
			padding-left: 10px;
		}
	</style>
	<title>数据查询</title>
</head>
<body>
	<div id="root_layout" class="easyui-layout original-table" style="width:100%;height:100%;" data-options="fit:true">
		<div id="tb">
			<form id="condForm">
				<table style="margin:10px 10px;font-family: 微软雅黑;font-size: 14px;">
					<tr height="38px" id="paramTr">
						<td>
							<input id="dataType" style="width:229px;">
						</td>
						<!-- <td class="fieldlabel" align="right">型号：</td> -->
						<!-- <td>
							<input class="easyui-textbox" id="dataModel" style="width:120px;">
						</td>
						<td class="fieldlabel" align="right">名称：</td>
						<td>
							<input class="easyui-textbox" id="dataName" style="width:120px;">
						</td> -->
					</tr>
					<tr height="38px">
						<td>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-normal " style=""
								onclick="searchTable()">
								<i class="layui-icon">&#xe615;</i> 搜索
							</button>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-danger "
								style="margin-left: 10px;" onclick="resetForm()">
								<i class="layui-icon">&#xe639;</i> 清除
							</button>
							<button type="button" class="layui-btn layui-btn-sm  layui-btn-warm"
								style="margin-left: 10px;" onclick="exportExcel()">
								<i class="layui-icon">&#xe601;</i> 导出
							</button>
							<button type="button" id="customExcel" class="layui-btn layui-btn-sm  layui-btn-warm"
								style="margin-left: 10px;" onclick="exportCustomExcel()">
								<i class="layui-icon">&#xe601;</i> 自定义导出
							</button>
						</td>
					</tr>
				</table>
			</form>
		</div>
		<div id="searchDataTable"></div>
	</div>
</body>
<script src="../../plugins/index/jquery.fileDownload.js"></script>
<script src="dataSearchTable.js"></script>
<script src="tableSearch.js"></script>

<script type="text/html" id="uploadHtml">
	<form class="layui-form" lay-filter="uploadForm">
		<div class="layui-form-item">
			<label class="param-lable layui-form-label" style="width: 120px;">模板下载:</label>
			<div class="layui-input-block">
				<button type="button" class="layui-btn layui-btn-normal" id="downloadTpl">点击下载</button>
			</div>
		</div>
		<div class="layui-form-item" id="curveDiv" style="display:none;">
			<label class="param-lable layui-form-label" style="width: 120px;">是否导出曲线:</label>
			<div class="layui-input-block">
				<input type="checkbox" name="isCurve" id="isCurve" title="是|否" lay-skin="switch" lay-filter="isCurve" checked>
			</div>
		</div>
		<div class="layui-form-item" id="relatedDataDiv" style="display:none;">
			<label class="param-lable layui-form-label" style="width: 120px;">是否关联数据:</label>
			<div class="layui-input-block">
				<input type="checkbox" name="isRelatedData" id="isRelatedData" title="是|否" lay-skin="switch">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="param-lable layui-form-label" style="width: 120px;">文件内容:</label>
			<div class="layui-input-block">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal" id="uploadChoice">选择文件</button>
					<button type="button" class="layui-btn" id="uploadStart" style="display: none;">开始上传</button>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="selectedFile" style="display: none;">
			<label class="param-lable layui-form-label" style="width: 120px;">已选文件:</label>
			<div class="layui-input-block">
				<div class="layui-form-mid layui-word-aux" style="width: max-content" id="selectedFileName"></div>
			</div>
		</div>
		<div class="layui-form-item" style="display:none;">
			<center>
				<button id="btn_ok" class="layui-btn" lay-submit lay-filter="excelExport">确认</button>
				<button id="btn_cancel" class="layui-btn">取消</button>
			</center>
		</div>
	</form>
</script>