<head>
	<meta http-equiv="content-type" content="txt/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<link rel="stylesheet" href="../../../plugins/layui/css/layui.css">


	<!-- <script src="../../../plugins/InsdepUI/jquery.min.js"></script>
    <script src="../../../plugins/InsdepUI/jquery.easyui.min.js"></script> -->
	<!-- <script src="../../../plugins/InsdepUI/insdep.extend.min.js"></script> -->
	<script src="../../../plugins/layui/layui.js"></script>
	<link rel="stylesheet" href="../../../plugins/easyui/themes/gray/easyui.css">
	<link rel="stylesheet" href="../../../css/icon.css">

	<!-- <link href="../../../plugins/InsdepUI/icon.css" rel="stylesheet" type="text/css">
	<link href="../../../plugins/InsdepUI/iconfont/iconfont.css" rel="stylesheet" type="text/css"> -->
	<link rel="stylesheet" type="text/css" href="../../../plugins/font-awesome-4.7.0/css/font-awesome.min.css">

	<script src="../../../plugins/easyui/jquery.min.js"></script>
	<script src="../../../plugins/easyui/jquery.easyui.min.js"></script>
	<script src="../../../plugins/easyui/locale/easyui-lang-zh_CN.js"></script>
	<script src="../../js/config/twxconfig.js"></script>
	<script src="../../js/util.js"></script>

	<script type="text/javascript" src="../../js/logUtil.js"></script>
	<script src="../../js/intercept.js"></script>
	<title>数据字典</title>
</head>

<body>
	<div id="root_layout" class="easyui-layout" style="width:100%;height:100%;" data-options="fit:true">
		<div data-options="region:'west',split:true,collapsible:false" title="数据字典" style="width:400px;">
			<div id="dictionaryTable_tb" style="padding: 5px;">
				 <button type="button"  class="layui-btn layui-btn-sm" id="dic_add">
				   <i class="layui-icon">&#xe608;</i> 添加
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm  layui-btn-warm" id="dic_edit">
				   <i class="layui-icon">&#xe642;</i> 编辑
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm layui-btn-danger" id="dic_del">
				   <i class="layui-icon">&#xe640;</i> 删除
				 </button>
			</div>
			<table id="dictionaryTable" data-options="border:false"></table>
		</div>
		<div data-options="region:'center'" title="数据列表">
			<div id="dataTable_tb" style="padding: 5px;">
				 <button type="button"  class="layui-btn layui-btn-sm" id="data_add">
				   <i class="layui-icon">&#xe608;</i> 添加
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm  layui-btn-warm" id="data_edit">
				   <i class="layui-icon">&#xe642;</i> 编辑
				 </button>
				 <button type="button"  class="layui-btn layui-btn-sm layui-btn-danger" id="data_del">
				   <i class="layui-icon">&#xe640;</i> 删除
				 </button>
			</div>
			<table id="dataTable" data-options="border:false"></table>
		</div>
	</div>
</body>
<script src="basicConfig.js"></script>

<script type="text/html" id="addDictionaryHtml">
	<form class="layui-form" lay-filter="addDictionaryForm">
	    <div class="layui-form-item">
	        <label class="layui-form-label">名称:</label>
	        <div class="layui-input-block" >
				<input type="text" name="NAME" id="dictionaryName" oldname = ""  lay-verify="required|dictionaryNameRepeat" required autocomplete="off" placeholder="请输入名称" class="layui-input">
	        </div>
	    </div>
	    <div class="layui-form-item">
	        <label class="layui-form-label">描述:</label>
	        <div class="layui-input-block">
				<textarea placeholder="请输入描述" name="REMARK" oldremark = "" style="resize:none" class="layui-textarea"></textarea>
	        </div>
	    </div>
	    <div class="layui-form-item" style="display:none;">
	        <center>
				<button id="btn_dic_submit" class="layui-btn" lay-submit lay-filter="addDictionarySubmit">提交</button>
				<button id="btn_dic_update" class="layui-btn" lay-submit lay-filter="editDictionarySubmit">保存</button>
	            <button id="btn_dic_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
	        </center>
	    </div>
	</form>
</script>

<script type="text/html" id="addDataHtml">
	<form class="layui-form" lay-filter="addDataForm">
	    <div class="layui-form-item">
	        <label class="layui-form-label">名称:</label>
	        <div class="layui-input-block" >
				<input type="text" name="NAME" id="dataName"  oldname = ""  lay-verify="required|dictionaryDataNameRepeat"  lay-verify="required" required autocomplete="off" placeholder="请输入名称" class="layui-input">
	        </div>
	    </div>
		<div class="layui-form-item">
		    <label class="layui-form-label">值:</label>
		    <div class="layui-input-block">
				<input placeholder="请输入KEY" name="KEY"  class="layui-input"></input>
		    </div>
		</div>
	    <div class="layui-form-item">
	        <label class="layui-form-label">描述:</label>
	        <div class="layui-input-block">
				<textarea placeholder="请输入描述" name="REMARK" style="resize:none" class="layui-textarea"></textarea>
	        </div>
	    </div>
	    <div class="layui-form-item" style="display:none;">
	        <center>
				<button id="btn_dicdata_submit" class="layui-btn" lay-submit lay-filter="addDataSubmit">提交</button>
				<button id="btn_dicdata_update" class="layui-btn" lay-submit lay-filter="editDataSubmit">保存</button>
	            <button id="btn_dicdata_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>
	        </center>
	    </div>
	</form>
</script>
