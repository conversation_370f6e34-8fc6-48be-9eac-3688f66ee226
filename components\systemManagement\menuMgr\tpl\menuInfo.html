<form id="menuform" class="layui-form" lay-filter="menuinfo" style="margin:10px">
    <div class="layui-form-item" style="display: none;">
        <div class="layui-input-block">
            <input type="text" name="menuid" value="" >
            <input type="text" name="menuparent" value="" >
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label"><font color=red>*</font>名称</label>
            <div class="layui-input-inline">
                <input type="text" name="menuname" placeholder="请输入名称" lay-reqtext="菜单名称为必填项" lay-verify="required" autocomplete="off" class="layui-input"/>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">图标名称</label>
            <div class="layui-input-inline">
                <input type="text" name="menuicon" placeholder="请填写图标样式名称" autocomplete="off" class="layui-input"/>
            </div>
        </div>
        <div class="layui-inline" id="previewicon">
            
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">路径</label>
            <div class="layui-input-inline">
                <input type="text" name="menupath" placeholder="请填写Menu路径" lay-verify="menupath" autocomplete="off" class="layui-input"/>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否启用</label>
        <div class="layui-input-block">
            <input type="checkbox" checked="" name="menuenable" lay-skin="switch" lay-text="是|否">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">是否默认</label>
        <div class="layui-input-block">
            <input type="checkbox" name="menudefault" lay-skin="switch" lay-text="是|否">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">类型</label>
        <div class="layui-input-inline">
            <select name="menutype" lay-verify="required">
                <option value="Virtual">虚拟组</option>
                <option value="Menu">菜单</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">功能ID</label>
        <div class="layui-input-inline">
            <input type="text" name="funcid" placeholder="功能ID" autocomplete="off" class="layui-input"/>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">说明</label>
        <div class="layui-input-inline">
            <textarea name="menudesc" class="layui-textarea" placeholder="菜单说明"></textarea>
        </div>
    </div>
    <div class="layui-form-item"  style="display: none">
        <div class="layui-input-block">
            <!-- 隐藏提交按钮，在父层中调用 -->
            <button id="btn_submit" class="layui-btn" lay-filter="formVerify" lay-submit style="display: none"></button>
            <button id="btn_update" class="layui-btn" lay-filter="updateVerify" lay-submit style="display: none"></button>
            <button id="btn_reset" type="reset" class="layui-btn" style="display: none"></button>
        </div>
    </div>
</form>