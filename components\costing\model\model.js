var $, form, layer, util, table, dropdown;
var currentTreeId; //当前选中的树节点
layui.use(['form', 'util', 'table', 'laydate'], function() {
	$ = layui.$;
	form = layui.form;
	layer = layui.layer;
	util = layui.util;
	table = layui.table;
	formVerify();
	loadTree();
});

/**
 * 自定义表单验证规则
 */
function formVerify() {
	form.verify({
		calcParamCode: function(value, item) {
			if (!new RegExp(/^[a-zA-Z]+$/).test(value)) {
				return '代号只允许输入字母！';
			}
		},
		calcResultCode: function(value, item) {
			if (!new RegExp(/^[a-zA-Z]+$/).test(value)) {
				return '代号只允许输入字母！';
			}
		}
	});
}

/**
 * 同时加载输入和输出参数的表格
 */
function renderTable() {
	renderParamTable();
	// renderResultTable();
}

/**
 * 获取当前计算模型的输入和输出参数集合
 */
function getCurrentParamAndResultData() {
	return getCurrentParamData().concat(getCurrentResultData());
}