$(function () {
	loadTree();
});
//加载树结构
function loadTree() {
	var cb_success = function (res) {
		var datas = res.data;
		if (datas.length > 0) {
			datas = dealDataIcons(datas);
			datas = dealDataNodeName(datas);
			treeSetting.callback.onClick = function (event, treeId, treeNode) {
				// loadTable(treeNode.TREEID);
				var selectedTab = $('#root_layout_tabs').tabs('getSelected');
				var tabName = selectedTab.panel('options').name;
				excelImportBtnIsShow(treeNode, tabName);
				// reloadTable(tabId);

				reloadRefDPTable(treeNode.TREEID);

				//型号权限233
				if (treeNode.userEditable) {
					$("#design_list_table_tb").show();
					$("#craft_list_table_tb").show();
					$("#processcontrol_list_table_tb").show();
					$("#quanlitycontrol_list_table_tb").show();
					if (treeNode.NODETYPE == 'leaf' && treeNode.NODENAME.indexOf('低频电缆网') > -1) {
						$("#processcontrol_list_table_syncCableReport").removeClass('layui-hide');
					} else {
						$("#processcontrol_list_table_syncCableReport").addClass('layui-hide');
					}

					if (treeNode.NODETYPE == 'leaf' && treeNode.NODENAME.indexOf('热管') > -1) {
						$("#processcontrol_list_table_syncRgPhoto").removeClass('layui-hide');
					} else {
						$("#processcontrol_list_table_syncRgPhoto").addClass('layui-hide');
					}

				} else {
					$("#design_list_table_tb").hide();
					$("#craft_list_table_tb").hide();
					$("#processcontrol_list_table_tb").hide();
					$("#quanlitycontrol_list_table_tb").hide();
				}
			};
			treeSetting.callback.onExpand = function (event, treeId, treeNode) {
				loadTreeEditSetting();
			};
			//禁止拖拽
			treeSetting.callback.beforeDrag = function () {
				return false;
			};
			treeSetting.callback.beforeDrop = function () {
				return false;
			};

			ztreeObj = $.fn.zTree.init($("#dpTree"), treeSetting, datas);
			var nodes = ztreeObj.getNodes();
			for (var i = 0; i < nodes.length; i++) { //设置节点展开
				ztreeObj.expandNode(nodes[i], true, false, true);
			}
			loadTreeEditSetting();
		}
	};
	//使用ajax进行异步加载Tree
	twxAjax('Thing.Fn.ProcessTree', 'QueryTreeRoot', {
		username: sessionStorage.getItem('username')
	}, true, cb_success);
}

//加载树节点右键菜单
function loadTreeEditSetting() {
	var editingMap = {};
	$("#dpTree a").each(function (i, n) {
		var menu = [];
		var node = ztreeObj.getNodeByTId($(n).parent().attr("id"));
		if (isAdmin()) {
			//管理员具有所有权限
			editingMap[node.TREEID] = true;
			node.userEditable = true;
			return;
		}
		//1层级是根（产品数据包），2层级是型号，这层级不应该继承上层级的是否可编辑属性
		if (node.NODETYPE == 'root') {
			editingMap[node.TREEID] = false;
			node.userEditable = false;
		} else if (node.NODETYPE == "product") {
			if (!node.CODE) {
				editingMap[node.TREEID] = true;
				node.userEditable = true;
			} else if (!sessionStorage.modelList) {
				editingMap[node.TREEID] = false;
				node.userEditable = false;
			} else {
				var arrModel = sessionStorage.modelList.split(',');
				var b = contains(arrModel, node.CODE);
				editingMap[node.TREEID] = b;
				node.userEditable = b;
			}
		} else {
			editingMap[node.TREEID] = editingMap[node.PARENTID];
			node.userEditable = editingMap[node.PARENTID];
		}
	});
}

function isAdmin() {
	var roles = sessionStorage.rolename ? sessionStorage.rolename.split(',') : [];
	for (var i = 0; i < roles.length; i++) {
		if (roles[i] == "质量分管领导") {
			return true;
		}
	}
	return false;
}

//去除显示节点前的序号
function getNodeName(name) {
	if (name.indexOf("-") > -1) {
		var arr = name.split("-");
		var arr2 = [];
		for (var i = 1; i < arr.length; i++) {
			arr2.push(arr[i]);
		}
		return arr2.join("-")
	}
	return name;
}