//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var selectedTabName = "design_list_table";
//四个类型表格是否加载
var tableLoadFlag = {
	'design_list_table': false,
	'craft_list_table': false,
	'processcontrol_list_table': false,
	'quanlitycontrol_list_table': false
};
//excel导入数据字典
var excelImportData = [];
//当前excel导入数据
var currentExcelImportData = {
	typeName: '',
	type: '',
	ftype: ''
};
var queryType = "process";
$(document).ready(function () {

	$("#root_layout").layout("panel", "center").panel({
		onResize: function (width, height) {
			//2是边框宽度
			var gridWidth = width - 2;
			var gridId = selectedTabName;
			try {
				$("#" + gridId).datagrid('resize', {
					width: gridWidth
				});
				changeWidth(gridId);
			} catch (e) {

			}
		}
	});

	layui.config({
		base: '../../plugins/layui/define/'
	}).extend({
		layuiTableColumnSelect: 'table-select/js/layui-table-select'
	}).use(['layer', 'form', 'upload', 'table', 'layuiTableColumnSelect'], function () {
		var gridids = ['design_list_table', 'craft_list_table', 'processcontrol_list_table',
			'quanlitycontrol_list_table'
		];
		// for (var i = 0; i < gridids.length; i++) {
		// 	var tableName = gridids[0];
		// 	loadTableData(tableName, []);

		// 	//需要初始化按钮
		// 	initBtns(tableName, layui);
		// }
		var tableName = gridids[0];
		loadTableData(tableName, []);
		initBtns(tableName, layui);

		//请求excel导入数据字典
		twxAjax("Thing.Fn.SystemDic", "getExcelImportData", '', false, function (data) {
			excelImportData = data.rows;
		});

		//绑定事件
		$('#root_layout_tabs').tabs({
			onSelect: function (title, index) {
				var tab = $('#root_layout_tabs').tabs('getTab', index);
				var tabName = tab.panel('options').name;
				queryType = tab.panel('options').queryType;
				selectedTabName = tabName;
				pageOptions.pageNumber = 1;
				if (!tableLoadFlag[tabName]) {
					loadTableData(tabName, []);
					initBtns(tabName, layui);
				}
				reloadTable(tabName);
				var selNodes = ztreeObj.getSelectedNodes();
				excelImportBtnIsShow(selNodes[0], tabName);

				//型号权限233
				if ((!!selNodes && selNodes.length > 0) && selNodes[0].userEditable) {
					$("#design_list_table_tb").show();
					$("#craft_list_table_tb").show();
					$("#processcontrol_list_table_tb").show();
					$("#quanlitycontrol_list_table_tb").show();
				} else {
					$("#design_list_table_tb").hide();
					$("#craft_list_table_tb").hide();
					$("#processcontrol_list_table_tb").hide();
					$("#quanlitycontrol_list_table_tb").hide();
				}
			}
		});
		//型号权限233
		$("#design_list_table_tb").hide();
		$("#craft_list_table_tb").hide();
		$("#processcontrol_list_table_tb").hide();
		$("#quanlitycontrol_list_table_tb").hide();
	});
});

//判断该节点下的该类别有无excel导入按钮
var excelImportBtnIsShow = function (treeNode, tabName) {
	if (treeNode == undefined) {
		return;
	}
	var typeName = '';
	if (tabName === 'design_list_table') {
		typeName = '设计类';
	} else if (tabName === 'craft_list_table') {
		typeName = '工艺类';
	} else if (tabName === 'processcontrol_list_table') {
		typeName = '过程控制';
	} else if (tabName === 'quanlitycontrol_list_table') {
		typeName = '质量综合';
	}
	var isShow = false;

	var major = '';
	var process = '';
	if (treeNode.NODETYPE == 'dir') {
		major = getNodeName(treeNode.NODENAME);
	} else if (treeNode.NODETYPE == 'leaf') {
		major = getNodeName(treeNode.getParentNode().NODENAME);
		process = getNodeName(treeNode.NODENAME);
	} else {
		isShow = false;
	}
	for (var i = 0; i < excelImportData.length; i++) {
		var row = excelImportData[i];
		var name = row.NAME;
		var key = row.KEY;
		var remark = eval(row.REMARK);
		if (remark == undefined) {
			continue;
		}
		for (var j = 0; j < remark.length; j++) {
			var r_major = remark[j].major;
			var r_process = remark[j].process;
			var r_type = remark[j].type;
			var r_fType = remark[j].fType;
			if (major == r_major) {
				if (process == r_process || r_process.indexOf('|' + process) > -1 || r_process.indexOf(process +
					'|') > -1) {
					if (typeName == r_type) {
						isShow = true;
						currentExcelImportData.type = key;
						currentExcelImportData.typeName = name;
						currentExcelImportData.ftype = r_fType;
					}
				}
			}
		}
	};
	var $importBtn = $("#" + tabName + "_excelimport");
	if (isShow) {
		if ($importBtn.hasClass('layui-hide')) {
			$importBtn.removeClass('layui-hide')
		}
	} else {
		if (!$importBtn.hasClass('layui-hide')) {
			$importBtn.addClass('layui-hide')
		}
		currentExcelImportData = {
			typeName: '',
			type: '',
			ftype: ''
		};
	}
}

//初始化按钮的函数
var initBtns = function (tableName, layui) {
	initUploadBtn(tableName, layui);
	initParamBtn(tableName, layui);
	initExcelImportBtn(tableName, layui);
	initManualSyncBtn(tableName, layui);
	initStateCheckBtn(tableName, layui);
	initDataRefBtn(tableName, layui);
	initDeleteBtn(tableName, layui);
	initProductRefBtn(tableName, layui);

	if (tableName === 'processcontrol_list_table') {
		initSyncCableReportBtn();
		initSyncRgPhotoBtn();
	}

}

var initSyncCableReportBtn = function () {
	$('#processcontrol_list_table_syncCableReport').bind('click', function () {
		var loadingIndex = layer.msg('正在同步数据，请稍候...', {
			icon: 16,
			shade: 0.3,
			time: 0
		});
		twxAjax("Thing.Fn.Dl", "SyncDLReport", {
			processTreeId: ztreeObj.getSelectedNodes()[0].TREEID
		}, true, function (res) {
			layer.close(loadingIndex);
			if (res.success) {
				layer.msg(res.msg);
				reloadRefDPTable(ztreeObj.getSelectedNodes()[0].TREEID);
			} else {
				layer.msg(res.msg);
			}
		});
	});
}

var initSyncRgPhotoBtn = function () {
	$('#processcontrol_list_table_syncRgPhoto').bind('click', function () {
		var loadingIndex = layer.msg('正在同步热管照片，请稍候...', {
			icon: 16,
			shade: 0.3,
			time: 0
		});
		twxAjax("Thing.Fn.RG", "SyncRgPhoto", {
			processTreeId: ztreeObj.getSelectedNodes()[0].TREEID
		}, true, function (res) {
			layer.close(loadingIndex);
			if (res.success) {
				layer.msg(res.msg);
				reloadRefDPTable(ztreeObj.getSelectedNodes()[0].TREEID);
			} else {
				layer.msg(res.msg);
			}
		});
	});
}

var getSelTabInfo = function () {
	var selectedTab = $('#root_layout_tabs').tabs('getSelected');
	var tabId = selectedTab.panel('options').name;
	var dataTableName = '';
	var typeName = "";
	if (tabId === 'design_list_table') {
		dataTableName = 'DESIGN_DATA_RESULT';
		typeName = '设计类';
	} else if (tabId === 'craft_list_table') {
		dataTableName = 'CRAFT_DATA_RESULT';
		typeName = '工艺类';
	} else if (tabId === 'processcontrol_list_table') {
		dataTableName = 'PROCESS_CONTROL_RESULT';
		typeName = '过程控制';
	} else if (tabId === 'quanlitycontrol_list_table') {
		dataTableName = 'QUALITY_CONTROL_RESULT';
		typeName = '质量综合';
	}
	return {
		tabId: tabId,
		dataTableName: dataTableName,
		typeName: typeName,
		datapkg: $('#datapkgref').datagrid('getSelections')[0]
	};
}
var initParamBtn = function (tableName, layui) {
	$('#' + tableName + '_param').bind('click', function () {
		var tabInfo = getSelTabInfo();
		editParam(layui, tabInfo.tabId, tabInfo.dataTableName, function () {
			reloadTable(tabInfo.tabId);
		});
	});
}

//初始化手动上传按钮
var initUploadBtn = function (tableName, layui) {
	$('#' + tableName + '-manual-upload').bind('click', function () {

		if (!getSelectedNode()) {
			return;
		}
		var tName = '';
		if (tableName === 'design_list_table') {
			tName = 'DESIGN_DATA_list';
		} else if (tableName === 'craft_list_table') {
			tName = 'CRAFT_DATA_list';
		} else if (tableName === 'processcontrol_list_table') {
			tName = 'PROCESS_CONTROL_list';
		} else if (tableName === 'quanlitycontrol_list_table') {
			tName = 'QUALITY_CONTROL_list';
		}
		var sels = $('#datapkgref').datagrid('getSelections');
		twxAjax("Thing.Fn.DataCollect", "GetUploadFileType", {
			tablename: tName,
			datapkgid: sels[0].ID
		}, true, function (data) {
			if (data.rows.length == 0) {
				layer.alert('您未创建手动上传数据包！', {
					icon: 2
				}, function () {
					layer.closeAll();
				});
				return;
			} else {
				uploadClick(tableName, layui)
			}
		});
	});
};

//初始化Excel导入按钮
var initExcelImportBtn = function (tableName, layui) {
	$('#' + tableName + '_excelimport').bind('click', function () {
		var layer = layui.layer,
			form = layui.form,
			upload = layui.upload,
			device = layui.device();
		var sels = $('#datapkgref').datagrid('getSelections');
		if (sels.length == 0) {
			layer.msg('请先创建数据包!');
			return;
		}
		var fileFlag = false;
		var nodeCode = sels[0].ID;
		var params = '';

		layer.open({
			title: 'Excel导入',
			type: 1,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			// fixed: false,
			maxmin: false,
			resize: false, //不允许拉伸
			area: ['450px', '330px'],
			content: '<div id="excelContent" style="padding-top: 15px;padding-right: 15px;"></div>',
			btn: ['确认', '取消'],
			yes: function () {
				var fileType = $("#fileType option:selected").text();
				var securityLevel = $("#securityLevel").val();
				var fileName = $("#fileName").val();
				if (fileType == '') {
					layer.msg('请选择文件类型!');
					return;
				}
				if (!fileFlag) {
					layer.msg('请选择需要导入的excel文件!');
					return;
				}
				if (fileName == '') {
					layer.msg('请输入文件名称!');
					return;
				}
				if (securityLevel == '') {
					layer.msg('请选择密级!');
					return;
				}
				params += 'type=' + resultTable;
				params += '&nodeCode=' + nodeCode;
				params += '&fileType=' + encodeURIComponent(fileType);
				params += '&fileTypeValue=' + $("#fileType").val();
				params += '&fileName=' + encodeURIComponent(fileName);
				params += '&fileFormat=' + $("#fileFormat").val();
				params += '&securityLevel=' + securityLevel;
				params += '&creator=' + sessionStorage.getItem('username');
				uploadInst.config.url = fileHandlerUrl + '/first/phase/import/excel?' + params;
				if (device.ie && device.ie < 10) {
					$("form[target]")[0].action = fileHandlerUrl + '/first/phase/import/excel?' + params;
				}
				// var fs = uploadInst.config.files;
				// var tempArr = [];
				// for (var f in fs) {
				// 	tempArr.push(f);
				// }
				// if (tempArr.length > 1) {
				// 	for (var i = 0; i < tempArr.length - 1; i++) {
				// 		delete fs[tempArr[i]];
				// 	}
				// 	uploadInst.config.files = fs;
				// }
				// params.fileType = fileType;
				// params.fileTypeValue = $("#fileType").val();
				// params.fileNumber = '';
				// params.fileName = fileName;
				// params.fileFormat = $("#fileFormat").val();
				// params.securityLevel = securityLevel;
				// params.creator = sessionStorage.getItem('username');
				$('#excelStart').click();
			},
			btn2: function () {
				// $('#btn_cancel').click();
				layer.closeAll();
			},
			success: function () {
				var addTpl = $("#excelHtml")[0].innerHTML;
				$("#excelContent").append(addTpl);
			}
		});

		// if(currentExcelImportData.ftype!=''){
		// 	 $("#fileType").append('<option value="' + currentExcelImportData.ftype + '">' + currentExcelImportData.ftype + '</option>');
		// }else{
		if (currentExcelImportData.type != '') {
			$("#fileType").append('<option value="' + currentExcelImportData.type + '">' +
				currentExcelImportData.typeName +
				'</option>');
		}
		// }


		// twxAjax("Thing.Fn.SystemDic", "GetDicDataByDicType", {
		// 	typename: "Excel导入类型"
		// }, false, function(data) {
		// 	for (var i = 0; i < data.rows.length; i++) {
		// 		$("#fileType").append('<option value="' + data.rows[i].KEY + '">' + data.rows[i].NAME + '</option>');
		// 	}
		// });
		var userSecData = [];
		twxAjax("Thing.Fn.SystemDic", "GetSecDataBySecLevel", {
			secLevel: userSecLevel
		}, false, function (data) {
			userSecData = data.rows;
			// for (var i = 0; i < data.rows.length; i++) {
			//     $("#securityLevel").append('<option value="' + data.rows[i].KEY + '">' + data.rows[i].NAME + '</option>');
			// }
		});

		form.render(null, 'excelForm');
		var tab = $('#root_layout_tabs').tabs('getSelected');
		var tableName = tab.panel('options').name;
		var resultTable = '';
		if (tableName === 'design_list_table') {
			resultTable = 'DESIGN_DATA_RESULT';
		} else if (tableName === 'craft_list_table') {
			resultTable = 'CRAFT_DATA_RESULT';
		} else if (tableName === 'processcontrol_list_table') {
			resultTable = 'PROCESS_CONTROL_RESULT';
		} else if (tableName === 'quanlitycontrol_list_table') {
			resultTable = 'QUALITY_CONTROL_RESULT';
		}
		var uploadInst = upload.render({
			elem: '#excelChoice',
			url: fileHandlerUrl + '/first/phase/import/excel?' + params,
			auto: false,
			accept: 'file',
			field: 'excelFile',
			exts: 'xls|xlsx',
			bindAction: '#excelStart',
			dataType: "json",
			before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
				// layer.load(); //上传loading
			},
			done: function (res, index, upload) {
				if (res.success) {
					layer.closeAll();
					layer.msg("成功导入" + res.result + "条数据！");
					reloadTable();
				} else {
					layer.alert('导入失败，日志:' + res.result, {
						icon: 2
					});
				}
			},
			choose: function (obj) {
				fileFlag = true;
				if (device.ie && device.ie < 10) {

				} else {
					var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
					var filename = '';
					for (var key in files) {
						filename = files[key].name;
					}
					var fileFormat = filename.substring(filename.lastIndexOf('.') + 1, filename
						.length);
					filename = filename.substring(0, filename.lastIndexOf('.'));

					var fileIsInUserSec = false;
					var fileSec = '';
					var fileSecName = '';
					for (var x = 0; x < userSecData.length; x++) {
						var security = userSecData[x];
						var name = "（" + security.NAME + "）";
						var name1 = "(" + security.NAME + ")";
						if (filename.lastIndexOf(name) > -1 || filename.lastIndexOf(name1) > -1) {
							fileIsInUserSec = true;
							fileSecName = security.NAME;
							fileSec = security.KEY;
						}
					}
					if (fileIsInUserSec) {
						$("#fileName").val(filename);
						$("#fileFormat").val(fileFormat);
						$("#securityLevelName").val(fileSecName);
						$("#securityLevel").val(fileSec);
					} else {
						layer.alert("请上传与自身密级相匹配的文件！", {
							icon: 2
						});
					}
				}
			}
		});

		if (device.ie && device.ie < 10) {
			$("input[name='excelFile']").change(function () {
				var filename = $(this).val();
				var fileFormat = filename.substring(filename.lastIndexOf('.') + 1, filename.length);
				filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.lastIndexOf(
					'.'));

				var fileIsInUserSec = false;
				var fileSec = '';
				var fileSecName = '';
				for (var x = 0; x < userSecData.length; x++) {
					var security = userSecData[x];
					var name = "（" + security.NAME + "）";
					var name1 = "(" + security.NAME + ")";
					if (filename.lastIndexOf(name) > -1 || filename.lastIndexOf(name1) > -1) {
						fileIsInUserSec = true;
						fileSecName = security.NAME;
						fileSec = security.KEY;
					}
				}
				if (fileIsInUserSec) {
					$("#fileName").val(filename);
					$("#fileFormat").val(fileFormat);
					$("#securityLevelName").val(fileSecName);
					$("#securityLevel").val(fileSec);
				} else {
					layer.alert("请上传与自身密级相匹配的文件！", {
						icon: 2
					});
				}
			});
		}
	});
};
//初始化手动同步按钮
var initManualSyncBtn = function (tableName, layui) {
	$('#' + tableName + '_manualSync').bind('click', function () {
		var layer = layui.layer;
		var sels = $('#datapkgref').datagrid('getSelections');
		if (sels.length == 0) {
			layer.alert('请先策划构建！', {
				icon: 2
			});
			return;
		}
		var treeid = sels[0].REFTREEID;
		var m = window.parent.manualSyncStatusMonitor;
		if (m.checkRunningManualSyncTask(treeid)) {
			return;
		}
		// layer.msg('同步任务正在后台执行，同步完成会提示您,您可以继续进行其它操作', {
		//     icon: 16,
		//     shade: 0.01,
		//     time: 2000
		// });
		layer.confirm("同步任务正在后台执行，同步完成会提示您，您可以继续进行其它操作", {
			btn: ['确定']
		}, function (index) {
			layer.close(index);
		});
		twxAjax('Thing.Integration.DataCollect', 'manualSync', {
			treeid: treeid,
			username: sessionStorage.getItem('username')
		}, true, function (data) {
			// var msg = data.rows[0].result;
			// layer.closeAll();
			// // layer.alert(msg, {
			// //     icon: 1
			// // });
			// //当数据选中后重新加载各类别的数据
			// var selectedTab = $('#root_layout_tabs').tabs('getSelected');
			// var tabId = selectedTab.panel('options').name;
			// reloadTable(tabId);
		}, function (data) {
			// layer.closeAll();
			// layer.alert('同步失败！', {
			//     icon: 2
			// });
		});
		window.parent.manualSyncStatusMonitor.loop();
	});
};
//初始化状态确认按钮
var initStateCheckBtn = function (tableName, layui) {
	$('#' + tableName + '_stateCheck').bind('click', function () {
		var tabInfo = getSelTabInfo();
		stateCheck(layui, tabInfo.tabId, tabInfo.dataTableName, tabInfo.datapkg, tabInfo.typeName,
			function () {
				reloadTable(tabInfo.tabId);
			});
	});
};

//初始化数据包关联按钮
var initDataRefBtn = function (tableName, layui) {
	$('#' + tableName + '_datapkgLink').bind('click', function () {
		var tabInfo = getSelTabInfo();
		reassociateProcessTree(layui, tabInfo.tabId, tabInfo.dataTableName, tabInfo.datapkg, tabInfo
			.typeName,
			function () {
				reloadTable(tabInfo.tabId);
			});
	});
};

//初始化产品结构树关联按钮
var initProductRefBtn = function (tableName, layui) {
	$('#' + tableName + '_procuct_link').bind('click', function () {
		var tabInfo = getSelTabInfo();
		productTreeRelation(layui, tabInfo.tabId, tabInfo.dataTableName, function () {
			reloadTable(tabInfo.tabId);
		});
	});
};

//初始化删除按钮
var initDeleteBtn = function (tableName, layui) {
	$('#' + tableName + '_delete').bind('click', function () {
		var tabInfo = getSelTabInfo();
		deleteDataList(layui, tabInfo.tabId, tabInfo.dataTableName, tabInfo.datapkg, tabInfo.typeName,
			function () {
				reloadTable(tabInfo.tabId);
			});
	});
};

//加载表格
var loadTableData = function (tableId, data) {
	var columns = listTableUtil.getColumns(queryType);
	columns[0].unshift({
		field: 'ck',
		checkbox: true
	});
	$('#' + tableId).datagrid({
		data: data,
		singleSelect: false,
		fitColumns: true,
		striped: true,
		rownumbers: true,
		pagination: true,
		height: windowH - 40,
		toolbar: "#" + tableId + "_tb",
		columns: columns,
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...',
		onLoadSuccess: function (data) {
			changeWidth(tableId);
		}
	});
	tableLoadFlag[tableId] = true;
	//初始化分页控件
	initPagination(tableId, {
		total: 0
	});
	//显示第一页的数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
};


//初始化行号
var initLineNumbers = function () {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function (index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};


var totalRecords = 0;
var dataLoadFlag = false;
var pageLoadFlag = false;
var paginationShow = function () {
	var tableName = '';
	var selectedTab = $('#root_layout_tabs').tabs('getSelected');
	tableName = selectedTab.panel('options').name;
	$('#' + tableName).datagrid('getPager').pagination('refresh', {
		total: totalRecords,
		pageSize: pageOptions.pageSize,
		pageNumber: pageOptions.pageNumber
	});
	//重新初始化行号
	initLineNumbers();
}

//获取查询条件参数
function getSearchParams() {
	var processTreeId = 1;
	if ($.fn.zTree.getZTreeObj("dpTree") != null) {
		var selNodes = $.fn.zTree.getZTreeObj("dpTree").getSelectedNodes();
		if (selNodes.length > 0) {
			processTreeId = selNodes[0].TREEID;
		}
	}
	var queryType = $('#root_layout_tabs').tabs('getSelected').panel('options').queryType;
	return {
		processTreeId: processTreeId,
		queryType: queryType,
		secLevel: '<' + userSecLevel,
		queryUser: sessionStorage.getItem('username')
	}
}


//初始化全部的记录条数
var initTotalRecords = function () {
	//初始化分页框架
	var sels = $('#datapkgref').datagrid('getSelections');
	if (sels.length > 0) {
		var cb_success = function (res) {
			if (res.success) {
				pageLoadFlag = true;
				totalRecords = res.data;
				if (dataLoadFlag) {
					paginationShow();
				}
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		};
		var cb_error = function () { };
		var params = {};
		params.query = getSearchParams();
		twxAjax('Thing.Fn.ListData', 'QueryListDataCount', params, true, cb_success, cb_error);
	}
};

//分页查询数据
var queryDataByPage = function (pageSize, pageNumber) {
	var tableName = '';
	var selectedTab = $('#root_layout_tabs').tabs('getSelected');
	tableName = selectedTab.panel('options').name;

	totalRecords = 0;
	dataLoadFlag = false;
	pageLoadFlag = false;
	//获取选中的数据包ID
	var sels = $('#datapkgref').datagrid('getSelections');
	if (sels.length > 0) {
		$('#' + tableName).datagrid('loading');
		initTotalRecords();
		var cb_success = function (res) {
			if (res.success) {
				dataLoadFlag = true;
				//调用成功后，渲染数据
				$('#' + tableName).datagrid('loadData', res.data);
				if (pageLoadFlag) {
					paginationShow();
				}
				$('#' + tableName).datagrid('loaded');
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}

		};
		var cb_error = function () {
			$('#' + tableName).datagrid('loaded');
			layui.use(['layer'], function () {
				var layer = layui.layer;
				layer.alert('加载出错...', {
					icon: 2
				});
			});
		};

		var params = {};
		params.query = getSearchParams();
		params.pageSize = pageOptions.pageSize;
		params.pageNumber = pageOptions.pageNumber;

		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.ListData', 'QueryListDataPage', params, true, cb_success, cb_error);
	} else {
		$('#' + tableName).datagrid('loading');
		$('#' + tableName).datagrid('loadData', []);
		paginationShow();
		$('#' + tableName).datagrid('loaded');
	}
};

//初始化分页组件
var initPagination = function (tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function () {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function (pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function (pageNumber, pageSize) {
			//返回false可以在取消刷新操作
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
		},
		onRefresh: function (pageNumber, pageSize) {
			//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function (pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	})
};

//加载table的数据
var reloadTable = function (tableName) {
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
}

//页面件参数传递使用
var selectedData = {
	tabName: undefined,
	data: undefined,
	node: undefined
};

//获取选中的zTree节点
function getSelectedNode() {
	//判断是否存在相应的数据包
	var sels = $('#datapkgref').datagrid('getSelections');
	if (sels.length == 0) {
		layui.use('layer', function () {
			layer.alert('请先创建数据包...', {
				icon: 2
			});
		});
		return false;
	}
	return true;
}

function showXmlContent(rec) {
	var infoHtml = "未定义";
	if (rec.FILE_TYPE = "影像记录") {
		infoHtml = document.getElementById("showXmlContent_photo").innerHTML;
	}

	var selectedTab = $('#root_layout_tabs').tabs('getSelected');
	var tabId = selectedTab.panel('options').name;
	var tableType = "";
	if (tabId === 'design_list_table') {
		tableType = 'DESIGN_DATA_RESULT';
	} else if (tabId === 'craft_list_table') {
		tableType = 'CRAFT_DATA_RESULT';
	} else if (tabId === 'processcontrol_list_table') {
		tableType = 'PROCESS_CONTROL_RESULT';
	} else if (tabId === 'quanlitycontrol_list_table') {
		tableType = 'QUALITY_CONTROL_RESULT';
	}
	layer.open({
		title: '详细信息',
		type: 1,
		area: ['500px', '400px'],
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		resize: false,
		content: infoHtml,
		success: function () {
			if (rec.FILE_TYPE = "影像记录") {
				twxAjax("Thing.XmlContent", "getXmlDataByTypeAndID", {
					table: "xmldata_photo",
					Result_ID: rec.ID,
					tableType: tableType
				}, false, function (data) {
					// for (var i = 0;i<data.rows.length;i++){
					//     $("#model").append('<option value="'+data.rows[i].TREEID+'">'+data.rows[i].NODENAME+'</option>');
					// }
					var row = data.rows[0];

					document.getElementById("PhotoName").value = row.PHOTONAME;
					document.getElementById("ParentType").value = row.PARENTTYPE;
					document.getElementById("ParentName").value = row.PARENTNAME;
					document.getElementById("UpLoadTime").value = row.UPLOADTIME;

				});
			}
		}
	});

}