/**
 * HotUtil.Editor.Style.js - Handsontable 编辑器工具库 (样式模块)
 *
 * 包含用于处理单元格样式的函数，如字体大小、颜色等。
 */

HotUtil.fontSizeItems = function () {
    var items = [];
    for (var i = 12; i <= 30; i++) {
        var obj = {};
        var name = i + "";
        obj.key = 'fontSize:' + name;
        obj.name = name;
        obj.callback = function (key, selection, clickEvent) {
            HotUtil.dealClass("font-size-" + key.split(":")[1], "add");
        }
        items.push(obj);
    }
    return items;
};

HotUtil.fontColorItems = function () {
    var items = [];
    var colors = [{
        color: 'purple',
        name: '紫色'
    }, {
        color: 'green',
        name: '绿色'
    }, {
        color: 'orange',
        name: '橙色'
    }, {
        color: 'deeppink',
        name: '粉色'
    }, {
        color: 'black',
        name: '黑色'
    }];
    for (var i = 0; i < colors.length; i++) {
        var obj = {};
        obj.key = 'fontColor:' + colors[i].color;
        obj.name = '<span class="font-color-' + colors[i].color + '">' + colors[i].name + '</span>';
        obj.callback = function (key, selection, clickEvent) {
            HotUtil.dealClass("font-color-" + key.split(":")[1], "add");
        }
        items.push(obj);
    }
    return items;
};

HotUtil.dealClass = function (className, type) {
    var selected = HotUtil.getSelecteds();
    for (var i = 0; i < selected.length; i++) {
        var obj = selected[i];
        var meta = hot.getCellMeta(obj.row, obj.col);
        var oldClass = "";
        if (meta.className) {
            oldClass = meta.className;
        }
        var classArr = oldClass.split(" ");
        var newClassArr = [];
        for (var j = 0; j < classArr.length; j++) {
            if (className.indexOf('font-size') > -1) {
                if (classArr[j] !== className && classArr[j] !== " " && classArr[j].indexOf('font-size') ===
                    -1) {
                    newClassArr.push(classArr[j]);
                }
            } else if (className.indexOf('font-color') > -1) {
                if (classArr[j] !== className && classArr[j] !== " " && classArr[j].indexOf(
                    'font-color') === -1) {
                    newClassArr.push(classArr[j]);
                }
            } else {
                if (classArr[j] !== className && classArr[j] !== " ") {
                    newClassArr.push(classArr[j]);
                }
            }

        }
        if (type === 'add') {
            if (className !== 'font-color-black') {
                newClassArr.push(className);
            }
        }
        hot.setCellMeta(obj.row, obj.col, 'className', newClassArr.join(" "));
    }
    hot.render();
};

HotUtil.setSelectedStyle = function (style) {
    var selected = HotUtil.getSelecteds();
    for (var i = 0; i < selected.length; i++) {
        var obj = selected[i];
        var cellTd = hot.getCell(obj.row, obj.col);
        for (var key in style) {
            cellTd.style[key] = style[key];
        }
    }
    // hot.resumeRender();
};

HotUtil.createFontSizeCss = function () {
    var res = '';
    for (var i = 12; i <= 40; i++) {
        res += '.font-size-' + i + '{font-size:' + i + 'px !important;line-height:' + (i + 7) +
            'px !important}\n';
    }
};