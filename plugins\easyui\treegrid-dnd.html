<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>jQuery EasyUI</title>
		<link rel="stylesheet" type="text/css" href="themes/default/easyui.css">
		<link rel="stylesheet" type="text/css" href="themes/icon.css">
		<script type="text/javascript" src="jquery.min.js"></script>
		<script type="text/javascript" src="jquery.easyui.min.js"></script>
		<script type="text/javascript" src="treegrid-dnd.js"></script>
		<script type="text/javascript">
			var data = {
				"MENU_TYPE": "Virtual",
				"MENU_DESC": "",
				"MENU_SORT": "",
				"MENU_ENABLE": 1,
				"MENU_ID": 1,
				"MENU_PARENT": -1,
				"MENU_ICON": "",
				"children": [{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "工作台",
						"MENU_SORT": 1,
						"MENU_ENABLE": 1,
						"MENU_ID": 71,
						"MENU_DEFAULT": 1,
						"MENU_PARENT": 1,
						"id": 71,
						"text": "工作台",
						"MENU_NAME": "工作台",
						"MENU_ICON": "fa-home",
						"MENU_PATH": "components/workbench/workbench.html"
					},
					{
						"MENU_TYPE": "Virtual",
						"MENU_DESC": "",
						"MENU_SORT": 2,
						"MENU_ENABLE": 1,
						"MENU_ID": 4,
						"MENU_PARENT": 1,
						"MENU_ICON": "fa-pie-chart",
						"children": [{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 1,
								"MENU_ENABLE": 1,
								"MENU_ID": 7,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 4,
								"id": 7,
								"text": "过程结构树查询",
								"MENU_NAME": "过程结构树查询",
								"MENU_ICON": "fa-gg",
								"MENU_PATH": "components/dataSearch/processDataSearch.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 2,
								"MENU_ENABLE": 1,
								"MENU_ID": 8,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 4,
								"id": 8,
								"text": "产品结构树查询",
								"MENU_NAME": "产品结构树查询",
								"MENU_ICON": "fa-product-hunt",
								"MENU_PATH": "components/dataSearch/productDataSearch.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 3,
								"MENU_ENABLE": 1,
								"MENU_ID": 9,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 4,
								"id": 9,
								"text": "高级查询",
								"MENU_NAME": "高级查询",
								"MENU_ICON": "fa-search",
								"MENU_PATH": "components/dataSearch/advancedSearch.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "结构化数据综合查询",
								"MENU_SORT": 4,
								"MENU_ENABLE": 1,
								"MENU_ID": 31,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 4,
								"id": 31,
								"text": "数据查询",
								"MENU_NAME": "数据查询",
								"MENU_ICON": "fa-database",
								"MENU_PATH": "components/tableSearch/tableSearch.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 5,
								"MENU_ENABLE": 1,
								"MENU_ID": 91,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 4,
								"id": 91,
								"text": "质量数据综合查询",
								"MENU_NAME": "质量数据综合查询",
								"MENU_ICON": "fa-random",
								"MENU_PATH": "components/dataSearch/qualitySearch/qualitySearch.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 6,
								"MENU_ENABLE": 1,
								"MENU_ID": 111,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 4,
								"id": 111,
								"text": "关键指标分析",
								"MENU_NAME": "关键指标分析",
								"MENU_ICON": "fa-line-chart",
								"MENU_PATH": "components/indexAnalysis/indexAnalysis.html"
							}
						],
						"MENU_DEFAULT": 0,
						"id": 4,
						"text": "查询展示",
						"MENU_NAME": "查询展示",
						"MENU_PATH": ""
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "对产品数据包进行策划构建",
						"MENU_SORT": 3,
						"MENU_ENABLE": 1,
						"MENU_ID": 2,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 2,
						"text": "策划构建",
						"MENU_NAME": "策划构建",
						"MENU_ICON": "fa-pencil-square-o",
						"MENU_PATH": "components/planBuild/planBuild.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 4,
						"MENU_ENABLE": 1,
						"MENU_ID": 3,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 3,
						"text": "采集管理",
						"MENU_NAME": "采集管理",
						"MENU_ICON": "fa-database",
						"MENU_PATH": "components/captureManagement/captureManagement.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 5,
						"MENU_ENABLE": 1,
						"MENU_ID": 5,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 5,
						"text": "下载管理",
						"MENU_NAME": "下载管理",
						"MENU_ICON": "fa-cloud-download",
						"MENU_PATH": "components/downloadManagement/index.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 6,
						"MENU_ENABLE": 0,
						"MENU_ID": 92,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 92,
						"text": "产品质量数据管理",
						"MENU_NAME": "产品质量数据管理",
						"MENU_ICON": "fa-quora",
						"MENU_PATH": "components/productQuality/productQuality.html"
					},
					{
						"MENU_TYPE": "Virtual",
						"MENU_DESC": "",
						"MENU_SORT": 7,
						"MENU_ENABLE": 1,
						"MENU_ID": 6,
						"MENU_PARENT": 1,
						"MENU_ICON": "fa-cogs",
						"children": [{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 1,
								"MENU_ENABLE": 1,
								"MENU_ID": 10,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 6,
								"id": 10,
								"text": "角色管理",
								"MENU_NAME": "角色管理",
								"MENU_ICON": "fa-users",
								"MENU_PATH": "components/systemManagement/roleMgr/roleMgr.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 2,
								"MENU_ENABLE": 1,
								"MENU_ID": 11,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 6,
								"id": 11,
								"text": "用户管理",
								"MENU_NAME": "用户管理",
								"MENU_ICON": "fa-user",
								"MENU_PATH": "components/systemManagement/userMgr/userMgr.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 3,
								"MENU_ENABLE": 1,
								"MENU_ID": 12,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 6,
								"id": 12,
								"text": "菜单管理",
								"MENU_NAME": "菜单管理",
								"MENU_ICON": "fa-lock",
								"MENU_PATH": "components/systemManagement/menuMgr/menuMgr.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 4,
								"MENU_ENABLE": 1,
								"MENU_ID": 13,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 6,
								"id": 13,
								"text": "数据字典",
								"MENU_NAME": "数据字典",
								"MENU_ICON": "fa-info-circle",
								"MENU_PATH": "components/systemManagement/basicConfig/basicConfig.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 6,
								"MENU_ENABLE": 1,
								"MENU_ID": 15,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 6,
								"id": 15,
								"text": "模板管理",
								"MENU_NAME": "模板管理",
								"MENU_ICON": "fa-file-text-o",
								"MENU_PATH": "components/systemManagement/templateMgr/templateMgr.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 7,
								"MENU_ENABLE": 1,
								"MENU_ID": 51,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 6,
								"id": 51,
								"text": "二、三级表配置",
								"MENU_NAME": "二、三级表配置",
								"MENU_ICON": "fa-cog",
								"MENU_PATH": "components/tableConfig/tableConfig.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 8,
								"MENU_ENABLE": 1,
								"MENU_ID": 171,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 6,
								"id": 171,
								"text": "质量确认模板配置",
								"MENU_NAME": "质量确认模板配置",
								"MENU_ICON": "fa-microchip",
								"MENU_PATH": "components/qualityReportTpl/qualityReportTpl.html"
							}
						],
						"MENU_DEFAULT": 0,
						"id": 6,
						"text": "系统管理",
						"MENU_NAME": "系统管理",
						"MENU_PATH": ""
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 8,
						"MENU_ENABLE": 1,
						"MENU_ID": 14,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 14,
						"text": "前台日志",
						"MENU_NAME": "前台日志",
						"MENU_ICON": "fa-file-text-o",
						"MENU_PATH": "components/logs/logs.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 9,
						"MENU_ENABLE": 1,
						"MENU_ID": 132,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 132,
						"text": "发射场确认",
						"MENU_NAME": "发射场确认",
						"MENU_ICON": "fa-wpexplorer",
						"MENU_PATH": "components/launchConfirm/launchConfirm.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 10,
						"MENU_ENABLE": 0,
						"MENU_ID": 151,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 151,
						"text": "发射场确认模板",
						"MENU_NAME": "发射场确认模板",
						"MENU_ICON": "fa-gear",
						"MENU_PATH": "components/launchConfirmConfig/launchConfirmConfig.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 11,
						"MENU_ENABLE": 1,
						"MENU_ID": 172,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 172,
						"text": "AIT质量确认",
						"MENU_NAME": "AIT质量确认",
						"MENU_ICON": "fa-table",
						"MENU_PATH": "components/qualityReport/qualityReport.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 12,
						"MENU_ENABLE": 1,
						"MENU_ID": 173,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 173,
						"text": "产品质量确认",
						"MENU_NAME": "产品质量确认",
						"MENU_ICON": "fa-th",
						"MENU_PATH": "components/qualityConfirm/qualityConfirm.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 13,
						"MENU_ENABLE": 1,
						"MENU_ID": 212,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 212,
						"text": "数据看板",
						"MENU_NAME": "数据看板",
						"MENU_ICON": "fa-desktop",
						"MENU_PATH": "components/bigScreen/bigScreen.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 14,
						"MENU_ENABLE": 1,
						"MENU_ID": 213,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 213,
						"text": "AIT质量数据看板",
						"MENU_NAME": "AIT质量数据看板",
						"MENU_ICON": "fa-laptop",
						"MENU_PATH": "components/aitScreen/aitScreen.html"
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 15,
						"MENU_ENABLE": 1,
						"MENU_ID": 232,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 232,
						"text": "确认表日志",
						"MENU_NAME": "确认表日志",
						"MENU_ICON": "fa-file-text-o",
						"MENU_PATH": "components/logs/confirmLogs.html"
					},
					{
						"MENU_TYPE": "Virtual",
						"MENU_DESC": "",
						"MENU_SORT": 16,
						"MENU_ENABLE": 1,
						"MENU_ID": 252,
						"MENU_PARENT": 1,
						"MENU_ICON": "fa-rmb",
						"children": [{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 1,
								"MENU_ENABLE": 1,
								"MENU_ID": 253,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 252,
								"id": 253,
								"text": "计算模型管理",
								"MENU_NAME": "计算模型管理",
								"MENU_ICON": "fa-calculator",
								"MENU_PATH": "components/costing/model/model.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 2,
								"MENU_ENABLE": 1,
								"MENU_ID": 254,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 252,
								"id": 254,
								"text": "我的任务",
								"MENU_NAME": "我的任务",
								"MENU_ICON": "fa-tasks",
								"MENU_PATH": "components/costing/task/task.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 3,
								"MENU_ENABLE": 1,
								"MENU_ID": 255,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 252,
								"id": 255,
								"text": "统计分析",
								"MENU_NAME": "统计分析",
								"MENU_ICON": "fa-superscript",
								"MENU_PATH": "components/costing/statistic/statistic.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 4,
								"MENU_ENABLE": 1,
								"MENU_ID": 272,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 252,
								"id": 272,
								"text": "归零通知单",
								"MENU_NAME": "归零通知单",
								"MENU_ICON": "fa-circle-o",
								"MENU_PATH": "components/costing/zero/zero.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 5,
								"MENU_ENABLE": 1,
								"MENU_ID": 292,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 252,
								"id": 292,
								"text": "流程定义",
								"MENU_NAME": "流程定义",
								"MENU_ICON": "fa-envira",
								"MENU_PATH": "components/costing/flow/flow.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 6,
								"MENU_ENABLE": 1,
								"MENU_ID": 312,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 252,
								"id": 312,
								"text": "归零成本分析表",
								"MENU_NAME": "归零成本分析表",
								"MENU_ICON": "fa-table",
								"MENU_PATH": "components/costing/analyse/analyse.html"
							},
							{
								"MENU_TYPE": "Menu",
								"MENU_DESC": "",
								"MENU_SORT": 7,
								"MENU_ENABLE": 1,
								"MENU_ID": 332,
								"MENU_DEFAULT": 0,
								"MENU_PARENT": 252,
								"id": 332,
								"text": "表单配置",
								"MENU_NAME": "表单配置",
								"MENU_ICON": "fa-wpforms",
								"MENU_PATH": "components/costing/form/form.html"
							}
						],
						"MENU_DEFAULT": 0,
						"id": 252,
						"text": "质量成本",
						"MENU_NAME": "质量成本",
						"MENU_PATH": ""
					},
					{
						"MENU_TYPE": "Menu",
						"MENU_DESC": "",
						"MENU_SORT": 17,
						"MENU_ENABLE": 1,
						"MENU_ID": 352,
						"MENU_DEFAULT": 0,
						"MENU_PARENT": 1,
						"id": 352,
						"text": "试验鉴定",
						"MENU_NAME": "试验鉴定",
						"MENU_ICON": "fa-plug",
						"MENU_PATH": "components/testEvaluation/testEvaluation.html"
					}
				],
				"MENU_DEFAULT": 0,
				"id": 1,
				"text": "系统菜单",
				"MENU_NAME": "系统菜单",
				"MENU_PATH": ""
			};
			var cols = [
				[{
						title: '名称',
						field: 'MENU_NAME',
						width: 180
					},
					{
						title: '图标名称',
						field: 'MENU_ICON',
						width: 180,
						formatter: function(value, row, index) {
							if (value !== '') {
								return '<i class="fa ' + value + '" aria-hidden="true"></i>&nbsp;&nbsp;' + value;
							}
						}
					},
					{
						title: '是否启用',
						field: 'MENU_ENABLE',
						width: 80,
						align: 'center',
						formatter: function(value, row, index) {
							if (value === 1) {
								return '√';
							}
						}
					},
					{
						title: '是否默认',
						field: 'MENU_DEFAULT',
						width: 80,
						align: 'center',
						formatter: function(value, row, index) {
							if (value === 1) {
								return '√';
							}
						}
					},
					{
						title: '类型',
						field: 'MENU_TYPE',
						align: 'center',
						width: 80,
						formatter: function(value, row, index) {
							var display = '';
							if (value === '') {
								return '';
							}
							if (value === 'Menu') {
								display = '菜单';
								return '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal">' + display + '</button>';
							} else if (value === 'Security') {
								display = '权限';
								return '<button type="button" class="layui-btn layui-btn-xs">' + display + '</button>';
							} else if (value === 'Virtual') {
								display = '虚拟组';
								return '<button type="button" class="layui-btn layui-btn-xs layui-btn-warm">' + display + '</button>';
							}
							return display;
						}
					},
					{
						title: '路径',
						field: 'MENU_PATH',
						width: 300
					},
					{
						title: '说明',
						field: 'MENU_DESC',
						width: 500
					},
					{
						title: '排序',
						field: 'MENU_SORT',
						width: 80
					},
					{
						title: 'id',
						field: 'MENU_ID',
						hidden: true
					},
					{
						title: 'parentid',
						field: 'MENU_PARENT',
						hidden: true
					}
				]
			];
		</script>
	</head>
	<body>
		<h1>Drag and Drop Rows in TreeGrid</h1>
		<table title="Folder Browser" id="demo" class="easyui-treegrid" style="width:700px;height:650px">
		</table>
	</body>
	<script>
		var num = 0;
		$("#demo").treegrid({
			data: [data],
			rownumbers: true,
			idField: 'MENU_ID',
			treeField: 'MENU_NAME',
			columns: cols,
			onLoadSuccess: function(row, data) {
				$(this).treegrid('enableDnd', row ? row.id : null);
			},
			onBeforeDrag: function(row) {
				if (row.MENU_NAME == '系统菜单') {
					return false;
				}
			},
			onStartDrag: function(row) {
			},
			onDragEnter: function(targetRow, sourceRow) {
				if (targetRow.MENU_PARENT !== sourceRow.MENU_PARENT) {
					return false;
				}
			},
			onDragOver: function(targetRow, sourceRow) {
				if (targetRow.MENU_PARENT !== sourceRow.MENU_PARENT) {
					return false;
				}
			},
			onDragLeave: function(targetRow, sourceRow) {
			},
			onBeforeDrop: function(targetRow, sourceRow, point) {
				if (point == 'append') {
					return false;
				}
			},
			onDrop: function(targetRow, sourceRow, point) {
				
			}
		});
	</script>
</html>