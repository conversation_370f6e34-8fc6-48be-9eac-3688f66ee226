//1、多层加工
var col1 = function(val1, val2) {
	return [
		[{
				field: 'xxx1',
				title: '型号',
				width: 100,
				colspan: 5,
				align: 'center'
			},
			{
				field: 'xxx2',
				title: val1,
				width: 100,
				colspan: 4,
				align: 'center'
			},
			{
				field: 'xxx3',
				title: '产品类别',
				width: 100,
				colspan: 5,
				align: 'center'
			},
			{
				field: 'xxx4',
				title: val2,
				width: 100,
				colspan: 5,
				align: 'center'
			}
		],
		[{
				field: 'ROWNUM',
				title: '序<br>号',
				width: 30,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL4',
				title: '多层名称',
				width: 70,
				rowspan: 2,
				align: 'center',
				formatter: function(value, rec) {
					var filepath = rec.FILEPATH;
					return '<a  class="table-row-a" onclick = "viewThreeTable(\'' + filepath + '\')">' + value + '</a>'
				}
			},
			{
				field: 'VAL5',
				title: '多层代号',
				width: 70,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL6',
				title: '多层物<br>料编号',
				width: 70,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL7',
				title: '件数',
				width: 35,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL8',
				title: '隔热层<br>层数',
				width: 60,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL9',
				title: '重量<br>(g)',
				width: 40,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL10',
				title: '底膜',
				width: 80,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL11',
				title: '面膜',
				width: 80,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL12',
				title: '包边<br>要求<br><√/×>',
				width: 50,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'xxx5',
				title: '接地线',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'VAL15',
				title: '二次表<br>面镜电<br>阻(kΩ)',
				width: 50,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL16',
				title: '最上层镀铝<br>膜到接地线<br>末端导通<br>阻值(Ω)',
				width: 80,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL17',
				title: '多层接地点<br>到接地线末<br>端导通阻值<br>(Ω)',
				width: 80,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL18',
				title: '影像<br>记录',
				width: 80,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL19',
				title: '结论',
				width: 80,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL20',
				title: '检验<br>人员',
				width: 80,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL21',
				title: '检验<br>时间',
				width: 80,
				rowspan: 2,
				align: 'center'
			}
		],
		[{
				field: 'VAL13',
				title: '代号',
				width: 60,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL14',
				title: '数量',
				width: 60,
				rowspan: 1,
				align: 'center'
			}
		]
	]
}
//2、热管加工
var col2 = function(val1, val2) {
	return [
		[{
				field: 'xxx1',
				title: '型号',
				width: 100,
				colspan: 4,
				align: 'center'
			},
			{
				field: 'xxx2',
				title: val1,
				width: 100,
				colspan: 9,
				align: 'center'
			},
			{
				field: 'xxx3',
				title: '产品类别',
				width: 100,
				colspan: 4,
				align: 'center'
			},
			{
				field: 'xxx4',
				title: val2,
				width: 100,
				colspan: 10,
				align: 'center'
			},
			{
				field: 'VAL28',
				title: '检验<br>人员',
				width: 100,
				align: 'center',
				rowspan: 3
			},
			{
				field: 'VAL29',
				title: '检验<br>时间',
				width: 100,
				align: 'center',
				rowspan: 3
			}
		],
		[{
				field: 'ROWNUM',
				title: '序<br>号',
				width: 30,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL4',
				title: '产品名称',
				width: 100,
				rowspan: 2,
				align: 'center',
				formatter: function(value, rec) {
					var filepath = rec.FILEPATH;
					return '<a  class="table-row-a" onclick = "viewThreeTable(\'' + filepath + '\')">' + value + '</a>'
				}
			},
			{
				field: 'VAL5',
				title: '产品代号',
				width: 60,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL6',
				title: '产品编号',
				width: 100,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL7',
				title: '外观',
				width: 40,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL8',
				title: '重量<br>(g)',
				width: 50,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL9',
				title: 'X射线<br>探伤',
				width: 50,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'xxx5',
				title: '长度(mm)',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx6',
				title: '宽度(mm)',
				width: 100,
				colspan: 2,
				align: 'center'
			}, {
				field: 'xxx7',
				title: '高度(mm)',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx8',
				title: '平面度(mm)',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx9',
				title: '直线度(mm)',
				width: 100,
				colspan: 2,
				align: 'center'
			}, {
				field: 'xxx10',
				title: '耐温性能(℃)',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx11',
				title: '等温性能(℃)',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx12',
				title: '管壳漏率(Pa·m3/s)',
				width: 100,
				colspan: 2,
				align: 'center'
			}, {
				field: 'xxx13',
				title: '充氨量(g)',
				width: 100,
				colspan: 2,
				align: 'center'
			}, {
				field: 'VAL26',
				title: '结<br>论',
				width: 40,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL27',
				title: '影像<br>记录',
				width: 100,
				rowspan: 2,
				align: 'center'
			}
		],
		[{
				field: 'VAL10',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL11',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL12',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL13',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL14',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL15',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL16',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL17',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL18',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL19',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL20',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL21',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL22',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL23',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL24',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL25',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL30',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL31',
				title: '实测',
				width: 60,
				rowspan: 1,
				align: 'center'
			}
		]
	]
}

//3、热控喷涂
var col3 = function(val1, val2) {
	return [
		[{
				field: 'val1',
				title: '型号',
				width: 100,
				colspan: 4,
				align: 'center'
			},
			{
				field: 'VAL1',
				title: val1,
				width: 100,
				colspan: 3,
				align: 'center'
			},
			{
				field: 'val2',
				title: '产品类别',
				width: 70,
				colspan: 3,
				align: 'center'
			},
			{
				field: 'VAL2',
				title: val2,
				width: 100,
				colspan: 4,
				align: 'center'
			},
			// {
			// 	field: 'val3',
			// 	title: '试片号',
			// 	width: 100,
			// 	align: 'center',
			// 	colspan: 2
			// },
			// {
			// 	field: 'VAL3',
			// 	title: val3,
			// 	width: 100,
			// 	align: 'center',
			// 	colspan: 2
			// },
			{
				field: 'VAL18',
				title: '影像<br>记录',
				width: 100,
				rowspan: 3,
				align: 'center'
			},
			{
				field: 'VAL19',
				title: '检验<br>人员',
				width: 100,
				align: 'center',
				rowspan: 3
			},
			{
				field: 'VAL20',
				title: '检验<br>时间',
				width: 100,
				align: 'center',
				rowspan: 3
			}
		],
		[{
				field: 'ROWNUM',
				title: '序<br>号',
				width: 30,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL5',
				title: '零件名称',
				width: 100,
				rowspan: 2,
				align: 'center',
				formatter: function(value, rec) {
					var filepath = rec.FILEPATH;
					return '<a  class="table-row-a" onclick = "viewThreeTable(\'' + filepath + '\')">' + value + '</a>'
				}
			},
			{
				field: 'VAL6',
				title: '涂层',
				width: 100,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL7',
				title: '外观',
				width: 40,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL8',
				title: '喷涂位<br>置及尺<br>寸',
				width: 70,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL9',
				title: '产品<br>重量<br>(g)',
				width: 70,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL10',
				title: '喷涂<br>增重<br>(g)',
				width: 70,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'name11',
				title: '发射率εh',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'price12',
				title: '太阳吸收率αs',
				width: 100,
				colspan: 2,
				align: 'center'
			}, {
				field: 'code13',
				title: '厚度(um)',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'VAL17',
				title: '结论',
				width: 50,
				rowspan: 2,
				align: 'center'
			}
		],
		[{
				field: 'VAL11',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL12',
				title: '实测值',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL13',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL14',
				title: '实测值',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL15',
				title: '设计值',
				width: 70,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL16',
				title: '实测值',
				width: 60,
				rowspan: 1,
				align: 'center'
			}
		]
	]
}
//4、OSR粘贴
var col4 = function(val1, val2) {
	return [
		[{
				field: 'xxx1',
				title: '型号',
				width: 100,
				colspan: 5,
				align: 'center'
			},
			{
				field: 'xxx2',
				title: val1,
				width: 100,
				colspan: 4,
				align: 'center'
			},
			{
				field: 'xxx3',
				title: '产品类别',
				width: 100,
				colspan: 4,
				align: 'center'
			},
			{
				field: 'xxx4',
				title: val2,
				width: 100,
				colspan: 4,
				align: 'center'
			},
			{
				field: 'VAL21',
				title: '影像<br>记录',
				width: 100,
				rowspan: 3,
				align: 'center'
			},
			{
				field: 'VAL22',
				title: '检验<br>人员',
				width: 100,
				align: 'center',
				rowspan: 3
			},
			{
				field: 'VAL23',
				title: '检验<br>时间',
				width: 100,
				align: 'center',
				rowspan: 3
			}
		],
		[{
				field: 'ROWNUM',
				title: '序<br>号',
				width: 30,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL5',
				title: '产品名称',
				width: 100,
				rowspan: 2,
				align: 'center',
				formatter: function(value, rec) {
					var filepath = rec.FILEPATH;
					return '<a  class="table-row-a" onclick = "viewThreeTable(\'' + filepath + '\')">' + value + '</a>'
				}
			},
			{
				field: 'VAL6',
				title: '外观',
				width: 40,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL7',
				title: '重量<br>(g)',
				width: 40,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL8',
				title: 'OSR增量<br>(g)',
				width: 50,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL9',
				title: '粘贴位<br>置及<br>尺寸',
				width: 60,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'xxx5',
				title: '太阳吸收比',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx6',
				title: '半球发射率',
				width: 100,
				colspan: 2,
				align: 'center'
			}, {
				field: 'xxx7',
				title: '电阻值',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx8',
				title: '附着力',
				width: 100,
				colspan: 2,
				align: 'center'
			}, {
				field: 'xxx9',
				title: '规格',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'VAL20',
				title: '结论',
				width: 40,
				rowspan: 2,
				align: 'center'
			}
		],
		[{
				field: 'VAL10',
				title: '设计值',
				width: 60,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL11',
				title: '实测值',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL12',
				title: '设计值',
				width: 60,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL13',
				title: '实测值',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL14',
				title: '设计值(kΩ)',
				width: 60,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL15',
				title: '实测值',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL16',
				title: '设计值',
				width: 60,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL17',
				title: '实测值',
				width: 60,
				rowspan: 1,
				align: 'center'
			}, {
				field: 'VAL18',
				title: '40*40mm',
				width: 60,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL19',
				title: '40*20mm',
				width: 60,
				rowspan: 1,
				align: 'center'
			}
		]
	]
}
//5、热敏电阻加工
var col5 = function(val1, val2) {
	return [
		[{
				field: 'xxx1',
				title: '型号',
				width: 100,
				colspan: 3,
				align: 'center'
			},
			{
				field: 'xxx2',
				title: val1,
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'xxx3',
				title: '产品类别',
				width: 100,
				colspan: 5,
				align: 'center'
			},
			{
				field: 'xxx4',
				title: val2,
				width: 100,
				colspan: 5,
				align: 'center'
			},
			{
				field: 'VAL18',
				title: '影像记录',
				width: 100,
				rowspan: 3,
				align: 'center'
			},
			{
				field: 'VAL19',
				title: '检验人员',
				width: 100,
				align: 'center',
				rowspan: 3
			},
			{
				field: 'VAL20',
				title: '检验时间',
				width: 100,
				align: 'center',
				rowspan: 3
			}
		],
		[{
				field: 'ROWNUM',
				title: '序<br>号',
				width: 30,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL4',
				title: '测点名称',
				width: 80,
				rowspan: 2,
				align: 'center',
				formatter: function(value, rec) {
					var filepath = rec.FILEPATH;
					return '<a  class="table-row-a" onclick = "viewThreeTable(\'' + filepath + '\')">' + value + '</a>'
				}
			},
			{
				field: 'VAL5',
				title: '遥测代号',
				width: 80,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL6',
				title: '型号',
				width: 60,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL7',
				title: '编号',
				width: 60,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL8',
				title: '外观',
				width: 50,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'xxx5',
				title: '长度(M)',
				width: 100,
				colspan: 2,
				align: 'center'
			},
			{
				field: 'VAL11',
				title: '导线型号',
				width: 100,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL12',
				title: '标定温度<br>(℃)',
				width: 70,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL13',
				title: '实测阻值<br>(kΩ)',
				width: 70,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL14',
				title: '端头焊<br>插针/孔',
				width: 60,
				rowspan: 2,
				align: 'center'
			}, {
				field: 'VAL15',
				title: '曲线表<br>报告编号',
				width: 80,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL16',
				title: '环境温度<br>(℃)',
				width: 70,
				rowspan: 2,
				align: 'center'
			},
			{
				field: 'VAL17',
				title: '结论',
				width: 40,
				rowspan: 2,
				align: 'center'
			}
		],
		[{
				field: 'VAL9',
				title: '设计值',
				width: 50,
				rowspan: 1,
				align: 'center'
			},
			{
				field: 'VAL10',
				title: '实测值',
				width: 50,
				rowspan: 1,
				align: 'center'
			}
		]
	]
}
//6、一般结构件
var col6 = function(val1, val2) {
	return [
		[{
				field: 'xxx1',
				title: '型号',
				width: 40,
				align: 'center'
			},
			{
				field: 'xxx2',
				title: val1,
				width: 80,
				align: 'center'
			},
			{
				field: 'xxx3',
				title: '产品类别',
				width: 100,
				align: 'center'
			},
			{
				field: 'xxx4',
				title: val2,
				width: 100,
				colspan: 8,
				align: 'center'
			}
		],
		[{
				field: 'ROWNUM',
				title: '序号',
				width: 40,
				align: 'center'
			},
			{
				field: 'VAL4',
				title: '图号',
				width: 80,
				align: 'center'
			},
			{
				field: 'VAL5',
				title: '结构件名称',
				width: 100,
				align: 'center',
				formatter: function(value, rec) {
					var filepath = rec.FILEPATH;
					return '<a  class="table-row-a" onclick = "viewThreeTable(\'' + filepath + '\')">' + value + '</a>'
				}
			},
			{
				field: 'VAL6',
				title: '数量',
				width: 60,
				align: 'center'
			},
			{
				field: 'VAL7',
				title: '结论',
				width: 50,
				align: 'center'
			},
			{
				field: 'VAL8',
				title: '重量（g）',
				width: 70,
				align: 'center'
			},
			{
				field: 'VAL9',
				title: '表面状态',
				width: 150,
				align: 'center'
			},
			{
				field: 'VAL10',
				title: '标识',
				width: 50,
				align: 'center'
			},
			{
				field: 'VAL11',
				title: '检验人',
				width: 90,
				align: 'center'
			},
			{
				field: 'VAL12',
				title: '检验时间',
				width: 85,
				align: 'center'
			},
			{
				field: 'VAL13',
				title: '影像记录',
				width: 150,
				align: 'center'
			}
		]
	]
}

var col66 = [
	[{
			field: 'code1',
			title: '型号',
			width: 100,
			colspan: 4,
			align: 'center'
		},
		{
			field: 'name1',
			title: 'xx卫星',
			width: 100,
			colspan: 2,
			align: 'center'
		},
		{
			field: 'price1',
			title: '产品类别',
			width: 100,
			colspan: 6,
			align: 'center'
		},
		{
			field: 'price22',
			title: '低频电缆网',
			width: 100,
			colspan: 10,
			align: 'center'
		}
	],
	[{
			field: 'code4',
			title: '序号',
			width: 100,
			rowspan: 2,
			align: 'center'
		},
		{
			field: 'name5',
			title: '元器件名称',
			width: 100,
			rowspan: 2,
			align: 'center'
		},
		{
			field: 'price6',
			title: '规格',
			width: 100,
			rowspan: 2,
			align: 'center'
		}, {
			field: 'code7',
			title: '批次号',
			width: 100,
			rowspan: 2,
			align: 'center'
		},
		{
			field: 'name8',
			title: '生产厂家',
			width: 100,
			rowspan: 2,
			align: 'center'
		},
		{
			field: 'price9',
			title: '质量等级',
			width: 100,
			rowspan: 2,
			align: 'center'
		},
		{
			field: 'name11',
			title: '实际装机情况',
			width: 100,
			colspan: 2,
			align: 'center'
		},
		{
			field: 'price12',
			title: '器件筛选情况',
			width: 100,
			colspan: 4,
			align: 'center'
		}, {
			field: 'code13',
			title: 'DPA情况',
			width: 100,
			rowspan: 2,
			align: 'center'
		},
		{
			field: 'price12',
			title: '进口情况',
			width: 100,
			rowspan: 2,
			align: 'center'
		}, {
			field: 'code13',
			title: '目外器件',
			width: 100,
			colspan: 2,
			align: 'center'
		}, {
			field: 'code133',
			title: '装机器件代用情况',
			width: 100,
			colspan: 2,
			align: 'center'
		},
		{
			field: 'price12',
			title: '超期器件',
			width: 100,
			colspan: 2,
			align: 'center'
		},
		{
			field: 'www',
			title: '检测报告号',
			width: 100,
			rowspan: 2,
			align: 'center'
		},
		{
			field: 'wwww',
			title: '所内合格证',
			width: 100,
			rowspan: 2,
			align: 'center'
		}
	],
	[{
			field: 'name22',
			title: '实际装机数量',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '所装单机',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '筛选中心',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '筛选',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '合格数',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '不合格情况',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '目外器件',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '批准人',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '图纸表述型号',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '实际使用型号',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '超期类型',
			width: 100,
			rowspan: 1,
			align: 'center'
		},
		{
			field: 'price23',
			title: '复验结论',
			width: 100,
			rowspan: 1,
			align: 'center'
		}
	]
];
