/**
 * HotUtil.HtmlTable.js - Handsontable工具类 (HTML表格渲染模块)
 *
 * 负责渲染和交互静态HTML表格视图。
 */

/**
 * 加载HTML表格
 */
HotUtil.loadHtmlTable = function (res, treeNode, tableSel, otherHeight, pageType, scrollObj) {
    if (res.success) {
        tableSel.empty();
        var html = res.data.HTML_DATA || "";
        // if (res.data.SIGN_HTML && res.data.TABLE_STATUS == 'sign') {
        // 	treeNode.SIGN_HTML = res.data.SIGN_HTML;
        // 	html = res.data.SIGN_HTML;
        // }
        //更新node节点的数据
        treeNode.HTML_DATA = html;
        treeNode.SAVE_DATA = res.data.SAVE_DATA || "";
        treeNode.TABLE_STATUS = res.data.TABLE_STATUS || "";
        treeNode.SAVE_TIME = res.data.SAVE_TIME || "";
        treeNode.SECURITY = res.data.SECURITY;
        treeNode.FILE_PATH = res.data.FILE_PATH;
        treeNode.FILE_FORMAT = res.data.FILE_FORMAT;
        treeNode.ATTACHMENT = res.data.ATTACHMENT || "";
        // 记录当前表格的保存时间，用于乐观锁检查
        HotUtil.currentSaveTime = treeNode.SAVE_TIME;
        HotUtil.currentTreeNodeId = treeNode.ID;
        ztreeObj.updateNode(treeNode);
        initTbr(treeNode);
        HotUtil.workType = '';
        if (treeNode['SAVE_DATA']) {
            HotUtil.workType = JSON.parse(treeNode['SAVE_DATA']).workType || '';
        }
        $(".table-security").text(HotUtil.getSecurityName(treeNode.SECURITY));
        if (HotUtil.isPdf(treeNode)) {
            tableSel.css({
                "padding": "0px 0px",
            });
            tableSel.append('<iframe id="iframe"></iframe>');
            var ifHeight = tableSel.parent().height() - $("#tbr").height() - 5;
            if (treeNode.TYPE.indexOf('table') > -1) {
                ifHeight = ifHeight - 40;
            }
            $("#iframe").css({
                "height": ifHeight + "px",
                "border": "none",
                "width": "100%"
            });
            $("#iframe").attr("src", "/File" + treeNode.FILE_PATH);
        } else {
            if (html != "") {
                //添加表格
                var $table = $(html.replaceAll("\n", "<br>"));
                HotUtil.delTableImg($table, res.data.IS_ELECTRIC_TEST === 1);
                //设置表格的class 区别显示的表格 表示为存储数据的表格 因为他们的class都有 layui-table
                $table.addClass('data-table').show();
                tableSel.css({
                    "height": "auto",
                    "padding": "0px 15px",
                    "overflow": "visible"
                });

                if (res.data.TABLE_HEADER != "0" && res.data.TABLE_HEADER != undefined && res.data
                    .TABLE_HEADER != "" && res.data.TABLE_HEADER != "0-0") {
                    //如果存在表头的话 固定表头显示
                    var $thead = $('<thead class="sticky-thead"></thead>');
                    var top = "-1px";
                    if (treeNode.TYPE == 'report' || treeNode.TYPE.indexOf('table') > -1) {
                        // top = "-11px";
                    }
                    $thead.css({
                        "top": top,
                    });

                    var tableHeader = HotUtil.getTableHeader(res.data.TABLE_HEADER);
                    //处理表头的显示
                    $table.find("tr").each(function (i, n) {
                        if (i <= (tableHeader.max - 1) && i >= (tableHeader.min - 1)) {
                            // $(n).css({
                            // 	"font-weight": "bold",
                            // 	"background-color": "#e6e6e6"
                            // });
                            $thead.append($(n).clone(true));
                            $(n).remove();
                        }
                    });
                    $table.prepend($thead);
                }
                var colWidths = JSON.parse(treeNode.SAVE_DATA).colWidths || [];
                if (colWidths.length > 0) {
                    var $colgroup = $('<colgroup></colgroup>');
                    for (var i = 0; i < colWidths.length; i++) {
                        $colgroup.append('<col width="' + colWidths[i] + '">');
                    }
                    $table.prepend($colgroup);
                }
                tableSel.append($table);

                // 在表格锁定状态下隐藏签署框样式
                if (treeNode.TABLE_STATUS === 'sign') {
                    $table.find('td.sign-box').removeClass('sign-box');
                }

                tableSel.find(".layui-table tbody tr:hover").css("background-color", "");
                $table.find("tr").each(function (i, n) {
                    HotUtil.trContextMenu($(n), treeNode, tableSel, pageType);
                });

                if (treeNode.TABLE_STATUS == 'sign') {
                    $(".layui-table td").each(function (i, n) {
                        HotUtil.tdContextSignMenu(n, treeNode, tableSel, false, pageType);
                    });
                }
                $table.find("td").each(function (i, n) {
                    var comment = $(n).attr("comment");
                    if (comment) {
                        $(n).mouseover(function () {
                            layer.tips(comment, this);
                        });

                        $(n).mouseout(function () {
                            layer.closeAll();
                        });
                    }
                });
                HotUtil.tableAddLink(treeNode, $table);
            } else {
                tableSel.append('<span style="color:red;"> 请先编辑表格！</span>');
            }
        }
    } else {
        tableSel.hide();
        $("#msg").text(res.msg).show();
    }
};

/**
 * 处理表格中图片的显示
 * @param {jQuery} $table
 * @param {boolean} isShowThumbnail 是否显示缩略图
 */
HotUtil.delTableImg = function ($table, isShowThumbnail) {
    $table.find("td").each(function (i, n) {
        var $td = $(n);
        if ($td.find("img[type='photo']").length > 0) {
            var imgs = [];
            var photoShowNums = [];
            $td.find("img[type='photo']").each(function (j, m) {
                var $img = $(m);
                var photoPath = $img.attr("src");
                var photoName = $img.attr("photoName");
                var photoShowNum = $img.attr("photoShowNum");
                var photoId = $img.attr("id");
                var photoformat = $img.attr("photoformat");
                var imgData = {
                    photoPath: photoPath,
                    photoName: photoName,
                    photoShowNum: photoShowNum,
                    photoId: photoId,
                    photoformat: photoformat
                };
                imgs.push(imgData);
                photoShowNums.push(photoShowNum);

                //删除元素
                var $br1 = $img.prev();
                var $br2 = $img.next();
                var $span = $br2.next();
                if (!isShowThumbnail) {
                    $br1.remove();
                    $br2.remove();
                    $span.remove();
                    $img.remove();
                } else {
                    $br2.remove();
                    $span.remove();
                }
            });
            $td.data("imgs", imgs);
            $td.data("hasImg", true);
            if (!isShowThumbnail) {
                var numText = HotUtil.dealImgNumShow(photoShowNums);
                $td.append(numText);
            }
        } else {
            $td.data("hasImg", false);
        }
    });
};

/**
 * 处理图片编号的显示
 * @param {Array<string>} photoShowNums
 * @returns {string}
 */
HotUtil.dealImgNumShow = function (photoShowNums) {
    //表a1-12
    var tableNum = photoShowNums[0].substr(0, photoShowNums[0].lastIndexOf('-'));
    if (tableNum) {
        tableNum = tableNum + "-";
    }

    function getNum(photoShowNum) {
        //表a1-12-图8
        var tempArr = photoShowNum.split("-");
        var num = parseInt(tempArr[tempArr.length - 1].substr(1));
        return num;
    }

    var arr = [];

    for (var i = 0; i < photoShowNums.length; i++) {
        var photoShowNum = photoShowNums[i];
        var num = getNum(photoShowNum);
        arr.push(num);
    }


    // 定义一个新数组用于存储结果
    var newArr = [];

    // 使用for循环遍历原始数组
    for (var i = 0; i < arr.length; i++) {
        // 定义一个临时数组用于存储连续数字
        var tempArr = [arr[i]];

        // 使用while循环查找连续数字
        while (arr[i + 1] - arr[i] === 1) {
            tempArr.push(arr[i + 1]);
            i++;
        }
        // 将结果推入新数组
        newArr.push(tempArr);
    }

    var textArr = [""];

    for (var i = 0; i < newArr.length; i++) {
        var arr = newArr[i];
        if (arr.length == 1) {
            textArr.push(tableNum + "图" + arr[0]);
        } else {
            textArr.push(tableNum + "图" + arr[0] + "~" + "图" + arr[arr.length - 1]);
        }
    }
    var span = '<span style="color:red;">' + textArr.join("<br>") + '</span>'
    return span;
};

/**
 * 表格增加跳转连接
 * @param {Object} treeNode
 * @param {jQuery} $table
 */
HotUtil.tableAddLink = function (treeNode, $table) {
    //如果表为A表 则需要在表中显示B表的超链接 根据B表的表序号来识别
    if (treeNode.TYPE == 'a' || treeNode.TYPE == 'report') {
        //首先查询型号下的所有表的信息
        var cb_success = function (res) {
            if (res.success) {
                var bNodes = res.data;
                for (var i = 0; i < bNodes.length; i++) {
                    var bNode = bNodes[i];
                    $table.find("td").each(function (j, td) {
                        if ($(td).text().indexOf(bNode.TABLE_NUM) > -1) {
                            var locationArr = [];
                            $(td).find(".location-text").each(function (x, span) {
                                locationArr.push({
                                    id: $(span).attr("refid"),
                                    num: $(span).text()
                                });
                                $(span).text('');
                            });
                            var spanHtml =
                                '<span class="location-text" style="color:blue;cursor:pointer;text-decoration:underline"  refid="' +
                                bNode.ID + '">' + bNode.TABLE_NUM + '</span>';
                            $(td).html($(td).html().replaceAll(bNode.TABLE_NUM, spanHtml));
                            for (var x = 0; x < locationArr.length; x++) {
                                $(td).find('span[refid=' + locationArr[x].id + ']').text(
                                    locationArr[x].num);
                            }
                        }
                    });
                }
                $(".location-text").click(function (e) {
                    HotUtil.locationTreeNode($(e.target).attr("refid"));
                });
            } else {
                layer.alert(res.msg, {
                    icon: 2
                });
            }
        }
        //请求失败的回调
        var cb_error = function (xhr, textStatus, errorThrown) {

        };
        var service = "QueryModelNodesById";
        if (treeNode.TYPE == 'report') {
            service = 'QueryProcessNodesById';
        }
        twxAjax(THING, service, {
            id: treeNode.ID
        }, true, cb_success, cb_error);
    }
};

/**
 * 定位在线确认树节点
 * @param {string} nodeId
 */
HotUtil.locationTreeNode = function (nodeId) {
    var cb_success = function (res) {
        if (res.success) {
            locationTreeNodeUtil(res.data, ztreeObj, 'ID', function (thisNode) {
                loadTreeMenu();
                reloadTable(thisNode);
            });
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }
    //请求失败的回调
    var cb_error = function (xhr, textStatus, errorThrown) {

    };
    twxAjax(THING, "QueryAllPId", {
        id: nodeId
    }, true, cb_success, cb_error);
};

/**
 * 滚动到指定元素
 * @param {Object} scrollObj
 * @param {jQuery} parentSelector
 */
HotUtil.scrollEle = function (scrollObj, parentSelector) {
    if (scrollObj && scrollObj.isScroll) {
        var $scrollEle = parentSelector.find(' td[row="' + scrollObj.row + '"][col="' + scrollObj.col +
            '"]');
        parentSelector.animate({
            scrollTop: $scrollEle.offset().top - 400
        }, 500);
    }
};