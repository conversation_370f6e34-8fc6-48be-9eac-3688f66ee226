/**
 * 产品结构树查询中的质量数据查询
	selTreeId 产品结构树的ID
 */
var layer;
layui.use(['layer'], function() {
	layer = layui.layer;
});

function QualityData(treeId) {
	var THING = 'Thing.Fn.ProductDataQuery';
	var othis = this;
	//加载质量数据类型下拉框
	this.loadTypeSelect = function() {
		$('#productTypeDiv').show();
		var cb_success = function(res) {
			if (res.success) {
				var types = res.data;
				if (types.length > 0) {
					$('#productType').combobox({
						data: types,
						valueField: 'TREE_NAME',
						textField: 'TREE_NAME',
						editable: false,
						width: 300,
						panelHeight: 400,
						onSelect: function(record) {
							othis.showSecondTable();
							othis.initTbrBtn(record, treeId);
							window.secondTable = new SecondTable(record, treeId);
						}
					});
					$('#productType').combobox("select", types[0].TREE_NAME);
				} else {
					othis.showMsg('数据加载中！');
				}
			} else {
				othis.showMsg(res.msg);
			}
		}
		//请求失败的回调
		var cb_error = function(xhr, textStatus, errorThrown) {
			layer.alert("加载质量数据类型下拉框出错！", {
				icon: 2
			});
		};
		twxAjax(THING, "QueryQualityTypeByTreeId", {
			treeId: treeId
		}, false, cb_success, cb_error);
	};
	//显示页面的提示信息，并且隐藏页面其他元素
	this.showMsg = function(msg) {
		$("#msg").text(msg).show();
		$('#productTypeDiv').hide();
		$('#secondTableDiv').hide();
		$('#tbr').hide();
	};
	//显示二级表
	this.showSecondTable = function() {
		$("#msg").hide();
		$('#productTypeDiv').show();
		$('#secondTableDiv').show();
		$('#tbr').show();
	};
	//加载人员选择的下拉框
	this.loadUser = function() {
		var cb_success = function(data) {
			var ds = data.rows;
			$("#receiver").empty();
			$("#receiver").append('<option value="">请选择人员</option>');
			for (var i = 0; i < ds.length; i++) {
				var d = ds[i];
				$("#receiver").append('<option value="' + d.USER_NAME + '">' + d.USER_FULLNAME + "</option>");
			}
		};
		twxAjax('Thing.Fn.SystemManagement', 'getAllUser', {}, false, cb_success);
	};
	//初始化按钮
	this.initTbrBtn = function(record, treeId) {
		//导出二级表按钮点击事件
		$('#product-quality-export').unbind("click").bind('click', function() {

			var loading;
			var url = fileHandlerUrl + "/table/second/export";
			$.fileDownload(url, {
				httpMethod: 'POST',
				data: {
					"productTreeId": treeId,
					"tableId": record.ID,
					"dlwIsAll": 2,
					"query": JSON.stringify({
						"queryUser": sessionStorage.getItem('username')
					})
				},
				prepareCallback: function(url) {
					loading = layer.msg("正在导出...", {
						icon: 16,
						shade: 0.3,
						time: 0
					});
				},
				abortCallback: function(url) {
					layer.close(loading);
					layer.msg("导出异常！！");
				},
				successCallback: function(url) {
					layer.close(loading);
				},
				failCallback: function(html, url) {
					layer.close(loading);
					layer.msg("导出失败！！");
				}
			});

		});
		//数据推送
		$('#push').unbind("click").bind('click', function() {
			var sels = $('#secondTable').datagrid('getSelections');
			if (sels.length > 0) {
				var dataIdArr = [];
				var existPush = false;
				for (var i = 0; i < sels.length; i++) {
					dataIdArr.push(sels[i]['ID']);
					if (sels[i]['STATUS'] == '已推送') {
						existPush = true;
						break;
					}
				}
				if (existPush) {
					layer.alert("请选择未推送的数据!", {
						icon: 2,
					});
					return false;
				}
				var dataIds = dataIdArr.join(',');

				layui.config({
					base: "../../plugins/layui/define/",
				}).extend({
					multiSelect: 'multiSelect'
				}).use(["multiSelect", "form"], function() {
					var multiSelect = layui.multiSelect,
						form = layui.form;
					var pushLayerIndex = layer.open({
						title: '数据推送',
						type: 1,
						anim: false,
						openDuration: 200,
						isOutAnim: false,
						closeDuration: 200,
						shadeClose: false,
						maxmin: false,
						resize: false, //不允许拉伸
						area: ['450px', '280px'],
						content: '<div id="pushContent" style="padding-top: 15px;padding-right: 15px;"></div>',
						btn: ['推送', '取消'],
						yes: function() {
							$('#push-form-submit').click();
						},
						btn2: function() {
							return true;
						},
						success: function() {
							/* 弹窗不加载滚动条 */
							$("#pushContent").parent().css('overflow', 'visible');
							$("#pushContent").append($("#pushHtml")[0].innerHTML);
							othis.loadUser();
						}
					});
					form.render(null, 'pushForm');
					multiSelect.render();

					//监听提交
					layui.form.on("submit(push)", function(data) {

						var cb_success = function(json) {
							console.log(json);
							layer.closeAll();
							layer.msg("推送成功");
							secondTable.queryDataByPage(secondTable.pageOptions.pageSize, secondTable.pageOptions.pageNumber);
						};
						var cb_error = function(xhr) {
							layer.alert("推送失败!", {
								icon: 2,
							});
						};
						var tableId = sels[0]["TABLE_CONFIG_ID"];
						var receiver = $("#receiver").val() == null ? '' : $("#receiver").val().join(",");
						if (receiver == "") {
							layer.alert("请选择确认人员!", {
								icon: 2,
							});
							return false;
						}
						var params = {
							table_config_id: tableId,
							pusher: sessionStorage.getItem('username'),
							table_data_id: dataIds,
							receiver: receiver,
							remark: data.field.remark
						};
						twxAjax(THING, "AddPush", params, true, cb_success, cb_error);
						return false;
					});
				})
			} else {
				layer.alert('请选择需要推送的数据！');
			}
		});

		//数据确认
		$('#confirm').unbind("click").bind('click', function() {

		});
	};
	this.loadTypeSelect();
}

//二级表对象
var SecondTable = function(record, treeId) {
	var THING = 'Thing.Fn.ProductDataQuery';
	var othis = this;
	this.tableId = "secondTable";
	this.cmenu = undefined;
	this.pageOptions = {
		pageSize: 30,
		pageNumber: 1
	};
	this.renderTable = function() {
		var gridHeight = windowH - 90;
		var cb_success = function(data) {
			if (data.success) {
				var dealCol = dealColumns(JSON.parse(data.result));
				$('#secondTable').datagrid({
					data: [],
					columns: dealCol.col,
					height: gridHeight,
					singleSelect: false,
					remoteSort: false,
					pagination: true,
					emptyMsg: '<div style="color:red; padding-left:15px;padding-top:10px;font-size:14px;text-align:left;">数据加载中！</div>',
					loadMsg: '正在加载数据...',
					striped: false,
					onDblClickRow: function(rowIndex, rowData) {
						twxAjax('Thing.Fn.ProductDataQuery', 'QueryParentsByTreeId', {
							treeId: rowData.PRODUCT_TREE_IDS.split(",")[0]
						}, true, function(data) {
							// locationTreeNode(data.rows);
						});
					},
					onHeaderContextMenu: function(e, field) {
						e.preventDefault();
						if (!othis.cmenu) {
							othis.cmenu = gridUtil.createColumnMenu(othis.tableId);
						}
						othis.cmenu.menu('show', {
							left: e.pageX,
							top: e.pageY
						});
					},
					onLoadSuccess: function(data) {
						var rows = data.rows;
						var $datagrid = $('#secondTable');
						if (rows.length > 0) {
							for (var i = 0; i < rows.length; i++) {
								var row = rows[i];
								var mergedInfo = row.mergedInfo;
								if (mergedInfo != "" && mergedInfo != undefined) {
									var mergeds = mergedInfo.split(",");
									for (var j = 0; j < mergeds.length; j++) {
										var merged = mergeds[j];
										var columnName = merged.split(":")[0];
										var rowspan = merged.split(":")[1];
										$datagrid.datagrid('mergeCells', {
											index: i,
											field: columnName,
											rowspan: rowspan
										});
									}
								}
							}
						}
						changeWidth('secondTable');
						$("#secondTableDiv .datagrid-body").css("overflow-x", "auto");
						$('#' + othis.tableId).datagrid('loaded');
					}
				});
			} else {
				layer.alert("表头获取失败！", {
					icon: 2
				});
			}
		}

		var cb_error = function() {
			layer.alert("表头获取失败！", {
				icon: 2
			});
		};

		var parmas = {
			id: record.ID
		};
		twxAjax('Thing.Fn.SecondTable', 'GetSecondTableHeader', parmas, false, cb_success, cb_error);
	};
	//初始化分页组件
	this.initPagination = function(data) {
		$('#' + othis.tableId).datagrid('getPager').pagination({
			total: data.total,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: 1,
			buttons: [{
				iconCls: 'icon-refresh',
				handler: function() {
					othis.queryDataByPage(othis.pageOptions.pageSize, othis.pageOptions.pageNumber);
				}
			}],
			pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
			showPageList: true,
			showRefresh: false,
			onSelectPage: function(pageNumber, pageSize) {
				//当页码发生改变的时候进行调用
				othis.pageOptions.pageNumber = pageNumber;
				othis.queryDataByPage(pageSize, pageNumber);
			},
			onBeforeRefresh: function(pageNumber, pageSize) {
				//返回false可以在取消刷新操作
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
			},
			onRefresh: function(pageNumber, pageSize) {
				//alert("pageNumeber:"+pageNumber+", pageSize:"+pageSize);
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			},
			onChangePageSize: function(pageSize) {
				//改变pageSize时触发
				othis.pageOptions.pageSize = pageSize;
				othis.queryDataByPage(pageSize, othis.pageOptions.pageNumber);
			}
		})
	};
	this.totalRecords = 0;
	this.dataLoadFlag = false;
	this.pageLoadFlag = false;
	this.paginationShow = function() {
		$('#' + othis.tableId).datagrid('getPager').pagination('refresh', {
			total: othis.totalRecords,
			pageSize: othis.pageOptions.pageSize,
			pageNumber: othis.pageOptions.pageNumber
		});
	};
	//初始化全部的记录条数
	this.initTotalRecords = function() {
		//查询所有的记录条数
		//初始化分页框架
		var cb_success = function(data) {
			othis.pageLoadFlag = true;
			othis.totalRecords = data.rows[0].result;
			if (othis.dataLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function() {};

		var parmas = {
			table_config_id: record.ID,
			table_config_name: record.TREE_NAME,
			productTreeId: treeId,
			type: record.TYPE,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataCount', parmas, true, cb_success, cb_error);
	};

	//分页查询数据
	this.queryDataByPage = function(pageSize, pageNumber) {
		othis.totalRecords = 0;
		othis.dataLoadFlag = false;
		othis.pageLoadFlag = false;
		$('#' + othis.tableId).datagrid('loading');
		othis.initTotalRecords();
		var cb_success = function(data) {
			othis.dataLoadFlag = true;
			//调用成功后，渲染数据
			$('#' + othis.tableId).datagrid('loadData', data.array);
			if (othis.pageLoadFlag) {
				othis.paginationShow();
			}
		};
		var cb_error = function() {
			layer.alert('加载出错...', {
				icon: 2
			});
		};
		var parmas = {
			table_config_id: record.ID,
			table_config_name: record.TREE_NAME,
			productTreeId: treeId,
			type: record.TYPE,
			pageSize: pageSize,
			pageNumber: pageNumber,
			query: {
				queryUser: sessionStorage.getItem('username')
			}
		};
		//初始化表格后调用Ajax进行数据的加载显示
		twxAjax('Thing.Fn.SecondTable', 'QueryTableDataPage', parmas, true, cb_success, cb_error);
	};

	this.renderTable();
	//初始化分页组件
	this.initPagination({
		total: 0
	});
	//显示第一页的数据
	this.queryDataByPage(this.pageOptions.pageSize, this.pageOptions.pageNumber);
}