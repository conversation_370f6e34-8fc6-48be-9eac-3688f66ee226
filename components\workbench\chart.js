function PieChart() {
	var othis = this;
	this.option = {
		tooltip: {
			trigger: 'item'
		},
		series: [{
			type: 'pie',
			radius: '50%',
			data: [{
					value: 1048,
					name: 'XXB01'
				},
				{
					value: 735,
					name: 'XXB02'
				},
				{
					value: 580,
					name: 'XXB03'
				},
				{
					value: 484,
					name: 'XXB04'
				},
				{
					value: 300,
					name: 'XXB05'
				},
				{
					value: 50,
					name: 'XXB06'
				},
				{
					value: 475,
					name: 'XXB07'
				},
				{
					value: 368,
					name: 'XXB08'
				},
				{
					value: 1256,
					name: 'XXB09'
				},
				{
					value: 400,
					name: 'XXB010'
				}
			],
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowOffsetX: 0,
					shadowColor: 'rgba(0, 0, 0, 0.5)'
				}
			}
		}]
	};

	this.elId = "pie-chart";
	this.showMsg = function(msg) {
		$('#' + othis.elId).append('<span style="color:red">' + msg + '<span>');
		$.mask_close('#' + othis.elId);
	};
	this.loadChart = function() {
		$.mask_element('#' + othis.elId, '正在统计数据中...');
		twxAjax(thingName, 'QueryModelCount', '', true, function(res) {
			if (res.success) {
				othis.chartDom = document.getElementById(othis.elId);
				othis.chart = echarts.init(othis.chartDom);
				othis.option.series[0].data = res.data;
				othis.chart.setOption(othis.option);
				$.mask_close('#' + othis.elId);
			} else {
				othis.showMsg(res.msg);
			}
		}, function(e) {
			othis.showMsg("加载出错！");
		});
	}
	this.loadChart();
}

function LineChart() {
	var othis = this;
	this.option = {
		tooltip: {
			trigger: 'axis'
		},
		xAxis: {
			type: 'category',
			axisLabel: {
				rotate: 45
			},
			data: []
		},
		yAxis: {
			type: 'value'
		},
		series: [{
			data: [],
			lineStyle: {
				color: '#63B7EC'
			},
			label: {
				show: true,
				position: 'top',
				textStyle: {
					color: 'black'
				}
			},
			itemStyle: {
				color: '#63B7EC'
			},
			type: 'line'
		}]
	};

	this.elId = "line-chart";
	this.showMsg = function(msg) {
		$('#' + othis.elId).append('<span style="color:red">' + msg + '<span>');
		$.mask_close('#' + othis.elId);
	};
	this.loadChart = function() {
		$.mask_element('#' + othis.elId, '正在统计数据中...');
		twxAjax(thingName, 'QueryDateCount', '', true, function(data) {
			var dates = [];
			var values = [];
			for (var i = data.rows.length - 1; i >= 0; i--) {
				dates.push(data.rows[i].MYDATE);
				values.push(data.rows[i].COUNT);
			}
			othis.chartDom = document.getElementById(othis.elId);
			othis.chart = echarts.init(othis.chartDom);
			othis.option.xAxis.data = dates;
			othis.option.series[0].data = values;
			othis.chart.setOption(othis.option);
			$.mask_close('#' + othis.elId);
		}, function(e) {
			othis.showMsg("加载出错！");
		});
	}
	this.loadChart();
}

function BarChart1() {
	var othis = this;
	this.option = {
		tooltip: {},
		xAxis: {
			type: 'category',
			data: ['XXB015', 'XXB017', 'XXB018', 'XXB019', 'XXB020', 'XXB033', 'XXB025', 'XXB011', 'XXB012', 'XXB013'],
			axisLabel: {
				rotate: 45
			}
		},
		yAxis: {
			type: 'value'
		},
		series: [{
			data: [120, 200, 150, 80, 70, 110, 130, 44, 88, 245],
			type: 'bar',
			label: {
				show: true,
				position: 'top',
				textStyle: {
					color: 'black'
				}
			},
			itemStyle: {
				color: '#63B7EC'
			},
			barWidth: 25
		}]
	};
	othis.chartDom = document.getElementById('bar-chart1');
	othis.chart = echarts.init(othis.chartDom);
	othis.chart.setOption(othis.option);
}

function BarChart2() {
	var othis = this;

	//加载型号下拉框
	twxAjax(thingName, "QueryModel", {}, true, function(modelData) {
		$("#chart-model").empty();
		$("#chart-model").append('<option value="">请选择型号</option>');
		if (modelData.rows.length > 0) {
			for (var i = 0; i < modelData.rows.length; i++) {
				var modelRow = modelData.rows[i];
				var modelName = modelRow.NODENAME;
				var $modelOption = $('<option value="' + modelName + '">' + modelName + '</option>');
				$modelOption.data('datas', modelRow);
				$("#chart-model").append($modelOption);
			}
			layui.form.render('select', 'search-form');
			layui.form.on('select(chart-model)', function(data) {
				var modeRow = $(data.elem).find('option:selected').data('datas');
				//加载型号下拉框
				twxAjax(thingName, "QueryPhase", {
					parentId: modeRow.TREEID
				}, true, function(phausData) {
					$("#chart-phaus").empty();
					$("#chart-phaus").append('<option value="">请选择阶段</option>');
					if (phausData.rows.length > 0) {
						for (var i = 0; i < phausData.rows.length; i++) {
							var phausRow = phausData.rows[i];
							var phausName = phausRow.NODENAME;
							var $phausOption = $('<option value="' + phausName + '">' + phausName + '</option>');
							$phausOption.data('datas', phausRow);
							$("#chart-phaus").append($phausOption);
						}
						layui.form.render('select', 'search-form');
						layui.form.on('select(chart-phaus)', function(data) {
							var phausRow = $(data.elem).find('option:selected').data('datas');
							console.log(phausRow);
						});
					} else {
						$("#chart-model").append('<option value="">暂无阶段可选</option>');
					}
				}, function(data) {
					$("#chart-model").append('<option value="">型号阶段错误</option>');
				});
			});
		} else {
			$("#chart-model").append('<option value="">暂无型号可选</option>');
		}
	}, function(data) {
		$("#chart-model").append('<option value="">型号获取错误</option>');
	});

	this.option = {
		legend: {
			bottom: 5
		},
		tooltip: {},
		dataset: {
			source: [
				['product', '数据包总数量', '照片数量'],
				['结构装配', 1500, 600],
				['精测数据', 500, 200],
				['整星热控实施', 3528, 874],
				['电测试', 2543, 232],
				['电测试前总装', 3567, 258],
				['OSR粘贴', 364, 25],
				['其他展开', 1845, 999]
			]
		},
		xAxis: {
			type: 'category'
		},
		yAxis: {},
		series: [{
				type: 'bar',
				label: {
					show: true,
					position: 'top',
					textStyle: {
						color: 'black'
					}
				},
				itemStyle: {
					color: '#63B7EC'
				},
				barWidth: 30
			},
			{
				type: 'bar',
				label: {
					show: true,
					position: 'top',
					textStyle: {
						color: 'black'
					}
				},
				itemStyle: {
					color: '#434348'
				},
				barWidth: 30
			}
		]
	};
	othis.chartDom = document.getElementById('bar-chart2');
	othis.chart = echarts.init(othis.chartDom);
	othis.chart.setOption(othis.option);
}

function BarChart3() {
	var othis = this;
	this.option = {
		tooltip: {},
		xAxis: {
			type: 'category',
			data: []
		},
		yAxis: {
			name: '质量信息表（单位：个）',
			nameRotate: 90,
			nameGap: 60,
			nameLocation: 'center',
			nameTextStyle: {
				fontSize: 14
			},
			type: 'value'
		},
		series: [{
			data: [],
			type: 'bar',
			label: {
				show: true,
				position: 'top',
				textStyle: {
					color: 'black'
				}
			},
			itemStyle: {
				color: '#63B7EC'
			},
			barWidth: 40
		}]
	};
	this.elId = "bar-chart3";
	this.showMsg = function(msg) {
		$('#' + othis.elId).append('<span style="color:red">' + msg + '<span>');
		$.mask_close('#' + othis.elId);
	};
	this.loadChart = function() {
		$.mask_element('#' + othis.elId, '正在统计数据中...');
		twxAjax(thingName, 'QueryQualityCount', '', true, function(res) {
			if (res.success) {
				othis.chartDom = document.getElementById(othis.elId);
				othis.chart = echarts.init(othis.chartDom);
				othis.option.xAxis.data = res.data.name;
				othis.option.series[0].data = res.data.value;
				othis.chart.setOption(othis.option);
				$.mask_close('#' + othis.elId);
			} else {
				othis.showMsg(res.msg);
			}

		}, function(e) {
			othis.showMsg("加载出错！");
		});
	}
	this.loadChart();
};
