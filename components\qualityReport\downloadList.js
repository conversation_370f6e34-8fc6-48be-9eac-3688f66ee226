/**
 * <AUTHOR>
 * @datetime 2025年4月2日10:47:57
 * @function	downloadList
 * @description	下载列表
 */

/**
 * 质量影像记录下载列表
 * @param {string} tableConfigId - 表配置ID
 * @param {string} treeId - 树ID
 * @param {string} type_ - 类型
 */     
function qualityDownloadList(tableConfigId, treeId, type_) {
    layer.open({
        title: "文件下载列表",
        type: 1,
        anim: false,
        openDuration: 200,
        isOutAnim: false,
        closeDuration: 200,
        shadeClose: false,
        maxmin: true,
        resize: false, //不允许拉伸
        area: ['1200px', '700px'],
        scrollbar: false,
        content: '<div id="quality-download-table"></div>',
        success: function () {
            table.render({
                elem: '#quality-download-table',
                id: 'quality-download-table',
                url: getUrl('Thing.Fn.SecondTable', 'QueryDownloadTable'),
                where: {
                    creator: sessionStorage.getItem("username"),
                    tableConfigId: tableConfigId,
                    treeId: treeId,
                    type_: type_
                },
                height: 650, // 最大高度减去其他容器已占有的高度差
                cellMinWidth: 80,
                page: {
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
                },
                cols: [
                    [{
                        title: '序号',
                        type: "numbers",
                        width: 60
                    },
                    {
                        field: 'DWONLOAD_ID',
                        hide: true
                    },
                    {
                        field: 'FILE_NAME',
                        width: 300,
                        title: '文件名称'
                    },
                    {
                        field: 'EXPORT_TYPE',
                        title: '文件类型',
                        width: 150,
                        templet: function (d) {
                            var html = "";
                            if (d.EXPORT_TYPE == 1) {
                                html =
                                    '<span class="layui-badge layui-bg-blue">质量影像记录表</span>';
                            } else if (d.EXPORT_TYPE == 2) {
                                html =
                                    '<span class="layui-badge layui-bg-green">所有照片</span>';
                            } else if (d.EXPORT_TYPE == 3) {

                            }
                            return html;
                        },
                        align: 'center'
                    },
                    {
                        field: 'START_TIME',
                        width: 152,
                        title: '提交时间',
                        align: 'center'
                    },
                    {
                        field: 'IS_COMPLETE',
                        title: '是否完成',
                        width: 90,
                        minWidth: 90,
                        templet: function (d) {
                            var html = "";
                            if (d.IS_COMPLETE == 0) {
                                html =
                                    '<span class="layui-badge layui-bg-blue">进行中</span>';
                            } else if (d.IS_COMPLETE == 1) {
                                html =
                                    '<span class="layui-badge layui-bg-green">已完成</span>';
                            } else if (d.IS_COMPLETE == 2) {
                                html =
                                    '<span class="layui-badge layui-bg-red show-msg" title="点击查看原因" msg="' +
                                    d.MSG + '">生成失败</span>';
                            }
                            return html;
                        },
                        align: 'center'
                    },
                    {
                        field: 'END_TIME',
                        title: '完成时间',
                        width: 152,
                        align: 'center'
                    },
                    {
                        field: 'IS_DOWNLOAD',
                        title: '是否下载',
                        width: 85,
                        templet: function (d) {
                            var html = "";
                            if (d.IS_DOWNLOAD == 0) {
                                html =
                                    '<span class="layui-badge layui-bg-blue">未下载</span>';
                            } else if (d.IS_DOWNLOAD == 1) {
                                html =
                                    '<span class="layui-badge layui-bg-green">已下载</span>';
                            }
                            return html;
                        },
                        align: 'center'
                    },
                    {
                        fixed: 'right',
                        title: '操作',
                        width: 100,
                        minWidth: 100,
                        toolbar: `<div class="layui-clear-space">
										<a class="layui-btn layui-btn-xs" lay-event="download">下载</a>
									</div>`,
                        align: 'left'
                    }
                    ]
                ],
                done: function () {
                    $(".show-msg").off('click').on('click', function () {
                        layer.alert($(this).attr("msg"));
                    });
                },
                error: function (res, msg) {
                    console.log(res, msg)
                }
            });

            // 工具栏事件
            table.on('tool(quality-download-table)', function (obj) {
                var data = obj.data; // 获得当前行数据
                if (obj.event === 'download') {
                    if (data.FILE_PATH) {
                        var filePath = "//" + data.FILE_PATH;
                        filePath = filePath.replace(/\\/g, "/");
                        var fileName = data.FILE_NAME;
                        var url = fileHandlerUrl + "/system/open/file?filePath=" + filePath + "&fileName=" +
                            encodeURIComponent(fileName);

                        twxAjax('Thing.Fn.SecondTable', 'RecordDownloadFile', {
                            downloadId: data.ID
                        }, true, function (res) {
                            window.open(url);
                            table.reload('quality-download-table');
                        }, function () { });
                    } else {
                        layer.alert("文件还未生成，请稍后再试！", {
                            icon: 2
                        });
                    }
                }
            });
        }
    });
}

function reloadSelect(sucessFn, s_folder = '', s_model = '', s_phase = '', s_dir = '', s_leaf = '') {
    twxAjax(THING, 'QueryDownloadSearch', {
        creator: sessionStorage.getItem("username"),
        s_folder: s_folder,
        s_model: s_model,
        s_phase: s_phase,
        s_dir: s_dir,
        s_leaf: s_leaf
    }, true, function (res) {
        if (res.success) {
            var folders = res.data.folders;
            var models = res.data.models;
            var phases = res.data.phases;
            var dirs = res.data.dirs;
            var leafs = res.data.leafs;
            if (s_folder == '') {
                $("#s_folder").empty().append('<option value=""></option>');
                for (var i = 0; i < folders.length; i++) {
                    $("#s_folder").append('<option value="' + folders[i] + '">' + folders[i] + '</option>');
                }
            }

            if (s_model == '') {
                $("#s_model").empty().append('<option value=""></option>');
                for (var i = 0; i < models.length; i++) {
                    $("#s_model").append('<option value="' + models[i] + '">' + models[i] + '</option>');
                }
            }

            if (s_phase == '') {
                $("#s_phase").empty().append('<option value=""></option>');
                for (var i = 0; i < phases.length; i++) {
                    if ('-' !== phases[i]) {
                        $("#s_phase").append('<option value="' + phases[i] + '">' + phases[i] + '</option>');
                    }
                }
            }
            if (s_dir == '') {
                $("#s_dir").empty().append('<option value=""></option>');
                for (var i = 0; i < dirs.length; i++) {
                    if ('-' !== dirs[i]) {
                        $("#s_dir").append('<option value="' + dirs[i] + '">' + dirs[i] + '</option>');
                    }
                }
            }
            if (s_leaf == '') {
                $("#s_leaf").empty().append('<option value=""></option>');
                for (var i = 0; i < leafs.length; i++) {
                    if ('-' !== leafs[i]) {
                        $("#s_leaf").append('<option value="' + leafs[i] + '">' + leafs[i] + '</option>');
                    }
                }
            }
            form.render(null, 'download-table-form');
            sucessFn();
        } else {
            layer.alert(res.msg, {
                icon: 2
            });
        }
    }, function (xhr, textStatus, errorThrown) {
        layer.alert('请求出错！', {
            icon: 2
        });
    });
}

/**
 * 弹出确认表下载列表
 */
function ejectDownloadTable() {
    var options = {};
    options.seachFormHtml = `<form class="layui-form search-form" lay-filter="download-table-form">
								<div class="layui-form-item">
									<div class="layui-inline">
										<label class="layui-form-label">分类</label>
										<div class="layui-input-inline">
											<select id="s_folder" name="s_folder" lay-filter="s_folder" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">型号</label>
										<div class="layui-input-inline">
											<select id="s_model" name="s_model" lay-filter="s_model" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">阶段</label>
										<div class="layui-input-inline">
											<select id="s_phase" name="s_phase" lay-filter="s_phase" lay-search></select>
										</div>
									</div>
								</div>
								<div class="layui-form-item">
									<div class="layui-inline">
										<label class="layui-form-label">专业</label>
										<div class="layui-input-inline">
											<select id="s_dir" name="s_dir" lay-filter="s_dir" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">过程</label>
										<div class="layui-input-inline">
											<select id="s_leaf" name="s_leaf" lay-filter="s_leaf" lay-search></select>
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">A表</label>
										<div class="layui-input-inline">
											<input type="text" name="s_a" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-inline">
										<button class="layui-btn layui-btn-sm" lay-submit lay-filter="download-table-search">搜索</button>
										<button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
									</div>
								</div>
							</form>`;
    options.cols = [{
        field: 'PHASE',
        width: 100,
        title: '阶段'
    }, {
        field: 'DIR',
        width: 170,
        title: '专业',
        templet: function (d) {
            return d.DIR == '-' ? "" : d.DIR;
        }
    }, {
        field: 'LEAF',
        width: 170,
        title: '阶段',
        templet: function (d) {
            return d.LEAF == '-' ? "" : d.LEAF;
        }
    }];
    options.tableHeight = 560;
    options.tableWidth = 1750;
    HotUtil.ejectDownloadTable(options);
}

/**
 * 加载搜索表单
 */
function renderSearchForm() {
    reloadSelect(function () {
        form.on('select(s_folder)', function (data) {
            var value = data.value;
            reloadSelect(function () { }, value);
        });

        form.on('select(s_model)', function (data) {
            var value = data.value;
            reloadSelect(function () { }, $("#s_folder").val(), value);
        });

        form.on('select(s_phase)', function (data) {
            var value = data.value;
            reloadSelect(function () { }, $("#s_folder").val(), $("#s_model").val(), value);
        });

        form.on('select(s_dir)', function (data) {
            var value = data.value;
            reloadSelect(function () { }, $("#s_folder").val(), $("#s_model").val(), $("#s_phase").val(),
                value);
        });

        // 搜索提交
        form.on('submit(download-table-search)', function (data) {
            var field = data.field; // 获得表单字段
            field.creator = sessionStorage.getItem("username");
            // 执行搜索重载
            table.reload('download-table', {
                page: {
                    curr: 1 // 重新从第 1 页开始
                },
                where: field // 搜索的字段
            });
            return false;
        });
    });
}