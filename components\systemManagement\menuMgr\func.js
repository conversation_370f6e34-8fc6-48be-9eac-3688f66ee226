var funcThingName = "Thing.Fn.SystemManagement";

var contentFuncHtml = '';

var initFuncTableComp = function() {
	$('#funcTable').datagrid({
		data: [],
		singleSelect: false,
		striped: true,
		rownumbers: true,
		toolbar: '#funcTable_tb',
		fit: true,
		columns: [
			[{
				field: 'ck',
				checkbox: true
			}, {
				field: 'FUNC_NAME',
				title: '功能名称',
				width: 300
			}, {
				field: 'FUNC_BTN_ID',
				title: '功能ID',
				width: 300
			}, {
				field: 'FUNC_ID',
				title: '后台唯一ID',
				width: 150,
				hidden: true
			}]
		],
		emptyMsg: '<div style="width:100%;padding:10px 10px;text-align:left;font-size:14px;"><font color=red>数据加载中...</font></div>',
		loadMsg: '正在加载数据...'
	});
};

var initFuncTableData = function() {
	$('#funcTable').datagrid('loading');

	// 调用ajax进行数据的加载
	var param = {};
	var sels = getSelectedData();
	param.menu_id = sels[0].MENU_ID;
	var cb_success = function(data) {
		$('#funcTable').datagrid('loadData', data.rows);
		$('#funcTable').datagrid('loaded');
	};
	var cb_error = function() {};
	twxAjax(funcThingName, 'QueryFuncsByMenuId', param, true, cb_success, cb_error);
};


var initFuncBtnAdd = function(layui) {
	$('#addfunc').bind('click', function() {
		var layer = layui.layer;
		var menuSels = getSelectedData();
		if (menuSels.length == 0) {
			layer.msg('请选择菜单!', {
				icon: 2,
				anim: 6
			});
			return;
		}

		//判断选择的菜单类型
		// if (menuSels[0].MENU_TYPE !== 'Menu') {
		// 	layer.msg('仅有类型为菜单的可以添加功能', {
		// 		icon: 2,
		// 		anim: 6
		// 	});
		// 	return;
		// }

		layer.open({
			title: '新增功能',
			type: 1,
			area: ['370px', '250px'],
			content: contentFuncHtml,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			btn: ['新增', '重置', '关闭'],
			yes: function() {
				$('#btn_func_submit').click();
			},
			btn2: function() {
				$('#btn_func_reset').click();
				return false;
			},
			btn3: function() {
				return true;
			}
		});
	});
};

var getFuncSelectedData = function() {
	var sels = $('#funcTable').datagrid('getSelections');
	return sels;
};

var initFuncBtnEdit = function(layui) {
	$('#editfunc').bind('click', function() {
		var layer = layui.layer,
			form = layui.form;
		var sels = getFuncSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择需要编辑的数据...', {
				icon: 2,
				anim: 6
			});
			return;
		}
		layer.open({
			title: '编辑功能',
			type: 1,
			area: ['370px', '250px'],
			content: contentFuncHtml,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			resize: false,
			btn: ['保存', '重置', '关闭'],
			yes: function() {
				$('#btn_func_update').click();
			},
			btn2: function() {
				$('#btn_func_reset').click();
				form.val('funcinfo', {
					funcid: sels[0].FUNC_ID,
					funcname: sels[0].FUNC_NAME,
					funcbtnid: sels[0].FUNC_BTN_ID
				});
				return false;
			},
			btn3: function() {
				return true;
			},
			success: function(layero, index) {
				form.val('funcinfo', {
					funcid: sels[0].FUNC_ID,
					funcname: sels[0].FUNC_NAME,
					funcbtnid: sels[0].FUNC_BTN_ID
				});
			}
		});
	});
};

var initFuncBtnRemove = function(layui) {
	$('#delfunc').bind('click', function() {
		var layer = layui.layer;
		var sels = getFuncSelectedData();
		if (sels.length == 0) {
			layer.msg('请选择需要删除的数据...', {
				icon: 2,
				anim: 6
			});
			return;
		}

		layer.confirm('确认删除选定的数据吗?', {
			icon: 3,
			title: '提示'
		}, function(index) {
			//调用Ajax进行删除
			var param = {};
			param.funcids = '';
			for (var i = 0; i < sels.length; i++) {
				param.funcids += ',' + sels[i].FUNC_ID;
			}
			param.funcids = param.funcids.substring(1);
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					return;
				}
				initFuncTableData();
				layer.closeAll();

				layer.msg('删除成功');
			};
			var cb_error = function() {};
			twxAjax(funcThingName, 'DeleteFuncByFuncID', param, true, cb_success, cb_error);
		});
	});
};

$(document).ready(function() {
	layui.use(['layer', 'form', 'jquery'], function() {
		var layer = layui.layer,
			form = layui.form;

		var htmlObj = $.ajax({
			url: 'tpl/funcInfo.html',
			async: false
		});
		contentFuncHtml = htmlObj.responseText;

		initFuncTableComp();

		form.on('submit(funcformVerify)', function(data) {
			var param = data.field;
			var sels = getSelectedData();
			param.menu_id = sels[0].MENU_ID;
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					return;
				}
				initFuncTableData();
				layer.closeAll();

				layer.msg('保存成功');
			};
			var cb_error = function() {};
			twxAjax(funcThingName, 'AddFunc', param, true, cb_success, cb_error);
			return false;
		});

		form.on('submit(funcupdateVerify)', function(data) {
			var param = data.field;
			var cb_success = function(data) {
				if (data.success === false) {
					layer.msg(data.message, {
						icon: 2,
						anim: 6
					});
					return;
				}
				initFuncTableData();
				layer.closeAll();

				layer.msg('更新成功');
			};
			var cb_error = function() {};
			twxAjax(funcThingName, 'UpdateFunc', param, true, cb_success, cb_error);
			return false;
		});

		initFuncBtnAdd(layui);
		initFuncBtnEdit(layui);
		initFuncBtnRemove(layui);
	});
});
