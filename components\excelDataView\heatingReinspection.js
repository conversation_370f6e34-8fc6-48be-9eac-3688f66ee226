var curInfo = {
	tableId: 'dataTable',
	tableType: '加热片入所复验',
	dpid: ''
};


$(document).ready(function() {
	layui.use('layer', function() {
		curInfo.dpid = parent.window.excelPid;
		//初始化easyui的表格
		initTableComp();

		//加载数据
		queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
	});
});
/** 开始搜索 */
var searchData = function() {

	pageOptions.pageNumber = 1;
	//搜索数据
	queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
};
/** 重置搜索条件 */
var resetSearchCondition = function() {
	$('#sa_model').textbox('setValue', '');
};

//初始化表格 - 
var initTableComp = function(layui) {
	$('#' + curInfo.tableId).datagrid({
		data: [],
		singleSelect: true,
		// fitColumns: true,
		striped: true,
		fit: true,
		rownumbers: true,
		pagination: true,
		columns: [
			[{
				field: 'ID',
				hidden: true
			}, {
				field: 'SERIAL_NUMBER',
				title: '序号',
				hidden: true
			}, {
				field: 'MODEL',
				title: '型号',
				align: 'center',
				width: 100
			}, {
				field: 'HEATING_PLATE_NAME',
				title: '加热片名称',
				align: 'center',
				width: 100
			}, {
				field: 'HEATING_PLATE_NUMBER',
				title: '加热片编号小编号',
				align: 'center',
				width: 130
			}, {
				field: 'RESISTANCE',
				title: '阻值',
				align: 'center',
				width: 100
			}, {
				field: 'INSULATION',
				title: '绝缘',
				align: 'center',
				width: 100
			}, {
				field: 'PHOTO_NUMBER',
				title: '照片号',
				align: 'center',
				width: 100
			}, {
				field: 'REMARK1',
				title: '备注1',
				align: 'center',
				width: 100
			}, {
				field: 'REMARK2',
				title: '备注2',
				align: 'center',
				width: 100
			}, {
				field: 'RESULTID',
				hidden: true
			}]
		],
		loadMsg: '正在加载数据...',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>'
	});

	//初始化分页控件
	initPagination(curInfo.tableId, []);
};


//分页信息
var pageOptions = {
	pageSize: 30,
	pageNumber: 1
};

var getFieldValue = function() {
	var param = {};
	param.type = curInfo.tableType;
	//数据包的ID
	param.dpid = curInfo.dpid;

	var sa_model = $('#sa_model').textbox('getValue');
	param.conditionData = {
		model: sa_model
	};

	return param;
};

//初始化行号
var initLineNumbers = function() {
	var rowNumbers = $('.datagrid-cell-rownumber');
	var start = (pageOptions.pageNumber - 1) * pageOptions.pageSize;
	$(rowNumbers).each(function(index) {
		var row = parseInt($(rowNumbers[index]).html()) + parseInt(start);
		$(rowNumbers[index]).html("");
		$(rowNumbers[index]).html(row);
	});
};

//初始化全部的记录条数
var initTotalRecords = function() {
	//查询所有的记录条数
	//初始化分页框架
	var cb_success = function(data) {
		//initPagination('logtable',{total:data.rows[0].COUNT});
		$('#' + curInfo.tableId).datagrid('getPager').pagination('refresh', {
			total: data.rows[0].COUNT,
			pageSize: pageOptions.pageSize,
			pageNumber: pageOptions.pageNumber
		});
		//重新初始化行号
		initLineNumbers();
	};
	var cb_error = function() {};

	//传递参数
	var param = getFieldValue();
	twxAjax('Thing.Fn.ExcelImport', 'QueryTotalNumbers', param, true, cb_success, cb_error);
};

//分页查询数据
var queryDataByPage = function(pageSize, pageNumber) {
	$('#' + curInfo.tableId).datagrid('loading');
	var cb_success = function(data) {
		//调用成功后，渲染数据
		$('#' + curInfo.tableId).datagrid('loadData', data.rows);
		initTotalRecords();
		$('#' + curInfo.tableId).datagrid('loaded');
		layui.use(['layer'], function() {
			var layer = layui.layer;
			$(".datagrid-view2>.datagrid-body .contentSpan").each(function(i, n) {
				var text = $(n).text();
				$(n).mouseenter(function() {
					var width = $(n).parent().width();
					layer.tips(text, n, {
						tips: [1, '#2983CF'], //还可配置颜色
						time: 0,
						area: width + 'px'
					});
				});
				$(n).mouseleave(function() {
					layer.close(layer.index);
				});
			})
		});
		// $('#root_layout').loading('stop');
	};
	var cb_error = function() {
		$('#' + curInfo.tableId).datagrid('loaded');
		layui.use(['layer'], function() {
			var layer = layui.layer;
			layer.alert('加载出错...', {
				icon: 2
			});
		});
	};
	//传递的参数
	var param = getFieldValue();
	param.pageSize = pageSize;
	param.pageNumber = pageNumber;
	//初始化表格后调用Ajax进行数据的加载显示
	twxAjax('Thing.Fn.ExcelImport', 'QueryExcelData', param, true, cb_success, cb_error);
};

//初始化分页组件
var initPagination = function(tableName, data) {
	$('#' + tableName).datagrid('getPager').pagination({
		total: data.total,
		pageSize: pageOptions.pageSize,
		pageNumber: 1,
		buttons: [{
			iconCls: 'icon-refresh',
			handler: function() {
				queryDataByPage(pageOptions.pageSize, pageOptions.pageNumber);
			}
		}],
		pageList: [10, 15, 20, 25, 30, 35, 40, 45, 50],
		showPageList: true,
		showRefresh: false,
		onSelectPage: function(pageNumber, pageSize) {
			//当页码发生改变的时候进行调用
			pageOptions.pageNumber = pageNumber;
			queryDataByPage(pageSize, pageNumber);
		},
		onBeforeRefresh: function(pageNumber, pageSize) {

		},
		onRefresh: function(pageNumber, pageSize) {
			queryDataByPage(pageSize, pageOptions.pageNumber);
		},
		onChangePageSize: function(pageSize) {
			//改变pageSize时触发
			pageOptions.pageSize = pageSize;
			queryDataByPage(pageSize, pageOptions.pageNumber);
		}
	});
};
