var $formula = $(".calc-formula");

var formulaInput = document.getElementById('calc-formula');
formulaInput.addEventListener('blur', function() {
	formulaInput.focus();
});

/**
 * 更新计算公式的card标题
 * @param {Object} resultName
 */
function formulaTitle(resultName) {
	if (resultName) {
		$("#formula-title").text('正在编辑`' + resultName + '`计算公式');
	} else {
		$("#formula-title").text('计算公式');
	}
}

/**
 * 设置当前计算公式对应的输出参数
 * @param {Object} resultData
 */
function setCurrentResult(resultData) {
	$("#formula-title").data("result", resultData);
}

/**
 * 设置当前计算公式对应的输出参数
 * @param {Object} resultData
 */
function getCurrentResult() {
	return $("#formula-title").data("result");
}

/**
 * 设置当前计算公式对应的输出参数的代码
 * @param {Object} resultData
 */
function getCurrentResultCode() {
	return getCurrentResult()['CODE'];
}

/**
 * 设置当前计算公式对应的输出参数的名称
 * @param {Object} resultData
 */
function getCurrentResultName() {
	return getCurrentResult()['NAME'];
}

/**
 * 设置当前计算公式对应的输出参数的ID
 * @param {Object} resultData
 */
function getCurrentResultId() {
	return getCurrentResult()['ID'];
}

/**
 * 设置当前计算公式对应的输出参数的公式
 * @param {Object} resultData
 */
function getCurrentResultFormula() {
	return getCurrentResult()['FORMULA'] || '';
}

/**
 * 隐藏计算公式模块 并给出信息提示
 * @param {Object} msg
 */
function hideFormula(msg) {
	$('#formula-div').hide();
	$("#formula-msg").text(msg).show();
	formulaTitle();
}

/**
 * 显示计算公式模块 并隐藏信息提示
 */
function showFormula() {
	$('#formula-div').show();
	$("#formula-msg").hide();
}

/**
 * 初始化公式计算模块
 */
function initFormula(resultData) {
	setCurrentResult(resultData);
	formulaTitle(getCurrentResultName());
	$formula.val(getCurrentResultFormula());
	showFormula();
	initFormulaClick();
}

/**
 * 初始化计算器按钮点击事件
 */
function initFormulaClick() {

	$('.calc-num').off('click').on('click', function() {
		var text = $(this).find('div').text();
		formulaAdd(text);
	});

	$('.calc-back').off('click').on('click', function() {
		formulaDelete();
	});

	$('.calc-clear').off('click').on('click', function() {
		clearFormula();
	});

	$('.calc-save').off('click').on('click', function() {
		saveFormula();
	});
}

/**
 * 清除计算公式
 */
function clearFormula() {
	var formula = $formula.val();
	if (formula) {
		layer.confirm('确认清除公式吗？', {
			icon: 3,
			title: '提示'
		}, function(index) {
			$formula.val("");
			layer.close(index);
		});
	}
}


/**
 * 校验公式的准确性 确保能正常运算
 */
function checkFormula(formula, successFn) {
	formula = replaceCalcSymbol(formula);
	var formatData = getCurrentParamAndResultData();
	for (var i = 0; i < formatData.length; i++) {
		var code = formatData[i]['CODE'];
		formula = formula.replaceAll('`' + code + '`', '1');
	}
	try {
		eval(formula);
		successFn();
	} catch (e) {
		layer.alert('公式校验不通过，请检查！', {
			icon: 2
		});
	}
}

/**
 * 保存公式
 */
function saveFormula() {
	var formula = $formula.val();
	checkFormula(formula, function() {
		twxAjax(THING, 'SaveResultFormula', {
			id: getCurrentResultId(),
			formula: formula,
			updateUser: sessionStorage.getItem("username")
		}, true, function(res) {
			if (res.success) {
				renderResultTable();
				layer.msg(res.msg)
			} else {
				layer.alert(res.msg);
			}
		}, function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	});
}

/**
 * 删除最后一个元素
 */
function formulaDelete() {
	var cursorPosition = formulaInput.selectionStart;
	var textBeforeCursor = formulaInput.value.substring(0, cursorPosition);
	var textAfterCursor = formulaInput.value.substring(cursorPosition);

	var lastChar = textBeforeCursor[textBeforeCursor.length - 1];
	textBeforeCursor = textBeforeCursor.substring(0, textBeforeCursor.length - 1);
	if (lastChar == '`') {
		textBeforeCursor = textBeforeCursor.substring(0, textBeforeCursor.lastIndexOf('`'));
	}
	$formula.val(textBeforeCursor + textAfterCursor);
	formulaInput.setSelectionRange(textBeforeCursor.length, textBeforeCursor.length);
}

/**
 * 往后面增加公式
 */
function formulaAdd(text) {
	var cursorPosition = formulaInput.selectionStart;
	var textBeforeCursor = formulaInput.value.substring(0, cursorPosition);
	var textAfterCursor = formulaInput.value.substring(cursorPosition);
	formulaInput.value = textBeforeCursor + text + textAfterCursor;
	formulaInput.setSelectionRange(cursorPosition + text.length, cursorPosition + text.length);
}