var renderTable = function() {
	$('#' + tableId).datagrid({
		data: [],
		toolbar: "#tableTable_tb",
		checkOnSelect: false,
		selectOnCheck: false,
		singleSelect: true,
		fit: true,
		frozenColumns: [
			[{
				field: 'ck',
				checkbox: true
			}, {
				field: 'TREE_NAME',
				title: '模板名称',
				width: 180,
				align: 'center'
			}]
		],
		columns: [
			[{
					field: 'TYPE',
					title: '二级表数据来源类型',
					width: 160,
					align: 'center',
					formatter: function(value, row, index) {
						// if (value == '1') {
						// 	return 'MES';
						// } else 
						if (value == '2') {
							return '三级表';
						} else if (value == '3') {
							return '二级表与三级表完全相同';
						} else if (value == '4') {
							return '二级表与三级表表头相同';
						} else if (value == '5') {
							return '结构化三级表';
						} else if (value == '6') {
							return '试验管控系统';
						}
					}
				},
				{
					field: 'MES_INTERFACE',
					title: 'MES接口',
					width: 160,
					align: 'center',
					formatter: function(value, row, index) {
						// if (row.TYPE != '1') {
						// 	return '<span style="color:red;">无需MES接口属性！</span>';;
						// }
						if (value == undefined || value == '' || value == null) {
							return '<span style="color:red;">暂不从MES同步数据！</span>';
						} else {
							return value;
						}
					}
				},
				{
					field: 'SECOND_FILEPATH',
					title: '二级表模板',
					width: 100,
					align: 'center',
					formatter: function(value, row, index) {
						var html = '';
						if (row.TYPE == '3' || row.TYPE == '4' || row.TYPE == '6') {
							html = '<span style="color:red;">无需上传！</span>';
						} else {
							if (value == undefined || value == '' || value == null) {
								html = '<span style="color:red;">暂未上传！</span>';
							} else {
								row.ftype = 2;
								var rowData = JSON.stringify(row).replace(/"/g, "\'");
								html =
									'<button type="button" class="layui-btn layui-btn-xs" onclick="viewTemplateFile(' +
									rowData +
									')">查看</button>';
								html +=
									'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="DownloadTemplateFile(' +
									rowData + ')">下载</button>';
							}
						}
						return html;
					}
				},
				{
					field: 'SECOND_DATA_ROWNUM',
					title: '二级表数据<br>起始行数',
					width: 80,
					align: 'center',
					formatter: function(value, row, index) {
						if (row.TYPE == '3' || row.TYPE == '6') {
							return '<span style="color:red;">无需填写！</span>';
						} else {
							return value;
						}
					}
				},
				{
					field: 'THREE_FILEPATH',
					title: '三级表模板',
					width: 90,
					align: 'center',
					formatter: function(value, row, index) {
						var html = '';
						if (row.TYPE == '3' || row.TYPE == '6') {
							return '<span style="color:red;">无需上传！</span>';
						}
						if (value == undefined || value == '' || value == null) {
							html = '<span style="color:red;">暂未上传！</span>';
						} else {
							row.ftype = 3;
							var rowData = JSON.stringify(row).replace(/"/g, "\'");
							html =
								'<button type="button" class="layui-btn layui-btn-xs" onclick="viewTemplateFile(' +
								rowData +
								')">查看</button>';
							html +=
								'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="DownloadTemplateFile(' +
								rowData + ')">下载</button>';
						}
						return html;
					}
				},
				{
					field: 'THREE_DATA_ROWNUM',
					title: '三级表数据<br>起始行数',
					width: 90,
					align: 'center',
					formatter: function(value, row, index) {
						if (row.TYPE != '5') {
							return '<span style="color:red;">无需填写！</span>';
						} else {
							return value;
						}
					}
				},
				{
					field: 'PLAN_FILEPATH',
					title: '策划表模板',
					width: 100,
					align: 'center',
					formatter: function(value, row, index) {
						var html = '';
						if (value == undefined || value == '' || value == null) {
							html = '<span style="color:red;">暂未上传！</span>';
						} else {
							row.ftype = 4;
							var rowData = JSON.stringify(row).replace(/"/g, "\'");
							html =
								'<button type="button" class="layui-btn layui-btn-xs" onclick="viewTemplateFile(' +
								rowData +
								')">查看</button>';
							html +=
								'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="DownloadTemplateFile(' +
								rowData + ')">下载</button>';
						}
						return html;
					}
				},
				{
					field: 'PLAN_START_COLINDEX',
					title: '策划<br>起始列',
					width: 60,
					align: 'center',
					formatter: function(value, row, index) {
						return value;
					}
				},
				{
					field: 'PLAN_END_COLINDEX',
					title: '策划<br>结束列',
					width: 60,
					align: 'center',
					formatter: function(value, row, index) {
						return value;
					}
				},
				{
					field: 'PHOTO_FILEPATH',
					title: '影像记录<br>策划模板',
					width: 90,
					align: 'center',
					formatter: function(value, row, index) {
						var html = '';
						if (value == undefined || value == '' || value == null) {
							html = '<span style="color:red;">暂未上传！</span>';
						} else {
							row.ftype = 5;
							var rowData = JSON.stringify(row).replace(/"/g, "\'");
							html =
								'<button type="button" class="layui-btn layui-btn-xs" onclick="viewTemplateFile(' +
								rowData +
								')">查看</button>';
							html +=
								'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="DownloadTemplateFile(' +
								rowData + ')">下载</button>';
						}
						return html;
					}
				},
				{
					field: 'PHOTO_START_COLINDEX',
					title: '影像记录<br>策划起始列',
					width: 80,
					align: 'center',
					formatter: function(value, row, index) {
						return value;
					}
				},
				{
					field: 'PHOTO_END_COLINDEX',
					title: '影像记录<br>策划结束列',
					width: 80,
					align: 'center',
					formatter: function(value, row, index) {
						return value;
					}
				},
				{
					field: 'PHOTO_SUMMARY_FILEPATH',
					title: '影像记录<br>汇总表模板',
					width: 100,
					hidden: true,
					align: 'center',
					formatter: function(value, row, index) {
						var html = '';
						if (value == undefined || value == '' || value == null) {
							html = '<span style="color:red;">暂未上传！</span>';
						} else {
							row.ftype = 6;
							var rowData = JSON.stringify(row).replace(/"/g, "\'");
							html =
								'<button type="button" class="layui-btn layui-btn-xs" onclick="viewTemplateFile(' +
								rowData +
								')">查看</button>';
							html +=
								'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="DownloadTemplateFile(' +
								rowData + ')">下载</button>';
						}
						return html;
					}
				},
				{
					field: 'IS_QUERY',
					title: '是否<br>数据查询',
					width: 66,
					hidden: false,
					align: 'center',
					formatter: function(value, row, index) {
						var html = '';
						if (value == 1) {
							html = '<span style="color:#1e9fff;">是</span>';
						} else {
							html = '否';
						}
						return html;
					}
				},
				{
					field: 'CERTIFICATE_FILEPATH',
					title: '合格证模板',
					width: 100,
					hidden: false,
					align: 'center',
					formatter: function(value, row, index) {
						var html = '';
						if (value == undefined || value == '' || value == null) {
							html = '<span style="color:red;">暂未上传！</span>';
						} else {
							row.ftype = 7;
							var rowData = JSON.stringify(row).replace(/"/g, "\'");
							html =
								'<button type="button" class="layui-btn layui-btn-xs" onclick="viewTemplateFile(' +
								rowData +
								')">查看</button>';
							html +=
								'<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="DownloadTemplateFile(' +
								rowData + ')">下载</button>';
						}
						return html;
					}
				},
				{
					field: 'ID',
					hidden: true
				}
			]
		],
		idField: 'ID',
		emptyMsg: '<div style="margin:5px 10px;font-size:14px;text-align:left;"><font color=red>没有数据...</font></div>',
		loadMsg: '正在加载数据...',
		rownumbers: true,
		striped: true,
		onSelect: function(rowIndex, rowData) {
			controlColShow(rowData.TYPE);
			var name = rowData.TREE_NAME;
			$("#c_layout").layout('panel', 'center').panel('setTitle', name + '-表格配置');
			loadParamTable(name);
		},
		onUnselectAll: function(rows) {
			$('#' + paramId).datagrid('loadData', []);
			$("#c_layout").layout('panel', 'center').panel('setTitle', '表格配置');
			$('#' + paramId).datagrid("getPanel").hide();
			$("#paramTableMsg").text('请选择一个模板！').show();
		}
	});
}

//增加一个table模板
function addTable() {
	var tableAddIndex = layer.open({
		title: '新增二级表',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['900px', tableLayerH],
		content: '<div id="addTableContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['新增', '重置', '关闭'],
		yes: function() {
			$('#btn_table_add').click();
		},
		btn2: function() {
			$('#btn_table_reset').click();
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function() {
			var addTpl = $("#tableHtml")[0].innerHTML;
			$("#addTableContent").append(addTpl);
		}
	});
	form.render(null, 'tableForm');
	//添加的时候默认选中三级表
	controlTableInputShow(2, tableAddIndex);
	form.on('select(type)', function(d) {
		controlTableInputShow(d.value, tableAddIndex);
	});

	//监听提交
	form.on('submit(tableFormAdd)', function(data) {
		var param = data.field;
		param.PHOTO_END_COLINDEX = param.PHOTO_END_COLINDEX || 0;
		param.PHOTO_START_COLINDEX = param.PHOTO_START_COLINDEX || 0;
		param.PLAN_END_COLINDEX = param.PLAN_END_COLINDEX || 0;
		param.PLAN_START_COLINDEX = param.PLAN_START_COLINDEX || 0;
		param.creator = 'admin';
		//添加成功的弹窗
		var cb_success = function(data) {
			//新增完成后需要刷新界面
			//提示完成后，点击确定再刷新界面
			layer.closeAll();
			loadTable('all');
			layer.msg('新增成功');
		};
		//添加失败的弹窗
		var cb_error = function(xhr) {
			layer.alert('新增失败!', {
				icon: 2
			});
		};
		//同步新增
		twxAjax(THING, "AddTable", param, false, cb_success, cb_error);
		return false;
	});
}

//复制选中的一个table模板
function copyTable() {
	//获取选中的数据
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		//提示用户
		layer.alert('请选择待复制数据...', {
			icon: 2
		});
		return;
	} else if (datas.length > 1) {
		layer.alert('只能选择一条数据进行复制...', {
			icon: 2
		});
		return;
	}
	var selectedData = datas[0];
	var tableCopyIndex = layer.open({
		title: '复制二级表',
		type: 1,
		shadeClose: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['450px', '150px'],
		content: '<div id="copyTableContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确定', '关闭'],
		yes: function() {
			$('#btn_copy_ok').click();
		},
		btn2: function() {
			return true;
		},
		success: function() {
			var tpl = $("#copyHtml")[0].innerHTML;
			$("#copyTableContent").append(tpl);
		}
	});

	form.render(null, 'copyForm');
	form.val("copyForm", selectedData);
	//监听提交
	form.on('submit(copyOk)', function(data) {
		var param = data.field;
		param.creator = 'admin';
		var cb_success = function(data) {
			layer.closeAll();
			refreshTable();
			layer.msg('复制成功');
		};
		//复制失败的弹窗
		var cb_error = function(xhr) {
			layer.alert('复制失败!', {
				icon: 2
			});
		};
		twxAjax(THING, "CopyTable", param, false, cb_success, cb_error);
		return false;
	});
}

//编辑一行table模板
function editTable() {
	//获取选中的数据
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		//提示用户，请选择待编辑的数据
		layer.alert('请选择待编辑数据...', {
			icon: 2
		});
		return;
	} else if (datas.length > 1) {
		layer.alert('只能选择一条数据进行编辑...', {
			icon: 2
		});
		return;
	}
	var selectedData = datas[0];
	var tableEditIndex = layer.open({
		title: '编辑二级表',
		type: 1,
		shadeClose: false,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['900px', tableLayerH2],
		content: '<div id="editTableContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['更新', '重置', '关闭'],
		yes: function() {
			$('#btn_table_update').click();
		},
		btn2: function() {
			$('#btn_table_reset').click();
			form.val("tableForm", selectedData);
			controlTableInputShow(selectedData.TYPE, tableEditIndex);
			return false;
		},
		btn3: function() {
			return true;
		},
		success: function() {
			var editTpl = $("#tableHtml")[0].innerHTML;
			$("#editTableContent").append(editTpl);
			$("#tree_name").attr('oldname', selectedData.TREE_NAME);
		}
	});

	form.render(null, 'tableForm');
	form.val("tableForm", selectedData);
	controlTableInputShow(selectedData.TYPE, tableEditIndex);
	form.on('select(type)', function(d) {
		controlTableInputShow(d.value, tableEditIndex);
	});

	//监听提交
	form.on('submit(tableFormUpdate)', function(data) {
		var param = data.field;
		param.PHOTO_END_COLINDEX = param.PHOTO_END_COLINDEX || 0;
		param.PHOTO_START_COLINDEX = param.PHOTO_START_COLINDEX || 0;
		param.PLAN_END_COLINDEX = param.PLAN_END_COLINDEX || 0;
		param.PLAN_START_COLINDEX = param.PLAN_START_COLINDEX || 0;
		param.modifier = 'admin';
		var cb_success = function(data) {
			layer.closeAll();
			refreshTable();
			layer.msg('修改成功');
		};
		//添加失败的弹窗
		var cb_error = function(xhr) {
			layer.alert('修改失败!', {
				icon: 2
			});
		};
		//同步新增
		twxAjax(THING, "UpdateTable", param, false, cb_success, cb_error);
		return false;
	});
}

//删除模板数据
function deleteTable() {
	//获取选中的数据
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		layer.alert('请选择需要删除的数据...', {
			icon: 2
		});
		return;
	}
	layer.confirm('是否确认删除选中数据?', {
		icon: 3,
		title: '删除提示',
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200
	}, function(index) {
		var row = datas[0];
		var ids = '';
		for (var i = 0; i < datas.length; i++) {
			ids += ',' + datas[i].ID;
		}
		if (ids !== '') {
			ids = ids.substring(1);
		}
		var cb_success = function(data) {
			layer.closeAll();
			refreshTable();
			layer.msg('删除成功');
		};
		var cb_error = function(xhr) {
			layer.alert('删除失败', {
				icon: 2
			});
		};
		twxAjax(THING, "DeleteTable", {
			ID: ids
		}, false, cb_success, cb_error);

	});
}

//保存按钮的点击事件
function save() {
	if (treeName == '') {
		layer.alert('请选择一个过程节点！', {
			icon: 2
		});
		return false;
	}
	var datas = $('#' + tableId).datagrid("getChecked");
	// if (datas.length == 0) {
	// 	layer.alert('至少勾选一个模板！', {
	// 		icon: 2
	// 	});
	// 	return false;
	// }
	var ids = "";
	for (var i = 0; i < datas.length; i++) {
		ids += ',' + datas[i].ID;
	}
	if (ids !== '') {
		ids = ids.substring(1);
	}
	var cb_success = function(data) {
		layer.msg('保存成功');
		loadTable(treeName);
	};
	var cb_error = function(xhr) {
		layer.alert('保存失败', {
			icon: 2
		});
	};
	twxAjax(THING, "RelationTable", {
		ids: ids,
		nodename: treeName,
		creator: 'admin'
	}, false, cb_success, cb_error);
}

//显示所有的table，并且勾选选中树节点关联的行
function showAllTable() {
	if (treeName != '') {
		//获取已经选中树节点的模板数据
		var datas = $('#' + tableId).datagrid('getChecked');
		var checkedIds = [];
		for (var i = 0; i < datas.length; i++) {
			checkedIds.push(datas[i].ID);
		}
		loadTable('all', false);
		if (checkedIds.length > 0) {
			for (var i = 0; i < checkedIds.length; i++) {
				var rowIndex = $('#' + tableId).datagrid('getRowIndex', checkedIds[i]);
				$('#' + tableId).datagrid('checkRow', rowIndex);
			}

		}
	} else {
		loadTable('all');
	}
}

//加载表数据
function loadTable(name, async) {
	//获取选中的这一行数据
	var selDatas = $('#' + tableId).datagrid('getSelections');
	var selRowId = -1;
	if (selDatas.length > 0) {
		selRowId = selDatas[0].ID;
	}
	$('#' + tableId).datagrid('loading');
	var cb_success = function(data) {
		$('#' + tableId).datagrid('loadData', data.rows);
		$('#' + tableId).datagrid('loaded');
		$('#' + tableId).datagrid('unselectAll');
		$('#' + tableId).datagrid('clearSelections');
		$('#' + tableId).datagrid("uncheckAll");
		$('#' + tableId).datagrid("clearChecked");
		if (data.rows.length == 0) {
			$('#' + paramId).datagrid("getPanel").hide();
			$("#paramTableMsg").text('没有数据！').show();
		}
		if (name != 'all') {
			if (data.rows.length > 0) {
				$('#' + tableId).datagrid("checkAll");
				if (data.rows.length == 1) {
					$('#' + tableId).datagrid("selectRow", 0);
				}
			}
		}
		if (selRowId != -1 && data.rows.length > 1) {
			var rowIndex = $('#' + tableId).datagrid('getRowIndex', selRowId);
			if (rowIndex != -1) {
				$('#' + tableId).datagrid("selectRow", rowIndex);
			}
		}
	}
	//请求失败的回调
	var cb_error = function(xhr, textStatus, errorThrown) {
		if (xhr.status !== 200) {
			layer.msg('后台错误，请联系管理员!', {
				icon: 2,
				anim: 6
			});
		}
	};
	var asy = true;
	if (async == false) {
		asy = false;
	}
	if (name == 'all') {
		isAllTable = true;
		twxAjax(THING, "QueryAllTable", {}, asy, cb_success, cb_error);
	} else {
		isAllTable = false;
		twxAjax(THING, "QueryTableByNodename", {
			nodename: name
		}, asy, cb_success, cb_error);
	}
}