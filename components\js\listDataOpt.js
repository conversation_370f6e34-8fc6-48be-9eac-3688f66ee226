/**
 * 采集管理页面中按钮操作的相关方法
 */

/**
 * 清单数据相互关联
 * @param {Object} layui
 * @param {Object} tableId
 * @param {Object} successFunc
 */
function linkListData(layui, tableId, processTreeId, successFunc) {
	var layer = layui.layer,
		form = layui.form;
	//获取选中的数据
	var srcDatas = $('#' + tableId).datagrid('getSelections');
	if (srcDatas.length == 0) {
		layer.alert('请选择关联数据...', {
			icon: 2
		});
		return;
	}
	var listTable;
	var srcTableName = srcDatas[0]['TABLE_NAME'];
	var srcFileTypes = [];
	var srcIds = [];
	for (var i = 0; i < srcDatas.length; i++) {
		srcIds.push(srcDatas[i]['ID']);
		srcFileTypes.push(srcDatas[i]['FILE_TYPE']);
	}

	var content = `<div id="listLinkContent">
							<div id="tb" >
								<form class="layui-form search-form" lay-filter="download-table-form">
															<div class="layui-form-item">
																<div class="layui-inline">
																	<label class="layui-form-label">分类</label>
																	<div class="layui-input-inline">
																		<select id="s_folder" name="s_folder" lay-filter="s_folder" lay-search></select>
																	</div>
																</div>
																<div class="layui-inline">
																	<label class="layui-form-label">型号</label>
																	<div class="layui-input-inline">
																		<select id="s_model" name="s_model" lay-filter="s_model" lay-search></select>
																	</div>
																</div>
																<div class="layui-inline">
																	<label class="layui-form-label">阶段</label>
																	<div class="layui-input-inline">
																		<select id="s_phase" name="s_phase" lay-filter="s_phase" lay-search></select>
																	</div>
																</div>
																<div class="layui-inline">
																	<label class="layui-form-label">专业</label>
																	<div class="layui-input-inline">
																		<select id="s_dir" name="s_dir" lay-filter="s_dir" lay-search></select>
																	</div>
																</div>
															</div>
															<div class="layui-form-item">
																<div class="layui-inline">
																	<label class="layui-form-label">过程</label>
																	<div class="layui-input-inline">
																		<select id="s_leaf" name="s_leaf" lay-filter="s_leaf" lay-search></select>
																	</div>
																</div>
																<div class="layui-inline">
																	<label class="layui-form-label">类别</label>
																	<div class="layui-input-inline">
																		<select id="s_type" name="s_type" lay-filter="s_type" lay-search>
																			<option value=""></option>
																			<option value="design">设计类</option>
																			<option value="craft">工艺类</option>
																			<option value="process">过程控制</option>
																			<option value="quality">质量综合</option>
																		</select>
																	</div>
																</div>
																<div class="layui-inline">
																	<label class="layui-form-label">文件类别</label>
																	<div class="layui-input-inline">
																		<select id="s_file_type" name="s_file_type" lay-filter="s_file_type" lay-search>
																		</select>
																	</div>
																</div>
																<div class="layui-inline">
																	<label class="layui-form-label">文件名称</label>
																	<div class="layui-input-inline">
																		<input type="text" name="s_file_name" id="s_file_name" autocomplete="off" class="layui-input">
																	</div>
																</div>
																<div class="layui-inline">
																	<button class="layui-btn layui-btn-sm" style="margin-left:10px;" lay-submit lay-filter="link-table-search">搜索</button>
																</div>
															</div>
														</form>
							</div>
							<div id="linkTable"></div>
						</div>`;

	function renderSelect(eleId, datas, current = -1) {
		$("#" + eleId).empty().append('<option value=""></option>');
		for (var i = 0; i < datas.length; i++) {
			if (datas[i]['id'] == current) {
				$("#" + eleId).append('<option value="' + datas[i]['id'] + '"  selected>' + datas[i]['name'] + '</option>');
			} else {
				$("#" + eleId).append('<option value="' + datas[i]['id'] + '">' + datas[i]['name'] + '</option>');
			}
		}
	}

	function loadSelect(eleId, pid) {
		loadFileTypeSelect();
		if (pid == '') {
			return false;
		}
		twxAjax('Thing.Fn.ProcessTree', 'QueryChildNode', {
			pid: pid
		}, true, function(res) {
			if (res.success) {
				var datas = res.data;
				renderSelect(eleId, datas);
				form.render('select');
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	}

	function loadFileTypeSelect() {
		var treeId = getSearchTreeId();
		twxAjax('Thing.Fn.ProcessTree', 'QueryLinkFileType', {
			treeId: treeId,
			type: $("#s_type").val()
		}, true, function(res) {
			if (res.success) {
				var d = res.data;
				renderSelect("s_file_type", d.datas, d.current);
				form.render('select');
			} else {
				layer.alert(res.msg, {
					icon: 2
				});
			}
		}, function(xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	}

	function getSearchTreeId() {
		if ($("#s_leaf").val() !== '') {
			return $("#s_leaf").val();
		}
		if ($("#s_dir").val() !== '') {
			return $("#s_dir").val();
		}
		if ($("#s_phase").val() !== '') {
			return $("#s_phase").val();
		}
		if ($("#s_model").val() !== '') {
			return $("#s_model").val();
		}
		if ($("#s_folder").val() !== '') {
			return $("#s_folder").val();
		} else {
			return -1;
		}
	}

	function getSearchParams() {
		return {
			processTreeId: getSearchTreeId(),
			queryType: $("#s_type").val(),
			fileName: $("#s_file_name").val(),
			fileType: $("#s_file_type").val()
		}
	}

	layer.open({
		title: '关联',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		maxmin: false,
		resize: false,
		area: ['1500px', '700px'],
		content: content,
		btn: ['关联', '关闭'],
		yes: function(index, layero) {
			var linkSels = listTable.getSelections();
			if (linkSels.length == 0) {
				layer.alert('请选择关联数据...', {
					icon: 2
				});
				return;
			} else {
				var targetTableNames = [];
				var targetFileTypes = [];
				var targetIds = [];
				for (var i = 0; i < linkSels.length; i++) {
					targetIds.push(linkSels[i]['ID']);
					targetFileTypes.push(linkSels[i]['FILE_TYPE']);
					targetTableNames.push(linkSels[i]['TABLE_NAME']);
				}
				var params = {
					targetIds: targetIds,
					targetFileTypes: targetFileTypes,
					targetTableNames: targetTableNames,
					srcTableName: srcTableName,
					srcFileTypes: srcFileTypes,
					srcIds: srcIds,
					creator: sessionStorage.getItem("username")
				};
				twxAjax('Thing.Fn.ListData', 'LinkListData', {
					params: params
				}, true, function(res) {
					if (res.success) {
						layer.close(index);
						layer.msg(res.msg)
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}, function(xhr, textStatus, errorThrown) {
					layer.alert('请求出错！', {
						icon: 2
					});
				});
			}
		},
		btn2: function(index, layero) {
			return true;
		},
		success: function() {
			twxAjax('Thing.Fn.ProcessTree', 'QueryAllNodeByLeafNode', {
				processId: processTreeId
			}, true, function(res) {
				if (res.success) {
					var d = res.data;
					renderSelect("s_folder", d.folder.datas, d.folder.current);
					renderSelect("s_model", d.product.datas, d.product.current);
					renderSelect("s_phase", d.phase.datas, d.phase.current);
					renderSelect("s_dir", d.dir.datas, d.dir.current);
					renderSelect("s_leaf", d.leaf.datas, d.leaf.current);
					renderSelect("s_file_type", d.fileType.datas, d.fileType.current);
					form.render();

					form.on('select(s_folder)', function(data) {
						var value = data.value;
						renderSelect('s_model', []);
						renderSelect('s_phase', []);
						renderSelect('s_dir', []);
						renderSelect('s_leaf', []);
						loadSelect('s_model', value);

					});
					form.on('select(s_model)', function(data) {
						var value = data.value;
						renderSelect('s_phase', []);
						renderSelect('s_dir', []);
						renderSelect('s_leaf', []);
						loadSelect('s_phase', value);
					});
					form.on('select(s_phase)', function(data) {
						var value = data.value;
						renderSelect('s_dir', []);
						renderSelect('s_leaf', []);
						loadSelect('s_dir', value);
					});
					form.on('select(s_dir)', function(data) {
						var value = data.value;
						renderSelect('s_leaf', []);
						loadSelect('s_leaf', value);
					});
					form.on('select(s_leaf)', function(data) {
						loadFileTypeSelect();
					});
					form.on('select(s_type)', function(data) {
						loadFileTypeSelect();
					});
					listTable = new ListDataGrid({
						eleId: 'linkTable',
						tableHeight: 600,
						colType: 'all',
						toolbar: 'tb',
						searchParams: d.searchParams,
						pageSize: 10,
						isCheck: true,
						initDatas: []
					});
					if (d.searchParams.fileType != '') {
						listTable.loadData();
					}
					// 搜索提交
					form.on('submit(link-table-search)', function(data) {
						listTable.searchParams = getSearchParams();
						if (listTable.searchParams.fileType != '') {
							listTable.loadData();
						} else {
							listTable.loadEmptyData();
						}

						return false;
					});

				} else {
					layer.alert(res.msg, {
						icon: 2
					});
				}
			}, function(xhr, textStatus, errorThrown) {
				layer.alert('请求出错！', {
					icon: 2
				});
			});

		}
	});


}


/**
 * 重新关联自动采集的清单数据
 * @param {*} layui 
 * @param {*} tableId 页面中的表格的ID
 * @param {*} dataTableName 数据库中的表的名称
 * @param {*} datapkg 所属的数据包的信息
 * @param {*} typeName 类型名称
 * @param {*} successFunc 执行成功回调函数
 */
function reassociateProcessTree(layui, tableId, dataTableName, datapkg, typeName, successFunc) {

	var layer = layui.layer,
		form = layui.form;

	//初始化文件类型
	var initFileType = function() {
		//判断条件是否齐全
		//获取数据包的ID
		//获取类别表
		var param = {};
		param.datapkgid = '';
		param.tabletype = '';

		param.datapkgid = $('#datapkgname').val();
		param.tabletype = $('#typelist').val();

		if (param.datapkgid === '' || param.tabletype === '') {
			return;
		}
		var cb_success = function(res) {
			if (res.success) {
				$('#filetype').empty();
				for (var i = 0; i < res.data.length; i++) {
					var row = res.data[i];
					$('#filetype').append('<option value="' + row.FILE_TYPE + '">' + row.FILE_TYPE + '</option>');
				}
			} else {
				$('#filetype').empty();
				layer.alert(res.msg);
			}
			form.render();
		};
		var cb_error = function() {

		};
		twxAjax('Thing.Fn.DataCollect', 'GetDataPkgPlanFileType', param, true, cb_success, cb_error);
	};

	//获取选中的数据
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		layer.alert('请选择关联数据...', {
			icon: 2
		});
		return;
	} else {
		// for (var i = 0; i < datas.length; i++) {
		// 	if (datas[i].GATHERING_METHOD != '自动采集') {
		// 		layer.alert('请选择自动采集的数据关联...', {
		// 			icon: 2
		// 		});
		// 		return;
		// 	}
		// }
	}

	form.on('select(model)', function(d) {
		twxAjax("Thing.Fn.DataDownload", "QueryPhase", {
			parentId: document.getElementById("model").value
		}, false, function(data) {
			$("#phase").empty();
			$("#datapkgname").empty();
			$("#phase").append('<option value="">请选择</option>');
			for (var i = 0; i < data.rows.length; i++) {
				$("#phase").append('<option value="' + data.rows[i].TREEID + '">' + data.rows[i].NODENAME + '</option>');
			}
			form.render();
		});

		//选择改变事件进行文件类型的获取
		initFileType();
	});

	form.on('select(phase)', function(d) {
		twxAjax("Thing.Fn.DataDownload", "QueryDataPkgByTreeId", {
			parentId: document.getElementById("phase").value
		}, false, function(data) {
			$("#datapkgname").empty();
			$("#datapkgname").append('<option value="">请选择</option>');
			for (var i = 0; i < data.rows.length; i++) {
				$("#datapkgname").append('<option value="' + data.rows[i].ID + '">' + data.rows[i].NAME + '《' + data.rows[i].CODE + '》' + '</option>');
			}
			form.render();
		});
		//选择改变事件进行文件类型的获取
		initFileType();
	});

	form.on('select(typelist)', function() {
		//选择改变事件进行文件类型的获取
		initFileType();
	});

	form.on('select(datapkgname)', function(d) {
		//选择改变事件进行文件类型的获取
		initFileType();
	});

	layer.open({
		title: '数据包关联',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		maxmin: false,
		resize: false, //不允许拉伸
		// maxmin: true,
		area: ["650px", '370px'],
		content: '<div id="datapkglinkContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['关联', '重置', '关闭'],
		yes: function(index, layero) {
			var param = {};
			param.tabletype = $('#typelist').val();
			var tableTypeName = $('#typelist  option:selected').text();
			param.file_type = $('#filetype  option:selected').text();
			// param.dataid = datas[0].ID;
			param.datapkgid = $('#datapkgname').val();
			var pkgName = $('#datapkgname  option:selected').text();
			param.dataids = '';

			for (var i = 0; i < datas.length; i++) {
				if (datas[i].ID !== undefined) {
					param.dataids += ',' + datas[i].ID;
				}
			}
			if (param.dataids !== '') {
				param.dataids = param.dataids.substring(1);
			}
			param.source_table = dataTableName;
			if (param.file_type === '' || param.datapkgid === '') {
				layer.alert('请选择需要关联的数据包的信息...', {
					icon: 2
				});
				return;
			}

			var logType = '编辑';
			var logInfo = '采集管理-数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的(类型：' +
				typeName + '、IDs：' + param.dataids + ')关联到数据包(ID：' + param.datapkgid + '、名称：' + pkgName + ')下的(类型：' +
				tableTypeName + '、文件类别：' + param.file_type + ')';
			var cb_success = function(data) {
				if (data.success === false) {
					logRecord(logType, logInfo, 0);
					layer.alert(data.message, {
						icon: 2
					});
				} else {
					layer.closeAll();
					logRecord(logType, logInfo, 1);
					successFunc();
					layer.msg('关联成功！');
				}
			};
			var cb_error = function() {
				logRecord(logType, logInfo, 0);
			};
			twxAjax('Thing.Fn.DataCollect', 'BatchReAssignData', param, true, cb_success, cb_error);
		},
		btn2: function(index, layero) {
			return false;
		},
		btn3: function(index, layero) {
			return true;
		},
		success: function() {
			var editTpl = '<form class="layui-form" style="">\
							<div class="layui-form-item">\
								<label class="layui-form-label datalinklabel">产品型号:</label>\
								<div class="layui-input-block">\
									<select name="model" lay-filter="model" lay-verify="required" lay-search id="model">\
										<option value=""></option>\
									</select>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label datalinklabel">阶段:</label>\
								<div class="layui-input-block">\
									<select name="phase" lay-filter="phase" lay-verify="required" id="phase">\
										<option value=""></option>\
									</select>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label datalinklabel">数据包:</label>\
								<div class="layui-input-block">\
									<select name="datapkgname" lay-filter="datapkgname" lay-search lay-verify="required" id="datapkgname">\
										<option value=""></option>\
									</select>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label datalinklabel">清单类别:</label>\
								<div class="layui-input-block">\
									<select name="typelist" lay-filter="typelist" lay-verify="required" id="typelist">\
										<option value=""></option>\
										<option value="DESIGN_DATA_LIST">设计</option>\
										<option value="CRAFT_DATA_LIST">工艺</option>\
										<option value="PROCESS_CONTROL_LIST">过程控制</option>\
										<option value="QUALITY_CONTROL_LIST">质量综合</option>\
									</select>\
								</div>\
							</div>\
							<div class="layui-form-item">\
								<label class="layui-form-label datalinklabel">文件类型:</label>\
								<div class="layui-input-block">\
									<select name="filetype" lay-filter="filetype" lay-verify="required" id="filetype">\
										<option value=""></option>\
									</select>\
								</div>\
							</div>\
							<div class="layui-form-item" style="display: none;">\
								<center>\
									<button id="btn_add" class="layui-btn" lay-submit lay-filter="addData">提交</button>\
									<button id="btn_reset" class="layui-btn layui-btn-primary" type="reset">重置</button>\
								</center>\
							</div>\
						</form>';
			$("#datapkglinkContent").append(editTpl);
			$("#datapkglinkContent").parent().css("overflow", "visible");
			//加载型号产品
			twxAjax("publishMissionThing", "getTreeNodeByTypeAndParentID", {
				type: "product"
			}, false, function(data) {
				for (var i = 0; i < data.rows.length; i++) {
					$("#model").append('<option value="' + data.rows[i].TREEID + '">' + data.rows[i].NODENAME + '</option>');
				}
			});
			form.render();
		}
	});
}


/**
 *  产品结构树关联
 * @param {*} layui 
 * @param {*} tableId 页面中的表格的ID
 * @param {*} dataTableName 数据库中的表的名称
 * @param {*} successFunc 执行成功回调函数
 */
function productTreeRelation(layui, tableId, dataTableName, successFunc) {

	var layer = layui.layer,
		form = layui.form;

	//获取选中的数据
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		layer.alert('请选择关联数据...', {
			icon: 2
		});
		return;
	}
	layer.open({
		title: '产品结构树关联',
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		maxmin: false,
		resize: false, //不允许拉伸
		// maxmin: true,
		area: ["700px", '600px'],
		content: '<div id="productLinkContent" style="padding-top: 15px;padding-right: 15px;"><ul id="bomTree" class="ztree"></ul></div>',
		btn: ['关联', '关闭'],
		yes: function(index, layero) {
			var checkedTrees = bomTree.ztreeObj.getCheckedNodes();
			if (checkedTrees.length > 0) {
				var relationIds = '';
				for (var i = 0; i < datas.length; i++) {
					if (datas[i].ID !== undefined) {
						relationIds += ',' + datas[i].ID;
					}
				}
				if (relationIds !== '') {
					relationIds = relationIds.substring(1);
				}
				var treeIds = '';
				for (var i = 0; i < checkedTrees.length; i++) {
					if (checkedTrees[i].ID !== undefined) {
						treeIds += ',' + checkedTrees[i].ID;
					}
				}
				if (treeIds !== '') {
					treeIds = treeIds.substring(1);
				}
				twxAjax("Thing.Fn.BOM", "ProductLinkList", {
					relationIds: relationIds,
					treeIds: treeIds,
					relationUser: sessionStorage.getItem('username'),
					relationName: dataTableName
				}, true, function(res) {
					if (res.success) {
						layer.closeAll();
						successFunc();
						layer.msg(res.msg);
					} else {
						layer.alert(res.msg, {
							icon: 2
						});
					}
				}, function(err) {
					layer.alert('关联请求失败！');
				});
			} else {
				layer.alert('请勾选要关联的产品结构树节点', {
					icon: 2
				});
				return false;
			}
		},
		btn2: function(index, layero) {
			return true;
		},
		success: function() {
			window.bomTree = new BomTree(true, false);
			bomTree.loadTree();
		}
	});
}


/**
 * 删除清单数据
 * @param {*} layui 
 * @param {*} tableId 页面中的表格的ID
 * @param {*} dataTableName 数据库中的表的名称
 * @param {*} datapkg 所属的数据包的信息
 * @param {*} typeName 类型名称
 * @param {*} successFunc 执行成功回调函数
 */
function deleteDataList(layui, tableId, dataTableName, datapkg, typeName, successFunc) {
	var layer = layui.layer;

	//判断是否选定了数据
	//获取选中的数据
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		//提示用户，请选择待编辑的数据
		layer.alert('请选择待删除数据...', {
			icon: 2
		});
		return;
	}

	layer.confirm('是否确认删除选中数据?', {
		icon: 3,
		title: '删除提示'
	}, function(index) {
		var ids = '';
		for (var i = 0; i < datas.length; i++) {
			if (datas[i].ID != undefined) {
				ids += ',' + datas[i].ID;
			}
		}

		ids = ids.substring(1);

		var logType = '删除';
		var logInfo = '采集管理-删除数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的数据(类型：' +
			typeName + '、IDs：' + ids + ')';

		var cb_success = function(data) {
			if (data.success === false) {
				logRecord(logType, logInfo, 0);
				layer.alert(data.message, {
					icon: 2
				});
				return;
			} else {
				//新增完成后需要刷新界面
				layer.closeAll();
				successFunc();
				//记录日志
				logRecord(logType, logInfo, 1);
				layer.msg('删除成功');

			}
		};
		//添加失败的弹窗
		var cb_error = function(xhr) {
			logRecord(logType, logInfo, 0);
			layer.alert('删除失败!', {
				icon: 2
			});
		};

		//向服务端发送删除指令
		twxAjax("Thing.Fn.PlanBuild", "DeletePlanListByID", {
			type: dataTableName,
			ids: ids
		}, false, cb_success, cb_error);

	});
}

/**
 * 清单数据确认
 * @param {*} layui 
 * @param {*} tableId 页面中的表格的ID
 * @param {*} dataTableName 数据库中的表的名称
 * @param {*} datapkg 所属的数据包的信息
 * @param {*} successFunc 执行成功的回调函数
 */
function stateCheck(layui, tableId, dataTableName, datapkg, typeName, successFunc) {
	var layer = layui.layer;

	var sels = $('#' + tableId).datagrid('getSelections');
	if (sels.length == 0) {
		layer.alert('请选择需要确认的数据...', {
			icon: 2
		});
		return;
	}

	var ids = '';
	for (var i = 0; i < sels.length; i++) {
		var row = sels[i];
		if (row.STATE_CHECK !== '已确认') {
			if (row.ID !== undefined) {
				ids += ',' + row.ID;
			}
		}
	}
	if (ids === '') {
		layer.alert('选择的数据均已确认', {
			icon: 1
		});
		return;
	}

	ids = ids.substring(1);

	var logType = '编辑';
	var logInfo = '采集管理-数据包(ID：' + datapkg.ID + '、名称：' + datapkg.NAME + '、编号：' + datapkg.CODE + ')下的(IDs:' + ids +
		'、类型：' + typeName + ')状态确认';

	var cb_success = function(data) {
		if (data.success === false) {
			logRecord(logType, logInfo, 0);
			layer.alert(data.message, {
				icon: 2
			});
			return;
		} else {
			layer.msg('确认成功');
			successFunc();
			//记录日志
			logRecord(logType, logInfo, 1);
		}
	};
	//添加失败的弹窗
	var cb_error = function(xhr) {
		//记录日志
		logRecord(logType, logInfo, 0)
		layer.alert('状态确认失败!', {
			icon: 2
		});
	};

	var param = {};
	param.type = dataTableName;
	param.ids = ids;
	param.STATE_CHECK = '已确认';

	twxAjax("Thing.Fn.DataCollect", "UpdateaDataState", param, true, cb_success, cb_error);
}

/**
 *  编辑属性的按钮
 * @param {*} layui 
 * @param {*} tableId 页面中数据表的ID
 * @param {*} dataTableName 数据库数据表的名称
 */
function editParam(layui, tableId, dataTableName, successFunc) {
	var layer = layui.layer,
		form = layui.form;
	//获取选中的数据
	var datas = $('#' + tableId).datagrid('getSelections');
	if (datas.length == 0) {
		layer.alert('请选择待编辑的数据...', {
			icon: 2
		});
		return;
	} else {
		var tpl = '<form class="layui-form" action="" lay-filter="param-form">\
						<div class="layui-form-item layui-form-text">\
						    <div class="">\
						      <textarea name="param" placeholder="请输入内容" lay-verify="required" style="height:160px;resize:none;" class="layui-textarea"></textarea>\
						    </div>\
						  </div>\
						<div class="layui-form-item" style="display:none;">\
							<div class="layui-input-block">\
								<div class="layui-footer">\
									<button class="layui-btn" id="submit-param" lay-submit="" lay-filter="submit-param">确认</button>\
								</div>\
							</div>\
						</div>\
					</form>';
		layer.open({
			title: '编写属性',
			type: 1,
			fixed: false,
			maxmin: false,
			anim: false,
			openDuration: 200,
			isOutAnim: false,
			closeDuration: 200,
			shadeClose: false,
			resize: false, //不允许拉伸
			area: ['380px', '270px'],
			content: '<div id="paramContent" style="padding-top: 15px;padding-left: 15px;padding-right: 15px;"></div>',
			btn: ['确认', '取消'],
			yes: function() {
				$('#submit-param').click();
			},
			btn2: function() {
				return true;
			},
			success: function() {
				$("#paramContent").append(tpl);
			}
		});
		form.render(null, 'param-form');
		form.on('submit(submit-param)', function(data) {
			var ids = '';
			for (var i = 0; i < datas.length; i++) {
				if (datas[i].ID != undefined) {
					ids += ',' + datas[i].ID;
				}
			}

			ids = ids.substring(1);
			var cb_success = function(res) {
				if (res.success) {
					layer.closeAll();
					successFunc();
				} else {
					layer.alert(res.msg, {
						icon: 2
					})
				}
			};
			//添加失败的弹窗
			var cb_error = function(xhr) {
				layer.alert('更新失败!', {
					icon: 2
				});
			};

			//向服务端发送删除指令
			twxAjax("Thing.Fn.DataCollect", "UpdateParam", {
				param: data.field.param,
				type: dataTableName,
				ids: ids
			}, false, cb_success, cb_error);

			return false;
		});
	}
}