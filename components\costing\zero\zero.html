<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>归零通知单</title>
		<link href="../../../plugins/layui-lasted/css/layui.css" rel="stylesheet" />
		<link href="../../../plugins/bpmn-js/diagram-js.css" rel="stylesheet" />
		<link href="../../../plugins/bpmn-js/bpmn-js.css" rel="stylesheet" />
		<link href="../css/common.css" rel="stylesheet" />
		<link href="zero.css" rel="stylesheet" />

		<script src="../../../plugins/bpmn-js/bpmn-viewer.development.js"></script>
		<script src="../../../plugins/easyui/jquery.min.js"></script>

		<script src="../../js/config/twxconfig.js"></script>
		<script src="../../js/util.js"></script>
	</head>
	<body>
		<div class="layui-tab layui-tab-brief zero-tab">
			<ul class="layui-tab-title">
				<li class="layui-this">我的流程</li>
				<li>待办任务</li>
				<li>已办任务</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<form class="layui-form search-form" lay-filter="init-table-form">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">流程名称</label>
								<div class="layui-input-inline">
									<input type="text" name="processName" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">创建时间</label>
								<div class="layui-inline" id="init-range-date">
									<div class="layui-input-inline">
										<input type="text" autocomplete="off" name="initStartDate" id="init-start-date" class="layui-input" placeholder="开始日期">
									</div>
									<div class="layui-form-mid">-</div>
									<div class="layui-input-inline">
										<input type="text" autocomplete="off" name="initEndDate" id="init-end-date" class="layui-input" placeholder="结束日期">
									</div>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layui-btn-sm" lay-submit lay-filter="init-table-search">搜索</button>
								<button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
							</div>
						</div>
					</form>
					<table class="layui-hide" id="init-table" lay-filter="init-table"></table>
				</div>
				<div class="layui-tab-item">
					<form class="layui-form search-form" lay-filter="todo-table-form">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">流程名称</label>
								<div class="layui-input-inline">
									<input type="text" name="processName" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">接收时间</label>
								<div class="layui-inline" id="todo-range-date">
									<div class="layui-input-inline">
										<input type="text" autocomplete="off" name="todoStartDate" id="todo-start-date" class="layui-input" placeholder="开始日期">
									</div>
									<div class="layui-form-mid">-</div>
									<div class="layui-input-inline">
										<input type="text" autocomplete="off" name="todoEndDate" id="todo-end-date" class="layui-input" placeholder="结束日期">
									</div>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layui-btn-sm" lay-submit lay-filter="todo-table-search">搜索</button>
								<button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
							</div>
						</div>
					</form>
					<table class="layui-hide" id="todo-table" lay-filter="todo-table"></table>
				</div>
				<div class="layui-tab-item">
					<form class="layui-form search-form" lay-filter="finished-table-form">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">流程名称</label>
								<div class="layui-input-inline">
									<input type="text" name="processName" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">结束时间</label>
								<div class="layui-inline" id="finished-range-date">
									<div class="layui-input-inline">
										<input type="text" autocomplete="off" name="finishedStartDate" id="finished-start-date" class="layui-input" placeholder="开始日期">
									</div>
									<div class="layui-form-mid">-</div>
									<div class="layui-input-inline">
										<input type="text" autocomplete="off" name="finishedEndDate" id="finished-end-date" class="layui-input" placeholder="结束日期">
									</div>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layui-btn-sm" lay-submit lay-filter="finished-table-search">搜索</button>
								<button class="layui-btn layui-btn-sm layui-btn-primary" type="reset">重置</button>
							</div>
						</div>
					</form>
					<table class="layui-hide" id="finished-table" lay-filter="finished-table"></table>
				</div>
			</div>
		</div>
	</body>
	<script type="text/html" id="init-toolbar">
		<div class="layui-btn-container">
			<button class="layui-btn layui-btn-sm" lay-event="init-btn">发起新流程</button>
		</div>
	</script>
	<script type="text/html" id="init-rowbar">
		<div class="layui-clear-space">
			<a class="layui-btn layui-btn-xs" lay-event="view">详情</a>
			<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="cancel">取消申请</a>
			<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
		</div>
	</script>

	<script type="text/html" id="todo-toolbar">
		<div class="layui-btn-container">
			<!-- <button class="layui-btn layui-btn-sm" lay-event="init-btn">删除</button> -->
		</div>
	</script>
	<script type="text/html" id="todo-rowbar">
		<div class="layui-clear-space">
			<a class="layui-btn layui-btn-xs" lay-event="handle">处理</a>
			<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="transfer">转办</a>
		</div>
	</script>
	<script type="text/html" id="finished-toolbar">
		<div class="layui-btn-container">
			<!-- <button class="layui-btn layui-btn-sm" lay-event="init-btn">删除</button> -->
		</div>
	</script>
	<script type="text/html" id="finished-rowbar">
		<div class="layui-clear-space">
			<a class="layui-btn layui-btn-xs" lay-event="record">流转记录</a>
			<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="revoke">撤回</a>
		</div>
	</script>
	<script type="text/html" id="zero-form-html">
		<form class="layui-form zero-form" action="" lay-filter="zero-form">
			<div class="layui-form-item">
				<label class="layui-form-label">编号</label>
				<div class="layui-input-block">
					<input type="text" name="Z_NUM" id="z_num" placeholder="自动生成" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">发生问题编号</label>
					<div class="layui-input-block">
						<input type="text" name="Z_QUESTION_NUM" id="z_question_num" lay-reqText="发生问题编号不能为空" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">研制阶段</label>
					<div class="layui-input-block">
						<select name="Z_PHASE" lay-filter="phase" id="z_phase">
							<option value="初样">初样</option>
							<option value="正样">正样</option>
							<option value="模样">模样</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">涉及产品名称</label>
					<div class="layui-input-block">
						<input type="text" name="Z_PRODUCT_NAME" id="z_product_name" lay-reqText="涉及产品名称不能为空" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">产品图号（代号）</label>
					<div class="layui-input-block">
						<input type="text" name="Z_PRODUCT_NUM" id="z_product_num" lay-reqText="产品图号不能为空" autocomplete="off" class="layui-input">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">发生地点</label>
					<div class="layui-input-block">
						<input type="text" name="Z_PLACE" id="z_place" lay-reqText="发生地点不能为空" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">发生日期</label>
					<div class="layui-input-block">
						<input type="text" name="Z_DATE" id="z_date" lay-reqText="发生日期不能为空" autocomplete="off" class="layui-input">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">责任部门</label>
				<div class="layui-input-block">
					<input type="text" name="Z_DEPARTMENT" id="z_department" lay-reqText="责任部门能为空" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">问题描述</label>
				<div class="layui-input-block">
					<textarea name="Z_QUESTION_DESC" placeholder="请输入问题描述" id="z_question_desc" class="layui-textarea"></textarea>
					<div class="layui-input-split layui-input-suffix">
						<div style="position: absolute; bottom:0">
							<p name="Z_QUESTION_DESC_IF_N"></p>
							<p name="Z_QUESTION_DESC_U_N"></p>
							<p name="Z_QUESTION_DESC_D"></p>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">归零要求</label>
				<div class="layui-input-block">
					<textarea placeholder="请输入归零要求" id="z_request" class="layui-textarea" name="Z_REQUEST"></textarea>
					<div class="layui-input-split layui-input-suffix">
						<div style="position: absolute; bottom:0">
							<p name="Z_REQUEST_IF_N"></p>
							<p name="Z_REQUEST_U_N"></p>
							<p name="Z_REQUEST_D"></p>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">主管所领导意见</label>
				<div class="layui-input-block">
					<textarea placeholder="请输入主管所领导意见" id="z_leader_opinion" class="layui-textarea" name="Z_LEADER_OPINION"></textarea>
					<div class="layui-input-split layui-input-suffix">
						<div style="position: absolute; bottom:0">
							<p name="Z_LEADER_OPINION_IF_N"></p>
							<p name="Z_LEADER_OPINION_U_N"></p>
							<p name="Z_LEADER_OPINION_D"></p>
						</div>
					</div>
				</div>
			</div>

			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">业务部门意见</label>
				<div class="layui-input-block">
					<textarea placeholder="请输入业务部门意见" id="z_business_opinion" class="layui-textarea" name="Z_BUSINESS_OPINION"></textarea>
					<div class="layui-input-split layui-input-suffix">
						<div style="position: absolute; bottom:0">
							<p name="Z_BUSINESS_OPINION_IF_N"></p>
							<p name="Z_BUSINESS_OPINION_U_N"></p>
							<p name="Z_BUSINESS_OPINION_D"></p>
						</div>
					</div>
				</div>
			</div>

			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">上传见证材料</label>
				<div class="layui-input-block upload-div">
					<button type="button" name="z_witness_file" class="layui-btn layui-btn-sm layui-btn-normal upload-choose">选择文件</button>
					<input type="text" name="Z_WITNESS_FILE_NAME" readonly="readonly" id="z_witness_file_name" class="layui-input upload-name">

					<input type="text" name="Z_WITNESS_FILE_PATH" id="z_witness_file_path" class="layui-hide upload-path">
					<input type="text" name="Z_WITNESS_FILE_SIZE" id="z_witness_file_size" class="layui-hide upload-size">
					<input type="text" name="Z_WITNESS_FILE_FORMAT" id="z_witness_file_format" class="layui-hide upload-format">
					<!-- <input type="file" class="layui-input upload-input" name="z_witness_file" id="z_witness_file"> -->
					<div class="upload-msg layui-hide"><i class="layui-icon layui-icon-success"></i><span>上传成功</span></div>
					<div class="upload-preview layui-hide">
						<button class="layui-btn layui-btn-sm">预览</button>
					</div>

				</div>
			</div>

			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">闭环情况检查</label>
				<div class="layui-input-block">
					<textarea placeholder="请输入闭环情况检查" id="z_loop" class="layui-textarea" name="Z_LOOP"></textarea>
					<div class="layui-input-split layui-input-suffix">
						<div style="position: absolute; bottom:0">
							<p name="Z_LOOP_IF_N"></p>
							<p name="Z_LOOP_U_N"></p>
							<p name="Z_LOOP_D"></p>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-form-item layui-hide">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit id="zero-form-submit" lay-filter="zero-form-submit">提交</button>
				</div>
			</div>
			<div class="layui-form-item layui-hide handle1">
				<div class="layui-input-block">
					<button class="layui-btn layui-bg-blue" lay-submit id="handle1-form-pass" lay-filter="handle1-form-pass">同意</button>
					<button class="layui-btn layui-bg-orange" lay-submit id="handle1-form-nopass" lay-filter="handle1-form-nopass">退回</button>
					<!-- <button class="layui-btn layui-bg-red" lay-submit id="handle1-form-reject" lay-filter="handle1-form-reject">拒绝</button> -->
					<button class="layui-btn" id="handle1-form-cancel">取消</button>
				</div>
			</div>
		</form>
	</script>
	<script type="text/html" id="user-form-html">
		<form class="layui-form user-form" action="" lay-filter="user-form">
			<div class="layui-form-item">
				<label class="layui-form-label">部门</label>
				<div class="layui-input-block">
					<input type="text" name="department" id="department" readonly="readonly" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">岗位</label>
				<div class="layui-input-block">
					<input type="text" name="job" id="job" autocomplete="off" readonly="readonly" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">执行人</label>
				<div class="layui-input-block">
					<select name="user" lay-filter="user" id="user" lay-verify="required" lay-search>
					</select>
				</div>
			</div>
			<div class="layui-form-item layui-hide">
				<div class="layui-input-block">
					<button class="layui-btn" lay-submit id="user-form-submit" lay-filter="user-form-submit">提交</button>
				</div>
			</div>
		</form>
	</script>
	<script src="../../../plugins/layui-lasted/layui.js"></script>
	<script src="../js/common.js"></script>
	<script src="../js/upload.js"></script>
	<script src="form.js"></script>
	<script src="process/view.js"></script>
	<script src="user.js"></script>
	<script src="process/process.js"></script>
	<script src="myTodo/myTodo.js"></script>
	<script src="finished/finished.js"></script>
	<script src="zero.js"></script>
</html>