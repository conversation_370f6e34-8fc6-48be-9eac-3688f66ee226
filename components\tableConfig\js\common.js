//下载模板文件
var DownloadTemplateFile = function (row) {
	var type = row.ftype;
	var filepath = '';
	var filename = '';
	var format = ".xlsx";
	if (type == 2) {
		filepath = row.SECOND_FILEPATH;
		filename = row.TREE_NAME + "_二级表模板";
	} else if (type == 3) {
		filepath = row.THREE_FILEPATH;
		filename = row.TREE_NAME + "_三级表模板";
	} else if (type == 4) {
		filepath = row.PLAN_FILEPATH;
		filename = row.TREE_NAME + "_策划表模板";
	} else if (type == 5) {
		filepath = row.PHOTO_FILEPATH;
		filename = row.TREE_NAME + "_影像记录策划表模板";
	} else if (type == 6) {
		filepath = row.PHOTO_SUMMARY_FILEPATH;
		filename = row.TREE_NAME + "_影像记录汇总表模板";
	} else if (type == 7) {
		filepath = row.CERTIFICATE_FILEPATH;
		format = ".docx";
		filename = row.TREE_NAME + "_合格证模板";
	}
	var name = filename + format;
	var url = fileHandlerUrl + "/first/phase/download/file";
	var form = $("<form></form>").attr("action", url).attr("method", "post");
	form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", name));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", filepath));
	form.appendTo('body').submit().remove();
}

//查看模板文件
var viewTemplateFile = function (row) {
	var type = row.ftype;
	var filepath = '';
	var filename = '';
	var fileFormat = 'xlsx';
	if (type == 2) {
		filepath = row.SECOND_FILEPATH;
		filename = row.TREE_NAME + "_二级表模板" + new Date().getTime();
	} else if (type == 3) {
		filepath = row.THREE_FILEPATH;
		filename = row.TREE_NAME + "_三级表模板" + new Date().getTime();
	} else if (type == 4) {
		filepath = row.PLAN_FILEPATH;
		filename = row.TREE_NAME + "_策划表模板" + new Date().getTime();
	} else if (type == 5) {
		filepath = row.PHOTO_FILEPATH;
		filename = row.TREE_NAME + "_影像记录策划表模板" + new Date().getTime();
	} else if (type == 6) {
		filepath = row.PHOTO_SUMMARY_FILEPATH;
		filename = row.TREE_NAME + "_影像记录汇总表模板" + new Date().getTime();
	} else if (type == 7) {
		filepath = row.CERTIFICATE_FILEPATH;
		filename = row.TREE_NAME + "_合格证模板" + new Date().getTime();
		fileFormat = 'docx';
	}
	filepath = filepath.replace(/\\/g, "//");
	var url = fileHandlerUrl + "/first/phase/download/file?filePath=" + filepath + "&fileName=" +
		encodeURIComponent(
			filename) + "." + fileFormat;
	url += "&fullfilename=" + encodeURIComponent(filename) + "." + fileFormat + "&browser=" + browser();
	url = encodeURIComponent(url);
	window.open(sessionStorage.getItem('previewUrl') + '/onlinePreview?url=' + url);

	// previewfile({
	// 	FILEPATH: filepath,
	// 	FILE_NAME: filename,
	// 	FILE_FORMAT: fileFormat,
	// 	FILE_TYPE: "",
	// 	GATHERING_METHOD: ""
	// });

}

//下载模板文件
var downloadReportTpl = function (nodeName) {
	var cb_success = function (data) {
		if (data.success) {
			var name = data.data.FILENAME;
			var url = fileHandlerUrl + "/first/phase/download/file";
			var form = $("<form></form>").attr("action", url).attr("method", "post");
			form.append($("<input></input>").attr("type", "hidden").attr("name", "fileName").attr("value", name));
			form.append($("<input></input>").attr("type", "hidden").attr("name", "filePath").attr("value", data.data.FILEPATH));
			form.appendTo('body').submit().remove();
		} else {
			layer.alert(data.msg);
		}
	};
	//添加失败的弹窗
	var cb_error = function (xhr) {
		layer.alert('下载失败!', {
			icon: 2
		});
	};
	twxAjax(THING, "QueryQualityReportTpl", {
		nodeName: nodeName
	}, false, cb_success, cb_error);
}

//上传质量报告模板
function uploadReportTpl(nodeName) {
	var fileFlag = false;
	layer.open({
		title: "上传质量报告模板",
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: ['350px', '220px'],
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要导入的模板文件!', {
					icon: 2
				});
				return false;
			}
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = '';
			addTpl = $("#uploadHtml")[0].innerHTML;
			$("#uploadContent").append(addTpl);
			form.render(null, 'uploadForm');
		}
	});

	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + '/file/upload/excel/tpl',
		auto: false,
		accept: 'file',
		field: 'file',
		exts: 'doc|docx',
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;
			var o = obj.pushFile();
			var filename = '';
			for (var k in o) {
				var file = o[k];
				filename = file.name;
			}
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			// layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			var param = {};
			param.creator = sessionStorage.getItem('username');
			param.filePath = res[0].filePath;
			param.fileFormat = res[0].fileFormat;
			param.fileName = res[0].fileName;
			param.nodeName = nodeName;
			var cb_success = function (data) {
				if (data.success) {
					layer.closeAll();
					layer.msg(data.msg);
				} else {
					layer.alert(data.msg);
				}
			};
			//添加失败的弹窗
			var cb_error = function (xhr) {
				layer.alert('上传失败!', {
					icon: 2
				});
			};
			//同步新增
			twxAjax(THING, "AddQualityReportTpl", param, false, cb_success, cb_error);
		}
	});
	if (device.ie && device.ie < 10) {
		$("input[name='uploadFile']").change(function () {
			var filename = $(this).val();
			filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		});
	}
}

//上传excel
function uploadExcel(type) {
	var title = "";
	var exts = 'xls|xlsx';
	var datas = $('#' + tableId).datagrid('getSelections');

	if (datas.length == 0) {
		layer.alert('请选择一个模板！', {
			icon: 2
		});
		return false;
	}
	var tableData = datas[0];
	var tableType = tableData.TYPE;

	if (type == 2) {
		title = '上传二级表模板';
		if (tableType == 4) {
			layer.alert("无须" + title + "!", {
				icon: 2
			});
			return false;
		}
	} else if (type == 3) {
		title = '上传三级表模板';
	} else if (type == 4) {
		title = '上传策划模板';
	} else if (type == 5) {
		title = '上传影像记录策划模板';
	} else if (type == 7) {
		title = '上传合格证模板';
		exts = "doc|docx";
	}
	if (tableType == 3 || tableType == 6) {
		layer.alert("无须" + title + "!", {
			icon: 2
		});
		return false;
	}
	var area = ['350px', '220px'];

	var fileFlag = false;
	layer.open({
		title: title,
		type: 1,
		anim: false,
		openDuration: 200,
		isOutAnim: false,
		closeDuration: 200,
		shadeClose: false,
		// fixed: false,
		maxmin: false,
		resize: false, //不允许拉伸
		area: area,
		content: '<div id="uploadContent" style="padding-top: 15px;padding-right: 15px;"></div>',
		btn: ['确认', '取消'],
		yes: function () {
			if (!fileFlag) {
				layer.alert('请选择需要导入的excel文件!', {
					icon: 2
				});
				return false;
			}
			$('#uploadStart').click();
		},
		btn2: function () {
			layer.closeAll();
		},
		success: function () {
			var addTpl = '';
			addTpl = $("#uploadHtml")[0].innerHTML;
			$("#uploadContent").append(addTpl);
		}
	});

	form.render(null, 'uploadForm');

	var uploadInst = upload.render({
		elem: '#uploadChoice',
		url: fileHandlerUrl + '/file/upload/excel/tpl',
		auto: false,
		accept: 'file',
		field: 'file',
		exts: exts,
		bindAction: '#uploadStart',
		dataType: "json",
		choose: function (obj) {
			fileFlag = true;
			var o = obj.pushFile();
			var filename = '';
			for (var k in o) {
				var file = o[k];
				filename = file.name;
			}
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		},
		before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
			// layer.load(); //上传loading
		},
		done: function (res, index, upload) {
			var param = {};
			param.id = tableData.ID;
			param.modifier = sessionStorage.getItem('username');
			param.type = type;
			param.filepath = res[0].filePath;
			param.fileformat = res[0].fileFormat;
			var cb_success = function (data) {
				if (data.success) {
					layer.closeAll();
					refreshTable();
					layer.msg(data.msg);
				} else {
					layer.alert(data.msg);
				}
			};
			//添加失败的弹窗
			var cb_error = function (xhr) {
				layer.alert('上传失败!', {
					icon: 2
				});
			};
			//同步新增
			twxAjax(THING, "UpdateTableTplFile", param, false, cb_success, cb_error);
		}
	});
	if (device.ie && device.ie < 10) {
		$("input[name='uploadFile']").change(function () {
			var filename = $(this).val();
			filename = filename.substring(filename.lastIndexOf('\\') + 1, filename.length);
			$("#selectedFile").show();
			$("#selectedFileName").text(filename);
		});
	}
}

//加载按钮事件
function renderBtn() {
	$('#table_add').bind('click', function () {
		addTable();
	});
	$('#table_copy').bind('click', function () {
		copyTable();
	});
	$('#table_edit').bind('click', function () {
		editTable();
	});
	$('#table_del').bind('click', function () {
		deleteTable();
	});
	$('#table_save').bind('click', function () {
		save();
	});
	$('#table_show_all').bind('click', function () {
		showAllTable();
	});
	$('#param_add').bind('click', function () {
		addParam();
	});
	$('#param_edit').bind('click', function () {
		editParam();
	});
	$('#param_del').bind('click', function () {
		deleteParam();
	});
}