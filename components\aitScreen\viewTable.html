<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<link rel="stylesheet" href="../../plugins/layui-lasted/css/layui.css" type="text/css" />
		<link rel="stylesheet" type="text/css" href="../../css/HotStyle.css">
		<link rel="stylesheet" href="../../plugins/preview/preview.css">
		<link rel="stylesheet" type="text/css" href="../../plugins/ztree/css/contextMenu.css">

		<title>查看</title>
		<script src="../../plugins/easyui/jquery.min.js"></script>
		<script src="../../plugins/ztree/js/jquery.contextMenu.min.js"></script>
		<script src="../../plugins/layui-lasted/layui.js"></script>
		<script src="../../plugins/preview/preview.js"></script>
		<script src="../../plugins/preview/jquery.rotate.min.js"></script>
		<script src="../js/config/twxconfig.js"></script>
		<script src="../js/util.js"></script>
		<script src="../js/HandsonTableUtil.js"></script>
	</head>
	<body>
		<div id="tableContent" style="width: 100%;overflow: auto;">
			<div id="table" style="width: 100%;">
			</div>
		</div>
		<script>
			$("#tableContent").css({
				height: $(window).height() + "px"
			});

			function getQueryString(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
				var r = window.location.search.substr(1).match(reg);
				if (r != null) return decodeURI(r[2]);
				return null;
			}

			var htmlDataKey = getQueryString("htmlDataKey");
			var saveDataKey = getQueryString("saveDataKey");
			var tableHeaderKey = getQueryString("tableHeaderKey");
			//登录拦截
			var _dcTime = (new Date().getTime());
			if (sessionStorage.username) {
				//如果存在session的话 继续预览
				if (sessionStorage.getItem(htmlDataKey)) {
					var html = sessionStorage.getItem(htmlDataKey);
					var saveData = sessionStorage.getItem(saveDataKey);
					var tableHeader = sessionStorage.getItem(tableHeaderKey) || 0;
					var tableSel = $("#table");
					//添加表格
					var $table = $(html.replaceAll("\n", "<br>"));
					HotUtil.delTableImg($table, true);
					$table.addClass('data-table').show();
					if (tableHeader != "0" && tableHeader != undefined && tableHeader != "" && tableHeader != "0-0") {
						//如果存在表头的话 固定表头显示
						var $thead = $('<thead class="sticky-thead"></thead>');
						$thead.css({
							"top": "-1px",
						});

						var tableHeaders = HotUtil.getTableHeader(tableHeader);
						//处理表头的显示
						$table.find("tr").each(function(i, n) {
							if (i <= (tableHeaders.max - 1) && i >= (tableHeaders.min - 1)) {
								$thead.append($(n).clone(true));
								$(n).remove();
							}
						});
						$table.prepend($thead);
					}
					var colWidths = JSON.parse(saveData).colWidths || [];
					if (colWidths.length > 0) {
						var $colgroup = $('<colgroup></colgroup>');
						for (var i = 0; i < colWidths.length; i++) {
							$colgroup.append('<col width="' + colWidths[i] + '">');
						}
						$table.prepend($colgroup);
					}
					tableSel.append($table);
					tableSel.find(".layui-table tbody tr:hover").css("background-color", "");

					$table.find("tr").each(function(i, n) {
						$(n).find("td").each(function(j, m) {
							var contextEle = void 0;
							var menus = [];
							if ($(m).data("hasImg")) {
								menus.push({
									text: "查看图片",
									icon: '../dataTree/images/view.png',
									callback: function() {
										var ele = HotUtil.findDataTableEle(contextEle);
										if (!$(ele).data("hasImg")) {
											layer.msg("暂无图片");
											return false;
										}
										var imgs = $(ele).data("imgs");
										HotUtil.viewImageLayer(imgs, false, function() {}, function() {});
									}
								});

								$(m).contextMenu({
									width: 110,
									menu: menus,
									target: function(ele) {
										contextEle = ele;
									}
								});
							}
						})
					});

					$table.find("td").each(function(i, n) {
						var comment = $(n).attr("comment");
						if (comment) {
							$(n).mouseover(function() {
								layer.tips(comment, this);
							});

							$(n).mouseout(function() {
								layer.closeAll();
							});
						}
					});
				} else {
					alert("无表格数据！");
				}
			} else {
				if (location.href.indexOf('login.html') == -1) {
					location.href = '/DataPackageManagement/login.html?_dc=' + _dcTime;
					//如果需要跳转到登录页面则把sessionStorage中的变量清空
					sessionStorage.clear();
				}
			}
		</script>
	</body>
</html>