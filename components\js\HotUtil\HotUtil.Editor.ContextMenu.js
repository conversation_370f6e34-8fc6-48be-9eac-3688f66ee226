/**
 * HotUtil.Editor.ContextMenu.js - Handsontable 编辑器工具库 (上下文菜单模块)
 *
 * 负责构建和管理编辑器内的右键上下文菜单。
 */

/**
 * 调整上下文菜单位置，防止超出视口边界（16.0.1版本优化）
 */
HotUtil.adjustContextMenuPosition = function () {
    try {
        // 使用短延迟确保菜单完全渲染
        setTimeout(function () {
            var menuElement = document.querySelector('.htContextMenu');
            if (!menuElement || !menuElement.offsetParent) {
                return; // 菜单不存在或不可见
            }

            // 检查菜单是否已经调整过，避免重复处理
            var currentStyle = window.getComputedStyle(menuElement);
            if (currentStyle.position === 'fixed') {
                return;
            }

            var menuRect = menuElement.getBoundingClientRect();
            var viewportWidth = window.innerWidth || document.documentElement.clientWidth;
            var viewportHeight = window.innerHeight || document.documentElement.clientHeight;
            var margin = 10; // 统一边距

            var needsAdjustment = false;
            var newTop = menuRect.top;
            var newLeft = menuRect.left;

            // 检查右边界溢出
            if (menuRect.right > viewportWidth - margin) {
                newLeft = Math.max(margin, viewportWidth - menuRect.width - margin);
                needsAdjustment = true;
            }

            // 检查底部边界溢出
            if (menuRect.bottom > viewportHeight - margin) {
                newTop = Math.max(margin, viewportHeight - menuRect.height - margin);
                needsAdjustment = true;
            }

            // 确保不超出左边界
            if (newLeft < margin) {
                newLeft = margin;
                needsAdjustment = true;
            }

            // 确保不超出顶部边界
            if (newTop < margin) {
                newTop = margin;
                needsAdjustment = true;
            }

            // 只在真正需要时才调整位置
            if (needsAdjustment) {
                // 应用新位置
                menuElement.style.position = 'fixed';
                menuElement.style.top = newTop + 'px';
                menuElement.style.left = newLeft + 'px';
                menuElement.style.zIndex = '9999';
            }

        }, 10); // 减少延迟时间

    } catch (error) {
        console.warn('调整上下文菜单位置时出错:', error);
    }
};

/**
 * 获取右键菜单项
 * @param {Object} treeNode
 * @returns {Object}
 */
HotUtil.getContextItems = function (treeNode) {
    var obj = {
        row_above: {
            name: '上方插入行'
        },
        row_below: {
            name: '下方插入行'
        },
        sp1: '---------',
        col_left: {
            name: '左方插入列',
            disabled() {
                return HotUtil.disableColContextMenu(this);
            }
        },
        col_right: {
            name: '右方插入列',
            disabled() {
                return HotUtil.disableColContextMenu(this);
            }
        },
        sp2: '---------',
        remove_row: {
            name: '移除该行',
            disabled() {
                //选中的单元格是否存在锁定的
                var hasLock = HotUtil.atLeastOneReadOnly(this);
                if (!hasLock) {
                    //检测选中单元格同行是否存在锁定的
                    var selectedRange = HotUtil.getSelectedRange()[0];
                    for (var row = selectedRange.from.row; row <= selectedRange.to
                        .row; row++) {
                        for (var col = 0; col < hot.countCols(); col++) {
                            if (col > -1 && row > -1) {
                                if (this.getCellMeta(row, col).readOnly) {
                                    hasLock = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                return hasLock;
            }
        },
        remove_col: {
            name: '移除该列',
            disabled() {
                return HotUtil.disableColContextMenu(this);
            }
        },
        sp3: '---------',
        alignment: {
            name: '对齐',
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        // borders: {
        // 	name: '边框'
        // },
        fontSize: {
            name() {
                return '字体大小';
            },
            submenu: {
                items: HotUtil.fontSizeItems()
            },
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        fontColor: {
            name() {
                return '字体颜色';
            },
            submenu: {
                items: HotUtil.fontColorItems()
            },
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        mergeCells: {
            name: function name() {
                var sel = this.getSelectedLast();
                if (sel) {
                    var info = this.getPlugin('MergeCells').mergedCellsCollection.get(
                        sel[0], sel[1]);

                    if (info.row === sel[0] && info.col === sel[1] && info.row + info
                        .rowspan - 1 === sel[2] && info.col + info.colspan - 1 === sel[
                        3]) {
                        return "取消合并";
                    }
                }
                return "合并";
            },
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        bold: { // Own custom option
            name() { // `name` can be a string or a function
                return '加粗'; // Name can contain HTML
            },
            callback(key, selection, clickEvent) { // Callback for specific option
                HotUtil.dealClass("c-bold", "add");
            },
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        cancelBold: { // Own custom option
            name() { // `name` can be a string or a function
                return '取消加粗'; // Name can contain HTML
            },
            callback(key, selection, clickEvent) { // Callback for specific option
                HotUtil.dealClass("c-bold", "cancel");
            },
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        commentsAddEdit: {
            name() {
                // 16.0.1版本兼容性修复：使用getCommentAtCell替代checkSelectionCommentsConsistency
                var range = this.getSelectedRangeLast();
                if (range && range.highlight && range.highlight.isCell()) {
                    var commentsPlugin = this.getPlugin("Comments");
                    if (commentsPlugin && commentsPlugin.getCommentAtCell(range.highlight.row, range.highlight.col)) {
                        return "编辑批注";
                    }
                }

                return "添加批注";
            },
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        commentsRemove: {
            name() {
                return "删除批注";
            },
            disabled() {
                return HotUtil.atLeastOneReadOnly(this);
            }
        },
        make_read_only: {
            name() {
                var label = "锁定";
                if (HotUtil.atLeastOneReadOnly(this)) {
                    label = "解除锁定";
                }
                return label;
            },
            callback(key, selection, clickEvent) {
                var _this = this;
                var atLeastOneReadOnly = HotUtil.atLeastOneReadOnly(_this);

                var label = "锁定";
                if (atLeastOneReadOnly) {
                    label = "解除锁定";
                }
                var log = {};
                log.operation = label + "单元格";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;
                var locations = "";
                var tdCount = 0;
                HotUtil.eachSelectedRange(_this, function (r, c) {
                    locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
                    tdCount++;
                    _this.setCellMeta(r, c, 'readOnly', !atLeastOneReadOnly);
                    if (atLeastOneReadOnly) {
                        //解除锁定需要清除签名
                        var meta = _this.getCellMeta(r, c);
                        var eles = meta.eles || [];
                        var newEles = [];
                        for (var e = 0; e < eles.length; e++) {
                            if (eles[e].type != 'sign') {
                                newEles.push(eles[e]);
                            }
                        }
                        _this.setCellMeta(r, c, 'eles', newEles);
                    }
                });
                locations = locations.substring(0, locations.length - 1);
                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中" + label +
                    "了" + tdCount + "个单元格，分别是" + locations;
                log.reqResult = 1;
                addConfirmLog(log);
                _this.render();
            },
            disabled() {
                if (funcIdent == "tpl") {
                    return true;
                } else {
                    if (contains(sessionStorage.getItem('funcids').split(','), 'func-' +
                        funcIdent + '-unlock-row')) {
                        return false;
                    } else {
                        return HotUtil.atLeastOneReadOnly(this);
                    }
                }

            }
        },
        setSignTd: {
            name() {
                return '上传签章';
            },
            callback(key, selection, clickEvent) {

                var log = {};
                log.operation = "上传签章";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;
                var content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中的";
                var locations = "";
                var _this = this;
                var srs = HotUtil.getSelectedRange();
                HotUtil.uploadSignLayer(function (src, date, signName) {
                    HotUtil.eachArrays(srs, _this, function (r, c) {
                        locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
                        var eles = _this.getCellMeta(r, c).eles || [];
                        eles.push({
                            type: "sign",
                            date: date,
                            signName: signName,
                            workno: sessionStorage.getItem('workno'),
                            src: src
                        });
                        _this.setCellMeta(r, c, "eles", eles);
                        //添加到签名数据库中
                        twxAjax(THING, "AddOneSign", {
                            img: src,
                            creator: sessionStorage.getItem(
                                "username"),
                            id: treeNode.ID
                        });
                    });
                    locations = locations.substring(0, locations.length - 1);
                    content = content + locations;
                    content += "上传了签章，签章日期为：" + date + "，签章路径为：" + src;
                    log.content = content;
                    log.reqResult = 1;
                    addConfirmLog(log);
                    _this.render();
                });
            },
            disabled() {
                if (funcIdent == "tpl") {
                    return true;
                } else {
                    return !HotUtil.allReadOnly(this);
                }
            }
        },
        setImgTd: {
            name() {
                return '上传图片';
            },
            callback(key, selection, clickEvent) {
                var _this = this;
                var srs = HotUtil.getSelectedRange();
                var log = {};
                log.operation = "上传图片";
                log.tablePid = treeNode.PID;
                log.tableId = treeNode.ID;
                log.content = "在节点【" + treeNode.NAME + "（" + treeNode.ID + "）】表中的";
                var locations = "";
                HotUtil.insertImageLayer(treeNode, function (datas, layerIndex) {
                    HotUtil.eachArrays(srs, _this, function (r, c) {
                        locations += "第" + (r + 1) + "行第" + (c + 1) + "列、";
                        var eles = _this.getCellMeta(r, c).eles || [];
                        for (var i = 0; i < datas.length; i++) {
                            var rowData = datas[i];
                            if (rowData.success) {
                                var params = {};
                                params.id = rowData.index;
                                params.photoPath = rowData.photoPath;
                                params.photoFormat = rowData.photoFormat;
                                params.photoName = rowData.photoName;
                                params.photoShowNum = rowData.photoShowNum;
                                params.type = "photo";
                                params.src = "/File" + rowData.photoPath;
                                params.class = "sign-img photo";
                                params.date = layui.util.toDateString(
                                    new Date(), 'yyyy-MM-dd');
                                eles.push(params);
                            }
                        }
                        _this.setCellMeta(r, c, "eles", eles);
                    });
                    locations = locations.substring(0, locations.length - 1);
                    var photoText = "[";
                    var photoCount = 0;
                    for (var i = 0; i < datas.length; i++) {
                        var rowData = datas[i];
                        if (rowData.success) {
                            photoText += "{照片名称：" + (rowData.photoName + "." +
                                rowData.photoFormat) +
                                "，路径：" + rowData.photoPath + "，大小：" + rowData
                                    .photoSize +
                                "，编号：" + rowData.photoShowNum + "},";
                            //添加到图片数据库中
                            twxAjax(THING, "AddOnePhoto", {
                                data: {
                                    id: treeNode.ID,
                                    photoName: rowData.photoName,
                                    photoPath: rowData.photoPath,
                                    photoFormat: rowData.photoFormat,
                                    photoNumber: rowData.photoNumber,
                                    photoSize: rowData.photoSize,
                                    creator: sessionStorage.getItem(
                                        "username")
                                }
                            });
                            photoCount++;
                        }
                    }
                    photoText = photoText.substring(0, photoText.length - 1);
                    photoText += "]";
                    log.content = log.content + locations + "上传了" + photoCount +
                        "张图片,分别是" + photoText;
                    log.reqResult = 1;
                    addConfirmLog(log);
                    _this.render();
                    layer.close(layerIndex);
                });
            },
            disabled() {
                if (funcIdent == "tpl") {
                    return true;
                } else {
                    return HotUtil.atLeastOneReadOnly(this);
                }
            }
        },
        viewImgTd: {
            name() {
                return '查看图片';
            },
            callback(key, selection, clickEvent) {
                var _this = this;
                var row = selection[0].start.row;
                var col = selection[0].start.col;
                var cellMeta = this.getCellMeta(row, col);
                var eles = cellMeta.eles;
                var readOnly = cellMeta.readOnly || false;
                var imgs = [];
                for (var i = 0; i < eles.length; i++) {
                    if (eles[i]['type'] == 'photo') {
                        imgs.push(eles[i]);
                    }
                }

                var delImgs = [];
                HotUtil.viewImageLayer(imgs, !readOnly, function () {
                    if ($(".picView-magnify-list").data("delete")) {
                        var newEles = [];
                        for (var i = 0; i < eles.length; i++) {
                            if (eles[i].type == 'photo') {
                                var isDel = false;
                                for (var j = 0; j < delImgs.length; j++) {
                                    if (eles[i].id == delImgs[j]) {
                                        isDel = true;
                                        break;
                                    }
                                }
                                if (!isDel) {
                                    newEles.push(eles[i]);
                                }
                            } else {
                                newEles.push(eles[i]);
                            }
                        }
                        _this.setCellMeta(row, col, 'eles', newEles);
                        _this.render();
                    }
                }, function ($i, $ul) {
                    var photoId = $i.attr("photo-id");
                    $i.parent().parent().remove();
                    $ul.data("delete", true);
                    delImgs.push(photoId);
                });

            },
            disabled() {
                var disable = true;
                var selectedRange = HotUtil.getSelectedRange();
                if (selectedRange.length == 1) {
                    var from = selectedRange[0].from;
                    var to = selectedRange[0].to;
                    var cellMeta = this.getCellMeta(from.row, from.col);
                    var colspan = cellMeta['colspan'] || 1;
                    var rowspan = cellMeta['rowspan'] || 1;
                    var isSingleCell = false;
                    if (from.row == to.row && from.col == to.col) {
                        isSingleCell = true;
                    } else {
                        if ((from.row + rowspan - 1) == to.row && (from.col + colspan -
                            1) == to.col) {
                            isSingleCell = true;
                        }
                    }

                    if (isSingleCell) {
                        if (cellMeta.eles && cellMeta.eles.length > 0) {
                            var eles = cellMeta.eles;
                            for (var i = 0; i < eles.length; i++) {
                                if (eles[i]['type'] == 'photo') {
                                    disable = false;
                                    break;
                                }
                            }
                        }
                    }
                }
                return disable;
            }
        },
    };
    obj['setSignBox'] = {
        name: function () {
            // 获取选中区域
            var selection = this.getSelected() || [];
            if (selection.length === 0) {
                return '设定为签署框';
            }

            // 检查选中区域是否包含已设置签署框的单元格
            for (var index = 0; index < selection.length; index += 1) {
                var [row1, column1, row2, column2] = selection[index];
                var startRow = Math.min(row1, row2);
                var endRow = Math.max(row1, row2);
                var startCol = Math.min(column1, column2);
                var endCol = Math.max(column1, column2);

                for (var row = startRow; row <= endRow; row++) {
                    for (var col = startCol; col <= endCol; col++) {
                        var currentClassName = this.getCellMeta(row, col).className || '';
                        if (currentClassName.indexOf('sign-box') !== -1) {
                            return '取消设定签署框';
                        }
                    }
                }
            }

            return '设定为签署框';
        },
        callback: function (key, selection) {
            HotUtil.setSignBox(this, selection);
        },
        disabled() {
            // 如果表格状态不是编辑状态，则禁用此选项
            // 不管选中的单元格是否锁定都可以操作
            if (treeNode && treeNode.TABLE_STATUS !== 'edit') {
                return true;
            }
            return false;
        }
    }
    obj['setSignUsers'] = {
        name: function () {
            return '设定签署人员';
        },
        callback: function (key, selection) {
            HotUtil.setSignUsers(this, selection);
        },
        disabled() {
            // 如果表格状态不是编辑状态，则禁用此选项
            if (treeNode && treeNode.TABLE_STATUS !== 'edit') {
                return true;
            }
            // 检查选中区域是否包含签署框类型的单元格
            var selection = this.getSelected() || [];
            if (selection.length === 0) {
                return true;
            }

            var hasSignBox = false;
            for (var index = 0; index < selection.length; index += 1) {
                var [row1, column1, row2, column2] = selection[index];
                var startRow = Math.min(row1, row2);
                var endRow = Math.max(row1, row2);
                var startCol = Math.min(column1, column2);
                var endCol = Math.max(column1, column2);

                for (var row = startRow; row <= endRow; row++) {
                    for (var col = startCol; col <= endCol; col++) {
                        var currentClassName = this.getCellMeta(row, col).className || '';
                        if (currentClassName.indexOf('sign-box') !== -1) {
                            hasSignBox = true;
                            break;
                        }
                    }
                    if (hasSignBox) break;
                }
                if (hasSignBox) break;
            }

            return !hasSignBox; // 只有当选中区域包含签署框时才启用
        }
    }
    if (HotUtil.workType !== '') {
        obj['postType'] = {
            name() {
                return '设置签名岗位';
            },
            submenu: {
                items: HotUtil.postTypeItems()
            },
            disabled() {
                if (HotUtil.getSelectedRange().length === 0) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    }

    return obj;
};

HotUtil.postTypeItems = function () {
    var items = [];
    for (var i = 0; i < HotUtil.postTypes.length; i++) {
        var obj = {};
        obj.key = 'postType:' + HotUtil.postTypes[i]['NAME'];
        obj.name = HotUtil.postTypes[i]['NAME'];
        obj.callback = function (key, selection, clickEvent) {
            HotUtil.setPostType(key.split(":")[1]);
        }
        items.push(obj);
    }
    return items;
};

HotUtil.setPostType = function (postType) {
    var selected = HotUtil.getSelecteds();
    for (var i = 0; i < selected.length; i++) {
        var obj = selected[i];
        hot.setCellMeta(obj.row, obj.col, 'postType', postType);
    }
    hot.render();
};