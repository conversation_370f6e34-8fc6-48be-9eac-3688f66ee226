<!--docxjs library predefined styles--><style>
.docx-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.docx-wrapper>section.docx { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }
.docx { color: black; hyphens: auto; text-underline-position: from-font; }
section.docx { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.docx>article { margin-bottom: auto; z-index: 1; }
section.docx>footer { z-index: 1; }
.docx table { border-collapse: collapse; }
.docx table td, .docx table th { vertical-align: top; }
.docx p { margin: 0pt; min-height: 1em; }
.docx span { white-space: pre-wrap; overflow-wrap: break-word; }
.docx a { color: inherit; text-decoration: inherit; }
</style><!--docxjs document theme values--><style>.docx {
  --docx-majorHAnsi-font: Calibri Light;
  --docx-minorHAnsi-font: Calibri;
  --docx-dk1-color: #000000;
  --docx-lt1-color: #FFFFFF;
  --docx-dk2-color: #44546A;
  --docx-lt2-color: #E7E6E6;
  --docx-accent1-color: #4472C4;
  --docx-accent2-color: #ED7D31;
  --docx-accent3-color: #A5A5A5;
  --docx-accent4-color: #FFC000;
  --docx-accent5-color: #5B9BD5;
  --docx-accent6-color: #70AD47;
  --docx-hlink-color: #0563C1;
  --docx-folHlink-color: #954F72;
}
</style><!--docxjs document styles--><style>.docx span {
  font-family: var(--docx-minorHAnsi-font);
  min-height: 11.00pt;
  font-size: 11.00pt;
}
.docx p {
  margin-bottom: 8.00pt;
  line-height: 1.08;
}
.docx table, table.docx_tablenormal td {
  padding-top: 0.00pt;
  padding-left: 5.40pt;
  padding-bottom: 0.00pt;
  padding-right: 5.40pt;
}
span.docx_placeholdertext {
  color: #808080;
}
</style><div class="docx-wrapper"><section class="docx" style="padding: 42.5pt 42.5pt 42.5pt 70.85pt; width: 595.3pt; min-height: 841.9pt;"><article><p><span><math><mrow><mo>{</mo><mrow><mtable><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">1÷2=x</ms></mrow></mtd></mtr><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">3+4=y</ms></mrow></mtd></mtr><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">5*6=z</ms></mrow></mtd></mtr><mtr><mtd><mrow><mroot><mrow><ms style="font-family: &quot;Cambria Math&quot;;">7</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">3</ms></mn></mroot><ms style="font-family: &quot;Cambria Math&quot;;">=w</ms></mrow></mtd></mtr><mtr><mtd><mrow><msub><mrow><ms style="font-family: &quot;Cambria Math&quot;;">x</ms></mrow><mn><msup><mrow><ms style="font-family: &quot;Cambria Math&quot;;">y</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">2</ms></mn></msup></mn></msub><msup><mrow><ms style="font-family: &quot;Cambria Math&quot;;">e</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">-iωt</ms></mn></msup><msup><mrow><ms style="font-family: &quot;Cambria Math&quot;;">x</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">2</ms></mn></msup><mrow><msubsup><mo></mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">1</ms></mn></mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mn></mo></msubsup><ms style="font-family: &quot;Cambria Math&quot;;">Y</ms></mrow></mrow></mtd></mtr><mtr><mtd><mrow><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">dy</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">dx</ms></mrow></mfrac><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">Δ</ms><ms style="font-family: &quot;Cambria Math&quot;;">y</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">Δ</ms><ms style="font-family: &quot;Cambria Math&quot;;">x</ms></mrow></mfrac><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">∂y</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">∂x</ms></mrow></mfrac><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">δy</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">δx</ms></mrow></mfrac><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">π</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">2</ms></mrow></mfrac></mrow></mtd></mtr><mtr><mtd><mrow><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">-b±</ms><msqrt><mrow><msup><mrow><ms style="font-family: &quot;Cambria Math&quot;;">b</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">2</ms></mn></msup><ms style="font-family: &quot;Cambria Math&quot;;">-4ac</ms></mrow></msqrt></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">2a</ms></mrow></mfrac></mrow></mtd></mtr><mtr><mtd><mrow><mtable><mtr><mtd><mrow><mrow><munderover><mo>∫</mo><mo><mn></mn></mo><mo><mn></mn></mo></munderover><ms style="font-family: &quot;Cambria Math&quot;;">dx</ms></mrow><mrow><munderover><mo>∬</mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">2</ms></mn></mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">1</ms></mn></mo></munderover><mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">dy</ms></mrow></mrow><mrow><munderover><mo>∭</mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">2</ms></mn></mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">1</ms></mn></mo></munderover><mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">dθ</ms></mrow></mrow></mrow></mrow></mrow></mtd></mtr><mtr><mtd><mrow><mrow><munderover><mo>∑</mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">k</ms></mn></mo><mo><mn></mn></mo></munderover><mrow><mo>(</mo><mrow><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">k</ms></mrow></mfrac></mrow><mo>)</mo></mrow></mrow><mrow><munderover><mo>∑</mo><mo><mn><mtable><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">0≤ i ≤ m</ms></mrow></mtd></mtr><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">0&lt;j&lt;n </ms></mrow></mtd></mtr></mtable></mn></mo><mo><mn></mn></mo></munderover><ms style="font-family: &quot;Cambria Math&quot;;">P</ms><mrow><mo>(</mo><mrow><ms style="font-family: &quot;Cambria Math&quot;;">i,j</ms></mrow><mo>)</mo></mrow></mrow><mrow><munderover><mo>∏</mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">k=1</ms></mn></mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mn></mo></munderover><msub><mrow><ms style="font-family: &quot;Cambria Math&quot;;">A</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">k</ms></mn></msub></mrow><mrow><munderover><mo>⋃</mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">n=1</ms></mn></mo><mo><mn><ms style="font-family: &quot;Cambria Math&quot;;">m</ms></mn></mo></munderover><mrow><mo>(</mo><mrow><msub><mrow><ms style="font-family: &quot;Cambria Math&quot;;">X</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mn></msub><ms style="font-family: &quot;Cambria Math&quot;;">∩</ms><msub><mrow><ms style="font-family: &quot;Cambria Math&quot;;">Y</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mn></msub></mrow><mo>)</mo></mrow></mrow></mrow></mtd></mtr><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">f</ms><mrow><mo>(</mo><mrow><ms style="font-family: &quot;Cambria Math&quot;;">x</ms></mrow><mo>)</mo></mrow><ms style="font-family: &quot;Cambria Math&quot;;">=</ms><mrow><mo>{</mo><mrow><mtable><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">-x,  &amp;x&lt;0</ms></mrow></mtd></mtr><mtr><mtd><mrow><ms style="font-family: &quot;Cambria Math&quot;;">x,  &amp;x≥0</ms></mrow></mtd></mtr></mtable></mrow><mo></mo></mrow><mrow><mo>(</mo><mrow><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">k</ms></mrow></mfrac></mrow><mo>)</mo></mrow><mrow><mo>⟨</mo><mrow><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">k</ms></mrow></mfrac></mrow><mo>⟩</mo></mrow></mrow></mtd></mtr><mtr><mtd><mrow><mrow><ms><ms style="font-family: &quot;Cambria Math&quot;;">tan</ms></ms><mrow><ms style="font-family: &quot;Cambria Math&quot;;">θ</ms></mrow></mrow><ms style="font-family: &quot;Cambria Math&quot;;">=</ms><mfrac><mrow><mrow><ms><ms style="font-family: &quot;Cambria Math&quot;;">sin</ms></ms><mrow><ms style="font-family: &quot;Cambria Math&quot;;">θ</ms></mrow></mrow></mrow><mrow><mrow><ms><ms style="font-family: &quot;Cambria Math&quot;;">cos</ms></ms><mrow><ms style="font-family: &quot;Cambria Math&quot;;">θ</ms></mrow></mrow></mrow></mfrac></mrow></mtd></mtr><mtr><mtd><mrow><mrow style="text-decoration: overline;"><mrow><ms style="font-family: &quot;Cambria Math&quot;;">A</ms></mrow></mrow><mrow style="text-decoration: overline;"><mrow><ms style="font-family: &quot;Cambria Math&quot;;">ABC</ms></mrow></mrow><mrow style="text-decoration: overline;"><mrow><ms style="font-family: &quot;Cambria Math&quot;;">x⊕y</ms></mrow></mrow></mrow></mtd></mtr><mtr><mtd><mrow><mrow><ms><munder><mrow><ms style="font-family: &quot;Cambria Math&quot;;">lim</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">n→∞</ms></mrow></munder></ms><mrow><msup><mrow><mrow><mo>(</mo><mrow><ms style="font-family: &quot;Cambria Math&quot;;">1+</ms><mfrac><mrow><ms style="font-family: &quot;Cambria Math&quot;;">1</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mrow></mfrac></mrow><mo>)</mo></mrow></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">n</ms></mn></msup></mrow></mrow><mrow><ms><munder><mrow><ms style="font-family: &quot;Cambria Math&quot;;">max</ms></mrow><mrow><ms style="font-family: &quot;Cambria Math&quot;;">0≤x≤1</ms></mrow></munder></ms><mrow><ms style="font-family: &quot;Cambria Math&quot;;">x</ms><msup><mrow><ms style="font-family: &quot;Cambria Math&quot;;">e</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">-</ms><msup><mrow><ms style="font-family: &quot;Cambria Math&quot;;">x</ms></mrow><mn><ms style="font-family: &quot;Cambria Math&quot;;">2</ms></mn></msup></mn></msup></mrow></mrow></mrow></mtd></mtr><mtr><mtd><mrow><mrow><mrow><mover><mrow><ms style="font-family: &quot;Cambria Math&quot;;">yields</ms></mrow><mo>→</mo></mover></mrow></mrow></mrow></mtd></mtr><mtr><mtd><mrow><mrow><mo>(</mo><mrow><mtable><mtr><mtd><ms style="font-family: &quot;Cambria Math&quot;;">1</ms></mtd><mtd><ms style="font-family: &quot;Cambria Math&quot;;">⋯</ms></mtd><mtd><ms style="font-family: &quot;Cambria Math&quot;;">10</ms></mtd></mtr><mtr><mtd><ms style="font-family: &quot;Cambria Math&quot;;">⋮</ms></mtd><mtd><ms style="font-family: &quot;Cambria Math&quot;;">⋱</ms></mtd><mtd><ms style="font-family: &quot;Cambria Math&quot;;">⋮</ms></mtd></mtr><mtr><mtd><ms style="font-family: &quot;Cambria Math&quot;;">20</ms></mtd><mtd><ms style="font-family: &quot;Cambria Math&quot;;">⋯</ms></mtd><mtd><ms style="font-family: &quot;Cambria Math&quot;;">30</ms></mtd></mtr></mtable></mrow><mo>)</mo></mrow></mrow></mtd></mtr></mtable></mrow></mtd></mtr></mtable></mrow><mo></mo></mrow></math></span></p></article></section></div>