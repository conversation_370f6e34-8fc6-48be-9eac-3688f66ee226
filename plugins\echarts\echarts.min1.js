!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t){var e={},n={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge\/([\d.]+)/),a=/micromessenger/i.test(t);return i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),o&&(n.edge=!0,n.version=o[1]),a&&(n.weChat=!0),{browser:n,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!n.ie&&!n.edge,pointerEventsSupported:"onpointerdown"in window&&(n.edge||n.ie&&n.version>=11),domSupported:"undefined"!=typeof document}}function n(t,e){"createCanvas"===t&&(Rv=null),zv[t]=e}function i(t){if(null==t||"object"!=typeof t)return t;var e=t,n=Tv.call(t);if("[object Array]"===n){if(!R(t)){e=[];for(var r=0,o=t.length;o>r;r++)e[r]=i(t[r])}}else if(Cv[n]){if(!R(t)){var a=t.constructor;if(t.constructor.from)e=a.from(t);else{e=new a(t.length);for(var r=0,o=t.length;o>r;r++)e[r]=i(t[r])}}}else if(!Iv[n]&&!R(t)&&!C(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=i(t[s]))}return e}function r(t,e,n){if(!S(e)||!S(t))return n?i(e):t;for(var o in e)if(e.hasOwnProperty(o)){var a=t[o],s=e[o];!S(s)||!S(a)||_(s)||_(a)||C(s)||C(a)||M(s)||M(a)||R(s)||R(a)?!n&&o in t||(t[o]=i(e[o],!0)):r(a,s,n)}return t}function o(t,e){for(var n=t[0],i=1,o=t.length;o>i;i++)n=r(n,t[i],e);return n}function a(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function s(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}function l(){return Rv||(Rv=Ev().getContext("2d")),Rv}function u(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return n}return-1}function h(t,e){function n(){}var i=t.prototype;n.prototype=e.prototype,t.prototype=new n;for(var r in i)i.hasOwnProperty(r)&&(t.prototype[r]=i[r]);t.prototype.constructor=t,t.superClass=e}function c(t,e,n){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,s(t,e,n)}function d(t){return t?"string"==typeof t?!1:"number"==typeof t.length:void 0}function f(t,e,n){if(t&&e)if(t.forEach&&t.forEach===Dv)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;r>i;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function p(t,e,n){if(t&&e){if(t.map&&t.map===Lv)return t.map(e,n);for(var i=[],r=0,o=t.length;o>r;r++)i.push(e.call(n,t[r],r,t));return i}}function g(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===Ov)return t.reduce(e,n,i);for(var r=0,o=t.length;o>r;r++)n=e.call(i,n,t[r],r,t);return n}}function v(t,e,n){if(t&&e){if(t.filter&&t.filter===kv)return t.filter(e,n);for(var i=[],r=0,o=t.length;o>r;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function m(t,e,n){if(t&&e)for(var i=0,r=t.length;r>i;i++)if(e.call(n,t[i],i,t))return t[i]}function y(t,e){var n=Pv.call(arguments,2);return function(){return t.apply(e,n.concat(Pv.call(arguments)))}}function x(t){var e=Pv.call(arguments,1);return function(){return t.apply(this,e.concat(Pv.call(arguments)))}}function _(t){return"[object Array]"===Tv.call(t)}function w(t){return"function"==typeof t}function b(t){return"[object String]"===Tv.call(t)}function S(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function M(t){return!!Iv[Tv.call(t)]}function I(t){return!!Cv[Tv.call(t)]}function C(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function T(t){return t!==t}function A(){for(var t=0,e=arguments.length;e>t;t++)if(null!=arguments[t])return arguments[t]}function D(t,e){return null!=t?t:e}function k(t,e,n){return null!=t?t:null!=e?e:n}function P(){return Function.call.apply(Pv,arguments)}function L(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function O(t,e){if(!t)throw new Error(e)}function z(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function E(t){t[Bv]=!0}function R(t){return t[Bv]}function B(t){function e(t,e){n?i.set(t,e):i.set(e,t)}var n=_(t);this.data={};var i=this;t instanceof B?t.each(e):t&&f(t,e)}function N(t){return new B(t)}function F(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function V(){}function H(t,e){var n=new Fv(2);return null==t&&(t=0),null==e&&(e=0),n[0]=t,n[1]=e,n}function W(t,e){return t[0]=e[0],t[1]=e[1],t}function G(t){var e=new Fv(2);return e[0]=t[0],e[1]=t[1],e}function Z(t,e,n){return t[0]=e,t[1]=n,t}function X(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Y(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function U(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function j(t){return Math.sqrt(q(t))}function q(t){return t[0]*t[0]+t[1]*t[1]}function $(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t}function K(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t}function Q(t,e){return t[0]*e[0]+t[1]*e[1]}function J(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function te(t,e){var n=j(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function ee(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function ne(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function ie(t,e){return t[0]=-e[0],t[1]=-e[1],t}function re(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function oe(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function ae(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function se(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}function le(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function ue(t,e){return{target:t,topTarget:e&&e.topTarget}}function he(t,e){var n=t._$eventProcessor;return null!=e&&n&&n.normalizeQuery&&(e=n.normalizeQuery(e)),e}function ce(t,e,n,i,r,o){var a=t._$handlers;if("function"==typeof n&&(r=i,i=n,n=null),!i||!e)return t;n=he(t,n),a[e]||(a[e]=[]);for(var s=0;s<a[e].length;s++)if(a[e][s].h===i)return t;var l={h:i,one:o,query:n,ctx:r||t,callAtLast:i.zrEventfulCallAtLast},u=a[e].length-1,h=a[e][u];return h&&h.callAtLast?a[e].splice(u,0,l):a[e].push(l),t}function de(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Uv);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,d=0,f=0;s>d;d++){var p=1<<d;p&r||(c+=(f%2?-1:1)*t[n][d]*de(t,e-1,h,u,r|p,o),f++)}return o[a]=c,c}function fe(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=de(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;8>a;a++)for(var s=0;8>s;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*de(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}function pe(t,e,n,i,r){return ge(qv,e,i,r,!0)&&ge(t,n,qv[0],qv[1])}function ge(t,e,n,i,r){if(e.getBoundingClientRect&&Mv.domSupported&&!ye(e)){var o=e[jv]||(e[jv]={}),a=ve(e,o),s=me(a,o,r);if(s)return s(t,n,i),!0}return!1}function ve(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;4>o;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}function me(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=!0,s=[],l=[],u=0;4>u;u++){var h=t[u].getBoundingClientRect(),c=2*u,d=h.left,f=h.top;s.push(d,f),a=a&&o&&d===o[c]&&f===o[c+1],l.push(t[u].offsetLeft,t[u].offsetTop)}return a&&r?r:(e.srcCoords=s,e[i]=n?fe(l,s):fe(s,l))}function ye(t){return"CANVAS"===t.nodeName.toUpperCase()}function xe(t,e,n,i){return n=n||{},i||!Mv.canvasSupported?_e(t,e,n):Mv.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):_e(t,e,n),n}function _e(t,e,n){if(Mv.domSupported&&t.getBoundingClientRect){var i=e.clientX,r=e.clientY;if(ye(t)){var o=t.getBoundingClientRect();return n.zrX=i-o.left,void(n.zrY=r-o.top)}if(ge(Qv,t,i,r))return n.zrX=Qv[0],void(n.zrY=Qv[1])}n.zrX=n.zrY=0}function we(t){return t||window.event}function be(t,e,n){if(e=we(e),null!=e.zrX)return e;var i=e.type,r=i&&i.indexOf("touch")>=0;if(r){var o="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];o&&xe(t,o,e,n)}else xe(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var a=e.button;return null==e.which&&void 0!==a&&Kv.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}function Se(t,e,n,i){$v?t.addEventListener(e,n,i):t.attachEvent("on"+e,n)}function Me(t,e,n,i){$v?t.removeEventListener(e,n,i):t.detachEvent("on"+e,n)}function Ie(t){return 2===t.which||3===t.which}function Ce(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function Te(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function Ae(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:De}}function De(){Jv(this.event)}function ke(){}function Pe(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return i?nm:!0}return!1}function Le(t,e,n){var i=t.painter;return 0>e||e>i.getWidth()||0>n||n>i.getHeight()}function Oe(){var t=new om(6);return ze(t),t}function ze(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Ee(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Re(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function Be(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function Ne(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],u=Math.sin(n),h=Math.cos(n);return t[0]=i*h+a*u,t[1]=-i*u+a*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*o+u*l,t[5]=h*l-u*o,t}function Fe(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function Ve(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}function He(t){var e=Oe();return Ee(e,t),e}function We(t){return t>lm||-lm>t}function Ge(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function Ze(t){return t=Math.round(t),0>t?0:t>255?255:t}function Xe(t){return t=Math.round(t),0>t?0:t>360?360:t}function Ye(t){return 0>t?0:t>1?1:t}function Ue(t){return Ze(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function je(t){return Ye(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function qe(t,e,n){return 0>n?n+=1:n>1&&(n-=1),1>6*n?t+(e-t)*n*6:1>2*n?e:2>3*n?t+(e-t)*(2/3-n)*6:t}function $e(t,e,n){return t+(e-t)*n}function Ke(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Qe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function Je(t,e){bm&&Qe(bm,e),bm=wm.put(t,bm||e.slice())}function tn(t,e){if(t){e=e||[];var n=wm.get(t);if(n)return Qe(e,n);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in _m)return Qe(e,_m[i]),Je(t,e),e;if("#"!==i.charAt(0)){var r=i.indexOf("("),o=i.indexOf(")");if(-1!==r&&o+1===i.length){var a=i.substr(0,r),s=i.substr(r+1,o-(r+1)).split(","),l=1;switch(a){case"rgba":if(4!==s.length)return void Ke(e,0,0,0,1);l=je(s.pop());case"rgb":return 3!==s.length?void Ke(e,0,0,0,1):(Ke(e,Ue(s[0]),Ue(s[1]),Ue(s[2]),l),Je(t,e),e);case"hsla":return 4!==s.length?void Ke(e,0,0,0,1):(s[3]=je(s[3]),en(s,e),Je(t,e),e);case"hsl":return 3!==s.length?void Ke(e,0,0,0,1):(en(s,e),Je(t,e),e);default:return}}Ke(e,0,0,0,1)}else{if(4===i.length){var u=parseInt(i.substr(1),16);return u>=0&&4095>=u?(Ke(e,(3840&u)>>4|(3840&u)>>8,240&u|(240&u)>>4,15&u|(15&u)<<4,1),Je(t,e),e):void Ke(e,0,0,0,1)}if(7===i.length){var u=parseInt(i.substr(1),16);return u>=0&&16777215>=u?(Ke(e,(16711680&u)>>16,(65280&u)>>8,255&u,1),Je(t,e),e):void Ke(e,0,0,0,1)}}}}function en(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=je(t[1]),r=je(t[2]),o=.5>=r?r*(i+1):r+i-r*i,a=2*r-o;return e=e||[],Ke(e,Ze(255*qe(a,o,n+1/3)),Ze(255*qe(a,o,n)),Ze(255*qe(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function nn(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0===l)e=0,n=0;else{n=.5>u?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-o)/6+l/2)/l;i===s?e=d-c:r===s?e=1/3+h-d:o===s&&(e=2/3+c-h),0>e&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}function rn(t,e){var n=tn(t);if(n){for(var i=0;3>i;i++)n[i]=0>e?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:t[i]<0&&(n[i]=0);return hn(n,4===n.length?"rgba":"rgb")}}function on(t){var e=tn(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function an(t,e,n){if(e&&e.length&&t>=0&&1>=t){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=Ze($e(a[0],s[0],l)),n[1]=Ze($e(a[1],s[1],l)),n[2]=Ze($e(a[2],s[2],l)),n[3]=Ye($e(a[3],s[3],l)),n}}function sn(t,e,n){if(e&&e.length&&t>=0&&1>=t){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=tn(e[r]),s=tn(e[o]),l=i-r,u=hn([Ze($e(a[0],s[0],l)),Ze($e(a[1],s[1],l)),Ze($e(a[2],s[2],l)),Ye($e(a[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:o,value:i}:u}}function ln(t,e,n,i){return t=tn(t),t?(t=nn(t),null!=e&&(t[0]=Xe(e)),null!=n&&(t[1]=je(n)),null!=i&&(t[2]=je(i)),hn(en(t),"rgba")):void 0}function un(t,e){return t=tn(t),t&&null!=e?(t[3]=Ye(e),hn(t,"rgba")):void 0}function hn(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(n+=","+t[3]),e+"("+n+")"}}function cn(t,e){return t[e]}function dn(t,e,n){t[e]=n}function fn(t,e,n){return(e-t)*n+t}function pn(t,e,n){return n>.5?e:t}function gn(t,e,n,i,r){var o=t.length;if(1===r)for(var a=0;o>a;a++)i[a]=fn(t[a],e[a],n);else for(var s=o&&t[0].length,a=0;o>a;a++)for(var l=0;s>l;l++)i[a][l]=fn(t[a][l],e[a][l],n)}function vn(t,e,n){var i=t.length,r=e.length;if(i!==r){var o=i>r;if(o)t.length=r;else for(var a=i;r>a;a++)t.push(1===n?e[a]:Cm.call(e[a]))}for(var s=t[0]&&t[0].length,a=0;a<t.length;a++)if(1===n)isNaN(t[a])&&(t[a]=e[a]);else for(var l=0;s>l;l++)isNaN(t[a][l])&&(t[a][l]=e[a][l])}function mn(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(var r=0;i>r;r++)if(t[r]!==e[r])return!1}else for(var o=t[0].length,r=0;i>r;r++)for(var a=0;o>a;a++)if(t[r][a]!==e[r][a])return!1;return!0}function yn(t,e,n,i,r,o,a,s,l){var u=t.length;if(1===l)for(var h=0;u>h;h++)s[h]=xn(t[h],e[h],n[h],i[h],r,o,a);else for(var c=t[0].length,h=0;u>h;h++)for(var d=0;c>d;d++)s[h][d]=xn(t[h][d],e[h][d],n[h][d],i[h][d],r,o,a)}function xn(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function _n(t){if(d(t)){var e=t.length;if(d(t[0])){for(var n=[],i=0;e>i;i++)n.push(Cm.call(t[i]));return n}return Cm.call(t)}return t}function wn(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function bn(t){var e=t[t.length-1].value;return d(e&&e[0])?2:1}function Sn(t,e,n,i,r,o){var a=t._getter,s=t._setter,l="spline"===e,u=i.length;if(u){var h,c=i[0].value,f=d(c),p=!1,g=!1,v=f?bn(i):0;i.sort(function(t,e){return t.time-e.time}),h=i[u-1].time;for(var m=[],y=[],x=i[0].value,_=!0,w=0;u>w;w++){m.push(i[w].time/h);var b=i[w].value;if(f&&mn(b,x,v)||!f&&b===x||(_=!1),x=b,"string"==typeof b){var S=tn(b);S?(b=S,p=!0):g=!0}y.push(b)}if(o||!_){for(var M=y[u-1],w=0;u-1>w;w++)f?vn(y[w],M,v):!isNaN(y[w])||isNaN(M)||g||p||(y[w]=M);f&&vn(a(t._target,r),M,v);var I,C,T,A,D,k,P=0,L=0;if(p)var O=[0,0,0,0];var z=function(t,e){var n;if(0>e)n=0;else if(L>e){for(I=Math.min(P+1,u-1),n=I;n>=0&&!(m[n]<=e);n--);n=Math.min(n,u-2)}else{for(n=P;u>n&&!(m[n]>e);n++);n=Math.min(n-1,u-2)}P=n,L=e;var i=m[n+1]-m[n];if(0!==i)if(C=(e-m[n])/i,l)if(A=y[n],T=y[0===n?n:n-1],D=y[n>u-2?u-1:n+1],k=y[n>u-3?u-1:n+2],f)yn(T,A,D,k,C,C*C,C*C*C,a(t,r),v);else{var o;if(p)o=yn(T,A,D,k,C,C*C,C*C*C,O,1),o=wn(O);else{if(g)return pn(A,D,C);o=xn(T,A,D,k,C,C*C,C*C*C)}s(t,r,o)}else if(f)gn(y[n],y[n+1],C,a(t,r),v);else{var o;if(p)gn(y[n],y[n+1],C,O,1),o=wn(O);else{if(g)return pn(y[n],y[n+1],C);o=fn(y[n],y[n+1],C)}s(t,r,o)}},E=new Ge({target:t._target,life:h,loop:t._loop,delay:t._delay,onframe:z,ondestroy:n});return e&&"spline"!==e&&(E.easing=e),E}}}function Mn(t,e,n,i,r,o,a,s){function l(){h--,h||o&&o()}b(i)?(o=r,r=i,i=0):w(r)?(o=r,r="linear",i=0):w(i)?(o=i,i=0):w(n)?(o=n,n=500):n||(n=500),t.stopAnimation(),In(t,"",t,e,n,i,s);var u=t.animators.slice(),h=u.length;h||o&&o();for(var c=0;c<u.length;c++)u[c].done(l).start(r,a)}function In(t,e,n,i,r,o,a){var s={},l=0;for(var u in i)i.hasOwnProperty(u)&&(null!=n[u]?S(i[u])&&!d(i[u])?In(t,e?e+"."+u:u,n[u],i[u],r,o,a):(a?(s[u]=n[u],Cn(t,e,u,i[u])):s[u]=i[u],l++):null==i[u]||a||Cn(t,e,u,i[u]));l>0&&t.animate(e,!1).when(null==r?500:r,s).delay(o||0)}function Cn(t,e,n,i){if(e){var r={};r[e]={},r[e][n]=i,t.attr(r)}else t.attr(n,i)}function Tn(t,e,n,i){0>n&&(t+=n,n=-n),0>i&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}function An(t){for(var e=0;t>=Fm;)e|=1&t,t>>=1;return t+e}function Dn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;n>r&&i(t[r],t[r-1])<0;)r++;kn(t,e,r)}else for(;n>r&&i(t[r],t[r-1])>=0;)r++;return r-e}function kn(t,e,n){for(n--;n>e;){var i=t[e];t[e++]=t[n],t[n--]=i}}function Pn(t,e,n,i,r){for(i===e&&i++;n>i;i++){for(var o,a=t[i],s=e,l=i;l>s;)o=s+l>>>1,r(a,t[o])<0?l=o:s=o+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=a}}function Ln(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;s>l&&o(t,e[n+r+l])>0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;s>l&&o(t,e[n+r-l])<=0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}for(a++;l>a;){var h=a+(l-a>>>1);o(t,e[n+h])>0?a=h+1:l=h}return l}function On(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;s>l&&o(t,e[n+r-l])<0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;s>l&&o(t,e[n+r+l])>=0;)a=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;l>a;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function zn(t,e){function n(t,e){l[c]=t,u[c]=e,c+=1}function i(){for(;c>1;){var t=c-2;if(t>=1&&u[t-1]<=u[t]+u[t+1]||t>=2&&u[t-2]<=u[t]+u[t-1])u[t-1]<u[t+1]&&t--;else if(u[t]>u[t+1])break;o(t)}}function r(){for(;c>1;){var t=c-2;t>0&&u[t-1]<u[t+1]&&t--,o(t)}}function o(n){var i=l[n],r=u[n],o=l[n+1],h=u[n+1];u[n]=r+h,n===c-3&&(l[n+1]=l[n+2],u[n+1]=u[n+2]),c--;var d=On(t[o],t,i,r,0,e);i+=d,r-=d,0!==r&&(h=Ln(t[i+r-1],t,o,h,h-1,e),0!==h&&(h>=r?a(i,r,o,h):s(i,r,o,h)))}function a(n,i,r,o){var a=0;for(a=0;i>a;a++)d[a]=t[n+a];var s=0,l=r,u=n;if(t[u++]=t[l++],0!==--o){if(1===i){for(a=0;o>a;a++)t[u+a]=t[l+a];return void(t[u+o]=d[s])}for(var c,f,p,g=h;;){c=0,f=0,p=!1;do if(e(t[l],d[s])<0){if(t[u++]=t[l++],f++,c=0,0===--o){p=!0;break}}else if(t[u++]=d[s++],c++,f=0,1===--i){p=!0;break}while(g>(c|f));if(p)break;do{if(c=On(t[l],d,s,i,0,e),0!==c){for(a=0;c>a;a++)t[u+a]=d[s+a];if(u+=c,s+=c,i-=c,1>=i){p=!0;break}}if(t[u++]=t[l++],0===--o){p=!0;break}if(f=Ln(d[s],t,l,o,0,e),0!==f){for(a=0;f>a;a++)t[u+a]=t[l+a];if(u+=f,l+=f,o-=f,0===o){p=!0;break}}if(t[u++]=d[s++],1===--i){p=!0;break}g--}while(c>=Vm||f>=Vm);if(p)break;0>g&&(g=0),g+=2}if(h=g,1>h&&(h=1),1===i){for(a=0;o>a;a++)t[u+a]=t[l+a];t[u+o]=d[s]}else{if(0===i)throw new Error;for(a=0;i>a;a++)t[u+a]=d[s+a]}}else for(a=0;i>a;a++)t[u+a]=d[s+a]}function s(n,i,r,o){var a=0;for(a=0;o>a;a++)d[a]=t[r+a];var s=n+i-1,l=o-1,u=r+o-1,c=0,f=0;if(t[u--]=t[s--],0!==--i){if(1===o){for(u-=i,s-=i,f=u+1,c=s+1,a=i-1;a>=0;a--)t[f+a]=t[c+a];return void(t[u]=d[l])}for(var p=h;;){var g=0,v=0,m=!1;do if(e(d[l],t[s])<0){if(t[u--]=t[s--],g++,v=0,0===--i){m=!0;break}}else if(t[u--]=d[l--],v++,g=0,1===--o){m=!0;break}while(p>(g|v));if(m)break;do{if(g=i-On(d[l],t,n,i,i-1,e),0!==g){for(u-=g,s-=g,i-=g,f=u+1,c=s+1,a=g-1;a>=0;a--)t[f+a]=t[c+a];if(0===i){m=!0;break}}if(t[u--]=d[l--],1===--o){m=!0;break}if(v=o-Ln(t[s],d,0,o,o-1,e),0!==v){for(u-=v,l-=v,o-=v,f=u+1,c=l+1,a=0;v>a;a++)t[f+a]=d[c+a];if(1>=o){m=!0;break}}if(t[u--]=t[s--],0===--i){m=!0;break}p--}while(g>=Vm||v>=Vm);if(m)break;0>p&&(p=0),p+=2}if(h=p,1>h&&(h=1),1===o){for(u-=i,s-=i,f=u+1,c=s+1,a=i-1;a>=0;a--)t[f+a]=t[c+a];t[u]=d[l]}else{if(0===o)throw new Error;for(c=u-(o-1),a=0;o>a;a++)t[c+a]=d[a]}}else for(c=u-(o-1),a=0;o>a;a++)t[c+a]=d[a]}var l,u,h=Vm,c=0,d=[];l=[],u=[],this.mergeRuns=i,this.forceMergeRuns=r,this.pushRun=n}function En(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(2>r)){var o=0;if(Fm>r)return o=Dn(t,n,i,e),void Pn(t,n,i,n+o,e);var a=new zn(t,e),s=An(r);do{if(o=Dn(t,n,i,e),s>o){var l=r;l>s&&(l=s),Pn(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}function Rn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function Bn(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,o=isNaN(o)?0:o,a=isNaN(a)?0:a;var s=t.createLinearGradient(i,o,r,a);return s}function Nn(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(a=a*i+n.x,s=s*r+n.y,l*=o);var u=t.createRadialGradient(a,s,0,a,s,l);return u}function Fn(){return!1}function Vn(t,e,n){var i=Ev(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left=0,a.top=0,a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}function Hn(t){if("string"==typeof t){var e=ty.get(t);return e&&e.image}return t}function Wn(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=ty.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?(e=o.image,!Zn(e)&&o.pending.push(a)):(e=new Image,e.onload=e.onerror=Gn,ty.put(t,e.__cachedImgObj={image:e,pending:[a]}),e.src=e.__zrImageSrc=t),e}return t}return e}function Gn(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function Zn(t){return t&&t.width&&t.height}function Xn(t,e){ay[t]=e}function Yn(t,e){e=e||oy;var n=t+":"+e;if(ey[n])return ey[n];for(var i=(t+"").split("\n"),r=0,o=0,a=i.length;a>o;o++)r=Math.max(ri(i[o],e).width,r);return ny>iy&&(ny=0,ey={}),ny++,ey[n]=r,r}function Un(t,e,n,i,r,o,a,s){return a?qn(t,e,n,i,r,o,a,s):jn(t,e,n,i,r,o,s)}function jn(t,e,n,i,r,o,a){var s=oi(t,e,r,o,a),l=Yn(t,e);r&&(l+=r[1]+r[3]);var u=s.outerHeight,h=$n(0,l,n),c=Kn(0,u,i),d=new Tn(h,c,l,u);return d.lineHeight=s.lineHeight,d}function qn(t,e,n,i,r,o,a,s){var l=ai(t,{rich:a,truncate:s,font:e,textAlign:n,textPadding:r,textLineHeight:o}),u=l.outerWidth,h=l.outerHeight,c=$n(0,u,n),d=Kn(0,h,i);return new Tn(c,d,u,h)}function $n(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Kn(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Qn(t,e,n){var i=e.textPosition,r=e.textDistance,o=n.x,a=n.y;r=r||0;var s=n.height,l=n.width,u=s/2,h="left",c="top";switch(i){case"left":o-=r,a+=u,h="right",c="middle";break;case"right":o+=r+l,a+=u,c="middle";break;case"top":o+=l/2,a-=r,h="center",c="bottom";break;case"bottom":o+=l/2,a+=s+r,h="center";break;case"inside":o+=l/2,a+=u,h="center",c="middle";break;case"insideLeft":o+=r,a+=u,c="middle";break;case"insideRight":o+=l-r,a+=u,h="right",c="middle";break;case"insideTop":o+=l/2,a+=r,h="center";break;case"insideBottom":o+=l/2,a+=s-r,h="center",c="bottom";break;case"insideTopLeft":o+=r,a+=r;break;case"insideTopRight":o+=l-r,a+=r,h="right";break;case"insideBottomLeft":o+=r,a+=s-r,c="bottom";break;case"insideBottomRight":o+=l-r,a+=s-r,h="right",c="bottom"}return t=t||{},t.x=o,t.y=a,t.textAlign=h,t.textVerticalAlign=c,t}function Jn(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=ti(e,n,i,r);for(var a=0,s=o.length;s>a;a++)o[a]=ei(o[a],r);return o.join("\n")}function ti(t,e,n,i){i=a({},i),i.font=e;var n=D(n,"...");i.maxIterations=D(i.maxIterations,2);var r=i.minChar=D(i.minChar,0);i.cnCharWidth=Yn("国",e);var o=i.ascCharWidth=Yn("a",e);i.placeholder=D(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;r>l&&s>=o;l++)s-=o;var u=Yn(n,e);return u>s&&(n="",u=0),s=t-u,i.ellipsis=n,i.ellipsisWidth=u,i.contentWidth=s,i.containerWidth=t,i}function ei(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=Yn(t,i);if(n>=o)return t;for(var a=0;;a++){if(r>=o||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?ni(t,r,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*r/o):0;t=t.substr(0,s),o=Yn(t,i)}return""===t&&(t=e.placeholder),t}function ni(t,e,n,i){for(var r=0,o=0,a=t.length;a>o&&e>r;o++){var s=t.charCodeAt(o);r+=s>=0&&127>=s?n:i}return o}function ii(t){return Yn("国",t)}function ri(t,e){return ay.measureText(t,e)}function oi(t,e,n,i,r){null!=t&&(t+="");var o=D(i,ii(e)),a=t?t.split("\n"):[],s=a.length*o,l=s,u=!0;if(n&&(l+=n[0]+n[2]),t&&r){u=!1;var h=r.outerHeight,c=r.outerWidth;if(null!=h&&l>h)t="",a=[];else if(null!=c)for(var d=ti(c-(n?n[1]+n[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=a.length;p>f;f++)a[f]=ei(a[f],d)}return{lines:a,height:s,outerHeight:l,lineHeight:o,canCacheByTextString:u}}function ai(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;for(var i,r=ry.lastIndex=0;null!=(i=ry.exec(t));){var o=i.index;o>r&&si(n,t.substring(r,o)),si(n,i[2],i[1]),r=ry.lastIndex}r<t.length&&si(n,t.substring(r,t.length));var a=n.lines,s=0,l=0,u=[],h=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;h&&(null!=d&&(d-=h[1]+h[3]),null!=f&&(f-=h[0]+h[2]));for(var p=0;p<a.length;p++){for(var g=a[p],v=0,m=0,y=0;y<g.tokens.length;y++){var x=g.tokens[y],_=x.styleName&&e.rich[x.styleName]||{},w=x.textPadding=_.textPadding,b=x.font=_.font||e.font,S=x.textHeight=D(_.textHeight,ii(b));if(w&&(S+=w[0]+w[2]),x.height=S,x.lineHeight=k(_.textLineHeight,e.textLineHeight,S),x.textAlign=_&&_.textAlign||e.textAlign,x.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+x.lineHeight>f)return{lines:[],width:0,height:0};x.textWidth=Yn(x.text,b);var M=_.textWidth,I=null==M||"auto"===M;if("string"==typeof M&&"%"===M.charAt(M.length-1))x.percentWidth=M,u.push(x),M=0;else{if(I){M=x.textWidth;var C=_.textBackgroundColor,T=C&&C.image;T&&(T=Hn(T),Zn(T)&&(M=Math.max(M,T.width*S/T.height)))}var A=w?w[1]+w[3]:0;M+=A;var P=null!=d?d-m:null;null!=P&&M>P&&(!I||A>P?(x.text="",x.textWidth=M=0):(x.text=Jn(x.text,P-A,b,c.ellipsis,{minChar:c.minChar}),x.textWidth=Yn(x.text,b),M=x.textWidth+A))}m+=x.width=M,_&&(v=Math.max(v,x.lineHeight))}g.width=m,g.lineHeight=v,s+=v,l=Math.max(l,m)}n.outerWidth=n.width=D(e.textWidth,l),n.outerHeight=n.height=D(e.textHeight,s),h&&(n.outerWidth+=h[1]+h[3],n.outerHeight+=h[0]+h[2]);for(var p=0;p<u.length;p++){var x=u[p],L=x.percentWidth;x.width=parseInt(L,10)/100*l}return n}function si(t,e,n){for(var i=""===e,r=e.split("\n"),o=t.lines,a=0;a<r.length;a++){var s=r[a],l={styleName:n,text:s,isLineHolder:!s&&!i};if(a)o.push({tokens:[l]});else{var u=(o[o.length-1]||(o[0]={tokens:[]})).tokens,h=u.length;1===h&&u[0].isLineHolder?u[0]=l:(s||!h||i)&&u.push(l)}}}function li(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&z(e)||t.textFont||t.font}function ui(t,e){var n,i,r,o,a=e.x,s=e.y,l=e.width,u=e.height,h=e.r;0>l&&(a+=l,l=-l),0>u&&(s+=u,u=-u),"number"==typeof h?n=i=r=o=h:h instanceof Array?1===h.length?n=i=r=o=h[0]:2===h.length?(n=r=h[0],i=o=h[1]):3===h.length?(n=h[0],i=o=h[1],r=h[2]):(n=h[0],i=h[1],r=h[2],o=h[3]):n=i=r=o=0;var c;n+i>l&&(c=n+i,n*=l/c,i*=l/c),r+o>l&&(c=r+o,r*=l/c,o*=l/c),i+r>u&&(c=i+r,i*=u/c,r*=u/c),n+o>u&&(c=n+o,n*=u/c,o*=u/c),t.moveTo(a+n,s),t.lineTo(a+l-i,s),0!==i&&t.arc(a+l-i,s+i,i,-Math.PI/2,0),t.lineTo(a+l,s+u-r),0!==r&&t.arc(a+l-r,s+u-r,r,0,Math.PI/2),t.lineTo(a+o,s+u),0!==o&&t.arc(a+o,s+u-o,o,Math.PI/2,Math.PI),t.lineTo(a,s+n),0!==n&&t.arc(a+n,s+n,n,Math.PI,1.5*Math.PI)}function hi(t){return ci(t),f(t.rich,ci),t}function ci(t){if(t){t.font=li(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||ly[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||uy[n]?n:"top";var i=t.textPadding;i&&(t.textPadding=L(t.textPadding))}}function di(t,e,n,i,r,o){i.rich?pi(t,e,n,i,r,o):fi(t,e,n,i,r,o)}function fi(t,e,n,i,r,o){var a,s=yi(i),l=!1,u=e.__attrCachedBy===Zm.PLAIN_TEXT;o!==Xm?(o&&(a=o.style,l=!s&&u&&a),e.__attrCachedBy=s?Zm.NONE:Zm.PLAIN_TEXT):u&&(e.__attrCachedBy=Zm.NONE);var h=i.font||sy;l&&h===(a.font||sy)||(e.font=h);var c=t.__computedFont;t.__styleFont!==h&&(t.__styleFont=h,c=t.__computedFont=e.font);var d=i.textPadding,f=i.textLineHeight,p=t.__textCotentBlock;(!p||t.__dirtyText)&&(p=t.__textCotentBlock=oi(n,c,d,f,i.truncate));var g=p.outerHeight,v=p.lines,m=p.lineHeight,y=wi(dy,t,i,r),x=y.baseX,_=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;vi(e,i,r,x,_);var S=Kn(_,g,b),M=x,I=S;if(s||d){var C=Yn(n,c),T=C;d&&(T+=d[1]+d[3]);var A=$n(x,T,w);s&&xi(t,e,i,A,S,T,g),d&&(M=Ci(x,w,d),I+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var D=0;D<hy.length;D++){var k=hy[D],P=k[0],L=k[1],O=i[P];l&&O===a[P]||(e[L]=Gm(e,L,O||k[2]))}I+=m/2;var z=i.textStrokeWidth,E=l?a.textStrokeWidth:null,R=!l||z!==E,B=!l||R||i.textStroke!==a.textStroke,N=Si(i.textStroke,z),F=Mi(i.textFill);if(N&&(R&&(e.lineWidth=z),B&&(e.strokeStyle=N)),F&&(l&&i.textFill===a.textFill||(e.fillStyle=F)),1===v.length)N&&e.strokeText(v[0],M,I),F&&e.fillText(v[0],M,I);else for(var D=0;D<v.length;D++)N&&e.strokeText(v[D],M,I),F&&e.fillText(v[D],M,I),I+=m}function pi(t,e,n,i,r,o){o!==Xm&&(e.__attrCachedBy=Zm.NONE);var a=t.__textCotentBlock;(!a||t.__dirtyText)&&(a=t.__textCotentBlock=ai(n,i)),gi(t,e,a,i,r)}function gi(t,e,n,i,r){var o=n.width,a=n.outerWidth,s=n.outerHeight,l=i.textPadding,u=wi(dy,t,i,r),h=u.baseX,c=u.baseY,d=u.textAlign,f=u.textVerticalAlign;vi(e,i,r,h,c);var p=$n(h,a,d),g=Kn(c,s,f),v=p,m=g;l&&(v+=l[3],m+=l[0]);var y=v+o;yi(i)&&xi(t,e,i,p,g,a,s);for(var x=0;x<n.lines.length;x++){for(var _,w=n.lines[x],b=w.tokens,S=b.length,M=w.lineHeight,I=w.width,C=0,T=v,A=y,D=S-1;S>C&&(_=b[C],!_.textAlign||"left"===_.textAlign);)mi(t,e,_,i,M,m,T,"left"),I-=_.width,T+=_.width,C++;for(;D>=0&&(_=b[D],"right"===_.textAlign);)mi(t,e,_,i,M,m,A,"right"),I-=_.width,A-=_.width,D--;for(T+=(o-(T-v)-(y-A)-I)/2;D>=C;)_=b[C],mi(t,e,_,i,M,m,T+_.width/2,"center"),T+=_.width,C++;m+=M}}function vi(t,e,n,i,r){if(n&&e.textRotation){var o=e.textOrigin;"center"===o?(i=n.width/2+n.x,r=n.height/2+n.y):o&&(i=o[0]+n.x,r=o[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function mi(t,e,n,i,r,o,a,s){var l=i.rich[n.styleName]||{};
l.text=n.text;var u=n.textVerticalAlign,h=o+r/2;"top"===u?h=o+n.height/2:"bottom"===u&&(h=o+r-n.height/2),!n.isLineHolder&&yi(l)&&xi(t,e,l,"right"===s?a-n.width:"center"===s?a-n.width/2:a,h-n.height/2,n.width,n.height);var c=n.textPadding;c&&(a=Ci(a,s,c),h-=n.height/2-c[2]-n.textHeight/2),bi(e,"shadowBlur",k(l.textShadowBlur,i.textShadowBlur,0)),bi(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),bi(e,"shadowOffsetX",k(l.textShadowOffsetX,i.textShadowOffsetX,0)),bi(e,"shadowOffsetY",k(l.textShadowOffsetY,i.textShadowOffsetY,0)),bi(e,"textAlign",s),bi(e,"textBaseline","middle"),bi(e,"font",n.font||sy);var d=Si(l.textStroke||i.textStroke,p),f=Mi(l.textFill||i.textFill),p=D(l.textStrokeWidth,i.textStrokeWidth);d&&(bi(e,"lineWidth",p),bi(e,"strokeStyle",d),e.strokeText(n.text,a,h)),f&&(bi(e,"fillStyle",f),e.fillText(n.text,a,h))}function yi(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function xi(t,e,n,i,r,o,a){var s=n.textBackgroundColor,l=n.textBorderWidth,u=n.textBorderColor,h=b(s);if(bi(e,"shadowBlur",n.textBoxShadowBlur||0),bi(e,"shadowColor",n.textBoxShadowColor||"transparent"),bi(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),bi(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),h||l&&u){e.beginPath();var c=n.textBorderRadius;c?ui(e,{x:i,y:r,width:o,height:a,r:c}):e.rect(i,r,o,a),e.closePath()}if(h)if(bi(e,"fillStyle",s),null!=n.fillOpacity){var d=e.globalAlpha;e.globalAlpha=n.fillOpacity*n.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(S(s)){var f=s.image;f=Wn(f,null,t,_i,s),f&&Zn(f)&&e.drawImage(f,i,r,o,a)}if(l&&u)if(bi(e,"lineWidth",l),bi(e,"strokeStyle",u),null!=n.strokeOpacity){var d=e.globalAlpha;e.globalAlpha=n.strokeOpacity*n.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function _i(t,e){e.image=t}function wi(t,e,n,i){var r=n.x||0,o=n.y||0,a=n.textAlign,s=n.textVerticalAlign;if(i){var l=n.textPosition;if(l instanceof Array)r=i.x+Ii(l[0],i.width),o=i.y+Ii(l[1],i.height);else{var u=e&&e.calculateTextPosition?e.calculateTextPosition(cy,n,i):Qn(cy,n,i);r=u.x,o=u.y,a=a||u.textAlign,s=s||u.textVerticalAlign}var h=n.textOffset;h&&(r+=h[0],o+=h[1])}return t=t||{},t.baseX=r,t.baseY=o,t.textAlign=a,t.textVerticalAlign=s,t}function bi(t,e,n){return t[e]=Gm(t,e,n),t[e]}function Si(t,e){return null==t||0>=e||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Mi(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ii(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Ci(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ti(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Ai(t){t=t||{},zm.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new Um(t.style,this),this._rect=null,this.__clipPaths=null}function Di(t){Ai.call(this,t)}function ki(t){return parseInt(t,10)}function Pi(t){return t?t.__builtin__?!0:"function"!=typeof t.resize||"function"!=typeof t.refresh?!1:!0:!1}function Li(t,e,n){return xy.copy(t.getBoundingRect()),t.transform&&xy.applyTransform(t.transform),_y.width=e,_y.height=n,!xy.intersect(_y)}function Oi(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}function zi(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function Ei(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}function Ri(t){return"mousewheel"===t&&Mv.browser.firefox?"DOMMouseScroll":t}function Bi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Ni(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}function Fi(t){t&&(t.zrByTouch=!0)}function Vi(t,e){return be(t.dom,new Wi(t,e),!0)}function Hi(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}function Wi(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}function Gi(t,e){var n=e.domHandlers;Mv.pointerEventsSupported?f(Iy.pointer,function(i){Xi(e,i,function(e){n[i].call(t,e)})}):(Mv.touchEventsSupported&&f(Iy.touch,function(i){Xi(e,i,function(r){n[i].call(t,r),Ni(e)})}),f(Iy.mouse,function(i){Xi(e,i,function(r){r=we(r),e.touching||n[i].call(t,r)})}))}function Zi(t,e){function n(n){function i(i){i=we(i),Hi(t,i.target)||(i=Vi(t,i),e.domHandlers[n].call(t,i))}Xi(e,n,i,{capture:!0})}Mv.pointerEventsSupported?f(Cy.pointer,n):Mv.touchEventsSupported||f(Cy.mouse,n)}function Xi(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,Se(t.domTarget,Ri(e),n,i)}function Yi(t){var e=t.mounted;for(var n in e)e.hasOwnProperty(n)&&Me(t.domTarget,Ri(n),e[n],t.listenerOpts[n]);t.mounted={}}function Ui(t,e){if(t._mayPointerCapture=null,My&&t._pointerCapturing^e){t._pointerCapturing=e;var n=t._globalHandlerScope;e?Zi(t,n):Yi(n)}}function ji(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function qi(t,e){Yv.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new ji(t,Ay),My&&(this._globalHandlerScope=new ji(document,Dy)),this._pointerCapturing=!1,this._mayPointerCapture=null,Gi(this,this._localHandlerScope)}function $i(t,e){var n=new Ey(bv(),t,e);return Oy[n.id]=n,n}function Ki(t){if(t)t.dispose();else{for(var e in Oy)Oy.hasOwnProperty(e)&&Oy[e].dispose();Oy={}}return this}function Qi(t){return Oy[t]}function Ji(t,e){Ly[t]=e}function tr(t){delete Oy[t]}function er(t){return t instanceof Array?t:null==t?[]:[t]}function nr(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;r>i;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}function ir(t){return!Ny(t)||Fy(t)||t instanceof Date?t:t.value}function rr(t){return Ny(t)&&!(t instanceof Array)}function or(t,e){e=(e||[]).slice();var n=p(t||[],function(t){return{exist:t}});return By(e,function(t,i){if(Ny(t)){for(var r=0;r<n.length;r++)if(!n[r].option&&null!=t.id&&n[r].exist.id===t.id+"")return n[r].option=t,void(e[i]=null);for(var r=0;r<n.length;r++){var o=n[r].exist;if(!(n[r].option||null!=o.id&&null!=t.id||null==t.name||lr(t)||lr(o)||o.name!==t.name+""))return n[r].option=t,void(e[i]=null)}}}),By(e,function(t){if(Ny(t)){for(var e=0;e<n.length;e++){var i=n[e].exist;if(!n[e].option&&!lr(i)&&null==t.id){n[e].option=t;break}}e>=n.length&&n.push({option:t})}}),n}function ar(t){var e=N();By(t,function(t){var n=t.exist;n&&e.set(n.id,t)}),By(t,function(t){var n=t.option;O(!n||null==n.id||!e.get(n.id)||e.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&e.set(n.id,t),!t.keyInfo&&(t.keyInfo={})}),By(t,function(t,n){var i=t.exist,r=t.option,o=t.keyInfo;if(Ny(r)){if(o.name=null!=r.name?r.name+"":i?i.name:Vy+n,i)o.id=i.id;else if(null!=r.id)o.id=r.id+"";else{var a=0;do o.id="\x00"+o.name+"\x00"+a++;while(e.get(o.id))}e.set(o.id,t)}})}function sr(t){var e=t.name;return!(!e||!e.indexOf(Vy))}function lr(t){return Ny(t)&&t.id&&0===(t.id+"").indexOf("\x00_ec_\x00")}function ur(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?_(e.dataIndex)?p(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?_(e.name)?p(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function hr(){var t="__\x00ec_inner_"+Wy++ +"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}function cr(t,e,n){if(b(e)){var i={};i[e+"Index"]=0,e=i}var r=n&&n.defaultMainType;!r||dr(e,r+"Index")||dr(e,r+"Id")||dr(e,r+"Name")||(e[r+"Index"]=0);var o={};return By(e,function(i,r){var i=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(o[r]=i);var a=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=a[1],l=(a[2]||"").toLowerCase();if(!(!s||!l||null==i||"index"===l&&"none"===i||n&&n.includeMainTypes&&u(n.includeMainTypes,s)<0)){var h={mainType:s};("index"!==l||"all"!==i)&&(h[l]=i);var c=t.queryComponents(h);o[s+"Models"]=c,o[s+"Model"]=c[0]}}),o}function dr(t,e){return t&&t.hasOwnProperty(e)}function fr(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function pr(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function gr(t){return"auto"===t?Mv.domSupported?"html":"richText":t||"html"}function vr(t){var e={main:"",sub:""};return t&&(t=t.split(Gy),e.main=t[0]||"",e.sub=t[1]||""),e}function mr(t){O(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function yr(t,e){t.$constructor=t,t.extend=function(t){_v&&f(e,function(e){t[e]||console.warn("Method `"+e+"` should be implemented"+(t.type?" in "+t.type:"")+".")});var n=this,i=function(){t.$constructor?t.$constructor.apply(this,arguments):n.apply(this,arguments)};return a(i.prototype,t),i.extend=this.extend,i.superCall=_r,i.superApply=wr,h(i,this),i.superClass=n,i}}function xr(t){var e=["__\x00is_clz",Xy++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,_v&&O(!t.isInstance,'The method "is" can not be defined.'),t.isInstance=function(t){return!(!t||!t[e])}}function _r(t,e){var n=P(arguments,2);return this.superClass.prototype[e].apply(t,n)}function wr(t,e,n){return this.superClass.prototype[e].apply(t,n)}function br(t,e){function n(t){var e=i[t.main];return e&&e[Zy]||(e=i[t.main]={},e[Zy]=!0),e}e=e||{};var i={};if(t.registerClass=function(t,e){if(e)if(mr(e),e=vr(e),e.sub){if(e.sub!==Zy){var r=n(e);r[e.sub]=t}}else _v&&i[e.main]&&console.warn(e.main+" exists."),i[e.main]=t;return t},t.getClass=function(t,e,n){var r=i[t];if(r&&r[Zy]&&(r=e?r[e]:null),n&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=vr(t);var e=[],n=i[t.main];return n&&n[Zy]?f(n,function(t,n){n!==Zy&&e.push(t)}):e.push(n),e},t.hasClass=function(t){return t=vr(t),!!i[t.main]},t.getAllClassMainTypes=function(){var t=[];return f(i,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){t=vr(t);var e=i[t.main];return e&&e[Zy]},t.parseClassType=vr,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var n=r.call(this,e);return t.registerClass(n,e.type)})}return t}function Sr(t){return t>-Jy&&Jy>t}function Mr(t){return t>Jy||-Jy>t}function Ir(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function Cr(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Tr(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,d=l*l-3*s*u,f=0;if(Sr(h)&&Sr(c))if(Sr(s))o[0]=0;else{var p=-l/s;p>=0&&1>=p&&(o[f++]=p)}else{var g=c*c-4*h*d;if(Sr(g)){var v=c/h,p=-s/a+v,m=-v/2;p>=0&&1>=p&&(o[f++]=p),m>=0&&1>=m&&(o[f++]=m)}else if(g>0){var y=Qy(g),x=h*s+1.5*a*(-c+y),_=h*s+1.5*a*(-c-y);x=0>x?-Ky(-x,nx):Ky(x,nx),_=0>_?-Ky(-_,nx):Ky(_,nx);var p=(-s-(x+_))/(3*a);p>=0&&1>=p&&(o[f++]=p)}else{var w=(2*h*s-3*a*c)/(2*Qy(h*h*h)),b=Math.acos(w)/3,S=Qy(h),M=Math.cos(b),p=(-s-2*S*M)/(3*a),m=(-s+S*(M+ex*Math.sin(b)))/(3*a),I=(-s+S*(M-ex*Math.sin(b)))/(3*a);p>=0&&1>=p&&(o[f++]=p),m>=0&&1>=m&&(o[f++]=m),I>=0&&1>=I&&(o[f++]=I)}}return f}function Ar(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(Sr(a)){if(Mr(o)){var u=-s/o;u>=0&&1>=u&&(r[l++]=u)}}else{var h=o*o-4*a*s;if(Sr(h))r[0]=-o/(2*a);else if(h>0){var c=Qy(h),u=(-o+c)/(2*a),d=(-o-c)/(2*a);u>=0&&1>=u&&(r[l++]=u),d>=0&&1>=d&&(r[l++]=d)}}return l}function Dr(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function kr(t,e,n,i,r,o,a,s,l,u,h){var c,d,f,p,g,v=.005,m=1/0;ix[0]=l,ix[1]=u;for(var y=0;1>y;y+=.05)rx[0]=Ir(t,n,r,a,y),rx[1]=Ir(e,i,o,s,y),p=Gv(ix,rx),m>p&&(c=y,m=p);m=1/0;for(var x=0;32>x&&!(tx>v);x++)d=c-v,f=c+v,rx[0]=Ir(t,n,r,a,d),rx[1]=Ir(e,i,o,s,d),p=Gv(rx,ix),d>=0&&m>p?(c=d,m=p):(ox[0]=Ir(t,n,r,a,f),ox[1]=Ir(e,i,o,s,f),g=Gv(ox,ix),1>=f&&m>g?(c=f,m=g):v*=.5);return h&&(h[0]=Ir(t,n,r,a,c),h[1]=Ir(e,i,o,s,c)),Qy(m)}function Pr(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Lr(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Or(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(Sr(o)){if(Mr(a)){var u=-s/a;u>=0&&1>=u&&(r[l++]=u)}}else{var h=a*a-4*o*s;if(Sr(h)){var u=-a/(2*o);u>=0&&1>=u&&(r[l++]=u)}else if(h>0){var c=Qy(h),u=(-a+c)/(2*o),d=(-a-c)/(2*o);u>=0&&1>=u&&(r[l++]=u),d>=0&&1>=d&&(r[l++]=d)}}return l}function zr(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function Er(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function Rr(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;ix[0]=a,ix[1]=s;for(var d=0;1>d;d+=.05){rx[0]=Pr(t,n,r,d),rx[1]=Pr(e,i,o,d);var f=Gv(ix,rx);c>f&&(u=d,c=f)}c=1/0;for(var p=0;32>p&&!(tx>h);p++){var g=u-h,v=u+h;rx[0]=Pr(t,n,r,g),rx[1]=Pr(e,i,o,g);var f=Gv(rx,ix);if(g>=0&&c>f)u=g,c=f;else{ox[0]=Pr(t,n,r,v),ox[1]=Pr(e,i,o,v);var m=Gv(ox,ix);1>=v&&c>m?(u=v,c=m):h*=.5}}return l&&(l[0]=Pr(t,n,r,u),l[1]=Pr(e,i,o,u)),Qy(c)}function Br(t,e,n){if(0!==t.length){var i,r=t[0],o=r[0],a=r[0],s=r[1],l=r[1];for(i=1;i<t.length;i++)r=t[i],o=ax(o,r[0]),a=sx(a,r[0]),s=ax(s,r[1]),l=sx(l,r[1]);e[0]=o,e[1]=s,n[0]=a,n[1]=l}}function Nr(t,e,n,i,r,o){r[0]=ax(t,n),r[1]=ax(e,i),o[0]=sx(t,n),o[1]=sx(e,i)}function Fr(t,e,n,i,r,o,a,s,l,u){var h,c=Ar,d=Ir,f=c(t,n,r,a,px);for(l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0,h=0;f>h;h++){var p=d(t,n,r,a,px[h]);l[0]=ax(p,l[0]),u[0]=sx(p,u[0])}for(f=c(e,i,o,s,gx),h=0;f>h;h++){var g=d(e,i,o,s,gx[h]);l[1]=ax(g,l[1]),u[1]=sx(g,u[1])}l[0]=ax(t,l[0]),u[0]=sx(t,u[0]),l[0]=ax(a,l[0]),u[0]=sx(a,u[0]),l[1]=ax(e,l[1]),u[1]=sx(e,u[1]),l[1]=ax(s,l[1]),u[1]=sx(s,u[1])}function Vr(t,e,n,i,r,o,a,s){var l=zr,u=Pr,h=sx(ax(l(t,n,r),1),0),c=sx(ax(l(e,i,o),1),0),d=u(t,n,r,h),f=u(e,i,o,c);a[0]=ax(t,r,d),a[1]=ax(e,o,f),s[0]=sx(t,r,d),s[1]=sx(e,o,f)}function Hr(t,e,n,i,r,o,a,s,l){var u=ae,h=se,c=Math.abs(r-o);if(1e-4>c%hx&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(cx[0]=ux(r)*n+t,cx[1]=lx(r)*i+e,dx[0]=ux(o)*n+t,dx[1]=lx(o)*i+e,u(s,cx,dx),h(l,cx,dx),r%=hx,0>r&&(r+=hx),o%=hx,0>o&&(o+=hx),r>o&&!a?o+=hx:o>r&&a&&(r+=hx),a){var d=o;o=r,r=d}for(var f=0;o>f;f+=Math.PI/2)f>r&&(fx[0]=ux(f)*n+t,fx[1]=lx(f)*i+e,u(s,fx,s),h(l,fx,l))}function Wr(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0,u=t;if(a>e+s&&a>i+s||e-s>a&&i-s>a||o>t+s&&o>n+s||t-s>o&&n-s>o)return!1;if(t===n)return Math.abs(o-t)<=s/2;l=(e-i)/(t-n),u=(t*i-n*e)/(t-n);var h=l*o-a+u,c=h*h/(l*l+1);return s/2*s/2>=c}function Gr(t,e,n,i,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;if(h>e+c&&h>i+c&&h>o+c&&h>s+c||e-c>h&&i-c>h&&o-c>h&&s-c>h||u>t+c&&u>n+c&&u>r+c&&u>a+c||t-c>u&&n-c>u&&r-c>u&&a-c>u)return!1;var d=kr(t,e,n,i,r,o,a,s,u,h,null);return c/2>=d}function Zr(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;if(l>e+u&&l>i+u&&l>o+u||e-u>l&&i-u>l&&o-u>l||s>t+u&&s>n+u&&s>r+u||t-u>s&&n-u>s&&r-u>s)return!1;var h=Rr(t,e,n,i,r,o,s,l,null);return u/2>=h}function Xr(t){return t%=kx,0>t&&(t+=kx),t}function Yr(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(h-u>n||n>h+u)return!1;if(Math.abs(i-r)%Px<1e-4)return!0;if(o){var c=i;i=Xr(r),r=Xr(c)}else i=Xr(i),r=Xr(r);i>r&&(r+=Px);var d=Math.atan2(l,s);return 0>d&&(d+=Px),d>=i&&r>=d||d+Px>=i&&r>=d+Px}function Ur(t,e,n,i,r,o){if(o>e&&o>i||e>o&&i>o)return 0;if(i===e)return 0;var a=e>i?1:-1,s=(o-e)/(i-e);(1===s||0===s)&&(a=e>i?.5:-.5);var l=s*(n-t)+t;return l===r?1/0:l>r?a:0}function jr(t,e){return Math.abs(t-e)<zx}function qr(){var t=Rx[0];Rx[0]=Rx[1],Rx[1]=t}function $r(t,e,n,i,r,o,a,s,l,u){if(u>e&&u>i&&u>o&&u>s||e>u&&i>u&&o>u&&s>u)return 0;var h=Tr(e,i,o,s,u,Ex);if(0===h)return 0;for(var c,d,f=0,p=-1,g=0;h>g;g++){var v=Ex[g],m=0===v||1===v?.5:1,y=Ir(t,n,r,a,v);l>y||(0>p&&(p=Ar(e,i,o,s,Rx),Rx[1]<Rx[0]&&p>1&&qr(),c=Ir(e,i,o,s,Rx[0]),p>1&&(d=Ir(e,i,o,s,Rx[1]))),f+=2===p?v<Rx[0]?e>c?m:-m:v<Rx[1]?c>d?m:-m:d>s?m:-m:v<Rx[0]?e>c?m:-m:c>s?m:-m)}return f}function Kr(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||e>s&&i>s&&o>s)return 0;var l=Or(e,i,o,s,Ex);if(0===l)return 0;var u=zr(e,i,o);if(u>=0&&1>=u){for(var h=0,c=Pr(e,i,o,u),d=0;l>d;d++){var f=0===Ex[d]||1===Ex[d]?.5:1,p=Pr(t,n,r,Ex[d]);a>p||(h+=Ex[d]<u?e>c?f:-f:c>o?f:-f)}return h}var f=0===Ex[0]||1===Ex[0]?.5:1,p=Pr(t,n,r,Ex[0]);return a>p?0:e>o?f:-f}function Qr(t,e,n,i,r,o,a,s){if(s-=e,s>n||-n>s)return 0;var l=Math.sqrt(n*n-s*s);Ex[0]=-l,Ex[1]=l;var u=Math.abs(i-r);if(1e-4>u)return 0;if(1e-4>u%Ox){i=0,r=Ox;var h=o?1:-1;return a>=Ex[0]+t&&a<=Ex[1]+t?h:0}if(o){var l=i;i=Xr(r),r=Xr(l)}else i=Xr(i),r=Xr(r);i>r&&(r+=Ox);for(var c=0,d=0;2>d;d++){var f=Ex[d];if(f+t>a){var p=Math.atan2(s,f),h=o?1:-1;0>p&&(p=Ox+p),(p>=i&&r>=p||p+Ox>=i&&r>=p+Ox)&&(p>Math.PI/2&&p<1.5*Math.PI&&(h=-h),c+=h)}}return c}function Jr(t,e,n,i,r){for(var o=0,a=0,s=0,l=0,u=0,h=0;h<t.length;){var c=t[h++];switch(c===Lx.M&&h>1&&(n||(o+=Ur(a,s,l,u,i,r))),1===h&&(a=t[h],s=t[h+1],l=a,u=s),c){case Lx.M:l=t[h++],u=t[h++],a=l,s=u;break;case Lx.L:if(n){if(Wr(a,s,t[h],t[h+1],e,i,r))return!0}else o+=Ur(a,s,t[h],t[h+1],i,r)||0;a=t[h++],s=t[h++];break;case Lx.C:if(n){if(Gr(a,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],e,i,r))return!0}else o+=$r(a,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],i,r)||0;a=t[h++],s=t[h++];break;case Lx.Q:if(n){if(Zr(a,s,t[h++],t[h++],t[h],t[h+1],e,i,r))return!0}else o+=Kr(a,s,t[h++],t[h++],t[h],t[h+1],i,r)||0;a=t[h++],s=t[h++];break;case Lx.A:var d=t[h++],f=t[h++],p=t[h++],g=t[h++],v=t[h++],m=t[h++];h+=1;var y=1-t[h++],x=Math.cos(v)*p+d,_=Math.sin(v)*g+f;h>1?o+=Ur(a,s,x,_,i,r):(l=x,u=_);var w=(i-d)*g/p+d;if(n){if(Yr(d,f,g,v,v+m,y,e,w,r))return!0}else o+=Qr(d,f,g,v,v+m,y,w,r);a=Math.cos(v+m)*p+d,s=Math.sin(v+m)*g+f;break;case Lx.R:l=a=t[h++],u=s=t[h++];var b=t[h++],S=t[h++],x=l+b,_=u+S;if(n){if(Wr(l,u,x,u,e,i,r)||Wr(x,u,x,_,e,i,r)||Wr(x,_,l,_,e,i,r)||Wr(l,_,l,u,e,i,r))return!0}else o+=Ur(x,u,x,_,i,r),o+=Ur(l,_,l,u,i,r);break;case Lx.Z:if(n){if(Wr(a,s,l,u,e,i,r))return!0}else o+=Ur(a,s,l,u,i,r);a=l,s=u}}return n||jr(s,u)||(o+=Ur(a,s,l,u,i,r)||0),0!==o}function to(t,e,n){return Jr(t,0,!1,e,n)}function eo(t,e,n,i){return Jr(t,e,!0,n,i)}function no(t){Ai.call(this,t),this.path=null}function io(t,e,n,i,r,o,a,s,l,u,h){var c=l*(jx/180),d=Ux(c)*(t-n)/2+Yx(c)*(e-i)/2,f=-1*Yx(c)*(t-n)/2+Ux(c)*(e-i)/2,p=d*d/(a*a)+f*f/(s*s);p>1&&(a*=Xx(p),s*=Xx(p));var g=(r===o?-1:1)*Xx((a*a*s*s-a*a*f*f-s*s*d*d)/(a*a*f*f+s*s*d*d))||0,v=g*a*f/s,m=g*-s*d/a,y=(t+n)/2+Ux(c)*v-Yx(c)*m,x=(e+i)/2+Yx(c)*v+Ux(c)*m,_=Kx([1,0],[(d-v)/a,(f-m)/s]),w=[(d-v)/a,(f-m)/s],b=[(-1*d-v)/a,(-1*f-m)/s],S=Kx(w,b);$x(w,b)<=-1&&(S=jx),$x(w,b)>=1&&(S=0),0===o&&S>0&&(S-=2*jx),1===o&&0>S&&(S+=2*jx),h.addData(u,y,x,a,s,_,S,c,o)}function ro(t){if(!t)return new Dx;for(var e,n=0,i=0,r=n,o=i,a=new Dx,s=Dx.CMD,l=t.match(Qx),u=0;u<l.length;u++){for(var h,c=l[u],d=c.charAt(0),f=c.match(Jx)||[],p=f.length,g=0;p>g;g++)f[g]=parseFloat(f[g]);for(var v=0;p>v;){var m,y,x,_,w,b,S,M=n,I=i;switch(d){case"l":n+=f[v++],i+=f[v++],h=s.L,a.addData(h,n,i);break;case"L":n=f[v++],i=f[v++],h=s.L,a.addData(h,n,i);break;case"m":n+=f[v++],i+=f[v++],h=s.M,a.addData(h,n,i),r=n,o=i,d="l";break;case"M":n=f[v++],i=f[v++],h=s.M,a.addData(h,n,i),r=n,o=i,d="L";break;case"h":n+=f[v++],h=s.L,a.addData(h,n,i);break;case"H":n=f[v++],h=s.L,a.addData(h,n,i);break;case"v":i+=f[v++],h=s.L,a.addData(h,n,i);break;case"V":i=f[v++],h=s.L,a.addData(h,n,i);break;case"C":h=s.C,a.addData(h,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),n=f[v-2],i=f[v-1];break;case"c":h=s.C,a.addData(h,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i),n+=f[v-2],i+=f[v-1];break;case"S":m=n,y=i;var C=a.len(),T=a.data;e===s.C&&(m+=n-T[C-4],y+=i-T[C-3]),h=s.C,M=f[v++],I=f[v++],n=f[v++],i=f[v++],a.addData(h,m,y,M,I,n,i);break;case"s":m=n,y=i;var C=a.len(),T=a.data;e===s.C&&(m+=n-T[C-4],y+=i-T[C-3]),h=s.C,M=n+f[v++],I=i+f[v++],n+=f[v++],i+=f[v++],a.addData(h,m,y,M,I,n,i);break;case"Q":M=f[v++],I=f[v++],n=f[v++],i=f[v++],h=s.Q,a.addData(h,M,I,n,i);break;case"q":M=f[v++]+n,I=f[v++]+i,n+=f[v++],i+=f[v++],h=s.Q,a.addData(h,M,I,n,i);break;case"T":m=n,y=i;var C=a.len(),T=a.data;e===s.Q&&(m+=n-T[C-4],y+=i-T[C-3]),n=f[v++],i=f[v++],h=s.Q,a.addData(h,m,y,n,i);break;case"t":m=n,y=i;var C=a.len(),T=a.data;e===s.Q&&(m+=n-T[C-4],y+=i-T[C-3]),n+=f[v++],i+=f[v++],h=s.Q,a.addData(h,m,y,n,i);break;case"A":x=f[v++],_=f[v++],w=f[v++],b=f[v++],S=f[v++],M=n,I=i,n=f[v++],i=f[v++],h=s.A,io(M,I,n,i,b,S,x,_,w,h,a);break;case"a":x=f[v++],_=f[v++],w=f[v++],b=f[v++],S=f[v++],M=n,I=i,n+=f[v++],i+=f[v++],h=s.A,io(M,I,n,i,b,S,x,_,w,h,a)}}("z"===d||"Z"===d)&&(h=s.Z,a.addData(h),n=r,i=o),e=h}return a.toStatic(),a}function oo(t,e){var n=ro(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;n.rebuildPath(e)}},e.applyTransform=function(t){Zx(n,t),this.dirty(!0)},e}function ao(t,e){return new no(oo(t,e))}function so(t,e){return no.extend(oo(t,e))}function lo(t,e){for(var n=[],i=t.length,r=0;i>r;r++){var o=t[r];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),n.push(o.path)}var a=new no(e);return a.createPathProxy(),a.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},a}function uo(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function ho(t,e,n){var i=e.points,r=e.smooth;if(i&&i.length>=2){if(r&&"spline"!==r){var o=s_(i,r,n,e.smoothConstraint);t.moveTo(i[0][0],i[0][1]);for(var a=i.length,s=0;(n?a:a-1)>s;s++){var l=o[2*s],u=o[2*s+1],h=i[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{"spline"===r&&(i=a_(i,n)),t.moveTo(i[0][0],i[0][1]);for(var s=1,c=i.length;c>s;s++)t.lineTo(i[s][0],i[s][1])}n&&t.closePath()}}function co(t,e,n){if(e){var i=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=r,t.y1=o,t.y2=a;var s=n&&n.lineWidth;s&&(h_(2*i)===h_(2*r)&&(t.x1=t.x2=po(i,s,!0)),h_(2*o)===h_(2*a)&&(t.y1=t.y2=po(o,s,!0)))}}function fo(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,a=e.height;t.x=i,t.y=r,t.width=o,t.height=a;var s=n&&n.lineWidth;s&&(t.x=po(i,s,!0),t.y=po(r,s,!0),t.width=Math.max(po(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(po(r+a,s,!1)-t.y,0===a?0:1))}}function po(t,e,n){if(!e)return t;var i=h_(2*t);return(i+h_(e))%2===0?i/2:(i+(n?1:-1))/2}function go(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?Cr:Ir)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Cr:Ir)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Lr:Pr)(t.x1,t.cpx1,t.x2,e),(n?Lr:Pr)(t.y1,t.cpy1,t.y2,e)]}function vo(t){Ai.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}function mo(t){return no.extend(t)}function yo(t,e){return so(t,e)}function xo(t,e){L_[t]=e}function _o(t){return L_.hasOwnProperty(t)?L_[t]:void 0}function wo(t,e,n,i){var r=ao(t,e);return n&&("center"===i&&(n=So(n,r.getBoundingRect())),Mo(r,n)),r}function bo(t,e,n){var i=new Di({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(So(e,r))}}});return i}function So(t,e){var n,i=e.width/e.height,r=t.height*i;r<=t.width?n=t.height:(r=t.width,n=r/i);var o=t.x+t.width/2,a=t.y+t.height/2;return{x:o-r/2,y:a-n/2,width:r,height:n}}function Mo(t,e){if(t.applyTransform){var n=t.getBoundingRect(),i=n.calculateTransform(e);t.applyTransform(i)}}function Io(t){return co(t.shape,t.shape,t.style),t}function Co(t){return fo(t.shape,t.shape,t.style),t}function To(t){return null!=t&&"none"!==t}function Ao(t){if("string"!=typeof t)return t;var e=E_.get(t);return e||(e=rn(t,-.1),1e4>R_&&(E_.set(t,e),R_++)),e}function Do(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(!e)return void(t.__cachedNormalStl=t.__cachedNormalZ2=null);var n=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var i=t.style;for(var r in e)null!=e[r]&&(n[r]=i[r]);n.fill=i.fill,n.stroke=i.stroke}}function ko(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var n=t.__zr,i=t.useHoverLayer&&n&&"canvas"===n.painter.type;if(t.__highlighted=i?"layer":"plain",!(t.isGroup||!n&&t.useHoverLayer)){var r=t,o=t.style;i&&(r=n.addHover(t),o=r.style),Jo(o),i||Do(r),o.extendFrom(e),Po(o,e,"fill"),Po(o,e,"stroke"),Qo(o),i||(t.dirty(!1),t.z2+=C_)}}}function Po(t,e,n){!To(e[n])&&To(t[n])&&(t[n]=Ao(t[n]))}function Lo(t){var e=t.__highlighted;if(e&&(t.__highlighted=!1,!t.isGroup))if("layer"===e)t.__zr&&t.__zr.removeHover(t);else{var n=t.style,i=t.__cachedNormalStl;i&&(Jo(n),t.setStyle(i),Qo(n));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===C_&&(t.z2=r)}}function Oo(t,e,n){var i,r=D_,o=D_;t.__highlighted&&(r=A_,i=!0),e(t,n),t.__highlighted&&(o=A_,i=!0),t.isGroup&&t.traverse(function(t){!t.isGroup&&e(t,n)}),i&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,o)}function zo(t,e){e=t.__hoverStl=e!==!1&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,Lo(t),ko(t))}function Eo(t){!Fo(this,t)&&!this.__highByOuter&&Oo(this,ko)}function Ro(t){!Fo(this,t)&&!this.__highByOuter&&Oo(this,Lo)}function Bo(t){this.__highByOuter|=1<<(t||0),Oo(this,ko)}function No(t){!(this.__highByOuter&=~(1<<(t||0)))&&Oo(this,Lo)}function Fo(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Vo(t,e){Ho(t,!0),Oo(t,zo,e)}function Ho(t,e){var n=e===!1;if(t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,!n||t.__highDownDispatcher){var i=n?"off":"on";t[i]("mouseover",Eo)[i]("mouseout",Ro),t[i]("emphasis",Bo)[i]("normal",No),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!n}}function Wo(t){return!(!t||!t.__highDownDispatcher)}function Go(t){var e=P_[t];return null==e&&32>=k_&&(e=P_[t]=k_++),e}function Zo(t,e,n,i,r,o,a){r=r||I_;var s,l=r.labelFetcher,u=r.labelDataIndex,h=r.labelDimIndex,c=r.labelProp,d=n.getShallow("show"),f=i.getShallow("show");(d||f)&&(l&&(s=l.getFormattedLabel(u,"normal",null,h,c)),null==s&&(s=w(r.defaultText)?r.defaultText(u,r):r.defaultText));var p=d?s:null,g=f?D(l?l.getFormattedLabel(u,"emphasis",null,h,c):null,s):null;(null!=p||null!=g)&&(Yo(t,n,o,r),Yo(e,i,a,r,!0)),t.text=p,e.text=g}function Xo(t,e,n){var i=t.style;e&&(Jo(i),t.setStyle(e),Qo(i)),i=t.__hoverStl,n&&i&&(Jo(i),a(i,n),Qo(i))}function Yo(t,e,n,i,r){return jo(t,e,i,r),n&&a(t,n),t}function Uo(t,e,n){var i,r={isRectText:!0};n===!1?i=!0:r.autoColor=n,jo(t,e,r,i)}function jo(t,e,n,i){if(n=n||I_,n.isRectText){var r;n.getTextPosition?r=n.getTextPosition(e,i):(r=e.getShallow("position")||(i?null:"inside"),"outside"===r&&(r="top")),t.textPosition=r,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=D(e.getShallow("distance"),i?null:5)}var a,s=e.ecModel,l=s&&s.option.textStyle,u=qo(e);if(u){a={};for(var h in u)if(u.hasOwnProperty(h)){var c=e.getModel(["rich",h]);$o(a[h]={},c,l,n,i)}}return t.rich=a,$o(t,e,l,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function qo(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||I_).rich;if(n){e=e||{};for(var i in n)n.hasOwnProperty(i)&&(e[i]=1)}t=t.parentModel}return e}function $o(t,e,n,i,r,o){n=!r&&n||I_,t.textFill=Ko(e.getShallow("color"),i)||n.color,t.textStroke=Ko(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textStrokeWidth=D(e.getShallow("textBorderWidth"),n.textBorderWidth),r||(o&&(t.insideRollbackOpt=i,Qo(t)),null==t.textFill&&(t.textFill=i.autoColor)),t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&i.disableBox||(t.textBackgroundColor=Ko(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=Ko(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function Ko(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Qo(t){var e,n=t.textPosition,i=t.insideRollbackOpt;if(i&&null==t.textFill){var r=i.autoColor,o=i.isRectText,a=i.useInsideStyle,s=a!==!1&&(a===!0||o&&n&&"string"==typeof n&&n.indexOf("inside")>=0),l=!s&&null!=r;(s||l)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),s&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=r,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),l&&(t.textFill=r)}t.insideRollback=e}function Jo(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function ta(t,e){var n=e&&e.getModel("textStyle");return z([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}function ea(t,e,n,i,r,o){"function"==typeof r&&(o=r,r=null);var a=i&&i.isAnimationEnabled();if(a){var s=t?"Update":"",l=i.getShallow("animationDuration"+s),u=i.getShallow("animationEasing"+s),h=i.getShallow("animationDelay"+s);"function"==typeof h&&(h=h(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(n,l,h||0,u,o,!!o):(e.stopAnimation(),e.attr(n),o&&o())}else e.stopAnimation(),e.attr(n),o&&o()}function na(t,e,n,i,r){ea(!0,t,e,n,i,r)}function ia(t,e,n,i,r){ea(!1,t,e,n,i,r)}function ra(t,e){for(var n=ze([]);t&&t!==e;)Re(n,t.getLocalTransform(),n),t=t.parent;return n}function oa(t,e,n){return e&&!d(e)&&(e=um.getLocalTransform(e)),n&&(e=Ve([],e)),oe([],t,e)}function aa(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return o=oa(o,e,n),Math.abs(o[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?"bottom":"top"}function sa(t,e,n){function i(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:G(t.position),rotation:t.rotation};return t.shape&&(e.shape=a({},t.shape)),e}if(t&&e){var o=i(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=o[t.anid];if(e){var i=r(t);t.attr(r(e)),na(t,i,n,t.dataIndex)}}})}}function la(t,e){return p(t,function(t){var n=t[0];n=S_(n,e.x),n=M_(n,e.x+e.width);var i=t[1];return i=S_(i,e.y),i=M_(i,e.y+e.height),[n,i]})}function ua(t,e){var n=S_(t.x,e.x),i=M_(t.x+t.width,e.x+e.width),r=S_(t.y,e.y),o=M_(t.y+t.height,e.y+e.height);return i>=n&&o>=r?{x:n,y:r,width:i-n,height:o-r}:void 0}function ha(t,e,n){e=a({rectHover:!0},e);var i=e.style={strokeNoScale:!0};return n=n||{x:-1,y:-1,width:2,height:2},t?0===t.indexOf("image://")?(i.image=t.slice(8),s(i,n),new Di(e)):wo(t.replace("path://",""),e,n,"center"):void 0}function ca(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(da(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}}function da(t,e,n,i,r,o,a,s){var l=n-t,u=i-e,h=a-r,c=s-o,d=fa(h,c,l,u);if(pa(d))return!1;var f=t-r,p=e-o,g=fa(f,p,l,u)/d;if(0>g||g>1)return!1;var v=fa(f,p,h,c)/d;return 0>v||v>1?!1:!0}function fa(t,e,n,i){return t*i-n*e}function pa(t){return 1e-6>=t&&t>=-1e-6}function ga(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function va(t,e,n){for(var i=0;i<e.length&&(!e[i]||(t=t&&"object"==typeof t?t[e[i]]:null,null!=t));i++);return null==t&&n&&(t=n.get(e)),t
}function ma(t,e){var n=G_(t).getParent;return n?n.call(t,e):t.parentModel}function ya(t){return[t||"",Z_++,Math.random().toFixed(5)].join("_")}function xa(t){var e={};return t.registerSubTypeDefaulter=function(t,n){t=vr(t),e[t.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=vr(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r},t}function _a(t,e){function n(t){var n={},o=[];return f(t,function(a){var s=i(n,a),l=s.originalDeps=e(a),h=r(l,t);s.entryCount=h.length,0===s.entryCount&&o.push(a),f(h,function(t){u(s.predecessor,t)<0&&s.predecessor.push(t);var e=i(n,t);u(e.successor,t)<0&&e.successor.push(a)})}),{graph:n,noEntryList:o}}function i(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var n=[];return f(t,function(t){u(e,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,e,i,r){function o(t){l[t].entryCount--,0===l[t].entryCount&&u.push(t)}function a(t){h[t]=!0,o(t)}if(t.length){var s=n(e),l=s.graph,u=s.noEntryList,h={};for(f(t,function(t){h[t]=!0});u.length;){var c=u.pop(),d=l[c],p=!!h[c];p&&(i.call(r,c,d.originalDeps.slice()),delete h[c]),f(d.successor,p?a:o)}f(h,function(){throw new Error("Circle dependency may exists")})}}}function wa(t){return t.replace(/^\s+|\s+$/g,"")}function ba(t,e,n,i){var r=e[1]-e[0],o=n[1]-n[0];if(0===r)return 0===o?n[0]:(n[0]+n[1])/2;if(i)if(r>0){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*o+n[0]}function Sa(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?wa(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?0/0:+t}function Ma(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function Ia(t){return t.sort(function(t,e){return t-e}),t}function Ca(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n}function Ta(t){var e=t.toString(),n=e.indexOf("e");if(n>0){var i=+e.slice(n+1);return 0>i?-i:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}function Aa(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Da(t,e,n){if(!t[e])return 0;var i=g(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),o=p(t,function(t){return(isNaN(t)?0:t)/i*r*100}),a=100*r,s=p(o,function(t){return Math.floor(t)}),l=g(s,function(t,e){return t+e},0),u=p(o,function(t,e){return t-s[e]});a>l;){for(var h=Number.NEGATIVE_INFINITY,c=null,d=0,f=u.length;f>d;++d)u[d]>h&&(h=u[d],c=d);++s[c],u[c]=0,++l}return s[e]/r}function ka(t){var e=2*Math.PI;return(t%e+e)%e}function Pa(t){return t>-X_&&X_>t}function La(t){if(t instanceof Date)return t;if("string"==typeof t){var e=U_.exec(t);if(!e)return new Date(0/0);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return new Date(null==t?0/0:Math.round(t))}function Oa(t){return Math.pow(10,za(t))}function za(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Ea(t,e){var n,i=za(t),r=Math.pow(10,i),o=t/r;return n=e?1.5>o?1:2.5>o?2:4>o?3:7>o?5:10:1>o?1:2>o?2:3>o?3:5>o?5:10,t=n*r,i>=-20?+t.toFixed(0>i?-i:0):t}function Ra(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r}function Ba(t){function e(t,n,i){return t.interval[i]<n.interval[i]||t.interval[i]===n.interval[i]&&(t.close[i]-n.close[i]===(i?-1:1)||!i&&e(t,n,1))}t.sort(function(t,n){return e(t,n,0)?-1:1});for(var n=-1/0,i=1,r=0;r<t.length;){for(var o=t[r].interval,a=t[r].close,s=0;2>s;s++)o[s]<=n&&(o[s]=n,a[s]=s?1:1-i),n=o[s],i=a[s];o[0]===o[1]&&a[0]*a[1]!==1?t.splice(r,1):r++}return t}function Na(t){return t-parseFloat(t)>=0}function Fa(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function Va(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}function Ha(t){return null==t?"":(t+"").replace($_,function(t,e){return K_[e]})}function Wa(t,e,n){_(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=Q_[o];t=t.replace(J_(a),J_(a,0))}for(var s=0;i>s;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(J_(Q_[l],s),n?Ha(u):u)}return t}function Ga(t,e,n){return f(e,function(e,i){t=t.replace("{"+i+"}",n?Ha(e):e)}),t}function Za(t,e){t=b(t)?{color:t,extraCssText:e}:t||{};var n=t.color,i=t.type,e=t.extraCssText,r=t.renderMode||"html",o=t.markerId||"X";return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Ha(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+Ha(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+o+"|}  ",style:{color:n}}:""}function Xa(t,e){return t+="","0000".substr(0,e-t.length)+t}function Ya(t,e,n){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var i=La(e),r=n?"UTC":"",o=i["get"+r+"FullYear"](),a=i["get"+r+"Month"]()+1,s=i["get"+r+"Date"](),l=i["get"+r+"Hours"](),u=i["get"+r+"Minutes"](),h=i["get"+r+"Seconds"](),c=i["get"+r+"Milliseconds"]();return t=t.replace("MM",Xa(a,2)).replace("M",a).replace("yyyy",o).replace("yy",o%100).replace("dd",Xa(s,2)).replace("d",s).replace("hh",Xa(l,2)).replace("h",l).replace("mm",Xa(u,2)).replace("m",u).replace("ss",Xa(h,2)).replace("s",h).replace("SSS",Xa(c,3))}function Ua(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function ja(t){return Un(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)}function qa(t,e,n,i,r,o,a,s){return Un(t,e,n,i,r,s,o,a)}function $a(t,e){if("_blank"===e||"blank"===e){var n=window.open();n.opener=null,n.location=t}else window.open(t,e)}function Ka(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,u){var h,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(u+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);h=o+v,h>i||l.newline?(o=0,h=v,a+=s+n,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);c=a+m,c>r||l.newline?(o+=s+n,a=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=o,d[1]=a,"horizontal"===t?o=h+n:a=c+n)})}function Qa(t,e,n){n=q_(n||0);var i=e.width,r=e.height,o=Sa(t.left,i),a=Sa(t.top,r),s=Sa(t.right,i),l=Sa(t.bottom,r),u=Sa(t.width,i),h=Sa(t.height,r),c=n[2]+n[0],d=n[1]+n[3],f=t.aspect;switch(isNaN(u)&&(u=i-s-d-o),isNaN(h)&&(h=r-l-c-a),null!=f&&(isNaN(u)&&isNaN(h)&&(f>i/r?u=.8*i:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(o)&&(o=i-s-u-d),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=i/2-u/2-n[3];break;case"right":o=i-u-d}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-n[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=i-d-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var p=new Tn(o+n[3],a+n[0],u,h);return p.margin=n,p}function Ja(t,e,n,i,r){var o=!r||!r.hv||r.hv[0],a=!r||!r.hv||r.hv[1],l=r&&r.boundingMode||"all";if(o||a){var u;if("raw"===l)u="group"===t.type?new Tn(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(u=t.getBoundingRect(),t.needLocalTransform()){var h=t.getLocalTransform();u=u.clone(),u.applyTransform(h)}e=Qa(s({width:u.width,height:u.height},e),n,i);var c=t.position,d=o?e.x-u.x:0,f=a?e.y-u.y:0;t.attr("position","raw"===l?[d,f]:[c[0]+d,c[1]+f])}}function ts(t,e,n){function i(n,i){var a={},l=0,u={},h=0,c=2;if(nw(n,function(e){u[e]=t[e]}),nw(n,function(t){r(e,t)&&(a[t]=u[t]=e[t]),o(a,t)&&l++,o(u,t)&&h++}),s[i])return o(e,n[1])?u[n[2]]=null:o(e,n[2])&&(u[n[1]]=null),u;if(h!==c&&l){if(l>=c)return a;for(var d=0;d<n.length;d++){var f=n[d];if(!r(a,f)&&r(t,f)){a[f]=t[f];break}}return a}return u}function r(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function a(t,e,n){nw(t,function(t){e[t]=n[t]})}!S(n)&&(n={});var s=n.ignoreSize;!_(s)&&(s=[s,s]);var l=i(rw[0],0),u=i(rw[1],1);a(rw[0],t,l),a(rw[1],t,u)}function es(t){return ns({},t)}function ns(t,e){return e&&t&&nw(iw,function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t}function is(t){var e=[];return f(lw.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=p(e,function(t){return vr(t).main}),"dataset"!==t&&u(e,"dataset")<=0&&e.unshift("dataset"),e}function rs(t,e){for(var n=t.length,i=0;n>i;i++)if(t[i].length>e)return t[i];return t[n-1]}function os(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===vw?{}:[]),this.sourceFormat=t.sourceFormat||mw,this.seriesLayoutBy=t.seriesLayoutBy||xw,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&N(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}function as(t){var e=t.option.source,n=mw;if(I(e))n=yw;else if(_(e)){0===e.length&&(n=pw);for(var i=0,r=e.length;r>i;i++){var o=e[i];if(null!=o){if(_(o)){n=pw;break}if(S(o)){n=gw;break}}}}else if(S(e)){for(var a in e)if(e.hasOwnProperty(a)&&d(e[a])){n=vw;break}}else if(null!=e)throw new Error("Invalid data");bw(t).sourceFormat=n}function ss(t){return bw(t).source}function ls(t){bw(t).datasetMap=N()}function us(t){var e=t.option,n=e.data,i=I(n)?yw:fw,r=!1,o=e.seriesLayoutBy,a=e.sourceHeader,s=e.dimensions,l=vs(t);if(l){var u=l.option;n=u.source,i=bw(l).sourceFormat,r=!0,o=o||u.seriesLayoutBy,null==a&&(a=u.sourceHeader),s=s||u.dimensions}var h=hs(n,i,o,a,s);bw(t).source=new os({data:n,fromDataset:r,seriesLayoutBy:o,sourceFormat:i,dimensionsDefine:h.dimensionsDefine,startIndex:h.startIndex,dimensionsDetectCount:h.dimensionsDetectCount,encodeDefine:e.encode})}function hs(t,e,n,i,r){if(!t)return{dimensionsDefine:cs(r)};var o,a;if(e===pw)"auto"===i||null==i?ds(function(t){null!=t&&"-"!==t&&(b(t)?null==a&&(a=1):a=0)},n,t,10):a=i?1:0,r||1!==a||(r=[],ds(function(t,e){r[e]=null!=t?t:""},n,t)),o=r?r.length:n===_w?t.length:t[0]?t[0].length:null;else if(e===gw)r||(r=fs(t));else if(e===vw)r||(r=[],f(t,function(t,e){r.push(e)}));else if(e===fw){var s=ir(t[0]);o=_(s)&&s.length||1}else e===yw&&_v&&O(!!r,"dimensions must be given if data is TypedArray.");return{startIndex:a,dimensionsDefine:cs(r),dimensionsDetectCount:o}}function cs(t){if(t){var e=N();return p(t,function(t){if(t=a({},S(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var n=e.get(t.name);return n?t.name+="-"+n.count++:e.set(t.name,{count:1}),t})}}function ds(t,e,n,i){if(null==i&&(i=1/0),e===_w)for(var r=0;r<n.length&&i>r;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&i>r;r++)t(o[r],r)}function fs(t){for(var e,n=0;n<t.length&&!(e=t[n++]););if(e){var i=[];return f(e,function(t,e){i.push(e)}),i}}function ps(t,e,n){function i(t,e,n){for(var i=0;n>i;i++)t.push(e+i)}function r(t){var e=t.dimsDef;return e?e.length:1}var o={},a=vs(e);if(!a||!t)return o;var s,l,u=[],h=[],c=e.ecModel,d=bw(c).datasetMap,p=a.uid+"_"+n.seriesLayoutBy;t=t.slice(),f(t,function(e,n){!S(e)&&(t[n]={name:e}),"ordinal"===e.type&&null==s&&(s=n,l=r(t[n])),o[e.name]=[]});var g=d.get(p)||d.set(p,{categoryWayDim:l,valueWayDim:0});return f(t,function(t,e){var n=t.name,a=r(t);if(null==s){var l=g.valueWayDim;i(o[n],l,a),i(h,l,a),g.valueWayDim+=a}else if(s===e)i(o[n],0,a),i(u,0,a);else{var l=g.categoryWayDim;i(o[n],l,a),i(h,l,a),g.categoryWayDim+=a}}),u.length&&(o.itemName=u),h.length&&(o.seriesName=h),o}function gs(t,e,n){var i={},r=vs(t);if(!r)return i;var o,a=e.sourceFormat,s=e.dimensionsDefine;(a===gw||a===vw)&&f(s,function(t,e){"name"===(S(t)?t.name:t)&&(o=e)});var l=function(){function t(t){return null!=t.v&&null!=t.n}for(var i={},r={},l=[],u=0,h=Math.min(5,n);h>u;u++){var c=ys(e.data,a,e.seriesLayoutBy,s,e.startIndex,u);l.push(c);var d=c===ww.Not;if(d&&null==i.v&&u!==o&&(i.v=u),(null==i.n||i.n===i.v||!d&&l[i.n]===ww.Not)&&(i.n=u),t(i)&&l[i.n]!==ww.Not)return i;d||(c===ww.Might&&null==r.v&&u!==o&&(r.v=u),(null==r.n||r.n===r.v)&&(r.n=u))}return t(i)?i:t(r)?r:null}();if(l){i.value=l.v;var u=null!=o?o:l.n;i.itemName=[u],i.seriesName=[u]}return i}function vs(t){var e=t.option,n=e.data;return n?void 0:t.ecModel.getComponent("dataset",e.datasetIndex||0)}function ms(t,e){return ys(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function ys(t,e,n,i,r,o){function a(t){var e=b(t);return null!=t&&isFinite(t)&&""!==t?e?ww.Might:ww.Not:e&&"-"!==t?ww.Must:void 0}var s,l=5;if(I(t))return ww.Not;var u,h;if(i){var c=i[o];S(c)?(u=c.name,h=c.type):b(c)&&(u=c)}if(null!=h)return"ordinal"===h?ww.Must:ww.Not;if(e===pw)if(n===_w){for(var d=t[o],f=0;f<(d||[]).length&&l>f;f++)if(null!=(s=a(d[r+f])))return s}else for(var f=0;f<t.length&&l>f;f++){var p=t[r+f];if(p&&null!=(s=a(p[o])))return s}else if(e===gw){if(!u)return ww.Not;for(var f=0;f<t.length&&l>f;f++){var g=t[f];if(g&&null!=(s=a(g[u])))return s}}else if(e===vw){if(!u)return ww.Not;var d=t[u];if(!d||I(d))return ww.Not;for(var f=0;f<d.length&&l>f;f++)if(null!=(s=a(d[f])))return s}else if(e===fw)for(var f=0;f<t.length&&l>f;f++){var g=t[f],v=ir(g);if(!_(v))return ww.Not;if(null!=(s=a(v[o])))return s}return ww.Not}function xs(t,e){if(e){var n=e.seiresIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}function _s(t,e){var n=t.color&&!t.colorLayer;f(e,function(e,o){"colorLayer"===o&&n||lw.hasClass(o)||("object"==typeof e?t[o]=t[o]?r(t[o],e,!1):i(e):null==t[o]&&(t[o]=e))})}function ws(t){t=t,this.option={},this.option[Sw]=1,this._componentsMap=N({series:[]}),this._seriesIndices,this._seriesIndicesMap,_s(t,this._theme.option),r(t,hw,!1),this.mergeOption(t)}function bs(t,e){_(e)||(e=e?[e]:[]);var n={};return f(e,function(e){n[e]=(t.get(e)||[]).slice()}),n}function Ss(t,e,n){var i=e.type?e.type:n?n.subType:lw.determineSubType(t,e);return i}function Ms(t,e){t._seriesIndicesMap=N(t._seriesIndices=p(e,function(t){return t.componentIndex})||[])}function Is(t,e){return e.hasOwnProperty("subType")?v(t,function(t){return t.subType===e.subType}):t}function Cs(t){if(_v&&!t._seriesIndices)throw new Error("Option should contains series.")}function Ts(t){f(Iw,function(e){this[e]=y(t[e],t)},this)}function As(){this._coordinateSystems=[]}function Ds(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function ks(t,e,n){var i,r,o=[],a=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},o=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;Tw(l,function(t){t&&t.option&&(t.query?a.push(t):i||(i=t))})}return r||(r=t),r.timeline||(r.timeline=s),Tw([r].concat(o).concat(p(a,function(t){return t.option})),function(t){Tw(e,function(e){e(t,n)})}),{baseOption:r,timelineOptions:o,mediaDefault:i,mediaList:a}}function Ps(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return f(t,function(t,e){var n=e.match(Pw);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();Ls(i[a],t,o)||(r=!1)}}),r}function Ls(t,e,n){return"min"===n?t>=e:"max"===n?e>=t:t===e}function Os(t,e){return t.join(",")===e.join(",")}function zs(t,e){e=e||{},Tw(e,function(e,n){if(null!=e){var i=t[n];if(lw.hasClass(n)){e=er(e),i=er(i);var r=or(i,e);t[n]=Dw(r,function(t){return t.option&&t.exist?kw(t.exist,t.option,!0):t.exist||t.option})}else t[n]=kw(i,e,!0)}})}function Es(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=zw.length;i>n;n++){var o=zw[n],a=e.normal,s=e.emphasis;a&&a[o]&&(t[o]=t[o]||{},t[o].normal?r(t[o].normal,a[o]):t[o].normal=a[o],a[o]=null),s&&s[o]&&(t[o]=t[o]||{},t[o].emphasis?r(t[o].emphasis,s[o]):t[o].emphasis=s[o],s[o]=null)}}function Rs(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,s(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function Bs(t){Rs(t,"itemStyle"),Rs(t,"lineStyle"),Rs(t,"areaStyle"),Rs(t,"label"),Rs(t,"labelLine"),Rs(t,"upperLabel"),Rs(t,"edgeLabel")}function Ns(t,e){var n=Ow(t)&&t[e],i=Ow(n)&&n.textStyle;if(i)for(var r=0,o=Hy.length;o>r;r++){var e=Hy[r];i.hasOwnProperty(e)&&(n[e]=i[e])}}function Fs(t){t&&(Bs(t),Ns(t,"label"),t.emphasis&&Ns(t.emphasis,"label"))}function Vs(t){if(Ow(t)){Es(t),Bs(t),Ns(t,"label"),Ns(t,"upperLabel"),Ns(t,"edgeLabel"),t.emphasis&&(Ns(t.emphasis,"label"),Ns(t.emphasis,"upperLabel"),Ns(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Es(e),Fs(e));var n=t.markLine;n&&(Es(n),Fs(n));var i=t.markArea;i&&Fs(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!I(o))for(var a=0;a<o.length;a++)Fs(o[a]);f(t.categories,function(t){Bs(t)})}if(r&&!I(r))for(var a=0;a<r.length;a++)Fs(r[a]);var e=t.markPoint;if(e&&e.data)for(var s=e.data,a=0;a<s.length;a++)Fs(s[a]);var n=t.markLine;if(n&&n.data)for(var l=n.data,a=0;a<l.length;a++)_(l[a])?(Fs(l[a][0]),Fs(l[a][1])):Fs(l[a]);"gauge"===t.type?(Ns(t,"axisLabel"),Ns(t,"title"),Ns(t,"detail")):"treemap"===t.type?(Rs(t.breadcrumb,"itemStyle"),f(t.levels,function(t){Bs(t)})):"tree"===t.type&&Bs(t.leaves)}}function Hs(t){return _(t)?t:t?[t]:[]}function Ws(t){return(_(t)?t[0]:t)||{}}function Gs(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&(n=n&&n[e[i]],null!=n);i++);return n}function Zs(t,e,n,i){e=e.split(",");for(var r,o=t,a=0;a<e.length-1;a++)r=e[a],null==o[r]&&(o[r]={}),o=o[r];(i||null==o[e[a]])&&(o[e[a]]=n)}function Xs(t){f(Rw,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function Ys(t){f(t,function(e,n){var i=[],r=[0/0,0/0],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,l=a.map(o,function(o,l,u){var h=a.get(e.stackedDimension,u);if(isNaN(h))return r;var c,d;s?d=a.getRawIndex(u):c=a.get(e.stackedByDimension,u);for(var f=0/0,p=n-1;p>=0;p--){var g=t[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var v=g.data.getByRawIndex(g.stackResultDimension,d);if(h>=0&&v>0||0>=h&&0>v){h+=v,f=v;break}}}return i[0]=h,i[1]=f,i});a.hostModel.setData(l),e.data=l})}function Us(t,e){os.isInstance(t)||(t=os.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;if(i===yw){if(_v&&null==e)throw new Error("Typed array data must specify dimension size");this._offset=0,this._dimSize=e,this._data=n}var r=Hw[i===pw?i+"_"+t.seriesLayoutBy:i];_v&&O(r,"Invalide sourceFormat: "+i),a(this,r)}function js(){return this._data.length}function qs(t){return this._data[t]}function $s(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Ks(t,e,n){return null!=n?t[n]:t}function Qs(t,e,n,i){return Js(t[i],this._dimensionInfos[e])}function Js(t,e){var n=e&&e.type;if("ordinal"===n){var i=e&&e.ordinalMeta;return i?i.parseAndCollect(t):t}return"time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+La(t)),null==t||""===t?0/0:+t}function tl(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r,o,a=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(n);return s&&(r=s.name,o=s.index),Ww[a](i,e,o,r)}}}function el(t,e,n){if(t){var i=t.getProvider().getSource().sourceFormat;if(i===fw||i===gw){var r=t.getRawDataItem(e);return i!==fw||S(r)||(r=null),r?r[n]:void 0}}}function nl(t){return new il(t)}function il(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}function rl(t,e,n,i,r,o){Uw.reset(n,i,r,o),t._callingProgress=e,t._callingProgress({start:n,end:i,count:i-n,next:Uw.next},t.context)}function ol(t,e){t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null;var n,i;!e&&t._reset&&(n=t._reset(t.context),n&&n.progress&&(i=n.forceFirstProgress,n=n.progress),_(n)&&!n.length&&(n=null)),t._progress=n,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),i}function al(t){var e=t.name;sr(t)||(t.name=sl(t)||e)}function sl(t){var e=t.getRawData(),n=e.mapDimension("seriesName",!0),i=[];return f(n,function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)}),i.join(" ")}function ll(t){return t.model.getRawData().count()}function ul(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),hl}function hl(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function cl(t,e){f(t.CHANGABLE_METHODS,function(n){t.wrapMethod(n,x(dl,e))})}function dl(t){var e=fl(t);e&&e.setOutputEnd(this.count())}function fl(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}function pl(){this.group=new Nm,this.uid=ya("viewChart"),this.renderTask=nl({plan:ml,reset:yl}),this.renderTask.context={view:this}}function gl(t,e,n){if(t&&(t.trigger(e,n),t.isGroup&&!Wo(t)))for(var i=0,r=t.childCount();r>i;i++)gl(t.childAt(i),e,n)}function vl(t,e,n){var i=ur(t,e),r=e&&null!=e.highlightKey?Go(e.highlightKey):null;null!=i?f(er(i),function(e){gl(t.getItemGraphicEl(e),n,r)}):t.eachItemGraphicEl(function(t){gl(t,n,r)})}function ml(t){return tb(t.model)}function yl(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&Jw(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),nb[l]}function xl(t,e,n){function i(){h=(new Date).getTime(),c=null,t.apply(a,s||[])}var r,o,a,s,l,u=0,h=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),a=this,s=arguments;var t=l||e,d=l||n;l=null,o=r-(d?u:h)-t,clearTimeout(c),d?c=setTimeout(i,t):o>=0?i():c=setTimeout(i,-o),u=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function _l(t,e,n,i){var r=t[e];if(r){var o=r[ib]||r,a=r[ob],s=r[rb];if(s!==n||a!==i){if(null==n||!i)return t[e]=o;r=t[e]=xl(o,n,"debounce"===i),r[ib]=o,r[ob]=i,r[rb]=n}return r}}function wl(t,e){var n=t[e];n&&n[ib]&&(t[e]=n[ib])}function bl(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished;var n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice();this._allHandlers=n.concat(i),this._stageTaskMap=N()}function Sl(t,e,n,i,r){function o(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}r=r||{};var a;f(e,function(e){if(!r.visualType||r.visualType===e.visualType){var s=t._stageTaskMap.get(e.uid),l=s.seriesTaskMap,u=s.overallTask;if(u){var h,c=u.agentStubMap;c.each(function(t){o(r,t)&&(t.dirty(),h=!0)}),h&&u.dirty(),db(u,i);var d=t.getPerformArgs(u,r.block);c.each(function(t){t.perform(d)}),a|=u.perform(d)}else l&&l.each(function(s){o(r,s)&&s.dirty();var l=t.getPerformArgs(s,r.block);l.skip=!e.performRawSeries&&n.isSeriesFiltered(s.context.model),db(s,i),a|=s.perform(l)})}}),t.unfinished|=a}function Ml(t,e,n,i,r){function o(n){var o=n.uid,s=a.get(o)||a.set(o,nl({plan:kl,reset:Pl,count:Ol}));s.context={model:n,ecModel:i,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},zl(t,n,s)}var a=n.seriesTaskMap||(n.seriesTaskMap=N()),s=e.seriesType,l=e.getTargetSeries;e.createOnAllSeries?i.eachRawSeries(o):s?i.eachRawSeriesByType(s,o):l&&l(i,r).each(o);var u=t._pipelineMap;a.each(function(t,e){u.get(e)||(t.dispose(),a.removeKey(e))})}function Il(t,e,n,i,r){function o(e){var n=e.uid,i=s.get(n);i||(i=s.set(n,nl({reset:Tl,onDirty:Dl})),a.dirty()),i.context={model:e,overallProgress:h,modifyOutputEnd:c},i.agent=a,i.__block=h,zl(t,e,i)}var a=n.overallTask=n.overallTask||nl({reset:Cl});a.context={ecModel:i,api:r,overallReset:e.overallReset,scheduler:t};var s=a.agentStubMap=a.agentStubMap||N(),l=e.seriesType,u=e.getTargetSeries,h=!0,c=e.modifyOutputEnd;l?i.eachRawSeriesByType(l,o):u?u(i,r).each(o):(h=!1,f(i.getSeries(),o));var d=t._pipelineMap;s.each(function(t,e){d.get(e)||(t.dispose(),a.dirty(),s.removeKey(e))})}function Cl(t){t.overallReset(t.ecModel,t.api,t.payload)}function Tl(t){return t.overallProgress&&Al}function Al(){this.agent.dirty(),this.getDownstream().dirty()}function Dl(){this.agent&&this.agent.dirty()}function kl(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function Pl(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=er(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?p(e,function(t,e){return Ll(e)}):fb}function Ll(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function Ol(t){return t.data.count()}function zl(t,e,n){var i=e.uid,r=t._pipelineMap.get(i);!r.head&&(r.head=n),r.tail&&r.tail.pipe(n),r.tail=n,n.__idxInPipeline=r.count++,n.__pipeline=r}function El(t){pb=null;try{t(gb,vb)}catch(e){}return pb}function Rl(t,e){for(var n in e.prototype)t[n]=V}function Bl(t){if(b(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}function Nl(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}function Fl(t,e){for(var n=t.firstChild;n;){if(1===n.nodeType){var i=n.getAttribute("offset");i=i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var r=n.getAttribute("stop-color")||"#000000";e.addColorStop(i,r)}n=n.nextSibling}}function Vl(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),s(e.__inheritedStyle,t.__inheritedStyle))}function Hl(t){for(var e=z(t).split(Mb),n=[],i=0;i<e.length;i+=2){var r=parseFloat(e[i]),o=parseFloat(e[i+1]);n.push([r,o])}return n}function Wl(t,e,n,i){var r=e.__inheritedStyle||{},o="text"===e.type;if(1===t.nodeType&&(Zl(t,e),a(r,Xl(t)),!i))for(var s in Tb)if(Tb.hasOwnProperty(s)){var l=t.getAttribute(s);null!=l&&(r[Tb[s]]=l)}var u=o?"textFill":"fill",h=o?"textStroke":"stroke";e.style=e.style||new Um;var c=e.style;null!=r.fill&&c.set(u,Gl(r.fill,n)),null!=r.stroke&&c.set(h,Gl(r.stroke,n)),f(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&o?"textStrokeWidth":t;null!=r[t]&&c.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),f(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&c.set(t,r[t])}),r.lineDash&&(e.style.lineDash=z(r.lineDash).split(Mb)),c[h]&&"none"!==c[h]&&(e[h]=!0),e.__inheritedStyle=r}function Gl(t,e){var n=e&&t&&t.match(Ab);if(n){var i=z(n[1]),r=e[i];return r}return t}function Zl(t,e){var n=t.getAttribute("transform");if(n){n=n.replace(/,/g," ");var i=null,r=[];n.replace(Db,function(t,e,n){r.push(e,n)});for(var o=r.length-1;o>0;o-=2){var a=r[o],s=r[o-1];switch(i=i||Oe(),s){case"translate":a=z(a).split(Mb),Be(i,i,[parseFloat(a[0]),parseFloat(a[1]||0)]);break;case"scale":a=z(a).split(Mb),Fe(i,i,[parseFloat(a[0]),parseFloat(a[1]||a[0])]);break;case"rotate":a=z(a).split(Mb),Ne(i,i,parseFloat(a[0]));break;case"skew":a=z(a).split(Mb),console.warn("Skew transform is not supported yet");break;case"matrix":var a=z(a).split(Mb);i[0]=parseFloat(a[0]),i[1]=parseFloat(a[1]),i[2]=parseFloat(a[2]),i[3]=parseFloat(a[3]),i[4]=parseFloat(a[4]),i[5]=parseFloat(a[5])}}e.setLocalTransform(i)}}function Xl(t){var e=t.getAttribute("style"),n={};if(!e)return n;var i={};kb.lastIndex=0;for(var r;null!=(r=kb.exec(e));)i[r[1]]=r[2];for(var o in Tb)Tb.hasOwnProperty(o)&&null!=i[o]&&(n[Tb[o]]=i[o]);return n}function Yl(t,e,n){var i=e/t.width,r=n/t.height,o=Math.min(i,r),a=[o,o],s=[-(t.x+t.width/2)*o+e/2,-(t.y+t.height/2)*o+n/2];return{scale:a,position:s}}function Ul(t,e){return function(n,i,r){return!e&&this._disposed?void hu(this.id):(n=n&&n.toLowerCase(),void Yv.prototype[t].call(this,n,i,r))}}function jl(){Yv.call(this)}function ql(t,e,n){function r(t,e){return t.__prio-e.__prio}n=n||{},"string"==typeof e&&(e=dS[e]),this.id,this.group,this._dom=t;var o="canvas";_v&&(o=("undefined"==typeof window?global:window).__ECHARTS__DEFAULT__RENDERER__||o);var a=this._zr=$i(t,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=xl(y(a.flush,a),17);var e=i(e);e&&Nw(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new As;var s=this._api=pu(this);En(cS,r),En(lS,r),this._scheduler=new bl(this,s,lS,cS),Yv.call(this,this._ecEventProcessor=new gu),this._messageCenter=new jl,this._initEvents(),this.resize=y(this.resize,this),this._pendingActions=[],a.animation.on("frame",this._onframe,this),iu(a,this),E(this)}function $l(t,e,n){if(this._disposed)return void hu(this.id);var i,r=this._model,o=this._coordSysMgr.getCoordinateSystems();e=cr(r,e);for(var a=0;a<o.length;a++){var s=o[a];if(s[t]&&null!=(i=s[t](r,e,n)))return i}_v&&console.warn("No coordinate system that supports "+t+" found by the given finder.")}function Kl(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),ru(t,"component",e,n),ru(t,"chart",e,n),n.plan()}function Ql(t,e,n,i,r){function o(i){i&&i.__alive&&i[e]&&i[e](i.__model,a,t._api,n)}var a=t._model;if(!i)return void Eb(t._componentsViews.concat(t._chartsViews),o);var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var l={mainType:i,query:s};r&&(l.subType=r);var u=n.excludeSeriesId;null!=u&&(u=N(er(u))),a&&a.eachComponent(l,function(e){u&&null!=u.get(e.id)||o(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function Jl(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})}function tu(t,e){var n=t.type,i=t.escapeConnect,r=aS[n],o=r.actionInfo,l=(o.update||"update").split(":"),u=l.pop();l=null!=l[0]&&Nb(l[0]),this[tS]=!0;var h=[t],c=!1;t.batch&&(c=!0,h=p(t.batch,function(e){return e=s(a({},e),t),e.batch=null,e}));var d,f=[],g="highlight"===n||"downplay"===n;Eb(h,function(t){d=r.action(t,this._model,this._api),d=d||a({},t),d.type=o.event||d.type,f.push(d),g?Ql(this,u,t,"series"):l&&Ql(this,u,t,l.main,l.sub)},this),"none"===u||g||l||(this[eS]?(Kl(this),rS.update.call(this,t),this[eS]=!1):rS[u].call(this,t)),d=c?{type:o.event||n,escapeConnect:i,batch:f}:f[0],this[tS]=!1,!e&&this._messageCenter.trigger(d.type,d)}function eu(t){for(var e=this._pendingActions;e.length;){var n=e.shift();tu.call(this,n,t)}}function nu(t){!t&&this.trigger("updated")}function iu(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[eS]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function ru(t,e,n,i){function r(t){var e="_ec_"+t.id+"_"+t.type,r=s[e];if(!r){var h=Nb(t.type),c=o?$w.getClass(h.main,h.sub):pl.getClass(h.sub);_v&&zb(c,h.sub+" does not exist."),r=new c,r.init(n,u),s[e]=r,a.push(r),l.add(r.group)}t.__viewId=r.__id=e,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!o&&i.prepareView(r,t,n,u)}for(var o="component"===e,a=o?t._componentsViews:t._chartsViews,s=o?t._componentsMap:t._chartsMap,l=t._zr,u=t._api,h=0;h<a.length;h++)a[h].__alive=!1;o?n.eachComponent(function(t,e){"series"!==t&&r(e)}):n.eachSeries(r);for(var h=0;h<a.length;){var c=a[h];c.__alive?h++:(!o&&c.renderTask.dispose(),l.remove(c.group),c.dispose(n,u),a.splice(h,1),delete s[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function ou(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function au(t,e,n,i){su(t,e,n,i),Eb(t._chartsViews,function(t){t.__alive=!1}),lu(t,e,n,i),Eb(t._chartsViews,function(t){t.__alive||t.remove(e,n)})}function su(t,e,n,i,r){Eb(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,n,i),fu(r,t)})}function lu(t,e,n,i,r){var o,a=t._scheduler;e.eachSeries(function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;
var s=n.renderTask;a.updatePayload(s,i),r&&r.get(e.uid)&&s.dirty(),o|=s.perform(a.getPerformArgs(s)),n.group.silent=!!e.get("silent"),fu(e,n),du(e,n)}),a.unfinished|=o,cu(t,e),lb(t._zr.dom,e)}function uu(t,e){Eb(hS,function(n){n(t,e)})}function hu(t){_v&&console.warn("Instance "+t+" has been disposed")}function cu(t,e){var n=t._zr,i=n.storage,r=0;i.traverse(function(){r++}),r>e.get("hoverLayerThreshold")&&!Mv.node&&e.eachSeries(function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.group.traverse(function(t){t.useHoverLayer=!0})}})}function du(t,e){var n=t.get("blendMode")||null;_v&&!Mv.canvasSupported&&n&&"source-over"!==n&&console.warn("Only canvas support blendMode"),e.group.traverse(function(t){t.isGroup||t.style.blend!==n&&t.setStyle("blend",n),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",n)})})}function fu(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function pu(t){var e=t._coordSysMgr;return a(new Ts(t),{getCoordinateSystems:y(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}}})}function gu(){this.eventInfo}function vu(t){function e(t,e){for(var n=0;n<t.length;n++){var i=t[n];i[o]=e}}var n=0,i=1,r=2,o="__connectUpdateStatus";Eb(sS,function(a,s){t._messageCenter.on(s,function(a){if(gS[t.group]&&t[o]!==n){if(a&&a.escapeConnect)return;var s=t.makeActionFromEvent(a),l=[];Eb(pS,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,n),Eb(l,function(t){t[o]!==i&&t.dispatchAction(s)}),e(l,r)}})})}function mu(t,e,n){if(_v){if(zy.replace(".","")-0<Vb.zrender.replace(".","")-0)throw new Error("zrender/src "+zy+" is too old for ECharts "+Fb+". Current version need ZRender "+Vb.zrender+"+");if(!t)throw new Error("Initialize failed: invalid dom.")}var i=wu(t);if(i)return _v&&console.warn("There is a chart instance already initialized on the dom."),i;_v&&(!C(t)||"CANVAS"===t.nodeName.toUpperCase()||(t.clientWidth||n&&null!=n.width)&&(t.clientHeight||n&&null!=n.height)||console.warn("Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload."));var r=new ql(t,e,n);return r.id="ec_"+vS++,pS[r.id]=r,fr(t,yS,r.id),vu(r),r}function yu(t){if(_(t)){var e=t;t=null,Eb(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+mS++,Eb(e,function(e){e.group=t})}return gS[t]=!0,t}function xu(t){gS[t]=!1}function _u(t){"string"==typeof t?t=pS[t]:t instanceof ql||(t=wu(t)),t instanceof ql&&!t.isDisposed()&&t.dispose()}function wu(t){return pS[pr(t,yS)]}function bu(t){return pS[t]}function Su(t,e){dS[t]=e}function Mu(t){uS.push(t)}function Iu(t,e){Lu(lS,t,e,Wb)}function Cu(t){hS.push(t)}function Tu(t,e,n){"function"==typeof e&&(n=e,e="");var i=Bb(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,zb(nS.test(i)&&nS.test(e)),aS[i]||(aS[i]={action:n,actionInfo:t}),sS[e]=i}function Au(t,e){As.register(t,e)}function Du(t){var e=As.get(t);return e?e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice():void 0}function ku(t,e){Lu(cS,t,e,Yb,"layout")}function Pu(t,e){Lu(cS,t,e,qb,"visual")}function Lu(t,e,n,i,r){if((Rb(e)||Bb(e))&&(n=e,e=i),_v){if(isNaN(e)||null==e)throw new Error("Illegal priority");Eb(t,function(t){zb(t.__raw!==n)})}var o=bl.wrapStageHandler(n,r);return o.__prio=e,o.__raw=n,t.push(o),o}function Ou(t,e){fS[t]=e}function zu(t){return lw.extend(t)}function Eu(t){return $w.extend(t)}function Ru(t){return qw.extend(t)}function Bu(t){return pl.extend(t)}function Nu(t){n("createCanvas",t)}function Fu(t,e,n){Lb.registerMap(t,e,n)}function Vu(t){var e=Lb.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}function Hu(t){return t}function Wu(t,e,n,i,r){this._old=t,this._new=e,this._oldKeyGetter=n||Hu,this._newKeyGetter=i||Hu,this.context=r}function Gu(t,e,n,i,r){for(var o=0;o<t.length;o++){var a="_ec_"+r[i](t[o],o),s=e[a];null==s?(n.push(a),e[a]=o):(s.length||(e[a]=s=[s]),s.push(o))}}function Zu(t){var e={},n=e.encode={},i=N(),r=[],o=[],a=e.userOutput={dimensionNames:t.dimensions.slice(),encode:{}};f(t.dimensions,function(e){var s=t.getDimensionInfo(e),l=s.coordDim;if(l){_v&&O(null==wS.get(l));var u=s.coordDimIndex;Xu(n,l)[u]=e,s.isExtraCoord||(i.set(l,1),Uu(s.type)&&(r[0]=e),Xu(a.encode,l)[u]=s.index),s.defaultTooltip&&o.push(e)}wS.each(function(t,e){var i=Xu(n,e),r=s.otherDims[e];null!=r&&r!==!1&&(i[r]=s.name)})});var s=[],l={};i.each(function(t,e){var i=n[e];l[e]=i[0],s=s.concat(i)}),e.dataDimsOnCoord=s,e.encodeFirstDimNotExtra=l;var u=n.label;u&&u.length&&(r=u.slice());var h=n.tooltip;return h&&h.length?o=h.slice():o.length||(o=r.slice()),n.defaultedLabel=r,n.defaultedTooltip=o,e}function Xu(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function Yu(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function Uu(t){return!("ordinal"===t||"time"===t)}function ju(t){null!=t&&a(this,t),this.otherDims={}}function qu(t){return t._rawCount>65535?TS:DS}function $u(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function Ku(t,e){f(kS.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods,f(PS,function(n){t[n]=i(e[n])}),t._calculationInfo=a(e._calculationInfo)}function Qu(t,e,n,i,r){var o=CS[e.type],a=i-1,s=e.name,l=t[s][a];if(l&&l.length<n){for(var u=new o(Math.min(r-a*n,n)),h=0;h<l.length;h++)u[h]=l[h];t[s][a]=u}for(var c=i*n;r>c;c+=n)t[s].push(new o(Math.min(r-c,n)))}function Ju(t){var e=t._invertedIndicesMap;f(e,function(n,i){var r=t._dimensionInfos[i],o=r.ordinalMeta;if(o){n=e[i]=new AS(o.categories.length);for(var a=0;a<n.length;a++)n[a]=MS;for(var a=0;a<t._count;a++)n[t.get(i,a)]=a}})}function th(t,e,n){var i;if(null!=e){var r=t._chunkSize,o=Math.floor(n/r),a=n%r,s=t.dimensions[e],l=t._storage[s][o];if(l){i=l[a];var u=t._dimensionInfos[s].ordinalMeta;u&&u.categories.length&&(i=u.categories[i])}}return i}function eh(t){return t}function nh(t){return t<this._count&&t>=0?this._indices[t]:-1}function ih(t,e){var n=t._idList[e];return null==n&&(n=th(t,t._idDimIdx,e)),null==n&&(n=IS+e),n}function rh(t){return _(t)||(t=[t]),t}function oh(t,e){for(var n=0;n<e.length;n++)t._dimensionInfos[e[n]]||console.error("Unkown dimension "+e[n])}function ah(t,e){var n=t.dimensions,i=new LS(p(n,t.getDimensionInfo,t),t.hostModel);Ku(i,t);for(var r=i._storage={},o=t._storage,a=0;a<n.length;a++){var s=n[a];o[s]&&(u(e,s)>=0?(r[s]=sh(o[s]),i._rawExtent[s]=lh(),i._extent[s]=null):r[s]=o[s])}return i}function sh(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=$u(t[n]);return e}function lh(){return[1/0,-1/0]}function uh(t,e,n){function r(t,e,n){null!=wS.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,u.set(e,!0))}os.isInstance(e)||(e=os.seriesDataToSource(e)),n=n||{},t=(t||[]).slice();for(var o=(n.dimsDef||[]).slice(),l=N(),u=N(),h=[],c=hh(e,t,o,n.dimCount),d=0;c>d;d++){var p=o[d]=a({},S(o[d])?o[d]:{name:o[d]}),g=p.name,v=h[d]=new ju;null!=g&&null==l.get(g)&&(v.name=v.displayName=g,l.set(g,d)),null!=p.type&&(v.type=p.type),null!=p.displayName&&(v.displayName=p.displayName)}var m=n.encodeDef;!m&&n.encodeDefaulter&&(m=n.encodeDefaulter(e,c)),m=N(m),m.each(function(t,e){if(t=er(t).slice(),1===t.length&&!b(t[0])&&t[0]<0)return void m.set(e,!1);var n=m.set(e,[]);f(t,function(t,i){b(t)&&(t=l.get(t)),null!=t&&c>t&&(n[i]=t,r(h[t],e,i))})});var y=0;f(t,function(t){var e,t,n,o;if(b(t))e=t,t={};else{e=t.name;var a=t.ordinalMeta;t.ordinalMeta=null,t=i(t),t.ordinalMeta=a,n=t.dimsDef,o=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var l=m.get(e);if(l!==!1){var l=er(l);if(!l.length)for(var u=0;u<(n&&n.length||1);u++){for(;y<h.length&&null!=h[y].coordDim;)y++;y<h.length&&l.push(y++)}f(l,function(i,a){var l=h[i];if(r(s(l,t),e,a),null==l.name&&n){var u=n[a];!S(u)&&(u={name:u}),l.name=l.displayName=u.name,l.defaultTooltip=u.defaultTooltip}o&&s(l.otherDims,o)})}});var x=n.generateCoord,_=n.generateCoordCount,w=null!=_;_=x?_||1:0;for(var M=x||"value",I=0;c>I;I++){var v=h[I]=h[I]||new ju,C=v.coordDim;null==C&&(v.coordDim=ch(M,u,w),v.coordDimIndex=0,(!x||0>=_)&&(v.isExtraCoord=!0),_--),null==v.name&&(v.name=ch(v.coordDim,l)),null!=v.type||ms(e,I,v.name)!==ww.Must&&(!v.isExtraCoord||null==v.otherDims.itemName&&null==v.otherDims.seriesName)||(v.type="ordinal")}return h}function hh(t,e,n,i){var r=Math.max(t.dimensionsDetectCount||1,e.length,n.length,i||0);return f(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function ch(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}function dh(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=N(),this.categoryAxisMap=N(),this.firstCategoryDimIndex=null}function fh(t){var e=t.get("coordinateSystem"),n=new dh(e),i=RS[e];return i?(i(t,n,n.axisMap,n.categoryAxisMap),n):void 0}function ph(t){return"category"===t.get("type")}function gh(t,e,n){n=n||{};var i,r,o,a,s=n.byIndex,l=n.stackedCoordDimension,u=!(!t||!t.get("stack"));if(f(e,function(t,n){b(t)&&(e[n]=t={name:t}),u&&!t.isExtraCoord&&(s||i||!t.ordinalMeta||(i=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||i||(s=!0),r){o="__\x00ecstackresult",a="__\x00ecstackedover",i&&(i.createInvertedIndices=!0);var h=r.coordDim,c=r.type,d=0;f(e,function(t){t.coordDim===h&&d++}),e.push({name:o,coordDim:h,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,e.push({name:a,coordDim:a,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:i&&i.name,isStackedByIndex:s,stackedOverDimension:a,stackResultDimension:o}}function vh(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function mh(t,e){return vh(t,e)?t.getCalculationInfo("stackResultDimension"):e}function yh(t,e,n){n=n||{},os.isInstance(t)||(t=os.seriesDataToSource(t));var i,r=e.get("coordinateSystem"),o=As.get(r),a=fh(e);a&&(i=p(a.coordSysDims,function(t){var e={name:t},n=a.axisMap.get(t);if(n){var i=n.get("type");e.type=Yu(i)}return e})),i||(i=o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"]);var s,l,u=ES(t,{coordDimensions:i,generateCoord:n.generateCoord,encodeDefaulter:n.useEncodeDefaulter?x(ps,i,e):null});a&&f(u,function(t,e){var n=t.coordDim,i=a.categoryAxisMap.get(n);i&&(null==s&&(s=e),t.ordinalMeta=i.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(u[s].otherDims.itemName=0);var h=gh(e,u),c=new LS(u,e);c.setCalculationInfo(h);var d=null!=s&&xh(t)?function(t,e,n,i){return i===s?n:this.defaultDimValueGetter(t,e,n,i)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function xh(t){if(t.sourceFormat===fw){var e=_h(t.data||[]);return null!=e&&!_(ir(e))}}function _h(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function wh(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function bh(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}function Sh(t){return t._map||(t._map=N(t.categories))}function Mh(t){return S(t)&&null!=t.value?t.value:t+""}function Ih(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=Ea(o/e,!0);null!=n&&n>a&&(a=r.interval=n),null!=i&&a>i&&(a=r.interval=i);var s=r.intervalPrecision=Ch(a),l=r.niceTickExtent=[VS(Math.ceil(t[0]/a)*a,s),VS(Math.floor(t[1]/a)*a,s)];return Ah(l,t),r}function Ch(t){return Ta(t)+2}function Th(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Ah(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),Th(t,0,e),Th(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function Dh(t){return t.get("stack")||GS+t.seriesIndex}function kh(t){return t.dim+t.index}function Ph(t,e){var n=[];return e.eachSeriesByType(t,function(t){Bh(t)&&!Nh(t)&&n.push(t)}),n}function Lh(t){var e={};f(t,function(t){var n=t.coordinateSystem,i=n.getBaseAxis();if("time"===i.type||"value"===i.type)for(var r=t.getData(),o=i.dim+"_"+i.index,a=r.mapDimension(i.dim),s=0,l=r.count();l>s;++s){var u=r.get(a,s);e[o]?e[o].push(u):e[o]=[u]}});var n=[];for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r){r.sort(function(t,e){return t-e});for(var o=null,a=1;a<r.length;++a){var s=r[a]-r[a-1];s>0&&(o=null===o?s:Math.min(o,s))}n[i]=o}}return n}function Oh(t){var e=Lh(t),n=[];return f(t,function(t){var i,r=t.coordinateSystem,o=r.getBaseAxis(),a=o.getExtent();if("category"===o.type)i=o.getBandWidth();else if("value"===o.type||"time"===o.type){var s=o.dim+"_"+o.index,l=e[s],u=Math.abs(a[1]-a[0]),h=o.scale.getExtent(),c=Math.abs(h[1]-h[0]);i=l?u/c*l:u}else{var d=t.getData();i=Math.abs(a[1]-a[0])/d.count()}var f=Sa(t.get("barWidth"),i),p=Sa(t.get("barMaxWidth"),i),g=Sa(t.get("barMinWidth")||1,i),v=t.get("barGap"),m=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:f,barMaxWidth:p,barMinWidth:g,barGap:v,barCategoryGap:m,axisKey:kh(o),stackId:Dh(t)})}),zh(n)}function zh(t){var e={};f(t,function(t){var n=t.axisKey,i=t.bandWidth,r=e[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=r.stacks;e[n]=r;var a=t.stackId;o[a]||r.autoWidthCount++,o[a]=o[a]||{width:0,maxWidth:0};var s=t.barWidth;s&&!o[a].width&&(o[a].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(o[a].maxWidth=l);var u=t.barMinWidth;u&&(o[a].minWidth=u);var h=t.barGap;null!=h&&(r.gap=h);var c=t.barCategoryGap;null!=c&&(r.categoryGap=c)});var n={};return f(e,function(t,e){n[e]={};var i=t.stacks,r=t.bandWidth,o=Sa(t.categoryGap,r),a=Sa(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,u=(s-o)/(l+(l-1)*a);u=Math.max(u,0),f(i,function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){var i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,s-=i+a*i,l--}else{var i=u;e&&i>e&&(i=Math.min(e,s)),n&&n>i&&(i=n),i!==u&&(t.width=i,s-=i+a*i,l--)}}),u=(s-o)/(l+(l-1)*a),u=Math.max(u,0);var h,c=0;f(i,function(t){t.width||(t.width=u),h=t,c+=t.width*(1+a)}),h&&(c-=h.width*a);var d=-c/2;f(i,function(t,i){n[e][i]=n[e][i]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+a)})}),n}function Eh(t,e,n){if(t&&e){var i=t[kh(e)];return null!=i&&null!=n&&(i=i[Dh(n)]),i}}function Rh(t,e){var n=Ph(t,e),i=Oh(n),r={};f(n,function(t){var e=t.getData(),n=t.coordinateSystem,o=n.getBaseAxis(),a=Dh(t),s=i[kh(o)][a],l=s.offset,u=s.width,h=n.getOtherAxis(o),c=t.get("barMinHeight")||0;r[a]=r[a]||[],e.setLayout({bandWidth:s.bandWidth,offset:l,size:u});for(var d=e.mapDimension(h.dim),f=e.mapDimension(o.dim),p=vh(e,d),g=h.isHorizontal(),v=Fh(o,h,p),m=0,y=e.count();y>m;m++){var x=e.get(d,m),_=e.get(f,m),w=x>=0?"p":"n",b=v;p&&(r[a][_]||(r[a][_]={p:v,n:v}),b=r[a][_][w]);var S,M,I,C;if(g){var T=n.dataToPoint([x,_]);S=b,M=T[1]+l,I=T[0]-v,C=u,Math.abs(I)<c&&(I=(0>I?-1:1)*c),isNaN(I)||p&&(r[a][_][w]+=I)}else{var T=n.dataToPoint([_,x]);S=T[0]+l,M=b,I=u,C=T[1]-v,Math.abs(C)<c&&(C=(0>=C?-1:1)*c),isNaN(C)||p&&(r[a][_][w]+=C)}e.setItemLayout(m,{x:S,y:M,width:I,height:C})}},this)}function Bh(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Nh(t){return t.pipelineContext&&t.pipelineContext.large}function Fh(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}function Vh(t,e){return aM(t,oM(e))}function Hh(t,e){var n,i,r,o=t.type,a=e.getMin(),s=e.getMax(),l=t.getExtent();"ordinal"===o?n=e.getCategories().length:(i=e.get("boundaryGap"),_(i)||(i=[i||0,i||0]),"boolean"==typeof i[0]&&(_v&&console.warn('Boolean type for boundaryGap is only allowed for ordinal axis. Please use string in percentage instead, e.g., "20%". Currently, boundaryGap is set to be 0.'),i=[0,0]),i[0]=Sa(i[0],1),i[1]=Sa(i[1],1),r=l[1]-l[0]||Math.abs(l[0])),"dataMin"===a?a=l[0]:"function"==typeof a&&(a=a({min:l[0],max:l[1]})),"dataMax"===s?s=l[1]:"function"==typeof s&&(s=s({min:l[0],max:l[1]}));var u=null!=a,h=null!=s;null==a&&(a="ordinal"===o?n?0:0/0:l[0]-i[0]*r),null==s&&(s="ordinal"===o?n?n-1:0/0:l[1]+i[1]*r),(null==a||!isFinite(a))&&(a=0/0),(null==s||!isFinite(s))&&(s=0/0),t.setBlank(T(a)||T(s)||"ordinal"===o&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(a>0&&s>0&&!u&&(a=0),0>a&&0>s&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===o){var d,p=Ph("bar",c);if(f(p,function(t){d|=t.getBaseAxis()===e.axis}),d){var g=Oh(p),v=Wh(a,s,e,g);a=v.min,s=v.max}}return{extent:[a,s],fixMin:u,fixMax:h}}function Wh(t,e,n,i){var r=n.axis.getExtent(),o=r[1]-r[0],a=Eh(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;f(a,function(t){s=Math.min(t.offset,s)});var l=-1/0;f(a,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=1-(s+l)/o,d=h/c-h;return e+=d*(l/u),t-=d*(s/u),{min:t,max:e}}function Gh(t,e){var n=Hh(t,e),i=n.extent,r=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var a=e.get("interval");null!=a&&t.setInterval&&t.setInterval(a)}function Zh(t,e){if(e=e||t.get("type"))switch(e){case"category":return new FS(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new WS;default:return(wh.getClass(e)||WS).create(t)}}function Xh(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||0>n&&0>i)}function Yh(t){var e=t.getLabelModel().get("formatter"),n="category"===t.type?t.scale.getExtent()[0]:null;return"string"==typeof e?e=function(e){return function(n){return n=t.scale.getLabel(n),e.replace("{value}",null!=n?n:"")}}(e):"function"==typeof e?function(i,r){return null!=n&&(r=i-n),e(Uh(t,i),r)}:function(e){return t.scale.getLabel(e)}}function Uh(t,e){return"category"===t.type?t.scale.getLabel(e):e}function jh(t){var e=t.model,n=t.scale;if(e.get("axisLabel.show")&&!n.isBlank()){var i,r,o="category"===t.type,a=n.getExtent();o?r=n.count():(i=n.getTicks(),r=i.length);var s,l=t.getLabelModel(),u=Yh(t),h=1;r>40&&(h=Math.ceil(r/40));for(var c=0;r>c;c+=h){var d=i?i[c]:a[0]+c,f=u(d),p=l.getTextRect(f),g=qh(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function qh(t,e){var n=e*Math.PI/180,i=t.plain(),r=i.width,o=i.height,a=r*Math.abs(Math.cos(n))+Math.abs(o*Math.sin(n)),s=r*Math.abs(Math.sin(n))+Math.abs(o*Math.cos(n)),l=new Tn(i.x,i.y,a,s);return l}function $h(t){var e=t.get("interval");return null==e?"auto":e}function Kh(t){return"category"===t.type&&0===$h(t.getLabelModel())}function Qh(t,e){if("image"!==this.type){var n=this.style,i=this.shape;i&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1)}}function Jh(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?bo(t.slice(8),new Tn(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?wo(t.slice(7),{},new Tn(e,n,i,r),a?"center":"cover"):new _M({shape:{symbolType:t,x:e,y:n,width:i,height:r}}),l.__isEmptyBrush=s,l.setColor=Qh,l.setColor(o),l}function tc(t){return yh(t.getSource(),t)}function ec(t,e){var n=e;ga.isInstance(e)||(n=new ga(e),c(n,dM));var i=Zh(n);return i.setExtent(t[0],t[1]),Gh(i,n),i}function nc(t){c(t,dM)}function ic(t,e){return Math.abs(t-e)<SM}function rc(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=Ur(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return ic(r[0],s[0])&&ic(r[1],s[1])||(i+=Ur(r[0],r[1],s[0],s[1],e,n)),0!==i}function oc(t,e,n){if(this.name=t,this.geometries=e,n)n=[n[0],n[1]];else{var i=this.getBoundingRect();n=[i.x+i.width/2,i.y+i.height/2]}this.center=n}function ac(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,i=0;i<n.length;i++)for(var r=n[i],o=r.geometry,a=o.coordinates,s=o.encodeOffsets,l=0;l<a.length;l++){var u=a[l];if("Polygon"===o.type)a[l]=sc(u,s[l],e);else if("MultiPolygon"===o.type)for(var h=0;h<u.length;h++){var c=u[h];u[h]=sc(c,s[l][h],e)}}return t.UTF8Encoding=!1,t}function sc(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),s+=r,l+=o,r=s,o=l,i.push([s/n,l/n])}return i}function lc(t){return"category"===t.type?hc(t):fc(t)}function uc(t,e){return"category"===t.type?dc(t,e):{ticks:t.scale.getTicks()}}function hc(t){var e=t.getLabelModel(),n=cc(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}function cc(t,e){var n=pc(t,"labels"),i=$h(e),r=gc(n,i);if(r)return r;var o,a;return w(i)?o=wc(t,i):(a="auto"===i?mc(t):i,o=_c(t,a)),vc(n,i,{labels:o,labelCategoryInterval:a})}function dc(t,e){var n=pc(t,"ticks"),i=$h(e),r=gc(n,i);if(r)return r;var o,a;if((!e.get("show")||t.scale.isBlank())&&(o=[]),w(i))o=wc(t,i,!0);else if("auto"===i){var s=cc(t,t.getLabelModel());a=s.labelCategoryInterval,o=p(s.labels,function(t){return t.tickValue})}else a=i,o=_c(t,a,!0);return vc(n,i,{ticks:o,tickCategoryInterval:a})}function fc(t){var e=t.scale.getTicks(),n=Yh(t);return{labels:p(e,function(e,i){return{formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e}})}}function pc(t,e){return IM(t)[e]||(IM(t)[e]=[])}function gc(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function vc(t,e,n){return t.push({key:e,value:n}),n}function mc(t){var e=IM(t).autoInterval;return null!=e?e:IM(t).autoInterval=t.calculateCategoryInterval()}function yc(t){var e=xc(t),n=Yh(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,o=r.getExtent(),a=r.count();if(o[1]-o[0]<1)return 0;var s=1;a>40&&(s=Math.max(1,Math.floor(a/40)));for(var l=o[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(i)),c=Math.abs(u*Math.sin(i)),d=0,f=0;l<=o[1];l+=s){var p=0,g=0,v=Un(n(l),e.font,"center","top");p=1.3*v.width,g=1.3*v.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var m=d/h,y=f/c;isNaN(m)&&(m=1/0),isNaN(y)&&(y=1/0);var x=Math.max(0,Math.floor(Math.min(m,y))),_=IM(t.model),w=t.getExtent(),b=_.lastAutoInterval,S=_.lastTickCount;return null!=b&&null!=S&&Math.abs(b-x)<=1&&Math.abs(S-a)<=1&&b>x&&_.axisExtend0===w[0]&&_.axisExtend1===w[1]?x=b:(_.lastTickCount=a,_.lastAutoInterval=x,_.axisExtend0=w[0],_.axisExtend1=w[1]),x}function xc(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function _c(t,e,n){function i(t){l.push(n?t:{formattedLabel:r(t),rawLabel:o.getLabel(t),tickValue:t})}var r=Yh(t),o=t.scale,a=o.getExtent(),s=t.getLabelModel(),l=[],u=Math.max((e||0)+1,1),h=a[0],c=o.count();0!==h&&u>1&&c/u>2&&(h=Math.round(Math.ceil(h/u)*u));var d=Kh(t),f=s.get("showMinLabel")||d,p=s.get("showMaxLabel")||d;f&&h!==a[0]&&i(a[0]);for(var g=h;g<=a[1];g+=u)i(g);return p&&g-u!==a[1]&&i(a[1]),l}function wc(t,e,n){var i=t.scale,r=Yh(t),o=[];return f(i.getTicks(),function(t){var a=i.getLabel(t);e(t,a)&&o.push(n?t:{formattedLabel:r(t),rawLabel:a,tickValue:t})}),o}function bc(t,e){var n=t[1]-t[0],i=e,r=n/i/2;t[0]+=r,t[1]-=r}function Sc(t,e,n,i){function r(t,e){return t=Ma(t),e=Ma(e),d?t>e:e>t}var o=e.length;if(t.onBand&&!n&&o){var a,s,l=t.getExtent();if(1===o)e[0].coord=l[0],a=e[1]={coord:l[0]};else{var u=e[o-1].tickValue-e[0].tickValue,h=(e[o-1].coord-e[0].coord)/u;f(e,function(t){t.coord-=h/2});var c=t.scale.getExtent();s=1+c[1]-e[o-1].tickValue,a={coord:e[o-1].coord+h*s},e.push(a)}var d=l[0]>l[1];r(e[0].coord,l[0])&&(i?e[0].coord=l[0]:e.shift()),i&&r(l[0],e[0].coord)&&e.unshift({coord:l[0]}),r(l[1],a.coord)&&(i?a.coord=l[1]:e.pop()),i&&r(a.coord,l[1])&&e.push({coord:l[1]})}}function Mc(t){return this._axes[t]}function Ic(t){PM.call(this,t)}function Cc(t,e){return e.type||(e.data?"category":"value")}function Tc(t,e){return t.getCoordSysModel()===e}function Ac(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}function Dc(t,e,n,i){function r(t){return t.dim+"_"+t.index}n.getAxesOnZeroOf=function(){return o?[o]:[]};var o,a=t[e],s=n.model,l=s.get("axisLine.onZero"),u=s.get("axisLine.onZeroAxisIndex");if(l){if(null!=u)kc(a[u])&&(o=a[u]);else for(var h in a)if(a.hasOwnProperty(h)&&kc(a[h])&&!i[r(a[h])]){o=a[h];break}o&&(i[r(o)]=!0)}}function kc(t){return t&&"category"!==t.type&&"time"!==t.type&&Xh(t)}function Pc(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}function Lc(t){return p(VM,function(e){var n=t.getReferringComponents(e)[0];if(_v&&!n)throw new Error(e+' "'+A(t.get(e+"Index"),t.get(e+"Id"),0)+'" not found');return n})}function Oc(t){return"cartesian2d"===t.get("coordinateSystem")}function zc(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return tl(t,e,n[0]);if(i){for(var r=[],o=0;o<n.length;o++){var a=tl(t,e,n[o]);r.push(a)}return r.join(" ")}}function Ec(t,e,n,i,r,o){var a=n.getModel("label"),s=n.getModel("emphasis.label");Zo(t,e,a,s,{labelFetcher:r,labelDataIndex:o,defaultText:zc(r.getData(),o),isRectText:!0,autoColor:i}),Rc(t),Rc(e)}function Rc(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function Bc(t,e,n){var i=t.getArea(),r=t.getBaseAxis().isHorizontal(),o=i.x,a=i.y,s=i.width,l=i.height,u=n.get("lineStyle.width")||2;o-=u/2,a-=u/2,s+=u,l+=u,o=Math.floor(o),s=Math.round(s);var h=new d_({shape:{x:o,y:a,width:s,height:l}});return e&&(h.shape[r?"width":"height"]=0,ia(h,{shape:{width:s,height:l}},n)),h}function Nc(t,e,n){var i=t.getArea(),r=new r_({shape:{cx:Ma(t.cx,1),cy:Ma(t.cy,1),r0:Ma(i.r0,1),r:Ma(i.r,1),startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});return e&&(r.shape.endAngle=i.startAngle,ia(r,{shape:{endAngle:i.endAngle}},n)),r}function Fc(t,e,n){return t?"polar"===t.type?Nc(t,e,n):"cartesian2d"===t.type?Bc(t,e,n):null:null}function Vc(t,e){var n=t.getArea&&t.getArea();if("cartesian2d"===t.type){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}function Hc(t,e,n){n.style.text=null,na(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function Wc(t,e,n){n.style.text=null,na(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}function Gc(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function Zc(t,e,n,i,r,o,a,l){var u=e.getItemVisual(n,"color"),h=e.getItemVisual(n,"opacity"),c=e.getVisual("borderColor"),d=i.getModel("itemStyle"),f=i.getModel("emphasis.itemStyle").getBarItemStyle();l||t.setShape("r",d.get("barBorderRadius")||0),t.useStyle(s({stroke:Gc(r)?"none":c,fill:Gc(r)?"none":u,opacity:h},d.getBarItemStyle()));var p=i.getShallow("cursor");p&&t.attr("cursor",p);var g=a?r.height>0?"bottom":"top":r.width>0?"left":"right";l||Ec(t.style,f,i,u,o,n,g),Gc(r)&&(f.fill=f.stroke="none"),Vo(t,f)}function Xc(t,e){var n=t.get(XM)||0,i=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),r=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(n,i,r)}function Yc(t,e,n){var i=t.getData(),r=[],o=i.getLayout("valueAxisHorizontal")?1:0;r[1-o]=i.getLayout("valueAxisStart");var a=i.getLayout("largeDataIndices"),s=i.getLayout("barWidth"),l=t.getModel("backgroundStyle"),u=t.get("showBackground",!0);if(u){var h=i.getLayout("largeBackgroundPoints"),c=[];c[1-o]=i.getLayout("backgroundStart");var d=new QM({shape:{points:h},incremental:!!n,__startPoint:c,__baseDimIdx:o,__largeDataIndices:a,__barWidth:s,silent:!0,z2:0});qc(d,l,i),e.add(d)}var f=new QM({shape:{points:i.getLayout("largePoints")},incremental:!!n,__startPoint:r,__baseDimIdx:o,__largeDataIndices:a,__barWidth:s});e.add(f),jc(f,t,i),f.seriesIndex=t.seriesIndex,t.get("silent")||(f.on("mousedown",JM),f.on("mousemove",JM))}function Uc(t,e,n){var i=t.__baseDimIdx,r=1-i,o=t.shape.points,a=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];YM[0]=e,YM[1]=n;for(var u=YM[i],h=YM[1-i],c=u-s,d=u+s,f=0,p=o.length/2;p>f;f++){var g=2*f,v=o[g+i],m=o[g+r];if(v>=c&&d>=v&&(m>=l?h>=l&&m>=h:h>=m&&l>=h))return a[f]}return-1}function jc(t,e,n){var i=n.getVisual("borderColor")||n.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}function qc(t,e,n){var i=e.get("borderColor")||e.get("color"),r=e.getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}function $c(t,e,n){var i,r="polar"===n.type;return i=r?n.getArea():n.grid.getRect(),r?{cx:i.cx,cy:i.cy,r0:t?i.r0:e.r0,r:t?i.r:e.r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}:{x:t?e.x:i.x,y:t?i.y:e.y,width:t?e.width:i.width,height:t?i.height:e.height}}function Kc(t,e,n){var i="polar"===t.type?r_:d_;return new i({shape:$c(e,n,t),silent:!0,z2:0})}function Qc(t,e,n,i){var r,o,a=ka(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;return Pa(a-tI/2)?(o=l?"bottom":"top",r="center"):Pa(a-1.5*tI)?(o=l?"top":"bottom",r="center"):(o="middle",r=1.5*tI>a&&a>tI/2?l?"left":"right":l?"right":"left"),{rotation:a,textAlign:r,textVerticalAlign:o}}function Jc(t,e,n){if(!Kh(t.axis)){var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],n=n||[];var o=e[0],a=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],d=n[n.length-2];i===!1?(td(o),td(u)):ed(o,a)&&(i?(td(a),td(h)):(td(o),td(u))),r===!1?(td(s),td(c)):ed(l,s)&&(r?(td(l),td(d)):(td(s),td(c)))}}function td(t){t&&(t.ignore=!0)}function ed(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=ze([]);return Ne(r,r,-t.rotation),n.applyTransform(Re([],r,t.getLocalTransform())),i.applyTransform(Re([],r,e.getLocalTransform())),n.intersect(i)}}function nd(t){return"middle"===t||"center"===t}function id(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,a[1]=0,s[0]=u,s[1]=n,e&&(oe(a,a,e),oe(s,s,e));var h=new p_({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,silent:!0});o.push(h)}return o}function rd(t,e,n){var i=e.axis,r=e.getModel("axisTick");if(r.get("show")&&!i.scale.isBlank()){for(var o=r.getModel("lineStyle"),a=n.tickDirection*r.get("length"),l=i.getTicksCoords(),u=id(l,t._transform,a,s(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),h=0;h<u.length;h++)t.group.add(u[h]);return u}}function od(t,e,n){var i=e.axis,r=e.getModel("minorTick");if(r.get("show")&&!i.scale.isBlank()){var o=i.getMinorTicksCoords();if(o.length)for(var a=r.getModel("lineStyle"),l=n.tickDirection*r.get("length"),u=s(a.getLineStyle(),s(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),h=0;h<o.length;h++)for(var c=id(o[h],t._transform,l,u,"minorticks_"+h),d=0;d<c.length;d++)t.group.add(c[d])}}function ad(t,e,n){var i=e.axis,r=A(n.axisLabelShow,e.get("axisLabel.show"));if(r&&!i.scale.isBlank()){var o=e.getModel("axisLabel"),a=o.get("margin"),s=i.getViewLabels(),l=(A(n.labelRotate,o.get("rotate"))||0)*tI/180,u=rI(n.rotation,l,n.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],d=oI(e),p=e.get("triggerEvent");return f(s,function(r,s){var l=r.tickValue,f=r.formattedLabel,g=r.rawLabel,v=o;h&&h[l]&&h[l].textStyle&&(v=new ga(h[l].textStyle,o,e.ecModel));var m=v.getTextColor()||e.get("axisLine.lineStyle.color"),y=i.dataToCoord(l),x=[y,n.labelOffset+n.labelDirection*a],_=new t_({anid:"label_"+l,position:x,rotation:u.rotation,silent:d,z2:10});Yo(_.style,v,{text:f,textAlign:v.getShallow("align",!0)||u.textAlign,textVerticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||u.textVerticalAlign,textFill:"function"==typeof m?m("category"===i.type?g:"value"===i.type?l+"":l,s):m}),p&&(_.eventData=iI(e),_.eventData.targetType="axisLabel",_.eventData.value=g),t._dumbGroup.add(_),_.updateTransform(),c.push(_),t.group.add(_),_.decomposeTransform()
}),c}}function sd(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return ld(n,t,e),n.seriesInvolved&&hd(n,t),n}function ld(t,e,n){var i=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),o=r.get("link",!0)||[],a=[];aI(n.getCoordinateSystems(),function(n){function s(i,s,l){var h=l.model.getModel("axisPointer",r),d=h.get("show");if(d&&("auto"!==d||i||vd(h))){null==s&&(s=h.get("triggerTooltip")),h=i?ud(l,c,r,e,i,s):h;var f=h.get("snap"),p=md(l.model),g=s||f||"category"===l.type,v=t.axesInfo[p]={key:p,axis:l,coordSys:n,axisPointerModel:h,triggerTooltip:s,involveSeries:g,snap:f,useHandle:vd(h),seriesModels:[]};u[p]=v,t.seriesInvolved|=g;var m=cd(o,l);if(null!=m){var y=a[m]||(a[m]={axesInfo:{}});y.axesInfo[p]=v,y.mapper=o[m].mapper,v.linkGroup=y}}}if(n.axisPointerEnabled){var l=md(n.model),u=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=n;var h=n.model,c=h.getModel("tooltip",i);if(aI(n.getAxes(),sI(s,!1,null)),n.getTooltipAxes&&i&&c.get("show")){var d="axis"===c.get("trigger"),f="cross"===c.get("axisPointer.type"),p=n.getTooltipAxes(c.get("axisPointer.axis"));(d||f)&&aI(p.baseAxes,sI(s,f?"cross":!0,d)),f&&aI(p.otherAxes,sI(s,"cross",!1))}}})}function ud(t,e,n,r,o,a){var l=e.getModel("axisPointer"),u={};aI(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){u[t]=i(l.get(t))}),u.snap="category"!==t.type&&!!a,"cross"===l.get("type")&&(u.type="line");var h=u.label||(u.label={});if(null==h.show&&(h.show=!1),"cross"===o){var c=l.get("label.show");if(h.show=null!=c?c:!0,!a){var d=u.lineStyle=l.get("crossStyle");d&&s(h,d.textStyle)}}return t.model.getModel("axisPointer",new ga(u,n,r))}function hd(t,e){e.eachSeries(function(e){var n=e.coordinateSystem,i=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);n&&"none"!==i&&i!==!1&&"item"!==i&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&aI(t.coordSysAxesInfo[md(n.model)],function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function cd(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(dd(o[i+"AxisId"],n.id)||dd(o[i+"AxisIndex"],n.componentIndex)||dd(o[i+"AxisName"],n.name))return r}}function dd(t,e){return"all"===t||_(t)&&u(t,e)>=0||t===e}function fd(t){var e=pd(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=vd(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function pd(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[md(t)]}function gd(t){var e=pd(t);return e&&e.axisPointerModel}function vd(t){return!!t.get("handle.show")}function md(t){return t.type+"||"+t.id}function yd(t,e,n,i,r,o){var a=lI.getAxisPointerClass(t.axisPointerClass);if(a){var s=gd(e);s?(t._axisPointer||(t._axisPointer=new a)).render(e,s,i,o):xd(t,i)}}function xd(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}function _d(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,o={},a=r.getAxesOnZeroOf()[0],s=r.position,l=a?"onZero":s,u=r.dim,h=i.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(a){var g=a.toGlobalCoord(a.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}o.position=["y"===u?p[d[l]]:c[0],"x"===u?p[d[l]]:c[3]],o.rotation=Math.PI/2*("x"===u?0:1);var v={top:-1,bottom:1,left:-1,right:1};o.labelDirection=o.tickDirection=o.nameDirection=v[s],o.labelOffset=a?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),A(n.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var m=e.get("axisLabel.rotate");return o.labelRotate="top"===l?-m:m,o.z2=1,o}function wd(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),l=a.get("color"),u=i.coordinateSystem.getRect(),h=r.getTicksCoords({tickModel:o,clamp:!0});if(h.length){var c=l.length,d=t.__splitAreaColors,f=N(),p=0;if(d)for(var g=0;g<h.length;g++){var v=d.get(h[g].tickValue);if(null!=v){p=(v+(c-1)*g)%c;break}}var m=r.toGlobalCoord(h[0].coord),y=a.getAreaStyle();l=_(l)?l:[l];for(var g=1;g<h.length;g++){var x,w,b,S,M=r.toGlobalCoord(h[g].coord);r.isHorizontal()?(x=m,w=u.y,b=M-x,S=u.height,m=x+b):(x=u.x,w=m,b=u.width,S=M-w,m=w+S);var I=h[g-1].tickValue;null!=I&&f.set(I,p),e.add(new d_({anid:null!=I?"area_"+I:null,shape:{x:x,y:w,width:b,height:S},style:s({fill:l[p]},y),silent:!0})),p=(p+1)%c}t.__splitAreaColors=f}}}function bd(t){t.__splitAreaColors=null}function Sd(t,e,n){Nm.call(this),this.updateData(t,e,n)}function Md(t){return[t[0]/2,t[1]/2]}function Id(t,e){this.parent.drift(t,e)}function Cd(t,e){if(!this.incremental&&!this.useHoverLayer)if("emphasis"===e){var n=this.__symbolOriginalScale,i=n[1]/n[0],r={scale:[Math.max(1.1*n[0],n[0]+3),Math.max(1.1*n[1],n[1]+3*i)]};this.animateTo(r,400,"elasticOut")}else"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}function Td(t){this.group=new Nm,this._symbolCtor=t||Sd}function Ad(t,e,n,i){return!(!e||isNaN(e[0])||isNaN(e[1])||i.isIgnore&&i.isIgnore(n)||i.clipShape&&!i.clipShape.contain(e[0],e[1])||"none"===t.getItemVisual(n,"symbol"))}function Dd(t){return null==t||S(t)||(t={isIgnore:t}),t||{}}function kd(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function Pd(t,e,n){var i,r=t.getBaseAxis(),o=t.getOtherAxis(r),a=Ld(o,n),s=r.dim,l=o.dim,u=e.mapDimension(l),h=e.mapDimension(s),c="x"===l||"radius"===l?1:0,d=p(t.dimensions,function(t){return e.mapDimension(t)}),f=e.getCalculationInfo("stackResultDimension");return(i|=vh(e,d[0]))&&(d[0]=f),(i|=vh(e,d[1]))&&(d[1]=f),{dataDimsForPoint:d,valueStart:a,valueAxisDim:l,baseAxisDim:s,stacked:!!i,valueDim:u,baseDim:h,baseDataOffset:c,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Ld(t,e){var n=0,i=t.scale.getExtent();return"start"===e?n=i[0]:"end"===e?n=i[1]:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]),n}function Od(t,e,n,i){var r=0/0;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}function zd(t,e){var n=[];return e.diff(t).add(function(t){n.push({cmd:"+",idx:t})}).update(function(t,e){n.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){n.push({cmd:"-",idx:t})}).execute(),n}function Ed(t){return isNaN(t[0])||isNaN(t[1])}function Rd(t,e,n,i,r,o,a,s,l,u){return"none"!==u&&u?Bd.apply(this,arguments):Nd.apply(this,arguments)}function Bd(t,e,n,i,r,o,a,s,l,u,h){for(var c=0,d=n,f=0;i>f;f++){var p=e[d];if(d>=r||0>d)break;if(Ed(p)){if(h){d+=o;continue}break}if(d===n)t[o>0?"moveTo":"lineTo"](p[0],p[1]);else if(l>0){var g=e[c],v="y"===u?1:0,m=(p[v]-g[v])*l;MI(CI,g),CI[v]=g[v]+m,MI(TI,p),TI[v]=p[v]-m,t.bezierCurveTo(CI[0],CI[1],TI[0],TI[1],p[0],p[1])}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}function Nd(t,e,n,i,r,o,a,s,l,u,h){for(var c=0,d=n,f=0;i>f;f++){var p=e[d];if(d>=r||0>d)break;if(Ed(p)){if(h){d+=o;continue}break}if(d===n)t[o>0?"moveTo":"lineTo"](p[0],p[1]),MI(CI,p);else if(l>0){var g=d+o,v=e[g];if(h)for(;v&&Ed(e[g]);)g+=o,v=e[g];var m=.5,y=e[c],v=e[g];if(!v||Ed(v))MI(TI,p);else{Ed(v)&&!h&&(v=p),U(II,v,y);var x,_;if("x"===u||"y"===u){var w="x"===u?0:1;x=Math.abs(p[w]-y[w]),_=Math.abs(p[w]-v[w])}else x=Wv(p,y),_=Wv(p,v);m=_/(_+x),SI(TI,p,II,-l*(1-m))}wI(CI,CI,s),bI(CI,CI,a),wI(TI,TI,s),bI(TI,TI,a),t.bezierCurveTo(CI[0],CI[1],TI[0],TI[1],p[0],p[1]),SI(CI,p,II,l*m)}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}function Fd(t,e){var n=[1/0,1/0],i=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var o=t[r];o[0]<n[0]&&(n[0]=o[0]),o[1]<n[1]&&(n[1]=o[1]),o[0]>i[0]&&(i[0]=o[0]),o[1]>i[1]&&(i[1]=o[1])}return{min:e?n:i,max:e?i:n}}function Vd(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return!0}}function Hd(t,e){var n=[],i=[],r=[],o=[];return Br(t,n,i),Br(e,r,o),Math.max(Math.abs(n[0]-r[0]),Math.abs(n[1]-r[1]),Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]))}function Wd(t){return"number"==typeof t?t:t?.5:0}function Gd(t,e,n){if(!n.valueDim)return[];for(var i=[],r=0,o=e.count();o>r;r++)i.push(Od(n,t,e,r));return i}function Zd(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,o=[],a=0;a<t.length-1;a++){var s=t[a+1],l=t[a];o.push(l);var u=[];switch(n){case"end":u[r]=s[r],u[1-r]=l[1-r],o.push(u);break;case"middle":var h=(l[r]+s[r])/2,c=[];u[r]=c[r]=h,u[1-r]=l[1-r],c[1-r]=s[1-r],o.push(u),o.push(c);break;default:u[r]=l[r],u[1-r]=s[1-r],o.push(u)}}return t[a]&&o.push(t[a]),o}function Xd(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()){if("cartesian2d"!==e.type)return void(_v&&console.warn("Visual map on line style is only supported on cartesian2d."));for(var i,r,o=n.length-1;o>=0;o--){var a=n[o].dimension,s=t.dimensions[a],l=t.getDimensionInfo(s);if(i=l&&l.coordDim,"x"===i||"y"===i){r=n[o];break}}if(!r)return void(_v&&console.warn("Visual map on line style only support x or y dimension."));var u=e.getAxis(i),h=p(r.stops,function(t){return{coord:u.toGlobalCoord(u.dataToCoord(t.value)),color:t.color}}),c=h.length,d=r.outerColors.slice();c&&h[0].coord>h[c-1].coord&&(h.reverse(),d.reverse());var g=10,v=h[0].coord-g,m=h[c-1].coord+g,y=m-v;if(.001>y)return"transparent";f(h,function(t){t.offset=(t.coord-v)/y}),h.push({offset:c?h[c-1].offset:.5,color:d[1]||"transparent"}),h.unshift({offset:c?h[0].offset:.5,color:d[0]||"transparent"});var x=new __(0,0,0,0,h,!0);return x[i]=v,x[i+"2"]=m,x}}function Yd(t,e,n){var i=t.get("showAllSymbol"),r="auto"===i;if(!i||r){var o=n.getAxesByScale("ordinal")[0];if(o&&(!r||!Ud(o,e))){var a=e.mapDimension(o.dim),s={};return f(o.getViewLabels(),function(t){s[t.tickValue]=1}),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function Ud(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;r>a;a+=o)if(1.5*Sd.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return!1;return!0}function jd(t,e,n){if("cartesian2d"===t.type){var i=t.getBaseAxis().isHorizontal(),r=Bc(t,e,n);if(!n.get("clip",!0)){var o=r.shape,a=Math.max(o.width,o.height);i?(o.y-=a,o.height+=2*a):(o.x-=a,o.width+=2*a)}return r}return Nc(t,e,n)}function qd(t,e){this.getAllNames=function(){var t=e();return t.mapArray(t.getName)},this.containName=function(t){var n=e();return n.indexOfName(t)>=0},this.indexOfName=function(e){var n=t();return n.indexOfName(e)},this.getItemVisual=function(e,n){var i=t();return i.getItemVisual(e,n)}}function $d(t,e,n,i){var r=e.getData(),o=this.dataIndex,a=r.getName(o),s=e.get("selectedOffset");i.dispatchAction({type:"pieToggleSelect",from:t,name:a,seriesId:e.id}),r.each(function(t){Kd(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,n)})}function Kd(t,e,n,i,r){var o=(e.startAngle+e.endAngle)/2,a=Math.cos(o),s=Math.sin(o),l=n?i:0,u=[a*l,s*l];r?t.animate().when(200,{position:u}).start("bounceOut"):t.attr("position",u)}function Qd(t,e){Nm.call(this);var n=new r_({z2:2}),i=new u_,r=new t_;this.add(n),this.add(i),this.add(r),this.updateData(t,e,!0)}function Jd(t,e,n,i,r,o,a,s,l,u){function h(e,n,i){for(var r=e;n>r&&!(t[r].y+i>l+a);r++)if(t[r].y+=i,r>e&&n>r+1&&t[r+1].y>t[r].y+t[r].height)return void c(r,i/2);c(n-1,i/2)}function c(e,n){for(var i=e;i>=0&&!(t[i].y-n<l)&&(t[i].y-=n,!(i>0&&t[i].y>t[i-1].y+t[i-1].height));i--);}function d(t,e,n,i,r,o){for(var a=o>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t.length;l>s;s++)if("none"===t[s].labelAlignTo){var u=Math.abs(t[s].y-i),h=t[s].len,c=t[s].len2,d=r+h>u?Math.sqrt((r+h+c)*(r+h+c)-u*u):Math.abs(t[s].x-n);e&&d>=a&&(d=a-10),!e&&a>=d&&(d=a+10),t[s].x=n+d*o,a=d}}t.sort(function(t,e){return t.y-e.y});for(var f,p=0,g=t.length,v=[],m=[],y=0;g>y;y++){if("outer"===t[y].position&&"labelLine"===t[y].labelAlignTo){var x=t[y].x-u;t[y].linePoints[1][0]+=x,t[y].x=u}f=t[y].y-p,0>f&&h(y,g,-f,r),p=t[y].y+t[y].height}0>a-p&&c(g-1,p-a);for(var y=0;g>y;y++)t[y].y>=n?m.push(t[y]):v.push(t[y]);d(v,!1,e,n,i,r),d(m,!0,e,n,i,r)}function tf(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,d=0;d<t.length;d++)ef(t[d])||(t[d].x<e?(h=Math.min(h,t[d].x),l.push(t[d])):(c=Math.max(c,t[d].x),u.push(t[d])));Jd(u,e,n,i,1,r,o,a,s,c),Jd(l,e,n,i,-1,r,o,a,s,h);for(var d=0;d<t.length;d++){var f=t[d];if(!ef(f)){var p=f.linePoints;if(p){var g,v="edge"===f.labelAlignTo,m=f.textRect.width;g=v?f.x<e?p[2][0]-f.labelDistance-a-f.labelMargin:a+r-f.labelMargin-p[2][0]-f.labelDistance:f.x<e?f.x-a-f.bleedMargin:a+r-f.x-f.bleedMargin,g<f.textRect.width&&(f.text=Jn(f.text,g,f.font),"edge"===f.labelAlignTo&&(m=Yn(f.text,f.font)));var y=p[1][0]-p[2][0];v?p[2][0]=f.x<e?a+f.labelMargin+m+f.labelDistance:a+r-f.labelMargin-m-f.labelDistance:(p[2][0]=f.x<e?f.x+f.labelDistance:f.x-f.labelDistance,p[1][0]=p[2][0]+y),p[1][1]=p[2][1]=f.y}}}}function ef(t){return"center"===t.position}function nf(t,e){return Qa(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function rf(t,e,n){var i,r={},o="toggleSelected"===t;return n.eachComponent("legend",function(n){o&&null!=i?n[i?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?n[t]():(n[t](e.name),i=n.isSelected(e.name));var a=n.getData();f(a,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var i=n.isSelected(e);r[e]=r.hasOwnProperty(e)?r[e]&&i:i}})}),"allSelect"===t||"inverseSelect"===t?{selected:r}:{name:e.name,selected:r}}function of(t,e,n){var i=e.getBoxLayoutParams(),r=e.get("padding"),o={width:n.getWidth(),height:n.getHeight()},a=Qa(i,o,r);ow(e.get("orient"),t,e.get("itemGap"),a.width,a.height),Ja(t,i,o,r)}function af(t,e){var n=q_(e.get("padding")),i=e.getItemStyle(["color","opacity"]);i.fill=e.get("backgroundColor");var t=new d_({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1});return t}function sf(t,e,n,i,r,o){var a;return"line"!==e&&e.indexOf("empty")<0?(a=n.getItemStyle(),t.style.stroke=i,o||(a.stroke=r)):a=n.getItemStyle(["borderWidth","borderColor"]),t.setStyle(a)}function lf(t,e,n,i){hf(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),uf(t,e,n,i)}function uf(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function hf(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}function cf(t,e,n){var i=t.getOrient(),r=[1,1];r[i.index]=0,ts(e,n,{type:"box",ignoreSize:r})}function df(t,e,n,i,r){var o=t.axis;if(!o.scale.isBlank()&&o.containData(e)){if(!t.involveSeries)return void n.showPointer(t,e);var s=ff(e,t),l=s.payloadBatch,u=s.snapToValue;l[0]&&null==r.seriesIndex&&a(r,l[0]),!i&&t.snap&&o.containData(u)&&null!=u&&(e=u),n.showPointer(t,e,l,r),n.showTooltip(t,s,u)}}function ff(t,e){var n=e.axis,i=n.dim,r=t,o=[],a=Number.MAX_VALUE,s=-1;return sC(e.seriesModels,function(e){var l,u,h=e.getData().mapDimension(i,!0);if(e.getAxisTooltipData){var c=e.getAxisTooltipData(h,t,n);u=c.dataIndices,l=c.nestestValue}else{if(u=e.getData().indicesOfNearest(h[0],t,"category"===n.type?.5:null),!u.length)return;l=e.getData().get(h[0],u[0])}if(null!=l&&isFinite(l)){var d=t-l,f=Math.abs(d);a>=f&&((a>f||d>=0&&0>s)&&(a=f,s=d,r=l,o.length=0),sC(u,function(t){o.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:o,snapToValue:r}}function pf(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function gf(t,e,n,i){var r=n.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,u=md(l),h=t.map[u];h||(h=t.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:i,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function vf(t,e,n){var i=n.axesInfo=[];sC(e,function(e,n){var r=e.axisPointerModel.option,o=t[n];o?(!e.useHandle&&(r.status="show"),r.value=o.value,r.seriesDataIndices=(o.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function mf(t,e,n,i){if(wf(e)||!t.list.length)return void i({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function yf(t,e,n){var i=n.getZr(),r="axisPointerLastHighlights",o=uC(i)[r]||{},a=uC(i)[r]={};sC(t,function(t){var e=t.axisPointerModel.option;"show"===e.status&&sC(e.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;a[e]=t})});var s=[],l=[];f(o,function(t,e){!a[e]&&l.push(t)}),f(a,function(t,e){!o[e]&&s.push(t)}),l.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function xf(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}function _f(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function wf(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function bf(t,e,n){if(!Mv.node){var i=e.getZr();cC(i).records||(cC(i).records={}),Sf(i,e);var r=cC(i).records[t]||(cC(i).records[t]={});r.handler=n}}function Sf(t,e){function n(n,i){t.on(n,function(n){var r=Tf(e);dC(cC(t).records,function(t){t&&i(t,n,r.dispatchAction)}),Mf(r.pendings,e)})}cC(t).initialized||(cC(t).initialized=!0,n("click",x(Cf,"click")),n("mousemove",x(Cf,"mousemove")),n("globalout",If))}function Mf(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}function If(t,e,n){t.handler("leave",null,n)}function Cf(t,e,n,i){e.handler(t,n,i)}function Tf(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}function Af(t,e){if(!Mv.node){var n=e.getZr(),i=(cC(n).records||{})[t];i&&(cC(n).records[t]=null)}}function Df(){}function kf(t,e,n,i){Pf(pC(n).lastProp,i)||(pC(n).lastProp=i,e?na(n,i,t):(n.stopAnimation(),n.attr(i)))}function Pf(t,e){if(S(t)&&S(e)){var n=!0;return f(e,function(e,i){n=n&&Pf(t[i],e)}),!!n}return t===e}function Lf(t,e){t[e.get("label.show")?"show":"hide"]()}function Of(t){return{position:t.position.slice(),rotation:t.rotation||0}}function zf(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function Ef(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle(),e.fill=null):"shadow"===n&&(e=i.getAreaStyle(),e.stroke=null),e}function Rf(t,e,n,i,r){var o=n.get("value"),a=Nf(o,e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),s=n.getModel("label"),l=q_(s.get("padding")||0),u=s.getFont(),h=Un(a,u),c=r.position,d=h.width+l[1]+l[3],f=h.height+l[0]+l[2],p=r.align;"right"===p&&(c[0]-=d),"center"===p&&(c[0]-=d/2);var g=r.verticalAlign;"bottom"===g&&(c[1]-=f),"middle"===g&&(c[1]-=f/2),Bf(c,d,f,i);var v=s.get("backgroundColor");v&&"auto"!==v||(v=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:d,height:f,r:s.get("borderRadius")},position:c.slice(),style:{text:a,textFont:u,textFill:s.getTextColor(),textPosition:"inside",textPadding:l,fill:v,stroke:s.get("borderColor")||"transparent",lineWidth:s.get("borderWidth")||0,shadowBlur:s.get("shadowBlur"),shadowColor:s.get("shadowColor"),shadowOffsetX:s.get("shadowOffsetX"),shadowOffsetY:s.get("shadowOffsetY")},z2:10}}function Bf(t,e,n,i){var r=i.getWidth(),o=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,o)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function Nf(t,e,n,i,r){t=e.scale.parse(t);var o=e.scale.getLabel(t,{precision:r.precision}),a=r.formatter;if(a){var s={value:Uh(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};f(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&s.seriesData.push(r)}),b(a)?o=a.replace("{value}",o):w(a)&&(o=a(s))}return o}function Ff(t,e,n){var i=Oe();return Ne(i,i,n.rotation),Be(i,i,n.position),oa([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function Vf(t,e,n,i,r,o){var a=eI.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get("label.margin"),Rf(e,i,r,o,{position:Ff(i.axis,t,n),align:a.textAlign,verticalAlign:a.textVerticalAlign})}function Hf(t,e,n){return n=n||0,{x1:t[n],y1:t[1-n],x2:e[n],y2:e[1-n]}}function Wf(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}function Gf(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}function Zf(t){return"x"===t.dim?0:1}function Xf(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",n="left "+t+"s "+e+",top "+t+"s "+e;return p(wC,function(t){return t+"transition:"+n}).join(";")}function Yf(t){var e=[],n=t.get("fontSize"),i=t.getTextColor();i&&e.push("color:"+i),e.push("font:"+t.getFont());var r=t.get("lineHeight");null==r&&(r=Math.round(3*n/2)),n&&e.push("line-height:"+r+"px");var o=t.get("textShadowColor"),a=t.get("textShadowBlur")||0,s=t.get("textShadowOffsetX")||0,l=t.get("textShadowOffsetY")||0;return a&&e.push("text-shadow:"+s+"px "+l+"px "+a+"px "+o),xC(["decoration","align"],function(n){var i=t.get(n);i&&e.push("text-"+n+":"+i)}),e.join(";")}function Uf(t){var e=[],n=t.get("transitionDuration"),i=t.get("backgroundColor"),r=t.getModel("textStyle"),o=t.get("padding");return n&&e.push(Xf(n)),i&&(Mv.canvasSupported?e.push("background-Color:"+i):(e.push("background-Color:#"+on(i)),e.push("filter:alpha(opacity=70)"))),xC(["width","color","radius"],function(n){var i="border-"+n,r=_C(i),o=t.get(r);null!=o&&e.push(i+":"+o+("color"===n?"":"px"))}),e.push(Yf(r)),null!=o&&e.push("padding:"+q_(o).join("px ")+"px"),e.join(";")+";"}function jf(t,e,n,i,r){var o=e&&e.painter;if(n){var a=o&&o.getViewportRoot();a&&pe(t,a,document.body,i,r)}else{t[0]=i,t[1]=r;var s=o&&o.getViewportRootOffset();s&&(t[0]+=s.offsetLeft,t[1]+=s.offsetTop)}t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function qf(t,e,n){if(Mv.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var r=this._zr=e.getZr(),o=this._appendToBody=n&&n.appendToBody;this._styleCoord=[0,0,0,0],jf(this._styleCoord,r,o,e.getWidth()/2,e.getHeight()/2),o?document.body.appendChild(i):t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var a=this;i.onmouseenter=function(){a._enterable&&(clearTimeout(a._hideTimeout),a._show=!0),a._inContent=!0},i.onmousemove=function(t){if(t=t||window.event,!a._enterable){var e=r.handler,n=r.painter.getViewportRoot();be(n,t,!0),e.dispatch("mousemove",t)}},i.onmouseleave=function(){a._enterable&&a._show&&a.hideLater(a._hideDelay),a._inContent=!1}}function $f(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function Kf(t){var e=this._zr=t.getZr();this._styleCoord=[0,0,0,0],$f(this._styleCoord,e,t.getWidth()/2,t.getHeight()/2),this._show=!1,this._hideTimeout}function Qf(t){for(var e=t.pop();t.length;){var n=t.pop();n&&(ga.isInstance(n)&&(n=n.get("tooltip",!0)),"string"==typeof n&&(n={formatter:n}),e=new ga(n,e,e.ecModel))}return e}function Jf(t,e){return t.dispatchAction||y(e.dispatchAction,e)}function tp(t,e,n,i,r,o,a){var s=n.getOuterSize(),l=s.width,u=s.height;return null!=o&&(t+l+o>i?t-=l+o:t+=o),null!=a&&(e+u+a>r?e-=u+a:e+=a),[t,e]}function ep(t,e,n,i,r){var o=n.getOuterSize(),a=o.width,s=o.height;return t=Math.min(t+a,i)-a,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function np(t,e,n){var i=n[0],r=n[1],o=5,a=0,s=0,l=e.width,u=e.height;switch(t){case"inside":a=e.x+l/2-i/2,s=e.y+u/2-r/2;break;case"top":a=e.x+l/2-i/2,s=e.y-r-o;break;case"bottom":a=e.x+l/2-i/2,s=e.y+u+o;break;case"left":a=e.x-i-o,s=e.y+u/2-r/2;break;case"right":a=e.x+l+o,s=e.y+u/2-r/2}return[a,s]}function ip(t){return"center"===t||"middle"===t}function rp(t){var e=t.type,n={number:"value",time:"time"};if(n[e]&&(t.axisType=n[e],delete t.type),op(t),ap(t,"controlPosition")){var i=t.controlStyle||(t.controlStyle={});ap(i,"position")||(i.position=t.controlPosition),"none"!==i.position||ap(i,"show")||(i.show=!1,delete i.position),delete t.controlPosition}f(t.data||[],function(t){S(t)&&!_(t)&&(!ap(t,"value")&&ap(t,"name")&&(t.value=t.name),op(t))})}function op(t){var e=t.itemStyle||(t.itemStyle={}),n=e.emphasis||(e.emphasis={}),i=t.label||t.label||{},r=i.normal||(i.normal={}),o={normal:1,emphasis:1};f(i,function(t,e){o[e]||ap(r,e)||(r[e]=t)}),n.label&&!ap(i,"emphasis")&&(i.emphasis=n.label,delete n.label)}function ap(t,e){return t.hasOwnProperty(e)}function sp(t,e){return Qa(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},t.get("padding"))}function lp(t,e,n,i){var r=i.style,o=ha(t.get(e),i||{},new Tn(n[0],n[1],n[2],n[3]));return r&&o.setStyle(r),o}function up(t,e,n,i,o,a){var s=e.get("color");if(o)o.setColor(s),n.add(o),a&&a.onUpdate(o);else{var l=t.get("symbol");o=Jh(l,-1,-1,2,2,s),o.setStyle("strokeNoScale",!0),n.add(o),a&&a.onCreate(o)}var u=e.getItemStyle(["color","symbol","symbolSize"]);o.setStyle(u),i=r({rectHover:!0,z2:100},i,!0);var h=t.get("symbolSize");h=h instanceof Array?h.slice():[+h,+h],h[0]/=2,h[1]/=2,i.scale=h;var c=t.get("symbolOffset");if(c){var d=i.position=i.position||[0,0];d[0]+=Sa(c[0],h[0]),d[1]+=Sa(c[1],h[1])}var f=t.get("symbolRotate");return i.rotation=(f||0)*Math.PI/180||0,o.attr(i),o.updateTransform(),o}function hp(t,e,n,i,r){if(!t.dragging){var o=i.getModel("checkpointStyle"),a=n.dataToCoord(i.getData().get(["value"],e));r||!o.get("animation",!0)?t.attr({position:[a,0]}):(t.stopAnimation(!0),t.animateTo({position:[a,0]},o.get("animationDuration",!0),o.get("animationEasing",!0)))}}function cp(t){return u(RC,t)>=0}function dp(t,e){t=t.slice();var n=p(t,Ua);e=(e||[]).slice();var i=p(e,Ua);return function(r,o){f(t,function(t,a){for(var s={name:t,capital:n[a]},l=0;l<e.length;l++)s[e[l]]=t+i[l];r.call(o,s)})}}function fp(t,e,n){function i(t,e){return u(e.nodes,t)>=0}function r(t,i){var r=!1;return e(function(e){f(n(t,e)||[],function(t){i.records[e.name][t]&&(r=!0)})}),r}function o(t,i){i.nodes.push(t),e(function(e){f(n(t,e)||[],function(t){i.records[e.name][t]=!0})})}return function(n){function a(t){!i(t,s)&&r(t,s)&&(o(t,s),l=!0)}var s={nodes:[],records:{}};if(e(function(t){s.records[t.name]={}}),!n)return s;o(n,s);var l;do l=!1,t(a);while(l);return s}}function pp(t,e){var n=t[e]-t[1-e];return{span:Math.abs(n),sign:n>0?-1:0>n?1:e?-1:1}}function gp(t,e){return Math.min(null!=e[1]?e[1]:1/0,Math.max(null!=e[0]?e[0]:-1/0,t))}function vp(t,e,n){var i=[1/0,-1/0];return FC(n,function(t){var n=t.getData();n&&FC(n.mapDimension(e,!0),function(t){var e=n.getApproximateExtent(t);e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1])})}),i[1]<i[0]&&(i=[0/0,0/0]),mp(t,i),i}function mp(t,e){var n=t.getAxisModel(),i=n.getMin(!0),r="category"===n.get("type"),o=r&&n.getCategories().length;null!=i&&"dataMin"!==i&&"function"!=typeof i?e[0]=i:r&&(e[0]=o>0?0:0/0);var a=n.getMax(!0);return null!=a&&"dataMax"!==a&&"function"!=typeof a?e[1]=a:r&&(e[1]=o>0?o-1:0/0),n.get("scale",!0)||(e[0]>0&&(e[0]=0),e[1]<0&&(e[1]=0)),e}function yp(t,e){var n=t.getAxisModel(),i=t._percentWindow,r=t._valueWindow;if(i){var o=Aa(r,[0,500]);o=Math.min(o,20);var a=e||0===i[0]&&100===i[1];n.setRange(a?null:+r[0].toFixed(o),a?null:+r[1].toFixed(o))}}function xp(t){var e=t._minMaxSpan={},n=t._dataZoomModel,i=t._dataExtent;FC(["min","max"],function(r){var o=n.get(r+"Span"),a=n.get(r+"ValueSpan");null!=a&&(a=t.getAxisModel().axis.scale.parse(a)),null!=a?o=ba(i[0]+a,i,[0,100],!0):null!=o&&(a=ba(o,[0,100],i,!0)-i[0]),e[r+"Span"]=o,e[r+"ValueSpan"]=a})}function _p(t){var e={};return WC(["start","end","startValue","endValue","throttle"],function(n){t.hasOwnProperty(n)&&(e[n]=t[n])}),e}function wp(t,e){var n=t._rangePropMode,i=t.get("rangeMode");WC([["start","startValue"],["end","endValue"]],function(t,r){var o=null!=e[t[0]],a=null!=e[t[1]];o&&!a?n[r]="percent":!o&&a?n[r]="value":i?n[r]=i[r]:o&&(n[r]="percent")})}function bp(t){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[t]}function Sp(t){return"vertical"===t?"ns-resize":"ew-resize"}function Mp(t,e,n){var i=Tp(t);i[e]=n}function Ip(t,e,n){var i=Tp(t),r=i[e];r===n&&(i[e]=null)}function Cp(t,e){return!!Tp(t)[e]}function Tp(t){return t[oT]||(t[oT]={})}function Ap(t){this.pointerChecker,this._zr=t,this._opt={};var e=y,n=e(Dp,this),r=e(kp,this),o=e(Pp,this),a=e(Lp,this),l=e(Op,this);Yv.call(this),this.setPointerChecker=function(t){this.pointerChecker=t},this.enable=function(e,u){this.disable(),this._opt=s(i(u)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),null==e&&(e=!0),(e===!0||"move"===e||"pan"===e)&&(t.on("mousedown",n),t.on("mousemove",r),t.on("mouseup",o)),(e===!0||"scale"===e||"zoom"===e)&&(t.on("mousewheel",a),t.on("pinch",l))},this.disable=function(){t.off("mousedown",n),t.off("mousemove",r),t.off("mouseup",o),t.off("mousewheel",a),t.off("pinch",l)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function Dp(t){if(!(Ie(t)||t.target&&t.target.draggable)){var e=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,e,n)&&(this._x=e,this._y=n,this._dragging=!0)}}function kp(t){if(this._dragging&&Rp("moveOnMouseMove",t,this._opt)&&"pinch"!==t.gestureEvent&&!Cp(this._zr,"globalPan")){var e=t.offsetX,n=t.offsetY,i=this._x,r=this._y,o=e-i,a=n-r;this._x=e,this._y=n,this._opt.preventDefaultMouseMove&&Jv(t.event),Ep(this,"pan","moveOnMouseMove",t,{dx:o,dy:a,oldX:i,oldY:r,newX:e,newY:n})}}function Pp(t){Ie(t)||(this._dragging=!1)}function Lp(t){var e=Rp("zoomOnMouseWheel",t,this._opt),n=Rp("moveOnMouseWheel",t,this._opt),i=t.wheelDelta,r=Math.abs(i),o=t.offsetX,a=t.offsetY;if(0!==i&&(e||n)){if(e){var s=r>3?1.4:r>1?1.2:1.1,l=i>0?s:1/s;zp(this,"zoom","zoomOnMouseWheel",t,{scale:l,originX:o,originY:a})}if(n){var u=Math.abs(i),h=(i>0?1:-1)*(u>3?.4:u>1?.15:.05);zp(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:h,originX:o,originY:a})}}}function Op(t){if(!Cp(this._zr,"globalPan")){var e=t.pinchScale>1?1.1:1/1.1;zp(this,"zoom",null,t,{scale:e,originX:t.pinchX,originY:t.pinchY})}}function zp(t,e,n,i,r){t.pointerChecker&&t.pointerChecker(i,r.originX,r.originY)&&(Jv(i.event),Ep(t,e,n,i,r))}function Ep(t,e,n,i,r){r.isAvailableBehavior=y(Rp,null,n,i),t.trigger(e,r)}function Rp(t,e,n){var i=n[t];return!t||i&&(!b(i)||e.event[i+"Key"])}function Bp(t,e){var n=Vp(t),i=e.dataZoomId,r=e.coordId;f(n,function(t){var n=t.dataZoomInfos;n[i]&&u(e.allCoordIds,r)<0&&(delete n[i],t.count--)}),Wp(n);var o=n[r];o||(o=n[r]={coordId:r,dataZoomInfos:{},count:0},o.controller=Hp(t,o),o.dispatchAction=x(Gp,t)),!o.dataZoomInfos[i]&&o.count++,o.dataZoomInfos[i]=e;var a=Zp(o.dataZoomInfos);o.controller.enable(a.controlType,a.opt),o.controller.setPointerChecker(e.containsPoint),_l(o,"dispatchAction",e.dataZoomModel.get("throttle",!0),"fixRate")
}function Np(t,e){var n=Vp(t);f(n,function(t){t.controller.dispose();var n=t.dataZoomInfos;n[e]&&(delete n[e],t.count--)}),Wp(n)}function Fp(t){return t.type+"\x00_"+t.id}function Vp(t){var e=t.getZr();return e[aT]||(e[aT]={})}function Hp(t,e){var n=new Ap(t.getZr());return f(["pan","zoom","scrollMove"],function(t){n.on(t,function(n){var i=[];f(e.dataZoomInfos,function(r){if(n.isAvailableBehavior(r.dataZoomModel.option)){var o=(r.getRange||{})[t],a=o&&o(e.controller,n);!r.dataZoomModel.get("disabled",!0)&&a&&i.push({dataZoomId:r.dataZoomId,start:a[0],end:a[1]})}}),i.length&&e.dispatchAction(i)})}),n}function Wp(t){f(t,function(e,n){e.count||(e.controller.dispose(),delete t[n])})}function Gp(t,e){t.dispatchAction({type:"dataZoom",batch:e})}function Zp(t){var e,n="type_",i={type_true:2,type_move:1,type_false:0,type_undefined:-1},r=!0;return f(t,function(t){var o=t.dataZoomModel,a=o.get("disabled",!0)?!1:o.get("zoomLock",!0)?"move":!0;i[n+a]>i[n+e]&&(e=a),r&=o.get("preventDefaultMouseMove",!0)}),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!r}}}function Xp(t){return function(e,n,i,r){var o=this._range,a=o.slice(),s=e.axisModels[0];if(s){var l=t(a,s,e,n,i,r);return NC(l,a,[0,100],"all"),this._range=a,o[0]!==a[0]||o[1]!==a[1]?a:void 0}}}function Yp(t,e){cT[t]=e}function Up(t){return cT[t]}function jp(t){return 0===t.indexOf("my")}function qp(t){this.model=t}function $p(t){this.model=t}function Kp(t){var e={},n=[],i=[];return t.eachRawSeries(function(t){var r=t.coordinateSystem;if(!r||"cartesian2d"!==r.type&&"polar"!==r.type)n.push(t);else{var o=r.getBaseAxis();if("category"===o.type){var a=o.dim+"_"+o.index;e[a]||(e[a]={categoryAxis:o,valueAxis:r.getOtherAxis(o),series:[]},i.push({axisDim:o.dim,axisIndex:o.index})),e[a].series.push(t)}else n.push(t)}}),{seriesGroupByCategoryAxis:e,other:n,meta:i}}function Qp(t){var e=[];return f(t,function(t){var n=t.categoryAxis,i=t.valueAxis,r=i.dim,o=[" "].concat(p(t.series,function(t){return t.name})),a=[n.model.getCategories()];f(t.series,function(t){var e=t.getRawData();a.push(t.getRawData().mapArray(e.mapDimension(r),function(t){return t}))});for(var s=[o.join(bT)],l=0;l<a[0].length;l++){for(var u=[],h=0;h<a.length;h++)u.push(a[h][l]);s.push(u.join(bT))}e.push(s.join("\n"))}),e.join("\n\n"+wT+"\n\n")}function Jp(t){return p(t,function(t){var e=t.getRawData(),n=[t.name],i=[];return e.each(e.dimensions,function(){for(var t=arguments.length,r=arguments[t-1],o=e.getName(r),a=0;t-1>a;a++)i[a]=arguments[a];n.push((o?o+bT:"")+i.join(bT))}),n.join("\n")}).join("\n\n"+wT+"\n\n")}function tg(t){var e=Kp(t);return{value:v([Qp(e.seriesGroupByCategoryAxis),Jp(e.other)],function(t){return t.replace(/[\n\t\s]/g,"")}).join("\n\n"+wT+"\n\n"),meta:e.meta}}function eg(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function ng(t){var e=t.slice(0,t.indexOf("\n"));return e.indexOf(bT)>=0?!0:void 0}function ig(t){for(var e=t.split(/\n+/g),n=eg(e.shift()).split(ST),i=[],r=p(n,function(t){return{name:t,data:[]}}),o=0;o<e.length;o++){var a=eg(e[o]).split(ST);i.push(a.shift());for(var s=0;s<a.length;s++)r[s]&&(r[s].data[o]=a[s])}return{series:r,categories:i}}function rg(t){for(var e=t.split(/\n+/g),n=eg(e.shift()),i=[],r=0;r<e.length;r++){var o=eg(e[r]);if(o){var a,s=o.split(ST),l="",u=!1;isNaN(s[0])?(u=!0,l=s[0],s=s.slice(1),i[r]={name:l,value:[]},a=i[r].value):a=i[r]=[];for(var h=0;h<s.length;h++)a.push(+s[h]);1===a.length&&(u?i[r].value=a[0]:i[r]=a[0])}}return{name:n,data:i}}function og(t,e){var n=t.split(new RegExp("\n*"+wT+"\n*","g")),i={series:[]};return f(n,function(t,n){if(ng(t)){var r=ig(t),o=e[n],a=o.axisDim+"Axis";o&&(i[a]=i[a]||[],i[a][o.axisIndex]={data:r.categories},i.series=i.series.concat(r.series))}else{var r=rg(t);i.series.push(r)}}),i}function ag(t){this._dom=null,this.model=t}function sg(t,e){return p(t,function(t,n){var i=e&&e[n];if(S(i)&&!_(i)){var r=S(t)&&!_(t);r||(t={value:t});var o=null!=i.name&&null==t.name;return t=s(t,i),o&&delete t.name,t}return t})}function lg(t){_v&&O(t),Yv.call(this),this._zr=t,this.group=new Nm,this._brushType,this._brushOption,this._panels,this._track=[],this._dragging,this._covers=[],this._creatingCover,this._creatingPanel,this._enableGlobalPan,_v&&this._mounted,this._uid="brushController_"+BT++,this._handlers={},IT(NT,function(t,e){this._handlers[e]=y(t,this)},this)}function ug(t,e){var n=t._zr;t._enableGlobalPan||Mp(n,OT,t._uid),cg(n,t._handlers),t._brushType=e.brushType,t._brushOption=r(i(RT),e,!0)}function hg(t){var e=t._zr;Ip(e,OT,t._uid),dg(e,t._handlers),t._brushType=t._brushOption=null}function cg(t,e){IT(e,function(e,n){t.on(n,e)})}function dg(t,e){IT(e,function(e,n){t.off(n,e)})}function fg(t,e){var n=FT[e.brushType].createCover(t,e);return n.__brushOption=e,vg(n,e),t.group.add(n),n}function pg(t,e){var n=yg(e);return n.endCreating&&(n.endCreating(t,e),vg(e,e.__brushOption)),e}function gg(t,e){var n=e.__brushOption;yg(e).updateCoverShape(t,e,n.range,n)}function vg(t,e){var n=e.z;null==n&&(n=kT),t.traverse(function(t){t.z=n,t.z2=n})}function mg(t,e){yg(e).updateCommon(t,e),gg(t,e)}function yg(t){return FT[t.__brushOption.brushType]}function xg(t,e,n){var i=t._panels;if(!i)return!0;var r,o=t._transform;return IT(i,function(t){t.isTargetByCursor(e,n,o)&&(r=t)}),r}function _g(t,e){var n=t._panels;if(!n)return!0;var i=e.__brushOption.panelId;return null!=i?n[i]:!0}function wg(t){var e=t._covers,n=e.length;return IT(e,function(e){t.group.remove(e)},t),e.length=0,!!n}function bg(t,e){var n=CT(t._covers,function(t){var e=t.__brushOption,n=i(e.range);return{brushType:e.brushType,panelId:e.panelId,range:n}});t.trigger("brush",n,{isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Sg(t){var e=t._track;if(!e.length)return!1;var n=e[e.length-1],i=e[0],r=n[0]-i[0],o=n[1]-i[1],a=DT(r*r+o*o,.5);return a>PT}function Mg(t){var e=t.length-1;return 0>e&&(e=0),[t[0],t[e]]}function Ig(t,e,n,i){var r=new Nm;return r.add(new d_({name:"main",style:Dg(n),silent:!0,draggable:!0,cursor:"move",drift:MT(t,e,r,"nswe"),ondragend:MT(bg,e,{isEnd:!0})})),IT(i,function(n){r.add(new d_({name:n,style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:MT(t,e,r,n),ondragend:MT(bg,e,{isEnd:!0})}))}),r}function Cg(t,e,n,i){var r=i.brushStyle.lineWidth||0,o=AT(r,LT),a=n[0][0],s=n[1][0],l=a-r/2,u=s-r/2,h=n[0][1],c=n[1][1],d=h-o+r/2,f=c-o+r/2,p=h-a,g=c-s,v=p+r,m=g+r;Ag(t,e,"main",a,s,p,g),i.transformable&&(Ag(t,e,"w",l,u,o,m),Ag(t,e,"e",d,u,o,m),Ag(t,e,"n",l,u,v,o),Ag(t,e,"s",l,f,v,o),Ag(t,e,"nw",l,u,o,o),Ag(t,e,"ne",d,u,o,o),Ag(t,e,"sw",l,f,o,o),Ag(t,e,"se",d,f,o,o))}function Tg(t,e){var n=e.__brushOption,i=n.transformable,r=e.childAt(0);r.useStyle(Dg(n)),r.attr({silent:!i,cursor:i?"move":"default"}),IT(["w","e","n","s","se","sw","ne","nw"],function(n){var r=e.childOfName(n),o=Lg(t,n);r&&r.attr({silent:!i,invisible:!i,cursor:i?ET[o]+"-resize":null})})}function Ag(t,e,n,i,r,o,a){var s=e.childOfName(n);s&&s.setShape(Bg(Rg(t,e,[[i,r],[i+o,r+a]])))}function Dg(t){return s({strokeNoScale:!0},t.brushStyle)}function kg(t,e,n,i){var r=[TT(t,n),TT(e,i)],o=[AT(t,n),AT(e,i)];return[[r[0],o[0]],[r[1],o[1]]]}function Pg(t){return ra(t.group)}function Lg(t,e){if(e.length>1){e=e.split("");var n=[Lg(t,e[0]),Lg(t,e[1])];return("e"===n[0]||"w"===n[0])&&n.reverse(),n.join("")}var i={w:"left",e:"right",n:"top",s:"bottom"},r={left:"w",right:"e",top:"n",bottom:"s"},n=aa(i[e],Pg(t));return r[n]}function Og(t,e,n,i,r,o,a){var s=i.__brushOption,l=t(s.range),u=Eg(n,o,a);IT(r.split(""),function(t){var e=zT[t];l[e[0]][e[1]]+=u[e[0]]}),s.range=e(kg(l[0][0],l[1][0],l[0][1],l[1][1])),mg(n,i),bg(n,{isEnd:!1})}function zg(t,e,n,i){var r=e.__brushOption.range,o=Eg(t,n,i);IT(r,function(t){t[0]+=o[0],t[1]+=o[1]}),mg(t,e),bg(t,{isEnd:!1})}function Eg(t,e,n){var i=t.group,r=i.transformCoordToLocal(e,n),o=i.transformCoordToLocal(0,0);return[r[0]-o[0],r[1]-o[1]]}function Rg(t,e,n){var r=_g(t,e);return r&&r!==!0?r.clipPath(n,t._transform):i(n)}function Bg(t){var e=TT(t[0][0],t[1][0]),n=TT(t[0][1],t[1][1]),i=AT(t[0][0],t[1][0]),r=AT(t[0][1],t[1][1]);return{x:e,y:n,width:i-e,height:r-n}}function Ng(t,e,n){if(t._brushType&&!Zg(t,e)){var i=t._zr,r=t._covers,o=xg(t,e,n);if(!t._dragging)for(var a=0;a<r.length;a++){var s=r[a].__brushOption;if(o&&(o===!0||s.panelId===o.panelId)&&FT[s.brushType].contain(r[a],n[0],n[1]))return}o&&i.setCursorStyle("crosshair")}}function Fg(t){var e=t.event;e.preventDefault&&e.preventDefault()}function Vg(t,e,n){return t.childOfName("main").contain(e,n)}function Hg(t,e,n,r){var o,a=t._creatingCover,s=t._creatingPanel,l=t._brushOption;if(t._track.push(n.slice()),Sg(t)||a){if(s&&!a){"single"===l.brushMode&&wg(t);var u=i(l);u.brushType=Wg(u.brushType,s),u.panelId=s===!0?null:s.panelId,a=t._creatingCover=fg(t,u),t._covers.push(a)}if(a){var h=FT[Wg(t._brushType,s)],c=a.__brushOption;c.range=h.getCreatingRange(Rg(t,a,t._track)),r&&(pg(t,a),h.updateCommon(t,a)),gg(t,a),o={isEnd:r}}}else r&&"single"===l.brushMode&&l.removeOnClick&&xg(t,e,n)&&wg(t)&&(o={isEnd:r,removeOnClick:!0});return o}function Wg(t,e){return"auto"===t?(_v&&O(e&&e.defaultBrushType,'MUST have defaultBrushType when brushType is "atuo"'),e.defaultBrushType):t}function Gg(t,e){if(t._dragging){Fg(e);var n=e.offsetX,i=e.offsetY,r=t.group.transformCoordToLocal(n,i),o=Hg(t,e,r,!0);t._dragging=!1,t._track=[],t._creatingCover=null,o&&bg(t,o)}}function Zg(t,e,n){var i=t._zr;return 0>e||e>i.getWidth()||0>n||n>i.getHeight()}function Xg(t){return{createCover:function(e,n){return Ig(MT(Og,function(e){var n=[e,[0,100]];return t&&n.reverse(),n},function(e){return e[t]}),e,n,[["w","e"],["n","s"]][t])},getCreatingRange:function(e){var n=Mg(e),i=TT(n[0][t],n[1][t]),r=AT(n[0][t],n[1][t]);return[i,r]},updateCoverShape:function(e,n,i,r){var o,a=_g(e,n);if(a!==!0&&a.getLinearBrushOtherExtent)o=a.getLinearBrushOtherExtent(t,e._transform);else{var s=e._zr;o=[0,[s.getWidth(),s.getHeight()][1-t]]}var l=[i,o];t&&l.reverse(),Cg(e,n,l,r)},updateCommon:Tg,contain:Vg}}function Yg(t,e,n){var i=e.getComponentByElement(t.topTarget),r=i&&i.coordinateSystem;return i&&i!==n&&!VT[i.mainType]&&r&&r.model!==n}function Ug(t){return t=$g(t),function(e){return la(e,t)}}function jg(t,e){return t=$g(t),function(n){var i=null!=e?e:n,r=i?t.width:t.height,o=i?t.x:t.y;return[o,o+(r||0)]}}function qg(t,e,n){return t=$g(t),function(i,r){return t.contain(r[0],r[1])&&!Yg(i,e,n)}}function $g(t){return Tn.create(t)}function Kg(t,e,n){var i=this._targetInfoList=[],r={},o=Jg(e,t);HT(UT,function(t,e){(!n||!n.include||WT(n.include,e)>=0)&&t(o,i,r)})}function Qg(t){return t[0]>t[1]&&t.reverse(),t}function Jg(t,e){return cr(t,e,{includeMainTypes:XT})}function tv(t,e,n,i){_v&&O("cartesian2d"===n.type,"lineX/lineY brush is available only in cartesian2d.");var r=n.getAxis(["x","y"][t]),o=Qg(p([0,1],function(t){return e?r.coordToData(r.toLocalCoord(i[t])):r.toGlobalCoord(r.dataToCoord(i[t]))})),a=[];return a[t]=o,a[1-t]=[0/0,0/0],{values:o,xyMinMax:a}}function ev(t,e,n,i){return[e[0]-i[t]*n[0],e[1]-i[t]*n[1]]}function nv(t,e){var n=iv(t),i=iv(e),r=[n[0]/i[0],n[1]/i[1]];return isNaN(r[0])&&(r[0]=1),isNaN(r[1])&&(r[1]=1),r}function iv(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[0/0,0/0]}function rv(t,e){var n=lv(t);QT(e,function(e,i){for(var r=n.length-1;r>=0;r--){var o=n[r];if(o[i])break}if(0>r){var a=t.queryComponents({mainType:"dataZoom",subType:"select",id:i})[0];if(a){var s=a.getPercentRange();n[0][i]={dataZoomId:i,start:s[0],end:s[1]}}}}),n.push(e)}function ov(t){var e=lv(t),n=e[e.length-1];e.length>1&&e.pop();var i={};return QT(n,function(t,n){for(var r=e.length-1;r>=0;r--){var t=e[r][n];if(t){i[n]=t;break}}}),i}function av(t){t[JT]=null}function sv(t){return lv(t).length}function lv(t){var e=t[JT];return e||(e=t[JT]=[{}]),e}function uv(t,e,n){(this._brushController=new lg(n.getZr())).on("brush",y(this._onBrush,this)).mount(),this._isZoomActive}function hv(t){var e={};return f(["xAxisIndex","yAxisIndex"],function(n){e[n]=t[n],null==e[n]&&(e[n]="all"),(e[n]===!1||"none"===e[n])&&(e[n]=[])}),e}function cv(t,e){t.setIconStatus("back",sv(e)>1?"emphasis":"normal")}function dv(t,e,n,i,r){var o=n._isZoomActive;i&&"takeGlobalCursor"===i.type&&(o="dataZoomSelect"===i.key?i.dataZoomSelectActive:!1),n._isZoomActive=o,t.setIconStatus("zoom",o?"emphasis":"normal");var a=new Kg(hv(t.option),e,{include:["grid"]});n._brushController.setPanels(a.makePanelOpts(r,function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"})).enableBrush(o?{brushType:"auto",brushStyle:t.getModel("brushStyle").getItemStyle()}:!1)}function fv(t){this.model=t}function pv(t){return sA(t)}function gv(){if(!hA&&cA){hA=!0;var t=cA.styleSheets;t.length<31?cA.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}function vv(t){return parseInt(t,10)}function mv(t,e){gv(),this.root=t,this.storage=e;var n=document.createElement("div"),i=document.createElement("div");n.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",i.style.cssText="position:absolute;left:0;top:0;",t.appendChild(n),this._vmlRoot=i,this._vmlViewport=n,this.resize();var r=e.delFromStorage,o=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(i)},e.addToStorage=function(t){t.onAdd&&t.onAdd(i),o.call(e,t)},this._firstPaint=!0}function yv(t){return function(){Lm('In IE8.0 VML mode painter not support method "'+t+'"')}}var xv;"undefined"!=typeof window?xv=window.__DEV__:"undefined"!=typeof global&&(xv=global.__DEV__),"undefined"==typeof xv&&(xv=!0);var _v=xv,wv=2311,bv=function(){return wv++},Sv={};Sv="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:e(navigator.userAgent);var Mv=Sv,Iv={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},Cv={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},Tv=Object.prototype.toString,Av=Array.prototype,Dv=Av.forEach,kv=Av.filter,Pv=Av.slice,Lv=Av.map,Ov=Av.reduce,zv={},Ev=function(){return zv.createCanvas()};zv.createCanvas=function(){return document.createElement("canvas")};var Rv,Bv="__ec_primitive__";B.prototype={constructor:B,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){void 0!==e&&(t=y(t,e));for(var n in this.data)this.data.hasOwnProperty(n)&&t(this.data[n],n)},removeKey:function(t){delete this.data[t]}};var Nv=(Object.freeze||Object)({$override:n,clone:i,merge:r,mergeAll:o,extend:a,defaults:s,createCanvas:Ev,getContext:l,indexOf:u,inherits:h,mixin:c,isArrayLike:d,each:f,map:p,reduce:g,filter:v,find:m,bind:y,curry:x,isArray:_,isFunction:w,isString:b,isObject:S,isBuiltInObject:M,isTypedArray:I,isDom:C,eqNaN:T,retrieve:A,retrieve2:D,retrieve3:k,slice:P,normalizeCssArray:L,assert:O,trim:z,setAsPrimitive:E,isPrimitive:R,createHashMap:N,concatArray:F,noop:V}),Fv="undefined"==typeof Float32Array?Array:Float32Array,Vv=j,Hv=q,Wv=ee,Gv=ne,Zv=(Object.freeze||Object)({create:H,copy:W,clone:G,set:Z,add:X,scaleAndAdd:Y,sub:U,len:j,length:Vv,lenSquare:q,lengthSquare:Hv,mul:$,div:K,dot:Q,scale:J,normalize:te,distance:ee,dist:Wv,distanceSquare:ne,distSquare:Gv,negate:ie,lerp:re,applyTransform:oe,min:ae,max:se});le.prototype={constructor:le,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(ue(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.dispatchToElement(ue(e,t),"drag",t.event);var a=this.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.dispatchToElement(ue(s,t),"dragleave",t.event),a&&a!==s&&this.dispatchToElement(ue(a,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(ue(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(ue(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var Xv=Array.prototype.slice,Yv=function(t){this._$handlers={},this._$eventProcessor=t};Yv.prototype={constructor:Yv,one:function(t,e,n,i){return ce(this,t,e,n,i,!0)},on:function(t,e,n,i){return ce(this,t,e,n,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;o>r;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;r>3&&(i=Xv.call(i,1));for(var o=e.length,a=0;o>a;){var s=e[a];if(n&&n.filter&&null!=s.query&&!n.filter(t,s.query))a++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,i[1]);break;case 3:s.h.call(s.ctx,i[1],i[2]);break;default:s.h.apply(s.ctx,i)}s.one?(e.splice(a,1),o--):a++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;r>4&&(i=Xv.call(i,1,i.length-1));for(var o=i[i.length-1],a=e.length,s=0;a>s;){var l=e[s];if(n&&n.filter&&null!=l.query&&!n.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(o);break;case 2:l.h.call(o,i[1]);break;case 3:l.h.call(o,i[1],i[2]);break;default:l.h.apply(o,i)}l.one?(e.splice(s,1),a--):s++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this}};var Uv=Math.log(2),jv="___zrEVENTSAVED",qv=[],$v="undefined"!=typeof window&&!!window.addEventListener,Kv=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Qv=[],Jv=$v?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},tm=function(){this._track=[]};tm.prototype={constructor:tm,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;a>o;o++){var s=i[o],l=xe(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in em)if(em.hasOwnProperty(e)){var n=em[e](this._track,t);if(n)return n}}};var em={pinch:function(t,e){var n=t.length;if(n){var i=(t[n-1]||{}).points,r=(t[n-2]||{}).points||i;if(r&&r.length>1&&i&&i.length>1){var o=Ce(i)/Ce(r);!isFinite(o)&&(o=1),e.pinchScale=o;var a=Te(i);return e.pinchX=a[0],e.pinchY=a[1],{type:"pinch",target:t[0].target,event:e}}}}},nm="silent";ke.prototype.dispose=function(){};var im=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],rm=function(t,e,n,i){Yv.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new ke,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,le.call(this),this.setHandlerProxy(n)};rm.prototype={constructor:rm,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(f(im,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,n=t.zrY,i=Le(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(r=this.findHover(r.x,r.y),o=r.target);var a=this._hovered=i?{x:e,y:n}:this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,n=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&!n&&this.trigger("globalout",{type:"globalout",event:t})},resize:function(){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){t=t||{};var i=t.target;if(!i||!i.silent){for(var r="on"+e,o=Ae(e,t,n);i&&(i[r]&&(o.cancelBubble=i[r].call(i,o)),i.trigger(e,o),i=i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},o=i.length-1;o>=0;o--){var a;if(i[o]!==n&&!i[o].ignore&&(a=Pe(i[o],t,e))&&(!r.topTarget&&(r.topTarget=i[o]),a!==nm)){r.target=i[o];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new tm);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r,this.dispatchToElement({target:i.target},r,i.event)}}},f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){rm.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,a=Le(this,r,o);if("mouseup"===t&&a||(n=this.findHover(r,o),i=n.target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Wv(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}),c(rm,Yv),c(rm,le);var om="undefined"==typeof Float32Array?Array:Float32Array,am=(Object.freeze||Object)({create:Oe,identity:ze,copy:Ee,mul:Re,translate:Be,rotate:Ne,scale:Fe,invert:Ve,clone:He}),sm=ze,lm=5e-5,um=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},hm=um.prototype;hm.transform=null,hm.needLocalTransform=function(){return We(this.rotation)||We(this.position[0])||We(this.position[1])||We(this.scale[0]-1)||We(this.scale[1]-1)};var cm=[];hm.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),i=this.transform;if(!n&&!e)return void(i&&sm(i));i=i||Oe(),n?this.getLocalTransform(i):sm(i),e&&(n?Re(i,t.transform,i):Ee(i,t.transform)),this.transform=i;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(cm);var o=cm[0]<0?-1:1,a=cm[1]<0?-1:1,s=((cm[0]-o)*r+o)/cm[0]||0,l=((cm[1]-a)*r+a)/cm[1]||0;i[0]*=s,i[1]*=s,i[2]*=l,i[3]*=l}this.invTransform=this.invTransform||Oe(),Ve(this.invTransform,i)},hm.getLocalTransform=function(t){return um.getLocalTransform(this,t)},hm.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},hm.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var dm=[],fm=Oe();hm.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=this.position,r=this.scale;We(e-1)&&(e=Math.sqrt(e)),We(n-1)&&(n=Math.sqrt(n)),t[0]<0&&(e=-e),t[3]<0&&(n=-n),i[0]=t[4],i[1]=t[5],r[0]=e,r[1]=n,this.rotation=Math.atan2(-t[1]/n,t[0]/e)}},hm.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(Re(dm,t.invTransform,e),e=dm);var n=this.origin;n&&(n[0]||n[1])&&(fm[4]=n[0],fm[5]=n[1],Re(dm,e,fm),dm[4]-=n[0],dm[5]-=n[1],e=dm),this.setLocalTransform(e)}},hm.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},hm.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&oe(n,n,i),n},hm.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&oe(n,n,i),n},um.getLocalTransform=function(t,e){e=e||[],sm(e);var n=t.origin,i=t.scale||[1,1],r=t.rotation||0,o=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),Fe(e,e,i),r&&Ne(e,e,r),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=o[0],e[5]+=o[1],e};var pm={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||1>n?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/i)))},elasticOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||1>n?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/i)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||1>n?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?-.5*n*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/i):n*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-pm.bounceOut(1-t)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return.5>t?.5*pm.bounceIn(2*t):.5*pm.bounceOut(2*t-1)+.5}};Ge.prototype={constructor:Ge,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var n=(t-this._startTime-this._pausedTime)/this._life;if(!(0>n)){n=Math.min(n,1);var i=this.easing,r="string"==typeof i?pm[i]:i,o="function"==typeof r?r(n):n;return this.fire("frame",o),1===n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var gm=function(){this.head=null,this.tail=null,this._len=0},vm=gm.prototype;vm.insert=function(t){var e=new mm(t);return this.insertEntry(e),e},vm.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},vm.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},vm.len=function(){return this._len},vm.clear=function(){this.head=this.tail=null,this._len=0};var mm=function(t){this.value=t,this.next,this.prev},ym=function(t){this._list=new gm,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},xm=ym.prototype;xm.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new mm(e),a.key=t,n.insertEntry(a),i[t]=a}return r},xm.get=function(t){var e=this._map[t],n=this._list;return null!=e?(e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value):void 0},xm.clear=function(){this._list.clear(),this._map={}};var _m={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},wm=new ym(20),bm=null,Sm=an,Mm=sn,Im=(Object.freeze||Object)({parse:tn,lift:rn,toHex:on,fastLerp:an,fastMapToColor:Sm,lerp:sn,mapToColor:Mm,modifyHSL:ln,modifyAlpha:un,stringify:hn}),Cm=Array.prototype.slice,Tm=function(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||cn,this._setter=i||dn,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]
};Tm.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:_n(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;e>n;n++)t[n].call(this)},start:function(t,e){var n,i=this,r=0,o=function(){r--,r||i._doneCallback()};for(var a in this._tracks)if(this._tracks.hasOwnProperty(a)){var s=Sn(this,t,o,this._tracks[a],a,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),n=s)}if(n){var l=n.onframe;n.onframe=function(t,e){l(t,e);for(var n=0;n<i._onframeList.length;n++)i._onframeList[n](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var Am=1;"undefined"!=typeof window&&(Am=Math.max(window.devicePixelRatio||1,1));var Dm=0,km=Am,Pm=function(){};1===Dm&&(Pm=console.error);var Lm=Pm,Om=function(){this.animators=[]};Om.prototype={constructor:Om,animate:function(t,e){var n,i=!1,r=this,o=this.__zr;if(t){var a=t.split("."),s=r;i="shape"===a[0];for(var l=0,h=a.length;h>l;l++)s&&(s=s[a[l]]);s&&(n=s)}else n=r;if(!n)return void Lm('Property "'+t+'" is not existed in element '+r.id);var c=r.animators,d=new Tm(n,e);return d.during(function(){r.dirty(i)}).done(function(){c.splice(u(c,d),1)}),c.push(d),o&&o.animation.addAnimator(d),d},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;n>i;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,o){Mn(this,t,e,n,i,r,o)},animateFrom:function(t,e,n,i,r,o){Mn(this,t,e,n,i,r,o,!0)}};var zm=function(t){um.call(this,t),Yv.call(this,t),Om.call(this,t),this.id=t.id||bv()};zm.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];n||(n=this[t]=[]),n[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(S(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},c(zm,Om),c(zm,um),c(zm,Yv);var Em=oe,Rm=Math.min,Bm=Math.max;Tn.prototype={constructor:Tn,union:function(t){var e=Rm(t.x,this.x),n=Rm(t.y,this.y);this.width=Bm(t.x+t.width,this.x+this.width)-e,this.height=Bm(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:function(){var t=[],e=[],n=[],i=[];return function(r){if(r){t[0]=n[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=n[1]=this.y+this.height,Em(t,t,r),Em(e,e,r),Em(n,n,r),Em(i,i,r),this.x=Rm(t[0],e[0],n[0],i[0]),this.y=Rm(t[1],e[1],n[1],i[1]);var o=Bm(t[0],e[0],n[0],i[0]),a=Bm(t[1],e[1],n[1],i[1]);this.width=o-this.x,this.height=a-this.y}}}(),calculateTransform:function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=Oe();return Be(r,r,[-e.x,-e.y]),Fe(r,r,[n,i]),Be(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof Tn||(t=Tn.create(t));var e=this,n=e.x,i=e.x+e.width,r=e.y,o=e.y+e.height,a=t.x,s=t.x+t.width,l=t.y,u=t.y+t.height;return!(a>i||n>s||l>o||r>u)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new Tn(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},Tn.create=function(t){return new Tn(t.x,t.y,t.width,t.height)};var Nm=function(t){t=t||{},zm.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Nm.prototype={constructor:Nm,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Nm&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,i=this._children,r=u(i,t);return 0>r?this:(i.splice(r,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof Nm&&t.delChildrenFromStorage(n)),e&&e.refresh(),this)},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof Nm&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof Nm&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof Nm&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new Tn(0,0,0,0),i=t||this._children,r=[],o=0;o<i.length;o++){var a=i[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(r);l?(n.copy(s),n.applyTransform(l),e=e||n.clone(),e.union(n)):(e=e||s.clone(),e.union(s))}}return e||n}},h(Nm,zm);var Fm=32,Vm=7,Hm=function(){this._roots=[],this._displayList=[],this._displayListLen=0};Hm.prototype={constructor:Hm,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;r>i;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,Mv.canvasSupported&&En(n,Rn)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Nm&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof Nm&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,i=t.length;i>e;e++)this.delRoot(t[e]);else{var r=u(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Nm&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:Rn};var Wm={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},Gm=function(t,e,n){return Wm.hasOwnProperty(e)?n*=t.dpr:n},Zm={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},Xm=9,Ym=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],Um=function(t){this.extendFrom(t,!1)};Um.prototype={constructor:Um,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){var i=this,r=n&&n.style,o=!r||t.__attrCachedBy!==Zm.STYLE_BIND;t.__attrCachedBy=Zm.STYLE_BIND;for(var a=0;a<Ym.length;a++){var s=Ym[a],l=s[0];(o||i[l]!==r[l])&&(t[l]=Gm(t,l,i[l]||s[1]))}if((o||i.fill!==r.fill)&&(t.fillStyle=i.fill),(o||i.stroke!==r.stroke)&&(t.strokeStyle=i.stroke),(o||i.opacity!==r.opacity)&&(t.globalAlpha=null==i.opacity?1:i.opacity),(o||i.blend!==r.blend)&&(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()){var u=i.lineWidth;t.lineWidth=u/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||e!==!0&&(e===!1?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i="radial"===e.type?Nn:Bn,r=i(t,e,n),o=e.colorStops,a=0;a<o.length;a++)r.addColorStop(o[a].offset,o[a].color);return r}};for(var jm=Um.prototype,qm=0;qm<Ym.length;qm++){var $m=Ym[qm];$m[0]in jm||(jm[$m[0]]=$m[1])}Um.getGradient=jm.getGradient;var Km=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};Km.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var Qm=function(t,e,n){var i;n=n||km,"string"==typeof t?i=Vn(t,e,n):S(t)&&(i=t,t=i.id),this.id=t,this.dom=i;var r=i.style;r&&(i.onselectstart=Fn,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n};Qm.prototype={constructor:Qm,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=Vn("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},clear:function(t,e){var n=this.dom,i=this.ctx,r=n.width,o=n.height,e=e||this.clearColor,a=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(a&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,r/l,o/l)),i.clearRect(0,0,r,o),e&&"transparent"!==e){var u;e.colorStops?(u=e.__canvasGradient||Um.getGradient(i,e,{x:0,y:0,width:r,height:o}),e.__canvasGradient=u):e.image&&(u=Km.prototype.getCanvasPattern.call(e,i)),i.save(),i.fillStyle=u||e,i.fillRect(0,0,r,o),i.restore()}if(a){var h=this.domBack;i.save(),i.globalAlpha=s,i.drawImage(h,0,0,r,o),i.restore()}}};var Jm="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},ty=new ym(50),ey={},ny=0,iy=5e3,ry=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,oy="12px sans-serif",ay={};ay.measureText=function(t,e){var n=l();return n.font=e||oy,n.measureText(t)};var sy=oy,ly={left:1,right:1,center:1},uy={top:1,bottom:1,middle:1},hy=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],cy={},dy={},fy=new Tn,py=function(){};py.prototype={constructor:py,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&hi(n,!0);var i=n.text;if(null!=i&&(i+=""),Ti(i,n)){t.save();var r=this.transform;n.transformText?this.setTransform(t):r&&(fy.copy(e),fy.applyTransform(r),e=fy),di(this,t,i,n,e,Xm),t.restore()}}},Ai.prototype={constructor:Ai,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(n[0],n[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?zm.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new Um(t,this),this.dirty(!1),this},calculateTextPosition:null},h(Ai,zm),c(Ai,py),Di.prototype={constructor:Di,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=Wn(i,this._image,this,this.onload);if(r&&Zn(r)){var o=n.x||0,a=n.y||0,s=n.width,l=n.height,u=r.width/r.height;if(null==s&&null!=l?s=l*u:null==l&&null!=s?l=s/u:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var h=n.sx||0,c=n.sy||0;t.drawImage(r,h,c,n.sWidth,n.sHeight,o,a,s,l)}else if(n.sx&&n.sy){var h=n.sx,c=n.sy,d=s-h,f=l-c;t.drawImage(r,h,c,d,f,o,a,s,l)}else t.drawImage(r,o,a,s,l);null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new Tn(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},h(Di,Ai);var gy=1e5,vy=314159,my=.01,yy=.001,xy=new Tn(0,0,0,0),_y=new Tn(0,0,0,0),wy=function(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=a({},n||{}),this.dpr=n.devicePixelRatio||km,this._singleCanvas=i,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var o=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,i){var l=t.width,u=t.height;null!=n.width&&(l=n.width),null!=n.height&&(u=n.height),this.dpr=n.devicePixelRatio||1,t.width=l*this.dpr,t.height=u*this.dpr,this._width=l,this._height=u;var h=new Qm(t,this,this.dpr);h.__builtin__=!0,h.initContext(),s[vy]=h,h.zlevel=vy,o.push(vy),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=Ei(this._width,this._height);t.appendChild(c)}this._hoverlayer=null,this._hoverElements=[]};wy.prototype={constructor:wy,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var r=n[i],o=this._layers[r];if(!o.__builtin__&&o.refresh){var a=0===i?this._backgroundColor:null;o.refresh(a)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return n.__from=t,t.__hoverMir=n,e&&n.setStyle(e),this._hoverElements.push(n),n}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=u(n,e);i>=0&&n.splice(i,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var n=t[e].__from;n&&(n.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){En(t,this.storage.displayableSortFunc),n||(n=this._hoverlayer=this.getLayer(gy));var i={};n.ctx.save();for(var r=0;e>r;){var o=t[r],a=o.__from;a&&a.__zr?(r++,a.invisible||(o.transform=a.transform,o.invTransform=a.invTransform,o.__clipPaths=a.__clipPaths,this._doPaintEl(o,n,!0,i))):(t.splice(r,1),a.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(gy)},_paintList:function(t,e,n){if(this._redrawId===n){e=e||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!i){var r=this;Jm(function(){r._paintList(t,e,n)})}}},_compositeManually:function(){var t=this.getLayer(vy).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer(function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)})},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i],o=this._layers[r];o.__builtin__&&o!==this._hoverlayer&&(o.__dirty||e)&&n.push(o)}for(var a=!0,s=0;s<n.length;s++){var o=n[s],l=o.ctx,u={};l.save();var h=e?o.__startIndex:o.__drawIndex,c=!e&&o.incremental&&Date.now,d=c&&Date.now(),p=o.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(o.__startIndex===o.__endIndex)o.clear(!1,p);else if(h===o.__startIndex){var g=t[h];g.incremental&&g.notClear&&!e||o.clear(!1,p)}-1===h&&(console.error("For some unknown reason. drawIndex is -1"),h=o.__startIndex);for(var v=h;v<o.__endIndex;v++){var m=t[v];if(this._doPaintEl(m,o,e,u),m.__dirty=m.__dirtyText=!1,c){var y=Date.now()-d;if(y>15)break}}o.__drawIndex=v,o.__drawIndex<o.__endIndex&&(a=!1),u.prevElClipPaths&&l.restore(),l.restore()}return Mv.wxa&&f(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),a},_doPaintEl:function(t,e,n,i){var r=e.ctx,o=t.transform;if(!(!e.__dirty&&!n||t.invisible||0===t.style.opacity||o&&!o[0]&&!o[3]||t.culling&&Li(t,this._width,this._height))){var a=t.__clipPaths,s=i.prevElClipPaths;(!s||Oi(a,s))&&(s&&(r.restore(),i.prevElClipPaths=null,i.prevEl=null),a&&(r.save(),zi(a,r),i.prevElClipPaths=a)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=vy);var n=this._layers[t];return n||(n=new Qm("zr_"+t,this,this.dpr),n.zlevel=t,n.__builtin__=!0,this._layerConfig[t]?r(n,this._layerConfig[t],!0):this._layerConfig[t-my]&&r(n,this._layerConfig[t-my],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=null,a=-1,s=this._domRoot;if(n[t])return void Lm("ZLevel "+t+" has been used already");if(!Pi(e))return void Lm("Layer of zlevel "+t+" is not valid");if(r>0&&t>i[0]){for(a=0;r-1>a&&!(i[a]<t&&i[a+1]>t);a++);o=n[i[a]]}if(i.splice(a+1,0,t),n[t]=e,!e.virtual)if(o){var l=o.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],n=this._layers[i],n.__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],n=this._layers[i],n.__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer(function(t){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++){var i=t[n];if(i.zlevel!==t[n-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}for(var r,o=null,a=0,n=0;n<t.length;n++){var s,i=t[n],l=i.zlevel;r!==l&&(r=l,a=0),i.incremental?(s=this.getLayer(l+yy,this._needsManuallyCompositing),s.incremental=!0,a=1):s=this.getLayer(l+(a>0?my:0),this._needsManuallyCompositing),s.__builtin__||Lm("ZLevel "+l+" has been used by unkown layer "+s.id),s!==o&&(s.__used=!0,s.__startIndex!==n&&(s.__dirty=!0),s.__startIndex=n,s.__drawIndex=s.incremental?-1:n,e(n),o=s),i.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=n))}e(n),this.eachBuiltinLayer(function(t){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?r(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var o=this._zlevelList[i];if(o===t||o===t+my){var a=this._layers[o];r(a,n[t],!0)}}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(u(n,t),1))},resize:function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!==t||e!==this._height){n.style.width=t+"px",n.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);f(this._progressiveLayers,function(n){n.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(vy).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[vy].dom;var e=new Qm("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var o={},a=this.storage.getDisplayList(!0),s=0;s<a.length;s++){var l=a[s];this._doPaintEl(l,e,!0,o)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||ki(s[n])||ki(a.style[n]))-(ki(s[r])||0)-(ki(s[o])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),o=t.style,a=o.shadowBlur*e,s=o.shadowOffsetX*e,l=o.shadowOffsetY*e,u=o.hasStroke()?o.lineWidth:0,h=Math.max(u/2,-s+a),c=Math.max(u/2,s+a),d=Math.max(u/2,-l+a),f=Math.max(u/2,l+a),p=r.width+h+c,g=r.height+d+f;n.width=p*e,n.height=g*e,i.scale(e,e),i.clearRect(0,0,p,g),i.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[h-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var m=Di,y=new m({style:{x:0,y:0,image:n}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}};var by=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,Yv.call(this)};by.prototype={constructor:by,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=u(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],o=[],a=0;i>a;a++){var s=n[a],l=s.step(t,e);l&&(r.push(l),o.push(s))}for(var a=0;i>a;)n[a]._needsRemove?(n[a]=n[i-1],n.pop(),i--):a++;i=r.length;for(var a=0;i>a;a++)o[a].fire(r[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(Jm(t),!e._paused&&e._update())}var e=this;this._running=!0,Jm(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var n=new Tm(t,e.loop,e.getter,e.setter);return this.addAnimator(n),n}},c(by,Yv);var Sy=300,My=Mv.domSupported,Iy=function(){var t=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],n={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=p(t,function(t){var e=t.replace("mouse","pointer");return n.hasOwnProperty(e)?e:t});return{mouse:t,touch:e,pointer:i}}(),Cy={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Ty=Wi.prototype;Ty.stopPropagation=Ty.stopImmediatePropagation=Ty.preventDefault=V;var Ay={mousedown:function(t){t=be(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=be(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||Ui(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=be(this.dom,t),Ui(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=be(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=Hi(this,e),this.trigger("mouseout",t)},touchstart:function(t){t=be(this.dom,t),Fi(t),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Ay.mousemove.call(this,t),Ay.mousedown.call(this,t)},touchmove:function(t){t=be(this.dom,t),Fi(t),this.handler.processGesture(t,"change"),Ay.mousemove.call(this,t)},touchend:function(t){t=be(this.dom,t),Fi(t),this.handler.processGesture(t,"end"),Ay.mouseup.call(this,t),+new Date-this._lastTouchMoment<Sy&&Ay.click.call(this,t)},pointerdown:function(t){Ay.mousedown.call(this,t)},pointermove:function(t){Bi(t)||Ay.mousemove.call(this,t)},pointerup:function(t){Ay.mouseup.call(this,t)},pointerout:function(t){Bi(t)||Ay.mouseout.call(this,t)}};f(["click","mousewheel","dblclick","contextmenu"],function(t){Ay[t]=function(e){e=be(this.dom,e),this.trigger(t,e)}});var Dy={pointermove:function(t){Bi(t)||Dy.mousemove.call(this,t)},pointerup:function(t){Dy.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;Ui(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}},ky=qi.prototype;ky.dispose=function(){Yi(this._localHandlerScope),My&&Yi(this._globalHandlerScope)},ky.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},c(qi,Yv);var Py=!Mv.canvasSupported,Ly={canvas:wy},Oy={},zy="4.3.2",Ey=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,r=new Hm,o=n.renderer;if(Py){if(!Ly.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");o="vml"}else o&&Ly[o]||(o="canvas");var a=new Ly[o](e,r,n,t);this.storage=r,this.painter=a;var s=Mv.node||Mv.worker?null:new qi(a.getViewportRoot(),a.root);this.handler=new rm(r,a,s,a.root),this.animation=new by({stage:{update:y(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,u=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(i)},r.addToStorage=function(t){u.call(r,t),t.addSelfToZr(i)}};Ey.prototype={constructor:Ey,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var n=this.painter.addHover(t,e);return this.refreshHover(),n}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()
},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,tr(this.id)}};var Ry=(Object.freeze||Object)({version:zy,init:$i,dispose:Ki,getInstance:Qi,registerPainter:Ji}),By=f,Ny=S,Fy=_,Vy="series\x00",Hy=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],Wy=0,Gy=".",Zy="___EC__COMPONENT__CONTAINER___",Xy=0,Yy=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,n,i){for(var r={},o=0;o<t.length;o++){var a=t[o][1];if(!(n&&u(n,a)>=0||i&&u(i,a)<0)){var s=e.getShallow(a);null!=s&&(r[t[o][0]]=s)}}return r}},Uy=Yy([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),jy={getLineStyle:function(t){var e=Uy(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"===e||null==e?!1:"dashed"===e?[i,i]:[n,n]}},qy=Yy([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),$y={getAreaStyle:function(t,e){return qy(this,t,e)}},Ky=Math.pow,Qy=Math.sqrt,Jy=1e-8,tx=1e-4,ex=Qy(3),nx=1/3,ix=H(),rx=H(),ox=H(),ax=Math.min,sx=Math.max,lx=Math.sin,ux=Math.cos,hx=2*Math.PI,cx=H(),dx=H(),fx=H(),px=[],gx=[],vx={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},mx=[],yx=[],xx=[],_x=[],bx=Math.min,Sx=Math.max,Mx=Math.cos,Ix=Math.sin,Cx=Math.sqrt,Tx=Math.abs,Ax="undefined"!=typeof Float32Array,Dx=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};Dx.prototype={constructor:Dx,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,n){n=n||0,this._ux=Tx(n/km/t)||0,this._uy=Tx(n/km/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(vx.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=Tx(t-this._xi)>this._ux||Tx(e-this._yi)>this._uy||this._len<5;return this.addData(vx.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,o){return this.addData(vx.C,t,e,n,i,r,o),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,o):this._ctx.bezierCurveTo(t,e,n,i,r,o)),this._xi=r,this._yi=o,this},quadraticCurveTo:function(t,e,n,i){return this.addData(vx.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,o){return this.addData(vx.A,t,e,n,n,i,r-i,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Mx(r)*n+t,this._yi=Ix(r)*n+e,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(vx.R,t,e,n,i),this},closePath:function(){this.addData(vx.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Ax||(this.data=new Float32Array(e));for(var n=0;e>n;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;e>r;r++)n+=t[r].len();Ax&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(var r=0;e>r;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,o=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,h=t-l,c=e-u,d=Cx(h*h+c*c),f=l,p=u,g=a.length;for(h/=d,c/=d,0>o&&(o=r+o),o%=r,f-=o*h,p-=o*c;h>0&&t>=f||0>h&&f>=t||0===h&&(c>0&&e>=p||0>c&&p>=e);)i=this._dashIdx,n=a[i],f+=h*n,p+=c*n,this._dashIdx=(i+1)%g,h>0&&l>f||0>h&&f>l||c>0&&u>p||0>c&&p>u||s[i%2?"moveTo":"lineTo"](h>=0?bx(f,t):Sx(f,t),c>=0?bx(p,e):Sx(p,e));h=f-t,c=p-e,this._dashOffset=-Cx(h*h+c*c)},_dashedBezierTo:function(t,e,n,i,r,o){var a,s,l,u,h,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,v=this._yi,m=Ir,y=0,x=this._dashIdx,_=f.length,w=0;for(0>d&&(d=c+d),d%=c,a=0;1>a;a+=.1)s=m(g,t,n,r,a+.1)-m(g,t,n,r,a),l=m(v,e,i,o,a+.1)-m(v,e,i,o,a),y+=Cx(s*s+l*l);for(;_>x&&(w+=f[x],!(w>d));x++);for(a=(w-d)/y;1>=a;)u=m(g,t,n,r,a),h=m(v,e,i,o,a),x%2?p.moveTo(u,h):p.lineTo(u,h),a+=f[x]/y,x=(x+1)%_;x%2!==0&&p.lineTo(r,o),s=r-u,l=o-h,this._dashOffset=-Cx(s*s+l*l)},_dashedQuadraticTo:function(t,e,n,i){var r=n,o=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,o)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Ax&&(this.data=new Float32Array(t)))},getBoundingRect:function(){mx[0]=mx[1]=xx[0]=xx[1]=Number.MAX_VALUE,yx[0]=yx[1]=_x[0]=_x[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,n=0,i=0,r=0,o=0;o<t.length;){var a=t[o++];switch(1===o&&(e=t[o],n=t[o+1],i=e,r=n),a){case vx.M:i=t[o++],r=t[o++],e=i,n=r,xx[0]=i,xx[1]=r,_x[0]=i,_x[1]=r;break;case vx.L:Nr(e,n,t[o],t[o+1],xx,_x),e=t[o++],n=t[o++];break;case vx.C:Fr(e,n,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],xx,_x),e=t[o++],n=t[o++];break;case vx.Q:Vr(e,n,t[o++],t[o++],t[o],t[o+1],xx,_x),e=t[o++],n=t[o++];break;case vx.A:var s=t[o++],l=t[o++],u=t[o++],h=t[o++],c=t[o++],d=t[o++]+c;o+=1;var f=1-t[o++];1===o&&(i=Mx(c)*u+s,r=Ix(c)*h+l),Hr(s,l,u,h,c,d,f,xx,_x),e=Mx(d)*u+s,n=Ix(d)*h+l;break;case vx.R:i=e=t[o++],r=n=t[o++];var p=t[o++],g=t[o++];Nr(i,r,i+p,r+g,xx,_x);break;case vx.Z:e=i,n=r}ae(mx,mx,xx),se(yx,yx,_x)}return 0===o&&(mx[0]=mx[1]=yx[0]=yx[1]=0),new Tn(mx[0],mx[1],yx[0]-mx[0],yx[1]-mx[1])},rebuildPath:function(t){for(var e,n,i,r,o,a,s=this.data,l=this._ux,u=this._uy,h=this._len,c=0;h>c;){var d=s[c++];switch(1===c&&(i=s[c],r=s[c+1],e=i,n=r),d){case vx.M:e=i=s[c++],n=r=s[c++],t.moveTo(i,r);break;case vx.L:o=s[c++],a=s[c++],(Tx(o-i)>l||Tx(a-r)>u||c===h-1)&&(t.lineTo(o,a),i=o,r=a);break;case vx.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case vx.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case vx.A:var f=s[c++],p=s[c++],g=s[c++],v=s[c++],m=s[c++],y=s[c++],x=s[c++],_=s[c++],w=g>v?g:v,b=g>v?1:g/v,S=g>v?v/g:1,M=Math.abs(g-v)>.001,I=m+y;M?(t.translate(f,p),t.rotate(x),t.scale(b,S),t.arc(0,0,w,m,I,1-_),t.scale(1/b,1/S),t.rotate(-x),t.translate(-f,-p)):t.arc(f,p,w,m,I,1-_),1===c&&(e=Mx(m)*g+f,n=Ix(m)*v+p),i=Mx(I)*g+f,r=Ix(I)*v+p;break;case vx.R:e=i=s[c],n=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case vx.Z:t.closePath(),i=e,r=n}}}},Dx.CMD=vx;var kx=2*Math.PI,Px=2*Math.PI,Lx=Dx.CMD,Ox=2*Math.PI,zx=1e-4,Ex=[-1,-1,-1],Rx=[-1,-1],Bx=Km.prototype.getCanvasPattern,Nx=Math.abs,Fx=new Dx(!0);no.prototype={constructor:no,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var n=this.style,i=this.path||Fx,r=n.hasStroke(),o=n.hasFill(),a=n.fill,s=n.stroke,l=o&&!!a.colorStops,u=r&&!!s.colorStops,h=o&&!!a.image,c=r&&!!s.image;if(n.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=n.getGradient(t,a,d)),u&&(d=d||this.getBoundingRect(),this._strokeGradient=n.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:h&&(t.fillStyle=Bx.call(a,t)),u?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=Bx.call(s,t));var f=n.lineDash,p=n.lineDashOffset,g=!!t.setLineDash,v=this.getGlobalScale();if(i.setScale(v[0],v[1],this.segmentIgnoreThreshold),this.__dirtyPath||f&&!g&&r?(i.beginPath(t),f&&!g&&(i.setLineDash(f),i.setLineDashOffset(p)),this.buildPath(i,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o)if(null!=n.fillOpacity){var m=t.globalAlpha;t.globalAlpha=n.fillOpacity*n.opacity,i.fill(t),t.globalAlpha=m}else i.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),r)if(null!=n.strokeOpacity){var m=t.globalAlpha;t.globalAlpha=n.strokeOpacity*n.opacity,i.stroke(t),t.globalAlpha=m}else i.stroke(t);f&&g&&t.setLineDash([]),null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(){},createPathProxy:function(){this.path=new Dx},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i||(i=this.path=new Dx),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var o=e.lineWidth,a=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),a>1e-10&&(r.width+=o/a,r.height+=o/a,r.x-=o/a/2,r.y-=o/a/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path.data;if(r.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),eo(o,a/s,t,e)))return!0}if(r.hasFill())return to(o,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Ai.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(S(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&Nx(t[0]-1)>1e-10&&Nx(t[3]-1)>1e-10?Math.sqrt(Nx(t[0]*t[3]-t[2]*t[1])):1}},no.extend=function(t){var e=function(e){no.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var i=this.shape;for(var r in n)!i.hasOwnProperty(r)&&n.hasOwnProperty(r)&&(i[r]=n[r])}t.init&&t.init.call(this,e)};h(e,no);for(var n in t)"style"!==n&&"shape"!==n&&(e.prototype[n]=t[n]);return e},h(no,Ai);var Vx=Dx.CMD,Hx=[[],[],[]],Wx=Math.sqrt,Gx=Math.atan2,Zx=function(t,e){var n,i,r,o,a,s,l=t.data,u=Vx.M,h=Vx.C,c=Vx.L,d=Vx.R,f=Vx.A,p=Vx.Q;for(r=0,o=0;r<l.length;){switch(n=l[r++],o=r,i=0,n){case u:i=1;break;case c:i=1;break;case h:i=3;break;case p:i=2;break;case f:var g=e[4],v=e[5],m=Wx(e[0]*e[0]+e[1]*e[1]),y=Wx(e[2]*e[2]+e[3]*e[3]),x=Gx(-e[1]/y,e[0]/m);l[r]*=m,l[r++]+=g,l[r]*=y,l[r++]+=v,l[r++]*=m,l[r++]*=y,l[r++]+=x,l[r++]+=x,r+=2,o=r;break;case d:s[0]=l[r++],s[1]=l[r++],oe(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],oe(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;i>a;a++){var s=Hx[a];s[0]=l[r++],s[1]=l[r++],oe(s,s,e),l[o++]=s[0],l[o++]=s[1]}}},Xx=Math.sqrt,Yx=Math.sin,Ux=Math.cos,jx=Math.PI,qx=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},$x=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(qx(t)*qx(e))},Kx=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos($x(t,e))},Qx=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Jx=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,t_=function(t){Ai.call(this,t)};t_.prototype={constructor:t_,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&hi(n,!0),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;return null!=i&&(i+=""),Ti(i,n)?(this.setTransform(t),di(this,t,i,n,null,e),void this.restoreTransform(t)):void(t.__attrCachedBy=Zm.NONE)},getBoundingRect:function(){var t=this.style;if(this.__dirty&&hi(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var n=Un(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,Si(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},h(t_,Ai);var e_=no.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),n_=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],i_=function(t){return Mv.browser.ie&&Mv.browser.version>=11?function(){var e,n=this.__clipPaths,i=this.style;if(n)for(var r=0;r<n.length;r++){var o=n[r],a=o&&o.shape,s=o&&o.type;if(a&&("sector"===s&&a.startAngle===a.endAngle||"rect"===s&&(!a.width||!a.height))){for(var l=0;l<n_.length;l++)n_[l][2]=i[n_[l][0]],i[n_[l][0]]=n_[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<n_.length;l++)i[n_[l][0]]=n_[l][2]}:t},r_=no.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:i_(no.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(a),h=Math.sin(a);t.moveTo(u*r+n,h*r+i),t.lineTo(u*o+n,h*o+i),t.arc(n,i,o,a,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,a,l),t.closePath()}}),o_=no.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}}),a_=function(t,e){for(var n=t.length,i=[],r=0,o=1;n>o;o++)r+=ee(t[o-1],t[o]);var a=r/2;a=n>a?n:a;for(var o=0;a>o;o++){var s,l,u,h=o/(a-1)*(e?n:n-1),c=Math.floor(h),d=h-c,f=t[c%n];e?(s=t[(c-1+n)%n],l=t[(c+1)%n],u=t[(c+2)%n]):(s=t[0===c?c:c-1],l=t[c>n-2?n-1:c+1],u=t[c>n-3?n-1:c+2]);var p=d*d,g=d*p;i.push([uo(s[0],f[0],l[0],u[0],d,p,g),uo(s[1],f[1],l[1],u[1],d,p,g)])}return i},s_=function(t,e,n,i){var r,o,a,s,l=[],u=[],h=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;f>d;d++)ae(a,a,t[d]),se(s,s,t[d]);ae(a,a,i[0]),se(s,s,i[1])}for(var d=0,f=t.length;f>d;d++){var p=t[d];if(n)r=t[d?d-1:f-1],o=t[(d+1)%f];else{if(0===d||d===f-1){l.push(G(t[d]));continue}r=t[d-1],o=t[d+1]}U(u,o,r),J(u,u,e);var g=ee(p,r),v=ee(p,o),m=g+v;0!==m&&(g/=m,v/=m),J(h,u,-g),J(c,u,v);var y=X([],p,h),x=X([],p,c);i&&(se(y,y,a),ae(y,y,s),se(x,x,a),ae(x,x,s)),l.push(y),l.push(x)}return n&&l.push(l.shift()),l},l_=no.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){ho(t,e,!0)}}),u_=no.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){ho(t,e,!1)}}),h_=Math.round,c_={},d_=no.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n,i,r,o;this.subPixelOptimize?(fo(c_,e,this.style),n=c_.x,i=c_.y,r=c_.width,o=c_.height,c_.r=e.r,e=c_):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?ui(t,e):t.rect(n,i,r,o),t.closePath()}}),f_={},p_=no.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n,i,r,o;this.subPixelOptimize?(co(f_,e,this.style),n=f_.x1,i=f_.y1,r=f_.x2,o=f_.y2):(n=e.x1,i=e.y1,r=e.x2,o=e.y2);var a=e.percent;0!==a&&(t.moveTo(n,i),1>a&&(r=n*(1-a)+r*a,o=i*(1-a)+o*a),t.lineTo(r,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),g_=[],v_=no.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(1>h&&(Er(n,a,r,h,g_),a=g_[1],r=g_[2],Er(i,s,o,h,g_),s=g_[1],o=g_[2]),t.quadraticCurveTo(a,s,r,o)):(1>h&&(Dr(n,a,l,r,h,g_),a=g_[1],l=g_[2],r=g_[3],Dr(i,s,u,o,h,g_),s=g_[1],u=g_[2],o=g_[3]),t.bezierCurveTo(a,s,l,u,r,o)))},pointAt:function(t){return go(this.shape,t,!1)},tangentAt:function(t){var e=go(this.shape,t,!0);return te(e,e)}}),m_=no.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)}}),y_=no.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),no.prototype.getBoundingRect.call(this)}}),x_=function(t){this.colorStops=t||[]};x_.prototype={constructor:x_,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var __=function(t,e,n,i,r,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,x_.call(this,r)};__.prototype={constructor:__},h(__,x_);var w_=function(t,e,n,i,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=r||!1,x_.call(this,i)};w_.prototype={constructor:w_},h(w_,x_),vo.prototype.incremental=!0,vo.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},vo.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},vo.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},vo.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},vo.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},vo.prototype.brush=function(t){for(var e=this._cursor;e<this._displayables.length;e++){var n=this._displayables[e];n.beforeBrush&&n.beforeBrush(t),n.brush(t,e===this._cursor?null:this._displayables[e-1]),n.afterBrush&&n.afterBrush(t)}this._cursor=e;for(var e=0;e<this._temporaryDisplayables.length;e++){var n=this._temporaryDisplayables[e];n.beforeBrush&&n.beforeBrush(t),n.brush(t,0===e?null:this._temporaryDisplayables[e-1]),n.afterBrush&&n.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var b_=[];vo.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Tn(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(b_)),t.union(i)}this._rect=t}return this._rect},vo.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();if(i.contain(n[0],n[1]))for(var r=0;r<this._displayables.length;r++){var o=this._displayables[r];if(o.contain(t,e))return!0}return!1},h(vo,Ai);var S_=Math.max,M_=Math.min,I_={},C_=1,T_={color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},A_="emphasis",D_="normal",k_=1,P_={},L_={},O_=lo,z_=po,E_=N(),R_=0;xo("circle",e_),xo("sector",r_),xo("ring",o_),xo("polygon",l_),xo("polyline",u_),xo("rect",d_),xo("line",p_),xo("bezierCurve",v_),xo("arc",m_);var B_=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:C_,CACHED_LABEL_STYLE_PROPERTIES:T_,extendShape:mo,extendPath:yo,registerShape:xo,getShapeClass:_o,makePath:wo,makeImage:bo,mergePath:O_,resizePath:Mo,subPixelOptimizeLine:Io,subPixelOptimizeRect:Co,subPixelOptimize:z_,setElementHoverStyle:zo,setHoverStyle:Vo,setAsHighDownDispatcher:Ho,isHighDownDispatcher:Wo,getHighlightDigit:Go,setLabelStyle:Zo,modifyLabelStyle:Xo,setTextStyle:Yo,setText:Uo,getFont:ta,updateProps:na,initProps:ia,getTransform:ra,applyTransform:oa,transformDirection:aa,groupTransition:sa,clipPointsByRect:la,clipRectByRect:ua,createIcon:ha,linePolygonIntersect:ca,lineLineIntersect:da,Group:Nm,Image:Di,Text:t_,Circle:e_,Sector:r_,Ring:o_,Polygon:l_,Polyline:u_,Rect:d_,Line:p_,BezierCurve:v_,Arc:m_,IncrementalDisplayable:vo,CompoundPath:y_,LinearGradient:__,RadialGradient:w_,BoundingRect:Tn}),N_=["textStyle","color"],F_={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(N_):null)},getFont:function(){return ta({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return Un(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},V_=Yy([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),H_={getItemStyle:function(t,e){var n=V_(this,t,e),i=this.getBorderLineDash();return i&&(n.lineDash=i),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},W_=c,G_=hr();ga.prototype={constructor:ga,init:null,mergeOption:function(t){r(this.option,t,!0)},get:function(t,e){return null==t?this.option:va(this.option,this.parsePath(t),!e&&ma(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&ma(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n,i=null==t?this.option:va(this.option,t=this.parsePath(t));return e=e||(n=ma(this,t))&&n.getModel(t),new ga(i,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){G_(this).getParent=t},isAnimationEnabled:function(){if(!Mv.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},yr(ga),xr(ga),W_(ga,jy),W_(ga,$y),W_(ga,F_),W_(ga,H_);var Z_=0,X_=1e-4,Y_=9007199254740991,U_=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,j_=(Object.freeze||Object)({linearMap:ba,parsePercent:Sa,round:Ma,asc:Ia,getPrecision:Ca,getPrecisionSafe:Ta,getPixelPrecision:Aa,getPercentWithPrecision:Da,MAX_SAFE_INTEGER:Y_,remRadian:ka,isRadianAroundZero:Pa,parseDate:La,quantity:Oa,quantityExponent:za,nice:Ea,quantile:Ra,reformIntervals:Ba,isNumeric:Na}),q_=L,$_=/([&<>"'])/g,K_={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Q_=["a","b","c","d","e","f","g"],J_=function(t,e){return"{"+t+(null==e?"":e)+"}"},tw=Jn,ew=(Object.freeze||Object)({addCommas:Fa,toCamelCase:Va,normalizeCssArray:q_,encodeHTML:Ha,formatTpl:Wa,formatTplSimple:Ga,getTooltipMarker:Za,formatTime:Ya,capitalFirst:Ua,truncateText:tw,getTextBoundingRect:ja,getTextRect:qa,windowOpen:$a}),nw=f,iw=["left","right","top","bottom","width","height"],rw=[["width","left","right"],["height","top","bottom"]],ow=Ka,aw=(x(Ka,"vertical"),x(Ka,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),sw=hr(),lw=ga.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){ga.call(this,t,e,n,i),this.uid=ya("ec_cpt_model")},init:function(t,e,n){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?es(t):{},o=e.getTheme();r(t,o.get(this.mainType)),r(t,this.getDefaultOption()),n&&ts(t,i,n)},mergeOption:function(t){r(this.option,t,!0);var e=this.layoutMode;e&&ts(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){var t=sw(this);if(!t.defaultOption){for(var e=[],n=this.constructor;n;){var i=n.prototype.defaultOption;i&&e.push(i),n=n.superClass}for(var o={},a=e.length-1;a>=0;a--)o=r(o,e[a],!0);t.defaultOption=o}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});br(lw,{registerWhenExtend:!0}),xa(lw),_a(lw,is),c(lw,aw);var uw="";"undefined"!=typeof navigator&&(uw=navigator.platform||"");var hw={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:uw.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},cw=hr(),dw={clearColorPalette:function(){cw(this).colorIdx=0,cw(this).colorNameMap={}},getColorFromPalette:function(t,e,n){e=e||this;var i=cw(e),r=i.colorIdx||0,o=i.colorNameMap=i.colorNameMap||{};if(o.hasOwnProperty(t))return o[t];var a=er(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=n&&s?rs(s,n):a;if(l=l||a,l&&l.length){var u=l[r];return t&&(o[t]=u),i.colorIdx=(r+1)%l.length,u}}},fw="original",pw="arrayRows",gw="objectRows",vw="keyedColumns",mw="unknown",yw="typedArray",xw="column",_w="row";os.seriesDataToSource=function(t){return new os({data:t,sourceFormat:I(t)?yw:fw,fromDataset:!1})},xr(os);var ww={Must:1,Might:2,Not:3},bw=hr(),Sw="\x00_ec_inner",Mw=ga.extend({init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new ga(n),this._optionManager=i},setOption:function(t,e){O(!(Sw in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):ws.call(this,i),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=n.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o.length&&f(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,i){var r=er(t[e]),s=or(o.get(e),r);ar(s),f(s,function(t){var n=t.option;S(n)&&(t.keyInfo.mainType=e,t.keyInfo.subType=Ss(e,n,t.exist))});var l=bs(o,i);n[e]=[],o.set(e,[]),f(s,function(t,i){var r=t.exist,s=t.option;if(O(S(s)||r,"Empty component definition"),s){var u=lw.getClass(e,t.keyInfo.subType,!0);if(r&&r.constructor===u)r.name=t.keyInfo.name,r.mergeOption(s,this),r.optionUpdated(s,!1);else{var h=a({dependentModels:l,componentIndex:i},t.keyInfo);r=new u(s,this,this,h),a(r,h),r.init(s,this,this,h),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);o.get(e)[i]=r,n[e][i]=r.option},this),"series"===e&&Ms(this,o.get("series"))}var n=this.option,o=this._componentsMap,s=[];ls(this),f(t,function(t,e){null!=t&&(lw.hasClass(e)?e&&s.push(e):n[e]=null==n[e]?i(t):r(n[e],t,!0))}),lw.topologicalTravel(s,lw.getAllClassMainTypes(),e,this),this._seriesIndicesMap=N(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=i(this.option);return f(t,function(e,n){if(lw.hasClass(n)){for(var e=er(e),i=e.length-1;i>=0;i--)lr(e[i])&&e.splice(i,1);t[n]=e}}),delete t[Sw],t},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);return n?n[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n=t.index,i=t.id,r=t.name,o=this._componentsMap.get(e);if(!o||!o.length)return[];var a;if(null!=n)_(n)||(n=[n]),a=v(p(n,function(t){return o[t]}),function(t){return!!t});else if(null!=i){var s=_(i);a=v(o,function(t){return s&&u(i,t.id)>=0||!s&&t.id===i})}else if(null!=r){var l=_(r);a=v(o,function(t){return l&&u(r,t.name)>=0||!l&&t.name===r})}else a=o.slice();return Is(a,t)},findComponents:function(t){function e(t){var e=r+"Index",n=r+"Id",i=r+"Name";return!t||null==t[e]&&null==t[n]&&null==t[i]?null:{mainType:r,index:t[e],id:t[n],name:t[i]}}function n(e){return t.filter?v(e,t.filter):e}var i=t.query,r=t.mainType,o=e(i),a=o?this.queryComponents(o):this._componentsMap.get(r);return n(Is(a,t))},eachComponent:function(t,e,n){var i=this._componentsMap;if("function"==typeof t)n=e,e=t,i.each(function(t,i){f(t,function(t,r){e.call(n,i,t,r)})});else if(b(t))f(i.get(t),e,n);else if(S(t)){var r=this.findComponents(t);f(r,e,n)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){Cs(this),f(this._seriesIndices,function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)},this)},eachRawSeries:function(t,e){f(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,n){Cs(this),f(this._seriesIndices,function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)},this)},eachRawSeriesByType:function(t,e,n){return f(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return Cs(this),null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){Cs(this);var n=v(this._componentsMap.get("series"),t,e);Ms(this,n)},restoreData:function(t){var e=this._componentsMap;Ms(this,e.get("series"));var n=[];e.each(function(t,e){n.push(e)}),lw.topologicalTravel(n,lw.getAllClassMainTypes(),function(n){f(e.get(n),function(e){("series"!==n||!xs(e,t))&&e.restoreData()})})}});c(Mw,dw);var Iw=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],Cw={};
As.prototype={constructor:As,create:function(t,e){var n=[];f(Cw,function(i){var r=i.create(t,e);n=n.concat(r||[])}),this._coordinateSystems=n},update:function(t,e){f(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},As.register=function(t,e){Cw[t]=e},As.get=function(t){return Cw[t]};var Tw=f,Aw=i,Dw=p,kw=r,Pw=/^(min|max)?(.+)$/;Ds.prototype={constructor:Ds,setOption:function(t,e){t&&f(er(t.series),function(t){t&&t.data&&I(t.data)&&E(t.data)}),t=Aw(t);var n=this._optionBackup,i=ks.call(this,t,e,!n);this._newBaseOption=i.baseOption,n?(zs(n.baseOption,i.baseOption),i.timelineOptions.length&&(n.timelineOptions=i.timelineOptions),i.mediaList.length&&(n.mediaList=i.mediaList),i.mediaDefault&&(n.mediaDefault=i.mediaDefault)):this._optionBackup=i},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=Dw(e.timelineOptions,Aw),this._mediaList=Dw(e.mediaList,Aw),this._mediaDefault=Aw(e.mediaDefault),this._currentMediaIndices=[],Aw(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=Aw(n[i.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),n=this._mediaList,i=this._mediaDefault,r=[],o=[];if(!n.length&&!i)return o;for(var a=0,s=n.length;s>a;a++)Ps(n[a].query,t,e)&&r.push(a);return!r.length&&i&&(r=[-1]),r.length&&!Os(r,this._currentMediaIndices)&&(o=Dw(r,function(t){return Aw(-1===t?i.option:n[t].option)})),this._currentMediaIndices=r,o}};var Lw=f,Ow=S,zw=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],Ew=function(t,e){Lw(Hs(t.series),function(t){Ow(t)&&Vs(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),Lw(n,function(e){Lw(Hs(t[e]),function(t){t&&(Ns(t,"axisLabel"),Ns(t.axisPointer,"label"))})}),Lw(Hs(t.parallel),function(t){var e=t&&t.parallelAxisDefault;Ns(e,"axisLabel"),Ns(e&&e.axisPointer,"label")}),Lw(Hs(t.calendar),function(t){Rs(t,"itemStyle"),Ns(t,"dayLabel"),Ns(t,"monthLabel"),Ns(t,"yearLabel")}),Lw(Hs(t.radar),function(t){Ns(t,"name")}),Lw(Hs(t.geo),function(t){Ow(t)&&(Fs(t),Lw(Hs(t.regions),function(t){Fs(t)}))}),Lw(Hs(t.timeline),function(t){Fs(t),Rs(t,"label"),Rs(t,"itemStyle"),Rs(t,"controlStyle",!0);var e=t.data;_(e)&&f(e,function(t){S(t)&&(Rs(t,"label"),Rs(t,"itemStyle"))})}),Lw(Hs(t.toolbox),function(t){Rs(t,"iconStyle"),Lw(t.feature,function(t){Rs(t,"iconStyle")})}),Ns(Ws(t.axisPointer),"label"),Ns(Ws(t.tooltip).axisPointer,"label")},Rw=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Bw=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Nw=function(t,e){Ew(t,e),t.series=er(t.series),f(t.series,function(t){if(S(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){var n=Gs(t,"pointer.color");null!=n&&Zs(t,"itemStyle.color",n)}Xs(t)}}),t.dataRange&&(t.visualMap=t.dataRange),f(Bw,function(e){var n=t[e];n&&(_(n)||(n=[n]),f(n,function(t){Xs(t)}))})},Fw=function(t){var e=N();t.eachSeries(function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}}),e.each(Ys)},Vw=Us.prototype;Vw.pure=!1,Vw.persistent=!0,Vw.getSource=function(){return this._source};var Hw={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:$s},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var r=n[i];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:js,getItem:qs,appendData:$s},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var r=this._data[n[i].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;f(t,function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])})}},original:{count:js,getItem:qs,appendData:$s},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){_v&&O(I(t),"Added data must be TypedArray if data in initialization is TypedArray"),this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}},Ww={arrayRows:Ks,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:Ks,original:function(t,e,n){var i=ir(t);return null!=n&&i instanceof Array?i[n]:i},typedArray:Ks},Gw={arrayRows:Qs,objectRows:function(t,e){return Js(t[e],this._dimensionInfos[e])},keyedColumns:Qs,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&rr(t)&&(this.hasItemOption=!0),Js(r instanceof Array?r[i]:r,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}},Zw=/\{@(.+?)\}/g,Xw={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"color"),l=n.getItemVisual(t,"borderColor"),u=this.ecModel.getComponent("tooltip"),h=u&&u.get("renderMode"),c=gr(h),d=this.mainType,f="series"===d,p=n.userOutput;return{componentType:d,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:f?this.subType:null,seriesIndex:this.seriesIndex,seriesId:f?this.id:null,seriesName:f?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:s,borderColor:l,dimensionNames:p?p.dimensionNames:null,encode:p?p.encode:null,marker:Za({color:s,renderMode:c}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,i,r){e=e||"normal";var o=this.getData(n),a=o.getItemModel(t),s=this.getDataParams(t,n);null!=i&&s.value instanceof Array&&(s.value=s.value[i]);var l=a.get("normal"===e?[r||"label","formatter"]:[e,r||"label","formatter"]);if("function"==typeof l)return s.status=e,s.dimensionIndex=i,l(s);if("string"==typeof l){var u=Wa(l,s);return u.replace(Zw,function(e,n){var i=n.length;return"["===n.charAt(0)&&"]"===n.charAt(i-1)&&(n=+n.slice(1,i-1)),tl(o,t,n)})}},getRawValue:function(t,e){return tl(this.getData(e),t)},formatTooltip:function(){}},Yw=il.prototype;Yw.perform=function(t){function e(t){return!(t>=1)&&(t=1),t}var n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var o;this._plan&&!i&&(o=this._plan(this.context));var a=e(this._modBy),s=this._modDataCount||0,l=e(t&&t.modBy),u=t&&t.modDataCount||0;(a!==l||s!==u)&&(o="reset");var h;(this._dirty||"reset"===o)&&(this._dirty=!1,h=ol(this,i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(n?(_v&&O(null!=n._outputDueEnd),this._dueEnd=n._outputDueEnd):(_v&&O(!this._progress||this._count),this._dueEnd=this._count?this._count(this.context):1/0),this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||f>d)){var p=this._progress;if(_(p))for(var g=0;g<p.length;g++)rl(this,p[g],d,f,l,u);else rl(this,p,d,f,l,u)}this._dueIndex=f;var v=null!=this._settedOutputEnd?this._settedOutputEnd:f;_v&&O(v>=this._outputDueEnd),this._outputDueEnd=v}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var Uw=function(){function t(){return n>i?i++:null}function e(){var t=i%a*r+Math.ceil(i/a),e=i>=n?null:o>t?t:i;return i++,e}var n,i,r,o,a,s={reset:function(l,u,h,c){i=l,n=u,r=h,o=c,a=Math.ceil(o/r),s.next=r>1&&o>0?e:t}};return s}();Yw.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Yw.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Yw.pipe=function(t){_v&&O(t&&!t._disposed&&t!==this),(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},Yw.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Yw.getUpstream=function(){return this._upstream},Yw.getDownstream=function(){return this._downstream},Yw.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var jw=hr(),qw=lw.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=nl({count:ll,reset:ul}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),us(this);var i=this.getInitialData(t,n);cl(i,this),this.dataTask.context.data=i,_v&&O(i,"getInitialData returned invalid data."),jw(this).dataBeforeProcessed=i,al(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?es(t):{},o=this.subType;lw.hasClass(o)&&(o+="Series"),r(t,e.getTheme().get(this.subType)),r(t,this.getDefaultOption()),nr(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&ts(t,i,n)},mergeOption:function(t,e){t=r(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&ts(this.option,t,n),us(this);var i=this.getInitialData(t,e);cl(i,this),this.dataTask.dirty(),this.dataTask.context.data=i,jw(this).dataBeforeProcessed=i,al(this)},fillDataTextStyle:function(t){if(t&&!I(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&nr(t[n],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=fl(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return jw(this).data},setData:function(t){var e=fl(this);if(e){var n=e.context;n.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),n.outputData=t,e!==this.dataTask&&(n.data=t)}jw(this).data=t},getSource:function(){return ss(this)},getRawData:function(){return jw(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,n,i){function r(n){function r(t,n){var r=c.getDimensionInfo(n);if(r&&r.otherDims.tooltip!==!1){var d=r.type,f="sub"+a.seriesIndex+"at"+h,p=Za({color:y,type:"subItem",renderMode:i,markerId:f}),g="string"==typeof p?p:p.content,v=(o?g+Ha(r.displayName||"-")+": ":"")+Ha("ordinal"===d?t+"":"time"===d?e?"":Ya("yyyy/MM/dd hh:mm:ss",t):Fa(t));v&&s.push(v),l&&(u[f]=y,++h)}}var o=g(n,function(t,e,n){var i=c.getDimensionInfo(n);return t|=i&&i.tooltip!==!1&&null!=i.displayName},0),s=[];d.length?f(d,function(e){r(tl(c,t,e),e)}):f(n,r);var p=o?l?"\n":"<br/>":"",v=p+s.join(p||", ");return{renderMode:i,content:v,style:u}}function o(t){return{renderMode:i,content:Ha(Fa(t)),style:u}}var a=this;i=i||"html";var s="html"===i?"<br/>":"\n",l="richText"===i,u={},h=0,c=this.getData(),d=c.mapDimension("defaultedTooltip",!0),p=d.length,v=this.getRawValue(t),m=_(v),y=c.getItemVisual(t,"color");S(y)&&y.colorStops&&(y=(y.colorStops[0]||{}).color),y=y||"transparent";var x=p>1||m&&!p?r(v):o(p?tl(c,t,d[0]):m?v[0]:v),w=x.content,b=a.seriesIndex+"at"+h,M=Za({color:y,type:"item",renderMode:i,markerId:b});u[b]=y,++h;var I=c.getName(t),C=this.name;sr(this)||(C=""),C=C?Ha(C)+(e?": ":s):"";var T="string"==typeof M?M:M.content,A=e?T+C+w:C+T+(I?Ha(I)+": "+w:w);return{html:A,markers:u}},isAnimationEnabled:function(){if(Mv.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel,r=dw.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});c(qw,Xw),c(qw,dw);var $w=function(){this.group=new Nm,this.uid=ya("viewComponent")};$w.prototype={constructor:$w,init:function(){},render:function(){},dispose:function(){},filterForExposedEvent:null};var Kw=$w.prototype;Kw.updateView=Kw.updateLayout=Kw.updateVisual=function(){},yr($w),br($w,{registerWhenExtend:!0});var Qw=function(){var t=hr();return function(e){var n=t(e),i=e.pipelineContext,r=n.large,o=n.progressiveRender,a=n.large=i&&i.large,s=n.progressiveRender=i&&i.progressiveRender;return!!(r^a||o^s)&&"reset"}},Jw=hr(),tb=Qw();pl.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,n,i){vl(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){vl(t.getData(),i,"normal")},remove:function(){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var eb=pl.prototype;eb.updateView=eb.updateLayout=eb.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},yr(pl,["dispose"]),br(pl,{registerWhenExtend:!0}),pl.markUpdateMethod=function(t,e){Jw(t).updateMethod=e};var nb={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},ib="\x00__throttleOriginMethod",rb="\x00__throttleRate",ob="\x00__throttleType",ab={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=(t.visualColorAccessPath||"itemStyle.color").split("."),r=t.get(i),o=!w(r)||r instanceof x_?null:r;(!r||o)&&(r=t.getColorFromPalette(t.name,null,e.getSeriesCount())),n.setVisual("color",r);var a=(t.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),s=t.get(a);if(n.setVisual("borderColor",s),!e.isSeriesFiltered(t)){o&&n.each(function(e){n.setItemVisual(e,"color",o(t.getDataParams(e)))});var l=function(t,e){var n=t.getItemModel(e),r=n.get(i,!0),o=n.get(a,!0);null!=r&&t.setItemVisual(e,"color",r),null!=o&&t.setItemVisual(e,"borderColor",o)};return{dataEach:n.hasItemOption?l:null}}}},sb={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},lb=function(t,e){function n(t,e){if("string"!=typeof t)return t;var n=t;return f(e,function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),n}function i(t){var e=a.get(t);if(null==e){for(var n=t.split("."),i=sb.aria,r=0;r<n.length;++r)i=i[n[r]];return i}return e}function r(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function o(t){return sb.series.typeNames[t]||"自定义图"}var a=e.getModel("aria");if(a.get("show")){if(a.get("description"))return void t.setAttribute("aria-label",a.get("description"));var s=0;e.eachSeries(function(){++s},this);var l,u=a.get("data.maxCount")||10,h=a.get("series.maxCount")||10,c=Math.min(s,h);if(!(1>s)){var d=r();l=d?n(i("general.withTitle"),{title:d}):i("general.withoutTitle");var p=[],g=s>1?"series.multiple.prefix":"series.single.prefix";l+=n(i(g),{seriesCount:s}),e.eachSeries(function(t,e){if(c>e){var r,a=t.get("name"),l="series."+(s>1?"multiple":"single")+".";r=i(a?l+"withName":l+"withoutName"),r=n(r,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:o(t.subType)});var h=t.getData();window.data=h,r+=h.count()>u?n(i("data.partialData"),{displayCnt:u}):i("data.allData");for(var d=[],f=0;f<h.count();f++)if(u>f){var g=h.getName(f),v=tl(h,f);d.push(n(i(g?"data.withName":"data.withoutName"),{name:g,value:v}))}r+=d.join(i("data.separator.middle"))+i("data.separator.end"),p.push(r)}}),l+=p.join(i("series.multiple.separator.middle"))+i("series.multiple.separator.end"),t.setAttribute("aria-label",l)}}},ub=Math.PI,hb=function(t,e){e=e||{},s(e,{text:"loading",textColor:"#000",fontSize:"12px",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#c23531",spinnerRadius:10,lineWidth:5,zlevel:0});var n=new Nm,i=new d_({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r=e.fontSize+" sans-serif",o=new d_({style:{fill:"none",text:e.text,font:r,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});if(n.add(o),e.showSpinner){var a=new m_({shape:{startAngle:-ub/2,endAngle:-ub/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001});a.animateShape(!0).when(1e3,{endAngle:3*ub/2}).start("circularInOut"),a.animateShape(!0).when(1e3,{startAngle:3*ub/2}).delay(300).start("circularInOut"),n.add(a)}return n.resize=function(){var n=Yn(e.text,r),s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner?0:n/2),u=t.getHeight()/2;e.showSpinner&&a.setShape({cx:l,cy:u}),o.setShape({x:l-s,y:u-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n},cb=bl.prototype;cb.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},cb.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex,o=r?n.step:null,a=i&&i.modDataCount,s=null!=a?Math.ceil(a/o):null;return{step:o,modBy:s,modDataCount:a}}},cb.getPipeline=function(t){return this._pipelineMap.get(t)},cb.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData(),r=i.count(),o=n.progressiveEnabled&&e.incrementalPrepareRender&&r>=n.threshold,a=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=n.context={progressiveRender:o,modDataCount:s,large:a}},cb.restorePipelines=function(t){var e=this,n=e._pipelineMap=N();t.eachSeries(function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),zl(e,t,t.dataTask)})},cb.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),n=this.api;f(this._allHandlers,function(i){var r=t.get(i.uid)||t.set(i.uid,[]);i.reset&&Ml(this,i,r,e,n),i.overallReset&&Il(this,i,r,e,n)},this)},cb.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,zl(this,e,r)},cb.performDataProcessorTasks=function(t,e){Sl(this,this._dataProcessorHandlers,t,e,{block:!0})},cb.performVisualTasks=function(t,e,n){Sl(this,this._visualHandlers,t,e,n)},cb.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},cb.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var db=cb.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},fb=Ll(0);bl.wrapStageHandler=function(t,e){return w(t)&&(t={overallReset:t,seriesType:El(t)}),t.uid=ya("stageHandler"),e&&(t.visualType=e),t};var pb,gb={},vb={};Rl(gb,Mw),Rl(vb,Ts),gb.eachSeriesByType=gb.eachRawSeriesByType=function(t){pb=t},gb.eachComponent=function(t){"series"===t.mainType&&t.subType&&(pb=t.subType)};var mb=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],yb={color:mb,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],mb]},xb="#eee",_b=function(){return{axisLine:{lineStyle:{color:xb}},axisTick:{lineStyle:{color:xb}},axisLabel:{textStyle:{color:xb}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:xb}}}},wb=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],bb={color:wb,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:xb},crossStyle:{color:xb},label:{color:"#000"}}},legend:{textStyle:{color:xb}},textStyle:{color:xb},title:{textStyle:{color:xb}},toolbox:{iconStyle:{normal:{borderColor:xb}}},dataZoom:{textStyle:{color:xb}},visualMap:{textStyle:{color:xb}},timeline:{lineStyle:{color:xb},itemStyle:{normal:{color:wb[1]}},label:{normal:{textStyle:{color:xb}}},controlStyle:{normal:{color:xb,borderColor:xb}}},timeAxis:_b(),logAxis:_b(),valueAxis:_b(),categoryAxis:_b(),line:{symbol:"circle"},graph:{color:wb},gauge:{title:{textStyle:{color:xb}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};bb.categoryAxis.splitLine.show=!1,lw.extend({type:"dataset",defaultOption:{seriesLayoutBy:xw,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){as(this)}}),$w.extend({type:"dataset"});var Sb=no.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var n=.5522848,i=e.cx,r=e.cy,o=e.rx,a=e.ry,s=o*n,l=a*n;t.moveTo(i-o,r),t.bezierCurveTo(i-o,r-l,i-s,r-a,i,r-a),t.bezierCurveTo(i+s,r-a,i+o,r-l,i+o,r),t.bezierCurveTo(i+o,r+l,i+s,r+a,i,r+a),t.bezierCurveTo(i-s,r+a,i-o,r+l,i-o,r),t.closePath()}}),Mb=/[\s,]+/;Nl.prototype.parse=function(t,e){e=e||{};var n=Bl(t);if(!n)throw new Error("Illegal svg");var i=new Nm;this._root=i;var r=n.getAttribute("viewBox")||"",o=parseFloat(n.getAttribute("width")||e.width),a=parseFloat(n.getAttribute("height")||e.height);isNaN(o)&&(o=null),isNaN(a)&&(a=null),Wl(n,i,null,!0);for(var s=n.firstChild;s;)this._parseNode(s,i),s=s.nextSibling;var l,u;if(r){var h=z(r).split(Mb);h.length>=4&&(l={x:parseFloat(h[0]||0),y:parseFloat(h[1]||0),width:parseFloat(h[2]),height:parseFloat(h[3])})}if(l&&null!=o&&null!=a&&(u=Yl(l,o,a),!e.ignoreViewBox)){var c=i;i=new Nm,i.add(c),c.scale=u.scale.slice(),c.position=u.position.slice()}return e.ignoreRootClip||null==o||null==a||i.setClipPath(new d_({shape:{x:0,y:0,width:o,height:a}})),{root:i,width:o,height:a,viewBoxRect:l,viewBoxTransform:u}},Nl.prototype._parseNode=function(t,e){var n=t.nodeName.toLowerCase();"defs"===n?this._isDefine=!0:"text"===n&&(this._isText=!0);var i;if(this._isDefine){var r=Cb[n];if(r){var o=r.call(this,t),a=t.getAttribute("id");a&&(this._defs[a]=o)}}else{var r=Ib[n];r&&(i=r.call(this,t,e),e.add(i))}for(var s=t.firstChild;s;)1===s.nodeType&&this._parseNode(s,i),3===s.nodeType&&this._isText&&this._parseText(s,i),s=s.nextSibling;"defs"===n?this._isDefine=!1:"text"===n&&(this._isText=!1)},Nl.prototype._parseText=function(t,e){if(1===t.nodeType){var n=t.getAttribute("dx")||0,i=t.getAttribute("dy")||0;this._textX+=parseFloat(n),this._textY+=parseFloat(i)}var r=new t_({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});Vl(e,r),Wl(t,r,this._defs);var o=r.style.fontSize;o&&9>o&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=o/9,r.scale[1]*=o/9);var a=r.getBoundingRect();return this._textX+=a.width,e.add(r),r};var Ib={g:function(t,e){var n=new Nm;return Vl(e,n),Wl(t,n,this._defs),n},rect:function(t,e){var n=new d_;return Vl(e,n),Wl(t,n,this._defs),n.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),n},circle:function(t,e){var n=new e_;return Vl(e,n),Wl(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),n},line:function(t,e){var n=new p_;return Vl(e,n),Wl(t,n,this._defs),n.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),n},ellipse:function(t,e){var n=new Sb;return Vl(e,n),Wl(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),n},polygon:function(t,e){var n=t.getAttribute("points");n&&(n=Hl(n));var i=new l_({shape:{points:n||[]}});return Vl(e,i),Wl(t,i,this._defs),i},polyline:function(t,e){var n=new no;Vl(e,n),Wl(t,n,this._defs);var i=t.getAttribute("points");i&&(i=Hl(i));var r=new u_({shape:{points:i||[]}});return r},image:function(t,e){var n=new Di;return Vl(e,n),Wl(t,n,this._defs),n.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),n},text:function(t,e){var n=t.getAttribute("x")||0,i=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0;this._textX=parseFloat(n)+parseFloat(r),this._textY=parseFloat(i)+parseFloat(o);var a=new Nm;return Vl(e,a),Wl(t,a,this._defs),a},tspan:function(t,e){var n=t.getAttribute("x"),i=t.getAttribute("y");null!=n&&(this._textX=parseFloat(n)),null!=i&&(this._textY=parseFloat(i));var r=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0,a=new Nm;return Vl(e,a),Wl(t,a,this._defs),this._textX+=r,this._textY+=o,a},path:function(t,e){var n=t.getAttribute("d")||"",i=ao(n);return Vl(e,i),Wl(t,i,this._defs),i}},Cb={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),n=parseInt(t.getAttribute("y1")||0,10),i=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),o=new __(e,n,i,r);return Fl(t,o),o},radialgradient:function(){}},Tb={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"},Ab=/url\(\s*#(.*?)\)/,Db=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g,kb=/([^\s:;]+)\s*:\s*([^:;]+)/g,Pb=N(),Lb={registerMap:function(t,e,n){var i;return _(e)?i=e:e.svg?i=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),i=[{type:"geoJSON",source:e,specialAreas:n}]),f(i,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var n=Ob[e];_v&&O(n,"Illegal map type: "+e),n(t)}),Pb.set(t,i)},retrieveMap:function(t){return Pb.get(t)}},Ob={geoJSON:function(t){var e=t.source;t.geoJSON=b(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=Bl(t.source)}},zb=O,Eb=f,Rb=w,Bb=S,Nb=lw.parseClassType,Fb="4.9.0",Vb={zrender:"4.3.2"},Hb=1,Wb=1e3,Gb=800,Zb=900,Xb=5e3,Yb=1e3,Ub=1100,jb=2e3,qb=3e3,$b=3500,Kb=4e3,Qb=5e3,Jb={PROCESSOR:{FILTER:Wb,SERIES_FILTER:Gb,STATISTIC:Xb},VISUAL:{LAYOUT:Yb,PROGRESSIVE_LAYOUT:Ub,GLOBAL:jb,CHART:qb,POST_CHART_LAYOUT:$b,COMPONENT:Kb,BRUSH:Qb}},tS="__flagInMainProcess",eS="__optionUpdated",nS=/^[a-zA-Z0-9_]+$/;jl.prototype.on=Ul("on",!0),jl.prototype.off=Ul("off",!0),jl.prototype.one=Ul("one",!0),c(jl,Yv);var iS=ql.prototype;iS._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[eS]){var e=this[eS].silent;this[tS]=!0,Kl(this),rS.update.call(this),this[tS]=!1,this[eS]=!1,eu.call(this,e),nu.call(this,e)}else if(t.unfinished){var n=Hb,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Jl(this,i),t.performVisualTasks(i),lu(this,this._model,r,"remain"),n-=+new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},iS.getDom=function(){return this._dom},iS.getZr=function(){return this._zr},iS.setOption=function(t,e,n){if(_v&&zb(!this[tS],"`setOption` should not be called during main process."),this._disposed)return void hu(this.id);var i;if(Bb(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[tS]=!0,!this._model||e){var r=new Ds(this._api),o=this._theme,a=this._model=new Mw;a.scheduler=this._scheduler,a.init(null,null,o,r)}this._model.setOption(t,uS),n?(this[eS]={silent:i},this[tS]=!1):(Kl(this),rS.update.call(this),this._zr.flush(),this[eS]=!1,this[tS]=!1,eu.call(this,i),nu.call(this,i))},iS.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},iS.getModel=function(){return this._model},iS.getOption=function(){return this._model&&this._model.getOption()},iS.getWidth=function(){return this._zr.getWidth()},iS.getHeight=function(){return this._zr.getHeight()},iS.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},iS.getRenderedCanvas=function(t){if(Mv.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},iS.getSvgDataURL=function(){if(Mv.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return f(e,function(t){t.stopAnimation(!0)}),t.painter.toDataURL()}},iS.getDataURL=function(t){if(this._disposed)return void hu(this.id);t=t||{};var e=t.excludeComponents,n=this._model,i=[],r=this;Eb(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return Eb(i,function(t){t.group.ignore=!1}),o},iS.getConnectedDataURL=function(t){if(this._disposed)return void hu(this.id);if(Mv.canvasSupported){var e="svg"===t.type,n=this.group,r=Math.min,o=Math.max,a=1/0;if(gS[n]){var s=a,l=a,u=-a,h=-a,c=[],d=t&&t.pixelRatio||1;f(pS,function(a){if(a.group===n){var d=e?a.getZr().painter.getSvgDom().innerHTML:a.getRenderedCanvas(i(t)),f=a.getDom().getBoundingClientRect();s=r(f.left,s),l=r(f.top,l),u=o(f.right,u),h=o(f.bottom,h),c.push({dom:d,left:f.left,top:f.top})}}),s*=d,l*=d,u*=d,h*=d;var p=u-s,g=h-l,v=Ev(),m=$i(v,{renderer:e?"svg":"canvas"});if(m.resize({width:p,height:g}),e){var y="";return Eb(c,function(t){var e=t.left-s,n=t.top-l;y+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"
}),m.painter.getSvgRoot().innerHTML=y,t.connectedBackgroundColor&&m.painter.setBackgroundColor(t.connectedBackgroundColor),m.refreshImmediately(),m.painter.toDataURL()}return t.connectedBackgroundColor&&m.add(new d_({shape:{x:0,y:0,width:p,height:g},style:{fill:t.connectedBackgroundColor}})),Eb(c,function(t){var e=new Di({style:{x:t.left*d-s,y:t.top*d-l,image:t.dom}});m.add(e)}),m.refreshImmediately(),v.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},iS.convertToPixel=x($l,"convertToPixel"),iS.convertFromPixel=x($l,"convertFromPixel"),iS.containPixel=function(t,e){if(this._disposed)return void hu(this.id);var n,i=this._model;return t=cr(i,t),f(t,function(t,i){i.indexOf("Models")>=0&&f(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n|=!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint?n|=o.containPoint(e,t):_v&&console.warn(i+": "+(o?"The found component do not support containPoint.":"No view mapping to the found component."))}else _v&&console.warn(i+": containPoint is not supported")},this)},this),!!n},iS.getVisual=function(t,e){var n=this._model;t=cr(n,t,{defaultMainType:"series"});var i=t.seriesModel;_v&&(i||console.warn("There is no specified seires model"));var r=i.getData(),o=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=o?r.getItemVisual(o,e):r.getVisual(e)},iS.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},iS.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var rS={prepareAndUpdate:function(t){Kl(this),rS.update.call(this,t)},update:function(t){var e=this._model,n=this._api,i=this._zr,r=this._coordSysMgr,o=this._scheduler;if(e){o.restoreData(e,t),o.performSeriesTasks(e),r.create(e,n),o.performDataProcessorTasks(e,t),Jl(this,e),r.update(e,n),ou(e),o.performVisualTasks(e,t),au(this,e,n,t);var a=e.get("backgroundColor")||"transparent";if(Mv.canvasSupported)i.setBackgroundColor(a);else{var s=tn(a);a=hn(s,"rgb"),0===s[3]&&(a="transparent")}uu(e,n)}},updateTransform:function(t){var e=this._model,n=this,i=this._api;if(e){var r=[];e.eachComponent(function(o,a){var s=n.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(a,e,i,t);l&&l.update&&r.push(s)}else r.push(s)});var o=N();e.eachSeries(function(r){var a=n._chartsMap[r.__viewId];if(a.updateTransform){var s=a.updateTransform(r,e,i,t);s&&s.update&&o.set(r.uid,1)}else o.set(r.uid,1)}),ou(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:o}),lu(n,e,i,t,o),uu(e,this._api)}},updateView:function(t){var e=this._model;e&&(pl.markUpdateMethod(t,"updateView"),ou(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),au(this,this._model,this._api,t),uu(e,this._api))},updateVisual:function(t){rS.update.call(this,t)},updateLayout:function(t){rS.update.call(this,t)}};iS.resize=function(t){if(_v&&zb(!this[tS],"`resize` should not be called during main process."),this._disposed)return void hu(this.id);this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[tS]=!0,n&&Kl(this),rS.update.call(this),this[tS]=!1,eu.call(this,i),nu.call(this,i)}},iS.showLoading=function(t,e){if(this._disposed)return void hu(this.id);if(Bb(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),!fS[t])return void(_v&&console.warn("Loading effects "+t+" not exists."));var n=fS[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)},iS.hideLoading=function(){return this._disposed?void hu(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),void(this._loadingFX=null))},iS.makeActionFromEvent=function(t){var e=a({},t);return e.type=sS[t.type],e},iS.dispatchAction=function(t,e){if(this._disposed)return void hu(this.id);if(Bb(e)||(e={silent:!!e}),aS[t.type]&&this._model){if(this[tS])return void this._pendingActions.push(t);tu.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&Mv.browser.weChat&&this._throttledZrFlush(),eu.call(this,e.silent),nu.call(this,e.silent)}},iS.appendData=function(t){if(this._disposed)return void hu(this.id);var e=t.seriesIndex,n=this.getModel(),i=n.getSeriesByIndex(e);_v&&zb(t.data&&i),i.appendData(t),this._scheduler.unfinished=!0},iS.on=Ul("on",!1),iS.off=Ul("off",!1),iS.one=Ul("one",!1);var oS=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];iS._initEvents=function(){Eb(oS,function(t){var e=function(e){var n,i=this.getModel(),r=e.target,o="globalout"===t;if(o)n={};else if(r&&null!=r.dataIndex){var s=r.dataModel||i.getSeriesByIndex(r.seriesIndex);n=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(n=a({},r.eventData));if(n){var l=n.componentType,u=n.componentIndex;("markLine"===l||"markPoint"===l||"markArea"===l)&&(l="series",u=n.seriesIndex);var h=l&&null!=u&&i.getComponent(l,u),c=h&&this["series"===h.mainType?"_chartsMap":"_componentsMap"][h.__viewId];_v&&(o||h&&c||console.warn("model or view can not be found by params")),n.event=e,n.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:n,model:h,view:c},this.trigger(t,n)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)},this),Eb(sS,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},iS.isDisposed=function(){return this._disposed},iS.clear=function(){return this._disposed?void hu(this.id):void this.setOption({series:[]},!0)},iS.dispose=function(){if(this._disposed)return void hu(this.id);this._disposed=!0,fr(this.getDom(),yS,"");var t=this._api,e=this._model;Eb(this._componentsViews,function(n){n.dispose(e,t)}),Eb(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),delete pS[this.id]},c(ql,Yv),gu.prototype={constructor:gu,normalizeQuery:function(t){var e={},n={},i={};if(b(t)){var r=Nb(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],a={name:1,dataIndex:1,dataType:1};f(t,function(t,r){for(var s=!1,l=0;l<o.length;l++){var u=o[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,s=!0)}}a.hasOwnProperty(r)&&(n[r]=t,s=!0),s||(i[r]=t)})}return{cptQuery:e,dataQuery:n,otherQuery:i}},filter:function(t,e){function n(t,e,n,i){return null==t[n]||e[i||n]===t[n]}var i=this.eventInfo;if(!i)return!0;var r=i.targetEl,o=i.packedEvent,a=i.model,s=i.view;if(!a||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return n(l,a,"mainType")&&n(l,a,"subType")&&n(l,a,"index","componentIndex")&&n(l,a,"name")&&n(l,a,"id")&&n(u,o,"name")&&n(u,o,"dataIndex")&&n(u,o,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,o))},afterTrigger:function(){this.eventInfo=null}};var aS={},sS={},lS=[],uS=[],hS=[],cS=[],dS={},fS={},pS={},gS={},vS=new Date-0,mS=new Date-0,yS="_echarts_instance_",xS=xu;Pu(jb,ab),Mu(Nw),Iu(Zb,Fw),Ou("default",hb),Tu({type:"highlight",event:"highlight",update:"highlight"},V),Tu({type:"downplay",event:"downplay",update:"downplay"},V),Su("light",yb),Su("dark",bb);var _S={};Wu.prototype={constructor:Wu,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,n=this._new,i={},r={},o=[],a=[];for(Gu(e,i,o,"_oldKeyGetter",this),Gu(n,r,a,"_newKeyGetter",this),t=0;t<e.length;t++){var s=o[t],l=r[s];if(null!=l){var u=l.length;u?(1===u&&(r[s]=null),l=l.shift()):r[s]=null,this._update&&this._update(l,t)}else this._remove&&this._remove(t)}for(var t=0;t<a.length;t++){var s=a[t];if(r.hasOwnProperty(s)){var l=r[s];if(null==l)continue;if(l.length)for(var h=0,u=l.length;u>h;h++)this._add&&this._add(l[h]);else this._add&&this._add(l)}}}};var wS=N(["tooltip","label","itemName","itemId","seriesName"]),bS=S,SS="undefined",MS=-1,IS="e\x00\x00",CS={"float":typeof Float64Array===SS?Array:Float64Array,"int":typeof Int32Array===SS?Array:Int32Array,ordinal:Array,number:Array,time:Array},TS=typeof Uint32Array===SS?Array:Uint32Array,AS=typeof Int32Array===SS?Array:Int32Array,DS=typeof Uint16Array===SS?Array:Uint16Array,kS=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],PS=["_extent","_approximateExtent","_rawExtent"],LS=function(t,e){t=t||["x","y"];for(var n={},i=[],r={},o=0;o<t.length;o++){var a=t[o];b(a)?a=new ju({name:a}):a instanceof ju||(a=new ju(a));var s=a.name;a.type=a.type||"float",a.coordDim||(a.coordDim=s,a.coordDimIndex=0),a.otherDims=a.otherDims||{},i.push(s),n[s]=a,a.index=o,a.createInvertedIndices&&(r[s]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=Zu(this),this._invertedIndicesMap=r,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},OS=LS.prototype;OS.type="list",OS.hasItemOption=!0,OS.getDimension=function(t){return("number"==typeof t||!isNaN(t)&&!this._dimensionInfos.hasOwnProperty(t))&&(t=this.dimensions[t]),t},OS.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},OS.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},OS.mapDimension=function(t,e){var n=this._dimensionsSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return e===!0?(i||[]).slice():i&&i[e]},OS.initData=function(t,e,n){var i=os.isInstance(t)||d(t);if(i&&(t=new Us(t,this.dimensions.length)),_v&&!i&&("function"!=typeof t.getItem||"function"!=typeof t.count))throw new Error("Inavlid data provider.");this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=Gw[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._dimValueGetterArrayRows=Gw.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},OS.getProvider=function(){return this._rawData},OS.appendData=function(t){_v&&O(!this._indices,"appendData can only be called on raw data.");var e=this._rawData,n=this.count();e.appendData(t);var i=e.count();e.persistent||(i+=n),this._initDataFromProvider(n,i)},OS.appendValues=function(t,e){for(var n=this._chunkSize,i=this._storage,r=this.dimensions,o=r.length,a=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),u=this._chunkCount,h=0;o>h;h++){var c=r[h];a[c]||(a[c]=lh()),i[c]||(i[c]=[]),Qu(i,this._dimensionInfos[c],n,u,l),this._chunkCount=i[c].length}for(var d=new Array(o),f=s;l>f;f++){for(var p=f-s,g=Math.floor(f/n),v=f%n,m=0;o>m;m++){var c=r[m],y=this._dimValueGetterArrayRows(t[p]||d,c,p,m);i[c][g][v]=y;var x=a[c];y<x[0]&&(x[0]=y),y>x[1]&&(x[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},Ju(this)},OS._initDataFromProvider=function(t,e){if(!(t>=e)){for(var n,i=this._chunkSize,r=this._rawData,o=this._storage,a=this.dimensions,s=a.length,l=this._dimensionInfos,u=this._nameList,h=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;s>p;p++){var g=a[p];c[g]||(c[g]=lh());var v=l[g];0===v.otherDims.itemName&&(n=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),o[g]||(o[g]=[]),Qu(o,v,i,f,e),this._chunkCount=o[g].length}for(var m=new Array(s),y=t;e>y;y++){m=r.getItem(y,m);for(var x=Math.floor(y/i),_=y%i,w=0;s>w;w++){var g=a[w],b=o[g][x],S=this._dimValueGetter(m,g,y,w);b[_]=S;var M=c[g];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var I=u[y];if(m&&null==I)if(null!=m.name)u[y]=I=m.name;else if(null!=n){var C=a[n],T=o[C][x];if(T){I=T[_];var A=l[C].ordinalMeta;A&&A.categories.length&&(I=A.categories[I])}}var D=null==m?null:m.id;null==D&&null!=I&&(d[I]=d[I]||0,D=I,d[I]>0&&(D+="__ec__"+d[I]),d[I]++),null!=D&&(h[y]=D)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},Ju(this)}},OS.count=function(){return this._count},OS.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;i>r;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else for(var n=qu(this),t=new n(this.count()),r=0;r<t.length;r++)t[r]=r;return t},OS.get=function(t,e){if(!(e>=0&&e<this._count))return 0/0;var n=this._storage;if(!n[t])return 0/0;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,o=n[t][i],a=o[r];return a},OS.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return 0/0;var n=this._storage[t];if(!n)return 0/0;var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,o=n[i];return o[r]},OS._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),i=e%this._chunkSize,r=this._storage[t][n];return r[i]},OS.getValues=function(t,e){var n=[];_(t)||(e=t,t=this.dimensions);for(var i=0,r=t.length;r>i;i++)n.push(this.get(t[i],e));return n},OS.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=0,i=e.length;i>n;n++)if(isNaN(this.get(e[n],t)))return!1;return!0},OS.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],n=lh();if(!e)return n;var i,r=this.count(),o=!this._indices;if(o)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();i=n;for(var a=i[0],s=i[1],l=0;r>l;l++){var u=this._getFast(t,this.getRawIndex(l));a>u&&(a=u),u>s&&(s=u)}return i=[a,s],this._extent[t]=i,i},OS.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},OS.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},OS.getCalculationInfo=function(t){return this._calculationInfo[t]},OS.setCalculationInfo=function(t,e){bS(t)?a(this._calculationInfo,t):this._calculationInfo[t]=e},OS.getSum=function(t){var e=this._storage[t],n=0;if(e)for(var i=0,r=this.count();r>i;i++){var o=this.get(t,i);isNaN(o)||(n+=o)}return n},OS.getMedian=function(t){var e=[];this.each(t,function(t){isNaN(t)||e.push(t)});var n=[].concat(e).sort(function(t,e){return t-e}),i=this.count();return 0===i?0:i%2===1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},OS.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t];if(_v&&!n)throw new Error("Do not supported yet");var i=n[e];return null==i||isNaN(i)?MS:i},OS.indexOfName=function(t){for(var e=0,n=this.count();n>e;e++)if(this.getName(e)===t)return e;return-1},OS.indexOfRawIndex=function(t){if(t>=this._rawCount||0>t)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;r>=i;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},OS.indicesOfNearest=function(t,e,n){var i=this._storage,r=i[t],o=[];if(!r)return o;null==n&&(n=1/0);for(var a=1/0,s=-1,l=0,u=0,h=this.count();h>u;u++){var c=e-this.get(t,u),d=Math.abs(c);n>=d&&((a>d||d===a&&c>=0&&0>s)&&(a=d,s=c,l=0),c===s&&(o[l++]=u))}return o.length=l,o},OS.getRawIndex=eh,OS.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},OS.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||th(this,this._nameDimIdx,e)||""},OS.getId=function(t){return ih(this,this.getRawIndex(t))},OS.each=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=p(rh(t),this.getDimension,this),_v&&oh(this,t);for(var r=t.length,o=0;o<this.count();o++)switch(r){case 0:e.call(n,o);break;case 1:e.call(n,this.get(t[0],o),o);break;case 2:e.call(n,this.get(t[0],o),this.get(t[1],o),o);break;default:for(var a=0,s=[];r>a;a++)s[a]=this.get(t[a],o);s[a]=o,e.apply(n,s)}}},OS.filterSelf=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=p(rh(t),this.getDimension,this),_v&&oh(this,t);for(var r=this.count(),o=qu(this),a=new o(r),s=[],l=t.length,u=0,h=t[0],c=0;r>c;c++){var d,f=this.getRawIndex(c);if(0===l)d=e.call(n,c);else if(1===l){var g=this._getFast(h,f);d=e.call(n,g,c)}else{for(var v=0;l>v;v++)s[v]=this._getFast(h,f);s[v]=c,d=e.apply(n,s)}d&&(a[u++]=f)}return r>u&&(this._indices=a),this._count=u,this._extent={},this.getRawIndex=this._indices?nh:eh,this}},OS.selectRange=function(t){if(this._count){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);_v&&oh(this,e);var i=e.length;if(i){var r=this.count(),o=qu(this),a=new o(r),s=0,l=e[0],u=t[l][0],h=t[l][1],c=!1;if(!this._indices){var d=0;if(1===i){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m];(y>=u&&h>=y||isNaN(y))&&(a[s++]=d),d++}c=!0}else if(2===i){for(var f=this._storage[l],x=this._storage[e[1]],_=t[e[1]][0],w=t[e[1]][1],p=0;p<this._chunkCount;p++)for(var g=f[p],b=x[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m],S=b[m];(y>=u&&h>=y||isNaN(y))&&(S>=_&&w>=S||isNaN(S))&&(a[s++]=d),d++}c=!0}}if(!c)if(1===i)for(var m=0;r>m;m++){var M=this.getRawIndex(m),y=this._getFast(l,M);(y>=u&&h>=y||isNaN(y))&&(a[s++]=M)}else for(var m=0;r>m;m++){for(var I=!0,M=this.getRawIndex(m),p=0;i>p;p++){var C=e[p],y=this._getFast(n,M);(y<t[C][0]||y>t[C][1])&&(I=!1)}I&&(a[s++]=this.getRawIndex(m))}return r>s&&(this._indices=a),this._count=s,this._extent={},this.getRawIndex=this._indices?nh:eh,this}}},OS.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},OS.map=function(t,e,n,i){n=n||i||this,t=p(rh(t),this.getDimension,this),_v&&oh(this,t);var r=ah(this,t);r._indices=this._indices,r.getRawIndex=r._indices?nh:eh;for(var o=r._storage,a=[],s=this._chunkSize,l=t.length,u=this.count(),h=[],c=r._rawExtent,d=0;u>d;d++){for(var f=0;l>f;f++)h[f]=this.get(t[f],d);h[l]=d;var g=e&&e.apply(n,h);if(null!=g){"object"!=typeof g&&(a[0]=g,g=a);for(var v=this.getRawIndex(d),m=Math.floor(v/s),y=v%s,x=0;x<g.length;x++){var _=t[x],w=g[x],b=c[_],S=o[_];S&&(S[m][y]=w),w<b[0]&&(b[0]=w),w>b[1]&&(b[1]=w)}}}return r},OS.downSample=function(t,e,n,i){for(var r=ah(this,[t]),o=r._storage,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=this._chunkSize,c=r._rawExtent[t],d=new(qu(this))(u),f=0,p=0;u>p;p+=s){s>u-p&&(s=u-p,a.length=s);for(var g=0;s>g;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/h),y=v%h;a[g]=l[m][y]}var x=n(a),_=this.getRawIndex(Math.min(p+i(a,x)||0,u-1)),w=Math.floor(_/h),b=_%h;l[w][b]=x,x<c[0]&&(c[0]=x),x>c[1]&&(c[1]=x),d[f++]=_}return r._count=f,r._indices=d,r.getRawIndex=nh,r},OS.getItemModel=function(t){var e=this.hostModel;return new ga(this.getRawDataItem(t),e,e&&e.ecModel)},OS.diff=function(t){var e=this;return new Wu(t?t.getIndices():[],this.getIndices(),function(e){return ih(t,e)},function(t){return ih(e,t)})},OS.getVisual=function(t){var e=this._visual;return e&&e[t]},OS.setVisual=function(t,e){if(bS(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},OS.setLayout=function(t,e){if(bS(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},OS.getLayout=function(t){return this._layout[t]},OS.getItemLayout=function(t){return this._itemLayouts[t]},OS.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?a(this._itemLayouts[t]||{},e):e},OS.clearItemLayouts=function(){this._itemLayouts.length=0},OS.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},OS.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=i,bS(e))for(var o in e)e.hasOwnProperty(o)&&(i[o]=e[o],r[o]=!0);else i[e]=n,r[e]=!0},OS.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var zS=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};OS.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(zS,e)),this._graphicEls[t]=e},OS.getItemGraphicEl=function(t){return this._graphicEls[t]},OS.eachItemGraphicEl=function(t,e){f(this._graphicEls,function(n,i){n&&t&&t.call(e,n,i)})},OS.cloneShallow=function(t){if(!t){var e=p(this.dimensions,this.getDimensionInfo,this);t=new LS(e,this.hostModel)}if(t._storage=this._storage,Ku(t,this),this._indices){var n=this._indices.constructor;t._indices=new n(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?nh:eh,t},OS.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(P(arguments)))})},OS.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],OS.CHANGABLE_METHODS=["filterSelf","selectRange"];var ES=function(t,e){return e=e||{},uh(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})},RS={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis")[0],o=t.getReferringComponents("yAxis")[0];if(_v){if(!r)throw new Error('xAxis "'+A(t.get("xAxisIndex"),t.get("xAxisId"),0)+'" not found');if(!o)throw new Error('yAxis "'+A(t.get("xAxisIndex"),t.get("yAxisId"),0)+'" not found')}e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),ph(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),ph(o)&&(i.set("y",o),null==e.firstCategoryDimIndex&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis")[0];if(_v&&!r)throw new Error("singleAxis should be specified.");e.coordSysDims=["single"],n.set("single",r),ph(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar")[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");if(_v){if(!a)throw new Error("angleAxis option not found");if(!o)throw new Error("radiusAxis option not found")}e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),ph(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),ph(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=r.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();f(o.parallelAxisIndex,function(t,o){var s=r.getComponent("parallelAxis",t),l=a[o];n.set(l,s),ph(s)&&null==e.firstCategoryDimIndex&&(i.set(l,s),e.firstCategoryDimIndex=o)})}};wh.prototype.parse=function(t){return t},wh.prototype.getSetting=function(t){return this._setting[t]},wh.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},wh.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},wh.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},wh.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},wh.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},wh.prototype.getExtent=function(){return this._extent.slice()},wh.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},wh.prototype.isBlank=function(){return this._isBlank},wh.prototype.setBlank=function(t){this._isBlank=t},wh.prototype.getLabel=null,yr(wh),br(wh,{registerWhenExtend:!0}),bh.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&p(n,Mh);return new bh({categories:i,needCollect:!i,deduplication:e.dedplication!==!1})};var BS=bh.prototype;BS.getOrdinal=function(t){return Sh(this).get(t)},BS.parseAndCollect=function(t){var e,n=this._needCollect;if("string"!=typeof t&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=Sh(this);return e=i.get(t),null==e&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=0/0),e};var NS=wh.prototype,FS=wh.extend({type:"ordinal",init:function(t,e){(!t||_(t))&&(t=new bh({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),NS.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return NS.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(NS.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){return this.isBlank()?void 0:this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:V,niceExtent:V});FS.create=function(){return new FS};var VS=Ma,HS=Ma,WS=wh.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),WS.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Ch(t)},getTicks:function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;var a=1e4;n[0]<i[0]&&o.push(t?HS(i[0]-e,r):n[0]);for(var s=i[0];s<=i[1]&&(o.push(s),s=HS(s+e,r),s!==o[o.length-1]);)if(o.length>a)return[];var l=o.length?o[o.length-1]:i[1];return n[1]>l&&o.push(t?HS(l+e,r):n[1]),o},getMinorTicks:function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=o-a,h=u/t;t-1>s;){var c=Ma(a+(s+1)*h);c>i[0]&&c<i[1]&&l.push(c),s++}n.push(l)}return n},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=Ta(t)||0:"auto"===n&&(n=this._intervalPrecision),t=HS(t,n,!0),Fa(t)},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){0>r&&(r=-r,i.reverse());var o=Ih(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax?e[0]-=n/2:(e[1]+=n/2,e[0]-=n/2)}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=HS(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=HS(Math.ceil(e[1]/r)*r))}});WS.create=function(){return new WS};var GS="__ec_stack_",ZS=.5,XS="undefined"!=typeof Float32Array?Float32Array:Array,YS={seriesType:"bar",plan:Qw(),reset:function(t){function e(t,e){for(var n,d=t.count,f=new XS(2*d),p=new XS(2*d),g=new XS(d),v=[],m=[],y=0,x=0;null!=(n=t.next());)m[h]=e.get(s,n),m[1-h]=e.get(l,n),v=i.dataToPoint(m,null,v),p[y]=u?r.x+r.width:v[0],f[y++]=v[0],p[y]=u?v[1]:r.y+r.height,f[y++]=v[1],g[x++]=n;e.setLayout({largePoints:f,largeDataIndices:g,largeBackgroundPoints:p,barWidth:c,valueAxisStart:Fh(o,a,!1),backgroundStart:u?r.x:r.y,valueAxisHorizontal:u})}if(Bh(t)&&Nh(t)){var n=t.getData(),i=t.coordinateSystem,r=i.grid.getRect(),o=i.getBaseAxis(),a=i.getOtherAxis(o),s=n.mapDimension(a.dim),l=n.mapDimension(o.dim),u=a.isHorizontal(),h=u?0:1,c=Eh(Oh([t]),o,t).width;return c>ZS||(c=ZS),{progress:e}}}},US=WS.prototype,jS=Math.ceil,qS=Math.floor,$S=1e3,KS=60*$S,QS=60*KS,JS=24*QS,tM=function(t,e,n,i){for(;i>n;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n},eM=WS.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return Ya(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=JS,e[1]+=JS),e[1]===-1/0&&1/0===e[0]){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-JS}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=Ma(qS(e[0]/i)*i)),t.fixMax||(e[1]=Ma(jS(e[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0],o=r/t;null!=e&&e>o&&(o=e),null!=n&&o>n&&(o=n);var a=nM.length,s=tM(nM,o,0,a),l=nM[Math.min(s,a-1)],u=l[1];if("year"===l[0]){var h=r/u,c=Ea(h/t,!0);u*=c}var d=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,f=[Math.round(jS((i[0]-d)/u)*u+d),Math.round(qS((i[1]-d)/u)*u+d)];Ah(f,i),this._stepLvl=l,this._interval=u,this._niceExtent=f},parse:function(t){return+La(t)}});f(["contain","normalize"],function(t){eM.prototype[t]=function(e){return US[t].call(this,this.parse(e))}});var nM=[["hh:mm:ss",$S],["hh:mm:ss",5*$S],["hh:mm:ss",10*$S],["hh:mm:ss",15*$S],["hh:mm:ss",30*$S],["hh:mm\nMM-dd",KS],["hh:mm\nMM-dd",5*KS],["hh:mm\nMM-dd",10*KS],["hh:mm\nMM-dd",15*KS],["hh:mm\nMM-dd",30*KS],["hh:mm\nMM-dd",QS],["hh:mm\nMM-dd",2*QS],["hh:mm\nMM-dd",6*QS],["hh:mm\nMM-dd",12*QS],["MM-dd\nyyyy",JS],["MM-dd\nyyyy",2*JS],["MM-dd\nyyyy",3*JS],["MM-dd\nyyyy",4*JS],["MM-dd\nyyyy",5*JS],["MM-dd\nyyyy",6*JS],["week",7*JS],["MM-dd\nyyyy",10*JS],["week",14*JS],["week",21*JS],["month",31*JS],["week",42*JS],["month",62*JS],["week",70*JS],["quarter",95*JS],["month",31*JS*4],["month",31*JS*5],["half-year",380*JS/2],["month",31*JS*8],["month",31*JS*10],["year",380*JS]];eM.create=function(t){return new eM({useUTC:t.ecModel.get("useUTC")})};var iM=wh.prototype,rM=WS.prototype,oM=Ta,aM=Ma,sM=Math.floor,lM=Math.ceil,uM=Math.pow,hM=Math.log,cM=wh.extend({type:"log",base:10,$constructor:function(){wh.apply(this,arguments),this._originalScale=new WS},getTicks:function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return p(rM.getTicks.call(this,t),function(t){var r=Ma(uM(this.base,t));return r=t===n[0]&&e.__fixMin?Vh(r,i[0]):r,r=t===n[1]&&e.__fixMax?Vh(r,i[1]):r},this)},getMinorTicks:rM.getMinorTicks,getLabel:rM.getLabel,scale:function(t){return t=iM.scale.call(this,t),uM(this.base,t)},setExtent:function(t,e){var n=this.base;t=hM(t)/hM(n),e=hM(e)/hM(n),rM.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=iM.getExtent.call(this);e[0]=uM(t,e[0]),e[1]=uM(t,e[1]);var n=this._originalScale,i=n.getExtent();return n.__fixMin&&(e[0]=Vh(e[0],i[0])),n.__fixMax&&(e[1]=Vh(e[1],i[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=hM(t[0])/hM(e),t[1]=hM(t[1])/hM(e),iM.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(1/0===n||0>=n)){var i=Oa(n),r=t/n*i;for(.5>=r&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var o=[Ma(lM(e[0]/i)*i),Ma(sM(e[1]/i)*i)];this._interval=i,this._niceExtent=o}},niceExtent:function(t){rM.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});f(["contain","normalize"],function(t){cM.prototype[t]=function(e){return e=hM(e)/hM(this.base),iM[t].call(this,e)}}),cM.create=function(){return new cM};var dM={getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!=typeof n&&!T(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!=typeof n&&!T(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},getCoordSysModel:V,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null
}},fM=mo({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),pM=mo({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),gM=mo({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),d=Math.cos(u),f=.6*a,p=.7*a;t.moveTo(n-h,l+s),t.arc(n,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-h+c*f,l+s+d*f,n-h,l+s),t.closePath()}}),vM=mo({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),mM={line:p_,rect:d_,roundRect:d_,square:d_,circle:e_,diamond:pM,pin:gM,arrow:vM,triangle:fM},yM={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},xM={};f(mM,function(t,e){xM[e]=new t});var _M=mo({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=Qn(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=xM[i];r||(i="rect",r=xM[i]),yM[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}}),wM={isDimensionStacked:vh,enableDataStack:gh,getStackedDimension:mh},bM=(Object.freeze||Object)({createList:tc,getLayoutRect:Qa,dataStack:wM,createScale:ec,mixinAxisModelCommonMethods:nc,completeDimensions:uh,createDimensions:ES,createSymbol:Jh}),SM=1e-8;oc.prototype={constructor:oc,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],i=[-e,-e],r=[],o=[],a=this.geometries,s=0;s<a.length;s++)if("polygon"===a[s].type){var l=a[s].exterior;Br(l,r,o),ae(n,n,r),se(i,i,o)}return 0===s&&(n[0]=n[1]=i[0]=i[1]=0),this._rect=new Tn(n[0],n[1],i[0]-n[0],i[1]-n[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;r>i;i++)if("polygon"===n[i].type){var o=n[i].exterior,a=n[i].interiors;if(rc(o,t[0],t[1])){for(var s=0;s<(a?a.length:0);s++)if(rc(a[s]))continue t;return!0}}return!1},transformTo:function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i||(i=n/o):n=o*i;for(var a=new Tn(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,u=0;u<l.length;u++)if("polygon"===l[u].type){for(var h=l[u].exterior,c=l[u].interiors,d=0;d<h.length;d++)oe(h[d],h[d],s);for(var f=0;f<(c?c.length:0);f++)for(var d=0;d<c[f].length;d++)oe(c[f][d],c[f][d],s)}r=this._rect,r.copy(a),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new oc(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};var MM=function(t,e){return ac(t),p(v(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var n=t.properties,i=t.geometry,r=i.coordinates,o=[];"Polygon"===i.type&&o.push({type:"polygon",exterior:r[0],interiors:r.slice(1)}),"MultiPolygon"===i.type&&f(r,function(t){t[0]&&o.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var a=new oc(n[e||"name"],o,n.cp);return a.properties=n,a})},IM=hr(),CM=[0,1],TM=function(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1};TM.prototype={constructor:TM,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&i>=t},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return Aa(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&(n=n.slice(),bc(n,i.count())),ba(t,CM,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&(n=n.slice(),bc(n,i.count()));var r=ba(t,n,CM,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),n=uc(this,e),i=n.ticks,r=p(i,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),o=e.get("alignWithLabel");return Sc(this,r,o,t.clamp),r},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&100>e||(e=5);var n=this.scale.getMinorTicks(e),i=p(n,function(t){return p(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this);return i},getViewLabels:function(){return lc(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return yc(this)}};var AM=MM,DM={};f(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){DM[t]=Nv[t]});var kM={};f(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){kM[t]=B_[t]});var PM=function(t){this._axes={},this._dimList=[],this.name=t||""};PM.prototype={constructor:PM,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return p(this._dimList,Mc,this)},getAxesByScale:function(t){return t=t.toLowerCase(),v(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var o=n[r],a=this._axes[o];i[o]=a[e](t[o])}return i}},Ic.prototype={constructor:Ic,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),r=this.getAxis("y");return n=n||[],n[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=r.toGlobalCoord(r.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return e=e||[],e[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]),r=Math.max(t[0],t[1])-n,o=Math.max(e[0],e[1])-i,a=new Tn(n,i,r,o);return a}},h(Ic,PM);var LM=function(t,e,n,i,r){TM.call(this,t,e,n),this.type=i||"value",this.position=r||"bottom"};LM.prototype={constructor:LM,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},h(LM,TM);var OM={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},zM={};zM.categoryAxis=r({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},OM),zM.valueAxis=r({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},OM),zM.timeAxis=s({scale:!0,min:"dataMin",max:"dataMax"},zM.valueAxis),zM.logAxis=s({scale:!0,logBase:10},zM.valueAxis);var EM=["value","category","time","log"],RM=function(t,e,n,i){f(EM,function(a){e.extend({type:t+"Axis."+a,mergeDefaultAndTheme:function(e,i){var o=this.layoutMode,s=o?es(e):{},l=i.getTheme();r(e,l.get(a+"Axis")),r(e,this.getDefaultOption()),e.type=n(t,e),o&&ts(e,s,o)},optionUpdated:function(){var t=this.option;"category"===t.type&&(this.__ordinalMeta=bh.createByAxisModel(this))},getCategories:function(t){var e=this.option;return"category"===e.type?t?e.data:this.__ordinalMeta.categories:void 0},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:o([{},zM[a+"Axis"],i],!0)})}),lw.registerSubTypeDefaulter(t+"Axis",x(n,t))},BM=lw.extend({type:"cartesian2dAxis",axis:null,init:function(){BM.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){BM.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){BM.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});r(BM.prototype,dM);var NM={offset:0};RM("x",BM,Cc,NM),RM("y",BM,Cc,NM),lw.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var FM=Ac.prototype;FM.type="grid",FM.axisPointerEnabled=!0,FM.getRect=function(){return this._rect},FM.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),f(n.x,function(t){Gh(t.scale,t.model)}),f(n.y,function(t){Gh(t.scale,t.model)});var i={};f(n.x,function(t){Dc(n,"y",t,i)}),f(n.y,function(t){Dc(n,"x",t,i)}),this.resize(this.model,e)},FM.resize=function(t,e,n){function i(){f(o,function(t){var e=t.isHorizontal(),n=e?[0,r.width]:[0,r.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),Pc(t,e?r.x:r.y)})}var r=Qa(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var o=this._axesList;i(),!n&&t.get("containLabel")&&(f(o,function(t){if(!t.model.get("axisLabel.inside")){var e=jh(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin");r[n]-=e[n]+i,"top"===t.position?r.y+=e.height+i:"left"===t.position&&(r.x+=e.width+i)}}}),i())},FM.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},FM.getAxes=function(){return this._axesList.slice()},FM.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}S(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},FM.getCartesians=function(){return this._coordsList.slice()},FM.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},FM.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},FM._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,o=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],a=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)n=r.coordinateSystem,u(l,n)<0&&(n=null);else if(o&&a)n=this.getCartesian(o.componentIndex,a.componentIndex);else if(o)i=this.getAxis("x",o.componentIndex);else if(a)i=this.getAxis("y",a.componentIndex);else if(s){var h=s.coordinateSystem;h===this&&(n=this._coordsList[0])}return{cartesian:n,axis:i}},FM.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},FM._initCartesian=function(t,e){function n(n){return function(a,s){if(Tc(a,t,e)){var l=a.get("position");"x"===n?"top"!==l&&"bottom"!==l&&(l=i.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=i.left?"right":"left"),i[l]=!0;var u=new LM(n,Zh(a),[0,0],a.get("type"),l),h="category"===u.type;u.onBand=h&&a.get("boundaryGap"),u.inverse=a.get("inverse"),a.axis=u,u.model=a,u.grid=this,u.index=s,this._axesList.push(u),r[n][s]=u,o[n]++}}}var i={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},o={x:0,y:0};return e.eachComponent("xAxis",n("x"),this),e.eachComponent("yAxis",n("y"),this),o.x&&o.y?(this._axesMap=r,void f(r.x,function(e,n){f(r.y,function(i,r){var o="x"+n+"y"+r,a=new Ic(o);a.grid=this,a.model=t,this._coordsMap[o]=a,this._coordsList.push(a),a.addAxis(e),a.addAxis(i)},this)},this)):(this._axesMap={},void(this._axesList=[]))},FM._updateScale=function(t,e){function n(t,e){f(t.mapDimension(e.dim,!0),function(n){e.scale.unionExtentFromData(t,mh(t,n))})}f(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(i){if(Oc(i)){var r=Lc(i,t),o=r[0],a=r[1];if(!Tc(o,e,t)||!Tc(a,e,t))return;var s=this.getCartesian(o.componentIndex,a.componentIndex),l=i.getData(),u=s.getAxis("x"),h=s.getAxis("y");"list"===l.type&&(n(l,u,i),n(l,h,i))}},this)},FM.getTooltipAxes=function(t){var e=[],n=[];return f(this.getCartesians(),function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);u(e,r)<0&&e.push(r),u(n,o)<0&&n.push(o)}),{baseAxes:e,otherAxes:n}};var VM=["xAxis","yAxis"];Ac.create=function(t,e){var n=[];return t.eachComponent("grid",function(i,r){var o=new Ac(i,t,e);o.name="grid_"+r,o.resize(i,e,!0),i.coordinateSystem=o,n.push(o)}),t.eachSeries(function(e){if(Oc(e)){var n=Lc(e,t),i=n[0],r=n[1],o=i.getCoordSysModel();if(_v){if(!o)throw new Error('Grid "'+A(i.get("gridIndex"),i.get("gridId"),0)+'" not found');if(i.getCoordSysModel()!==r.getCoordSysModel())throw new Error("xAxis and yAxis must use the same grid")}var a=o.coordinateSystem;e.coordinateSystem=a.getCartesian(i.componentIndex,r.componentIndex)}}),n},Ac.dimensions=Ac.prototype.dimensions=Ic.prototype.dimensions,As.register("cartesian2d",Ac);var HM=qw.extend({type:"series.__base_bar__",getInitialData:function(){return yh(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(e.clampData(t)),i=this.getData(),r=i.getLayout("offset"),o=i.getLayout("size"),a=e.getBaseAxis().isHorizontal()?0:1;return n[a]+=r+o/2,n}return[0/0,0/0]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});HM.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return this.get("large")?this.get("progressive"):!1},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},defaultOption:{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1}}});var WM=Yy([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),GM={getBarItemStyle:function(t){var e=WM(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(e.lineDash=n)}return e}},ZM=mo({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=Math.cos(l),d=Math.sin(l),f=Math.cos(u),p=Math.sin(u),g=h?u-l<2*Math.PI:l-u<2*Math.PI;g&&(t.moveTo(c*r+n,d*r+i),t.arc(c*s+n,d*s+i,a,-Math.PI+l,l,!h)),t.arc(n,i,o,l,u,!h),t.moveTo(f*o+n,p*o+i),t.arc(f*s+n,p*s+i,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&(t.arc(n,i,r,u,l,h),t.moveTo(c*r+n,p*r+i)),t.closePath()}}),XM=["itemStyle","barBorderWidth"],YM=[0,0];a(ga.prototype,GM),Bu({type:"bar",render:function(t,e,n){this._updateDrawMode(t);var i=t.get("coordinateSystem");return"cartesian2d"===i||"polar"===i?this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n):_v&&console.warn("Only cartesian2d and polar supported for bar."),this.group},incrementalPrepareRender:function(t){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t){var e,n=this.group,i=t.getData(),r=this._data,o=t.coordinateSystem,a=o.getBaseAxis();"cartesian2d"===o.type?e=a.isHorizontal():"polar"===o.type&&(e="angle"===a.dim);var s=t.isAnimationEnabled()?t:null,l=t.get("clip",!0),u=Vc(o,i);n.removeClipPath();var h=t.get("roundCap",!0),c=t.get("showBackground",!0),d=t.getModel("backgroundStyle"),f=d.get("barBorderRadius")||0,p=[],g=this._backgroundEls||[],v=function(t){var n=KM[o.type](i,t),r=Kc(o,e,n);return r.useStyle(d.getBarItemStyle()),"cartesian2d"===o.type&&r.setShape("r",f),p[t]=r,r};i.diff(r).add(function(r){var a=i.getItemModel(r),d=KM[o.type](i,r,a);if(c&&v(r),i.hasValue(r)){if(l){var f=qM[o.type](u,d);if(f)return void n.remove(p)}var p=$M[o.type](r,d,e,s,!1,h);i.setItemGraphicEl(r,p),n.add(p),Zc(p,i,r,a,d,t,e,"polar"===o.type)}}).update(function(a,m){var y=i.getItemModel(a),x=KM[o.type](i,a,y);if(c){var _;0===g.length?_=v(m):(_=g[m],_.useStyle(d.getBarItemStyle()),"cartesian2d"===o.type&&_.setShape("r",f),p[a]=_);var w=KM[o.type](i,a),b=$c(e,w,o);na(_,{shape:b},s,a)}var S=r.getItemGraphicEl(m);if(!i.hasValue(a))return void n.remove(S);if(l){var M=qM[o.type](u,x);if(M)return void n.remove(S)}S?na(S,{shape:x},s,a):S=$M[o.type](a,x,e,s,!0,h),i.setItemGraphicEl(a,S),n.add(S),Zc(S,i,a,y,x,t,e,"polar"===o.type)}).remove(function(t){var e=r.getItemGraphicEl(t);"cartesian2d"===o.type?e&&Hc(t,s,e):e&&Wc(t,s,e)}).execute();var m=this._backgroundGroup||(this._backgroundGroup=new Nm);m.removeAll();for(var y=0;y<p.length;++y)m.add(p[y]);n.add(m),this._backgroundEls=p,this._data=i},_renderLarge:function(t){this._clear(),Yc(t,this.group);var e=t.get("clip",!0)?Fc(t.coordinateSystem,!1,t):null;e?this.group.setClipPath(e):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){this._removeBackground(),Yc(e,this.group,!0)},dispose:V,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,n=this._data;t&&t.get("animation")&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(e){"sector"===e.type?Wc(e.dataIndex,t,e):Hc(e.dataIndex,t,e)})):e.removeAll(),this._data=null},_removeBackground:function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null}});var UM=Math.max,jM=Math.min,qM={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;0>n&&(e.x+=e.width,e.width=-e.width),0>i&&(e.y+=e.height,e.height=-e.height);var r=UM(e.x,t.x),o=jM(e.x+e.width,t.x+t.width),a=UM(e.y,t.y),s=jM(e.y+e.height,t.y+t.height);e.x=r,e.y=a,e.width=o-r,e.height=s-a;var l=e.width<0||e.height<0;return 0>n&&(e.x+=e.width,e.width=-e.width),0>i&&(e.y+=e.height,e.height=-e.height),l},polar:function(t,e){var n=e.r0<=e.r?1:-1;if(0>n){var i=e.r;e.r=e.r0,e.r0=i}var i=jM(e.r,t.r),r=UM(e.r0,t.r0);e.r=i,e.r0=r;var o=0>i-r;if(0>n){var i=e.r;e.r=e.r0,e.r0=i}return o}},$M={cartesian2d:function(t,e,n,i,r){var o=new d_({shape:a({},e),z2:1});if(o.name="item",i){var s=o.shape,l=n?"height":"width",u={};s[l]=0,u[l]=e[l],B_[r?"updateProps":"initProps"](o,{shape:u},i,t)}return o},polar:function(t,e,n,i,r,o){var a=e.startAngle<e.endAngle,l=!n&&o?ZM:r_,u=new l({shape:s({clockwise:a},e),z2:1});if(u.name="item",i){var h=u.shape,c=n?"r":"endAngle",d={};h[c]=n?0:e.startAngle,d[c]=e[c],B_[r?"updateProps":"initProps"](u,{shape:d},i,t)}return u}},KM={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=n?Xc(n,i):0,o=i.width>0?1:-1,a=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e){var n=t.getItemLayout(e);return{cx:n.cx,cy:n.cy,r0:n.r0,r:n.r,startAngle:n.startAngle,endAngle:n.endAngle}}},QM=no.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var n=e.points,i=this.__startPoint,r=this.__baseDimIdx,o=0;o<n.length;o+=2)i[r]=n[o+r],t.moveTo(i[0],i[1]),t.lineTo(n[o],n[o+1])}}),JM=xl(function(t){var e=this,n=Uc(e,t.offsetX,t.offsetY);e.dataIndex=n>=0?n:null},30,!1),tI=Math.PI,eI=function(t,e){this.opt=e,this.axisModel=t,s(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Nm;var n=new Nm({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n};eI.prototype={constructor:eI,hasBuilder:function(t){return!!nI[t]},add:function(t){nI[t].call(this)},getGroup:function(){return this.group}};var nI={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var n=this.axisModel.axis.getExtent(),i=this._transform,r=[n[0],0],o=[n[1],0];i&&(oe(r,r,i),oe(o,o,i));var s=a({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new p_({anid:"line",subPixelOptimize:!0,shape:{x1:r[0],y1:r[1],x2:o[0],y2:o[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1}));var l=e.get("axisLine.symbol"),u=e.get("axisLine.symbolSize"),h=e.get("axisLine.symbolOffset")||0;if("number"==typeof h&&(h=[h,h]),null!=l){"string"==typeof l&&(l=[l,l]),("string"==typeof u||"number"==typeof u)&&(u=[u,u]);var c=u[0],d=u[1];f([{rotate:t.rotation+Math.PI/2,offset:h[0],r:0},{rotate:t.rotation-Math.PI/2,offset:h[1],r:Math.sqrt((r[0]-o[0])*(r[0]-o[0])+(r[1]-o[1])*(r[1]-o[1]))}],function(e,n){if("none"!==l[n]&&null!=l[n]){var i=Jh(l[n],-c/2,-d/2,c,d,s.stroke,!0),o=e.r+e.offset,a=[r[0]+o*Math.cos(t.rotation),r[1]-o*Math.sin(t.rotation)];i.attr({rotation:e.rotate,position:a,silent:!0,z2:11}),this.group.add(i)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=rd(this,t,e),i=ad(this,t,e);Jc(t,i,n),od(this,t,e)},axisName:function(){var t=this.opt,e=this.axisModel,n=A(t.axisName,e.get("name"));if(n){var i,r=e.get("nameLocation"),o=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,u=this.axisModel.axis.getExtent(),h=u[0]>u[1]?-1:1,c=["start"===r?u[0]-h*l:"end"===r?u[1]+h*l:(u[0]+u[1])/2,nd(r)?t.labelOffset+o*l:0],d=e.get("nameRotate");null!=d&&(d=d*tI/180);var f;nd(r)?i=rI(t.rotation,null!=d?d:t.rotation,o):(i=Qc(t,r,d||0,u),f=t.axisNameAvailableWidth,null!=f&&(f=Math.abs(f/Math.sin(i.rotation)),!isFinite(f)&&(f=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},v=g.ellipsis,m=A(t.nameTruncateMaxWidth,g.maxWidth,f),y=null!=v&&null!=m?tw(n,m,p,v,{minChar:2,placeholder:g.placeholder}):n,x=e.get("tooltip",!0),_=e.mainType,w={componentType:_,name:n,$vars:["name"]};w[_+"Index"]=e.componentIndex;var b=new t_({anid:"name",__fullText:n,__truncatedText:y,position:c,rotation:i.rotation,silent:oI(e),z2:1,tooltip:x&&x.show?a({content:n,formatter:function(){return n},formatterParams:w},x):null});Yo(b.style,s,{text:y,textFont:p,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:s.get("align")||i.textAlign,textVerticalAlign:s.get("verticalAlign")||i.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=iI(e),b.eventData.targetType="axisName",b.eventData.name=n),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},iI=eI.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},rI=eI.innerTextLayout=function(t,e,n){var i,r,o=ka(e-t);return Pa(o)?(r=n>0?"top":"bottom",i="center"):Pa(o-tI)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&tI>o?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},oI=eI.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},aI=f,sI=x,lI=Eu({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){this.axisPointerClass&&fd(t),lI.superApply(this,"render",arguments),yd(this,t,e,n,i,!0)},updateAxisPointer:function(t,e,n,i){yd(this,t,e,n,i,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),lI.superApply(this,"remove",arguments)},dispose:function(t,e){xd(this,e),lI.superApply(this,"dispose",arguments)}}),uI=[];lI.registerAxisPointerClass=function(t,e){if(_v&&uI[t])throw new Error("axisPointer "+t+" exists");uI[t]=e},lI.getAxisPointerClass=function(t){return t&&uI[t]};var hI=["axisLine","axisTickLabel","axisName"],cI=["splitArea","splitLine","minorSplitLine"],dI=lI.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,n,i){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Nm,this.group.add(this._axisGroup),t.get("show")){var o=t.getCoordSysModel(),a=_d(o,t),s=new eI(t,a);f(hI,s.add,s),this._axisGroup.add(s.getGroup()),f(cI,function(e){t.get(e+".show")&&this["_"+e](t,o)},this),sa(r,this._axisGroup,t),dI.superCall(this,"render",t,e,n,i)}},remove:function(){bd(this)},_splitLine:function(t,e){var n=t.axis;if(!n.scale.isBlank()){var i=t.getModel("splitLine"),r=i.getModel("lineStyle"),o=r.get("color");o=_(o)?o:[o];for(var a=e.coordinateSystem.getRect(),l=n.isHorizontal(),u=0,h=n.getTicksCoords({tickModel:i}),c=[],d=[],f=r.getLineStyle(),p=0;p<h.length;p++){var g=n.toGlobalCoord(h[p].coord);l?(c[0]=g,c[1]=a.y,d[0]=g,d[1]=a.y+a.height):(c[0]=a.x,c[1]=g,d[0]=a.x+a.width,d[1]=g);var v=u++%o.length,m=h[p].tickValue;this._axisGroup.add(new p_({anid:null!=m?"line_"+h[p].tickValue:null,subPixelOptimize:!0,shape:{x1:c[0],y1:c[1],x2:d[0],y2:d[1]},style:s({stroke:o[v]},f),silent:!0}))}}},_minorSplitLine:function(t,e){var n=t.axis,i=t.getModel("minorSplitLine"),r=i.getModel("lineStyle"),o=e.coordinateSystem.getRect(),a=n.isHorizontal(),s=n.getMinorTicksCoords();if(s.length)for(var l=[],u=[],h=r.getLineStyle(),c=0;c<s.length;c++)for(var d=0;d<s[c].length;d++){var f=n.toGlobalCoord(s[c][d].coord);a?(l[0]=f,l[1]=o.y,u[0]=f,u[1]=o.y+o.height):(l[0]=o.x,l[1]=f,u[0]=o.x+o.width,u[1]=f),this._axisGroup.add(new p_({anid:"minor_line_"+s[c][d].tickValue,subPixelOptimize:!0,shape:{x1:l[0],y1:l[1],x2:u[0],y2:u[1]},style:h,silent:!0}))}},_splitArea:function(t,e){wd(this,this._axisGroup,t,e)}});dI.extend({type:"xAxis"}),dI.extend({type:"yAxis"}),Eu({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new d_({shape:t.coordinateSystem.getRect(),style:s({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),Mu(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),ku(Jb.VISUAL.LAYOUT,x(Rh,"bar")),ku(Jb.VISUAL.PROGRESSIVE_LAYOUT,YS),Pu({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}}),qw.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t){if(_v){var e=t.coordinateSystem;if("polar"!==e&&"cartesian2d"!==e)throw new Error("Line not support coordinateSystem besides cartesian and polar")}return yh(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var fI=Sd.prototype,pI=Sd.getSymbolSize=function(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]};fI._createSymbol=function(t,e,n,i,r){this.removeAll();var o=e.getItemVisual(n,"color"),a=Jh(t,-1,-1,2,2,o,r);a.attr({z2:100,culling:!0,scale:Md(i)}),a.drift=Id,this._symbolType=t,this.add(a)},fI.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},fI.getSymbolPath=function(){return this.childAt(0)},fI.getScale=function(){return this.childAt(0).scale},fI.highlight=function(){this.childAt(0).trigger("emphasis")},fI.downplay=function(){this.childAt(0).trigger("normal")},fI.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},fI.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},fI.updateData=function(t,e,n){this.silent=!1;var i=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,o=pI(t,e),a=i!==this._symbolType;if(a){var s=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(i,t,e,o,s)}else{var l=this.childAt(0);l.silent=!1,na(l,{scale:Md(o)},r,e)}if(this._updateCommon(t,e,o,n),a){var l=this.childAt(0),u=n&&n.fadeIn,h={scale:l.scale.slice()};u&&(h.style={opacity:l.style.opacity}),l.scale=[0,0],u&&(l.style.opacity=0),ia(l,h,r,e)}this._seriesModel=r};var gI=["itemStyle"],vI=["emphasis","itemStyle"],mI=["label"],yI=["emphasis","label"];fI._updateCommon=function(t,e,n,i){function r(e){return b?t.getName(e):zc(t,e)}var o=this.childAt(0),s=t.hostModel,l=t.getItemVisual(e,"color");"image"!==o.type?o.useStyle({strokeNoScale:!0}):o.setStyle({opacity:1,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null});var u=i&&i.itemStyle,h=i&&i.hoverItemStyle,c=i&&i.symbolOffset,d=i&&i.labelModel,f=i&&i.hoverLabelModel,p=i&&i.hoverAnimation,g=i&&i.cursorStyle;if(!i||t.hasItemOption){var v=i&&i.itemModel?i.itemModel:t.getItemModel(e);u=v.getModel(gI).getItemStyle(["color"]),h=v.getModel(vI).getItemStyle(),c=v.getShallow("symbolOffset"),d=v.getModel(mI),f=v.getModel(yI),p=v.getShallow("hoverAnimation"),g=v.getShallow("cursor")}else h=a({},h);var m=o.style,y=t.getItemVisual(e,"symbolRotate");o.attr("rotation",(y||0)*Math.PI/180||0),c&&o.attr("position",[Sa(c[0],n[0]),Sa(c[1],n[1])]),g&&o.attr("cursor",g),o.setColor(l,i&&i.symbolInnerColor),o.setStyle(u);
var x=t.getItemVisual(e,"opacity");null!=x&&(m.opacity=x);var _=t.getItemVisual(e,"liftZ"),w=o.__z2Origin;null!=_?null==w&&(o.__z2Origin=o.z2,o.z2+=_):null!=w&&(o.z2=w,o.__z2Origin=null);var b=i&&i.useNameLabel;Zo(m,h,d,f,{labelFetcher:s,labelDataIndex:e,defaultText:r,isRectText:!0,autoColor:l}),o.__symbolOriginalScale=Md(n),o.hoverStyle=h,o.highDownOnUpdate=p&&s.isAnimationEnabled()?Cd:null,Vo(o)},fI.fadeOut=function(t,e){var n=this.childAt(0);this.silent=n.silent=!0,!(e&&e.keepLabel)&&(n.style.text=null),na(n,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},h(Sd,Nm);var xI=Td.prototype;xI.updateData=function(t,e){e=Dd(e);var n=this.group,i=t.hostModel,r=this._data,o=this._symbolCtor,a=kd(t);r||n.removeAll(),t.diff(r).add(function(i){var r=t.getItemLayout(i);if(Ad(t,r,i,e)){var s=new o(t,i,a);s.attr("position",r),t.setItemGraphicEl(i,s),n.add(s)}}).update(function(s,l){var u=r.getItemGraphicEl(l),h=t.getItemLayout(s);return Ad(t,h,s,e)?(u?(u.updateData(t,s,a),na(u,{position:h},i)):(u=new o(t,s),u.attr("position",h)),n.add(u),void t.setItemGraphicEl(s,u)):void n.remove(u)}).remove(function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut(function(){n.remove(e)})}).execute(),this._data=t},xI.isPersistent=function(){return!0},xI.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,n){var i=t.getItemLayout(n);e.attr("position",i)})},xI.incrementalPrepareUpdate=function(t){this._seriesScope=kd(t),this._data=null,this.group.removeAll()},xI.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}n=Dd(n);for(var r=t.start;r<t.end;r++){var o=e.getItemLayout(r);if(Ad(e,o,r,n)){var a=new this._symbolCtor(e,r,this._seriesScope);a.traverse(i),a.attr("position",o),this.group.add(a),e.setItemGraphicEl(r,a)}}},xI.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var _I=function(t,e,n,i,r,o,a,s){for(var l=zd(t,e),u=[],h=[],c=[],d=[],f=[],p=[],g=[],v=Pd(r,e,a),m=Pd(o,t,s),y=0;y<l.length;y++){var x=l[y],_=!0;switch(x.cmd){case"=":var w=t.getItemLayout(x.idx),b=e.getItemLayout(x.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=b.slice()),u.push(w),h.push(b),c.push(n[x.idx]),d.push(i[x.idx1]),g.push(e.getRawIndex(x.idx1));break;case"+":var S=x.idx;u.push(r.dataToPoint([e.get(v.dataDimsForPoint[0],S),e.get(v.dataDimsForPoint[1],S)])),h.push(e.getItemLayout(S).slice()),c.push(Od(v,r,e,S)),d.push(i[S]),g.push(e.getRawIndex(S));break;case"-":var S=x.idx,M=t.getRawIndex(S);M!==S?(u.push(t.getItemLayout(S)),h.push(o.dataToPoint([t.get(m.dataDimsForPoint[0],S),t.get(m.dataDimsForPoint[1],S)])),c.push(n[S]),d.push(Od(m,o,t,S)),g.push(M)):_=!1}_&&(f.push(x),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});for(var I=[],C=[],T=[],A=[],D=[],y=0;y<p.length;y++){var S=p[y];I[y]=u[S],C[y]=h[S],T[y]=c[S],A[y]=d[S],D[y]=f[S]}return{current:I,next:C,stackedOnCurrent:T,stackedOnNext:A,status:D}},wI=ae,bI=se,SI=Y,MI=W,II=[],CI=[],TI=[],AI=no.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:i_(no.prototype.brush),buildPath:function(t,e){var n=e.points,i=0,r=n.length,o=Fd(n,e.smoothConstraint);if(e.connectNulls){for(;r>0&&Ed(n[r-1]);r--);for(;r>i&&Ed(n[i]);i++);}for(;r>i;)i+=Rd(t,n,i,r,r,1,o.min,o.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),DI=no.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:i_(no.prototype.brush),buildPath:function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length,a=e.smoothMonotone,s=Fd(n,e.smoothConstraint),l=Fd(i,e.smoothConstraint);if(e.connectNulls){for(;o>0&&Ed(n[o-1]);o--);for(;o>r&&Ed(n[r]);r++);}for(;o>r;){var u=Rd(t,n,r,o,o,1,s.min,s.max,e.smooth,a,e.connectNulls);Rd(t,i,r+u-1,u,o,-1,l.min,l.max,e.stackedOnSmooth,a,e.connectNulls),r+=u+1,t.closePath()}}});pl.extend({type:"line",init:function(){var t=new Nm,e=new Td;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var i=t.coordinateSystem,r=this.group,o=t.getData(),a=t.getModel("lineStyle"),l=t.getModel("areaStyle"),u=o.mapArray(o.getItemLayout),h="polar"===i.type,c=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,g=this._lineGroup,v=t.get("animation"),m=!l.isEmpty(),y=l.get("origin"),x=Pd(i,o,y),_=Gd(i,o,x),w=t.get("showSymbol"),b=w&&!h&&Yd(t,o,i),S=this._data;S&&S.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),S.setItemGraphicEl(e,null))}),w||d.remove(),r.add(g);var M,I=!h&&t.get("step");i&&i.getArea&&t.get("clip",!0)&&(M=i.getArea(),null!=M.width?(M.x-=.1,M.y-=.1,M.width+=.2,M.height+=.2):M.r0&&(M.r0-=.5,M.r1+=.5)),this._clipShapeForSymbol=M,f&&c.type===i.type&&I===this._step?(m&&!p?p=this._newPolygon(u,_,i,v):p&&!m&&(g.remove(p),p=this._polygon=null),g.setClipPath(jd(i,!1,t)),w&&d.updateData(o,{isIgnore:b,clipShape:M}),o.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),Vd(this._stackedOnPoints,_)&&Vd(this._points,u)||(v?this._updateAnimation(o,_,i,n,I,y):(I&&(u=Zd(u,i,I),_=Zd(_,i,I)),f.setShape({points:u}),p&&p.setShape({points:u,stackedOnPoints:_})))):(w&&d.updateData(o,{isIgnore:b,clipShape:M}),I&&(u=Zd(u,i,I),_=Zd(_,i,I)),f=this._newPolyline(u,i,v),m&&(p=this._newPolygon(u,_,i,v)),g.setClipPath(jd(i,!0,t)));var C=Xd(o,i)||o.getVisual("color");f.useStyle(s(a.getLineStyle(),{fill:"none",stroke:C,lineJoin:"bevel"}));var T=t.get("smooth");if(T=Wd(t.get("smooth")),f.setShape({smooth:T,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p){var A=o.getCalculationInfo("stackedOnSeries"),D=0;p.useStyle(s(l.getAreaStyle(),{fill:C,opacity:.7,lineJoin:"bevel"})),A&&(D=Wd(A.get("smooth"))),p.setShape({smooth:T,stackedOnSmooth:D,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=o,this._coordSys=i,this._stackedOnPoints=_,this._points=u,this._step=I,this._valueOrigin=y},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),o=ur(r,i);if(!(o instanceof Array)&&null!=o&&o>=0){var a=r.getItemGraphicEl(o);if(!a){var s=r.getItemLayout(o);if(!s)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s[0],s[1]))return;a=new Sd(r,o),a.position=s,a.setZ(t.get("zlevel"),t.get("z")),a.ignore=isNaN(s[0])||isNaN(s[1]),a.__temp=!0,r.setItemGraphicEl(o,a),a.stopSymbolAnimation(!0),this.group.add(a)}a.highlight()}else pl.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r=t.getData(),o=ur(r,i);if(null!=o&&o>=0){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else pl.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new AI({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new DI({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n,n},_updateAnimation:function(t,e,n,i,r,o){var a=this._polyline,s=this._polygon,l=t.hostModel,u=_I(this._data,t,this._stackedOnPoints,e,this._coordSys,n,this._valueOrigin,o),h=u.current,c=u.stackedOnCurrent,d=u.next,f=u.stackedOnNext;if(r&&(h=Zd(u.current,n,r),c=Zd(u.stackedOnCurrent,n,r),d=Zd(u.next,n,r),f=Zd(u.stackedOnNext,n,r)),Hd(h,d)>3e3||s&&Hd(c,f)>3e3)return a.setShape({points:d}),void(s&&s.setShape({points:d,stackedOnPoints:f}));a.shape.__points=u.current,a.shape.points=h,na(a,{shape:{points:d}},l),s&&(s.setShape({points:h,stackedOnPoints:c}),na(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p=[],g=u.status,v=0;v<g.length;v++){var m=g[v].cmd;if("="===m){var y=t.getItemGraphicEl(g[v].idx1);y&&p.push({el:y,ptIdx:v})}}a.animators&&a.animators.length&&a.animators[0].during(function(){for(var t=0;t<p.length;t++){var e=p[t].el;e.attr("position",a.shape.__points[p[t].ptIdx])}})},remove:function(){var t=this.group,e=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),e&&e.eachItemGraphicEl(function(n,i){n.__temp&&(t.remove(n),e.setItemGraphicEl(i,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});var kI=function(t,e,n){return{seriesType:t,performRawSeries:!0,reset:function(t,i){function r(e,n){if(f){var i=t.getRawValue(n),r=t.getDataParams(n);h&&e.setItemVisual(n,"symbol",a(i,r)),c&&e.setItemVisual(n,"symbolSize",s(i,r)),d&&e.setItemVisual(n,"symbolRotate",u(i,r))}if(e.hasItemOption){var o=e.getItemModel(n),l=o.getShallow("symbol",!0),p=o.getShallow("symbolSize",!0),g=o.getShallow("symbolRotate",!0),v=o.getShallow("symbolKeepAspect",!0);null!=l&&e.setItemVisual(n,"symbol",l),null!=p&&e.setItemVisual(n,"symbolSize",p),null!=g&&e.setItemVisual(n,"symbolRotate",g),null!=v&&e.setItemVisual(n,"symbolKeepAspect",v)}}var o=t.getData(),a=t.get("symbol"),s=t.get("symbolSize"),l=t.get("symbolKeepAspect"),u=t.get("symbolRotate"),h=w(a),c=w(s),d=w(u),f=h||c||d,p=!h&&a?a:e,g=c?null:s;return o.setVisual({legendSymbol:n||p,symbol:p,symbolSize:g,symbolKeepAspect:l,symbolRotate:u}),i.isSeriesFiltered(t)?void 0:{dataEach:o.hasItemOption||f?r:null}}}},PI=function(t){return{seriesType:t,plan:Qw(),reset:function(t){function e(t,e){for(var n=t.end-t.start,r=o&&new Float32Array(n*s),l=t.start,u=0,h=[],c=[];l<t.end;l++){var d;if(1===s){var f=e.get(a[0],l);d=!isNaN(f)&&i.dataToPoint(f,null,c)}else{var f=h[0]=e.get(a[0],l),p=h[1]=e.get(a[1],l);d=!isNaN(f)&&!isNaN(p)&&i.dataToPoint(h,null,c)}o?(r[u++]=d?d[0]:0/0,r[u++]=d?d[1]:0/0):e.setItemLayout(l,d&&d.slice()||[0/0,0/0])}o&&e.setLayout("symbolPoints",r)}var n=t.getData(),i=t.coordinateSystem,r=t.pipelineContext,o=r.large;if(i){var a=p(i.dimensions,function(t){return n.mapDimension(t)}).slice(0,2),s=a.length,l=n.getCalculationInfo("stackResultDimension");return vh(n,a[0])&&(a[0]=l),vh(n,a[1])&&(a[1]=l),s&&{progress:e}}}}},LI={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?0/0:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:0/0},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:0/0},nearest:function(t){return t[0]}},OI=function(t){return Math.round(t.length/2)},zI=function(t){return{seriesType:t,modifyOutputEnd:!0,reset:function(t){var e=t.getData(),n=t.get("sampling"),i=t.coordinateSystem;if("cartesian2d"===i.type&&n){var r=i.getBaseAxis(),o=i.getOtherAxis(r),a=r.getExtent(),s=Math.abs(a[1]-a[0]),l=Math.round(e.count()/s);if(l>1){var u;"string"==typeof n?u=LI[n]:"function"==typeof n&&(u=n),u&&t.setData(e.downSample(e.mapDimension(o.dim),1/l,u,OI))}}}}};Pu(kI("line","circle","line")),ku(PI("line")),Iu(Jb.PROCESSOR.STATISTIC,zI("line"));var EI=function(t,e,n){e=_(e)&&{coordDimensions:e}||a({},e);var i=t.getSource(),r=ES(i,e),o=new LS(r,t);return o.initData(i,n),o},RI={updateSelectedMap:function(t){this._targetList=_(t)?t.slice():[],this._selectTargetMap=g(t||[],function(t,e){return t.set(e.name,e),t},N())},select:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t),i=this.get("selectedMode");"single"===i&&this._selectTargetMap.each(function(t){t.selected=!1}),n&&(n.selected=!0)},unSelect:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);n&&(n.selected=!1)},toggleSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return null!=n?(this[n.selected?"unSelect":"select"](t,e),n.selected):void 0},isSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return n&&n.selected}},BI=Ru({type:"series.pie",init:function(t){BI.superApply(this,"init",arguments),this.legendVisualProvider=new qd(y(this.getData,this),y(this.getRawData,this)),this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){BI.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(){return EI(this,{coordDimensions:["value"],encodeDefaulter:x(gs,this)})},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),n=[],i=0,r=t.count();r>i;i++)n.push({name:t.getName(i),value:t.get(e,i),selected:el(t,i,"selected")});return n},getDataParams:function(t){var e=this.getData(),n=BI.superCall(this,"getDataParams",t),i=[];return e.each(e.mapDimension("value"),function(t){i.push(t)}),n.percent=Da(i,t,e.hostModel.get("percentPrecision")),n.$vars.push("percent"),n},_defaultLabelLine:function(t){nr(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:!1,show:!0,position:"outer",alignTo:"none",margin:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationTypeUpdate:"transition",animationEasing:"cubicOut"}});c(BI,RI);var NI=Qd.prototype;NI.updateData=function(t,e,n){var i=this.childAt(0),r=this.childAt(1),o=this.childAt(2),l=t.hostModel,u=t.getItemModel(e),h=t.getItemLayout(e),c=a({},h);c.label=null;var d=l.getShallow("animationTypeUpdate");if(n){i.setShape(c);var f=l.getShallow("animationType");"scale"===f?(i.shape.r=h.r0,ia(i,{shape:{r:h.r}},l,e)):(i.shape.endAngle=h.startAngle,na(i,{shape:{endAngle:h.endAngle}},l,e))}else"expansion"===d?i.setShape(c):na(i,{shape:c},l,e);var p=t.getItemVisual(e,"color");i.useStyle(s({lineJoin:"bevel",fill:p},u.getModel("itemStyle").getItemStyle())),i.hoverStyle=u.getModel("emphasis.itemStyle").getItemStyle();var g=u.getShallow("cursor");g&&i.attr("cursor",g),Kd(this,t.getItemLayout(e),l.isSelected(t.getName(e)),l.get("selectedOffset"),l.get("animation"));var v=!n&&"transition"===d;this._updateLabel(t,e,v),this.highDownOnUpdate=l.get("silent")?null:function(t,e){var n=l.isAnimationEnabled()&&u.get("hoverAnimation");"emphasis"===e?(r.ignore=r.hoverIgnore,o.ignore=o.hoverIgnore,n&&(i.stopAnimation(!0),i.animateTo({shape:{r:h.r+l.get("hoverOffset")}},300,"elasticOut"))):(r.ignore=r.normalIgnore,o.ignore=o.normalIgnore,n&&(i.stopAnimation(!0),i.animateTo({shape:{r:h.r}},300,"elasticOut")))},Vo(this)},NI._updateLabel=function(t,e,n){var i=this.childAt(1),r=this.childAt(2),o=t.hostModel,a=t.getItemModel(e),s=t.getItemLayout(e),l=s.label,u=t.getItemVisual(e,"color");if(!l||isNaN(l.x)||isNaN(l.y))return void(r.ignore=r.normalIgnore=r.hoverIgnore=i.ignore=i.normalIgnore=i.hoverIgnore=!0);var h={points:l.linePoints||[[l.x,l.y],[l.x,l.y],[l.x,l.y]]},c={x:l.x,y:l.y};n?(na(i,{shape:h},o,e),na(r,{style:c},o,e)):(i.attr({shape:h}),r.attr({style:c})),r.attr({rotation:l.rotation,origin:[l.x,l.y],z2:10});var d=a.getModel("label"),f=a.getModel("emphasis.label"),p=a.getModel("labelLine"),g=a.getModel("emphasis.labelLine"),u=t.getItemVisual(e,"color");Zo(r.style,r.hoverStyle={},d,f,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:l.text,autoColor:u,useInsideStyle:!!l.inside},{textAlign:l.textAlign,textVerticalAlign:l.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),r.ignore=r.normalIgnore=!d.get("show"),r.hoverIgnore=!f.get("show"),i.ignore=i.normalIgnore=!p.get("show"),i.hoverIgnore=!g.get("show"),i.setStyle({stroke:u,opacity:t.getItemVisual(e,"opacity")}),i.setStyle(p.getModel("lineStyle").getLineStyle()),i.hoverStyle=g.getModel("lineStyle").getLineStyle();var v=p.get("smooth");v&&v===!0&&(v=.4),i.setShape({smooth:v})},h(Qd,Nm);var FI=(pl.extend({type:"pie",init:function(){var t=new Nm;this._sectorGroup=t},render:function(t,e,n,i){if(!i||i.from!==this.uid){var r=t.getData(),o=this._data,a=this.group,s=e.get("animation"),l=!o,u=t.get("animationType"),h=t.get("animationTypeUpdate"),c=x($d,this.uid,t,s,n),d=t.get("selectedMode");if(r.diff(o).add(function(t){var e=new Qd(r,t);l&&"scale"!==u&&e.eachChild(function(t){t.stopAnimation(!0)}),d&&e.on("click",c),r.setItemGraphicEl(t,e),a.add(e)}).update(function(t,e){var n=o.getItemGraphicEl(e);l||"transition"===h||n.eachChild(function(t){t.stopAnimation(!0)}),n.updateData(r,t),n.off("click"),d&&n.on("click",c),a.add(n),r.setItemGraphicEl(t,n)}).remove(function(t){var e=o.getItemGraphicEl(t);a.remove(e)}).execute(),s&&r.count()>0&&(l?"scale"!==u:"transition"!==h)){for(var f=r.getItemLayout(0),p=1;isNaN(f.startAngle)&&p<r.count();++p)f=r.getItemLayout(p);var g=Math.max(n.getWidth(),n.getHeight())/2,v=y(a.removeClipPath,a);a.setClipPath(this._createClipPath(f.cx,f.cy,g,f.startAngle,f.clockwise,v,t,l))}else a.removeClipPath();this._data=r}},dispose:function(){},_createClipPath:function(t,e,n,i,r,o,a,s){var l=new r_({shape:{cx:t,cy:e,r0:0,r:n,startAngle:i,endAngle:i,clockwise:r}}),u=s?ia:na;return u(l,{shape:{endAngle:i+(r?1:-1)*Math.PI*2}},a,o),l},containPoint:function(t,e){var n=e.getData(),i=n.getItemLayout(0);if(i){var r=t[0]-i.cx,o=t[1]-i.cy,a=Math.sqrt(r*r+o*o);return a<=i.r&&a>=i.r0}}}),function(t,e){f(e,function(e){e.update="updateView",Tu(e,function(n,i){var r={};return i.eachComponent({mainType:"series",subType:t,query:n},function(t){t[e.method]&&t[e.method](n.name,n.dataIndex);var i=t.getData();i.each(function(e){var n=i.getName(e);r[n]=t.isSelected(n)||!1})}),{name:n.name,selected:r,seriesId:n.seriesId}})})}),VI=function(t){return{getTargetSeries:function(e){var n={},i=N();return e.eachSeriesByType(t,function(t){t.__paletteScope=n,i.set(t.uid,t)}),i},reset:function(t){var e=t.getRawData(),n={},i=t.getData();i.each(function(t){var e=i.getRawIndex(t);n[e]=t}),e.each(function(r){var o,a=n[r],s=null!=a&&i.getItemVisual(a,"color",!0),l=null!=a&&i.getItemVisual(a,"borderColor",!0);if(s&&l||(o=e.getItemModel(r)),!s){var u=o.get("itemStyle.color")||t.getColorFromPalette(e.getName(r)||r+"",t.__paletteScope,e.count());null!=a&&i.setItemVisual(a,"color",u)}if(!l){var h=o.get("itemStyle.borderColor");null!=a&&i.setItemVisual(a,"borderColor",h)}})}}},HI=Math.PI/180,WI=function(t,e,n,i,r,o){var a,s,l=t.getData(),u=[],h=!1,c=(t.get("minShowLabelAngle")||0)*HI;l.each(function(i){var o=l.getItemLayout(i),d=l.getItemModel(i),f=d.getModel("label"),p=f.get("position")||d.get("emphasis.label.position"),g=f.get("distanceToLabelLine"),v=f.get("alignTo"),m=Sa(f.get("margin"),n),y=f.get("bleedMargin"),x=f.getFont(),_=d.getModel("labelLine"),w=_.get("length");w=Sa(w,n);var b=_.get("length2");if(b=Sa(b,n),!(o.angle<c)){var S,M,I,C,T=(o.startAngle+o.endAngle)/2,A=Math.cos(T),D=Math.sin(T);a=o.cx,s=o.cy;var k=t.getFormattedLabel(i,"normal")||l.getName(i),P=Un(k,x,C,"top"),L="inside"===p||"inner"===p;if("center"===p)S=o.cx,M=o.cy,C="center";else{var O=(L?(o.r+o.r0)/2*A:o.r*A)+a,z=(L?(o.r+o.r0)/2*D:o.r*D)+s;if(S=O+3*A,M=z+3*D,!L){var E=O+A*(w+e-o.r),R=z+D*(w+e-o.r),B=E+(0>A?-1:1)*b,N=R;S="edge"===v?0>A?r+m:r+n-m:B+(0>A?-g:g),M=N,I=[[O,z],[E,R],[B,N]]}C=L?"center":"edge"===v?A>0?"right":"left":A>0?"left":"right"}var F,V=f.get("rotate");F="number"==typeof V?V*(Math.PI/180):V?0>A?-T+Math.PI:-T:0,h=!!F,o.label={x:S,y:M,position:p,height:P.height,len:w,len2:b,linePoints:I,textAlign:C,verticalAlign:"middle",rotation:F,inside:L,labelDistance:g,labelAlignTo:v,labelMargin:m,bleedMargin:y,textRect:P,text:k,font:x},L||u.push(o.label)}}),!h&&t.get("avoidLabelOverlap")&&tf(u,a,s,e,n,i,r,o)},GI=2*Math.PI,ZI=Math.PI/180,XI=function(t,e,n){e.eachSeriesByType(t,function(t){var e=t.getData(),i=e.mapDimension("value"),r=nf(t,n),o=t.get("center"),a=t.get("radius");_(a)||(a=[0,a]),_(o)||(o=[o,o]);var s=Sa(r.width,n.getWidth()),l=Sa(r.height,n.getHeight()),u=Math.min(s,l),h=Sa(o[0],s)+r.x,c=Sa(o[1],l)+r.y,d=Sa(a[0],u/2),f=Sa(a[1],u/2),p=-t.get("startAngle")*ZI,g=t.get("minAngle")*ZI,v=0;e.each(i,function(t){!isNaN(t)&&v++});var m=e.getSum(i),y=Math.PI/(m||v)*2,x=t.get("clockwise"),w=t.get("roseType"),b=t.get("stillShowZeroSum"),S=e.getDataExtent(i);S[0]=0;var M=GI,I=0,C=p,T=x?1:-1;if(e.each(i,function(t,n){var i;if(isNaN(t))return void e.setItemLayout(n,{angle:0/0,startAngle:0/0,endAngle:0/0,clockwise:x,cx:h,cy:c,r0:d,r:w?0/0:f,viewRect:r});i="area"!==w?0===m&&b?y:t*y:GI/v,g>i?(i=g,M-=g):I+=t;var o=C+T*i;e.setItemLayout(n,{angle:i,startAngle:C,endAngle:o,clockwise:x,cx:h,cy:c,r0:d,r:w?ba(t,S,[d,f]):f,viewRect:r}),C=o}),GI>M&&v)if(.001>=M){var A=GI/v;e.each(i,function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n);i.angle=A,i.startAngle=p+T*n*A,i.endAngle=p+T*(n+1)*A}})}else y=M/I,C=p,e.each(i,function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n),r=i.angle===g?g:t*y;i.startAngle=C,i.endAngle=C+T*r,C+=T*r}});WI(t,f,r.width,r.height,r.x,r.y)})},YI=function(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var i=t.getData();i.filterSelf(function(t){for(var e=i.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(e))return!1;return!0})}}}};FI("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),Pu(VI("pie")),ku(x(XI,"pie")),Iu(YI("pie")),zu({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),Eu({type:"title",render:function(t,e,n){if(this.group.removeAll(),t.get("show")){var i=this.group,r=t.getModel("textStyle"),o=t.getModel("subtextStyle"),a=t.get("textAlign"),s=D(t.get("textBaseline"),t.get("textVerticalAlign")),l=new t_({style:Yo({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),u=l.getBoundingRect(),h=t.get("subtext"),c=new t_({style:Yo({},o,{text:h,textFill:o.getTextColor(),y:u.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0);l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){$a(d,"_"+t.get("target"))}),f&&c.on("click",function(){$a(f,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(l),h&&i.add(c);var g=i.getBoundingRect(),v=t.getBoxLayoutParams();v.width=g.width,v.height=g.height;var m=Qa(v,{width:n.getWidth(),height:n.getHeight()},t.get("padding"));a||(a=t.get("left")||t.get("right"),"middle"===a&&(a="center"),"right"===a?m.x+=m.width:"center"===a&&(m.x+=m.width/2)),s||(s=t.get("top")||t.get("bottom"),"center"===s&&(s="middle"),"bottom"===s?m.y+=m.height:"middle"===s&&(m.y+=m.height/2),s=s||"top"),i.attr("position",[m.x,m.y]);var y={textAlign:a,textVerticalAlign:s};l.setStyle(y),c.setStyle(y),g=i.getBoundingRect();var x=m.margin,_=t.getItemStyle(["color","opacity"]);_.fill=t.get("backgroundColor");var w=new d_({shape:{x:g.x-x[3],y:g.y-x[0],width:g.width+x[1]+x[3],height:g.height+x[0]+x[2],r:t.get("borderRadius")},style:_,subPixelOptimize:!0,silent:!0});i.add(w)}}});var UI=sb.legend.selector,jI={all:{type:"all",title:i(UI.all)},inverse:{type:"inverse",title:i(UI.inverse)}},qI=zu({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){qI.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var e=t.selector;e===!0&&(e=t.selector=["all","inverse"]),_(e)&&f(e,function(t,n){b(t)&&(t={type:t}),e[n]=r(t,jI[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=[],n=[];t.eachRawSeries(function(i){var r=i.name;n.push(r);var o;if(i.legendVisualProvider){var a=i.legendVisualProvider,s=a.getAllNames();t.isSeriesFiltered(i)||(n=n.concat(s)),s.length?e=e.concat(s):o=!0}else o=!0;o&&sr(i)&&e.push(i.name)}),this._availableNames=n;var i=this.get("data")||e,r=p(i,function(t){return("string"==typeof t||"number"==typeof t)&&(t={name:t}),new ga(t,this,this.ecModel)},this);this._data=r},getData:function(){return this._data},select:function(t){var e=this.option.selected,n=this.get("selectedMode");if("single"===n){var i=this._data;f(i,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;f(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,e=this.option.selected;f(t,function(t){var n=t.get("name",!0);e.hasOwnProperty(n)||(e[n]=!0),e[n]=!e[n]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&u(this._availableNames,t)>=0},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}});Tu("legendToggleSelect","legendselectchanged",x(rf,"toggleSelected")),Tu("legendAllSelect","legendselectall",x(rf,"allSelect")),Tu("legendInverseSelect","legendinverseselect",x(rf,"inverseSelect")),Tu("legendSelect","legendselected",x(rf,"select")),Tu("legendUnSelect","legendunselected",x(rf,"unSelect"));var $I=x,KI=f,QI=Nm,JI=Eu({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new QI),this._backgroundEl,this.group.add(this._selectorGroup=new QI),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,n){var i=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align"),o=t.get("orient");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===o?"right":"left");var a=t.get("selector",!0),l=t.get("selectorPosition",!0);!a||l&&"auto"!==l||(l="horizontal"===o?"end":"start"),this.renderInner(r,t,e,n,a,o,l);var u=t.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},c=t.get("padding"),d=Qa(u,h,c),f=this.layoutInner(t,r,d,i,a,l),p=Qa(s({width:f.width,height:f.height},u),h,c);this.group.attr("position",[p.x-f.x,p.y-f.y]),this.group.add(this._backgroundEl=af(f,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(t,e,n,i,r,o,a){var s=this.getContentGroup(),l=N(),u=e.get("selectedMode"),h=[];n.eachRawSeries(function(t){!t.get("legendHoverLink")&&h.push(t.id)}),KI(e.getData(),function(r,o){var a=r.get("name");if(!this.newlineDisabled&&(""===a||"\n"===a))return void s.add(new QI({newline:!0}));var c=n.getSeriesByName(a)[0];if(!l.get(a)){if(c){var d=c.getData(),f=d.getVisual("color"),p=d.getVisual("borderColor");"function"==typeof f&&(f=f(c.getDataParams(0))),"function"==typeof p&&(p=p(c.getDataParams(0)));var g=d.getVisual("legendSymbol")||"roundRect",v=d.getVisual("symbol"),m=this._createItem(a,o,r,e,g,v,t,f,p,u);m.on("click",$I(lf,a,null,i,h)).on("mouseover",$I(uf,c.name,null,i,h)).on("mouseout",$I(hf,c.name,null,i,h)),l.set(a,!0)}else n.eachRawSeries(function(n){if(!l.get(a)&&n.legendVisualProvider){var s=n.legendVisualProvider;if(!s.containName(a))return;var c=s.indexOfName(a),d=s.getItemVisual(c,"color"),f=s.getItemVisual(c,"borderColor"),p="roundRect",g=this._createItem(a,o,r,e,p,null,t,d,f,u);g.on("click",$I(lf,null,a,i,h)).on("mouseover",$I(uf,null,a,i,h)).on("mouseout",$I(hf,null,a,i,h)),l.set(a,!0)}},this);_v&&(l.get(a)||console.warn(a+" series not exists. Legend data should be same with series name or data name."))}},this),r&&this._createSelector(r,e,i,o,a)},_createSelector:function(t,e,n){function i(t){var i=t.type,o=new t_({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:"all"===i?"legendAllSelect":"legendInverseSelect"})}});r.add(o);var a=e.getModel("selectorLabel"),s=e.getModel("emphasis.selectorLabel");Zo(o.style,o.hoverStyle={},a,s,{defaultText:t.title,isRectText:!1}),Vo(o)}var r=this.getSelectorGroup();KI(t,function(t){i(t)})},_createItem:function(t,e,n,i,r,o,s,l,u,h){var c=i.get("itemWidth"),d=i.get("itemHeight"),f=i.get("inactiveColor"),p=i.get("inactiveBorderColor"),g=i.get("symbolKeepAspect"),v=i.getModel("itemStyle"),m=i.isSelected(t),y=new QI,x=n.getModel("textStyle"),_=n.get("icon"),w=n.getModel("tooltip"),b=w.parentModel;r=_||r;var S=Jh(r,0,0,c,d,m?l:f,null==g?!0:g);if(y.add(sf(S,r,v,u,p,m)),!_&&o&&(o!==r||"none"===o)){var M=.8*d;"none"===o&&(o="circle");var I=Jh(o,(c-M)/2,(d-M)/2,M,M,m?l:f,null==g?!0:g);y.add(sf(I,o,v,u,p,m))}var C="left"===s?c+5:-5,T=s,A=i.get("formatter"),D=t;"string"==typeof A&&A?D=A.replace("{name}",null!=t?t:""):"function"==typeof A&&(D=A(t)),y.add(new t_({style:Yo({},x,{text:D,x:C,y:d/2,textFill:m?x.getTextColor():f,textAlign:T,textVerticalAlign:"middle"})}));var k=new d_({shape:y.getBoundingRect(),invisible:!0,tooltip:w.get("show")?a({content:t,formatter:b.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},w.option):null});return y.add(k),y.eachChild(function(t){t.silent=!0}),k.silent=!h,this.getContentGroup().add(y),Vo(y),y.__legendDataIndex=e,y},layoutInner:function(t,e,n,i,r,o){var a=this.getContentGroup(),s=this.getSelectorGroup();ow(t.get("orient"),a,t.get("itemGap"),n.width,n.height);var l=a.getBoundingRect(),u=[-l.x,-l.y];if(r){ow("horizontal",s,t.get("selectorItemGap",!0));var h=s.getBoundingRect(),c=[-h.x,-h.y],d=t.get("selectorButtonGap",!0),f=t.getOrient().index,p=0===f?"width":"height",g=0===f?"height":"width",v=0===f?"y":"x";"end"===o?c[f]+=l[p]+d:u[f]+=h[p]+d,c[1-f]+=l[g]/2-h[g]/2,s.attr("position",c),a.attr("position",u);var m={x:0,y:0};return m[p]=l[p]+d+h[p],m[g]=Math.max(l[g],h[g]),m[v]=Math.min(0,h[v]+c[1-f]),m}return a.attr("position",u),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}}),tC=function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var n=0;n<e.length;n++)if(!e[n].isSelected(t.name))return!1;return!0})};Iu(Jb.PROCESSOR.SERIES_FILTER,tC),lw.registerSubTypeDefaulter("legend",function(){return"plain"});var eC=qI.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,n,i){var r=es(t);eC.superCall(this,"init",t,e,n,i),cf(this,t,r)},mergeOption:function(t,e){eC.superCall(this,"mergeOption",t,e),cf(this,this.option,t)
}}),nC=Nm,iC=["width","height"],rC=["x","y"],oC=JI.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){oC.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new nC),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new nC),this._showController},resetInner:function(){oC.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,n,i,r,o,a){function s(t,n){var r=t+"DataIndex",o=ha(e.get("pageIcons",!0)[e.getOrient().name][n],{onclick:y(l._pageGo,l,r,e,i)},{x:-h[0]/2,y:-h[1]/2,width:h[0],height:h[1]});o.name=t,u.add(o)}var l=this;oC.superCall(this,"renderInner",t,e,n,i,r,o,a);var u=this._controllerGroup,h=e.get("pageIconSize",!0);_(h)||(h=[h,h]),s("pagePrev",0);var c=e.getModel("pageTextStyle");u.add(new t_({name:"pageText",style:{textFill:c.getTextColor(),font:c.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),s("pageNext",1)},layoutInner:function(t,e,n,r,o,a){var s=this.getSelectorGroup(),l=t.getOrient().index,u=iC[l],h=rC[l],c=iC[1-l],d=rC[1-l];o&&ow("horizontal",s,t.get("selectorItemGap",!0));var f=t.get("selectorButtonGap",!0),p=s.getBoundingRect(),g=[-p.x,-p.y],v=i(n);o&&(v[u]=n[u]-p[u]-f);var m=this._layoutContentAndController(t,r,v,l,u,c,d);if(o){if("end"===a)g[l]+=m[u]+f;else{var y=p[u]+f;g[l]-=y,m[h]-=y}m[u]+=p[u]+f,g[1-l]+=m[d]+m[c]/2-p[c]/2,m[c]=Math.max(m[c],p[c]),m[d]=Math.min(m[d],p[d]+g[1-l]),s.attr("position",g)}return m},_layoutContentAndController:function(t,e,n,i,r,o,a){var s=this.getContentGroup(),l=this._containerGroup,u=this._controllerGroup;ow(t.get("orient"),s,t.get("itemGap"),i?n.width:null,i?null:n.height),ow("horizontal",u,t.get("pageButtonItemGap",!0));var h=s.getBoundingRect(),c=u.getBoundingRect(),d=this._showController=h[r]>n[r],f=[-h.x,-h.y];e||(f[i]=s.position[i]);var p=[0,0],g=[-c.x,-c.y],v=D(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(d){var m=t.get("pageButtonPosition",!0);"end"===m?g[i]+=n[r]-c[r]:p[i]+=c[r]+v}g[1-i]+=h[o]/2-c[o]/2,s.attr("position",f),l.attr("position",p),u.attr("position",g);var y={x:0,y:0};if(y[r]=d?n[r]:h[r],y[o]=Math.max(h[o],c[o]),y[a]=Math.min(0,c[a]+g[1-i]),l.__rectSize=n[r],d){var x={x:0,y:0};x[r]=Math.max(n[r]-c[r]-v,0),x[o]=y[o],l.setClipPath(new d_({shape:x})),l.__rectSize=x[r]}else u.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var _=this._getPageInfo(t);return null!=_.pageIndex&&na(s,{position:_.contentPosition},d?t:!1),this._updatePageInfoView(t,_),y},_pageGo:function(t,e,n){var i=this._getPageInfo(e)[t];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:e.id})},_updatePageInfoView:function(t,e){var n=this._controllerGroup;f(["pagePrev","pageNext"],function(i){var r=null!=e[i+"DataIndex"],o=n.childOfName(i);o&&(o.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),o.cursor=r?"pointer":"default")});var i=n.childOfName("pageText"),r=t.get("pageFormatter"),o=e.pageIndex,a=null!=o?o+1:0,s=e.pageCount;i&&r&&i.setStyle("text",b(r)?r.replace("{current}",a).replace("{total}",s):r({current:a,total:s}))},_getPageInfo:function(t){function e(t){if(t){var e=t.getBoundingRect(),n=e[l]+t.position[a];return{s:n,e:n+e[s],i:t.__legendDataIndex}}}function n(t,e){return t.e>=e&&t.s<=e+o}var i=t.get("scrollDataIndex",!0),r=this.getContentGroup(),o=this._containerGroup.__rectSize,a=t.getOrient().index,s=iC[a],l=rC[a],u=this._findTargetItemIndex(i),h=r.children(),c=h[u],d=h.length,f=d?1:0,p={contentPosition:r.position.slice(),pageCount:f,pageIndex:f-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!c)return p;var g=e(c);p.contentPosition[a]=-g.s;for(var v=u+1,m=g,y=g,x=null;d>=v;++v)x=e(h[v]),(!x&&y.e>m.s+o||x&&!n(x,m.s))&&(m=y.i>m.i?y:x,m&&(null==p.pageNextDataIndex&&(p.pageNextDataIndex=m.i),++p.pageCount)),y=x;for(var v=u-1,m=g,y=g,x=null;v>=-1;--v)x=e(h[v]),x&&n(y,x.s)||!(m.i<y.i)||(y=m,null==p.pagePrevDataIndex&&(p.pagePrevDataIndex=m.i),++p.pageCount,++p.pageIndex),m=x;return p},_findTargetItemIndex:function(t){if(!this._showController)return 0;var e,n,i=this.getContentGroup();return i.eachChild(function(i,r){var o=i.__legendDataIndex;null==n&&null!=o&&(n=r),o===t&&(e=r)}),null!=e?e:n}});Tu("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})});var aC=function(t,e){var n,i=[],r=t.seriesIndex;if(null==r||!(n=e.getSeriesByIndex(r)))return{point:[]};var o=n.getData(),a=ur(o,t);if(null==a||0>a||_(a))return{point:[]};var s=o.getItemGraphicEl(a),l=n.coordinateSystem;if(n.getTooltipPosition)i=n.getTooltipPosition(a)||[];else if(l&&l.dataToPoint)i=l.dataToPoint(o.getValues(p(l.dimensions,function(t){return o.mapDimension(t)}),a,!0))||[];else if(s){var u=s.getBoundingRect().clone();u.applyTransform(s.transform),i=[u.x+u.width/2,u.y+u.height/2]}return{point:i,el:s}},sC=f,lC=x,uC=hr(),hC=function(t,e,n){var i=t.currTrigger,r=[t.x,t.y],o=t,a=t.dispatchAction||y(n.dispatchAction,n),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){wf(r)&&(r=aC({seriesIndex:o.seriesIndex,dataIndex:o.dataIndex},e).point);var l=wf(r),u=o.axesInfo,h=s.axesInfo,c="leave"===i||wf(r),d={},f={},p={list:[],map:{}},g={showPointer:lC(pf,f),showTooltip:lC(gf,p)};sC(s.coordSysMap,function(t,e){var n=l||t.containPoint(r);sC(s.coordSysAxesInfo[e],function(t){var e=t.axis,i=xf(u,t);if(!c&&n&&(!u||i)){var o=i&&i.value;null!=o||l||(o=e.pointToData(r)),null!=o&&df(t,o,g,!1,d)}})});var v={};return sC(h,function(t,e){var n=t.linkGroup;n&&!f[e]&&sC(n.axesInfo,function(e,i){var r=f[i];if(e!==t&&r){var o=r.value;n.mapper&&(o=t.axis.scale.parse(n.mapper(o,_f(e),_f(t)))),v[t.key]=o}})}),sC(v,function(t,e){df(h[e],t,g,!0,d)}),vf(f,h,d),mf(p,r,t,a),yf(h,a,n),d}},cC=(zu({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),hr()),dC=f,fC=Eu({type:"axisPointer",render:function(t,e,n){var i=e.getComponent("tooltip"),r=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";bf("axisPointer",n,function(t,e,n){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){Af(e.getZr(),"axisPointer"),fC.superApply(this._model,"remove",arguments)},dispose:function(t,e){Af("axisPointer",e),fC.superApply(this._model,"dispose",arguments)}}),pC=hr(),gC=i,vC=y;Df.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,i){var r=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,i||this._lastValue!==r||this._lastStatus!==o){this._lastValue=r,this._lastStatus=o;var a=this._group,s=this._handle;if(!o||"hide"===o)return a&&a.hide(),void(s&&s.hide());a&&a.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,n);var u=l.graphicKey;u!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=u;var h=this._moveAnimation=this.determineAnimation(t,e);if(a){var c=x(kf,e,h);this.updatePointerEl(a,l,c,e),this.updateLabelEl(a,l,c,e)}else a=this._group=new Nm,this.createPointerEl(a,l,t,e),this.createLabelEl(a,l,t,e),n.getZr().add(a);zf(a,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"===n||null==n){var a=this.animationThreshold;if(r&&i.getBandWidth()>a)return!0;if(o){var s=pd(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1}return n===!0},makeElOption:function(){},createPointerEl:function(t,e){var n=e.pointer;if(n){var i=pC(t).pointerEl=new B_[n.type](gC(e.pointer));t.add(i)}},createLabelEl:function(t,e,n,i){if(e.label){var r=pC(t).labelEl=new d_(gC(e.label));t.add(r),Lf(r,i)}},updatePointerEl:function(t,e,n){var i=pC(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=pC(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),Lf(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),o=e.get("status");if(!r.get("show")||!o||"hide"===o)return i&&n.remove(i),void(this._handle=null);var a;this._handle||(a=!0,i=this._handle=ha(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Jv(t.event)},onmousedown:vC(this._onHandleDragMove,this,0,0),drift:vC(this._onHandleDragMove,this),ondragend:vC(this._onHandleDragEnd,this)}),n.add(i)),zf(i,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];i.setStyle(r.getItemStyle(null,s));var l=r.get("size");_(l)||(l=[l,l]),i.attr("scale",[l[0]/2,l[1]/2]),_l(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,a)}},_moveHandleToValue:function(t,e){kf(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Of(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(Of(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(Of(i)),pC(n).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},_onHandleDragEnd:function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}},Df.prototype.constructor=Df,yr(Df);var mC=Df.extend({makeElOption:function(t,e,n,i,r){var o=n.axis,a=o.grid,s=i.get("type"),l=Gf(a,o).getOtherAxis(o).getGlobalExtent(),u=o.toGlobalCoord(o.dataToCoord(e,!0));if(s&&"none"!==s){var h=Ef(i),c=yC[s](o,u,l);c.style=h,t.graphicKey=c.type,t.pointer=c}var d=_d(a.model,n);Vf(e,t,d,n,i,r)},getHandleTransform:function(t,e,n){var i=_d(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:Ff(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n){var i=n.axis,r=i.grid,o=i.getGlobalExtent(!0),a=Gf(r,i).getOtherAxis(i).getGlobalExtent(),s="x"===i.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(o[1],l[s]),l[s]=Math.max(o[0],l[s]);var u=(a[1]+a[0])/2,h=[u,u];h[s]=l[s];var c=[{verticalAlign:"middle"},{align:"center"}];return{position:l,rotation:t.rotation,cursorPoint:h,tooltipOption:c[s]}}}),yC={line:function(t,e,n){var i=Hf([e,n[0]],[e,n[1]],Zf(t));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:Wf([e-i/2,n[0]],[i,r],Zf(t))}}};lI.registerAxisPointerClass("CartesianAxisPointer",mC),Mu(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!_(e)&&(t.axisPointer.link=[e])}}),Iu(Jb.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=sd(t,e)}),Tu({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},hC),zu({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var xC=f,_C=Va,wC=["","-webkit-","-moz-","-o-"],bC="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";qf.prototype={constructor:qf,_enterable:!0,update:function(t){var e=this._container,n=e.currentStyle||document.defaultView.getComputedStyle(e),i=e.style;"absolute"!==i.position&&"absolute"!==n.position&&(i.position="relative");var r=t.get("alwaysShowContent");r&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],n=t*this._zr.getWidth(),i=e*this._zr.getHeight();this.moveTo(n,i)},show:function(t){clearTimeout(this._hideTimeout);var e=this.el,n=this._styleCoord;e.style.cssText=bC+Uf(t)+";left:"+n[0]+"px;top:"+n[1]+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n=this._styleCoord;jf(n,this._zr,this._appendToBody,t,e);var i=this.el.style;i.left=n[0]+"px",i.top=n[1]+"px"},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){this.el.parentNode.removeChild(this.el)},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var n=document.defaultView.getComputedStyle(this.el);n&&(t+=parseInt(n.borderLeftWidth,10)+parseInt(n.borderRightWidth,10),e+=parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10))}return{width:t,height:e}}},Kf.prototype={constructor:Kf,_enterable:!0,update:function(t){var e=t.get("alwaysShowContent");e&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],n=t*this._zr.getWidth(),i=e*this._zr.getHeight();this.moveTo(n,i)},show:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,n){this.el&&this._zr.remove(this.el);for(var i={},r=t,o="{marker",a="|}",s=r.indexOf(o);s>=0;){var l=r.indexOf(a),u=r.substr(s+o.length,l-s-o.length);i["marker"+u]=u.indexOf("sub")>-1?{textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[u],textOffset:[3,0]}:{textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[u]},r=r.substr(l+1),s=r.indexOf("{marker")}var h=n.getModel("textStyle"),c=h.get("fontSize"),d=n.get("textLineHeight");null==d&&(d=Math.round(3*c/2)),this.el=new t_({style:Yo({},h,{rich:i,text:t,textBackgroundColor:n.get("backgroundColor"),textBorderRadius:n.get("borderRadius"),textFill:n.get("textStyle.color"),textPadding:n.get("padding"),textLineHeight:d}),z:n.get("z")}),this._zr.add(this.el);var f=this;this.el.on("mouseover",function(){f._enterable&&(clearTimeout(f._hideTimeout),f._show=!0),f._inContent=!0}),this.el.on("mouseout",function(){f._enterable&&f._show&&f.hideLater(f._hideDelay),f._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){if(this.el){var n=this._styleCoord;$f(n,this._zr,t,e),this.el.attr("position",[n[0],n[1]])}},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){clearTimeout(this._hideTimeout),this.el&&this._zr.remove(this.el)},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var SC=y,MC=f,IC=Sa,CC=new d_({shape:{x:-1,y:-1,width:2,height:2}});Eu({type:"tooltip",init:function(t,e){if(!Mv.node){var n=t.getComponent("tooltip"),i=n.get("renderMode");this._renderMode=gr(i);var r;"html"===this._renderMode?(r=new qf(e.getDom(),e,{appendToBody:n.get("appendToBody",!0)}),this._newLine="<br/>"):(r=new Kf(e),this._newLine="\n"),this._tooltipContent=r}},render:function(t,e,n){if(!Mv.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var i=this._tooltipContent;i.update(t),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");bf("itemTooltip",this._api,SC(function(t,n,i){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(n,i):"leave"===t&&this._hide(i))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,n=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var i=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&i.manuallyShowTip(t,e,n,{x:i._lastX,y:i._lastY})})}},manuallyShowTip:function(t,e,n,i){if(i.from!==this.uid&&!Mv.node){var r=Jf(i,n);this._ticket="";var o=i.dataByCoordSys;if(i.tooltip&&null!=i.x&&null!=i.y){var a=CC;a.position=[i.x,i.y],a.update(),a.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)}else if(o)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},r);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var s=aC(i,e),l=s.point[0],u=s.point[1];null!=l&&null!=u&&this._tryShow({offsetX:l,offsetY:u,position:i.position,target:s.el},r)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r))}},manuallyHideTip:function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(Jf(i,n))},_manuallyAxisShowTip:function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s){var l=s.getData(),t=Qf([l.getItemModel(o),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}}},_tryShow:function(t,e){var n=t.target,i=this._tooltipModel;if(i){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):n&&null!=n.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,n,e)):n&&n.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,n,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var n=t.get("showDelay");e=y(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},_showAxisTooltip:function(t,e){var n=this._ecModel,i=this._tooltipModel,o=[e.offsetX,e.offsetY],a=[],s=[],l=Qf([e.tooltipOption,i]),u=this._renderMode,h=this._newLine,c={};MC(t,function(t){MC(t.dataByAxis,function(t){var e=n.getComponent(t.axisDim+"Axis",t.axisIndex),i=t.value,o=[];if(e&&null!=i){var l=Nf(i,e.axis,n,t.seriesDataIndices,t.valueLabelOpt);f(t.seriesDataIndices,function(a){var h=n.getSeriesByIndex(a.seriesIndex),d=a.dataIndexInside,f=h&&h.getDataParams(d);if(f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=Uh(e.axis,i),f.axisValueLabel=l,f){s.push(f);var p,g=h.formatTooltip(d,!0,null,u);if(S(g)){p=g.html;var v=g.markers;r(c,v)}else p=g;o.push(p)}});var d=l;a.push("html"!==u?o.join(h):(d?Ha(d)+h:"")+o.join(h))}})},this),a.reverse(),a=a.join(this._newLine+this._newLine);var d=e.position;this._showOrMove(l,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(l,d,o[0],o[1],this._tooltipContent,s):this._showTooltipContent(l,a,s,Math.random(),o[0],o[1],d,void 0,c)})},_showSeriesItemTooltip:function(t,e,n){var i=this._ecModel,r=e.seriesIndex,o=i.getSeriesByIndex(r),a=e.dataModel||o,s=e.dataIndex,l=e.dataType,u=a.getData(l),h=Qf([u.getItemModel(s),a,o&&(o.coordinateSystem||{}).model,this._tooltipModel]),c=h.get("trigger");if(null==c||"item"===c){var d,f,p=a.getDataParams(s,l),g=a.formatTooltip(s,!1,l,this._renderMode);S(g)?(d=g.html,f=g.markers):(d=g,f=null);var v="item_"+a.name+"_"+s;this._showOrMove(h,function(){this._showTooltipContent(h,d,p,v,t.offsetX,t.offsetY,t.position,t.target,f)}),n({type:"showTip",dataIndexInside:s,dataIndex:u.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip;if("string"==typeof i){var r=i;i={content:r,formatter:r}}var o=new ga(i,this._tooltipModel,this._ecModel),a=o.get("content"),s=Math.random();this._showOrMove(o,function(){this._showTooltipContent(o,a,o.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,n,i,r,o,a,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var u=this._tooltipContent,h=t.get("formatter");a=a||t.get("position");var c=e;if(h&&"string"==typeof h)c=Wa(h,n,!0);else if("function"==typeof h){var d=SC(function(e,i){e===this._ticket&&(u.setContent(i,l,t),this._updatePosition(t,a,r,o,u,n,s))},this);this._ticket=i,c=h(n,i,d)}u.setContent(c,l,t),u.show(t),this._updatePosition(t,a,r,o,u,n,s)}},_updatePosition:function(t,e,n,i,r,o,a){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var u=r.getSize(),h=t.get("align"),c=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();if(a&&d.applyTransform(a.transform),"function"==typeof e&&(e=e([n,i],o,r.el,d,{viewSize:[s,l],contentSize:u.slice()})),_(e))n=IC(e[0],s),i=IC(e[1],l);else if(S(e)){e.width=u[0],e.height=u[1];var f=Qa(e,{width:s,height:l});n=f.x,i=f.y,h=null,c=null}else if("string"==typeof e&&a){var p=np(e,d,u);n=p[0],i=p[1]}else{var p=tp(n,i,r,s,l,h?null:20,c?null:20);n=p[0],i=p[1]}if(h&&(n-=ip(h)?u[0]/2:"right"===h?u[0]:0),c&&(i-=ip(c)?u[1]/2:"bottom"===c?u[1]:0),t.get("confine")){var p=ep(n,i,r,s,l);n=p[0],i=p[1]}r.moveTo(n,i)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,n=!!e&&e.length===t.length;return n&&MC(e,function(e,i){var r=e.dataByAxis||{},o=t[i]||{},a=o.dataByAxis||[];n&=r.length===a.length,n&&MC(r,function(t,e){var i=a[e]||{},r=t.seriesDataIndices||[],o=i.seriesDataIndices||[];n&=t.value===i.value&&t.axisType===i.axisType&&t.axisId===i.axisId&&r.length===o.length,n&&MC(r,function(t,e){var i=o[e];n&=t.seriesIndex===i.seriesIndex&&t.dataIndex===i.dataIndex})})}),this._lastDataByCoordSys=t,!!n},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){Mv.node||(this._tooltipContent.dispose(),Af("itemTooltip",e))}}),Tu({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),Tu({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var TC=function(t){var e=t&&t.timeline;_(e)||(e=e?[e]:[]),f(e,function(t){t&&rp(t)})};lw.registerSubTypeDefaulter("timeline",function(){return"slider"}),Tu({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(t,e){var n=e.getComponent("timeline");return n&&null!=t.currentIndex&&(n.setCurrentIndex(t.currentIndex),!n.get("loop",!0)&&n.isIndexMax()&&n.setPlayState(!1)),e.resetOption("timeline"),s({currentIndex:n.option.currentIndex},t)}),Tu({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(t,e){var n=e.getComponent("timeline");n&&null!=t.playState&&n.setPlayState(t.playState)});var AC=lw.extend({type:"timeline",layoutMode:"box",defaultOption:{zlevel:0,z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},init:function(t,e,n){this._data,this._names,this.mergeDefaultAndTheme(t,n),this._initData()},mergeOption:function(){AC.superApply(this,"mergeOption",arguments),this._initData()},setCurrentIndex:function(t){null==t&&(t=this.option.currentIndex);var e=this._data.count();this.option.loop?t=(t%e+e)%e:(t>=e&&(t=e-1),0>t&&(t=0)),this.option.currentIndex=t},getCurrentIndex:function(){return this.option.currentIndex},isIndexMax:function(){return this.getCurrentIndex()>=this._data.count()-1},setPlayState:function(t){this.option.autoPlay=!!t},getPlayState:function(){return!!this.option.autoPlay},_initData:function(){var t=this.option,e=t.data||[],n=t.axisType,r=this._names=[];if("category"===n){var o=[];f(e,function(t,e){var n,a=ir(t);S(t)?(n=i(t),n.value=e):n=e,o.push(n),b(a)||null!=a&&!isNaN(a)||(a=""),r.push(a+"")}),e=o}var a={category:"ordinal",time:"time"}[n]||"number",s=this._data=new LS([{name:"value",type:a}],this);s.initData(e,r)},getData:function(){return this._data},getCategories:function(){return"category"===this.get("axisType")?this._names.slice():void 0}}),DC=AC.extend({type:"timeline.slider",defaultOption:{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"emptyCircle",symbolSize:10,lineStyle:{show:!0,width:2,color:"#304654"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#304654"},itemStyle:{color:"#304654",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:13,color:"#c23531",borderWidth:5,borderColor:"rgba(194,53,49, 0.5)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:22,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"path://M18.6,50.8l22.5-22.5c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.5-0.3-0.7L18.7,4.4c-0.1-0.1-0.2-0.3-0.2-0.5 c0-0.4,0.3-0.8,0.8-0.8c0.2,0,0.5,0.1,0.6,0.3l23.5,23.5l0,0c0.2,0.2,0.3,0.4,0.3,0.7c0,0.3-0.1,0.5-0.3,0.7l-0.1,0.1L19.7,52 c-0.1,0.1-0.3,0.2-0.5,0.2c-0.4,0-0.8-0.3-0.8-0.8C18.4,51.2,18.5,51,18.6,50.8z",prevIcon:"path://M43,52.8L20.4,30.3c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.3,0.1-0.5,0.3-0.7L42.9,6.4c0.1-0.1,0.2-0.3,0.2-0.5 c0-0.4-0.3-0.8-0.8-0.8c-0.2,0-0.5,0.1-0.6,0.3L18.3,28.8l0,0c-0.2,0.2-0.3,0.4-0.3,0.7c0,0.3,0.1,0.5,0.3,0.7l0.1,0.1L41.9,54 c0.1,0.1,0.3,0.2,0.5,0.2c0.4,0,0.8-0.3,0.8-0.8C43.2,53.2,43.1,53,43,52.8z",color:"#304654",borderColor:"#304654",borderWidth:1},emphasis:{label:{show:!0,color:"#c23531"},itemStyle:{color:"#c23531"},controlStyle:{color:"#c23531",borderColor:"#c23531",borderWidth:2}},data:[]}});c(DC,Xw);var kC=$w.extend({type:"timeline"}),PC=function(t,e,n,i){TM.call(this,t,e,n),this.type=i||"value",this.model=null};PC.prototype={constructor:PC,getLabelModel:function(){return this.model.getModel("label")},isHorizontal:function(){return"horizontal"===this.model.get("orient")}},h(PC,TM);var LC=y,OC=f,zC=Math.PI;kC.extend({type:"timeline.slider",init:function(t,e){this.api=e,this._axis,this._viewRect,this._timer,this._currentPointer,this._mainGroup,this._labelGroup},render:function(t,e,n){if(this.model=t,this.api=n,this.ecModel=e,this.group.removeAll(),t.get("show",!0)){var i=this._layout(t,n),r=this._createGroup("mainGroup"),o=this._createGroup("labelGroup"),a=this._axis=this._createAxis(i,t);t.formatTooltip=function(t){return Ha(a.scale.getLabel(t))},OC(["AxisLine","AxisTick","Control","CurrentPointer"],function(e){this["_render"+e](i,r,a,t)},this),this._renderAxisLabel(i,o,a,t),this._position(i,t)}this._doPlayStop()},remove:function(){this._clearTimer(),this.group.removeAll()},dispose:function(){this._clearTimer()},_layout:function(t,e){var n=t.get("label.position"),i=t.get("orient"),r=sp(t,e);null==n||"auto"===n?n="horizontal"===i?r.y+r.height/2<e.getHeight()/2?"-":"+":r.x+r.width/2<e.getWidth()/2?"+":"-":isNaN(n)&&(n={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[i][n]);var o={horizontal:"center",vertical:n>=0||"+"===n?"left":"right"},a={horizontal:n>=0||"+"===n?"top":"bottom",vertical:"middle"},s={horizontal:0,vertical:zC/2},l="vertical"===i?r.height:r.width,u=t.getModel("controlStyle"),h=u.get("show",!0),c=h?u.get("itemSize"):0,d=h?u.get("itemGap"):0,f=c+d,p=t.get("label.rotate")||0;p=p*zC/180;var g,v,m,y,x=u.get("position",!0),_=h&&u.get("showPlayBtn",!0),w=h&&u.get("showPrevBtn",!0),b=h&&u.get("showNextBtn",!0),S=0,M=l;return"left"===x||"bottom"===x?(_&&(g=[0,0],S+=f),w&&(v=[S,0],S+=f),b&&(m=[M-c,0],M-=f)):(_&&(g=[M-c,0],M-=f),w&&(v=[0,0],S+=f),b&&(m=[M-c,0],M-=f)),y=[S,M],t.get("inverse")&&y.reverse(),{viewRect:r,mainLength:l,orient:i,rotation:s[i],labelRotation:p,labelPosOpt:n,labelAlign:t.get("label.align")||o[i],labelBaseline:t.get("label.verticalAlign")||t.get("label.baseline")||a[i],playPosition:g,prevBtnPosition:v,nextBtnPosition:m,axisExtent:y,controlSize:c,controlGap:d}},_position:function(t){function e(t){var e=t.position;t.origin=[h[0][0]-e[0],h[1][0]-e[1]]}function n(t){return[[t.x,t.x+t.width],[t.y,t.y+t.height]]}function i(t,e,n,i,r){t[i]+=n[i][r]-e[i][r]}var r=this._mainGroup,o=this._labelGroup,a=t.viewRect;if("vertical"===t.orient){var s=Oe(),l=a.x,u=a.y+a.height;Be(s,s,[-l,-u]),Ne(s,s,-zC/2),Be(s,s,[l,u]),a=a.clone(),a.applyTransform(s)}var h=n(a),c=n(r.getBoundingRect()),d=n(o.getBoundingRect()),f=r.position,p=o.position;p[0]=f[0]=h[0][0];var g=t.labelPosOpt;if(isNaN(g)){var v="+"===g?0:1;i(f,c,h,1,v),i(p,d,h,1,1-v)}else{var v=g>=0?0:1;i(f,c,h,1,v),p[1]=f[1]+g}r.attr("position",f),o.attr("position",p),r.rotation=o.rotation=t.rotation,e(r),e(o)},_createAxis:function(t,e){var n=e.getData(),i=e.get("axisType"),r=Zh(e,i);r.getTicks=function(){return n.mapArray(["value"],function(t){return t})};var o=n.getDataExtent("value");r.setExtent(o[0],o[1]),r.niceTicks();var a=new PC("value",r,t.axisExtent,i);return a.model=e,a},_createGroup:function(t){var e=this["_"+t]=new Nm;return this.group.add(e),e},_renderAxisLine:function(t,e,n,i){var r=n.getExtent();
i.get("lineStyle.show")&&e.add(new p_({shape:{x1:r[0],y1:0,x2:r[1],y2:0},style:a({lineCap:"round"},i.getModel("lineStyle").getLineStyle()),silent:!0,z2:1}))},_renderAxisTick:function(t,e,n,i){var r=i.getData(),o=n.scale.getTicks();OC(o,function(t){var o=n.dataToCoord(t),a=r.getItemModel(t),s=a.getModel("itemStyle"),l=a.getModel("emphasis.itemStyle"),u={position:[o,0],onclick:LC(this._changeTimeline,this,t)},h=up(a,s,e,u);Vo(h,l.getItemStyle()),a.get("tooltip")?(h.dataIndex=t,h.dataModel=i):h.dataIndex=h.dataModel=null},this)},_renderAxisLabel:function(t,e,n,i){var r=n.getLabelModel();if(r.get("show")){var o=i.getData(),a=n.getViewLabels();OC(a,function(i){var r=i.tickValue,a=o.getItemModel(r),s=a.getModel("label"),l=a.getModel("emphasis.label"),u=n.dataToCoord(i.tickValue),h=new t_({position:[u,0],rotation:t.labelRotation-t.rotation,onclick:LC(this._changeTimeline,this,r),silent:!1});Yo(h.style,s,{text:i.formattedLabel,textAlign:t.labelAlign,textVerticalAlign:t.labelBaseline}),e.add(h),Vo(h,Yo({},l))},this)}},_renderControl:function(t,e,n,i){function r(t,n,r,h){if(t){var c={position:t,origin:[o/2,0],rotation:h?-a:0,rectHover:!0,style:s,onclick:r},d=lp(i,n,u,c);e.add(d),Vo(d,l)}}var o=t.controlSize,a=t.rotation,s=i.getModel("controlStyle").getItemStyle(),l=i.getModel("emphasis.controlStyle").getItemStyle(),u=[0,-o/2,o,o],h=i.getPlayState(),c=i.get("inverse",!0);r(t.nextBtnPosition,"controlStyle.nextIcon",LC(this._changeTimeline,this,c?"-":"+")),r(t.prevBtnPosition,"controlStyle.prevIcon",LC(this._changeTimeline,this,c?"+":"-")),r(t.playPosition,"controlStyle."+(h?"stopIcon":"playIcon"),LC(this._handlePlayClick,this,!h),!0)},_renderCurrentPointer:function(t,e,n,i){var r=i.getData(),o=i.getCurrentIndex(),a=r.getItemModel(o).getModel("checkpointStyle"),s=this,l={onCreate:function(t){t.draggable=!0,t.drift=LC(s._handlePointerDrag,s),t.ondragend=LC(s._handlePointerDragend,s),hp(t,o,n,i,!0)},onUpdate:function(t){hp(t,o,n,i)}};this._currentPointer=up(a,a,this._mainGroup,{},this._currentPointer,l)},_handlePlayClick:function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},_handlePointerDrag:function(t,e,n){this._clearTimer(),this._pointerChangeTimeline([n.offsetX,n.offsetY])},_handlePointerDragend:function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},_pointerChangeTimeline:function(t,e){var n=this._toAxisCoord(t)[0],i=this._axis,r=Ia(i.getExtent().slice());n>r[1]&&(n=r[1]),n<r[0]&&(n=r[0]),this._currentPointer.position[0]=n,this._currentPointer.dirty();var o=this._findNearestTick(n),a=this.model;(e||o!==a.getCurrentIndex()&&a.get("realtime"))&&this._changeTimeline(o)},_doPlayStop:function(){function t(){var t=this.model;this._changeTimeline(t.getCurrentIndex()+(t.get("rewind",!0)?-1:1))}this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(LC(t,this),this.model.get("playInterval")))},_toAxisCoord:function(t){var e=this._mainGroup.getLocalTransform();return oa(t,e,!0)},_findNearestTick:function(t){var e,n=this.model.getData(),i=1/0,r=this._axis;return n.each(["value"],function(n,o){var a=r.dataToCoord(n),s=Math.abs(a-t);i>s&&(i=s,e=o)}),e},_clearTimer:function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},_changeTimeline:function(t){var e=this.model.getCurrentIndex();"+"===t?t=e+1:"-"===t&&(t=e-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})}}),Mu(TC),lw.registerSubTypeDefaulter("dataZoom",function(){return"slider"});var EC=["x","y","z","radius","angle","single"],RC=["cartesian2d","polar","singleAxis"],BC=dp(EC,["axisIndex","axis","index","id"]),NC=function(t,e,n,i,r,o){t=t||0;var a=n[1]-n[0];if(null!=r&&(r=gp(r,[0,a])),null!=o&&(o=Math.max(o,null!=r?r:0)),"all"===i){var s=Math.abs(e[1]-e[0]);s=gp(s,[0,a]),r=o=gp(s,[r,o]),i=0}e[0]=gp(e[0],n),e[1]=gp(e[1],n);var l=pp(e,i);e[i]+=t;var u=r||0,h=n.slice();l.sign<0?h[0]+=u:h[1]-=u,e[i]=gp(e[i],h);var c=pp(e,i);null!=r&&(c.sign!==l.sign||c.span<r)&&(e[1-i]=e[i]+l.sign*r);var c=pp(e,i);return null!=o&&c.span>o&&(e[1-i]=e[i]+c.sign*o),e},FC=f,VC=Ia,HC=function(t,e,n,i){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=i,this._dataZoomModel=n};HC.prototype={constructor:HC,hostedBy:function(t){return this._dataZoomModel===t},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var t=[],e=this.ecModel;return e.eachSeries(function(n){if(cp(n.get("coordinateSystem"))){var i=this._dimName,r=e.queryComponents({mainType:i+"Axis",index:n.get(i+"AxisIndex"),id:n.get(i+"AxisId")})[0];this._axisIndex===(r&&r.componentIndex)&&t.push(n)}},this),t},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var t,e,n=this._dimName,i=this.ecModel,r=this.getAxisModel(),o="x"===n||"y"===n;o?(e="gridIndex",t="x"===n?"y":"x"):(e="polarIndex",t="angle"===n?"radius":"angle");var a;return i.eachComponent(t+"Axis",function(t){(t.get(e)||0)===(r.get(e)||0)&&(a=t)}),a},getMinMaxSpan:function(){return i(this._minMaxSpan)},calculateDataWindow:function(t){function e(t,e,n,i,r){var a=r?"Span":"ValueSpan";NC(0,t,n,"all",h["min"+a],h["max"+a]);for(var s=0;2>s;s++)e[s]=ba(t[s],n,i,!0),r&&(e[s]=o.parse(e[s]))}var n,i=this._dataExtent,r=this.getAxisModel(),o=r.axis.scale,a=this._dataZoomModel.getRangePropMode(),s=[0,100],l=[],u=[];FC(["start","end"],function(e,r){var h=t[e],c=t[e+"Value"];"percent"===a[r]?(null==h&&(h=s[r]),c=o.parse(ba(h,s,i))):(n=!0,c=null==c?i[r]:o.parse(c),h=ba(c,i,s)),u[r]=c,l[r]=h}),VC(u),VC(l);var h=this._minMaxSpan;return n?e(u,l,i,s,!1):e(l,u,s,i,!0),{valueWindow:u,percentWindow:l}},reset:function(t){if(t===this._dataZoomModel){var e=this.getTargetSeriesModels();this._dataExtent=vp(this,this._dimName,e),xp(this);var n=this.calculateDataWindow(t.settledOption);this._valueWindow=n.valueWindow,this._percentWindow=n.percentWindow,yp(this)}},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,yp(this,!0))},filterData:function(t){function e(t){return t>=o[0]&&t<=o[1]}if(t===this._dataZoomModel){var n=this._dimName,i=this.getTargetSeriesModels(),r=t.get("filterMode"),o=this._valueWindow;"none"!==r&&FC(i,function(t){var i=t.getData(),a=i.mapDimension(n,!0);a.length&&("weakFilter"===r?i.filterSelf(function(t){for(var e,n,r,s=0;s<a.length;s++){var l=i.get(a[s],t),u=!isNaN(l),h=l<o[0],c=l>o[1];if(u&&!h&&!c)return!0;u&&(r=!0),h&&(e=!0),c&&(n=!0)}return r&&e&&n}):FC(a,function(n){if("empty"===r)t.setData(i=i.map(n,function(t){return e(t)?t:0/0}));else{var a={};a[n]=o,i.selectRange(a)}}),FC(a,function(t){i.setApproximateExtent(o,t)}))})}}};var WC=f,GC=BC,ZC=zu({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(t,e,n){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var i=_p(t);this.settledOption=i,this.mergeDefaultAndTheme(t,n),this.doInit(i)},mergeOption:function(t){var e=_p(t);r(this.option,t,!0),r(this.settledOption,e,!0),this.doInit(e)},doInit:function(t){var e=this.option;Mv.canvasSupported||(e.realtime=!1),this._setDefaultThrottle(t),wp(this,t);var n=this.settledOption;WC([["start","startValue"],["end","endValue"]],function(t,i){"value"===this._rangePropMode[i]&&(e[t[0]]=n[t[0]]=null)},this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var t=this._axisProxies;this.eachTargetAxis(function(e,n,i,r){var o=this.dependentModels[e.axis][n],a=o.__dzAxisProxy||(o.__dzAxisProxy=new HC(e.name,n,this,r));t[e.name+"_"+n]=a},this)},_resetTarget:function(){var t=this.option,e=this._judgeAutoMode();GC(function(e){var n=e.axisIndex;t[n]=er(t[n])},this),"axisIndex"===e?this._autoSetAxisIndex():"orient"===e&&this._autoSetOrient()},_judgeAutoMode:function(){var t=this.option,e=!1;GC(function(n){null!=t[n.axisIndex]&&(e=!0)},this);var n=t.orient;return null==n&&e?"orient":e?void 0:(null==n&&(t.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var t=!0,e=this.get("orient",!0),n=this.option,i=this.dependentModels;if(t){var r="vertical"===e?"y":"x";i[r+"Axis"].length?(n[r+"AxisIndex"]=[0],t=!1):WC(i.singleAxis,function(i){t&&i.get("orient",!0)===e&&(n.singleAxisIndex=[i.componentIndex],t=!1)})}t&&GC(function(e){if(t){var i=[],r=this.dependentModels[e.axis];if(r.length&&!i.length)for(var o=0,a=r.length;a>o;o++)"category"===r[o].get("type")&&i.push(o);n[e.axisIndex]=i,i.length&&(t=!1)}},this),t&&this.ecModel.eachSeries(function(t){this._isSeriesHasAllAxesTypeOf(t,"value")&&GC(function(e){var i=n[e.axisIndex],r=t.get(e.axisIndex),o=t.get(e.axisId),a=t.ecModel.queryComponents({mainType:e.axis,index:r,id:o})[0];if(_v&&!a)throw new Error(e.axis+' "'+A(r,o,0)+'" not found');r=a.componentIndex,u(i,r)<0&&i.push(r)})},this)},_autoSetOrient:function(){var t;this.eachTargetAxis(function(e){!t&&(t=e.name)},this),this.option.orient="y"===t?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(t,e){var n=!0;return GC(function(i){var r=t.get(i.axisIndex),o=this.dependentModels[i.axis][r];o&&o.get("type")===e||(n=!1)},this),n},_setDefaultThrottle:function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&e.animationDurationUpdate>0?100:20}},getFirstTargetAxisModel:function(){var t;return GC(function(e){if(null==t){var n=this.get(e.axisIndex);n.length&&(t=this.dependentModels[e.axis][n[0]])}},this),t},eachTargetAxis:function(t,e){var n=this.ecModel;GC(function(i){WC(this.get(i.axisIndex),function(r){t.call(e,i,r,this,n)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},getAxisModel:function(t,e){var n=this.getAxisProxy(t,e);return n&&n.getAxisModel()},setRawRange:function(t){var e=this.option,n=this.settledOption;WC([["start","startValue"],["end","endValue"]],function(i){(null!=t[i[0]]||null!=t[i[1]])&&(e[i[0]]=n[i[0]]=t[i[0]],e[i[1]]=n[i[1]]=t[i[1]])},this),wp(this,t)},setCalculatedRange:function(t){var e=this.option;WC(["start","startValue","end","endValue"],function(n){e[n]=t[n]})},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();return t?t.getDataPercentWindow():void 0},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var n=this.findRepresentativeAxisProxy();return n?n.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(t){if(t)return t.__dzAxisProxy;var e=this._axisProxies;for(var n in e)if(e.hasOwnProperty(n)&&e[n].hostedBy(this))return e[n];for(var n in e)if(e.hasOwnProperty(n)&&!e[n].hostedBy(this))return e[n]},getRangePropMode:function(){return this._rangePropMode.slice()}}),XC=$w.extend({type:"dataZoom",render:function(t,e,n){this.dataZoomModel=t,this.ecModel=e,this.api=n},getTargetCoordInfo:function(){function t(t,e,n,i){for(var r,o=0;o<n.length;o++)if(n[o].model===t){r=n[o];break}r||n.push(r={model:t,axisModels:[],coordIndex:i}),r.axisModels.push(e)}var e=this.dataZoomModel,n=this.ecModel,i={};return e.eachTargetAxis(function(e,r){var o=n.getComponent(e.axis,r);if(o){var a=o.getCoordSysModel();a&&t(a,o,i[a.mainType]||(i[a.mainType]=[]),a.componentIndex)}},this),i}}),YC=(ZC.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}}),d_),UC=ba,jC=Ia,qC=y,$C=f,KC=7,QC=1,JC=30,tT="horizontal",eT="vertical",nT=5,iT=["line","bar","candlestick","scatter"],rT=XC.extend({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,n,i){return rT.superApply(this,"render",arguments),_l(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),this.dataZoomModel.get("show")===!1?void this.group.removeAll():(i&&"dataZoom"===i.type&&i.from===this.uid||this._buildView(),void this._updateView())},remove:function(){rT.superApply(this,"remove",arguments),wl(this,"_dispatchZoomAction")},dispose:function(){rT.superApply(this,"dispose",arguments),wl(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t.removeAll(),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new Nm;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,n=this._findCoordRect(),i={width:e.getWidth(),height:e.getHeight()},r=this._orient===tT?{right:i.width-n.x-n.width,top:i.height-JC-KC,width:n.width,height:JC}:{right:KC,top:n.y,width:JC,height:n.height},o=es(t.option);f(["right","top","width","height"],function(t){"ph"===o[t]&&(o[t]=r[t])});var a=Qa(o,i,t.padding);this._location={x:a.x,y:a.y},this._size=[a.width,a.height],this._orient===eT&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),r=i&&i.get("inverse"),o=this._displayables.barGroup,a=(this._dataShadowInfo||{}).otherAxisInverse;o.attr(n!==tT||r?n===tT&&r?{scale:a?[-1,1]:[-1,-1]}:n!==eT||r?{scale:a?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:a?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:a?[1,1]:[1,-1]});var s=t.getBoundingRect([o]);t.attr("position",[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size,n=this._displayables.barGroup;n.add(new YC({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40})),n.add(new YC({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:y(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(t){var e=this._size,n=t.series,i=n.getRawData(),r=n.getShadowDim?n.getShadowDim():t.otherDim;if(null!=r){var o=i.getDataExtent(r),a=.3*(o[1]-o[0]);o=[o[0]-a,o[1]+a];var l,u=[0,e[1]],h=[0,e[0]],c=[[e[0],0],[0,0]],d=[],f=h[1]/(i.count()-1),p=0,g=Math.round(i.count()/e[0]);i.each([r],function(t,e){if(g>0&&e%g)return void(p+=f);var n=null==t||isNaN(t)||""===t,i=n?0:UC(t,o,u,!0);n&&!l&&e?(c.push([c[c.length-1][0],0]),d.push([d[d.length-1][0],0])):!n&&l&&(c.push([p,0]),d.push([p,0])),c.push([p,i]),d.push([p,i]),p+=f,l=n});var v=this.dataZoomModel;this._displayables.barGroup.add(new l_({shape:{points:c},style:s({fill:v.get("dataBackgroundColor")},v.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new u_({shape:{points:d},style:v.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}}},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,e=t.get("showDataShadow");if(e!==!1){var n,i=this.ecModel;return t.eachTargetAxis(function(r,o){var a=t.getAxisProxy(r.name,o).getTargetSeriesModels();f(a,function(t){if(!(n||e!==!0&&u(iT,t.get("type"))<0)){var a,s=i.getComponent(r.axis,o).axis,l=bp(r.name),h=t.coordinateSystem;null!=l&&h.getOtherAxis&&(a=h.getOtherAxis(s).inverse),l=t.getData().mapDimension(l),n={thisAxis:s,series:t,thisDim:r.name,otherDim:l,otherAxisInverse:a}}},this)},this),n}},_renderHandle:function(){var t=this._displayables,e=t.handles=[],n=t.handleLabels=[],i=this._displayables.barGroup,r=this._size,o=this.dataZoomModel;i.add(t.filler=new YC({draggable:!0,cursor:Sp(this._orient),drift:qC(this._onDragMove,this,"all"),ondragstart:qC(this._showDataInfo,this,!0),ondragend:qC(this._onDragEnd,this),onmouseover:qC(this._showDataInfo,this,!0),onmouseout:qC(this._showDataInfo,this,!1),style:{fill:o.get("fillerColor"),textPosition:"inside"}})),i.add(new YC({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{stroke:o.get("dataBackgroundColor")||o.get("borderColor"),lineWidth:QC,fill:"rgba(0,0,0,0)"}})),$C([0,1],function(t){var r=ha(o.get("handleIcon"),{cursor:Sp(this._orient),draggable:!0,drift:qC(this._onDragMove,this,t),ondragend:qC(this._onDragEnd,this),onmouseover:qC(this._showDataInfo,this,!0),onmouseout:qC(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),a=r.getBoundingRect();this._handleHeight=Sa(o.get("handleSize"),this._size[1]),this._handleWidth=a.width/a.height*this._handleHeight,r.setStyle(o.getModel("handleStyle").getItemStyle());var s=o.get("handleColor");null!=s&&(r.style.fill=s),i.add(e[t]=r);var l=o.textStyleModel;this.group.add(n[t]=new t_({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:l.getTextColor(),textFont:l.getFont()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[UC(t[0],[0,100],e,!0),UC(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var n=this.dataZoomModel,i=this._handleEnds,r=this._getViewExtent(),o=n.findRepresentativeAxisProxy().getMinMaxSpan(),a=[0,100];NC(e,i,r,n.get("zoomLock")?"all":t,null!=o.minSpan?UC(o.minSpan,a,r,!0):null,null!=o.maxSpan?UC(o.maxSpan,a,r,!0):null);var s=this._range,l=this._range=jC([UC(i[0],r,a,!0),UC(i[1],r,a,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},_updateView:function(t){var e=this._displayables,n=this._handleEnds,i=jC(n.slice()),r=this._size;$C([0,1],function(t){var i=e.handles[t],o=this._handleHeight;i.attr({scale:[o/2,o/2],position:[n[t],r[1]/2-o/2]})},this),e.filler.setShape({x:i[0],y:0,width:i[1]-i[0],height:r[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){function e(t){var e=ra(i.handles[t].parent,this.group),n=aa(0===t?"right":"left",e),s=this._handleWidth/2+nT,l=oa([c[t]+(0===t?-s:s),this._size[1]/2],e);r[t].setStyle({x:l[0],y:l[1],textVerticalAlign:o===tT?"middle":n,textAlign:o===tT?n:"center",text:a[t]})}var n=this.dataZoomModel,i=this._displayables,r=i.handleLabels,o=this._orient,a=["",""];if(n.get("showDetail")){var s=n.findRepresentativeAxisProxy();if(s){var l=s.getAxisModel().axis,u=this._range,h=t?s.calculateDataWindow({start:u[0],end:u[1]}).valueWindow:s.getDataValueWindow();a=[this._formatLabel(h[0],l),this._formatLabel(h[1],l)]}}var c=jC(this._handleEnds.slice());e.call(this,0),e.call(this,1)},_formatLabel:function(t,e){var n=this.dataZoomModel,i=n.get("labelFormatter"),r=n.get("labelPrecision");(null==r||"auto"===r)&&(r=e.getPixelPrecision());var o=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel(Math.round(t)):t.toFixed(Math.min(r,20));return w(i)?i(t,o):b(i)?i.replace("{value}",o):o},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,n,i){this._dragging=!0,Jv(i.event);var r=this._displayables.barGroup.getLocalTransform(),o=oa([e,n],r,!0),a=this._updateInterval(t,o[0]),s=this.dataZoomModel.get("realtime");this._updateView(!s),a&&s&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1);var t=this.dataZoomModel.get("realtime");!t&&this._dispatchZoomAction()},_onClickPanelClick:function(t){var e=this._size,n=this._displayables.barGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(n[0]<0||n[0]>e[0]||n[1]<0||n[1]>e[1])){var i=this._handleEnds,r=(i[0]+i[1])/2,o=this._updateInterval("all",n[0]-r);this._updateView(),o&&this._dispatchZoomAction()}},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_findCoordRect:function(){var t;if($C(this.getTargetCoordInfo(),function(e){if(!t&&e.length){var n=e[0].model.coordinateSystem;t=n.getRect&&n.getRect()}}),!t){var e=this.api.getWidth(),n=this.api.getHeight();t={x:.2*e,y:.2*n,width:.6*e,height:.6*n}}return t}});Iu({getTargetSeries:function(t){var e=N();return t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,n,i){var r=i.getAxisProxy(t.name,n);f(r.getTargetSeriesModels(),function(t){e.set(t.uid,t)})})}),e},modifyOutputEnd:!0,overallReset:function(t,e){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,n,i){i.getAxisProxy(t.name,n).reset(i,e)}),t.eachTargetAxis(function(t,n,i){i.getAxisProxy(t.name,n).filterData(i,e)})}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),n=e.getDataPercentWindow(),i=e.getDataValueWindow();t.setCalculatedRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]})})}}),Tu("dataZoom",function(t,e){var n=fp(y(e.eachComponent,e,"dataZoom"),BC,function(t,e){return t.get(e.axisIndex)}),i=[];e.eachComponent({mainType:"dataZoom",query:t},function(t){i.push.apply(i,n(t).nodes)}),f(i,function(e){e.setRawRange({start:t.start,end:t.end,startValue:t.startValue,endValue:t.endValue})})}),ZC.extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}});var oT="\x00_ec_interaction_mutex";Tu({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),c(Ap,Yv);var aT="\x00_ec_dataZoom_roams",sT=y,lT=XC.extend({type:"dataZoom.inside",init:function(){this._range},render:function(t,e,n){lT.superApply(this,"render",arguments),this._range=t.getPercentRange(),f(this.getTargetCoordInfo(),function(e,i){var r=p(e,function(t){return Fp(t.model)});f(e,function(e){var o=e.model,a={};f(["pan","zoom","scrollMove"],function(t){a[t]=sT(uT[t],this,e,i)},this),Bp(n,{coordId:Fp(o),allCoordIds:r,containsPoint:function(t,e,n){return o.coordinateSystem.containPoint([e,n])},dataZoomId:t.id,dataZoomModel:t,getRange:a})},this)},this)},dispose:function(){Np(this.api,this.dataZoomModel.id),lT.superApply(this,"dispose",arguments),this._range=null}}),uT={zoom:function(t,e,n,i){var r=this._range,o=r.slice(),a=t.axisModels[0];if(a){var s=hT[e](null,[i.originX,i.originY],a,n,t),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(o[1]-o[0])+o[0],u=Math.max(1/i.scale,0);o[0]=(o[0]-l)*u+l,o[1]=(o[1]-l)*u+l;var h=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return NC(0,o,[0,100],0,h.minSpan,h.maxSpan),this._range=o,r[0]!==o[0]||r[1]!==o[1]?o:void 0}},pan:Xp(function(t,e,n,i,r,o){var a=hT[i]([o.oldX,o.oldY],[o.newX,o.newY],e,r,n);return a.signal*(t[1]-t[0])*a.pixel/a.pixelLength}),scrollMove:Xp(function(t,e,n,i,r,o){var a=hT[i]([0,0],[o.scrollDelta,o.scrollDelta],e,r,n);return a.signal*(t[1]-t[0])*o.scrollDelta})},hT={grid:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem.getRect();return t=t||[0,0],"x"===o.dim?(a.pixel=e[0]-t[0],a.pixelLength=s.width,a.pixelStart=s.x,a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=s.height,a.pixelStart=s.y,a.signal=o.inverse?-1:1),a},polar:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===n.mainType?(a.pixel=e[0]-t[0],a.pixelLength=l[1]-l[0],a.pixelStart=l[0],a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=u[1]-u[0],a.pixelStart=u[0],a.signal=o.inverse?-1:1),a},singleAxis:function(t,e,n,i,r){var o=n.axis,a=r.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===o.orient?(s.pixel=e[0]-t[0],s.pixelLength=a.width,s.pixelStart=a.x,s.signal=o.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=a.height,s.pixelStart=a.y,s.signal=o.inverse?-1:1),s}},cT={},dT=zu({type:"toolbox",layoutMode:{type:"box",ignoreSize:!0},optionUpdated:function(){dT.superApply(this,"optionUpdated",arguments),f(this.option.feature,function(t,e){var n=Up(e);n&&r(t,n.defaultOption)})},defaultOption:{show:!0,z:6,zlevel:0,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1}}});Eu({type:"toolbox",render:function(t,e,n,i){function r(r,a){var s,l=c[r],d=c[a],f=u[l],p=new ga(f,t,t.ecModel);if(i&&null!=i.newTitle&&i.featureName===l&&(f.title=i.newTitle),l&&!d){if(jp(l))s={model:p,onclick:p.option.onclick,featureName:l};else{var g=Up(l);if(!g)return;s=new g(p,e,n)}h[l]=s}else{if(s=h[d],!s)return;s.model=p,s.ecModel=e,s.api=n}return!l&&d?void(s.dispose&&s.dispose(e,n)):!p.get("show")||s.unusable?void(s.remove&&s.remove(e,n)):(o(p,s,l),p.setIconStatus=function(t,e){var n=this.option,i=this.iconPaths;n.iconStatus=n.iconStatus||{},n.iconStatus[t]=e,i[t]&&i[t].trigger(e)},void(s.render&&s.render(p,e,n,i)))}function o(i,r,o){var u=i.getModel("iconStyle"),h=i.getModel("emphasis.iconStyle"),c=r.getIcons?r.getIcons():i.get("icon"),d=i.get("title")||{};if("string"==typeof c){var p=c,g=d;c={},d={},c[o]=p,d[o]=g}var v=i.iconPaths={};f(c,function(o,c){var f=ha(o,{},{x:-l/2,y:-l/2,width:l,height:l});f.setStyle(u.getItemStyle()),f.hoverStyle=h.getItemStyle(),f.setStyle({text:d[c],textAlign:h.get("textAlign"),textBorderRadius:h.get("textBorderRadius"),textPadding:h.get("textPadding"),textFill:null});var p=t.getModel("tooltip");p&&p.get("show")&&f.attr("tooltip",a({content:d[c],formatter:p.get("formatter",!0)||function(){return d[c]},formatterParams:{componentType:"toolbox",name:c,title:d[c],$vars:["name","title"]},position:p.get("position",!0)||"bottom"},p.option)),Vo(f),t.get("showTitle")&&(f.__title=d[c],f.on("mouseover",function(){var e=h.getItemStyle(),n="vertical"===t.get("orient")?null==t.get("right")?"right":"left":null==t.get("bottom")?"bottom":"top";f.setStyle({textFill:h.get("textFill")||e.fill||e.stroke||"#000",textBackgroundColor:h.get("textBackgroundColor"),textPosition:h.get("textPosition")||n})}).on("mouseout",function(){f.setStyle({textFill:null,textBackgroundColor:null})})),f.trigger(i.get("iconStatus."+c)||"normal"),s.add(f),f.on("click",y(r.onclick,r,e,n,c)),v[c]=f})}var s=this.group;if(s.removeAll(),t.get("show")){var l=+t.get("itemSize"),u=t.get("feature")||{},h=this._features||(this._features={}),c=[];f(u,function(t,e){c.push(e)}),new Wu(this._featureNames||[],c).add(r).update(r).remove(x(r,null)).execute(),this._featureNames=c,of(s,t,n),s.add(af(s.getBoundingRect(),t)),s.eachChild(function(t){var e=t.__title,i=t.hoverStyle;if(i&&e){var r=Un(e,li(i)),o=t.position[0]+s.position[0],a=t.position[1]+s.position[1]+l,u=!1;a+r.height>n.getHeight()&&(i.textPosition="top",u=!0);var h=u?-5-r.height:l+8;o+r.width/2>n.getWidth()?(i.textPosition=["100%",h],i.textAlign="right"):o-r.width/2<0&&(i.textPosition=[0,h],i.textAlign="left")}})}},updateView:function(t,e,n,i){f(this._features,function(t){t.updateView&&t.updateView(t.model,e,n,i)})},remove:function(t,e){f(this._features,function(n){n.remove&&n.remove(t,e)}),this.group.removeAll()},dispose:function(t,e){f(this._features,function(n){n.dispose&&n.dispose(t,e)})}});var fT=sb.toolbox.saveAsImage;qp.defaultOption={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:fT.title,type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],pixelRatio:1,lang:fT.lang.slice()},qp.prototype.unusable=!Mv.canvasSupported;var pT=qp.prototype;pT.onclick=function(t,e){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",r="svg"===e.getZr().painter.getType(),o=r?"svg":n.get("type",!0)||"png",a=e.getConnectedDataURL({type:o,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:n.get("connectedBackgroundColor"),excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")});if("function"!=typeof MouseEvent||Mv.browser.ie||Mv.browser.edge)if(window.navigator.msSaveOrOpenBlob){for(var s=atob(a.split(",")[1]),l=s.length,u=new Uint8Array(l);l--;)u[l]=s.charCodeAt(l);var h=new Blob([u]);window.navigator.msSaveOrOpenBlob(h,i+"."+o)}else{var c=n.get("lang"),d='<body style="margin:0;"><img src="'+a+'" style="max-width:100%;" title="'+(c&&c[0]||"")+'" /></body>',f=window.open();f.document.write(d)}else{var p=document.createElement("a");p.download=i+"."+o,p.target="_blank",p.href=a;var g=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});p.dispatchEvent(g)}},Yp("saveAsImage",qp);var gT=sb.toolbox.magicType,vT="__ec_magicType_stack__";$p.defaultOption={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:i(gT.title),option:{},seriesIndex:{}};var mT=$p.prototype;mT.getIcons=function(){var t=this.model,e=t.get("icon"),n={};return f(t.get("type"),function(t){e[t]&&(n[t]=e[t])}),n};var yT={line:function(t,e,n,i){return"bar"===t?r({id:e,type:"line",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.line")||{},!0):void 0},bar:function(t,e,n,i){return"line"===t?r({id:e,type:"bar",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.bar")||{},!0):void 0},stack:function(t,e,n,i){var o=n.get("stack")===vT;return"line"===t||"bar"===t?(i.setIconStatus("stack",o?"normal":"emphasis"),r({id:e,stack:o?"":vT},i.get("option.stack")||{},!0)):void 0}},xT=[["line","bar"],["stack"]];mT.onclick=function(t,e,n){var o=this.model,a=o.get("seriesIndex."+n);if(yT[n]){var l={series:[]},h=function(e){var i=e.subType,r=e.id,a=yT[n](i,r,e,o);a&&(s(a,e.option),l.series.push(a));var u=e.coordinateSystem;if(u&&"cartesian2d"===u.type&&("line"===n||"bar"===n)){var h=u.getAxesByScale("ordinal")[0];if(h){var c=h.dim,d=c+"Axis",f=t.queryComponents({mainType:d,index:e.get(name+"Index"),id:e.get(name+"Id")})[0],p=f.componentIndex;l[d]=l[d]||[];for(var g=0;p>=g;g++)l[d][p]=l[d][p]||{};l[d][p].boundaryGap="bar"===n}}};f(xT,function(t){u(t,n)>=0&&f(t,function(t){o.setIconStatus(t,"normal")})}),o.setIconStatus(n,"emphasis"),t.eachComponent({mainType:"series",query:null==a?null:{seriesIndex:a}},h);var c;if("stack"===n){var d=l.series&&l.series[0]&&l.series[0].stack===vT;c=d?r({stack:gT.title.tiled},gT.title):i(gT.title)}e.dispatchAction({type:"changeMagicType",currentType:n,newOption:l,newTitle:c,featureName:"magicType"})}},Tu({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(t,e){e.mergeOption(t.newOption)}),Yp("magicType",$p);var _T=sb.toolbox.dataView,wT=new Array(60).join("-"),bT="    ",ST=new RegExp("["+bT+"]+","g");ag.defaultOption={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:i(_T.title),lang:i(_T.lang),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"},ag.prototype.onclick=function(t,e){function n(){i.removeChild(o),x._dom=null
}var i=e.getDom(),r=this.model;this._dom&&i.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;left:5px;top:5px;bottom:5px;right:5px;",o.style.backgroundColor=r.get("backgroundColor")||"#fff";var a=document.createElement("h4"),s=r.get("lang")||[];a.innerHTML=s[0]||r.get("title"),a.style.cssText="margin: 10px 20px;",a.style.color=r.get("textColor");var l=document.createElement("div"),u=document.createElement("textarea");l.style.cssText="display:block;width:100%;overflow:auto;";var h=r.get("optionToContent"),c=r.get("contentToOption"),d=tg(t);if("function"==typeof h){var f=h(e.getOption());"string"==typeof f?l.innerHTML=f:C(f)&&l.appendChild(f)}else l.appendChild(u),u.readOnly=r.get("readOnly"),u.style.cssText="width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;",u.style.color=r.get("textColor"),u.style.borderColor=r.get("textareaBorderColor"),u.style.backgroundColor=r.get("textareaColor"),u.value=d.value;var p=d.meta,g=document.createElement("div");g.style.cssText="position:absolute;bottom:0;left:0;right:0;";var v="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",m=document.createElement("div"),y=document.createElement("div");v+=";background-color:"+r.get("buttonColor"),v+=";color:"+r.get("buttonTextColor");var x=this;Se(m,"click",n),Se(y,"click",function(){var t;try{t="function"==typeof c?c(l,e.getOption()):og(u.value,p)}catch(i){throw n(),new Error("Data view format error "+i)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),n()}),m.innerHTML=s[1],y.innerHTML=s[2],y.style.cssText=v,m.style.cssText=v,!r.get("readOnly")&&g.appendChild(y),g.appendChild(m),o.appendChild(a),o.appendChild(l),o.appendChild(g),l.style.height=i.clientHeight-80+"px",i.appendChild(o),this._dom=o},ag.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},ag.prototype.dispose=function(t,e){this.remove(t,e)},Yp("dataView",ag),Tu({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(t,e){var n=[];f(t.newOption.series,function(t){var i=e.getSeriesByName(t.name)[0];if(i){var r=i.get("data");n.push({name:t.name,data:sg(t.data,r)})}else n.push(a({type:"scatter"},t))}),e.mergeOption(s({series:n},t.newOption))});var MT=x,IT=f,CT=p,TT=Math.min,AT=Math.max,DT=Math.pow,kT=1e4,PT=6,LT=6,OT="globalPan",zT={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},ET={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},RT={brushStyle:{lineWidth:2,stroke:"rgba(0,0,0,0.3)",fill:"rgba(0,0,0,0.1)"},transformable:!0,brushMode:"single",removeOnClick:!1},BT=0;lg.prototype={constructor:lg,enableBrush:function(t){return _v&&O(this._mounted),this._brushType&&hg(this),t.brushType&&ug(this,t),this},setPanels:function(t){if(t&&t.length){var e=this._panels={};f(t,function(t){e[t.panelId]=i(t)})}else this._panels=null;return this},mount:function(t){t=t||{},_v&&(this._mounted=!0),this._enableGlobalPan=t.enableGlobalPan;var e=this.group;return this._zr.add(e),e.attr({position:t.position||[0,0],rotation:t.rotation||0,scale:t.scale||[1,1]}),this._transform=e.getLocalTransform(),this},eachCover:function(t,e){IT(this._covers,t,e)},updateCovers:function(t){function e(t,e){return(null!=t.id?t.id:s+e)+"-"+t.brushType}function n(t,n){return e(t.__brushOption,n)}function o(e,n){var i=t[e];if(null!=n&&l[n]===c)u[e]=l[n];else{var r=u[e]=null!=n?(l[n].__brushOption=i,l[n]):pg(h,fg(h,i));mg(h,r)}}function a(t){l[t]!==c&&h.group.remove(l[t])}_v&&O(this._mounted),t=p(t,function(t){return r(i(RT),t,!0)});var s="\x00-brush-index-",l=this._covers,u=this._covers=[],h=this,c=this._creatingCover;return new Wu(l,t,n,e).add(o).update(o).remove(a).execute(),this},unmount:function(){return!_v||this._mounted?(this.enableBrush(!1),wg(this),this._zr.remove(this.group),_v&&(this._mounted=!1),this):void 0},dispose:function(){this.unmount(),this.off()}},c(lg,Yv);var NT={mousedown:function(t){if(this._dragging)Gg(this,t);else if(!t.target||!t.target.draggable){Fg(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);this._creatingCover=null;var n=this._creatingPanel=xg(this,t,e);n&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(t){var e=t.offsetX,n=t.offsetY,i=this.group.transformCoordToLocal(e,n);if(Ng(this,t,i),this._dragging){Fg(t);var r=Hg(this,t,i,!1);r&&bg(this,r)}},mouseup:function(t){Gg(this,t)}},FT={lineX:Xg(0),lineY:Xg(1),rect:{createCover:function(t,e){return Ig(MT(Og,function(t){return t},function(t){return t}),t,e,["w","e","n","s","se","sw","ne","nw"])},getCreatingRange:function(t){var e=Mg(t);return kg(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,n,i){Cg(t,e,n,i)},updateCommon:Tg,contain:Vg},polygon:{createCover:function(t,e){var n=new Nm;return n.add(new u_({name:"main",style:Dg(e),silent:!0})),n},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new l_({name:"main",draggable:!0,drift:MT(zg,t,e),ondragend:MT(bg,t,{isEnd:!0})}))},updateCoverShape:function(t,e,n){e.childAt(0).setShape({points:Rg(t,e,n)})},updateCommon:Tg,contain:Vg}},VT={axisPointer:1,tooltip:1,brush:1},HT=f,WT=u,GT=x,ZT=["dataToPoint","pointToData"],XT=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],YT=Kg.prototype;YT.setOutputRanges=function(t,e){this.matchOutputRanges(t,e,function(t,e,n){if((t.coordRanges||(t.coordRanges=[])).push(e),!t.coordRange){t.coordRange=e;var i=$T[t.brushType](0,n,e);t.__rangeOffset={offset:KT[t.brushType](i.values,t.range,[1,1]),xyMinMax:i.xyMinMax}}})},YT.matchOutputRanges=function(t,e,n){HT(t,function(t){var i=this.findTargetInfo(t,e);i&&i!==!0&&f(i.coordSyses,function(i){var r=$T[t.brushType](1,i,t.range);n(t,r.values,i,e)})},this)},YT.setInputRanges=function(t,e){HT(t,function(t){var n=this.findTargetInfo(t,e);if(_v&&(O(!n||n===!0||t.coordRange,"coordRange must be specified when coord index specified."),O(!n||n!==!0||t.range,"range must be specified in global brush.")),t.range=t.range||[],n&&n!==!0){t.panelId=n.panelId;var i=$T[t.brushType](0,n.coordSys,t.coordRange),r=t.__rangeOffset;t.range=r?KT[t.brushType](i.values,r.offset,nv(i.xyMinMax,r.xyMinMax)):i.values}},this)},YT.makePanelOpts=function(t,e){return p(this._targetInfoList,function(n){var i=n.getPanelRect();return{panelId:n.panelId,defaultBrushType:e&&e(n),clipPath:Ug(i),isTargetByCursor:qg(i,t,n.coordSysModel),getLinearBrushOtherExtent:jg(i)}})},YT.controlSeries=function(t,e,n){var i=this.findTargetInfo(t,n);return i===!0||i&&WT(i.coordSyses,e.coordinateSystem)>=0},YT.findTargetInfo=function(t,e){for(var n=this._targetInfoList,i=Jg(e,t),r=0;r<n.length;r++){var o=n[r],a=t.panelId;if(a){if(o.panelId===a)return o}else for(var r=0;r<jT.length;r++)if(jT[r](i,o))return o}return!0};var UT={grid:function(t,e){var n=t.xAxisModels,i=t.yAxisModels,r=t.gridModels,o=N(),a={},s={};(n||i||r)&&(HT(n,function(t){var e=t.axis.grid.model;o.set(e.id,e),a[e.id]=!0}),HT(i,function(t){var e=t.axis.grid.model;o.set(e.id,e),s[e.id]=!0}),HT(r,function(t){o.set(t.id,t),a[t.id]=!0,s[t.id]=!0}),o.each(function(t){var r=t.coordinateSystem,o=[];HT(r.getCartesians(),function(t){(WT(n,t.getAxis("x").model)>=0||WT(i,t.getAxis("y").model)>=0)&&o.push(t)}),e.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:o[0],coordSyses:o,getPanelRect:qT.grid,xAxisDeclared:a[t.id],yAxisDeclared:s[t.id]})}))},geo:function(t,e){HT(t.geoModels,function(t){var n=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:n,coordSyses:[n],getPanelRect:qT.geo})})}},jT=[function(t,e){var n=t.xAxisModel,i=t.yAxisModel,r=t.gridModel;return!r&&n&&(r=n.axis.grid.model),!r&&i&&(r=i.axis.grid.model),r&&r===e.gridModel},function(t,e){var n=t.geoModel;return n&&n===e.geoModel}],qT={grid:function(){return this.coordSys.grid.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(ra(t)),e}},$T={lineX:GT(tv,0),lineY:GT(tv,1),rect:function(t,e,n){var i=e[ZT[t]]([n[0][0],n[1][0]]),r=e[ZT[t]]([n[0][1],n[1][1]]),o=[Qg([i[0],r[0]]),Qg([i[1],r[1]])];return{values:o,xyMinMax:o}},polygon:function(t,e,n){var i=[[1/0,-1/0],[1/0,-1/0]],r=p(n,function(n){var r=e[ZT[t]](n);return i[0][0]=Math.min(i[0][0],r[0]),i[1][0]=Math.min(i[1][0],r[1]),i[0][1]=Math.max(i[0][1],r[0]),i[1][1]=Math.max(i[1][1],r[1]),r});return{values:r,xyMinMax:i}}},KT={lineX:GT(ev,0),lineY:GT(ev,1),rect:function(t,e,n){return[[t[0][0]-n[0]*e[0][0],t[0][1]-n[0]*e[0][1]],[t[1][0]-n[1]*e[1][0],t[1][1]-n[1]*e[1][1]]]},polygon:function(t,e,n){return p(t,function(t,i){return[t[0]-n[0]*e[i][0],t[1]-n[1]*e[i][1]]})}},QT=f,JT="\x00_ec_hist_store";ZC.extend({type:"dataZoom.select"}),XC.extend({type:"dataZoom.select"});var tA=sb.toolbox.dataZoom,eA=f,nA="\x00_ec_\x00toolbox-dataZoom_";uv.defaultOption={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:i(tA.title),brushStyle:{borderWidth:0,color:"rgba(0,0,0,0.2)"}};var iA=uv.prototype;iA.render=function(t,e,n,i){this.model=t,this.ecModel=e,this.api=n,dv(t,e,this,i,n),cv(t,e)},iA.onclick=function(t,e,n){rA[n].call(this)},iA.remove=function(){this._brushController.unmount()},iA.dispose=function(){this._brushController.dispose()};var rA={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(ov(this.ecModel))}};iA._onBrush=function(t,e){function n(t,e,n){var a=e.getAxis(t),s=a.model,l=i(t,s,o),u=l.findRepresentativeAxisProxy(s).getMinMaxSpan();(null!=u.minValueSpan||null!=u.maxValueSpan)&&(n=NC(0,n.slice(),a.scale.getExtent(),0,u.minValueSpan,u.maxValueSpan)),l&&(r[l.id]={dataZoomId:l.id,startValue:n[0],endValue:n[1]})}function i(t,e,n){var i;return n.eachComponent({mainType:"dataZoom",subType:"select"},function(n){var r=n.getAxisModel(t,e.componentIndex);r&&(i=n)}),i}if(e.isEnd&&t.length){var r={},o=this.ecModel;this._brushController.updateCovers([]);var a=new Kg(hv(this.model.option),o,{include:["grid"]});a.matchOutputRanges(t,o,function(t,e,i){if("cartesian2d"===i.type){var r=t.brushType;"rect"===r?(n("x",i,e[0]),n("y",i,e[1])):n({lineX:"x",lineY:"y"}[r],i,e)}}),rv(o,r),this._dispatchZoomAction(r)}},iA._dispatchZoomAction=function(t){var e=[];eA(t,function(t){e.push(i(t))}),e.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:e})},Yp("dataZoom",uv),Mu(function(t){function e(t,e){if(e){var r=t+"Index",o=e[r];null==o||"all"===o||_(o)||(o=o===!1||"none"===o?[]:[o]),n(t,function(n,a){if(null==o||"all"===o||-1!==u(o,a)){var s={type:"select",$fromToolbox:!0,filterMode:e.filterMode||"filter",id:nA+t+a};s[r]=a,i.push(s)}})}}function n(e,n){var i=t[e];_(i)||(i=i?[i]:[]),eA(i,n)}if(t){var i=t.dataZoom||(t.dataZoom=[]);_(i)||(t.dataZoom=i=[i]);var r=t.toolbox;if(r&&(_(r)&&(r=r[0]),r&&r.feature)){var o=r.feature.dataZoom;e("xAxis",o),e("yAxis",o)}}});var oA=sb.toolbox.restore;fv.defaultOption={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:oA.title};var aA=fv.prototype;aA.onclick=function(t,e){av(t),e.dispatchAction({type:"restore",from:this.uid})},Yp("restore",fv),Tu({type:"restore",event:"restore",update:"prepareAndUpdate"},function(t,e){e.resetOption("recreate")});var sA,lA="urn:schemas-microsoft-com:vml",uA="undefined"==typeof window?null:window,hA=!1,cA=uA&&uA.document;if(cA&&!Mv.canvasSupported)try{!cA.namespaces.zrvml&&cA.namespaces.add("zrvml",lA),sA=function(t){return cA.createElement("<zrvml:"+t+' class="zrvml">')}}catch(dA){sA=function(t){return cA.createElement("<"+t+' xmlns="'+lA+'" class="zrvml">')}}var fA=Dx.CMD,pA=Math.round,gA=Math.sqrt,vA=Math.abs,mA=Math.cos,yA=Math.sin,xA=Math.max;if(!Mv.canvasSupported){var _A=",",wA="progid:DXImageTransform.Microsoft",bA=21600,SA=bA/2,MA=1e5,IA=1e3,CA=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=bA+","+bA,t.coordorigin="0,0"},TA=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},AA=function(t,e,n){return"rgb("+[t,e,n].join(",")+")"},DA=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},kA=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},PA=function(t,e,n){return(parseFloat(t)||0)*MA+(parseFloat(e)||0)*IA+n},LA=Ii,OA=function(t,e,n){var i=tn(e);n=+n,isNaN(n)&&(n=1),i&&(t.color=AA(i[0],i[1],i[2]),t.opacity=n*i[3])},zA=function(t){var e=tn(t);return[AA(e[0],e[1],e[2]),e[3]]},EA=function(t,e,n){var i=e.fill;if(null!=i)if(i instanceof x_){var r,o=0,a=[0,0],s=0,l=1,u=n.getBoundingRect(),h=u.width,c=u.height;if("linear"===i.type){r="gradient";var d=n.transform,f=[i.x*h,i.y*c],p=[i.x2*h,i.y2*c];d&&(oe(f,f,d),oe(p,p,d));var g=p[0]-f[0],v=p[1]-f[1];o=180*Math.atan2(g,v)/Math.PI,0>o&&(o+=360),1e-6>o&&(o=0)}else{r="gradientradial";var f=[i.x*h,i.y*c],d=n.transform,m=n.scale,y=h,x=c;a=[(f[0]-u.x)/y,(f[1]-u.y)/x],d&&oe(f,f,d),y/=m[0]*bA,x/=m[1]*bA;var _=xA(y,x);s=0/_,l=2*i.r/_-s}var w=i.colorStops.slice();w.sort(function(t,e){return t.offset-e.offset});for(var b=w.length,S=[],M=[],I=0;b>I;I++){var C=w[I],T=zA(C.color);M.push(C.offset*l+s+" "+T[0]),(0===I||I===b-1)&&S.push(T)}if(b>=2){var A=S[0][0],D=S[1][0],k=S[0][1]*e.opacity,P=S[1][1]*e.opacity;t.type=r,t.method="none",t.focus="100%",t.angle=o,t.color=A,t.color2=D,t.colors=M.join(","),t.opacity=P,t.opacity2=k}"radial"===r&&(t.focusposition=a.join(","))}else OA(t,i,e.opacity)},RA=function(t,e){e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof x_||OA(t,e.stroke,e.opacity)},BA=function(t,e,n,i){var r="fill"===e,o=t.getElementsByTagName(e)[0];null!=n[e]&&"none"!==n[e]&&(r||!r&&n.lineWidth)?(t[r?"filled":"stroked"]="true",n[e]instanceof x_&&kA(t,o),o||(o=pv(e)),r?EA(o,n,i):RA(o,n),DA(t,o)):(t[r?"filled":"stroked"]="false",kA(t,o))},NA=[[],[],[]],FA=function(t,e){var n,i,r,o,a,s,l=fA.M,u=fA.C,h=fA.L,c=fA.A,d=fA.Q,f=[],p=t.data,g=t.len();for(o=0;g>o;){switch(r=p[o++],i="",n=0,r){case l:i=" m ",n=1,a=p[o++],s=p[o++],NA[0][0]=a,NA[0][1]=s;break;case h:i=" l ",n=1,a=p[o++],s=p[o++],NA[0][0]=a,NA[0][1]=s;break;case d:case u:i=" c ",n=3;var v,m,y=p[o++],x=p[o++],_=p[o++],w=p[o++];r===d?(v=_,m=w,_=(_+2*y)/3,w=(w+2*x)/3,y=(a+2*y)/3,x=(s+2*x)/3):(v=p[o++],m=p[o++]),NA[0][0]=y,NA[0][1]=x,NA[1][0]=_,NA[1][1]=w,NA[2][0]=v,NA[2][1]=m,a=v,s=m;break;case c:var b=0,S=0,M=1,I=1,C=0;e&&(b=e[4],S=e[5],M=gA(e[0]*e[0]+e[1]*e[1]),I=gA(e[2]*e[2]+e[3]*e[3]),C=Math.atan2(-e[1]/I,e[0]/M));var T=p[o++],A=p[o++],D=p[o++],k=p[o++],P=p[o++]+C,L=p[o++]+P+C;o++;var O=p[o++],z=T+mA(P)*D,E=A+yA(P)*k,y=T+mA(L)*D,x=A+yA(L)*k,R=O?" wa ":" at ";Math.abs(z-y)<1e-4&&(Math.abs(L-P)>.01?O&&(z+=270/bA):Math.abs(E-A)<1e-4?O&&T>z||!O&&z>T?x-=270/bA:x+=270/bA:O&&A>E||!O&&E>A?y+=270/bA:y-=270/bA),f.push(R,pA(((T-D)*M+b)*bA-SA),_A,pA(((A-k)*I+S)*bA-SA),_A,pA(((T+D)*M+b)*bA-SA),_A,pA(((A+k)*I+S)*bA-SA),_A,pA((z*M+b)*bA-SA),_A,pA((E*I+S)*bA-SA),_A,pA((y*M+b)*bA-SA),_A,pA((x*I+S)*bA-SA)),a=y,s=x;break;case fA.R:var B=NA[0],N=NA[1];B[0]=p[o++],B[1]=p[o++],N[0]=B[0]+p[o++],N[1]=B[1]+p[o++],e&&(oe(B,B,e),oe(N,N,e)),B[0]=pA(B[0]*bA-SA),N[0]=pA(N[0]*bA-SA),B[1]=pA(B[1]*bA-SA),N[1]=pA(N[1]*bA-SA),f.push(" m ",B[0],_A,B[1]," l ",N[0],_A,B[1]," l ",N[0],_A,N[1]," l ",B[0],_A,N[1]);break;case fA.Z:f.push(" x ")}if(n>0){f.push(i);for(var F=0;n>F;F++){var V=NA[F];e&&oe(V,V,e),f.push(pA(V[0]*bA-SA),_A,pA(V[1]*bA-SA),n-1>F?_A:"")}}}return f.join("")};no.prototype.brushVML=function(t){var e=this.style,n=this._vmlEl;n||(n=pv("shape"),CA(n),this._vmlEl=n),BA(n,"fill",e,this),BA(n,"stroke",e,this);var i=this.transform,r=null!=i,o=n.getElementsByTagName("stroke")[0];if(o){var a=e.lineWidth;if(r&&!e.strokeNoScale){var s=i[0]*i[3]-i[1]*i[2];a*=gA(vA(s))}o.weight=a+"px"}var l=this.path||(this.path=new Dx);this.__dirtyPath&&(l.beginPath(),l.subPixelOptimize=!1,this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),n.path=FA(l,this.transform),n.style.zIndex=PA(this.zlevel,this.z,this.z2),DA(t,n),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},no.prototype.onRemove=function(t){kA(t,this._vmlEl),this.removeRectText(t)},no.prototype.onAdd=function(t){DA(t,this._vmlEl),this.appendRectText(t)};var VA=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};Di.prototype.brushVML=function(t){var e,n,i=this.style,r=i.image;if(VA(r)){var o=r.src;if(o===this._imageSrc)e=this._imageWidth,n=this._imageHeight;else{var a=r.runtimeStyle,s=a.width,l=a.height;a.width="auto",a.height="auto",e=r.width,n=r.height,a.width=s,a.height=l,this._imageSrc=o,this._imageWidth=e,this._imageHeight=n}r=o}else r===this._imageSrc&&(e=this._imageWidth,n=this._imageHeight);if(r){var u=i.x||0,h=i.y||0,c=i.width,d=i.height,f=i.sWidth,p=i.sHeight,g=i.sx||0,v=i.sy||0,m=f&&p,y=this._vmlEl;y||(y=cA.createElement("div"),CA(y),this._vmlEl=y);var x,_=y.style,w=!1,b=1,S=1;if(this.transform&&(x=this.transform,b=gA(x[0]*x[0]+x[1]*x[1]),S=gA(x[2]*x[2]+x[3]*x[3]),w=x[1]||x[2]),w){var M=[u,h],I=[u+c,h],C=[u,h+d],T=[u+c,h+d];oe(M,M,x),oe(I,I,x),oe(C,C,x),oe(T,T,x);var A=xA(M[0],I[0],C[0],T[0]),D=xA(M[1],I[1],C[1],T[1]),k=[];k.push("M11=",x[0]/b,_A,"M12=",x[2]/S,_A,"M21=",x[1]/b,_A,"M22=",x[3]/S,_A,"Dx=",pA(u*b+x[4]),_A,"Dy=",pA(h*S+x[5])),_.padding="0 "+pA(A)+"px "+pA(D)+"px 0",_.filter=wA+".Matrix("+k.join("")+", SizingMethod=clip)"}else x&&(u=u*b+x[4],h=h*S+x[5]),_.filter="",_.left=pA(u)+"px",_.top=pA(h)+"px";var P=this._imageEl,L=this._cropEl;P||(P=cA.createElement("div"),this._imageEl=P);var O=P.style;if(m){if(e&&n)O.width=pA(b*e*c/f)+"px",O.height=pA(S*n*d/p)+"px";else{var z=new Image,E=this;z.onload=function(){z.onload=null,e=z.width,n=z.height,O.width=pA(b*e*c/f)+"px",O.height=pA(S*n*d/p)+"px",E._imageWidth=e,E._imageHeight=n,E._imageSrc=r},z.src=r}L||(L=cA.createElement("div"),L.style.overflow="hidden",this._cropEl=L);var R=L.style;R.width=pA((c+g*c/f)*b),R.height=pA((d+v*d/p)*S),R.filter=wA+".Matrix(Dx="+-g*c/f*b+",Dy="+-v*d/p*S+")",L.parentNode||y.appendChild(L),P.parentNode!==L&&L.appendChild(P)}else O.width=pA(b*c)+"px",O.height=pA(S*d)+"px",y.appendChild(P),L&&L.parentNode&&(y.removeChild(L),this._cropEl=null);var B="",N=i.opacity;1>N&&(B+=".Alpha(opacity="+pA(100*N)+") "),B+=wA+".AlphaImageLoader(src="+r+", SizingMethod=scale)",O.filter=B,y.style.zIndex=PA(this.zlevel,this.z,this.z2),DA(t,y),null!=i.text&&this.drawRectText(t,this.getBoundingRect())}},Di.prototype.onRemove=function(t){kA(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},Di.prototype.onAdd=function(t){DA(t,this._vmlEl),this.appendRectText(t)};var HA,WA="normal",GA={},ZA=0,XA=100,YA=document.createElement("div"),UA=function(t){var e=GA[t];if(!e){ZA>XA&&(ZA=0,GA={});var n,i=YA.style;try{i.font=t,n=i.fontFamily.split(",")[0]}catch(r){}e={style:i.fontStyle||WA,variant:i.fontVariant||WA,weight:i.fontWeight||WA,size:0|parseFloat(i.fontSize||12),family:n||"Microsoft YaHei"},GA[t]=e,ZA++}return e};Xn("measureText",function(t,e){var n=cA;HA||(HA=n.createElement("div"),HA.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",cA.body.appendChild(HA));try{HA.style.font=e}catch(i){}return HA.innerHTML="",HA.appendChild(n.createTextNode(t)),{width:HA.offsetWidth}});for(var jA=new Tn,qA=function(t,e,n,i){var r=this.style;this.__dirty&&hi(r,!0);var o=r.text;if(null!=o&&(o+=""),o){if(r.rich){var a=ai(o,r);o=[];for(var s=0;s<a.lines.length;s++){for(var l=a.lines[s].tokens,u=[],h=0;h<l.length;h++)u.push(l[h].text);o.push(u.join(""))}o=o.join("\n")}var c,d,f=r.textAlign,p=r.textVerticalAlign,g=UA(r.font),v=g.style+" "+g.variant+" "+g.weight+" "+g.size+'px "'+g.family+'"';n=n||Un(o,v,f,p,r.textPadding,r.textLineHeight);var m=this.transform;if(m&&!i&&(jA.copy(e),jA.applyTransform(m),e=jA),i)c=e.x,d=e.y;else{var y=r.textPosition;if(y instanceof Array)c=e.x+LA(y[0],e.width),d=e.y+LA(y[1],e.height),f=f||"left";else{var x=this.calculateTextPosition?this.calculateTextPosition({},r,e):Qn({},r,e);c=x.x,d=x.y,f=f||x.textAlign,p=p||x.textVerticalAlign}}c=$n(c,n.width,f),d=Kn(d,n.height,p),d+=n.height/2;var _,w,b,S=pv,M=this._textVmlEl;M?(b=M.firstChild,_=b.nextSibling,w=_.nextSibling):(M=S("line"),_=S("path"),w=S("textpath"),b=S("skew"),w.style["v-text-align"]="left",CA(M),_.textpathok=!0,w.on=!0,M.from="0 0",M.to="1000 0.05",DA(M,b),DA(M,_),DA(M,w),this._textVmlEl=M);var I=[c,d],C=M.style;m&&i?(oe(I,I,m),b.on=!0,b.matrix=m[0].toFixed(3)+_A+m[2].toFixed(3)+_A+m[1].toFixed(3)+_A+m[3].toFixed(3)+",0,0",b.offset=(pA(I[0])||0)+","+(pA(I[1])||0),b.origin="0 0",C.left="0px",C.top="0px"):(b.on=!1,C.left=pA(c)+"px",C.top=pA(d)+"px"),w.string=TA(o);try{w.style.font=v}catch(T){}BA(M,"fill",{fill:r.textFill,opacity:r.opacity},this),BA(M,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash||null},this),M.style.zIndex=PA(this.zlevel,this.z,this.z2),DA(t,M)}},$A=function(t){kA(t,this._textVmlEl),this._textVmlEl=null},KA=function(t){DA(t,this._textVmlEl)},QA=[py,Ai,Di,no,t_],JA=0;JA<QA.length;JA++){var tD=QA[JA].prototype;tD.drawRectText=qA,tD.removeRectText=$A,tD.appendRectText=KA}t_.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},t_.prototype.onRemove=function(t){this.removeRectText(t)},t_.prototype.onAdd=function(t){this.appendRectText(t)}}mv.prototype={constructor:mv,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,n=0;n<t.length;n++){var i=t[n];i.invisible||i.ignore?(i.__alreadyNotVisible||i.onRemove(e),i.__alreadyNotVisible=!0):(i.__alreadyNotVisible&&i.onAdd(e),i.__alreadyNotVisible=!1,i.__dirty&&(i.beforeBrush&&i.beforeBrush(),(i.brushVML||i.brush).call(i,e),i.afterBrush&&i.afterBrush())),i.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!==t||this._height!==e){this._width=t,this._height=e;var n=this._vmlViewport.style;n.width=t+"px",n.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||vv(e.width))-vv(e.paddingLeft)-vv(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||vv(e.height))-vv(e.paddingTop)-vv(e.paddingBottom)|0}},f(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){mv.prototype[t]=yv(t)}),Ji("vml",mv),t.version=Fb,t.dependencies=Vb,t.PRIORITY=Jb,t.init=mu,t.connect=yu,t.disConnect=xu,t.disconnect=xS,t.dispose=_u,t.getInstanceByDom=wu,t.getInstanceById=bu,t.registerTheme=Su,t.registerPreprocessor=Mu,t.registerProcessor=Iu,t.registerPostUpdate=Cu,t.registerAction=Tu,t.registerCoordinateSystem=Au,t.getCoordinateSystemDimensions=Du,t.registerLayout=ku,t.registerVisual=Pu,t.registerLoading=Ou,t.extendComponentModel=zu,t.extendComponentView=Eu,t.extendSeriesModel=Ru,t.extendChartView=Bu,t.setCanvasCreator=Nu,t.registerMap=Fu,t.getMap=Vu,t.dataTool=_S,t.zrender=Ry,t.number=j_,t.format=ew,t.throttle=xl,t.helper=bM,t.matrix=am,t.vector=Zv,t.color=Im,t.parseGeoJSON=MM,t.parseGeoJson=AM,t.util=DM,t.graphic=kM,t.List=LS,t.Model=ga,t.Axis=TM,t.env=Mv});