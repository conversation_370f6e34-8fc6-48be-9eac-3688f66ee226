var layer, form, laydate, table, element;
var thing = "Thing.Fn.AitScreen";

var username = sessionStorage.getItem("username") || 'adm';


layui.use(['layer', 'form', 'laydate', 'table', 'element'], function () {
	layer = layui.layer;
	form = layui.form;
	laydate = layui.laydate;
	table = layui.table;
	element = layui.element;
	renderUpdateTime();
	loadModelSelect();
	renderSearchBtn();
	//日期范围
	laydate.render({
		elem: '#date-range', //设置开始日期、日期日期的 input 选择器 //数组格式为 2.6.6 开始新增，之前版本直接配置 true 或任意分割字符即可
		range: ['#start-date', '#end-date']
	});

	// 型号研制进度刷新按钮点击事件
	$('#refresh-model-process-btn').on('click', function () {
		var loadingIndex = layer.msg('正在同步型号研制进度数据，请稍等...', {
			icon: 16,
			shade: 0.01,
			time: 0
		});

		// 调用同步服务
		twxAjax(thing, 'SyncMESModelProgress', {}, true,
			function (res) {
				if (res.success) {
					layer.close(loadingIndex);
					layer.msg('同步成功，正在刷新数据...', { icon: 1 });

					// 获取当前选择的型号
					var treeId = $('#model-select').val() || '-1';

					// 重新加载型号研制进度数据
					loadModelProcess(treeId);
				} else {
					layer.close(loadingIndex);
					layer.alert(res.msg, {
						icon: 2
					});
				}
			},
			function (xhr, textStatus, errorThrown) {
				layer.close(loadingIndex);
				layer.alert('请求出错！', {
					icon: 2
				});
			}
		);
	});

	// 现场问题处理单导出按钮点击事件
	$('#export-problem-btn').on('click', function () {
		// 获取当前筛选条件
		var treeId = $('#model-select').val() || '-1';
		var startDate = $('#start-date').val() || '';
		var endDate = $('#end-date').val() || '';

		// 打开状态选择弹窗
		layer.open({
			type: 1,
			title: '导出现场问题处理单',
			area: ['400px', '240px'],
			content: '<div class="layui-form" style="padding: 10px 0 0 0;">' +
				'<div class="layui-form-item">' +
				'<label class="layui-form-label">导出状态</label>' +
				'<div class="layui-input-block">' +
				'<input type="radio" name="export-status" value="all" title="全部" checked>' +
				'<input type="radio" name="export-status" value="finished" title="已完成">' +
				'<input type="radio" name="export-status" value="unfinished" title="未完成">' +
				'</div>' +
				'</div>' +
				'<div class="layui-form-item" style="margin-top: 60px; text-align: center;">' +
				'<button type="button" class="layui-btn layui-btn-normal" id="confirm-export-btn">确定</button>' +
				'<button type="button" class="layui-btn layui-btn-primary" id="cancel-export-btn">取消</button>' +
				'</div>' +
				'</div>',
			success: function (layero, index) {
				form.render('radio');

				// 取消按钮
				$('#cancel-export-btn').on('click', function () {
					layer.close(index);
				});

				// 确定按钮
				$('#confirm-export-btn').on('click', function () {
					var status = $('input[name=export-status]:checked').val();
					// 显示loading，并添加文字提示
					// var loadingIndex = layer.load(2, {
					// 	shade: [0.2, '#000'],
					// 	content: '<div style="padding: 10px 0 0 45px; color: #fff;">正在导出请稍等...</div>',
					// 	success: function(layero) {
					// 		layero.find('.layui-layer-content').css({
					// 			'padding-top': '40px',
					// 			'width': '150px'
					// 		});
					// 	}
					// });

					var loadingIndex = layer.msg('正在导出,请稍等......', {
						icon: 16,
						shade: 0.01,
						time: 0
					});

					// 调用导出接口
					$.fileDownload(fileHandlerUrl + '/aitScreen/exportProblemExcel', {
						httpMethod: 'POST',
						data: {
							treeId: treeId,
							startDate: startDate,
							endDate: endDate,
							status: status
						},
						successCallback: function () {
							layer.close(loadingIndex);
							layer.close(index);
							layer.msg('导出成功', { icon: 1 });
						},
						failCallback: function (responseHtml, url) {
							layer.close(loadingIndex);
							layer.msg('导出失败', { icon: 2 });
							console.error('导出失败:', responseHtml);
						}
					});
				});
			}
		});
	});

	// 现场临时处理单导出按钮点击事件
	$('#export-temp-btn').on('click', function () {
		// 获取当前筛选条件
		var treeId = $('#model-select').val() || '-1';
		var startDate = $('#start-date').val() || '';
		var endDate = $('#end-date').val() || '';

		// 打开状态选择弹窗
		layer.open({
			type: 1,
			title: '导出现场临时处理单',
			area: ['400px', '240px'],
			content: '<div class="layui-form" style="padding: 10px 0 0 0;">' +
				'<div class="layui-form-item">' +
				'<label class="layui-form-label">导出状态</label>' +
				'<div class="layui-input-block">' +
				'<input type="radio" name="export-status" value="all" title="全部" checked>' +
				'<input type="radio" name="export-status" value="finished" title="已完成">' +
				'<input type="radio" name="export-status" value="unfinished" title="未完成">' +
				'</div>' +
				'</div>' +
				'<div class="layui-form-item" style="margin-top: 60px; text-align: center;">' +
				'<button type="button" class="layui-btn layui-btn-normal" id="confirm-export-btn">确定</button>' +
				'<button type="button" class="layui-btn layui-btn-primary" id="cancel-export-btn">取消</button>' +
				'</div>' +
				'</div>',
			success: function (layero, index) {
				form.render('radio');

				// 取消按钮
				$('#cancel-export-btn').on('click', function () {
					layer.close(index);
				});

				// 确定按钮
				$('#confirm-export-btn').on('click', function () {
					var status = $('input[name=export-status]:checked').val();
					// 显示loading，并添加文字提示
					var loadingIndex = layer.msg('正在导出,请稍等......', {
						icon: 16,
						shade: 0.01,
						time: 0
					});

					// 调用导出接口
					$.fileDownload(fileHandlerUrl + '/aitScreen/exportTempExcel', {
						httpMethod: 'POST',
						data: {
							treeId: treeId,
							startDate: startDate,
							endDate: endDate,
							status: status
						},
						successCallback: function () {
							layer.close(loadingIndex);
							layer.close(index);
							layer.msg('导出成功', { icon: 1 });
						},
						failCallback: function (responseHtml, url) {
							layer.close(loadingIndex);
							layer.msg('导出失败', { icon: 2 });
							console.error('导出失败:', responseHtml);
						}
					});
				});
			}
		});
	});

	// 技术状态更改单导出按钮点击事件
	$('#export-change-order-btn').on('click', function () {
		// 获取当前筛选条件
		var treeId = $('#model-select').val() || '-1';
		var startDate = $('#start-date').val() || '';
		var endDate = $('#end-date').val() || '';

		// 先获取所有情况类型
		twxAjax(thing, 'GetSituationTypes', {}, true, function (res) {
			if (res.success) {
				var situationTypes = res.data;

				// 构建情况类型选项HTML
				var situationOptionsHtml = '<option value="all">全部</option>';
				for (var i = 0; i < situationTypes.length; i++) {
					situationOptionsHtml += '<option value="' + situationTypes[i] + '">' + situationTypes[i] + '</option>';
				}

				// 打开状态选择弹窗
				layer.open({
					type: 1,
					title: '导出技术状态更改单',
					area: ['500px', '320px'],
					content: '<div class="layui-form" style="padding: 10px 0 0 0;">' +
						'  <div class="layui-form-item" style="margin-bottom: 20px;">' +
						'    <label class="layui-form-label">导出状态</label>' +
						'    <div class="layui-input-block">' +
						'      <input type="radio" name="export-status" value="all" title="全部" checked>' +
						'      <input type="radio" name="export-status" value="finished" title="已完成">' +
						'      <input type="radio" name="export-status" value="unfinished" title="未完成">' +
						'    </div>' +
						'  </div>' +
						'  <div class="layui-form-item">' +
						'    <label class="layui-form-label">情况筛选</label>' +
						'    <div class="layui-input-block" style="width: 300px;">' +
						'      <select name="export-situation">' +
						situationOptionsHtml +
						'      </select>' +
						'    </div>' +
						'  </div>' +
						'  <div class="layui-form-item" style="margin-top: 40px; text-align: center;">' +
						'    <button type="button" class="layui-btn layui-btn-normal" id="confirm-export-btn">确定</button>' +
						'    <button type="button" class="layui-btn layui-btn-primary" id="cancel-export-btn">取消</button>' +
						'  </div>' +
						'</div>',
					success: function (layero, index) {
						form.render();

						// 取消按钮
						$('#cancel-export-btn').on('click', function () {
							layer.close(index);
						});

						// 确定按钮
						$('#confirm-export-btn').on('click', function () {
							var status = $('input[name=export-status]:checked').val();
							var situation = $('select[name=export-situation]').val();

							// 显示loading，并添加文字提示
							var loadingIndex = layer.msg('正在导出,请稍等......', {
								icon: 16,
								shade: 0.01,
								time: 0
							});

							// 调用导出接口
							$.fileDownload(fileHandlerUrl + '/aitScreen/exportChangeOrderExcel', {
								httpMethod: 'POST',
								data: {
									treeId: treeId,
									username: username,	
									startDate: startDate,
									endDate: endDate,
									status: status,
									situation: situation
								},
								successCallback: function () {
									layer.close(loadingIndex);
									layer.close(index);
									layer.msg('导出成功', { icon: 1 });
								},
								failCallback: function (responseHtml, url) {
									layer.close(loadingIndex);
									layer.msg('导出失败', { icon: 2 });
									console.error('导出失败:', responseHtml);
								}
							});
						});
					}
				});
			} else {
				layer.alert(res.msg || '获取情况类型失败', {
					icon: 2
				});
			}
		}, function (xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	});

	// 不合格品审理单导出按钮点击事件
	$('#bhgp-chart').parent().parent().find('.card-header').append('<div id="export-nonconformity-btn" title="导出不合格品审理单" style="float: right;cursor: pointer;font-weight: 500; margin-right: 5px;"><i class="layui-icon layui-icon-download-circle"></i></div>');

	$('#export-nonconformity-btn').on('click', function () {
		// 获取当前筛选条件
		var treeId = $('#model-select').val() || '-1';
		var startDate = $('#start-date').val() || '';
		var endDate = $('#end-date').val() || '';

		// 先获取所有严重程度类型
		twxAjax(thing, 'QueryNonconformityCount', {
			username: username,
			treeId: -1,
			startDate: '',
			endDate: ''
		}, true, function (res) {
			if (res.success) {
				// 从返回的数据中获取严重程度类型
				var severityLevels = [];
				if (res.data && res.data.statusData && res.data.statusData.severityStatus) {
					severityLevels = Object.keys(res.data.statusData.severityStatus);
				}

				// 构建严重程度选项HTML
				var severityOptionsHtml = '<option value="all">全部</option>';
				for (var i = 0; i < severityLevels.length; i++) {
					severityOptionsHtml += '<option value="' + severityLevels[i] + '">' + severityLevels[i] + '</option>';
				}

				// 打开状态选择弹窗
				layer.open({
					type: 1,
					title: '导出不合格品审理单',
					area: ['500px', '320px'],
					content: '<div class="layui-form" style="padding: 10px 0 0 0;">' +
						'  <div class="layui-form-item" style="margin-bottom: 20px;">' +
						'    <label class="layui-form-label">导出状态</label>' +
						'    <div class="layui-input-block">' +
						'      <input type="radio" name="export-status" value="all" title="全部" checked>' +
						'      <input type="radio" name="export-status" value="finished" title="已完成">' +
						'      <input type="radio" name="export-status" value="unfinished" title="未完成">' +
						'    </div>' +
						'  </div>' +
						'  <div class="layui-form-item">' +
						'    <label class="layui-form-label">严重程度</label>' +
						'    <div class="layui-input-block" style="width: 300px;">' +
						'      <select name="export-severity">' +
						severityOptionsHtml +
						'      </select>' +
						'    </div>' +
						'  </div>' +
						'  <div class="layui-form-item" style="margin-top: 40px; text-align: center;">' +
						'    <button type="button" class="layui-btn layui-btn-normal" id="confirm-export-btn">确定</button>' +
						'    <button type="button" class="layui-btn layui-btn-primary" id="cancel-export-btn">取消</button>' +
						'  </div>' +
						'</div>',
					success: function (layero, index) {
						form.render();

						// 取消按钮
						$('#cancel-export-btn').on('click', function () {
							layer.close(index);
						});

						// 确定按钮
						$('#confirm-export-btn').on('click', function () {
							var status = $('input[name=export-status]:checked').val();
							var severityLevel = $('select[name=export-severity]').val();

							// 显示loading，并添加文字提示
							var loadingIndex = layer.msg('正在导出,请稍等......', {
								icon: 16,
								shade: 0.01,
								time: 0
							});

							// 调用导出接口
							$.fileDownload(fileHandlerUrl + '/aitScreen/exportNonconformityExcel', {
								httpMethod: 'POST',
								data: {
									treeId: treeId,
									username: username,
									startDate: startDate,
									endDate: endDate,
									status: status,
									severityLevel: severityLevel
								},
								successCallback: function () {
									layer.close(loadingIndex);
									layer.close(index);
									layer.msg('导出成功', { icon: 1 });
								},
								failCallback: function (responseHtml, url) {
									layer.close(loadingIndex);
									layer.msg('导出失败', { icon: 2 });
									console.error('导出失败:', responseHtml);
								}
							});
						});
					}
				});
			} else {
				layer.alert(res.msg || '获取严重程度类型失败', {
					icon: 2
				});
			}
		}, function (xhr, textStatus, errorThrown) {
			layer.alert('请求出错！', {
				icon: 2
			});
		});
	});

	// 未交付装星单机汇总导出按钮点击事件
	$('#export-wjfzx-btn').on('click', function () {
		exportSummaryData('ISCERTIFICATE', '未交付装星单机汇总');
	});

	// 落焊单机汇总导出按钮点击事件
	$('#export-lhdj-btn').on('click', function () {
		exportSummaryData('ISLUOHAN', '落焊单机汇总');
	});

	// 单机证明材料交付汇总导出按钮点击事件
	$('#export-zmcl-btn').on('click', function () {
		exportSummaryData('ISSUBMIT', '单机证明材料交付汇总');
	});

	/**
	 * 导出汇总统计数据
	 * @param {string} groupType 分组类型
	 * @param {string} title 标题
	 */
	function exportSummaryData(groupType, title) {
		// 获取当前筛选条件
		var treeId = $('#model-select').val() || '-1';
		var startDate = $('#start-date').val() || '';
		var endDate = $('#end-date').val() || '';

		// 显示loading，并添加文字提示
		var loadingIndex = layer.msg('正在导出' + title + ',请稍等......', {
			icon: 16,
			shade: 0.01,
			time: 0
		});

		// 调用导出接口
		$.fileDownload(fileHandlerUrl + '/aitScreen/exportSummaryExcel', {
			httpMethod: 'POST',
			data: {
				treeId: treeId,
				startDate: startDate,
				endDate: endDate,
				groupType: groupType
			},
			successCallback: function () {
				layer.close(loadingIndex);
				layer.msg('导出成功', { icon: 1 });
			},
			failCallback: function (responseHtml, url) {
				layer.close(loadingIndex);
				layer.msg('导出失败', { icon: 2 });
				console.error('导出失败:', responseHtml);
			}
		});
	}
});

function loadAll(treeId, startDate, endDate) {
	loadModelProcess(treeId);
	for (var i = 0; i < handlerList.length; i++) {
		var handlerObj = handlerList[i];
		loadHandleChart(treeId, startDate, endDate, handlerObj.fileType, handlerObj.chartId, handlerObj.service);
	}
	// for (var i = 0; i < searchList.length; i++) {
	// 	var searchObj = searchList[i];
	// 	loadListChart(treeId, startDate, endDate, searchObj.fileType, searchObj.numId, searchObj.chartId, searchObj.maxValueSpan);
	// }

	for (var i = 0; i < submitList.length; i++) {
		var submitObj = submitList[i];
		loadSubmitChart(treeId, startDate, endDate, submitObj.groupType, submitObj.numId, submitObj.chartId);
	}

	// 加载技术状态更改单柱状图
	loadChangeOrderChart(treeId, startDate, endDate);

	// 加载不合格品审理单柱状图
	loadNonconformityChart(treeId, startDate, endDate);
}

loadAll(-1, "", "");